{"version": 3, "sources": ["../src/utils.ts"], "sourcesContent": ["/**\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * Deletes any properties with `undefined` values in the provided object.\n * Modifies the provided object.\n */\nexport function deleteUndefinedProps(obj: any) {\n  for (const prop in obj) {\n    if (obj[prop] === undefined) {\n      delete obj[prop];\n    } else {\n      if (typeof obj[prop] === 'object') {\n        deleteUndefinedProps(obj[prop]);\n      }\n    }\n  }\n}\n\n/**\n * Strips (non distructively) any properties with `undefined` values in the provided object and returns\n */\nexport function stripUndefinedProps<T>(input: T): T {\n  if (\n    input === undefined ||\n    input === null ||\n    Array.isArray(input) ||\n    typeof input !== 'object'\n  ) {\n    return input;\n  }\n  const out = {} as T;\n  for (const key in input) {\n    if (input[key] !== undefined) {\n      out[key] = stripUndefinedProps(input[key]);\n    }\n  }\n  return out;\n}\n\n/**\n * Returns the current environment that the app code is running in.\n *\n * @hidden\n */\nexport function getCurrentEnv(): string {\n  return process.env.GENKIT_ENV || 'prod';\n}\n\n/**\n * Whether the current environment is `dev`.\n */\nexport function isDevEnv(): boolean {\n  return getCurrentEnv() === 'dev';\n}\n\n/**\n * Adds flow-specific prefix for OpenTelemetry span attributes.\n */\nexport function featureMetadataPrefix(name: string) {\n  return `feature:${name}`;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAoBO,SAAS,qBAAqB,KAAU;AAC7C,aAAW,QAAQ,KAAK;AACtB,QAAI,IAAI,IAAI,MAAM,QAAW;AAC3B,aAAO,IAAI,IAAI;AAAA,IACjB,OAAO;AACL,UAAI,OAAO,IAAI,IAAI,MAAM,UAAU;AACjC,6BAAqB,IAAI,IAAI,CAAC;AAAA,MAChC;AAAA,IACF;AAAA,EACF;AACF;AAKO,SAAS,oBAAuB,OAAa;AAClD,MACE,UAAU,UACV,UAAU,QACV,MAAM,QAAQ,KAAK,KACnB,OAAO,UAAU,UACjB;AACA,WAAO;AAAA,EACT;AACA,QAAM,MAAM,CAAC;AACb,aAAW,OAAO,OAAO;AACvB,QAAI,MAAM,GAAG,MAAM,QAAW;AAC5B,UAAI,GAAG,IAAI,oBAAoB,MAAM,GAAG,CAAC;AAAA,IAC3C;AAAA,EACF;AACA,SAAO;AACT;AAOO,SAAS,gBAAwB;AACtC,SAAO,QAAQ,IAAI,cAAc;AACnC;AAKO,SAAS,WAAoB;AAClC,SAAO,cAAc,MAAM;AAC7B;AAKO,SAAS,sBAAsB,MAAc;AAClD,SAAO,WAAW,IAAI;AACxB;", "names": []}