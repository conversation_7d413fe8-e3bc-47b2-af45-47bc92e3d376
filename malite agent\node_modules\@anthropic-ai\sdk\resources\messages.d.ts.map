{"version": 3, "file": "messages.d.ts", "sourceRoot": "", "sources": ["../src/resources/messages.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,WAAW,EAAE,MAAM,4BAA4B,CAAC;AACzD,OAAO,EAAE,UAAU,EAAE,MAAM,wBAAwB,CAAC;AACpD,OAAO,KAAK,IAAI,MAAM,wBAAwB,CAAC;AAC/C,OAAO,EAAE,aAAa,EAAE,MAAM,qCAAqC,CAAC;AACpE,OAAO,KAAK,WAAW,MAAM,sCAAsC,CAAC;AACpE,OAAO,EAAE,MAAM,EAAE,MAAM,6BAA6B,CAAC;AAErD,OAAO,EAAE,aAAa,EAAE,MAAM,qCAAqC,CAAC;AAEpE,qBAAa,QAAS,SAAQ,WAAW;IACvC;;;;;;;;OAQG;IACH,MAAM,CAAC,IAAI,EAAE,+BAA+B,EAAE,OAAO,CAAC,EAAE,IAAI,CAAC,cAAc,GAAG,UAAU,CAAC,OAAO,CAAC;IACjG,MAAM,CACJ,IAAI,EAAE,4BAA4B,EAClC,OAAO,CAAC,EAAE,IAAI,CAAC,cAAc,GAC5B,UAAU,CAAC,MAAM,CAAC,qBAAqB,CAAC,CAAC;IAC5C,MAAM,CACJ,IAAI,EAAE,uBAAuB,EAC7B,OAAO,CAAC,EAAE,IAAI,CAAC,cAAc,GAC5B,UAAU,CAAC,MAAM,CAAC,qBAAqB,CAAC,GAAG,OAAO,CAAC;IAatD;;OAEG;IACH,MAAM,CAAC,IAAI,EAAE,mBAAmB,EAAE,OAAO,CAAC,EAAE,IAAI,CAAC,cAAc,GAAG,aAAa;CAGhF;AAED,MAAM,MAAM,YAAY,GAAG,SAAS,GAAG,YAAY,CAAC;AAEpD,MAAM,MAAM,sBAAsB,GAAG,yBAAyB,CAAC;AAE/D,MAAM,MAAM,sBAAsB,GAAG,yBAAyB,CAAC;AAE/D,MAAM,MAAM,qBAAqB,GAAG,wBAAwB,CAAC;AAE7D,MAAM,WAAW,eAAe;IAC9B,MAAM,EAAE,eAAe,CAAC,MAAM,CAAC;IAE/B,IAAI,EAAE,OAAO,CAAC;CACf;AAED,yBAAiB,eAAe,CAAC;IAC/B,UAAiB,MAAM;QACrB,IAAI,EAAE,MAAM,CAAC;QAEb,UAAU,EAAE,YAAY,GAAG,WAAW,GAAG,WAAW,GAAG,YAAY,CAAC;QAEpE,IAAI,EAAE,QAAQ,CAAC;KAChB;CACF;AAED,MAAM,WAAW,cAAc;IAC7B,YAAY,EAAE,MAAM,CAAC;IAErB,IAAI,EAAE,kBAAkB,CAAC;CAC1B;AAED,MAAM,WAAW,OAAO;IACtB;;;;OAIG;IACH,EAAE,EAAE,MAAM,CAAC;IAEX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAiCG;IACH,OAAO,EAAE,KAAK,CAAC,YAAY,CAAC,CAAC;IAE7B;;OAEG;IACH,KAAK,EAAE,MAAM,CAAC;IAEd;;;;OAIG;IACH,IAAI,EAAE,WAAW,CAAC;IAElB;;;;;;;;;;;;OAYG;IACH,WAAW,EAAE,UAAU,GAAG,YAAY,GAAG,eAAe,GAAG,UAAU,GAAG,IAAI,CAAC;IAE7E;;;;;OAKG;IACH,aAAa,EAAE,MAAM,GAAG,IAAI,CAAC;IAE7B;;;;OAIG;IACH,IAAI,EAAE,SAAS,CAAC;IAEhB;;;;;;;;;;;;;OAaG;IACH,KAAK,EAAE,KAAK,CAAC;CACd;AAED,MAAM,MAAM,iBAAiB,GAAG,oBAAoB,CAAC;AAErD,MAAM,WAAW,iBAAiB;IAChC;;OAEG;IACH,aAAa,EAAE,MAAM,CAAC;CACvB;AAED,MAAM,WAAW,YAAY;IAC3B,OAAO,EAAE,MAAM,GAAG,KAAK,CAAC,cAAc,GAAG,eAAe,GAAG,iBAAiB,GAAG,oBAAoB,CAAC,CAAC;IAErG,IAAI,EAAE,MAAM,GAAG,WAAW,CAAC;CAC5B;AAED,MAAM,MAAM,iBAAiB,GAAG,oBAAoB,CAAC;AAErD,MAAM,MAAM,gBAAgB,GAAG,mBAAmB,CAAC;AAEnD,MAAM,MAAM,kBAAkB,GAAG,qBAAqB,CAAC;AAEvD,MAAM,WAAW,yBAAyB;IACxC,KAAK,EAAE,SAAS,GAAG,cAAc,CAAC;IAElC,KAAK,EAAE,MAAM,CAAC;IAEd,IAAI,EAAE,qBAAqB,CAAC;CAC7B;AAED,MAAM,WAAW,yBAAyB;IACxC,aAAa,EAAE,SAAS,GAAG,YAAY,CAAC;IAExC,KAAK,EAAE,MAAM,CAAC;IAEd,IAAI,EAAE,qBAAqB,CAAC;CAC7B;AAED,MAAM,WAAW,wBAAwB;IACvC,KAAK,EAAE,MAAM,CAAC;IAEd,IAAI,EAAE,oBAAoB,CAAC;CAC5B;AAED,MAAM,WAAW,oBAAoB;IACnC,KAAK,EAAE,oBAAoB,CAAC,KAAK,CAAC;IAElC,IAAI,EAAE,eAAe,CAAC;IAEtB;;;;;;;;;;;;;OAaG;IACH,KAAK,EAAE,iBAAiB,CAAC;CAC1B;AAED,yBAAiB,oBAAoB,CAAC;IACpC,UAAiB,KAAK;QACpB,WAAW,EAAE,UAAU,GAAG,YAAY,GAAG,eAAe,GAAG,UAAU,GAAG,IAAI,CAAC;QAE7E,aAAa,EAAE,MAAM,GAAG,IAAI,CAAC;KAC9B;CACF;AAED,MAAM,WAAW,oBAAoB;IACnC,OAAO,EAAE,OAAO,CAAC;IAEjB,IAAI,EAAE,eAAe,CAAC;CACvB;AAED,MAAM,WAAW,mBAAmB;IAClC,IAAI,EAAE,cAAc,CAAC;CACtB;AAED,MAAM,MAAM,qBAAqB,GAC7B,oBAAoB,GACpB,oBAAoB,GACpB,mBAAmB,GACnB,yBAAyB,GACzB,yBAAyB,GACzB,wBAAwB,CAAC;AAE7B,MAAM,WAAW,SAAS;IACxB,IAAI,EAAE,MAAM,CAAC;IAEb,IAAI,EAAE,MAAM,CAAC;CACd;AAED,MAAM,WAAW,cAAc;IAC7B,IAAI,EAAE,MAAM,CAAC;IAEb,IAAI,EAAE,MAAM,CAAC;CACd;AAED,MAAM,WAAW,SAAS;IACxB,IAAI,EAAE,MAAM,CAAC;IAEb,IAAI,EAAE,YAAY,CAAC;CACpB;AAED,MAAM,WAAW,IAAI;IACnB;;;;;OAKG;IACH,YAAY,EAAE,IAAI,CAAC,WAAW,CAAC;IAE/B,IAAI,EAAE,MAAM,CAAC;IAEb;;;;;;;OAOG;IACH,WAAW,CAAC,EAAE,MAAM,CAAC;CACtB;AAED,yBAAiB,IAAI,CAAC;IACpB;;;;;OAKG;IACH,UAAiB,WAAW;QAC1B,IAAI,EAAE,QAAQ,CAAC;QAEf,UAAU,CAAC,EAAE,OAAO,GAAG,IAAI,CAAC;QAC5B,CAAC,CAAC,EAAE,MAAM,GAAG,OAAO,CAAC;KACtB;CACF;AAED,MAAM,WAAW,oBAAoB;IACnC,WAAW,EAAE,MAAM,CAAC;IAEpB,IAAI,EAAE,aAAa,CAAC;IAEpB,OAAO,CAAC,EAAE,MAAM,GAAG,KAAK,CAAC,cAAc,GAAG,eAAe,CAAC,CAAC;IAE3D,QAAQ,CAAC,EAAE,OAAO,CAAC;CACpB;AAED,MAAM,WAAW,YAAY;IAC3B,EAAE,EAAE,MAAM,CAAC;IAEX,KAAK,EAAE,OAAO,CAAC;IAEf,IAAI,EAAE,MAAM,CAAC;IAEb,IAAI,EAAE,UAAU,CAAC;CAClB;AAED,MAAM,WAAW,iBAAiB;IAChC,EAAE,EAAE,MAAM,CAAC;IAEX,KAAK,EAAE,OAAO,CAAC;IAEf,IAAI,EAAE,MAAM,CAAC;IAEb,IAAI,EAAE,UAAU,CAAC;CAClB;AAED,MAAM,WAAW,KAAK;IACpB;;OAEG;IACH,YAAY,EAAE,MAAM,CAAC;IAErB;;OAEG;IACH,aAAa,EAAE,MAAM,CAAC;CACvB;AAED,MAAM,MAAM,mBAAmB,GAAG,+BAA+B,GAAG,4BAA4B,CAAC;AAEjG,MAAM,WAAW,uBAAuB;IACtC;;;;;;;;OAQG;IACH,UAAU,EAAE,MAAM,CAAC;IAEnB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAsFG;IACH,QAAQ,EAAE,KAAK,CAAC,YAAY,CAAC,CAAC;IAE9B;;;;;OAKG;IACH,KAAK,EACD,CAAC,MAAM,GAAG,EAAE,CAAC,GACb,4BAA4B,GAC5B,wBAAwB,GACxB,0BAA0B,GAC1B,yBAAyB,GACzB,YAAY,GACZ,YAAY,GACZ,oBAAoB,CAAC;IAEzB;;OAEG;IACH,QAAQ,CAAC,EAAE,mBAAmB,CAAC,QAAQ,CAAC;IAExC;;;;;;;;;;OAUG;IACH,cAAc,CAAC,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC;IAE/B;;;;;OAKG;IACH,MAAM,CAAC,EAAE,OAAO,CAAC;IAEjB;;;;;;OAMG;IACH,MAAM,CAAC,EAAE,MAAM,GAAG,KAAK,CAAC,cAAc,CAAC,CAAC;IAExC;;;;;;;;;OASG;IACH,WAAW,CAAC,EAAE,MAAM,CAAC;IAErB;;;OAGG;IACH,WAAW,CAAC,EACR,mBAAmB,CAAC,cAAc,GAClC,mBAAmB,CAAC,aAAa,GACjC,mBAAmB,CAAC,cAAc,CAAC;IAEvC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAqEG;IACH,KAAK,CAAC,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC;IAEpB;;;;;;;;OAQG;IACH,KAAK,CAAC,EAAE,MAAM,CAAC;IAEf;;;;;;;;;;OAUG;IACH,KAAK,CAAC,EAAE,MAAM,CAAC;CAChB;AAED,yBAAiB,mBAAmB,CAAC;IACnC;;OAEG;IACH,UAAiB,QAAQ;QACvB;;;;;;WAMG;QACH,OAAO,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;KACzB;IAED;;OAEG;IACH,UAAiB,cAAc;QAC7B,IAAI,EAAE,MAAM,CAAC;KACd;IAED;;OAEG;IACH,UAAiB,aAAa;QAC5B,IAAI,EAAE,KAAK,CAAC;KACb;IAED;;OAEG;IACH,UAAiB,cAAc;QAC7B;;WAEG;QACH,IAAI,EAAE,MAAM,CAAC;QAEb,IAAI,EAAE,MAAM,CAAC;KACd;IAED,KAAY,+BAA+B,GAAG,WAAW,CAAC,+BAA+B,CAAC;IAC1F,KAAY,4BAA4B,GAAG,WAAW,CAAC,4BAA4B,CAAC;CACrF;AAED,MAAM,WAAW,+BAAgC,SAAQ,uBAAuB;IAC9E;;;;;OAKG;IACH,MAAM,CAAC,EAAE,KAAK,CAAC;CAChB;AAED,MAAM,WAAW,4BAA6B,SAAQ,uBAAuB;IAC3E;;;;;OAKG;IACH,MAAM,EAAE,IAAI,CAAC;CACd;AAED,MAAM,MAAM,mBAAmB,GAAG,uBAAuB,CAAC;AAE1D,yBAAiB,QAAQ,CAAC;IACxB,MAAM,QAAQ,YAAY,GAAG,WAAW,CAAC,YAAY,CAAC;IACtD,MAAM,QAAQ,sBAAsB,GAAG,WAAW,CAAC,sBAAsB,CAAC;IAC1E,MAAM,QAAQ,sBAAsB,GAAG,WAAW,CAAC,sBAAsB,CAAC;IAC1E,MAAM,QAAQ,qBAAqB,GAAG,WAAW,CAAC,qBAAqB,CAAC;IACxE,MAAM,QAAQ,eAAe,GAAG,WAAW,CAAC,eAAe,CAAC;IAC5D,MAAM,QAAQ,cAAc,GAAG,WAAW,CAAC,cAAc,CAAC;IAC1D,MAAM,QAAQ,OAAO,GAAG,WAAW,CAAC,OAAO,CAAC;IAC5C,MAAM,QAAQ,iBAAiB,GAAG,WAAW,CAAC,iBAAiB,CAAC;IAChE,MAAM,QAAQ,iBAAiB,GAAG,WAAW,CAAC,iBAAiB,CAAC;IAChE,MAAM,QAAQ,YAAY,GAAG,WAAW,CAAC,YAAY,CAAC;IACtD,MAAM,QAAQ,iBAAiB,GAAG,WAAW,CAAC,iBAAiB,CAAC;IAChE,MAAM,QAAQ,gBAAgB,GAAG,WAAW,CAAC,gBAAgB,CAAC;IAC9D,MAAM,QAAQ,kBAAkB,GAAG,WAAW,CAAC,kBAAkB,CAAC;IAClE,MAAM,QAAQ,yBAAyB,GAAG,WAAW,CAAC,yBAAyB,CAAC;IAChF,MAAM,QAAQ,yBAAyB,GAAG,WAAW,CAAC,yBAAyB,CAAC;IAChF,MAAM,QAAQ,wBAAwB,GAAG,WAAW,CAAC,wBAAwB,CAAC;IAC9E,MAAM,QAAQ,oBAAoB,GAAG,WAAW,CAAC,oBAAoB,CAAC;IACtE,MAAM,QAAQ,oBAAoB,GAAG,WAAW,CAAC,oBAAoB,CAAC;IACtE,MAAM,QAAQ,mBAAmB,GAAG,WAAW,CAAC,mBAAmB,CAAC;IACpE,MAAM,QAAQ,qBAAqB,GAAG,WAAW,CAAC,qBAAqB,CAAC;IACxE,MAAM,QAAQ,SAAS,GAAG,WAAW,CAAC,SAAS,CAAC;IAChD,MAAM,QAAQ,cAAc,GAAG,WAAW,CAAC,cAAc,CAAC;IAC1D,MAAM,QAAQ,SAAS,GAAG,WAAW,CAAC,SAAS,CAAC;IAChD,MAAM,QAAQ,IAAI,GAAG,WAAW,CAAC,IAAI,CAAC;IACtC,MAAM,QAAQ,oBAAoB,GAAG,WAAW,CAAC,oBAAoB,CAAC;IACtE,MAAM,QAAQ,YAAY,GAAG,WAAW,CAAC,YAAY,CAAC;IACtD,MAAM,QAAQ,iBAAiB,GAAG,WAAW,CAAC,iBAAiB,CAAC;IAChE,MAAM,QAAQ,KAAK,GAAG,WAAW,CAAC,KAAK,CAAC;IACxC,MAAM,QAAQ,mBAAmB,GAAG,WAAW,CAAC,mBAAmB,CAAC;IACpE,MAAM,QAAQ,+BAA+B,GAAG,WAAW,CAAC,+BAA+B,CAAC;IAC5F,MAAM,QAAQ,4BAA4B,GAAG,WAAW,CAAC,4BAA4B,CAAC;IACtF,MAAM,QAAQ,mBAAmB,GAAG,WAAW,CAAC,mBAAmB,CAAC;CACrE"}