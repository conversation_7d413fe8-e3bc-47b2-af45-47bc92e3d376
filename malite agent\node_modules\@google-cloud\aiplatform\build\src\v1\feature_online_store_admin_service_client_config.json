{"interfaces": {"google.cloud.aiplatform.v1.FeatureOnlineStoreAdminService": {"retry_codes": {"non_idempotent": [], "idempotent": ["DEADLINE_EXCEEDED", "UNAVAILABLE"]}, "retry_params": {"default": {"initial_retry_delay_millis": 100, "retry_delay_multiplier": 1.3, "max_retry_delay_millis": 60000, "initial_rpc_timeout_millis": 60000, "rpc_timeout_multiplier": 1, "max_rpc_timeout_millis": 60000, "total_timeout_millis": 600000}}, "methods": {"CreateFeatureOnlineStore": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "GetFeatureOnlineStore": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "ListFeatureOnlineStores": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "UpdateFeatureOnlineStore": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "DeleteFeatureOnlineStore": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "CreateFeatureView": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "GetFeatureView": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "ListFeatureViews": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "UpdateFeatureView": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "DeleteFeatureView": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "SyncFeatureView": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "GetFeatureViewSync": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "ListFeatureViewSyncs": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}}}}}