// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.aiplatform.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/httpbody.proto";
import "google/api/resource.proto";
import "google/protobuf/struct.proto";

option csharp_namespace = "Google.Cloud.AIPlatform.V1";
option go_package = "cloud.google.com/go/aiplatform/apiv1/aiplatformpb;aiplatformpb";
option java_multiple_files = true;
option java_outer_classname = "ReasoningEngineExecutionServiceProto";
option java_package = "com.google.cloud.aiplatform.v1";
option php_namespace = "Google\\Cloud\\AIPlatform\\V1";
option ruby_package = "Google::Cloud::AIPlatform::V1";

// A service for executing queries on Reasoning Engine.
service ReasoningEngineExecutionService {
  option (google.api.default_host) = "aiplatform.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Queries using a reasoning engine.
  rpc QueryReasoningEngine(QueryReasoningEngineRequest)
      returns (QueryReasoningEngineResponse) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/reasoningEngines/*}:query"
      body: "*"
    };
  }

  // Streams queries using a reasoning engine.
  rpc StreamQueryReasoningEngine(StreamQueryReasoningEngineRequest)
      returns (stream google.api.HttpBody) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/reasoningEngines/*}:streamQuery"
      body: "*"
    };
  }
}

// Request message for [ReasoningEngineExecutionService.Query][].
message QueryReasoningEngineRequest {
  // Required. The name of the ReasoningEngine resource to use.
  // Format:
  // `projects/{project}/locations/{location}/reasoningEngines/{reasoning_engine}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/ReasoningEngine"
    }
  ];

  // Optional. Input content provided by users in JSON object format. Examples
  // include text query, function calling parameters, media bytes, etc.
  google.protobuf.Struct input = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Class method to be used for the query.
  // It is optional and defaults to "query" if unspecified.
  string class_method = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for [ReasoningEngineExecutionService.Query][]
message QueryReasoningEngineResponse {
  // Response provided by users in JSON object format.
  google.protobuf.Value output = 1;
}

// Request message for [ReasoningEngineExecutionService.StreamQuery][].
message StreamQueryReasoningEngineRequest {
  // Required. The name of the ReasoningEngine resource to use.
  // Format:
  // `projects/{project}/locations/{location}/reasoningEngines/{reasoning_engine}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/ReasoningEngine"
    }
  ];

  // Optional. Input content provided by users in JSON object format. Examples
  // include text query, function calling parameters, media bytes, etc.
  google.protobuf.Struct input = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Class method to be used for the stream query.
  // It is optional and defaults to "stream_query" if unspecified.
  string class_method = 3 [(google.api.field_behavior) = OPTIONAL];
}
