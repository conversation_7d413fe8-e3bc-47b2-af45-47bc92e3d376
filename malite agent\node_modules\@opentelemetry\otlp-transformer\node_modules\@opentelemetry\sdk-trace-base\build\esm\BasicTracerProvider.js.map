{"version": 3, "file": "BasicTracerProvider.js", "sourceRoot": "", "sources": ["../../src/BasicTracerProvider.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAEH,OAAO,EACL,OAAO,EACP,IAAI,EACJ,WAAW,EAEX,KAAK,GAEN,MAAM,oBAAoB,CAAC;AAC5B,OAAO,EACL,mBAAmB,EACnB,oBAAoB,EACpB,yBAAyB,EACzB,MAAM,EACN,KAAK,GACN,MAAM,qBAAqB,CAAC;AAC7B,OAAO,EAAa,QAAQ,EAAE,MAAM,0BAA0B,CAAC;AAC/D,OAAO,EAAiB,MAAM,EAAE,MAAM,GAAG,CAAC;AAC1C,OAAO,EAAE,iBAAiB,EAAE,MAAM,UAAU,CAAC;AAC7C,OAAO,EAAE,kBAAkB,EAAE,MAAM,sBAAsB,CAAC;AAC1D,OAAO,EAAE,iBAAiB,EAAE,MAAM,4BAA4B,CAAC;AAG/D,OAAO,EAAE,kBAAkB,EAAE,MAAM,YAAY,CAAC;AAChD,OAAO,EAAE,iBAAiB,EAAE,MAAM,WAAW,CAAC;AAK9C,MAAM,CAAN,IAAY,eAKX;AALD,WAAY,eAAe;IACzB,6DAAU,CAAA;IACV,2DAAS,CAAA;IACT,uDAAO,CAAA;IACP,iEAAY,CAAA;AACd,CAAC,EALW,eAAe,KAAf,eAAe,QAK1B;AAED;;GAEG;AACH;IAqBE,6BAAY,MAAyB;QAAzB,uBAAA,EAAA,WAAyB;;QANpB,8BAAyB,GAAoB,EAAE,CAAC;QAChD,aAAQ,GAAwB,IAAI,GAAG,EAAE,CAAC;QAMzD,IAAM,YAAY,GAAG,KAAK,CACxB,EAAE,EACF,iBAAiB,EAAE,EACnB,iBAAiB,CAAC,MAAM,CAAC,CAC1B,CAAC;QACF,IAAI,CAAC,QAAQ,GAAG,MAAA,YAAY,CAAC,QAAQ,mCAAI,QAAQ,CAAC,KAAK,EAAE,CAAC;QAC1D,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC,OAAO,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACxD,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,YAAY,EAAE;YAC7C,QAAQ,EAAE,IAAI,CAAC,QAAQ;SACxB,CAAC,CAAC;QAEH,IAAM,eAAe,GAAG,IAAI,CAAC,qBAAqB,EAAE,CAAC;QACrD,IAAI,eAAe,KAAK,SAAS,EAAE;YACjC,IAAM,cAAc,GAAG,IAAI,kBAAkB,CAAC,eAAe,CAAC,CAAC;YAC/D,IAAI,CAAC,mBAAmB,GAAG,cAAc,CAAC;SAC3C;aAAM;YACL,IAAI,CAAC,mBAAmB,GAAG,IAAI,iBAAiB,EAAE,CAAC;SACpD;IACH,CAAC;IAED,uCAAS,GAAT,UACE,IAAY,EACZ,OAAgB,EAChB,OAAgC;QAEhC,IAAM,GAAG,GAAM,IAAI,UAAI,OAAO,IAAI,EAAE,WAAI,CAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,SAAS,KAAI,EAAE,CAAE,CAAC;QACnE,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;YAC3B,IAAI,CAAC,QAAQ,CAAC,GAAG,CACf,GAAG,EACH,IAAI,MAAM,CACR,EAAE,IAAI,MAAA,EAAE,OAAO,SAAA,EAAE,SAAS,EAAE,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,SAAS,EAAE,EAChD,IAAI,CAAC,OAAO,EACZ,IAAI,CACL,CACF,CAAC;SACH;QAED,oEAAoE;QACpE,OAAO,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAE,CAAC;IACjC,CAAC;IAED;;;OAGG;IACH,8CAAgB,GAAhB,UAAiB,aAA4B;QAC3C,IAAI,IAAI,CAAC,yBAAyB,CAAC,MAAM,KAAK,CAAC,EAAE;YAC/C,yEAAyE;YACzE,4BAA4B;YAC5B,IAAI,CAAC,mBAAmB;iBACrB,QAAQ,EAAE;iBACV,KAAK,CAAC,UAAA,GAAG;gBACR,OAAA,IAAI,CAAC,KAAK,CACR,uDAAuD,EACvD,GAAG,CACJ;YAHD,CAGC,CACF,CAAC;SACL;QACD,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QACnD,IAAI,CAAC,mBAAmB,GAAG,IAAI,kBAAkB,CAC/C,IAAI,CAAC,yBAAyB,CAC/B,CAAC;IACJ,CAAC;IAED,oDAAsB,GAAtB;QACE,OAAO,IAAI,CAAC,mBAAmB,CAAC;IAClC,CAAC;IAED;;;;;;OAMG;IACH,sCAAQ,GAAR,UAAS,MAAkC;QAAlC,uBAAA,EAAA,WAAkC;QACzC,KAAK,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC;QACpC,IAAI,MAAM,CAAC,UAAU,KAAK,SAAS,EAAE;YACnC,MAAM,CAAC,UAAU,GAAG,IAAI,CAAC,uBAAuB,EAAE,CAAC;SACpD;QAED,IAAI,MAAM,CAAC,cAAc,EAAE;YACzB,OAAO,CAAC,uBAAuB,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;SACxD;QAED,IAAI,MAAM,CAAC,UAAU,EAAE;YACrB,WAAW,CAAC,mBAAmB,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;SACpD;IACH,CAAC;IAED,wCAAU,GAAV;QACE,IAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,uBAAuB,CAAC;QACrD,IAAM,QAAQ,GAAG,IAAI,CAAC,yBAAyB,CAAC,GAAG,CACjD,UAAC,aAA4B;YAC3B,OAAO,IAAI,OAAO,CAAC,UAAA,OAAO;gBACxB,IAAI,KAAsB,CAAC;gBAC3B,IAAM,eAAe,GAAG,UAAU,CAAC;oBACjC,OAAO,CACL,IAAI,KAAK,CACP,+DAA6D,OAAO,QAAK,CAC1E,CACF,CAAC;oBACF,KAAK,GAAG,eAAe,CAAC,OAAO,CAAC;gBAClC,CAAC,EAAE,OAAO,CAAC,CAAC;gBAEZ,aAAa;qBACV,UAAU,EAAE;qBACZ,IAAI,CAAC;oBACJ,YAAY,CAAC,eAAe,CAAC,CAAC;oBAC9B,IAAI,KAAK,KAAK,eAAe,CAAC,OAAO,EAAE;wBACrC,KAAK,GAAG,eAAe,CAAC,QAAQ,CAAC;wBACjC,OAAO,CAAC,KAAK,CAAC,CAAC;qBAChB;gBACH,CAAC,CAAC;qBACD,KAAK,CAAC,UAAA,KAAK;oBACV,YAAY,CAAC,eAAe,CAAC,CAAC;oBAC9B,KAAK,GAAG,eAAe,CAAC,KAAK,CAAC;oBAC9B,OAAO,CAAC,KAAK,CAAC,CAAC;gBACjB,CAAC,CAAC,CAAC;YACP,CAAC,CAAC,CAAC;QACL,CAAC,CACF,CAAC;QAEF,OAAO,IAAI,OAAO,CAAO,UAAC,OAAO,EAAE,MAAM;YACvC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC;iBAClB,IAAI,CAAC,UAAA,OAAO;gBACX,IAAM,MAAM,GAAG,OAAO,CAAC,MAAM,CAC3B,UAAA,MAAM,IAAI,OAAA,MAAM,KAAK,eAAe,CAAC,QAAQ,EAAnC,CAAmC,CAC9C,CAAC;gBACF,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE;oBACrB,MAAM,CAAC,MAAM,CAAC,CAAC;iBAChB;qBAAM;oBACL,OAAO,EAAE,CAAC;iBACX;YACH,CAAC,CAAC;iBACD,KAAK,CAAC,UAAA,KAAK,IAAI,OAAA,MAAM,CAAC,CAAC,KAAK,CAAC,CAAC,EAAf,CAAe,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC;IACL,CAAC;IAED,sCAAQ,GAAR;QACE,OAAO,IAAI,CAAC,mBAAmB,CAAC,QAAQ,EAAE,CAAC;IAC7C,CAAC;IAED;;;;;;OAMG;IACO,4CAAc,GAAxB,UAAyB,IAAY;;QACnC,OAAO,MACL,IAAI,CAAC,WACN,CAAC,sBAAsB,CAAC,GAAG,CAAC,IAAI,CAAC,2CAAI,CAAC;IACzC,CAAC;IAES,8CAAgB,GAA1B,UAA2B,IAAY;;QACrC,OAAO,MACL,IAAI,CAAC,WACN,CAAC,oBAAoB,CAAC,GAAG,CAAC,IAAI,CAAC,2CAAI,CAAC;IACvC,CAAC;IAES,qDAAuB,GAAjC;QAAA,iBAmCC;QAlCC,sDAAsD;QACtD,IAAM,qBAAqB,GAAG,KAAK,CAAC,IAAI,CACtC,IAAI,GAAG,CAAC,MAAM,EAAE,CAAC,gBAAgB,CAAC,CACnC,CAAC;QAEF,IAAM,WAAW,GAAG,qBAAqB,CAAC,GAAG,CAAC,UAAA,IAAI;YAChD,IAAM,UAAU,GAAG,KAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;YAC7C,IAAI,CAAC,UAAU,EAAE;gBACf,IAAI,CAAC,IAAI,CACP,kBAAe,IAAI,8DAA0D,CAC9E,CAAC;aACH;YAED,OAAO,UAAU,CAAC;QACpB,CAAC,CAAC,CAAC;QACH,IAAM,gBAAgB,GAAG,WAAW,CAAC,MAAM,CACzC,UAAC,IAAI,EAAE,IAAI;YACT,IAAI,IAAI,EAAE;gBACR,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;aACjB;YACD,OAAO,IAAI,CAAC;QACd,CAAC,EACD,EAAE,CACH,CAAC;QAEF,IAAI,gBAAgB,CAAC,MAAM,KAAK,CAAC,EAAE;YACjC,OAAO;SACR;aAAM,IAAI,qBAAqB,CAAC,MAAM,KAAK,CAAC,EAAE;YAC7C,OAAO,gBAAgB,CAAC,CAAC,CAAC,CAAC;SAC5B;aAAM;YACL,OAAO,IAAI,mBAAmB,CAAC;gBAC7B,WAAW,EAAE,gBAAgB;aAC9B,CAAC,CAAC;SACJ;IACH,CAAC;IAES,mDAAqB,GAA/B;QACE,IAAM,YAAY,GAAG,MAAM,EAAE,CAAC,oBAAoB,CAAC;QACnD,IAAI,YAAY,KAAK,MAAM,IAAI,YAAY,KAAK,EAAE;YAAE,OAAO;QAC3D,IAAM,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC,CAAC;QACrD,IAAI,CAAC,QAAQ,EAAE;YACb,IAAI,CAAC,KAAK,CACR,gBAAa,YAAY,8DAA0D,CACpF,CAAC;SACH;QACD,OAAO,QAAQ,CAAC;IAClB,CAAC;IAtOyB,0CAAsB,GAAG,IAAI,GAAG,CAGxD;QACA,CAAC,cAAc,EAAE,cAAM,OAAA,IAAI,yBAAyB,EAAE,EAA/B,CAA+B,CAAC;QACvD,CAAC,SAAS,EAAE,cAAM,OAAA,IAAI,oBAAoB,EAAE,EAA1B,CAA0B,CAAC;KAC9C,CAAC,CAAC;IAEuB,wCAAoB,GAAG,IAAI,GAAG,EAGrD,CAAC;IA4NN,0BAAC;CAAA,AAxOD,IAwOC;SAxOY,mBAAmB", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  context,\n  diag,\n  propagation,\n  TextMapPropagator,\n  trace,\n  TracerProvider,\n} from '@opentelemetry/api';\nimport {\n  CompositePropagator,\n  W3CBaggagePropagator,\n  W3CTraceContextPropagator,\n  getEnv,\n  merge,\n} from '@opentelemetry/core';\nimport { IResource, Resource } from '@opentelemetry/resources';\nimport { SpanProcessor, Tracer } from '.';\nimport { loadDefaultConfig } from './config';\nimport { MultiSpanProcessor } from './MultiSpanProcessor';\nimport { NoopSpanProcessor } from './export/NoopSpanProcessor';\nimport { SDKRegistrationConfig, TracerConfig } from './types';\nimport { SpanExporter } from './export/SpanExporter';\nimport { BatchSpanProcessor } from './platform';\nimport { reconfigureLimits } from './utility';\n\nexport type PROPAGATOR_FACTORY = () => TextMapPropagator;\nexport type EXPORTER_FACTORY = () => SpanExporter;\n\nexport enum ForceFlushState {\n  'resolved',\n  'timeout',\n  'error',\n  'unresolved',\n}\n\n/**\n * This class represents a basic tracer provider which platform libraries can extend\n */\nexport class BasicTracerProvider implements TracerProvider {\n  protected static readonly _registeredPropagators = new Map<\n    string,\n    PROPAGATOR_FACTORY\n  >([\n    ['tracecontext', () => new W3CTraceContextPropagator()],\n    ['baggage', () => new W3CBaggagePropagator()],\n  ]);\n\n  protected static readonly _registeredExporters = new Map<\n    string,\n    EXPORTER_FACTORY\n  >();\n\n  private readonly _config: TracerConfig;\n  private readonly _registeredSpanProcessors: SpanProcessor[] = [];\n  private readonly _tracers: Map<string, Tracer> = new Map();\n\n  activeSpanProcessor: SpanProcessor;\n  readonly resource: IResource;\n\n  constructor(config: TracerConfig = {}) {\n    const mergedConfig = merge(\n      {},\n      loadDefaultConfig(),\n      reconfigureLimits(config)\n    );\n    this.resource = mergedConfig.resource ?? Resource.empty();\n    this.resource = Resource.default().merge(this.resource);\n    this._config = Object.assign({}, mergedConfig, {\n      resource: this.resource,\n    });\n\n    const defaultExporter = this._buildExporterFromEnv();\n    if (defaultExporter !== undefined) {\n      const batchProcessor = new BatchSpanProcessor(defaultExporter);\n      this.activeSpanProcessor = batchProcessor;\n    } else {\n      this.activeSpanProcessor = new NoopSpanProcessor();\n    }\n  }\n\n  getTracer(\n    name: string,\n    version?: string,\n    options?: { schemaUrl?: string }\n  ): Tracer {\n    const key = `${name}@${version || ''}:${options?.schemaUrl || ''}`;\n    if (!this._tracers.has(key)) {\n      this._tracers.set(\n        key,\n        new Tracer(\n          { name, version, schemaUrl: options?.schemaUrl },\n          this._config,\n          this\n        )\n      );\n    }\n\n    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n    return this._tracers.get(key)!;\n  }\n\n  /**\n   * Adds a new {@link SpanProcessor} to this tracer.\n   * @param spanProcessor the new SpanProcessor to be added.\n   */\n  addSpanProcessor(spanProcessor: SpanProcessor): void {\n    if (this._registeredSpanProcessors.length === 0) {\n      // since we might have enabled by default a batchProcessor, we disable it\n      // before adding the new one\n      this.activeSpanProcessor\n        .shutdown()\n        .catch(err =>\n          diag.error(\n            'Error while trying to shutdown current span processor',\n            err\n          )\n        );\n    }\n    this._registeredSpanProcessors.push(spanProcessor);\n    this.activeSpanProcessor = new MultiSpanProcessor(\n      this._registeredSpanProcessors\n    );\n  }\n\n  getActiveSpanProcessor(): SpanProcessor {\n    return this.activeSpanProcessor;\n  }\n\n  /**\n   * Register this TracerProvider for use with the OpenTelemetry API.\n   * Undefined values may be replaced with defaults, and\n   * null values will be skipped.\n   *\n   * @param config Configuration object for SDK registration\n   */\n  register(config: SDKRegistrationConfig = {}): void {\n    trace.setGlobalTracerProvider(this);\n    if (config.propagator === undefined) {\n      config.propagator = this._buildPropagatorFromEnv();\n    }\n\n    if (config.contextManager) {\n      context.setGlobalContextManager(config.contextManager);\n    }\n\n    if (config.propagator) {\n      propagation.setGlobalPropagator(config.propagator);\n    }\n  }\n\n  forceFlush(): Promise<void> {\n    const timeout = this._config.forceFlushTimeoutMillis;\n    const promises = this._registeredSpanProcessors.map(\n      (spanProcessor: SpanProcessor) => {\n        return new Promise(resolve => {\n          let state: ForceFlushState;\n          const timeoutInterval = setTimeout(() => {\n            resolve(\n              new Error(\n                `Span processor did not completed within timeout period of ${timeout} ms`\n              )\n            );\n            state = ForceFlushState.timeout;\n          }, timeout);\n\n          spanProcessor\n            .forceFlush()\n            .then(() => {\n              clearTimeout(timeoutInterval);\n              if (state !== ForceFlushState.timeout) {\n                state = ForceFlushState.resolved;\n                resolve(state);\n              }\n            })\n            .catch(error => {\n              clearTimeout(timeoutInterval);\n              state = ForceFlushState.error;\n              resolve(error);\n            });\n        });\n      }\n    );\n\n    return new Promise<void>((resolve, reject) => {\n      Promise.all(promises)\n        .then(results => {\n          const errors = results.filter(\n            result => result !== ForceFlushState.resolved\n          );\n          if (errors.length > 0) {\n            reject(errors);\n          } else {\n            resolve();\n          }\n        })\n        .catch(error => reject([error]));\n    });\n  }\n\n  shutdown(): Promise<void> {\n    return this.activeSpanProcessor.shutdown();\n  }\n\n  /**\n   * TS cannot yet infer the type of this.constructor:\n   * https://github.com/Microsoft/TypeScript/issues/3841#issuecomment-337560146\n   * There is no need to override either of the getters in your child class.\n   * The type of the registered component maps should be the same across all\n   * classes in the inheritance tree.\n   */\n  protected _getPropagator(name: string): TextMapPropagator | undefined {\n    return (\n      this.constructor as typeof BasicTracerProvider\n    )._registeredPropagators.get(name)?.();\n  }\n\n  protected _getSpanExporter(name: string): SpanExporter | undefined {\n    return (\n      this.constructor as typeof BasicTracerProvider\n    )._registeredExporters.get(name)?.();\n  }\n\n  protected _buildPropagatorFromEnv(): TextMapPropagator | undefined {\n    // per spec, propagators from env must be deduplicated\n    const uniquePropagatorNames = Array.from(\n      new Set(getEnv().OTEL_PROPAGATORS)\n    );\n\n    const propagators = uniquePropagatorNames.map(name => {\n      const propagator = this._getPropagator(name);\n      if (!propagator) {\n        diag.warn(\n          `Propagator \"${name}\" requested through environment variable is unavailable.`\n        );\n      }\n\n      return propagator;\n    });\n    const validPropagators = propagators.reduce<TextMapPropagator[]>(\n      (list, item) => {\n        if (item) {\n          list.push(item);\n        }\n        return list;\n      },\n      []\n    );\n\n    if (validPropagators.length === 0) {\n      return;\n    } else if (uniquePropagatorNames.length === 1) {\n      return validPropagators[0];\n    } else {\n      return new CompositePropagator({\n        propagators: validPropagators,\n      });\n    }\n  }\n\n  protected _buildExporterFromEnv(): SpanExporter | undefined {\n    const exporterName = getEnv().OTEL_TRACES_EXPORTER;\n    if (exporterName === 'none' || exporterName === '') return;\n    const exporter = this._getSpanExporter(exporterName);\n    if (!exporter) {\n      diag.error(\n        `Exporter \"${exporterName}\" requested through environment variable is unavailable.`\n      );\n    }\n    return exporter;\n  }\n}\n"]}