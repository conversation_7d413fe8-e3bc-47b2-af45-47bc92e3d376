// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.aiplatform.v1;

import "google/api/field_behavior.proto";
import "google/protobuf/timestamp.proto";
import "google/rpc/status.proto";

option csharp_namespace = "Google.Cloud.AIPlatform.V1";
option go_package = "cloud.google.com/go/aiplatform/apiv1/aiplatformpb;aiplatformpb";
option java_multiple_files = true;
option java_outer_classname = "OperationProto";
option java_package = "com.google.cloud.aiplatform.v1";
option php_namespace = "Google\\Cloud\\AIPlatform\\V1";
option ruby_package = "Google::Cloud::AIPlatform::V1";

// Generic Metadata shared by all operations.
message GenericOperationMetadata {
  // Output only. Partial failures encountered.
  // E.g. single files that couldn't be read.
  // This field should never exceed 20 entries.
  // Status details field will contain standard Google Cloud error details.
  repeated google.rpc.Status partial_failures = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Time when the operation was created.
  google.protobuf.Timestamp create_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Time when the operation was updated for the last time.
  // If the operation has finished (successfully or not), this is the finish
  // time.
  google.protobuf.Timestamp update_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Details of operations that perform deletes of any entities.
message DeleteOperationMetadata {
  // The common part of the operation metadata.
  GenericOperationMetadata generic_metadata = 1;
}
