{"version": 3, "sources": ["../src/model.ts"], "sourcesContent": ["/**\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  Action,\n  ActionMetadata,\n  defineAction,\n  GenkitError,\n  getStreamingCallback,\n  SimpleMiddleware,\n  StreamingCallback,\n  z,\n} from '@genkit-ai/core';\nimport { logger } from '@genkit-ai/core/logging';\nimport { Registry } from '@genkit-ai/core/registry';\nimport { toJsonSchema } from '@genkit-ai/core/schema';\nimport { performance } from 'node:perf_hooks';\nimport {\n  CustomPart,\n  CustomPartSchema,\n  DataPart,\n  DataPartSchema,\n  DocumentDataSchema,\n  MediaPart,\n  MediaPartSchema,\n  ReasoningPartSchema,\n  TextPart,\n  TextPartSchema,\n  ToolRequestPart,\n  ToolRequestPartSchema,\n  ToolResponsePart,\n  ToolResponsePartSchema,\n} from './document.js';\nimport {\n  augmentWithContext,\n  simulateConstrainedGeneration,\n  validateSupport,\n} from './model/middleware.js';\nexport { defineGenerateAction } from './generate/action.js';\n// Export imports from document.js to retain API compatibility\nexport {\n  CustomPartSchema,\n  DataPartSchema,\n  MediaPartSchema,\n  simulateConstrainedGeneration,\n  TextPartSchema,\n  ToolRequestPartSchema,\n  ToolResponsePartSchema,\n  type CustomPart,\n  type DataPart,\n  type MediaPart,\n  type TextPart,\n  type ToolRequestPart,\n  type ToolResponsePart,\n};\n\n//\n// IMPORTANT: Please keep type definitions in sync with\n//   genkit-tools/src/types/model.ts\n//\n\n/**\n * Zod schema of message part.\n */\nexport const PartSchema = z.union([\n  TextPartSchema,\n  MediaPartSchema,\n  ToolRequestPartSchema,\n  ToolResponsePartSchema,\n  DataPartSchema,\n  CustomPartSchema,\n  ReasoningPartSchema,\n]);\n\n/**\n * Message part.\n */\nexport type Part = z.infer<typeof PartSchema>;\n\n/**\n * Zod schema of a message role.\n */\nexport const RoleSchema = z.enum(['system', 'user', 'model', 'tool']);\n\n/**\n * Message role.\n */\nexport type Role = z.infer<typeof RoleSchema>;\n\n/**\n * Zod schema of a message.\n */\nexport const MessageSchema = z.object({\n  role: RoleSchema,\n  content: z.array(PartSchema),\n  metadata: z.record(z.unknown()).optional(),\n});\n\n/**\n * Model message data.\n */\nexport type MessageData = z.infer<typeof MessageSchema>;\n\n/**\n * Zod schema of model info metadata.\n */\nexport const ModelInfoSchema = z.object({\n  /** Acceptable names for this model (e.g. different versions). */\n  versions: z.array(z.string()).optional(),\n  /** Friendly label for this model (e.g. \"Google AI - Gemini Pro\") */\n  label: z.string().optional(),\n  /** Model Specific configuration. */\n  configSchema: z.record(z.any()).optional(),\n  /** Supported model capabilities. */\n  supports: z\n    .object({\n      /** Model can process historical messages passed with a prompt. */\n      multiturn: z.boolean().optional(),\n      /** Model can process media as part of the prompt (multimodal input). */\n      media: z.boolean().optional(),\n      /** Model can perform tool calls. */\n      tools: z.boolean().optional(),\n      /** Model can accept messages with role \"system\". */\n      systemRole: z.boolean().optional(),\n      /** Model can output this type of data. */\n      output: z.array(z.string()).optional(),\n      /** Model supports output in these content types. */\n      contentType: z.array(z.string()).optional(),\n      /** Model can natively support document-based context grounding. */\n      context: z.boolean().optional(),\n      /** Model can natively support constrained generation. */\n      constrained: z.enum(['none', 'all', 'no-tools']).optional(),\n      /** Model supports controlling tool choice, e.g. forced tool calling. */\n      toolChoice: z.boolean().optional(),\n    })\n    .optional(),\n  /** At which stage of development this model is.\n   * - `featured` models are recommended for general use.\n   * - `stable` models are well-tested and reliable.\n   * - `unstable` models are experimental and may change.\n   * - `legacy` models are no longer recommended for new projects.\n   * - `deprecated` models are deprecated by the provider and may be removed in future versions.\n   */\n  stage: z\n    .enum(['featured', 'stable', 'unstable', 'legacy', 'deprecated'])\n    .optional(),\n});\n\n/**\n * Model info metadata.\n */\nexport type ModelInfo = z.infer<typeof ModelInfoSchema>;\n\n/**\n * Zod schema of a tool definition.\n */\nexport const ToolDefinitionSchema = z.object({\n  name: z.string(),\n  description: z.string(),\n  inputSchema: z\n    .record(z.any())\n    .describe('Valid JSON Schema representing the input of the tool.')\n    .nullish(),\n  outputSchema: z\n    .record(z.any())\n    .describe('Valid JSON Schema describing the output of the tool.')\n    .nullish(),\n  metadata: z\n    .record(z.any())\n    .describe('additional metadata for this tool definition')\n    .optional(),\n});\n\n/**\n * Tool definition.\n */\nexport type ToolDefinition = z.infer<typeof ToolDefinitionSchema>;\n\n/**\n * Configuration parameter descriptions.\n */\nexport const GenerationCommonConfigDescriptions = {\n  temperature:\n    'Controls the degree of randomness in token selection. A lower value is ' +\n    'good for a more predictable response. A higher value leads to more ' +\n    'diverse or unexpected results.',\n  maxOutputTokens: 'The maximum number of tokens to include in the response.',\n  topK: 'The maximum number of tokens to consider when sampling.',\n  topP:\n    'Decides how many possible words to consider. A higher value means ' +\n    'that the model looks at more possible words, even the less likely ' +\n    'ones, which makes the generated text more diverse.',\n};\n\n/**\n * Zod schema of a common config object.\n */\nexport const GenerationCommonConfigSchema = z\n  .object({\n    version: z\n      .string()\n      .describe(\n        'A specific version of a model family, e.g. `gemini-2.0-flash` ' +\n          'for the `googleai` family.'\n      )\n      .optional(),\n    temperature: z\n      .number()\n      .describe(GenerationCommonConfigDescriptions.temperature)\n      .optional(),\n    maxOutputTokens: z\n      .number()\n      .describe(GenerationCommonConfigDescriptions.maxOutputTokens)\n      .optional(),\n    topK: z\n      .number()\n      .describe(GenerationCommonConfigDescriptions.topK)\n      .optional(),\n    topP: z\n      .number()\n      .describe(GenerationCommonConfigDescriptions.topP)\n      .optional(),\n    stopSequences: z\n      .array(z.string())\n      .length(5)\n      .describe(\n        'Set of character sequences (up to 5) that will stop output generation.'\n      )\n      .optional(),\n  })\n  .passthrough();\n\n/**\n * Common config object.\n */\nexport type GenerationCommonConfig = typeof GenerationCommonConfigSchema;\n\n/**\n * Zod schema of output config.\n */\nexport const OutputConfigSchema = z.object({\n  format: z.string().optional(),\n  schema: z.record(z.any()).optional(),\n  constrained: z.boolean().optional(),\n  contentType: z.string().optional(),\n});\n\n/**\n * Output config.\n */\nexport type OutputConfig = z.infer<typeof OutputConfigSchema>;\n\n/** ModelRequestSchema represents the parameters that are passed to a model when generating content. */\nexport const ModelRequestSchema = z.object({\n  messages: z.array(MessageSchema),\n  config: z.any().optional(),\n  tools: z.array(ToolDefinitionSchema).optional(),\n  toolChoice: z.enum(['auto', 'required', 'none']).optional(),\n  output: OutputConfigSchema.optional(),\n  docs: z.array(DocumentDataSchema).optional(),\n});\n/** ModelRequest represents the parameters that are passed to a model when generating content. */\nexport interface ModelRequest<\n  CustomOptionsSchema extends z.ZodTypeAny = z.ZodTypeAny,\n> extends z.infer<typeof ModelRequestSchema> {\n  config?: z.infer<CustomOptionsSchema>;\n}\n/**\n * Zod schema of a generate request.\n */\nexport const GenerateRequestSchema = ModelRequestSchema.extend({\n  /** @deprecated All responses now return a single candidate. This will always be `undefined`. */\n  candidates: z.number().optional(),\n});\n\n/**\n * Generate request data.\n */\nexport type GenerateRequestData = z.infer<typeof GenerateRequestSchema>;\n\n/**\n * Generate request.\n */\nexport interface GenerateRequest<\n  CustomOptionsSchema extends z.ZodTypeAny = z.ZodTypeAny,\n> extends z.infer<typeof GenerateRequestSchema> {\n  config?: z.infer<CustomOptionsSchema>;\n}\n\n/**\n * Zod schema of usage info from a generate request.\n */\nexport const GenerationUsageSchema = z.object({\n  inputTokens: z.number().optional(),\n  outputTokens: z.number().optional(),\n  totalTokens: z.number().optional(),\n  inputCharacters: z.number().optional(),\n  outputCharacters: z.number().optional(),\n  inputImages: z.number().optional(),\n  outputImages: z.number().optional(),\n  inputVideos: z.number().optional(),\n  outputVideos: z.number().optional(),\n  inputAudioFiles: z.number().optional(),\n  outputAudioFiles: z.number().optional(),\n  custom: z.record(z.number()).optional(),\n  thoughtsTokens: z.number().optional(),\n  cachedContentTokens: z.number().optional(),\n});\n\n/**\n * Usage info from a generate request.\n */\nexport type GenerationUsage = z.infer<typeof GenerationUsageSchema>;\n\n/** Model response finish reason enum. */\nexport const FinishReasonSchema = z.enum([\n  'stop',\n  'length',\n  'blocked',\n  'interrupted',\n  'other',\n  'unknown',\n]);\n\n/** @deprecated All responses now return a single candidate. Only the first candidate will be used if supplied. */\nexport const CandidateSchema = z.object({\n  index: z.number(),\n  message: MessageSchema,\n  usage: GenerationUsageSchema.optional(),\n  finishReason: FinishReasonSchema,\n  finishMessage: z.string().optional(),\n  custom: z.unknown(),\n});\n/** @deprecated All responses now return a single candidate. Only the first candidate will be used if supplied. */\nexport type CandidateData = z.infer<typeof CandidateSchema>;\n\n/** @deprecated All responses now return a single candidate. Only the first candidate will be used if supplied. */\nexport const CandidateErrorSchema = z.object({\n  index: z.number(),\n  code: z.enum(['blocked', 'other', 'unknown']),\n  message: z.string().optional(),\n});\n/** @deprecated All responses now return a single candidate. Only the first candidate will be used if supplied. */\nexport type CandidateError = z.infer<typeof CandidateErrorSchema>;\n\n/**\n * Zod schema of a model response.\n */\nexport const ModelResponseSchema = z.object({\n  message: MessageSchema.optional(),\n  finishReason: FinishReasonSchema,\n  finishMessage: z.string().optional(),\n  latencyMs: z.number().optional(),\n  usage: GenerationUsageSchema.optional(),\n  /** @deprecated use `raw` instead */\n  custom: z.unknown(),\n  raw: z.unknown(),\n  request: GenerateRequestSchema.optional(),\n});\n\n/**\n * Model response data.\n */\nexport type ModelResponseData = z.infer<typeof ModelResponseSchema>;\n\n/**\n * Zod schema of generaete response.\n */\nexport const GenerateResponseSchema = ModelResponseSchema.extend({\n  /** @deprecated All responses now return a single candidate. Only the first candidate will be used if supplied. Return `message`, `finishReason`, and `finishMessage` instead. */\n  candidates: z.array(CandidateSchema).optional(),\n  finishReason: FinishReasonSchema.optional(),\n});\n\n/**\n * Generate response data.\n */\nexport type GenerateResponseData = z.infer<typeof GenerateResponseSchema>;\n\n/** ModelResponseChunkSchema represents a chunk of content to stream to the client. */\nexport const ModelResponseChunkSchema = z.object({\n  role: RoleSchema.optional(),\n  /** index of the message this chunk belongs to. */\n  index: z.number().optional(),\n  /** The chunk of content to stream right now. */\n  content: z.array(PartSchema),\n  /** Model-specific extra information attached to this chunk. */\n  custom: z.unknown().optional(),\n  /** If true, the chunk includes all data from previous chunks. Otherwise, considered to be incremental. */\n  aggregated: z.boolean().optional(),\n});\nexport type ModelResponseChunkData = z.infer<typeof ModelResponseChunkSchema>;\n\nexport const GenerateResponseChunkSchema = ModelResponseChunkSchema;\nexport type GenerateResponseChunkData = z.infer<\n  typeof GenerateResponseChunkSchema\n>;\n\nexport type ModelAction<\n  CustomOptionsSchema extends z.ZodTypeAny = z.ZodTypeAny,\n> = Action<\n  typeof GenerateRequestSchema,\n  typeof GenerateResponseSchema,\n  typeof GenerateResponseChunkSchema\n> & {\n  __configSchema: CustomOptionsSchema;\n};\n\nexport type ModelMiddleware = SimpleMiddleware<\n  z.infer<typeof GenerateRequestSchema>,\n  z.infer<typeof GenerateResponseSchema>\n>;\n\nexport type DefineModelOptions<\n  CustomOptionsSchema extends z.ZodTypeAny = z.ZodTypeAny,\n> = {\n  name: string;\n  /** Known version names for this model, e.g. `gemini-1.0-pro-001`. */\n  versions?: string[];\n  /** Capabilities this model supports. */\n  supports?: ModelInfo['supports'];\n  /** Custom options schema for this model. */\n  configSchema?: CustomOptionsSchema;\n  /** Descriptive name for this model e.g. 'Google AI - Gemini Pro'. */\n  label?: string;\n  /** Middleware to be used with this model. */\n  use?: ModelMiddleware[];\n};\n\n/**\n * Defines a new model and adds it to the registry.\n */\nexport function defineModel<\n  CustomOptionsSchema extends z.ZodTypeAny = z.ZodTypeAny,\n>(\n  registry: Registry,\n  options: DefineModelOptions<CustomOptionsSchema>,\n  runner: (\n    request: GenerateRequest<CustomOptionsSchema>,\n    streamingCallback?: StreamingCallback<GenerateResponseChunkData>\n  ) => Promise<GenerateResponseData>\n): ModelAction<CustomOptionsSchema> {\n  const label = options.label || options.name;\n  const middleware: ModelMiddleware[] = [\n    ...(options.use || []),\n    validateSupport(options),\n  ];\n  if (!options?.supports?.context) middleware.push(augmentWithContext());\n  const constratedSimulator = simulateConstrainedGeneration();\n  middleware.push((req, next) => {\n    if (\n      !options?.supports?.constrained ||\n      options?.supports?.constrained === 'none' ||\n      (options?.supports?.constrained === 'no-tools' &&\n        (req.tools?.length ?? 0) > 0)\n    ) {\n      return constratedSimulator(req, next);\n    }\n    return next(req);\n  });\n  const act = defineAction(\n    registry,\n    {\n      actionType: 'model',\n      name: options.name,\n      description: label,\n      inputSchema: GenerateRequestSchema,\n      outputSchema: GenerateResponseSchema,\n      metadata: {\n        model: {\n          label,\n          customOptions: options.configSchema\n            ? toJsonSchema({ schema: options.configSchema })\n            : undefined,\n          versions: options.versions,\n          supports: options.supports,\n        },\n      },\n      use: middleware,\n    },\n    (input) => {\n      const startTimeMs = performance.now();\n\n      return runner(input, getStreamingCallback(registry)).then((response) => {\n        const timedResponse = {\n          ...response,\n          latencyMs: performance.now() - startTimeMs,\n        };\n        return timedResponse;\n      });\n    }\n  );\n  Object.assign(act, {\n    __configSchema: options.configSchema || z.unknown(),\n  });\n  return act as ModelAction<CustomOptionsSchema>;\n}\n\nexport interface ModelReference<CustomOptions extends z.ZodTypeAny> {\n  name: string;\n  configSchema?: CustomOptions;\n  info?: ModelInfo;\n  version?: string;\n  config?: z.infer<CustomOptions>;\n\n  withConfig(cfg: z.infer<CustomOptions>): ModelReference<CustomOptions>;\n  withVersion(version: string): ModelReference<CustomOptions>;\n}\n\n/**\n * Packages model information into ActionMetadata object.\n */\nexport function modelActionMetadata({\n  name,\n  info,\n  configSchema,\n}: {\n  name: string;\n  info?: ModelInfo;\n  configSchema?: z.ZodTypeAny;\n}): ActionMetadata {\n  return {\n    actionType: 'model',\n    name: name,\n    inputJsonSchema: toJsonSchema({ schema: GenerateRequestSchema }),\n    outputJsonSchema: toJsonSchema({ schema: GenerateResponseSchema }),\n    metadata: {\n      model: {\n        ...info,\n        customOptions: configSchema\n          ? toJsonSchema({ schema: configSchema })\n          : undefined,\n      },\n    },\n  } as ActionMetadata;\n}\n\n/** Cretes a model reference. */\nexport function modelRef<\n  CustomOptionsSchema extends z.ZodTypeAny = z.ZodTypeAny,\n>(\n  options: Omit<\n    ModelReference<CustomOptionsSchema>,\n    'withConfig' | 'withVersion'\n  >\n): ModelReference<CustomOptionsSchema> {\n  const ref: Partial<ModelReference<CustomOptionsSchema>> = { ...options };\n  ref.withConfig = (\n    cfg: z.infer<CustomOptionsSchema>\n  ): ModelReference<CustomOptionsSchema> => {\n    return modelRef({\n      ...options,\n      config: cfg,\n    });\n  };\n  ref.withVersion = (version: string): ModelReference<CustomOptionsSchema> => {\n    return modelRef({\n      ...options,\n      version,\n    });\n  };\n  return ref as ModelReference<CustomOptionsSchema>;\n}\n\n/** Container for counting usage stats for a single input/output {Part} */\ntype PartCounts = {\n  characters: number;\n  images: number;\n  videos: number;\n  audio: number;\n};\n\n/**\n * Calculates basic usage statistics from the given model inputs and outputs.\n */\nexport function getBasicUsageStats(\n  input: MessageData[],\n  response: MessageData | CandidateData[]\n): GenerationUsage {\n  const inputCounts = getPartCounts(input.flatMap((md) => md.content));\n  const outputCounts = getPartCounts(\n    Array.isArray(response)\n      ? response.flatMap((c) => c.message.content)\n      : response.content\n  );\n  return {\n    inputCharacters: inputCounts.characters,\n    inputImages: inputCounts.images,\n    inputVideos: inputCounts.videos,\n    inputAudioFiles: inputCounts.audio,\n    outputCharacters: outputCounts.characters,\n    outputImages: outputCounts.images,\n    outputVideos: outputCounts.videos,\n    outputAudioFiles: outputCounts.audio,\n  };\n}\n\nfunction getPartCounts(parts: Part[]): PartCounts {\n  return parts.reduce(\n    (counts, part) => {\n      const isImage =\n        part.media?.contentType?.startsWith('image') ||\n        part.media?.url?.startsWith('data:image');\n      const isVideo =\n        part.media?.contentType?.startsWith('video') ||\n        part.media?.url?.startsWith('data:video');\n      const isAudio =\n        part.media?.contentType?.startsWith('audio') ||\n        part.media?.url?.startsWith('data:audio');\n      return {\n        characters: counts.characters + (part.text?.length || 0),\n        images: counts.images + (isImage ? 1 : 0),\n        videos: counts.videos + (isVideo ? 1 : 0),\n        audio: counts.audio + (isAudio ? 1 : 0),\n      };\n    },\n    { characters: 0, images: 0, videos: 0, audio: 0 }\n  );\n}\n\nexport type ModelArgument<CustomOptions extends z.ZodTypeAny = z.ZodTypeAny> =\n  | ModelAction<CustomOptions>\n  | ModelReference<CustomOptions>\n  | string;\n\nexport interface ResolvedModel<\n  CustomOptions extends z.ZodTypeAny = z.ZodTypeAny,\n> {\n  modelAction: ModelAction;\n  config?: z.infer<CustomOptions>;\n  version?: string;\n}\n\nexport async function resolveModel<C extends z.ZodTypeAny = z.ZodTypeAny>(\n  registry: Registry,\n  model: ModelArgument<C> | undefined,\n  options?: { warnDeprecated?: boolean }\n): Promise<ResolvedModel<C>> {\n  let out: ResolvedModel<C>;\n  let modelId: string;\n\n  if (!model) {\n    model = await registry.lookupValue('defaultModel', 'defaultModel');\n  }\n  if (!model) {\n    throw new GenkitError({\n      status: 'INVALID_ARGUMENT',\n      message: 'Must supply a `model` to `generate()` calls.',\n    });\n  }\n  if (typeof model === 'string') {\n    modelId = model;\n    out = { modelAction: await registry.lookupAction(`/model/${model}`) };\n  } else if (model.hasOwnProperty('__action')) {\n    modelId = (model as ModelAction).__action.name;\n    out = { modelAction: model as ModelAction };\n  } else {\n    const ref = model as ModelReference<any>;\n    modelId = ref.name;\n    out = {\n      modelAction: (await registry.lookupAction(\n        `/model/${ref.name}`\n      )) as ModelAction,\n      config: {\n        ...ref.config,\n      },\n      version: ref.version,\n    };\n  }\n\n  if (!out.modelAction) {\n    throw new GenkitError({\n      status: 'NOT_FOUND',\n      message: `Model '${modelId}' not found`,\n    });\n  }\n\n  if (\n    options?.warnDeprecated &&\n    out.modelAction.__action.metadata?.model?.stage === 'deprecated'\n  ) {\n    logger.warn(\n      `Model '${out.modelAction.__action.name}' is deprecated and may be removed in a future release.`\n    );\n  }\n\n  return out;\n}\n\nexport const GenerateActionOutputConfig = z.object({\n  format: z.string().optional(),\n  contentType: z.string().optional(),\n  instructions: z.union([z.boolean(), z.string()]).optional(),\n  jsonSchema: z.any().optional(),\n  constrained: z.boolean().optional(),\n});\n\nexport const GenerateActionOptionsSchema = z.object({\n  /** A model name (e.g. `vertexai/gemini-1.0-pro`). */\n  model: z.string(),\n  /** Retrieved documents to be used as context for this generation. */\n  docs: z.array(DocumentDataSchema).optional(),\n  /** Conversation history for multi-turn prompting when supported by the underlying model. */\n  messages: z.array(MessageSchema),\n  /** List of registered tool names for this generation if supported by the underlying model. */\n  tools: z.array(z.string()).optional(),\n  /** Tool calling mode. `auto` lets the model decide whether to use tools, `required` forces the model to choose a tool, and `none` forces the model not to use any tools. Defaults to `auto`.  */\n  toolChoice: z.enum(['auto', 'required', 'none']).optional(),\n  /** Configuration for the generation request. */\n  config: z.any().optional(),\n  /** Configuration for the desired output of the request. Defaults to the model's default output if unspecified. */\n  output: GenerateActionOutputConfig.optional(),\n  /** Options for resuming an interrupted generation. */\n  resume: z\n    .object({\n      respond: z.array(ToolResponsePartSchema).optional(),\n      restart: z.array(ToolRequestPartSchema).optional(),\n      metadata: z.record(z.any()).optional(),\n    })\n    .optional(),\n  /** When true, return tool calls for manual processing instead of automatically resolving them. */\n  returnToolRequests: z.boolean().optional(),\n  /** Maximum number of tool call iterations that can be performed in a single generate call (default 5). */\n  maxTurns: z.number().optional(),\n});\nexport type GenerateActionOptions = z.infer<typeof GenerateActionOptionsSchema>;\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAgBA,kBASO;AACP,qBAAuB;AAEvB,oBAA6B;AAC7B,6BAA4B;AAC5B,sBAeO;AACP,wBAIO;AACP,oBAAqC;AA0B9B,MAAM,aAAa,cAAE,MAAM;AAAA,EAChC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,CAAC;AAUM,MAAM,aAAa,cAAE,KAAK,CAAC,UAAU,QAAQ,SAAS,MAAM,CAAC;AAU7D,MAAM,gBAAgB,cAAE,OAAO;AAAA,EACpC,MAAM;AAAA,EACN,SAAS,cAAE,MAAM,UAAU;AAAA,EAC3B,UAAU,cAAE,OAAO,cAAE,QAAQ,CAAC,EAAE,SAAS;AAC3C,CAAC;AAUM,MAAM,kBAAkB,cAAE,OAAO;AAAA;AAAA,EAEtC,UAAU,cAAE,MAAM,cAAE,OAAO,CAAC,EAAE,SAAS;AAAA;AAAA,EAEvC,OAAO,cAAE,OAAO,EAAE,SAAS;AAAA;AAAA,EAE3B,cAAc,cAAE,OAAO,cAAE,IAAI,CAAC,EAAE,SAAS;AAAA;AAAA,EAEzC,UAAU,cACP,OAAO;AAAA;AAAA,IAEN,WAAW,cAAE,QAAQ,EAAE,SAAS;AAAA;AAAA,IAEhC,OAAO,cAAE,QAAQ,EAAE,SAAS;AAAA;AAAA,IAE5B,OAAO,cAAE,QAAQ,EAAE,SAAS;AAAA;AAAA,IAE5B,YAAY,cAAE,QAAQ,EAAE,SAAS;AAAA;AAAA,IAEjC,QAAQ,cAAE,MAAM,cAAE,OAAO,CAAC,EAAE,SAAS;AAAA;AAAA,IAErC,aAAa,cAAE,MAAM,cAAE,OAAO,CAAC,EAAE,SAAS;AAAA;AAAA,IAE1C,SAAS,cAAE,QAAQ,EAAE,SAAS;AAAA;AAAA,IAE9B,aAAa,cAAE,KAAK,CAAC,QAAQ,OAAO,UAAU,CAAC,EAAE,SAAS;AAAA;AAAA,IAE1D,YAAY,cAAE,QAAQ,EAAE,SAAS;AAAA,EACnC,CAAC,EACA,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQZ,OAAO,cACJ,KAAK,CAAC,YAAY,UAAU,YAAY,UAAU,YAAY,CAAC,EAC/D,SAAS;AACd,CAAC;AAUM,MAAM,uBAAuB,cAAE,OAAO;AAAA,EAC3C,MAAM,cAAE,OAAO;AAAA,EACf,aAAa,cAAE,OAAO;AAAA,EACtB,aAAa,cACV,OAAO,cAAE,IAAI,CAAC,EACd,SAAS,uDAAuD,EAChE,QAAQ;AAAA,EACX,cAAc,cACX,OAAO,cAAE,IAAI,CAAC,EACd,SAAS,sDAAsD,EAC/D,QAAQ;AAAA,EACX,UAAU,cACP,OAAO,cAAE,IAAI,CAAC,EACd,SAAS,8CAA8C,EACvD,SAAS;AACd,CAAC;AAUM,MAAM,qCAAqC;AAAA,EAChD,aACE;AAAA,EAGF,iBAAiB;AAAA,EACjB,MAAM;AAAA,EACN,MACE;AAGJ;AAKO,MAAM,+BAA+B,cACzC,OAAO;AAAA,EACN,SAAS,cACN,OAAO,EACP;AAAA,IACC;AAAA,EAEF,EACC,SAAS;AAAA,EACZ,aAAa,cACV,OAAO,EACP,SAAS,mCAAmC,WAAW,EACvD,SAAS;AAAA,EACZ,iBAAiB,cACd,OAAO,EACP,SAAS,mCAAmC,eAAe,EAC3D,SAAS;AAAA,EACZ,MAAM,cACH,OAAO,EACP,SAAS,mCAAmC,IAAI,EAChD,SAAS;AAAA,EACZ,MAAM,cACH,OAAO,EACP,SAAS,mCAAmC,IAAI,EAChD,SAAS;AAAA,EACZ,eAAe,cACZ,MAAM,cAAE,OAAO,CAAC,EAChB,OAAO,CAAC,EACR;AAAA,IACC;AAAA,EACF,EACC,SAAS;AACd,CAAC,EACA,YAAY;AAUR,MAAM,qBAAqB,cAAE,OAAO;AAAA,EACzC,QAAQ,cAAE,OAAO,EAAE,SAAS;AAAA,EAC5B,QAAQ,cAAE,OAAO,cAAE,IAAI,CAAC,EAAE,SAAS;AAAA,EACnC,aAAa,cAAE,QAAQ,EAAE,SAAS;AAAA,EAClC,aAAa,cAAE,OAAO,EAAE,SAAS;AACnC,CAAC;AAQM,MAAM,qBAAqB,cAAE,OAAO;AAAA,EACzC,UAAU,cAAE,MAAM,aAAa;AAAA,EAC/B,QAAQ,cAAE,IAAI,EAAE,SAAS;AAAA,EACzB,OAAO,cAAE,MAAM,oBAAoB,EAAE,SAAS;AAAA,EAC9C,YAAY,cAAE,KAAK,CAAC,QAAQ,YAAY,MAAM,CAAC,EAAE,SAAS;AAAA,EAC1D,QAAQ,mBAAmB,SAAS;AAAA,EACpC,MAAM,cAAE,MAAM,kCAAkB,EAAE,SAAS;AAC7C,CAAC;AAUM,MAAM,wBAAwB,mBAAmB,OAAO;AAAA;AAAA,EAE7D,YAAY,cAAE,OAAO,EAAE,SAAS;AAClC,CAAC;AAmBM,MAAM,wBAAwB,cAAE,OAAO;AAAA,EAC5C,aAAa,cAAE,OAAO,EAAE,SAAS;AAAA,EACjC,cAAc,cAAE,OAAO,EAAE,SAAS;AAAA,EAClC,aAAa,cAAE,OAAO,EAAE,SAAS;AAAA,EACjC,iBAAiB,cAAE,OAAO,EAAE,SAAS;AAAA,EACrC,kBAAkB,cAAE,OAAO,EAAE,SAAS;AAAA,EACtC,aAAa,cAAE,OAAO,EAAE,SAAS;AAAA,EACjC,cAAc,cAAE,OAAO,EAAE,SAAS;AAAA,EAClC,aAAa,cAAE,OAAO,EAAE,SAAS;AAAA,EACjC,cAAc,cAAE,OAAO,EAAE,SAAS;AAAA,EAClC,iBAAiB,cAAE,OAAO,EAAE,SAAS;AAAA,EACrC,kBAAkB,cAAE,OAAO,EAAE,SAAS;AAAA,EACtC,QAAQ,cAAE,OAAO,cAAE,OAAO,CAAC,EAAE,SAAS;AAAA,EACtC,gBAAgB,cAAE,OAAO,EAAE,SAAS;AAAA,EACpC,qBAAqB,cAAE,OAAO,EAAE,SAAS;AAC3C,CAAC;AAQM,MAAM,qBAAqB,cAAE,KAAK;AAAA,EACvC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,CAAC;AAGM,MAAM,kBAAkB,cAAE,OAAO;AAAA,EACtC,OAAO,cAAE,OAAO;AAAA,EAChB,SAAS;AAAA,EACT,OAAO,sBAAsB,SAAS;AAAA,EACtC,cAAc;AAAA,EACd,eAAe,cAAE,OAAO,EAAE,SAAS;AAAA,EACnC,QAAQ,cAAE,QAAQ;AACpB,CAAC;AAKM,MAAM,uBAAuB,cAAE,OAAO;AAAA,EAC3C,OAAO,cAAE,OAAO;AAAA,EAChB,MAAM,cAAE,KAAK,CAAC,WAAW,SAAS,SAAS,CAAC;AAAA,EAC5C,SAAS,cAAE,OAAO,EAAE,SAAS;AAC/B,CAAC;AAOM,MAAM,sBAAsB,cAAE,OAAO;AAAA,EAC1C,SAAS,cAAc,SAAS;AAAA,EAChC,cAAc;AAAA,EACd,eAAe,cAAE,OAAO,EAAE,SAAS;AAAA,EACnC,WAAW,cAAE,OAAO,EAAE,SAAS;AAAA,EAC/B,OAAO,sBAAsB,SAAS;AAAA;AAAA,EAEtC,QAAQ,cAAE,QAAQ;AAAA,EAClB,KAAK,cAAE,QAAQ;AAAA,EACf,SAAS,sBAAsB,SAAS;AAC1C,CAAC;AAUM,MAAM,yBAAyB,oBAAoB,OAAO;AAAA;AAAA,EAE/D,YAAY,cAAE,MAAM,eAAe,EAAE,SAAS;AAAA,EAC9C,cAAc,mBAAmB,SAAS;AAC5C,CAAC;AAQM,MAAM,2BAA2B,cAAE,OAAO;AAAA,EAC/C,MAAM,WAAW,SAAS;AAAA;AAAA,EAE1B,OAAO,cAAE,OAAO,EAAE,SAAS;AAAA;AAAA,EAE3B,SAAS,cAAE,MAAM,UAAU;AAAA;AAAA,EAE3B,QAAQ,cAAE,QAAQ,EAAE,SAAS;AAAA;AAAA,EAE7B,YAAY,cAAE,QAAQ,EAAE,SAAS;AACnC,CAAC;AAGM,MAAM,8BAA8B;AAuCpC,SAAS,YAGd,UACA,SACA,QAIkC;AAClC,QAAM,QAAQ,QAAQ,SAAS,QAAQ;AACvC,QAAM,aAAgC;AAAA,IACpC,GAAI,QAAQ,OAAO,CAAC;AAAA,QACpB,mCAAgB,OAAO;AAAA,EACzB;AACA,MAAI,CAAC,SAAS,UAAU,QAAS,YAAW,SAAK,sCAAmB,CAAC;AACrE,QAAM,0BAAsB,iDAA8B;AAC1D,aAAW,KAAK,CAAC,KAAK,SAAS;AAC7B,QACE,CAAC,SAAS,UAAU,eACpB,SAAS,UAAU,gBAAgB,UAClC,SAAS,UAAU,gBAAgB,eACjC,IAAI,OAAO,UAAU,KAAK,GAC7B;AACA,aAAO,oBAAoB,KAAK,IAAI;AAAA,IACtC;AACA,WAAO,KAAK,GAAG;AAAA,EACjB,CAAC;AACD,QAAM,UAAM;AAAA,IACV;AAAA,IACA;AAAA,MACE,YAAY;AAAA,MACZ,MAAM,QAAQ;AAAA,MACd,aAAa;AAAA,MACb,aAAa;AAAA,MACb,cAAc;AAAA,MACd,UAAU;AAAA,QACR,OAAO;AAAA,UACL;AAAA,UACA,eAAe,QAAQ,mBACnB,4BAAa,EAAE,QAAQ,QAAQ,aAAa,CAAC,IAC7C;AAAA,UACJ,UAAU,QAAQ;AAAA,UAClB,UAAU,QAAQ;AAAA,QACpB;AAAA,MACF;AAAA,MACA,KAAK;AAAA,IACP;AAAA,IACA,CAAC,UAAU;AACT,YAAM,cAAc,mCAAY,IAAI;AAEpC,aAAO,OAAO,WAAO,kCAAqB,QAAQ,CAAC,EAAE,KAAK,CAAC,aAAa;AACtE,cAAM,gBAAgB;AAAA,UACpB,GAAG;AAAA,UACH,WAAW,mCAAY,IAAI,IAAI;AAAA,QACjC;AACA,eAAO;AAAA,MACT,CAAC;AAAA,IACH;AAAA,EACF;AACA,SAAO,OAAO,KAAK;AAAA,IACjB,gBAAgB,QAAQ,gBAAgB,cAAE,QAAQ;AAAA,EACpD,CAAC;AACD,SAAO;AACT;AAgBO,SAAS,oBAAoB;AAAA,EAClC;AAAA,EACA;AAAA,EACA;AACF,GAImB;AACjB,SAAO;AAAA,IACL,YAAY;AAAA,IACZ;AAAA,IACA,qBAAiB,4BAAa,EAAE,QAAQ,sBAAsB,CAAC;AAAA,IAC/D,sBAAkB,4BAAa,EAAE,QAAQ,uBAAuB,CAAC;AAAA,IACjE,UAAU;AAAA,MACR,OAAO;AAAA,QACL,GAAG;AAAA,QACH,eAAe,mBACX,4BAAa,EAAE,QAAQ,aAAa,CAAC,IACrC;AAAA,MACN;AAAA,IACF;AAAA,EACF;AACF;AAGO,SAAS,SAGd,SAIqC;AACrC,QAAM,MAAoD,EAAE,GAAG,QAAQ;AACvE,MAAI,aAAa,CACf,QACwC;AACxC,WAAO,SAAS;AAAA,MACd,GAAG;AAAA,MACH,QAAQ;AAAA,IACV,CAAC;AAAA,EACH;AACA,MAAI,cAAc,CAAC,YAAyD;AAC1E,WAAO,SAAS;AAAA,MACd,GAAG;AAAA,MACH;AAAA,IACF,CAAC;AAAA,EACH;AACA,SAAO;AACT;AAaO,SAAS,mBACd,OACA,UACiB;AACjB,QAAM,cAAc,cAAc,MAAM,QAAQ,CAAC,OAAO,GAAG,OAAO,CAAC;AACnE,QAAM,eAAe;AAAA,IACnB,MAAM,QAAQ,QAAQ,IAClB,SAAS,QAAQ,CAAC,MAAM,EAAE,QAAQ,OAAO,IACzC,SAAS;AAAA,EACf;AACA,SAAO;AAAA,IACL,iBAAiB,YAAY;AAAA,IAC7B,aAAa,YAAY;AAAA,IACzB,aAAa,YAAY;AAAA,IACzB,iBAAiB,YAAY;AAAA,IAC7B,kBAAkB,aAAa;AAAA,IAC/B,cAAc,aAAa;AAAA,IAC3B,cAAc,aAAa;AAAA,IAC3B,kBAAkB,aAAa;AAAA,EACjC;AACF;AAEA,SAAS,cAAc,OAA2B;AAChD,SAAO,MAAM;AAAA,IACX,CAAC,QAAQ,SAAS;AAChB,YAAM,UACJ,KAAK,OAAO,aAAa,WAAW,OAAO,KAC3C,KAAK,OAAO,KAAK,WAAW,YAAY;AAC1C,YAAM,UACJ,KAAK,OAAO,aAAa,WAAW,OAAO,KAC3C,KAAK,OAAO,KAAK,WAAW,YAAY;AAC1C,YAAM,UACJ,KAAK,OAAO,aAAa,WAAW,OAAO,KAC3C,KAAK,OAAO,KAAK,WAAW,YAAY;AAC1C,aAAO;AAAA,QACL,YAAY,OAAO,cAAc,KAAK,MAAM,UAAU;AAAA,QACtD,QAAQ,OAAO,UAAU,UAAU,IAAI;AAAA,QACvC,QAAQ,OAAO,UAAU,UAAU,IAAI;AAAA,QACvC,OAAO,OAAO,SAAS,UAAU,IAAI;AAAA,MACvC;AAAA,IACF;AAAA,IACA,EAAE,YAAY,GAAG,QAAQ,GAAG,QAAQ,GAAG,OAAO,EAAE;AAAA,EAClD;AACF;AAeA,eAAsB,aACpB,UACA,OACA,SAC2B;AAC3B,MAAI;AACJ,MAAI;AAEJ,MAAI,CAAC,OAAO;AACV,YAAQ,MAAM,SAAS,YAAY,gBAAgB,cAAc;AAAA,EACnE;AACA,MAAI,CAAC,OAAO;AACV,UAAM,IAAI,wBAAY;AAAA,MACpB,QAAQ;AAAA,MACR,SAAS;AAAA,IACX,CAAC;AAAA,EACH;AACA,MAAI,OAAO,UAAU,UAAU;AAC7B,cAAU;AACV,UAAM,EAAE,aAAa,MAAM,SAAS,aAAa,UAAU,KAAK,EAAE,EAAE;AAAA,EACtE,WAAW,MAAM,eAAe,UAAU,GAAG;AAC3C,cAAW,MAAsB,SAAS;AAC1C,UAAM,EAAE,aAAa,MAAqB;AAAA,EAC5C,OAAO;AACL,UAAM,MAAM;AACZ,cAAU,IAAI;AACd,UAAM;AAAA,MACJ,aAAc,MAAM,SAAS;AAAA,QAC3B,UAAU,IAAI,IAAI;AAAA,MACpB;AAAA,MACA,QAAQ;AAAA,QACN,GAAG,IAAI;AAAA,MACT;AAAA,MACA,SAAS,IAAI;AAAA,IACf;AAAA,EACF;AAEA,MAAI,CAAC,IAAI,aAAa;AACpB,UAAM,IAAI,wBAAY;AAAA,MACpB,QAAQ;AAAA,MACR,SAAS,UAAU,OAAO;AAAA,IAC5B,CAAC;AAAA,EACH;AAEA,MACE,SAAS,kBACT,IAAI,YAAY,SAAS,UAAU,OAAO,UAAU,cACpD;AACA,0BAAO;AAAA,MACL,UAAU,IAAI,YAAY,SAAS,IAAI;AAAA,IACzC;AAAA,EACF;AAEA,SAAO;AACT;AAEO,MAAM,6BAA6B,cAAE,OAAO;AAAA,EACjD,QAAQ,cAAE,OAAO,EAAE,SAAS;AAAA,EAC5B,aAAa,cAAE,OAAO,EAAE,SAAS;AAAA,EACjC,cAAc,cAAE,MAAM,CAAC,cAAE,QAAQ,GAAG,cAAE,OAAO,CAAC,CAAC,EAAE,SAAS;AAAA,EAC1D,YAAY,cAAE,IAAI,EAAE,SAAS;AAAA,EAC7B,aAAa,cAAE,QAAQ,EAAE,SAAS;AACpC,CAAC;AAEM,MAAM,8BAA8B,cAAE,OAAO;AAAA;AAAA,EAElD,OAAO,cAAE,OAAO;AAAA;AAAA,EAEhB,MAAM,cAAE,MAAM,kCAAkB,EAAE,SAAS;AAAA;AAAA,EAE3C,UAAU,cAAE,MAAM,aAAa;AAAA;AAAA,EAE/B,OAAO,cAAE,MAAM,cAAE,OAAO,CAAC,EAAE,SAAS;AAAA;AAAA,EAEpC,YAAY,cAAE,KAAK,CAAC,QAAQ,YAAY,MAAM,CAAC,EAAE,SAAS;AAAA;AAAA,EAE1D,QAAQ,cAAE,IAAI,EAAE,SAAS;AAAA;AAAA,EAEzB,QAAQ,2BAA2B,SAAS;AAAA;AAAA,EAE5C,QAAQ,cACL,OAAO;AAAA,IACN,SAAS,cAAE,MAAM,sCAAsB,EAAE,SAAS;AAAA,IAClD,SAAS,cAAE,MAAM,qCAAqB,EAAE,SAAS;AAAA,IACjD,UAAU,cAAE,OAAO,cAAE,IAAI,CAAC,EAAE,SAAS;AAAA,EACvC,CAAC,EACA,SAAS;AAAA;AAAA,EAEZ,oBAAoB,cAAE,QAAQ,EAAE,SAAS;AAAA;AAAA,EAEzC,UAAU,cAAE,OAAO,EAAE,SAAS;AAChC,CAAC;", "names": []}