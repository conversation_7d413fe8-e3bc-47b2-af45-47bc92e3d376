/**
 * اختبارات للتأكد من عمل نماذج Vertex AI بشكل صحيح
 * 
 * يتضمن هذا الملف اختبارات شاملة لجميع النماذج المتاحة
 * والتأكد من صحة التكوين والاستجابات
 */

import { 
  getModel, 
  listAvailableModels, 
  isModelAvailable, 
  getModelInfo 
} from '../vertex-ai/config.js';

/**
 * اختبار التكوين الأساسي
 */
function testBasicConfiguration() {
  console.log('🧪 اختبار التكوين الأساسي...');
  
  try {
    // اختبار قائمة النماذج المتاحة
    const availableModels = listAvailableModels();
    console.log('✅ تم الحصول على قائمة النماذج بنجاح');
    console.log(`📊 إجمالي النماذج: ${Object.values(availableModels).flat().length}`);
    
    // اختبار معلومات النماذج
    const modelInfo = getModelInfo();
    console.log('✅ تم الحصول على معلومات النماذج بنجاح');
    console.log(`📈 نماذج النصوص: ${modelInfo.totalModels.text}`);
    console.log(`👁️ نماذج الرؤية: ${modelInfo.totalModels.vision}`);
    console.log(`🔗 نماذج التضمين: ${modelInfo.totalModels.embedding}`);
    console.log(`⚙️ النماذج المتخصصة: ${modelInfo.totalModels.specialized}`);
    
    return true;
  } catch (error) {
    console.error('❌ فشل في اختبار التكوين الأساسي:', error.message);
    return false;
  }
}

/**
 * اختبار توفر النماذج المختلفة
 */
function testModelAvailability() {
  console.log('\n🧪 اختبار توفر النماذج...');
  
  const testModels = [
    // نماذج النصوص
    { type: 'text', name: 'gemini.pro15', label: 'Gemini 1.5 Pro' },
    { type: 'text', name: 'claude.sonnet35v2', label: 'Claude 3.5 Sonnet v2' },
    { type: 'text', name: 'llama.llama31_70b', label: 'Llama 3.1 70B' },
    { type: 'text', name: 'mistral.mistralLarge', label: 'Mistral Large' },
    
    // نماذج الرؤية
    { type: 'vision', name: 'geminiVision15', label: 'Gemini 1.5 Vision' },
    { type: 'vision', name: 'llama32Vision90b', label: 'Llama 3.2 Vision 90B' },
    
    // نماذج التضمين
    { type: 'embedding', name: 'textEmbeddingMultilingual', label: 'Text Embedding Multilingual' },
    
    // النماذج المتخصصة
    { type: 'specialized', name: 'codey', label: 'Codey' },
    { type: 'specialized', name: 'codeGemma', label: 'CodeGemma' },
  ];
  
  let passedTests = 0;
  let totalTests = testModels.length;
  
  for (const testModel of testModels) {
    try {
      const isAvailable = isModelAvailable(testModel.type, testModel.name);
      if (isAvailable) {
        console.log(`✅ ${testModel.label}: متوفر`);
        passedTests++;
      } else {
        console.log(`⚠️ ${testModel.label}: غير متوفر`);
      }
    } catch (error) {
      console.log(`❌ ${testModel.label}: خطأ - ${error.message}`);
    }
  }
  
  console.log(`📊 النتيجة: ${passedTests}/${totalTests} نماذج متوفرة`);
  return passedTests > 0;
}

/**
 * اختبار الحصول على النماذج مع الإعدادات
 */
function testModelRetrieval() {
  console.log('\n🧪 اختبار الحصول على النماذج...');
  
  const testCases = [
    {
      type: 'text',
      name: 'gemini.flash',
      customSettings: { temperature: 0.5, maxOutputTokens: 500 },
      label: 'Gemini Flash مع إعدادات مخصصة'
    },
    {
      type: 'text',
      name: 'claude.haiku35',
      customSettings: {},
      label: 'Claude 3.5 Haiku مع إعدادات افتراضية'
    },
    {
      type: 'vision',
      name: 'geminiVision15',
      customSettings: { temperature: 0.3 },
      label: 'Gemini Vision مع إعدادات مخصصة'
    },
    {
      type: 'specialized',
      name: 'codey',
      customSettings: { temperature: 0.1, maxOutputTokens: 2048 },
      label: 'Codey مع إعدادات للبرمجة'
    }
  ];
  
  let passedTests = 0;
  
  for (const testCase of testCases) {
    try {
      const { model, settings } = getModel(
        testCase.type, 
        testCase.name, 
        testCase.customSettings
      );
      
      if (model && settings) {
        console.log(`✅ ${testCase.label}: تم الحصول على النموذج والإعدادات`);
        
        // التحقق من دمج الإعدادات
        if (testCase.customSettings.temperature) {
          if (settings.temperature === testCase.customSettings.temperature) {
            console.log(`   ✅ تم دمج الإعدادات المخصصة بنجاح`);
          } else {
            console.log(`   ⚠️ لم يتم دمج الإعدادات المخصصة بشكل صحيح`);
          }
        }
        
        passedTests++;
      } else {
        console.log(`❌ ${testCase.label}: فشل في الحصول على النموذج`);
      }
    } catch (error) {
      console.log(`❌ ${testCase.label}: خطأ - ${error.message}`);
    }
  }
  
  console.log(`📊 النتيجة: ${passedTests}/${testCases.length} اختبارات نجحت`);
  return passedTests === testCases.length;
}

/**
 * اختبار معالجة الأخطاء
 */
function testErrorHandling() {
  console.log('\n🧪 اختبار معالجة الأخطاء...');
  
  const errorTests = [
    {
      test: () => getModel('invalid_type', 'gemini.pro'),
      expectedError: 'نوع النموذج غير صالح',
      label: 'نوع نموذج غير صالح'
    },
    {
      test: () => getModel('text', 'invalid.model'),
      expectedError: 'النموذج غير موجود',
      label: 'نموذج غير موجود'
    },
    {
      test: () => getModel('vision', 'nonexistent_vision_model'),
      expectedError: 'نموذج الرؤية غير موجود',
      label: 'نموذج رؤية غير موجود'
    },
    {
      test: () => getModel('specialized', 'nonexistent_specialized_model'),
      expectedError: 'النموذج المتخصص غير موجود',
      label: 'نموذج متخصص غير موجود'
    }
  ];
  
  let passedTests = 0;
  
  for (const errorTest of errorTests) {
    try {
      errorTest.test();
      console.log(`❌ ${errorTest.label}: لم يتم رفع خطأ كما هو متوقع`);
    } catch (error) {
      if (error.message.includes(errorTest.expectedError)) {
        console.log(`✅ ${errorTest.label}: تم رفع الخطأ المتوقع`);
        passedTests++;
      } else {
        console.log(`⚠️ ${errorTest.label}: خطأ غير متوقع - ${error.message}`);
      }
    }
  }
  
  console.log(`📊 النتيجة: ${passedTests}/${errorTests.length} اختبارات أخطاء نجحت`);
  return passedTests === errorTests.length;
}

/**
 * اختبار الأداء والسرعة
 */
function testPerformance() {
  console.log('\n🧪 اختبار الأداء...');
  
  const performanceTests = [
    {
      name: 'الحصول على قائمة النماذج',
      test: () => listAvailableModels()
    },
    {
      name: 'الحصول على معلومات النماذج',
      test: () => getModelInfo()
    },
    {
      name: 'التحقق من توفر نموذج',
      test: () => isModelAvailable('text', 'gemini.pro15')
    },
    {
      name: 'الحصول على نموذج مع إعدادات',
      test: () => getModel('text', 'gemini.flash', { temperature: 0.7 })
    }
  ];
  
  let allTestsPassed = true;
  
  for (const perfTest of performanceTests) {
    const startTime = Date.now();
    
    try {
      perfTest.test();
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      if (duration < 100) { // أقل من 100ms
        console.log(`✅ ${perfTest.name}: ${duration}ms (سريع)`);
      } else if (duration < 500) { // أقل من 500ms
        console.log(`⚠️ ${perfTest.name}: ${duration}ms (متوسط)`);
      } else {
        console.log(`❌ ${perfTest.name}: ${duration}ms (بطيء)`);
        allTestsPassed = false;
      }
    } catch (error) {
      console.log(`❌ ${perfTest.name}: فشل - ${error.message}`);
      allTestsPassed = false;
    }
  }
  
  return allTestsPassed;
}

/**
 * تشغيل جميع الاختبارات
 */
async function runAllTests() {
  console.log('🚀 بدء اختبارات نماذج Vertex AI...\n');
  
  const tests = [
    { name: 'التكوين الأساسي', test: testBasicConfiguration },
    { name: 'توفر النماذج', test: testModelAvailability },
    { name: 'الحصول على النماذج', test: testModelRetrieval },
    { name: 'معالجة الأخطاء', test: testErrorHandling },
    { name: 'الأداء', test: testPerformance }
  ];
  
  let passedTests = 0;
  const results = [];
  
  for (const test of tests) {
    try {
      const result = test.test();
      if (result) {
        console.log(`\n✅ اختبار ${test.name}: نجح`);
        passedTests++;
        results.push({ name: test.name, status: 'نجح' });
      } else {
        console.log(`\n❌ اختبار ${test.name}: فشل`);
        results.push({ name: test.name, status: 'فشل' });
      }
    } catch (error) {
      console.log(`\n❌ اختبار ${test.name}: خطأ - ${error.message}`);
      results.push({ name: test.name, status: 'خطأ', error: error.message });
    }
  }
  
  // تقرير النتائج النهائي
  console.log('\n' + '='.repeat(50));
  console.log('📋 تقرير الاختبارات النهائي');
  console.log('='.repeat(50));
  
  results.forEach(result => {
    const status = result.status === 'نجح' ? '✅' : '❌';
    console.log(`${status} ${result.name}: ${result.status}`);
    if (result.error) {
      console.log(`   خطأ: ${result.error}`);
    }
  });
  
  console.log('\n📊 النتيجة الإجمالية:');
  console.log(`✅ نجح: ${passedTests}/${tests.length} اختبارات`);
  console.log(`❌ فشل: ${tests.length - passedTests}/${tests.length} اختبارات`);
  
  if (passedTests === tests.length) {
    console.log('\n🎉 جميع الاختبارات نجحت! النماذج جاهزة للاستخدام.');
  } else {
    console.log('\n⚠️ بعض الاختبارات فشلت. راجع الأخطاء أعلاه.');
  }
  
  return passedTests === tests.length;
}

// تشغيل الاختبارات إذا تم استدعاء الملف مباشرة
if (import.meta.url === `file://${process.argv[1]}`) {
  runAllTests().then(success => {
    process.exit(success ? 0 : 1);
  });
}

export {
  testBasicConfiguration,
  testModelAvailability,
  testModelRetrieval,
  testErrorHandling,
  testPerformance,
  runAllTests
};
