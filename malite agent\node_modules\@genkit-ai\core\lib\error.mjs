import { httpStatusCode } from "./statusTypes.js";
class GenkitError extends Error {
  source;
  status;
  detail;
  code;
  // For easy printing, we wrap the error with information like the source
  // and status, but that's redundant with JSON.
  originalMessage;
  constructor({
    status,
    message,
    detail,
    source
  }) {
    super(`${source ? `${source}: ` : ""}${status}: ${message}`);
    this.originalMessage = message;
    this.code = httpStatusCode(status);
    this.status = status;
    this.detail = detail;
    this.name = "GenkitError";
  }
  /**
   * Returns a JSON-serializable representation of this object.
   */
  toJSON() {
    return {
      // This error type is used by 3P authors with the field "detail",
      // but the actual Callable protocol value is "details"
      ...this.detail === void 0 ? {} : { details: this.detail },
      status: this.status,
      message: this.originalMessage
    };
  }
}
class UnstableApiError extends GenkitError {
  constructor(level, message) {
    super({
      status: "FAILED_PRECONDITION",
      message: `${message ? message + " " : ""}This API requires '${level}' stability level.

To use this feature, initialize Genkit using \`import {genkit} from "genkit/${level}"\`.`
    });
    this.name = "UnstableApiError";
  }
}
function assertUnstable(registry, level, message) {
  if (level === "beta" && registry.apiStability === "stable") {
    throw new UnstableApiError(level, message);
  }
}
class UserFacingError extends GenkitError {
  constructor(status, message, details) {
    super({ status, detail: details, message });
    super.name = "UserFacingError";
  }
}
function getHttpStatus(e) {
  if (e instanceof GenkitError) {
    return e.code;
  }
  return 500;
}
function getCallableJSON(e) {
  if (e instanceof GenkitError) {
    return e.toJSON();
  }
  return {
    message: "Internal Error",
    status: "INTERNAL"
  };
}
function getErrorMessage(e) {
  if (e instanceof Error) {
    return e.message;
  }
  return `${e}`;
}
function getErrorStack(e) {
  if (e instanceof Error) {
    return e.stack;
  }
  return void 0;
}
export {
  GenkitError,
  UnstableApiError,
  UserFacingError,
  assertUnstable,
  getCallableJSON,
  getErrorMessage,
  getErrorStack,
  getHttpStatus
};
//# sourceMappingURL=error.mjs.map