{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../../src/platform/node/index.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,6CAA8D;AAArD,oHAAA,qBAAqB,OAAA;AAAE,qGAAA,MAAM,OAAA;AACtC,2CAA2C;AAAlC,yGAAA,WAAW,OAAA;AACpB,iDAA8C;AAArC,4GAAA,WAAW,OAAA;AACpB,yDAAwD;AAA/C,sHAAA,iBAAiB,OAAA;AAC1B,6CAA8C;AAArC,4GAAA,aAAa,OAAA;AACtB,uCAAsC;AAA7B,oGAAA,QAAQ,OAAA;AACjB,2CAA0C;AAAjC,wGAAA,UAAU,OAAA", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport { getEnvWithoutDefaults, getEnv } from './environment';\nexport { _globalThis } from './globalThis';\nexport { hexToBase64 } from './hex-to-base64';\nexport { RandomIdGenerator } from './RandomIdGenerator';\nexport { otperformance } from './performance';\nexport { SDK_INFO } from './sdk-info';\nexport { unrefTimer } from './timer-util';\n"]}