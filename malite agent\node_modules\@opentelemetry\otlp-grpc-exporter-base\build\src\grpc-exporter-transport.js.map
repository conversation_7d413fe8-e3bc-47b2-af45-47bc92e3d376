{"version": 3, "file": "grpc-exporter-transport.js", "sourceRoot": "", "sources": ["../../src/grpc-exporter-transport.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAaH,gFAAgF;AAChF,MAAM,qBAAqB,GAAG,CAAC,CAAC;AAChC,MAAM,qBAAqB,GAAG,CAAC,CAAC;AAEhC,SAAS,iBAAiB,CAAC,WAA4B;IACrD,OAAO,WAAW,KAAK,MAAM,CAAC,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAC,qBAAqB,CAAC;AAChF,CAAC;AAED,SAAgB,yBAAyB;IACvC,iHAAiH;IACjH,MAAM,EACJ,WAAW;IACX,8DAA8D;MAC/D,GAAG,OAAO,CAAC,eAAe,CAAC,CAAC;IAC7B,OAAO,WAAW,CAAC,cAAc,EAAE,CAAC;AACtC,CAAC;AAPD,8DAOC;AAED,SAAgB,oBAAoB,CAClC,QAAiB,EACjB,UAAmB,EACnB,SAAkB;IAElB,iHAAiH;IACjH,MAAM,EACJ,WAAW;IACX,8DAA8D;MAC/D,GAAG,OAAO,CAAC,eAAe,CAAC,CAAC;IAC7B,OAAO,WAAW,CAAC,SAAS,CAAC,QAAQ,EAAE,UAAU,EAAE,SAAS,CAAC,CAAC;AAChE,CAAC;AAXD,oDAWC;AAED,SAAgB,mBAAmB;IACjC,iHAAiH;IACjH,MAAM,EACJ,QAAQ;IACR,8DAA8D;MAC/D,GAAG,OAAO,CAAC,eAAe,CAAC,CAAC;IAC7B,OAAO,IAAI,QAAQ,EAAE,CAAC;AACxB,CAAC;AAPD,kDAOC;AA2BD,MAAa,qBAAqB;IAIhC,YAAoB,WAA4C;QAA5C,gBAAW,GAAX,WAAW,CAAiC;IAAG,CAAC;IAEpE,QAAQ;;QACN,MAAA,IAAI,CAAC,OAAO,0CAAE,KAAK,EAAE,CAAC;IACxB,CAAC;IAED,IAAI,CAAC,IAAgB;QACnB,6BAA6B;QAC7B,MAAM,MAAM,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAEjC,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,EAAE;YACxB,qFAAqF;YACrF,MAAM,EACJ,8BAA8B;YAC9B,8DAA8D;cAC/D,GAAG,OAAO,CAAC,qCAAqC,CAAC,CAAC;YAEnD,IAAI;gBACF,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,CAAC;aAC9C;YAAC,OAAO,KAAK,EAAE;gBACd,OAAO,OAAO,CAAC,OAAO,CAAC;oBACrB,MAAM,EAAE,SAAS;oBACjB,KAAK,EAAE,KAAK;iBACb,CAAC,CAAC;aACJ;YAED,MAAM,iBAAiB,GAAG,8BAA8B,CACtD,IAAI,CAAC,WAAW,CAAC,QAAQ,EACzB,IAAI,CAAC,WAAW,CAAC,QAAQ,CAC1B,CAAC;YAEF,IAAI;gBACF,IAAI,CAAC,OAAO,GAAG,IAAI,iBAAiB,CAClC,IAAI,CAAC,WAAW,CAAC,OAAO,EACxB,IAAI,CAAC,WAAW,CAAC,WAAW,EAAE,EAC9B;oBACE,oCAAoC,EAAE,iBAAiB,CACrD,IAAI,CAAC,WAAW,CAAC,WAAW,CAC7B;iBACF,CACF,CAAC;aACH;YAAC,OAAO,KAAK,EAAE;gBACd,OAAO,OAAO,CAAC,OAAO,CAAC;oBACrB,MAAM,EAAE,SAAS;oBACjB,KAAK,EAAE,KAAK;iBACb,CAAC,CAAC;aACJ;SACF;QAED,OAAO,IAAI,OAAO,CAAiB,OAAO,CAAC,EAAE;YAC3C,8BAA8B;YAC9B,oEAAoE;YACpE,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC;YAE7D,2BAA2B;YAC3B,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,EAAE;gBAC1B,OAAO,OAAO,CAAC;oBACb,KAAK,EAAE,IAAI,KAAK,CAAC,mBAAmB,CAAC;oBACrC,MAAM,EAAE,SAAS;iBAClB,CAAC,CAAC;aACJ;YAED,6DAA6D;YAC7D,qHAAqH;YACrH,IAAI,CAAC,OAAO,CAAC,MAAM,CACjB,MAAM,EACN,IAAI,CAAC,SAAS,EACd,EAAE,QAAQ,EAAE,QAAQ,EAAE,EACtB,CAAC,GAAiB,EAAE,QAAgB,EAAE,EAAE;gBACtC,IAAI,GAAG,EAAE;oBACP,OAAO,CAAC;wBACN,MAAM,EAAE,SAAS;wBACjB,KAAK,EAAE,GAAG;qBACX,CAAC,CAAC;iBACJ;qBAAM;oBACL,OAAO,CAAC;wBACN,IAAI,EAAE,QAAQ;wBACd,MAAM,EAAE,SAAS;qBAClB,CAAC,CAAC;iBACJ;YACH,CAAC,CACF,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC;CACF;AAxFD,sDAwFC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n// NOTE: do not change these type imports to actual imports. Doing so WILL break `@opentelemetry/instrumentation-http`,\n// as they'd be imported before the http/https modules can be wrapped.\nimport type {\n  Metadata,\n  ServiceError,\n  ChannelCredentials,\n  Client,\n} from '@grpc/grpc-js';\nimport { ExportResponse } from './export-response';\nimport { IExporterTransport } from './exporter-transport';\n\n// values taken from '@grpc/grpc-js` so that we don't need to require/import it.\nconst GRPC_COMPRESSION_NONE = 0;\nconst GRPC_COMPRESSION_GZIP = 2;\n\nfunction toGrpcCompression(compression: 'gzip' | 'none'): number {\n  return compression === 'gzip' ? GRPC_COMPRESSION_GZIP : GRPC_COMPRESSION_NONE;\n}\n\nexport function createInsecureCredentials(): ChannelCredentials {\n  // Lazy-load so that we don't need to require/import '@grpc/grpc-js' before it can be wrapped by instrumentation.\n  const {\n    credentials,\n    // eslint-disable-next-line @typescript-eslint/no-var-requires\n  } = require('@grpc/grpc-js');\n  return credentials.createInsecure();\n}\n\nexport function createSslCredentials(\n  rootCert?: Buffer,\n  privateKey?: Buffer,\n  certChain?: Buffer\n): ChannelCredentials {\n  // Lazy-load so that we don't need to require/import '@grpc/grpc-js' before it can be wrapped by instrumentation.\n  const {\n    credentials,\n    // eslint-disable-next-line @typescript-eslint/no-var-requires\n  } = require('@grpc/grpc-js');\n  return credentials.createSsl(rootCert, privateKey, certChain);\n}\n\nexport function createEmptyMetadata(): Metadata {\n  // Lazy-load so that we don't need to require/import '@grpc/grpc-js' before it can be wrapped by instrumentation.\n  const {\n    Metadata,\n    // eslint-disable-next-line @typescript-eslint/no-var-requires\n  } = require('@grpc/grpc-js');\n  return new Metadata();\n}\n\nexport interface GrpcExporterTransportParameters {\n  grpcPath: string;\n  grpcName: string;\n  address: string;\n  /**\n   * NOTE: Ensure that you're only importing/requiring gRPC inside the function providing the channel credentials,\n   * otherwise, gRPC and http/https instrumentations may break.\n   *\n   * For common cases, you can avoid to import/require gRPC your function by using\n   *   - {@link createSslCredentials}\n   *   - {@link createInsecureCredentials}\n   */\n  credentials: () => ChannelCredentials;\n  /**\n   * NOTE: Ensure that you're only importing/requiring gRPC inside the function providing the metadata,\n   * otherwise, gRPC and http/https instrumentations may break.\n   *\n   * To avoid having to import/require gRPC from your function to create a new Metadata object,\n   * use {@link createEmptyMetadata}\n   */\n  metadata: () => Metadata;\n  compression: 'gzip' | 'none';\n  timeoutMillis: number;\n}\n\nexport class GrpcExporterTransport implements IExporterTransport {\n  private _client?: Client;\n  private _metadata?: Metadata;\n\n  constructor(private _parameters: GrpcExporterTransportParameters) {}\n\n  shutdown() {\n    this._client?.close();\n  }\n\n  send(data: Uint8Array): Promise<ExportResponse> {\n    // We need to make a for gRPC\n    const buffer = Buffer.from(data);\n\n    if (this._client == null) {\n      // Lazy require to ensure that grpc is not loaded before instrumentations can wrap it\n      const {\n        createServiceClientConstructor,\n        // eslint-disable-next-line @typescript-eslint/no-var-requires\n      } = require('./create-service-client-constructor');\n\n      try {\n        this._metadata = this._parameters.metadata();\n      } catch (error) {\n        return Promise.resolve({\n          status: 'failure',\n          error: error,\n        });\n      }\n\n      const clientConstructor = createServiceClientConstructor(\n        this._parameters.grpcPath,\n        this._parameters.grpcName\n      );\n\n      try {\n        this._client = new clientConstructor(\n          this._parameters.address,\n          this._parameters.credentials(),\n          {\n            'grpc.default_compression_algorithm': toGrpcCompression(\n              this._parameters.compression\n            ),\n          }\n        );\n      } catch (error) {\n        return Promise.resolve({\n          status: 'failure',\n          error: error,\n        });\n      }\n    }\n\n    return new Promise<ExportResponse>(resolve => {\n      // this will always be defined\n      // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n      const deadline = Date.now() + this._parameters.timeoutMillis;\n\n      // this should never happen\n      if (this._metadata == null) {\n        return resolve({\n          error: new Error('metadata was null'),\n          status: 'failure',\n        });\n      }\n\n      // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n      // @ts-ignore The gRPC client constructor is created on runtime, so we don't have any types for the resulting client.\n      this._client.export(\n        buffer,\n        this._metadata,\n        { deadline: deadline },\n        (err: ServiceError, response: Buffer) => {\n          if (err) {\n            resolve({\n              status: 'failure',\n              error: err,\n            });\n          } else {\n            resolve({\n              data: response,\n              status: 'success',\n            });\n          }\n        }\n      );\n    });\n  }\n}\n"]}