{"name": "@google-cloud/aiplatform", "version": "3.35.0", "description": "Vertex AI client for Node.js", "repository": {"type": "git", "directory": "packages/google-cloud-aiplatform", "url": "https://github.com/googleapis/google-cloud-node.git"}, "license": "Apache-2.0", "author": "Google LLC", "main": "build/src/index.js", "files": ["build/src", "build/protos", "!build/src/**/*.map"], "keywords": ["google apis client", "google api client", "google apis", "google api", "google", "google cloud platform", "google cloud", "cloud", "google vertex ai", "google vertex", "aiplatform", "dataset service", "endpoint service", "job service", "migration service", "model service", "pipeline service", "prediction service", "specialist pool service"], "scripts": {"clean": "gts clean", "compile": "tsc -p . && cp -r protos build/", "compile-protos": "compileProtos src", "docs": "NODE_OPTIONS=--max-old-space-size=8192 jsdoc -c .jsdoc.js", "predocs-test": "npm run docs", "docs-test": "linkinator docs", "fix": "gts fix", "lint": "gts check", "prepare": "npm run compile", "prelint": "cd samples; npm link ../; npm i", "postpack": "minifyProtoJson", "samples-test": "cd samples/ && npm link ../ && npm i && npm test", "system-test": "c8 mocha build/system-test", "test": "c8 node build/test/run.js"}, "dependencies": {"google-gax": "^4.0.3", "protobuf.js": "^1.1.2"}, "devDependencies": {"@types/mocha": "^9.0.0", "@types/node": "^22.0.0", "@types/sinon": "^17.0.0", "c8": "^9.0.0", "gapic-tools": "^0.4.0", "gts": "^5.0.0", "jsdoc": "^4.0.0", "jsdoc-fresh": "^3.0.0", "jsdoc-region-tag": "^3.0.0", "linkinator": "4.1.2", "long": "^5.2.3", "mocha": "^9.2.2", "pack-n-play": "^2.0.0", "sinon": "^18.0.0", "typescript": "^5.1.6"}, "engines": {"node": ">=14.0.0"}, "homepage": "https://github.com/googleapis/google-cloud-node/tree/main/packages/google-cloud-aiplatform"}