"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
var src_exports = {};
__export(src_exports, {
  GeminiConfigSchema: () => import_gemini.GeminiConfigSchema,
  ImagenConfigSchema: () => import_imagen.ImagenConfigSchema,
  SafetySettingsSchema: () => import_gemini.SafetySettingsSchema,
  default: () => src_default,
  gemini: () => import_gemini.gemini,
  gemini10Pro: () => import_gemini.gemini10Pro,
  gemini15Flash: () => import_gemini.gemini15Flash,
  gemini15Pro: () => import_gemini.gemini15Pro,
  gemini20Flash: () => import_gemini.gemini20Flash,
  gemini20Flash001: () => import_gemini.gemini20Flash001,
  gemini20FlashLite: () => import_gemini.gemini20FlashLite,
  gemini20FlashLitePreview0205: () => import_gemini.gemini20FlashLitePreview0205,
  gemini20ProExp0205: () => import_gemini.gemini20ProExp0205,
  gemini25FlashPreview0417: () => import_gemini.gemini25FlashPreview0417,
  gemini25ProExp0325: () => import_gemini.gemini25ProExp0325,
  gemini25ProPreview0325: () => import_gemini.gemini25ProPreview0325,
  imagen2: () => import_imagen.imagen2,
  imagen3: () => import_imagen.imagen3,
  imagen3Fast: () => import_imagen.imagen3Fast,
  multimodalEmbedding001: () => import_embedder.multimodalEmbedding001,
  textEmbedding004: () => import_embedder.textEmbedding004,
  textEmbedding005: () => import_embedder.textEmbedding005,
  textEmbeddingGecko003: () => import_embedder.textEmbeddingGecko003,
  textEmbeddingGeckoMultilingual001: () => import_embedder.textEmbeddingGeckoMultilingual001,
  textMultilingualEmbedding002: () => import_embedder.textMultilingualEmbedding002,
  vertexAI: () => vertexAI
});
module.exports = __toCommonJS(src_exports);
var import_genkit = require("genkit");
var import_plugin = require("genkit/plugin");
var import_common = require("./common/index.js");
var import_embedder = require("./embedder.js");
var import_gemini = require("./gemini.js");
var import_imagen = require("./imagen.js");
var import_list_models = require("./list-models.js");
/**
 * @license
 *
 * Copyright 2024 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
async function initializer(ai, options) {
  const { projectId, location, vertexClientFactory, authClient } = await (0, import_common.getDerivedParams)(options);
  Object.keys(import_imagen.SUPPORTED_IMAGEN_MODELS).map(
    (name) => (0, import_imagen.defineImagenModel)(ai, name, authClient, { projectId, location })
  );
  Object.keys(import_gemini.SUPPORTED_GEMINI_MODELS).map(
    (name) => (0, import_gemini.defineGeminiKnownModel)(
      ai,
      name,
      vertexClientFactory,
      {
        projectId,
        location
      },
      options?.experimental_debugTraces
    )
  );
  if (options?.models) {
    for (const modelOrRef of options?.models) {
      const modelName = typeof modelOrRef === "string" ? modelOrRef : (
        // strip out the `vertexai/` prefix
        modelOrRef.name.split("/")[1]
      );
      const modelRef2 = typeof modelOrRef === "string" ? (0, import_gemini.gemini)(modelOrRef) : modelOrRef;
      (0, import_gemini.defineGeminiModel)({
        ai,
        modelName: modelRef2.name,
        version: modelName,
        modelInfo: modelRef2.info,
        vertexClientFactory,
        options: {
          projectId,
          location
        },
        debugTraces: options.experimental_debugTraces
      });
    }
  }
  Object.keys(import_embedder.SUPPORTED_EMBEDDER_MODELS).map(
    (name) => (0, import_embedder.defineVertexAIEmbedder)(ai, name, authClient, { projectId, location })
  );
}
async function resolver(ai, actionType, actionName, options) {
  switch (actionType) {
    case "model":
      await resolveModel(ai, actionName, options);
      break;
    case "embedder":
      await resolveEmbedder(ai, actionName, options);
      break;
    default:
  }
}
async function resolveModel(ai, actionName, options) {
  const { projectId, location, vertexClientFactory, authClient } = await (0, import_common.getDerivedParams)(options);
  if (actionName.startsWith("imagen")) {
    (0, import_imagen.defineImagenModel)(ai, actionName, authClient, { projectId, location });
    return;
  }
  const modelRef2 = (0, import_gemini.gemini)(actionName);
  (0, import_gemini.defineGeminiModel)({
    ai,
    modelName: modelRef2.name,
    version: actionName,
    modelInfo: modelRef2.info,
    vertexClientFactory,
    options: {
      projectId,
      location
    },
    debugTraces: options?.experimental_debugTraces
  });
}
async function resolveEmbedder(ai, actionName, options) {
  const { projectId, location, authClient } = await (0, import_common.getDerivedParams)(options);
  (0, import_embedder.defineVertexAIEmbedder)(ai, actionName, authClient, { projectId, location });
}
const KNOWN_DECOMISSIONED_MODELS = [
  "gemini-pro-vision",
  "gemini-pro",
  "gemini-ultra",
  "gemini-ultra-vision"
];
async function listActions(options) {
  const { location, projectId, authClient } = await (0, import_common.getDerivedParams)(options);
  const models = await (0, import_list_models.listModels)(authClient, location, projectId);
  return [
    // Gemini
    ...models.filter(
      (m) => m.name.includes("gemini") && !KNOWN_DECOMISSIONED_MODELS.includes(m.name.split("/").at(-1))
    ).map((m) => {
      const ref = (0, import_gemini.gemini)(m.name.split("/").at(-1));
      return (0, import_genkit.modelActionMetadata)({
        name: ref.name,
        info: ref.info,
        configSchema: import_gemini.GeminiConfigSchema
      });
    }),
    // Imagen
    ...models.filter((m) => m.name.includes("imagen")).map((m) => {
      const name = m.name.split("/").at(-1);
      return (0, import_genkit.modelActionMetadata)({
        name: "vertexai/" + name,
        info: {
          ...import_imagen.GENERIC_IMAGEN_INFO,
          label: `Vertex AI - ${name}`
        },
        configSchema: import_imagen.ImagenConfigSchema
      });
    })
  ];
}
function vertexAIPlugin(options) {
  let listActionsCache;
  return (0, import_plugin.genkitPlugin)(
    "vertexai",
    async (ai) => await initializer(ai, options),
    async (ai, actionType, actionName) => await resolver(ai, actionType, actionName, options),
    async () => {
      if (listActionsCache) return listActionsCache;
      listActionsCache = await listActions(options);
      return listActionsCache;
    }
  );
}
const vertexAI = vertexAIPlugin;
vertexAI.model = (name, config) => {
  if (name.startsWith("imagen")) {
    return (0, import_genkit.modelRef)({
      name: `vertexai/${name}`,
      config,
      configSchema: import_imagen.ImagenConfigSchema
    });
  }
  return (0, import_genkit.modelRef)({
    name: `vertexai/${name}`,
    config,
    configSchema: import_gemini.GeminiConfigSchema
  });
};
vertexAI.embedder = (name, config) => {
  return (0, import_genkit.embedderRef)({
    name: `vertexai/${name}`,
    config,
    configSchema: import_embedder.VertexEmbeddingConfigSchema
  });
};
var src_default = vertexAI;
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  GeminiConfigSchema,
  ImagenConfigSchema,
  SafetySettingsSchema,
  gemini,
  gemini10Pro,
  gemini15Flash,
  gemini15Pro,
  gemini20Flash,
  gemini20Flash001,
  gemini20FlashLite,
  gemini20FlashLitePreview0205,
  gemini20ProExp0205,
  gemini25FlashPreview0417,
  gemini25ProExp0325,
  gemini25ProPreview0325,
  imagen2,
  imagen3,
  imagen3Fast,
  multimodalEmbedding001,
  textEmbedding004,
  textEmbedding005,
  textEmbeddingGecko003,
  textEmbeddingGeckoMultilingual001,
  textMultilingualEmbedding002,
  vertexAI
});
//# sourceMappingURL=index.js.map