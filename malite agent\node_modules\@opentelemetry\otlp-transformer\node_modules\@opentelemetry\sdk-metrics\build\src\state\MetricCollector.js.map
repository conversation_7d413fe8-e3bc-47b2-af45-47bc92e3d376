{"version": 3, "file": "MetricCollector.js", "sourceRoot": "", "sources": ["../../../src/state/MetricCollector.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,8CAAqD;AASrD;;;;GAIG;AACH,MAAa,eAAe;IAC1B,YACU,YAAsC,EACtC,aAA2B;QAD3B,iBAAY,GAAZ,YAAY,CAA0B;QACtC,kBAAa,GAAb,aAAa,CAAc;IAClC,CAAC;IAEJ,KAAK,CAAC,OAAO,CAAC,OAA8B;QAC1C,MAAM,cAAc,GAAG,IAAA,qBAAc,EAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC;QAClD,MAAM,YAAY,GAAmB,EAAE,CAAC;QACxC,MAAM,MAAM,GAAc,EAAE,CAAC;QAE7B,MAAM,uBAAuB,GAAG,KAAK,CAAC,IAAI,CACxC,IAAI,CAAC,YAAY,CAAC,iBAAiB,CAAC,MAAM,EAAE,CAC7C,CAAC,GAAG,CAAC,KAAK,EAAC,gBAAgB,EAAC,EAAE;YAC7B,MAAM,OAAO,GAAG,MAAM,gBAAgB,CAAC,OAAO,CAC5C,IAAI,EACJ,cAAc,EACd,OAAO,CACR,CAAC;YAEF,sCAAsC;YACtC,IAAI,CAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,YAAY,KAAI,IAAI,EAAE;gBACjC,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;aACzC;YAED,+BAA+B;YAC/B,IAAI,CAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,MAAM,KAAI,IAAI,EAAE;gBAC3B,MAAM,CAAC,IAAI,CAAC,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC;aAChC;QACH,CAAC,CAAC,CAAC;QACH,MAAM,OAAO,CAAC,GAAG,CAAC,uBAAuB,CAAC,CAAC;QAE3C,OAAO;YACL,eAAe,EAAE;gBACf,QAAQ,EAAE,IAAI,CAAC,YAAY,CAAC,QAAQ;gBACpC,YAAY,EAAE,YAAY;aAC3B;YACD,MAAM,EAAE,MAAM;SACf,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,UAAU,CAAC,OAA2B;QAC1C,MAAM,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;IAC/C,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,QAAQ,CAAC,OAAyB;QACtC,MAAM,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;IAC7C,CAAC;IAED,4BAA4B,CAAC,cAA8B;QACzD,OAAO,IAAI,CAAC,aAAa,CAAC,4BAA4B,CAAC,cAAc,CAAC,CAAC;IACzE,CAAC;IAED,iBAAiB,CAAC,cAA8B;QAC9C,OAAO,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC,cAAc,CAAC,CAAC;IAC9D,CAAC;CACF;AA9DD,0CA8DC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { millisToHrTime } from '@opentelemetry/core';\nimport { AggregationTemporalitySelector } from '../export/AggregationSelector';\nimport { CollectionResult, ScopeMetrics } from '../export/MetricData';\nimport { MetricProducer, MetricCollectOptions } from '../export/MetricProducer';\nimport { MetricReader } from '../export/MetricReader';\nimport { InstrumentType } from '../InstrumentDescriptor';\nimport { ForceFlushOptions, ShutdownOptions } from '../types';\nimport { MeterProviderSharedState } from './MeterProviderSharedState';\n\n/**\n * An internal opaque interface that the MetricReader receives as\n * MetricProducer. It acts as the storage key to the internal metric stream\n * state for each MetricReader.\n */\nexport class MetricCollector implements MetricProducer {\n  constructor(\n    private _sharedState: MeterProviderSharedState,\n    private _metricReader: MetricReader\n  ) {}\n\n  async collect(options?: MetricCollectOptions): Promise<CollectionResult> {\n    const collectionTime = millisToHrTime(Date.now());\n    const scopeMetrics: ScopeMetrics[] = [];\n    const errors: unknown[] = [];\n\n    const meterCollectionPromises = Array.from(\n      this._sharedState.meterSharedStates.values()\n    ).map(async meterSharedState => {\n      const current = await meterSharedState.collect(\n        this,\n        collectionTime,\n        options\n      );\n\n      // only add scope metrics if available\n      if (current?.scopeMetrics != null) {\n        scopeMetrics.push(current.scopeMetrics);\n      }\n\n      // only add errors if available\n      if (current?.errors != null) {\n        errors.push(...current.errors);\n      }\n    });\n    await Promise.all(meterCollectionPromises);\n\n    return {\n      resourceMetrics: {\n        resource: this._sharedState.resource,\n        scopeMetrics: scopeMetrics,\n      },\n      errors: errors,\n    };\n  }\n\n  /**\n   * Delegates for MetricReader.forceFlush.\n   */\n  async forceFlush(options?: ForceFlushOptions): Promise<void> {\n    await this._metricReader.forceFlush(options);\n  }\n\n  /**\n   * Delegates for MetricReader.shutdown.\n   */\n  async shutdown(options?: ShutdownOptions): Promise<void> {\n    await this._metricReader.shutdown(options);\n  }\n\n  selectAggregationTemporality(instrumentType: InstrumentType) {\n    return this._metricReader.selectAggregationTemporality(instrumentType);\n  }\n\n  selectAggregation(instrumentType: InstrumentType) {\n    return this._metricReader.selectAggregation(instrumentType);\n  }\n}\n\n/**\n * An internal interface for MetricCollector. Exposes the necessary\n * information for metric collection.\n */\nexport interface MetricCollectorHandle {\n  selectAggregationTemporality: AggregationTemporalitySelector;\n}\n"]}