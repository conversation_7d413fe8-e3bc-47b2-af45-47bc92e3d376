import { z, Genkit } from 'genkit';
import { EmbedderReference, EmbedderAction } from 'genkit/embedder';
import { GoogleAuth } from 'google-auth-library';
import { P as PluginOptions } from './types-Bc0LKM8D.js';
import '@google-cloud/vertexai';
import 'genkit/model';

/**
 * Copyright 2024 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

declare const TaskTypeSchema: z.ZodEnum<["RETRIEVAL_DOCUMENT", "RETRIEVAL_QUERY", "SEMANTIC_SIMILARITY", "CLASSIFICATION", "CLUSTERING"]>;
type TaskType = z.infer<typeof TaskTypeSchema>;
declare const VertexEmbeddingConfigSchema: z.ZodObject<{
    /**
     * The `task_type` parameter is defined as the intended downstream application
     * to help the model produce better quality embeddings.
     **/
    taskType: z.ZodOptional<z.ZodEnum<["RETRIEVAL_DOCUMENT", "RETRIEVAL_QUERY", "SEMANTIC_SIMILARITY", "CLASSIFICATION", "CLUSTERING"]>>;
    title: z.ZodOptional<z.ZodString>;
    location: z.ZodOptional<z.ZodString>;
    version: z.ZodOptional<z.ZodString>;
    /**
     * The `outputDimensionality` parameter allows you to specify the dimensionality of the embedding output.
     * By default, the model generates embeddings with 768 dimensions. Models such as
     * `text-embedding-004`, `text-embedding-005`, and `text-multilingual-embedding-002`
     * allow the output dimensionality to be adjusted between 1 and 768.
     * By selecting a smaller output dimensionality, users can save memory and storage space, leading to more efficient computations.
     **/
    outputDimensionality: z.ZodOptional<z.ZodNumber>;
}, "strip", z.ZodTypeAny, {
    location?: string | undefined;
    version?: string | undefined;
    taskType?: "RETRIEVAL_DOCUMENT" | "RETRIEVAL_QUERY" | "SEMANTIC_SIMILARITY" | "CLASSIFICATION" | "CLUSTERING" | undefined;
    title?: string | undefined;
    outputDimensionality?: number | undefined;
}, {
    location?: string | undefined;
    version?: string | undefined;
    taskType?: "RETRIEVAL_DOCUMENT" | "RETRIEVAL_QUERY" | "SEMANTIC_SIMILARITY" | "CLASSIFICATION" | "CLUSTERING" | undefined;
    title?: string | undefined;
    outputDimensionality?: number | undefined;
}>;
type VertexEmbeddingConfig = z.infer<typeof VertexEmbeddingConfigSchema>;
declare const textEmbeddingGecko003: EmbedderReference<z.ZodObject<{
    /**
     * The `task_type` parameter is defined as the intended downstream application
     * to help the model produce better quality embeddings.
     **/
    taskType: z.ZodOptional<z.ZodEnum<["RETRIEVAL_DOCUMENT", "RETRIEVAL_QUERY", "SEMANTIC_SIMILARITY", "CLASSIFICATION", "CLUSTERING"]>>;
    title: z.ZodOptional<z.ZodString>;
    location: z.ZodOptional<z.ZodString>;
    version: z.ZodOptional<z.ZodString>;
    /**
     * The `outputDimensionality` parameter allows you to specify the dimensionality of the embedding output.
     * By default, the model generates embeddings with 768 dimensions. Models such as
     * `text-embedding-004`, `text-embedding-005`, and `text-multilingual-embedding-002`
     * allow the output dimensionality to be adjusted between 1 and 768.
     * By selecting a smaller output dimensionality, users can save memory and storage space, leading to more efficient computations.
     **/
    outputDimensionality: z.ZodOptional<z.ZodNumber>;
}, "strip", z.ZodTypeAny, {
    location?: string | undefined;
    version?: string | undefined;
    taskType?: "RETRIEVAL_DOCUMENT" | "RETRIEVAL_QUERY" | "SEMANTIC_SIMILARITY" | "CLASSIFICATION" | "CLUSTERING" | undefined;
    title?: string | undefined;
    outputDimensionality?: number | undefined;
}, {
    location?: string | undefined;
    version?: string | undefined;
    taskType?: "RETRIEVAL_DOCUMENT" | "RETRIEVAL_QUERY" | "SEMANTIC_SIMILARITY" | "CLASSIFICATION" | "CLUSTERING" | undefined;
    title?: string | undefined;
    outputDimensionality?: number | undefined;
}>>;
declare const textEmbedding004: EmbedderReference<z.ZodObject<{
    /**
     * The `task_type` parameter is defined as the intended downstream application
     * to help the model produce better quality embeddings.
     **/
    taskType: z.ZodOptional<z.ZodEnum<["RETRIEVAL_DOCUMENT", "RETRIEVAL_QUERY", "SEMANTIC_SIMILARITY", "CLASSIFICATION", "CLUSTERING"]>>;
    title: z.ZodOptional<z.ZodString>;
    location: z.ZodOptional<z.ZodString>;
    version: z.ZodOptional<z.ZodString>;
    /**
     * The `outputDimensionality` parameter allows you to specify the dimensionality of the embedding output.
     * By default, the model generates embeddings with 768 dimensions. Models such as
     * `text-embedding-004`, `text-embedding-005`, and `text-multilingual-embedding-002`
     * allow the output dimensionality to be adjusted between 1 and 768.
     * By selecting a smaller output dimensionality, users can save memory and storage space, leading to more efficient computations.
     **/
    outputDimensionality: z.ZodOptional<z.ZodNumber>;
}, "strip", z.ZodTypeAny, {
    location?: string | undefined;
    version?: string | undefined;
    taskType?: "RETRIEVAL_DOCUMENT" | "RETRIEVAL_QUERY" | "SEMANTIC_SIMILARITY" | "CLASSIFICATION" | "CLUSTERING" | undefined;
    title?: string | undefined;
    outputDimensionality?: number | undefined;
}, {
    location?: string | undefined;
    version?: string | undefined;
    taskType?: "RETRIEVAL_DOCUMENT" | "RETRIEVAL_QUERY" | "SEMANTIC_SIMILARITY" | "CLASSIFICATION" | "CLUSTERING" | undefined;
    title?: string | undefined;
    outputDimensionality?: number | undefined;
}>>;
declare const textEmbedding005: EmbedderReference<z.ZodObject<{
    /**
     * The `task_type` parameter is defined as the intended downstream application
     * to help the model produce better quality embeddings.
     **/
    taskType: z.ZodOptional<z.ZodEnum<["RETRIEVAL_DOCUMENT", "RETRIEVAL_QUERY", "SEMANTIC_SIMILARITY", "CLASSIFICATION", "CLUSTERING"]>>;
    title: z.ZodOptional<z.ZodString>;
    location: z.ZodOptional<z.ZodString>;
    version: z.ZodOptional<z.ZodString>;
    /**
     * The `outputDimensionality` parameter allows you to specify the dimensionality of the embedding output.
     * By default, the model generates embeddings with 768 dimensions. Models such as
     * `text-embedding-004`, `text-embedding-005`, and `text-multilingual-embedding-002`
     * allow the output dimensionality to be adjusted between 1 and 768.
     * By selecting a smaller output dimensionality, users can save memory and storage space, leading to more efficient computations.
     **/
    outputDimensionality: z.ZodOptional<z.ZodNumber>;
}, "strip", z.ZodTypeAny, {
    location?: string | undefined;
    version?: string | undefined;
    taskType?: "RETRIEVAL_DOCUMENT" | "RETRIEVAL_QUERY" | "SEMANTIC_SIMILARITY" | "CLASSIFICATION" | "CLUSTERING" | undefined;
    title?: string | undefined;
    outputDimensionality?: number | undefined;
}, {
    location?: string | undefined;
    version?: string | undefined;
    taskType?: "RETRIEVAL_DOCUMENT" | "RETRIEVAL_QUERY" | "SEMANTIC_SIMILARITY" | "CLASSIFICATION" | "CLUSTERING" | undefined;
    title?: string | undefined;
    outputDimensionality?: number | undefined;
}>>;
declare const textEmbeddingGeckoMultilingual001: EmbedderReference<z.ZodObject<{
    /**
     * The `task_type` parameter is defined as the intended downstream application
     * to help the model produce better quality embeddings.
     **/
    taskType: z.ZodOptional<z.ZodEnum<["RETRIEVAL_DOCUMENT", "RETRIEVAL_QUERY", "SEMANTIC_SIMILARITY", "CLASSIFICATION", "CLUSTERING"]>>;
    title: z.ZodOptional<z.ZodString>;
    location: z.ZodOptional<z.ZodString>;
    version: z.ZodOptional<z.ZodString>;
    /**
     * The `outputDimensionality` parameter allows you to specify the dimensionality of the embedding output.
     * By default, the model generates embeddings with 768 dimensions. Models such as
     * `text-embedding-004`, `text-embedding-005`, and `text-multilingual-embedding-002`
     * allow the output dimensionality to be adjusted between 1 and 768.
     * By selecting a smaller output dimensionality, users can save memory and storage space, leading to more efficient computations.
     **/
    outputDimensionality: z.ZodOptional<z.ZodNumber>;
}, "strip", z.ZodTypeAny, {
    location?: string | undefined;
    version?: string | undefined;
    taskType?: "RETRIEVAL_DOCUMENT" | "RETRIEVAL_QUERY" | "SEMANTIC_SIMILARITY" | "CLASSIFICATION" | "CLUSTERING" | undefined;
    title?: string | undefined;
    outputDimensionality?: number | undefined;
}, {
    location?: string | undefined;
    version?: string | undefined;
    taskType?: "RETRIEVAL_DOCUMENT" | "RETRIEVAL_QUERY" | "SEMANTIC_SIMILARITY" | "CLASSIFICATION" | "CLUSTERING" | undefined;
    title?: string | undefined;
    outputDimensionality?: number | undefined;
}>>;
declare const textMultilingualEmbedding002: EmbedderReference<z.ZodObject<{
    /**
     * The `task_type` parameter is defined as the intended downstream application
     * to help the model produce better quality embeddings.
     **/
    taskType: z.ZodOptional<z.ZodEnum<["RETRIEVAL_DOCUMENT", "RETRIEVAL_QUERY", "SEMANTIC_SIMILARITY", "CLASSIFICATION", "CLUSTERING"]>>;
    title: z.ZodOptional<z.ZodString>;
    location: z.ZodOptional<z.ZodString>;
    version: z.ZodOptional<z.ZodString>;
    /**
     * The `outputDimensionality` parameter allows you to specify the dimensionality of the embedding output.
     * By default, the model generates embeddings with 768 dimensions. Models such as
     * `text-embedding-004`, `text-embedding-005`, and `text-multilingual-embedding-002`
     * allow the output dimensionality to be adjusted between 1 and 768.
     * By selecting a smaller output dimensionality, users can save memory and storage space, leading to more efficient computations.
     **/
    outputDimensionality: z.ZodOptional<z.ZodNumber>;
}, "strip", z.ZodTypeAny, {
    location?: string | undefined;
    version?: string | undefined;
    taskType?: "RETRIEVAL_DOCUMENT" | "RETRIEVAL_QUERY" | "SEMANTIC_SIMILARITY" | "CLASSIFICATION" | "CLUSTERING" | undefined;
    title?: string | undefined;
    outputDimensionality?: number | undefined;
}, {
    location?: string | undefined;
    version?: string | undefined;
    taskType?: "RETRIEVAL_DOCUMENT" | "RETRIEVAL_QUERY" | "SEMANTIC_SIMILARITY" | "CLASSIFICATION" | "CLUSTERING" | undefined;
    title?: string | undefined;
    outputDimensionality?: number | undefined;
}>>;
declare const multimodalEmbedding001: EmbedderReference<z.ZodObject<{
    /**
     * The `task_type` parameter is defined as the intended downstream application
     * to help the model produce better quality embeddings.
     **/
    taskType: z.ZodOptional<z.ZodEnum<["RETRIEVAL_DOCUMENT", "RETRIEVAL_QUERY", "SEMANTIC_SIMILARITY", "CLASSIFICATION", "CLUSTERING"]>>;
    title: z.ZodOptional<z.ZodString>;
    location: z.ZodOptional<z.ZodString>;
    version: z.ZodOptional<z.ZodString>;
    /**
     * The `outputDimensionality` parameter allows you to specify the dimensionality of the embedding output.
     * By default, the model generates embeddings with 768 dimensions. Models such as
     * `text-embedding-004`, `text-embedding-005`, and `text-multilingual-embedding-002`
     * allow the output dimensionality to be adjusted between 1 and 768.
     * By selecting a smaller output dimensionality, users can save memory and storage space, leading to more efficient computations.
     **/
    outputDimensionality: z.ZodOptional<z.ZodNumber>;
}, "strip", z.ZodTypeAny, {
    location?: string | undefined;
    version?: string | undefined;
    taskType?: "RETRIEVAL_DOCUMENT" | "RETRIEVAL_QUERY" | "SEMANTIC_SIMILARITY" | "CLASSIFICATION" | "CLUSTERING" | undefined;
    title?: string | undefined;
    outputDimensionality?: number | undefined;
}, {
    location?: string | undefined;
    version?: string | undefined;
    taskType?: "RETRIEVAL_DOCUMENT" | "RETRIEVAL_QUERY" | "SEMANTIC_SIMILARITY" | "CLASSIFICATION" | "CLUSTERING" | undefined;
    title?: string | undefined;
    outputDimensionality?: number | undefined;
}>>;
declare const SUPPORTED_EMBEDDER_MODELS: Record<string, EmbedderReference>;
declare function defineVertexAIEmbedder(ai: Genkit, name: string, client: GoogleAuth, options: PluginOptions): EmbedderAction<any>;

export { SUPPORTED_EMBEDDER_MODELS, type TaskType, TaskTypeSchema, type VertexEmbeddingConfig, VertexEmbeddingConfigSchema, defineVertexAIEmbedder, multimodalEmbedding001, textEmbedding004, textEmbedding005, textEmbeddingGecko003, textEmbeddingGeckoMultilingual001, textMultilingualEmbedding002 };
