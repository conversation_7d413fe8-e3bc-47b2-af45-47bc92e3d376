{"version": 3, "file": "Buckets.js", "sourceRoot": "", "sources": ["../../../../src/aggregator/exponential-histogram/Buckets.ts"], "names": [], "mappings": ";;;AAAA;;;;;;;;;;;;;;GAcG;AACH,MAAa,OAAO;IAClB;;;;;;;;;;;;;;;;OAgBG;IACH,YACS,UAAU,IAAI,cAAc,EAAE,EAC9B,YAAY,CAAC,EACb,aAAa,CAAC,EACd,WAAW,CAAC;QAHZ,YAAO,GAAP,OAAO,CAAuB;QAC9B,cAAS,GAAT,SAAS,CAAI;QACb,eAAU,GAAV,UAAU,CAAI;QACd,aAAQ,GAAR,QAAQ,CAAI;IAClB,CAAC;IAEJ;;;OAGG;IACH,IAAI,MAAM;QACR,OAAO,IAAI,CAAC,UAAU,CAAC;IACzB,CAAC;IAED;;;OAGG;IACH,IAAI,MAAM;QACR,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE;YAC7B,OAAO,CAAC,CAAC;SACV;QAED,IAAI,IAAI,CAAC,QAAQ,KAAK,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE;YACzD,OAAO,CAAC,CAAC;SACV;QAED,OAAO,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC;IAC7C,CAAC;IAED;;;;;;OAMG;IACH,MAAM;QACJ,OAAO,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACnE,CAAC;IAED;;;;;OAKG;IACH,EAAE,CAAC,QAAgB;QACjB,MAAM,IAAI,GAAG,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC;QAC9C,IAAI,QAAQ,GAAG,IAAI,EAAE;YACnB,QAAQ,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;SACjC;QAED,QAAQ,IAAI,IAAI,CAAC;QACjB,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;IACxC,CAAC;IAED;;;;OAIG;IACH,eAAe,CAAC,WAAmB,EAAE,SAAiB;QACpD,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;IACjD,CAAC;IAED;;;;;OAKG;IACH,eAAe,CAAC,WAAmB,EAAE,SAAiB;QACpD,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;IACjD,CAAC;IAED;;;;OAIG;IACH,IAAI;QACF,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACpC,IAAI,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE;gBACpB,IAAI,CAAC,UAAU,IAAI,CAAC,CAAC;gBACrB,MAAM;aACP;iBAAM,IAAI,CAAC,KAAK,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE;gBAChC,gCAAgC;gBAChC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC;gBACrD,OAAO;aACR;SACF;QAED,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;YACzC,IAAI,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE;gBACpB,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;gBACrC,MAAM;aACP;SACF;QAED,IAAI,CAAC,OAAO,EAAE,CAAC;IACjB,CAAC;IAED;;;OAGG;IACH,SAAS,CAAC,EAAU;QAClB,IAAI,CAAC,OAAO,EAAE,CAAC;QAEf,MAAM,IAAI,GAAG,CAAC,GAAG,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC;QACjD,MAAM,IAAI,GAAG,CAAC,IAAI,EAAE,CAAC;QACrB,IAAI,KAAK,GAAG,CAAC,CAAC;QACd,IAAI,MAAM,GAAG,CAAC,CAAC;QAEf,KAAK,IAAI,GAAG,GAAG,IAAI,CAAC,UAAU,EAAE,GAAG,IAAI,IAAI,CAAC,QAAQ,GAAI;YACtD,IAAI,GAAG,GAAG,GAAG,GAAG,IAAI,CAAC;YACrB,IAAI,GAAG,GAAG,CAAC,EAAE;gBACX,GAAG,IAAI,IAAI,CAAC;aACb;YACD,KAAK,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,IAAI,IAAI,KAAK,GAAG,IAAI,EAAE,CAAC,EAAE,EAAE;gBAC/C,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;gBACpC,KAAK,EAAE,CAAC;gBACR,GAAG,EAAE,CAAC;aACP;YACD,MAAM,EAAE,CAAC;SACV;QAED,IAAI,CAAC,UAAU,KAAK,EAAE,CAAC;QACvB,IAAI,CAAC,QAAQ,KAAK,EAAE,CAAC;QACrB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC;IACnC,CAAC;IAED;;;OAGG;IACH,KAAK;QACH,OAAO,IAAI,OAAO,CAChB,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,EACpB,IAAI,CAAC,SAAS,EACd,IAAI,CAAC,UAAU,EACf,IAAI,CAAC,QAAQ,CACd,CAAC;IACJ,CAAC;IAED;;;OAGG;IACK,OAAO;QACb,MAAM,IAAI,GAAG,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC;QAE9C,IAAI,IAAI,KAAK,CAAC,EAAE;YACd,OAAO;SACR;aAAM,IAAI,IAAI,GAAG,CAAC,EAAE;YACnB,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;YAC7C,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;YAC9B,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;SACjD;aAAM;YACL,6DAA6D;YAC7D,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;YAC7C,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC,CAAC;SACrD;QACD,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC;IACnC,CAAC;IAED;;;OAGG;IACK,eAAe,CAAC,IAAY,EAAE,GAAW;QAC/C,IAAI,IAAI,KAAK,GAAG,EAAE;YAChB,OAAO;SACR;QACD,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC;IAC5D,CAAC;CACF;AApMD,0BAoMC;AAED;;;GAGG;AACH,MAAM,cAAc;IAClB,YAAoB,UAAU,CAAC,CAAC,CAAC;QAAb,YAAO,GAAP,OAAO,CAAM;IAAG,CAAC;IAErC;;;OAGG;IACH,IAAI,MAAM;QACR,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;IAC7B,CAAC;IAED;;OAEG;IACH,OAAO,CAAC,GAAW;QACjB,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;IAC3B,CAAC;IAED;;;OAGG;IACH,MAAM,CAAC,OAAe,EAAE,gBAAwB,EAAE,gBAAwB;QACxE,MAAM,GAAG,GAAG,IAAI,KAAK,CAAS,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC/C,GAAG,CAAC,MAAM,CACR,gBAAgB,EAChB,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,gBAAgB,EACtC,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,gBAAgB,CAAC,CACxC,CAAC;QACF,GAAG,CAAC,MAAM,CAAC,CAAC,EAAE,gBAAgB,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,gBAAgB,CAAC,CAAC,CAAC;QAC5E,IAAI,CAAC,OAAO,GAAG,GAAG,CAAC;IACrB,CAAC;IAED;;OAEG;IACH,OAAO,CAAC,IAAY,EAAE,KAAa;QACjC,MAAM,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC;QAClD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;YAC5B,MAAM,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC;YACnC,IAAI,CAAC,OAAO,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;YACrD,IAAI,CAAC,OAAO,CAAC,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC;SACnC;IACH,CAAC;IAED;;;OAGG;IACH,WAAW,CAAC,GAAW;QACrB,MAAM,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;QAC9B,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QACtB,OAAO,GAAG,CAAC;IACb,CAAC;IAED;;OAEG;IACH,SAAS,CAAC,WAAmB,EAAE,SAAiB;QAC9C,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,SAAS,CAAC;IACzC,CAAC;IAED;;OAEG;IACH,SAAS,CAAC,WAAmB,EAAE,SAAiB;QAC9C,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,SAAS,EAAE;YAC1C,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,SAAS,CAAC;SACxC;aAAM;YACL,4DAA4D;YAC5D,mBAAmB;YACnB,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;SAC/B;IACH,CAAC;IAED;;OAEG;IACH,KAAK;QACH,OAAO,IAAI,cAAc,CAAC,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;IAC/C,CAAC;CACF", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nexport class Buckets {\n  /**\n   * The term index refers to the number of the exponential histogram bucket\n   * used to determine its boundaries. The lower boundary of a bucket is\n   * determined by base ** index and the upper boundary of a bucket is\n   * determined by base ** (index + 1). index values are signed to account\n   * for values less than or equal to 1.\n   *\n   * indexBase is the index of the 0th position in the\n   * backing array, i.e., backing[0] is the count\n   * in the bucket with index `indexBase`.\n   *\n   * indexStart is the smallest index value represented\n   * in the backing array.\n   *\n   * indexEnd is the largest index value represented in\n   * the backing array.\n   */\n  constructor(\n    public backing = new BucketsBacking(),\n    public indexBase = 0,\n    public indexStart = 0,\n    public indexEnd = 0\n  ) {}\n\n  /**\n   * Offset is the bucket index of the smallest entry in the counts array\n   * @returns {number}\n   */\n  get offset(): number {\n    return this.indexStart;\n  }\n\n  /**\n   * Buckets is a view into the backing array.\n   * @returns {number}\n   */\n  get length(): number {\n    if (this.backing.length === 0) {\n      return 0;\n    }\n\n    if (this.indexEnd === this.indexStart && this.at(0) === 0) {\n      return 0;\n    }\n\n    return this.indexEnd - this.indexStart + 1;\n  }\n\n  /**\n   * An array of counts, where count[i] carries the count\n   * of the bucket at index (offset+i).  count[i] is the count of\n   * values greater than base^(offset+i) and less than or equal to\n   * base^(offset+i+1).\n   * @returns {number} The logical counts based on the backing array\n   */\n  counts(): number[] {\n    return Array.from({ length: this.length }, (_, i) => this.at(i));\n  }\n\n  /**\n   * At returns the count of the bucket at a position in the logical\n   * array of counts.\n   * @param position\n   * @returns {number}\n   */\n  at(position: number): number {\n    const bias = this.indexBase - this.indexStart;\n    if (position < bias) {\n      position += this.backing.length;\n    }\n\n    position -= bias;\n    return this.backing.countAt(position);\n  }\n\n  /**\n   * incrementBucket increments the backing array index by `increment`\n   * @param bucketIndex\n   * @param increment\n   */\n  incrementBucket(bucketIndex: number, increment: number) {\n    this.backing.increment(bucketIndex, increment);\n  }\n\n  /**\n   * decrementBucket decrements the backing array index by `decrement`\n   * if decrement is greater than the current value, it's set to 0.\n   * @param bucketIndex\n   * @param decrement\n   */\n  decrementBucket(bucketIndex: number, decrement: number) {\n    this.backing.decrement(bucketIndex, decrement);\n  }\n\n  /**\n   * trim removes leading and / or trailing zero buckets (which can occur\n   * after diffing two histos) and rotates the backing array so that the\n   * smallest non-zero index is in the 0th position of the backing array\n   */\n  trim() {\n    for (let i = 0; i < this.length; i++) {\n      if (this.at(i) !== 0) {\n        this.indexStart += i;\n        break;\n      } else if (i === this.length - 1) {\n        //the entire array is zeroed out\n        this.indexStart = this.indexEnd = this.indexBase = 0;\n        return;\n      }\n    }\n\n    for (let i = this.length - 1; i >= 0; i--) {\n      if (this.at(i) !== 0) {\n        this.indexEnd -= this.length - i - 1;\n        break;\n      }\n    }\n\n    this._rotate();\n  }\n\n  /**\n   * downscale first rotates, then collapses 2**`by`-to-1 buckets.\n   * @param by\n   */\n  downscale(by: number) {\n    this._rotate();\n\n    const size = 1 + this.indexEnd - this.indexStart;\n    const each = 1 << by;\n    let inpos = 0;\n    let outpos = 0;\n\n    for (let pos = this.indexStart; pos <= this.indexEnd; ) {\n      let mod = pos % each;\n      if (mod < 0) {\n        mod += each;\n      }\n      for (let i = mod; i < each && inpos < size; i++) {\n        this._relocateBucket(outpos, inpos);\n        inpos++;\n        pos++;\n      }\n      outpos++;\n    }\n\n    this.indexStart >>= by;\n    this.indexEnd >>= by;\n    this.indexBase = this.indexStart;\n  }\n\n  /**\n   * Clone returns a deep copy of Buckets\n   * @returns {Buckets}\n   */\n  clone(): Buckets {\n    return new Buckets(\n      this.backing.clone(),\n      this.indexBase,\n      this.indexStart,\n      this.indexEnd\n    );\n  }\n\n  /**\n   * _rotate shifts the backing array contents so that indexStart ==\n   * indexBase to simplify the downscale logic.\n   */\n  private _rotate() {\n    const bias = this.indexBase - this.indexStart;\n\n    if (bias === 0) {\n      return;\n    } else if (bias > 0) {\n      this.backing.reverse(0, this.backing.length);\n      this.backing.reverse(0, bias);\n      this.backing.reverse(bias, this.backing.length);\n    } else {\n      // negative bias, this can happen when diffing two histograms\n      this.backing.reverse(0, this.backing.length);\n      this.backing.reverse(0, this.backing.length + bias);\n    }\n    this.indexBase = this.indexStart;\n  }\n\n  /**\n   * _relocateBucket adds the count in counts[src] to counts[dest] and\n   * resets count[src] to zero.\n   */\n  private _relocateBucket(dest: number, src: number) {\n    if (dest === src) {\n      return;\n    }\n    this.incrementBucket(dest, this.backing.emptyBucket(src));\n  }\n}\n\n/**\n * BucketsBacking holds the raw buckets and some utility methods to\n * manage them.\n */\nclass BucketsBacking {\n  constructor(private _counts = [0]) {}\n\n  /**\n   * length returns the physical size of the backing array, which\n   * is >= buckets.length()\n   */\n  get length(): number {\n    return this._counts.length;\n  }\n\n  /**\n   * countAt returns the count in a specific bucket\n   */\n  countAt(pos: number): number {\n    return this._counts[pos];\n  }\n\n  /**\n   * growTo grows a backing array and copies old entries\n   * into their correct new positions.\n   */\n  growTo(newSize: number, oldPositiveLimit: number, newPositiveLimit: number) {\n    const tmp = new Array<number>(newSize).fill(0);\n    tmp.splice(\n      newPositiveLimit,\n      this._counts.length - oldPositiveLimit,\n      ...this._counts.slice(oldPositiveLimit)\n    );\n    tmp.splice(0, oldPositiveLimit, ...this._counts.slice(0, oldPositiveLimit));\n    this._counts = tmp;\n  }\n\n  /**\n   * reverse the items in the backing array in the range [from, limit).\n   */\n  reverse(from: number, limit: number) {\n    const num = Math.floor((from + limit) / 2) - from;\n    for (let i = 0; i < num; i++) {\n      const tmp = this._counts[from + i];\n      this._counts[from + i] = this._counts[limit - i - 1];\n      this._counts[limit - i - 1] = tmp;\n    }\n  }\n\n  /**\n   * emptyBucket empties the count from a bucket, for\n   * moving into another.\n   */\n  emptyBucket(src: number): number {\n    const tmp = this._counts[src];\n    this._counts[src] = 0;\n    return tmp;\n  }\n\n  /**\n   * increments a bucket by `increment`\n   */\n  increment(bucketIndex: number, increment: number) {\n    this._counts[bucketIndex] += increment;\n  }\n\n  /**\n   * decrements a bucket by `decrement`\n   */\n  decrement(bucketIndex: number, decrement: number) {\n    if (this._counts[bucketIndex] >= decrement) {\n      this._counts[bucketIndex] -= decrement;\n    } else {\n      // this should not happen, but we're being defensive against\n      // negative counts.\n      this._counts[bucketIndex] = 0;\n    }\n  }\n\n  /**\n   * clone returns a deep copy of BucketsBacking\n   */\n  clone(): BucketsBacking {\n    return new BucketsBacking([...this._counts]);\n  }\n}\n"]}