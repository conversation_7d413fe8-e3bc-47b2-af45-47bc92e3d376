// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.aiplatform.v1beta1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.AIPlatform.V1Beta1";
option go_package = "cloud.google.com/go/aiplatform/apiv1beta1/aiplatformpb;aiplatformpb";
option java_multiple_files = true;
option java_outer_classname = "MigratableResourceProto";
option java_package = "com.google.cloud.aiplatform.v1beta1";
option php_namespace = "Google\\Cloud\\AIPlatform\\V1beta1";
option ruby_package = "Google::Cloud::AIPlatform::V1beta1";
option (google.api.resource_definition) = {
  type: "ml.googleapis.com/Version"
  pattern: "projects/{project}/models/{model}/versions/{version}"
};
option (google.api.resource_definition) = {
  type: "automl.googleapis.com/Model"
  pattern: "projects/{project}/locations/{location}/models/{model}"
};
option (google.api.resource_definition) = {
  type: "automl.googleapis.com/Dataset"
  pattern: "projects/{project}/locations/{location}/datasets/{dataset}"
};
option (google.api.resource_definition) = {
  type: "datalabeling.googleapis.com/Dataset"
  pattern: "projects/{project}/datasets/{dataset}"
};
option (google.api.resource_definition) = {
  type: "datalabeling.googleapis.com/AnnotatedDataset"
  pattern: "projects/{project}/datasets/{dataset}/annotatedDatasets/{annotated_dataset}"
};

// Represents one resource that exists in automl.googleapis.com,
// datalabeling.googleapis.com or ml.googleapis.com.
message MigratableResource {
  // Represents one model Version in ml.googleapis.com.
  message MlEngineModelVersion {
    // The ml.googleapis.com endpoint that this model Version currently lives
    // in.
    // Example values:
    //
    // * ml.googleapis.com
    // * us-centrall-ml.googleapis.com
    // * europe-west4-ml.googleapis.com
    // * asia-east1-ml.googleapis.com
    string endpoint = 1;

    // Full resource name of ml engine model Version.
    // Format: `projects/{project}/models/{model}/versions/{version}`.
    string version = 2 [
      (google.api.resource_reference) = { type: "ml.googleapis.com/Version" }
    ];
  }

  // Represents one Model in automl.googleapis.com.
  message AutomlModel {
    // Full resource name of automl Model.
    // Format:
    // `projects/{project}/locations/{location}/models/{model}`.
    string model = 1 [
      (google.api.resource_reference) = { type: "automl.googleapis.com/Model" }
    ];

    // The Model's display name in automl.googleapis.com.
    string model_display_name = 3;
  }

  // Represents one Dataset in automl.googleapis.com.
  message AutomlDataset {
    // Full resource name of automl Dataset.
    // Format:
    // `projects/{project}/locations/{location}/datasets/{dataset}`.
    string dataset = 1 [(google.api.resource_reference) = {
      type: "automl.googleapis.com/Dataset"
    }];

    // The Dataset's display name in automl.googleapis.com.
    string dataset_display_name = 4;
  }

  // Represents one Dataset in datalabeling.googleapis.com.
  message DataLabelingDataset {
    // Represents one AnnotatedDataset in datalabeling.googleapis.com.
    message DataLabelingAnnotatedDataset {
      // Full resource name of data labeling AnnotatedDataset.
      // Format:
      // `projects/{project}/datasets/{dataset}/annotatedDatasets/{annotated_dataset}`.
      string annotated_dataset = 1 [(google.api.resource_reference) = {
        type: "datalabeling.googleapis.com/AnnotatedDataset"
      }];

      // The AnnotatedDataset's display name in datalabeling.googleapis.com.
      string annotated_dataset_display_name = 3;
    }

    // Full resource name of data labeling Dataset.
    // Format:
    // `projects/{project}/datasets/{dataset}`.
    string dataset = 1 [(google.api.resource_reference) = {
      type: "datalabeling.googleapis.com/Dataset"
    }];

    // The Dataset's display name in datalabeling.googleapis.com.
    string dataset_display_name = 4;

    // The migratable AnnotatedDataset in datalabeling.googleapis.com belongs to
    // the data labeling Dataset.
    repeated DataLabelingAnnotatedDataset data_labeling_annotated_datasets = 3;
  }

  oneof resource {
    // Output only. Represents one Version in ml.googleapis.com.
    MlEngineModelVersion ml_engine_model_version = 1
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Represents one Model in automl.googleapis.com.
    AutomlModel automl_model = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Represents one Dataset in automl.googleapis.com.
    AutomlDataset automl_dataset = 3
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Represents one Dataset in datalabeling.googleapis.com.
    DataLabelingDataset data_labeling_dataset = 4
        [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // Output only. Timestamp when the last migration attempt on this
  // MigratableResource started. Will not be set if there's no migration attempt
  // on this MigratableResource.
  google.protobuf.Timestamp last_migrate_time = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Timestamp when this MigratableResource was last updated.
  google.protobuf.Timestamp last_update_time = 6
      [(google.api.field_behavior) = OUTPUT_ONLY];
}
