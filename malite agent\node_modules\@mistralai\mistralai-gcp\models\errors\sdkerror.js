"use strict";
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.SDKError = void 0;
class SDKError extends Error {
    constructor(message, rawResponse, body = "") {
        const statusCode = rawResponse.status;
        const contentType = rawResponse.headers.get("content-type") || "";
        const bodyString = body.length > 0 ? `\n${body}` : "";
        super(`${message}: Status ${statusCode} Content-Type ${contentType} Body ${bodyString}`);
        this.rawResponse = rawResponse;
        this.body = body;
        this.statusCode = statusCode;
        this.contentType = contentType;
        this.name = "SDKError";
    }
}
exports.SDKError = SDKError;
//# sourceMappingURL=sdkerror.js.map