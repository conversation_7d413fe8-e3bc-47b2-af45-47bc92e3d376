"use strict";
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AssistantMessage$ = exports.AssistantMessage$outboundSchema = exports.AssistantMessage$inboundSchema = exports.AssistantMessageRole$ = exports.AssistantMessageRole$outboundSchema = exports.AssistantMessageRole$inboundSchema = exports.AssistantMessageContent$ = exports.AssistantMessageContent$outboundSchema = exports.AssistantMessageContent$inboundSchema = exports.AssistantMessageRole = void 0;
exports.assistantMessageContentToJSON = assistantMessageContentToJSON;
exports.assistantMessageContentFromJSON = assistantMessageContentFromJSON;
exports.assistantMessageToJSON = assistantMessageToJSON;
exports.assistantMessageFromJSON = assistantMessageFromJSON;
const z = __importStar(require("zod"));
const primitives_js_1 = require("../../lib/primitives.js");
const schemas_js_1 = require("../../lib/schemas.js");
const contentchunk_js_1 = require("./contentchunk.js");
const toolcall_js_1 = require("./toolcall.js");
exports.AssistantMessageRole = {
    Assistant: "assistant",
};
/** @internal */
exports.AssistantMessageContent$inboundSchema = z.union([z.string(), z.array(contentchunk_js_1.ContentChunk$inboundSchema)]);
/** @internal */
exports.AssistantMessageContent$outboundSchema = z.union([z.string(), z.array(contentchunk_js_1.ContentChunk$outboundSchema)]);
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var AssistantMessageContent$;
(function (AssistantMessageContent$) {
    /** @deprecated use `AssistantMessageContent$inboundSchema` instead. */
    AssistantMessageContent$.inboundSchema = exports.AssistantMessageContent$inboundSchema;
    /** @deprecated use `AssistantMessageContent$outboundSchema` instead. */
    AssistantMessageContent$.outboundSchema = exports.AssistantMessageContent$outboundSchema;
})(AssistantMessageContent$ || (exports.AssistantMessageContent$ = AssistantMessageContent$ = {}));
function assistantMessageContentToJSON(assistantMessageContent) {
    return JSON.stringify(exports.AssistantMessageContent$outboundSchema.parse(assistantMessageContent));
}
function assistantMessageContentFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.AssistantMessageContent$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'AssistantMessageContent' from JSON`);
}
/** @internal */
exports.AssistantMessageRole$inboundSchema = z.nativeEnum(exports.AssistantMessageRole);
/** @internal */
exports.AssistantMessageRole$outboundSchema = exports.AssistantMessageRole$inboundSchema;
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var AssistantMessageRole$;
(function (AssistantMessageRole$) {
    /** @deprecated use `AssistantMessageRole$inboundSchema` instead. */
    AssistantMessageRole$.inboundSchema = exports.AssistantMessageRole$inboundSchema;
    /** @deprecated use `AssistantMessageRole$outboundSchema` instead. */
    AssistantMessageRole$.outboundSchema = exports.AssistantMessageRole$outboundSchema;
})(AssistantMessageRole$ || (exports.AssistantMessageRole$ = AssistantMessageRole$ = {}));
/** @internal */
exports.AssistantMessage$inboundSchema = z.object({
    content: z.nullable(z.union([z.string(), z.array(contentchunk_js_1.ContentChunk$inboundSchema)])).optional(),
    tool_calls: z.nullable(z.array(toolcall_js_1.ToolCall$inboundSchema)).optional(),
    prefix: z.boolean().default(false),
    role: exports.AssistantMessageRole$inboundSchema.default("assistant"),
}).transform((v) => {
    return (0, primitives_js_1.remap)(v, {
        "tool_calls": "toolCalls",
    });
});
/** @internal */
exports.AssistantMessage$outboundSchema = z.object({
    content: z.nullable(z.union([z.string(), z.array(contentchunk_js_1.ContentChunk$outboundSchema)])).optional(),
    toolCalls: z.nullable(z.array(toolcall_js_1.ToolCall$outboundSchema)).optional(),
    prefix: z.boolean().default(false),
    role: exports.AssistantMessageRole$outboundSchema.default("assistant"),
}).transform((v) => {
    return (0, primitives_js_1.remap)(v, {
        toolCalls: "tool_calls",
    });
});
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var AssistantMessage$;
(function (AssistantMessage$) {
    /** @deprecated use `AssistantMessage$inboundSchema` instead. */
    AssistantMessage$.inboundSchema = exports.AssistantMessage$inboundSchema;
    /** @deprecated use `AssistantMessage$outboundSchema` instead. */
    AssistantMessage$.outboundSchema = exports.AssistantMessage$outboundSchema;
})(AssistantMessage$ || (exports.AssistantMessage$ = AssistantMessage$ = {}));
function assistantMessageToJSON(assistantMessage) {
    return JSON.stringify(exports.AssistantMessage$outboundSchema.parse(assistantMessage));
}
function assistantMessageFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.AssistantMessage$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'AssistantMessage' from JSON`);
}
//# sourceMappingURL=assistantmessage.js.map