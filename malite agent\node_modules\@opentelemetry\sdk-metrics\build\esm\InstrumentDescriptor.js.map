{"version": 3, "file": "InstrumentDescriptor.js", "sourceRoot": "", "sources": ["../../src/InstrumentDescriptor.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAEH,OAAO,EAAiB,SAAS,EAAE,IAAI,EAAE,MAAM,oBAAoB,CAAC;AAEpE,OAAO,EAAE,qBAAqB,EAAE,MAAM,SAAS,CAAC;AAEhD;;GAEG;AACH,MAAM,CAAN,IAAY,cAQX;AARD,WAAY,cAAc;IACxB,qCAAmB,CAAA;IACnB,iCAAe,CAAA;IACf,yCAAuB,CAAA;IACvB,qDAAmC,CAAA;IACnC,2DAAyC,CAAA;IACzC,uDAAqC,CAAA;IACrC,2EAAyD,CAAA;AAC3D,CAAC,EARW,cAAc,KAAd,cAAc,QAQzB;AA6BD,MAAM,UAAU,0BAA0B,CACxC,IAAY,EACZ,IAAoB,EACpB,OAAuB;;IAEvB,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE;QACtB,IAAI,CAAC,IAAI,CACP,4BAAyB,IAAI,+FAA2F,CACzH,CAAC;KACH;IACD,OAAO;QACL,IAAI,MAAA;QACJ,IAAI,MAAA;QACJ,WAAW,EAAE,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,WAAW,mCAAI,EAAE;QACvC,IAAI,EAAE,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,IAAI,mCAAI,EAAE;QACzB,SAAS,EAAE,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,SAAS,mCAAI,SAAS,CAAC,MAAM;QACjD,MAAM,EAAE,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,MAAM,mCAAI,EAAE;KAC9B,CAAC;AACJ,CAAC;AAED,MAAM,UAAU,kCAAkC,CAChD,IAAU,EACV,UAAgC;;IAEhC,OAAO;QACL,IAAI,EAAE,MAAA,IAAI,CAAC,IAAI,mCAAI,UAAU,CAAC,IAAI;QAClC,WAAW,EAAE,MAAA,IAAI,CAAC,WAAW,mCAAI,UAAU,CAAC,WAAW;QACvD,IAAI,EAAE,UAAU,CAAC,IAAI;QACrB,IAAI,EAAE,UAAU,CAAC,IAAI;QACrB,SAAS,EAAE,UAAU,CAAC,SAAS;QAC/B,MAAM,EAAE,UAAU,CAAC,MAAM;KAC1B,CAAC;AACJ,CAAC;AAED,MAAM,UAAU,0BAA0B,CACxC,UAAgC,EAChC,eAAqC;IAErC,sCAAsC;IACtC,OAAO,CACL,qBAAqB,CAAC,UAAU,CAAC,IAAI,EAAE,eAAe,CAAC,IAAI,CAAC;QAC5D,UAAU,CAAC,IAAI,KAAK,eAAe,CAAC,IAAI;QACxC,UAAU,CAAC,IAAI,KAAK,eAAe,CAAC,IAAI;QACxC,UAAU,CAAC,SAAS,KAAK,eAAe,CAAC,SAAS,CACnD,CAAC;AACJ,CAAC;AAED,6DAA6D;AAC7D,4DAA4D;AAC5D,IAAM,WAAW,GAAG,8BAA8B,CAAC;AACnD,MAAM,UAAU,WAAW,CAAC,IAAY;IACtC,OAAO,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,IAAI,CAAC;AACzC,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { MetricOptions, ValueType, diag } from '@opentelemetry/api';\nimport { View } from './view/View';\nimport { equalsCaseInsensitive } from './utils';\n\n/**\n * Supported types of metric instruments.\n */\nexport enum InstrumentType {\n  COUNTER = 'COUNTER',\n  GAUGE = 'GAUGE',\n  HISTOGRAM = 'HISTOGRAM',\n  UP_DOWN_COUNTER = 'UP_DOWN_COUNTER',\n  OBSERVABLE_COUNTER = 'OBSERVABLE_COUNTER',\n  OBSERVABLE_GAUGE = 'OBSERVABLE_GAUGE',\n  OBSERVABLE_UP_DOWN_COUNTER = 'OBSERVABLE_UP_DOWN_COUNTER',\n}\n\n/**\n * An internal interface describing the instrument.\n *\n * This is intentionally distinguished from the public MetricDescriptor (a.k.a. InstrumentDescriptor)\n * which may not contains internal fields like metric advice.\n */\nexport interface InstrumentDescriptor {\n  readonly name: string;\n  readonly description: string;\n  readonly unit: string;\n  readonly type: InstrumentType;\n  readonly valueType: ValueType;\n  /**\n   * @experimental\n   *\n   * This is intentionally not using the API's type as it's only available from @opentelemetry/api 1.7.0 and up.\n   * In SDK 2.0 we'll be able to bump the minimum API version and remove this workaround.\n   */\n  readonly advice: {\n    /**\n     * Hint the explicit bucket boundaries for SDK if the metric has been\n     * aggregated with a HistogramAggregator.\n     */\n    explicitBucketBoundaries?: number[];\n  };\n}\n\nexport function createInstrumentDescriptor(\n  name: string,\n  type: InstrumentType,\n  options?: MetricOptions\n): InstrumentDescriptor {\n  if (!isValidName(name)) {\n    diag.warn(\n      `Invalid metric name: \"${name}\". The metric name should be a ASCII string with a length no greater than 255 characters.`\n    );\n  }\n  return {\n    name,\n    type,\n    description: options?.description ?? '',\n    unit: options?.unit ?? '',\n    valueType: options?.valueType ?? ValueType.DOUBLE,\n    advice: options?.advice ?? {},\n  };\n}\n\nexport function createInstrumentDescriptorWithView(\n  view: View,\n  instrument: InstrumentDescriptor\n): InstrumentDescriptor {\n  return {\n    name: view.name ?? instrument.name,\n    description: view.description ?? instrument.description,\n    type: instrument.type,\n    unit: instrument.unit,\n    valueType: instrument.valueType,\n    advice: instrument.advice,\n  };\n}\n\nexport function isDescriptorCompatibleWith(\n  descriptor: InstrumentDescriptor,\n  otherDescriptor: InstrumentDescriptor\n) {\n  // Names are case-insensitive strings.\n  return (\n    equalsCaseInsensitive(descriptor.name, otherDescriptor.name) &&\n    descriptor.unit === otherDescriptor.unit &&\n    descriptor.type === otherDescriptor.type &&\n    descriptor.valueType === otherDescriptor.valueType\n  );\n}\n\n// ASCII string with a length no greater than 255 characters.\n// NB: the first character counted separately from the rest.\nconst NAME_REGEXP = /^[a-z][a-z0-9_.\\-/]{0,254}$/i;\nexport function isValidName(name: string): boolean {\n  return name.match(NAME_REGEXP) != null;\n}\n"]}