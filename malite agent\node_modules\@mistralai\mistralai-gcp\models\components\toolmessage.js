"use strict";
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ToolMessage$ = exports.ToolMessage$outboundSchema = exports.ToolMessage$inboundSchema = exports.ToolMessageRole$ = exports.ToolMessageRole$outboundSchema = exports.ToolMessageRole$inboundSchema = exports.ToolMessageContent$ = exports.ToolMessageContent$outboundSchema = exports.ToolMessageContent$inboundSchema = exports.ToolMessageRole = void 0;
exports.toolMessageContentToJSON = toolMessageContentToJSON;
exports.toolMessageContentFromJSON = toolMessageContentFromJSON;
exports.toolMessageToJSON = toolMessageToJSON;
exports.toolMessageFromJSON = toolMessageFromJSON;
const z = __importStar(require("zod"));
const primitives_js_1 = require("../../lib/primitives.js");
const schemas_js_1 = require("../../lib/schemas.js");
const contentchunk_js_1 = require("./contentchunk.js");
exports.ToolMessageRole = {
    Tool: "tool",
};
/** @internal */
exports.ToolMessageContent$inboundSchema = z.union([z.string(), z.array(contentchunk_js_1.ContentChunk$inboundSchema)]);
/** @internal */
exports.ToolMessageContent$outboundSchema = z.union([z.string(), z.array(contentchunk_js_1.ContentChunk$outboundSchema)]);
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var ToolMessageContent$;
(function (ToolMessageContent$) {
    /** @deprecated use `ToolMessageContent$inboundSchema` instead. */
    ToolMessageContent$.inboundSchema = exports.ToolMessageContent$inboundSchema;
    /** @deprecated use `ToolMessageContent$outboundSchema` instead. */
    ToolMessageContent$.outboundSchema = exports.ToolMessageContent$outboundSchema;
})(ToolMessageContent$ || (exports.ToolMessageContent$ = ToolMessageContent$ = {}));
function toolMessageContentToJSON(toolMessageContent) {
    return JSON.stringify(exports.ToolMessageContent$outboundSchema.parse(toolMessageContent));
}
function toolMessageContentFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.ToolMessageContent$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'ToolMessageContent' from JSON`);
}
/** @internal */
exports.ToolMessageRole$inboundSchema = z.nativeEnum(exports.ToolMessageRole);
/** @internal */
exports.ToolMessageRole$outboundSchema = exports.ToolMessageRole$inboundSchema;
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var ToolMessageRole$;
(function (ToolMessageRole$) {
    /** @deprecated use `ToolMessageRole$inboundSchema` instead. */
    ToolMessageRole$.inboundSchema = exports.ToolMessageRole$inboundSchema;
    /** @deprecated use `ToolMessageRole$outboundSchema` instead. */
    ToolMessageRole$.outboundSchema = exports.ToolMessageRole$outboundSchema;
})(ToolMessageRole$ || (exports.ToolMessageRole$ = ToolMessageRole$ = {}));
/** @internal */
exports.ToolMessage$inboundSchema = z.object({
    content: z.nullable(z.union([z.string(), z.array(contentchunk_js_1.ContentChunk$inboundSchema)])),
    tool_call_id: z.nullable(z.string()).optional(),
    name: z.nullable(z.string()).optional(),
    role: exports.ToolMessageRole$inboundSchema.default("tool"),
}).transform((v) => {
    return (0, primitives_js_1.remap)(v, {
        "tool_call_id": "toolCallId",
    });
});
/** @internal */
exports.ToolMessage$outboundSchema = z.object({
    content: z.nullable(z.union([z.string(), z.array(contentchunk_js_1.ContentChunk$outboundSchema)])),
    toolCallId: z.nullable(z.string()).optional(),
    name: z.nullable(z.string()).optional(),
    role: exports.ToolMessageRole$outboundSchema.default("tool"),
}).transform((v) => {
    return (0, primitives_js_1.remap)(v, {
        toolCallId: "tool_call_id",
    });
});
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var ToolMessage$;
(function (ToolMessage$) {
    /** @deprecated use `ToolMessage$inboundSchema` instead. */
    ToolMessage$.inboundSchema = exports.ToolMessage$inboundSchema;
    /** @deprecated use `ToolMessage$outboundSchema` instead. */
    ToolMessage$.outboundSchema = exports.ToolMessage$outboundSchema;
})(ToolMessage$ || (exports.ToolMessage$ = ToolMessage$ = {}));
function toolMessageToJSON(toolMessage) {
    return JSON.stringify(exports.ToolMessage$outboundSchema.parse(toolMessage));
}
function toolMessageFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.ToolMessage$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'ToolMessage' from JSON`);
}
//# sourceMappingURL=toolmessage.js.map