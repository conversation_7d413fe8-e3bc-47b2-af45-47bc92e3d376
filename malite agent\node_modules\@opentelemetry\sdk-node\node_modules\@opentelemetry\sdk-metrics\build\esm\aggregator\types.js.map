{"version": 3, "file": "types.js", "sourceRoot": "", "sources": ["../../../src/aggregator/types.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAOH,8BAA8B;AAC9B,MAAM,CAAN,IAAY,cAMX;AAND,WAAY,cAAc;IACxB,mDAAI,CAAA;IACJ,iDAAG,CAAA;IACH,+DAAU,CAAA;IACV,6DAAS,CAAA;IACT,qFAAqB,CAAA;AACvB,CAAC,EANW,cAAc,KAAd,cAAc,QAMzB", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { HrTime, MetricAttributes } from '@opentelemetry/api';\nimport { AggregationTemporality } from '../export/AggregationTemporality';\nimport { MetricData, MetricDescriptor } from '../export/MetricData';\nimport { Maybe } from '../utils';\n\n/** The kind of aggregator. */\nexport enum AggregatorKind {\n  DROP,\n  SUM,\n  LAST_VALUE,\n  HISTOGRAM,\n  EXPONENTIAL_HISTOGRAM,\n}\n\n/** DataPoint value type for SumAggregation. */\nexport type Sum = number;\n\n/** DataPoint value type for LastValueAggregation. */\nexport type LastValue = number;\n\n/** DataPoint value type for HistogramAggregation. */\nexport interface Histogram {\n  /**\n   * Buckets are implemented using two different arrays:\n   *  - boundaries: contains every finite bucket boundary, which are inclusive lower bounds\n   *  - counts: contains event counts for each bucket\n   *\n   * Note that we'll always have n+1 buckets, where n is the number of boundaries.\n   * This is because we need to count events that are below the lowest boundary.\n   *\n   * Example: if we measure the values: [5, 30, 5, 40, 5, 15, 15, 15, 25]\n   *  with the boundaries [ 10, 20, 30 ], we will have the following state:\n   *\n   * buckets: {\n   *\tboundaries: [10, 20, 30],\n   *\tcounts: [3, 3, 1, 2],\n   * }\n   */\n  buckets: {\n    boundaries: number[];\n    counts: number[];\n  };\n  sum?: number;\n  count: number;\n  min?: number;\n  max?: number;\n}\n\n/** DataPoint value type for ExponentialHistogramAggregation. */\nexport interface ExponentialHistogram {\n  count: number;\n  sum?: number;\n  scale: number;\n  zeroCount: number;\n  positive: {\n    offset: number;\n    bucketCounts: number[];\n  };\n  negative: {\n    offset: number;\n    bucketCounts: number[];\n  };\n  min?: number;\n  max?: number;\n}\n\n/**\n * An Aggregator accumulation state.\n */\nexport interface Accumulation {\n  setStartTime(startTime: HrTime): void;\n  record(value: number): void;\n}\n\nexport type AccumulationRecord<T> = [MetricAttributes, T];\n\n/**\n * Base interface for aggregators. Aggregators are responsible for holding\n * aggregated values and taking a snapshot of these values upon export.\n */\nexport interface Aggregator<T> {\n  /** The kind of the aggregator. */\n  kind: AggregatorKind;\n\n  /**\n   * Create a clean state of accumulation.\n   */\n  createAccumulation(startTime: HrTime): T;\n\n  /**\n   * Returns the result of the merge of the given accumulations.\n   *\n   * This should always assume that the accumulations do not overlap and merge together for a new\n   * cumulative report.\n   *\n   * @param previous the previously captured accumulation\n   * @param delta the newly captured (delta) accumulation\n   * @returns the result of the merge of the given accumulations\n   */\n  merge(previous: T, delta: T): T;\n\n  /**\n   * Returns a new DELTA aggregation by comparing two cumulative measurements.\n   *\n   * @param previous the previously captured accumulation\n   * @param current the newly captured (cumulative) accumulation\n   * @returns The resulting delta accumulation\n   */\n  diff(previous: T, current: T): T;\n\n  /**\n   * Returns the {@link MetricData} that this {@link Aggregator} will produce.\n   *\n   * @param descriptor the metric descriptor.\n   * @param aggregationTemporality the temporality of the resulting {@link MetricData}\n   * @param accumulationByAttributes the array of attributes and accumulation pairs.\n   * @param endTime the end time of the metric data.\n   * @return the {@link MetricData} that this {@link Aggregator} will produce.\n   */\n  toMetricData(\n    descriptor: MetricDescriptor,\n    aggregationTemporality: AggregationTemporality,\n    accumulationByAttributes: AccumulationRecord<T>[],\n    endTime: HrTime\n  ): Maybe<MetricData>;\n}\n"]}