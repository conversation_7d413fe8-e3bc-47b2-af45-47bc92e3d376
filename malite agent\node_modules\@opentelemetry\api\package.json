{"name": "@opentelemetry/api", "version": "1.9.0", "description": "Public API for OpenTelemetry", "main": "build/src/index.js", "module": "build/esm/index.js", "esnext": "build/esnext/index.js", "types": "build/src/index.d.ts", "browser": {"./src/platform/index.ts": "./src/platform/browser/index.ts", "./build/esm/platform/index.js": "./build/esm/platform/browser/index.js", "./build/esnext/platform/index.js": "./build/esnext/platform/browser/index.js", "./build/src/platform/index.js": "./build/src/platform/browser/index.js"}, "exports": {".": {"module": "./build/esm/index.js", "esnext": "./build/esnext/index.js", "types": "./build/src/index.d.ts", "default": "./build/src/index.js"}, "./experimental": {"module": "./build/esm/experimental/index.js", "esnext": "./build/esnext/experimental/index.js", "types": "./build/src/experimental/index.d.ts", "default": "./build/src/experimental/index.js"}}, "repository": "open-telemetry/opentelemetry-js", "scripts": {"clean": "tsc --build --clean tsconfig.json tsconfig.esm.json tsconfig.esnext.json", "codecov:browser": "nyc report --reporter=json && codecov -f coverage/*.json -p ../", "codecov:webworker": "nyc report --reporter=json && codecov -f coverage/*.json -p ../", "codecov": "nyc report --reporter=json && codecov -f coverage/*.json -p ../", "precompile": "cross-var lerna run version --scope $npm_package_name --include-dependencies", "compile": "tsc --build tsconfig.json tsconfig.esm.json tsconfig.esnext.json", "docs": "typedoc", "docs:deploy": "gh-pages --dist docs/out", "docs:test": "linkinator docs/out --silent && linkinator docs/*.md *.md --markdown --silent", "lint:fix": "eslint . --ext .ts --fix", "lint": "eslint . --ext .ts", "test:browser": "karma start --single-run", "test": "nyc ts-mocha -p tsconfig.json 'test/**/*.test.ts'", "test:eol": "ts-mocha -p tsconfig.json 'test/**/*.test.ts'", "test:webworker": "karma start karma.worker.js --single-run", "cycle-check": "dpdm --exit-code circular:1 src/index.ts", "version": "node ../scripts/version-update.js", "prewatch": "npm run precompile", "watch": "tsc --build --watch tsconfig.json tsconfig.esm.json tsconfig.esnext.json", "peer-api-check": "node ../scripts/peer-api-check.js"}, "keywords": ["opentelemetry", "nodejs", "browser", "tracing", "profiling", "stats", "monitoring"], "author": "OpenTelemetry Authors", "license": "Apache-2.0", "engines": {"node": ">=8.0.0"}, "files": ["build/esm/**/*.js", "build/esm/**/*.js.map", "build/esm/**/*.d.ts", "build/esnext/**/*.js", "build/esnext/**/*.js.map", "build/esnext/**/*.d.ts", "build/src/**/*.js", "build/src/**/*.js.map", "build/src/**/*.d.ts", "LICENSE", "README.md"], "publishConfig": {"access": "public"}, "devDependencies": {"@types/mocha": "10.0.6", "@types/node": "18.6.5", "@types/sinon": "17.0.3", "@types/webpack": "5.28.5", "@types/webpack-env": "1.16.3", "babel-plugin-istanbul": "6.1.1", "codecov": "3.8.3", "cross-var": "1.1.0", "dpdm": "3.13.1", "karma": "6.4.3", "karma-chrome-launcher": "3.1.0", "karma-coverage": "2.2.1", "karma-mocha": "2.0.1", "karma-mocha-webworker": "1.3.0", "karma-spec-reporter": "0.0.36", "karma-webpack": "5.0.1", "lerna": "6.6.2", "memfs": "3.5.3", "mocha": "10.2.0", "nyc": "15.1.0", "sinon": "15.1.2", "ts-loader": "9.5.1", "ts-mocha": "10.0.0", "typescript": "4.4.4", "unionfs": "4.5.4", "webpack": "5.89.0"}, "homepage": "https://github.com/open-telemetry/opentelemetry-js/tree/main/api", "sideEffects": false, "gitHead": "c4d3351b6b3f5593c8d7cbfec97b45cea9fe1511"}