{"version": 3, "sources": ["../../../src/vectorsearch/vector_search/types.ts"], "sourcesContent": ["/**\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport * as aiplatform from '@google-cloud/aiplatform';\nimport { z } from 'genkit';\nimport { EmbedderReference } from 'genkit/embedder';\nimport { CommonRetrieverOptionsSchema, Document } from 'genkit/retriever';\nimport { GoogleAuth } from 'google-auth-library';\nimport { PluginOptions } from '../types.js';\n\n// This internal interface will be passed to the vertexIndexers and vertexRetrievers functions\nexport interface VertexVectorSearchOptions<\n  EmbedderCustomOptions extends z.ZodTypeAny,\n> {\n  pluginOptions: PluginOptions;\n  authClient: GoogleAuth;\n  defaultEmbedder?: EmbedderReference<EmbedderCustomOptions>;\n}\n\nexport type IIndexDatapoint =\n  aiplatform.protos.google.cloud.aiplatform.v1.IIndexDatapoint;\n\nexport class Datapoint extends aiplatform.protos.google.cloud.aiplatform.v1\n  .IndexDatapoint {\n  constructor(properties: IIndexDatapoint) {\n    super(properties);\n  }\n}\n\nexport type IFindNeighborsRequest =\n  aiplatform.protos.google.cloud.aiplatform.v1.IFindNeighborsRequest;\nexport type IFindNeighborsResponse =\n  aiplatform.protos.google.cloud.aiplatform.v1.IFindNeighborsResponse;\nexport type ISparseEmbedding =\n  aiplatform.protos.google.cloud.aiplatform.v1.IndexDatapoint.ISparseEmbedding;\nexport type IRestriction =\n  aiplatform.protos.google.cloud.aiplatform.v1.IndexDatapoint.IRestriction;\nexport type INumericRestriction =\n  aiplatform.protos.google.cloud.aiplatform.v1.IndexDatapoint.INumericRestriction;\n\n// Define the Zod schema for ISparseEmbedding\nexport const SparseEmbeddingSchema = z.object({\n  values: z.array(z.number()).optional(),\n  dimensions: z.array(z.union([z.number(), z.string()])).optional(),\n});\n\nexport type SparseEmbedding = z.infer<typeof SparseEmbeddingSchema>;\n\n// Define the Zod schema for IRestriction\nexport const RestrictionSchema = z.object({\n  namespace: z.string(),\n  allowList: z.array(z.string()),\n  denyList: z.array(z.string()),\n});\n\nexport type Restriction = z.infer<typeof RestrictionSchema>;\n\nexport const NumericRestrictionOperatorSchema = z.enum([\n  'OPERATOR_UNSPECIFIED',\n  'LESS',\n  'LESS_EQUAL',\n  'EQUAL',\n  'GREATER_EQUAL',\n  'GREATER',\n  'NOT_EQUAL',\n]);\n\nexport type NumericRestrictionOperator = z.infer<\n  typeof NumericRestrictionOperatorSchema\n>;\n\n// Define the Zod schema for INumericRestriction\nexport const NumericRestrictionSchema = z.object({\n  valueInt: z.union([z.number(), z.string()]).optional(),\n  valueFloat: z.number().optional(),\n  valueDouble: z.number().optional(),\n  namespace: z.string(),\n  op: z.union([NumericRestrictionOperatorSchema, z.null()]).optional(),\n});\n\nexport type NumericRestriction = z.infer<typeof NumericRestrictionSchema>;\n\n// Define the Zod schema for ICrowdingTag\nexport const CrowdingTagSchema = z.object({\n  crowdingAttribute: z.string().optional(),\n});\n\nexport type CrowdingTag = z.infer<typeof CrowdingTagSchema>;\n\n// Define the Zod schema for IIndexDatapoint\nconst IndexDatapointSchema = z.object({\n  datapointId: z.string().optional(),\n  featureVector: z.array(z.number()).optional(),\n  sparseEmbedding: SparseEmbeddingSchema.optional(),\n  restricts: z.array(RestrictionSchema).optional(),\n  numericRestricts: z.array(NumericRestrictionSchema).optional(),\n  crowdingTag: CrowdingTagSchema.optional(),\n});\n\n// Define the Zod schema for INeighbor\nexport const NeighborSchema = z.object({\n  datapoint: IndexDatapointSchema.optional(),\n  distance: z.number().optional(),\n  sparseDistance: z.number().optional(),\n});\n\nexport type Neighbor = z.infer<typeof NeighborSchema>;\n\n// Define the Zod schema for INearestNeighbors\nconst NearestNeighborsSchema = z.object({\n  id: z.string().optional(),\n  neighbors: z.array(NeighborSchema).optional(),\n});\n\n// Define the Zod schema for IFindNeighborsResponse\nexport const FindNeighborsResponseSchema = z.object({\n  nearestNeighbors: z.array(NearestNeighborsSchema).optional(),\n});\n\nexport type FindNeighborsResponse = z.infer<typeof FindNeighborsResponseSchema>;\n\n// TypeScript types for Zod schemas\ntype IndexDatapoint = z.infer<typeof IndexDatapointSchema>;\n\n// Function to assert type equality\nfunction assertTypeEquality<T>(value: T): void {}\n\n// Asserting type equality\nassertTypeEquality<IIndexDatapoint>({} as IndexDatapoint);\nassertTypeEquality<IFindNeighborsResponse>({} as FindNeighborsResponse);\n\nexport const VertexAIVectorRetrieverOptionsSchema =\n  CommonRetrieverOptionsSchema.extend({}).optional();\n\nexport type VertexAIVectorRetrieverOptions = z.infer<\n  typeof VertexAIVectorRetrieverOptionsSchema\n>;\n\nexport const VertexAIVectorIndexerOptionsSchema = z.any();\n\nexport type VertexAIVectorIndexerOptions = z.infer<\n  typeof VertexAIVectorIndexerOptionsSchema\n>;\n\n/**\n * A document retriever function that takes an array of Neighbors from Vertex AI Vector Search query result, and resolves to a list of documents.\n * Also takes an options object that can be used to configure the retriever.\n */\nexport type DocumentRetriever<Options extends { k?: number } = { k?: number }> =\n  (docIds: Neighbor[], options?: Options) => Promise<Document[]>;\n\n/**\n * Indexer function that takes an array of documents, stores them in a database of the user's choice, and resolves to a list of document ids.\n * Also takes an options object that can be used to configure the indexer. Only Streaming Update Indexers are supported.\n */\nexport type DocumentIndexer<Options extends {} = {}> = (\n  docs: Document[],\n  options?: Options\n) => Promise<string[]>;\n\nexport interface VectorSearchOptions<\n  EmbedderCustomOptions extends z.ZodTypeAny,\n  IndexerOptions extends {},\n  RetrieverOptions extends { k?: number },\n> {\n  // Specify the Vertex AI Index and IndexEndpoint to use for indexing and retrieval\n  deployedIndexId: string;\n  indexEndpointId: string;\n  publicDomainName: string;\n  indexId: string;\n  // Document retriever and indexer functions to use for indexing and retrieval by the plugin's own indexers and retrievers\n  documentRetriever: DocumentRetriever<RetrieverOptions>;\n  documentIndexer: DocumentIndexer<IndexerOptions>;\n  // Embedder and default options to use for indexing and retrieval\n  embedder?: EmbedderReference<EmbedderCustomOptions>;\n  embedderOptions?: z.infer<EmbedderCustomOptions>;\n}\n"], "mappings": "AAgBA,YAAY,gBAAgB;AAC5B,SAAS,SAAS;AAElB,SAAS,oCAA8C;AAgBhD,MAAM,kBAAkB,WAAW,OAAO,OAAO,MAAM,WAAW,GACtE,eAAe;AAAA,EAChB,YAAY,YAA6B;AACvC,UAAM,UAAU;AAAA,EAClB;AACF;AAcO,MAAM,wBAAwB,EAAE,OAAO;AAAA,EAC5C,QAAQ,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,SAAS;AAAA,EACrC,YAAY,EAAE,MAAM,EAAE,MAAM,CAAC,EAAE,OAAO,GAAG,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,SAAS;AAClE,CAAC;AAKM,MAAM,oBAAoB,EAAE,OAAO;AAAA,EACxC,WAAW,EAAE,OAAO;AAAA,EACpB,WAAW,EAAE,MAAM,EAAE,OAAO,CAAC;AAAA,EAC7B,UAAU,EAAE,MAAM,EAAE,OAAO,CAAC;AAC9B,CAAC;AAIM,MAAM,mCAAmC,EAAE,KAAK;AAAA,EACrD;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,CAAC;AAOM,MAAM,2BAA2B,EAAE,OAAO;AAAA,EAC/C,UAAU,EAAE,MAAM,CAAC,EAAE,OAAO,GAAG,EAAE,OAAO,CAAC,CAAC,EAAE,SAAS;AAAA,EACrD,YAAY,EAAE,OAAO,EAAE,SAAS;AAAA,EAChC,aAAa,EAAE,OAAO,EAAE,SAAS;AAAA,EACjC,WAAW,EAAE,OAAO;AAAA,EACpB,IAAI,EAAE,MAAM,CAAC,kCAAkC,EAAE,KAAK,CAAC,CAAC,EAAE,SAAS;AACrE,CAAC;AAKM,MAAM,oBAAoB,EAAE,OAAO;AAAA,EACxC,mBAAmB,EAAE,OAAO,EAAE,SAAS;AACzC,CAAC;AAKD,MAAM,uBAAuB,EAAE,OAAO;AAAA,EACpC,aAAa,EAAE,OAAO,EAAE,SAAS;AAAA,EACjC,eAAe,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,SAAS;AAAA,EAC5C,iBAAiB,sBAAsB,SAAS;AAAA,EAChD,WAAW,EAAE,MAAM,iBAAiB,EAAE,SAAS;AAAA,EAC/C,kBAAkB,EAAE,MAAM,wBAAwB,EAAE,SAAS;AAAA,EAC7D,aAAa,kBAAkB,SAAS;AAC1C,CAAC;AAGM,MAAM,iBAAiB,EAAE,OAAO;AAAA,EACrC,WAAW,qBAAqB,SAAS;AAAA,EACzC,UAAU,EAAE,OAAO,EAAE,SAAS;AAAA,EAC9B,gBAAgB,EAAE,OAAO,EAAE,SAAS;AACtC,CAAC;AAKD,MAAM,yBAAyB,EAAE,OAAO;AAAA,EACtC,IAAI,EAAE,OAAO,EAAE,SAAS;AAAA,EACxB,WAAW,EAAE,MAAM,cAAc,EAAE,SAAS;AAC9C,CAAC;AAGM,MAAM,8BAA8B,EAAE,OAAO;AAAA,EAClD,kBAAkB,EAAE,MAAM,sBAAsB,EAAE,SAAS;AAC7D,CAAC;AAQD,SAAS,mBAAsB,OAAgB;AAAC;AAGhD,mBAAoC,CAAC,CAAmB;AACxD,mBAA2C,CAAC,CAA0B;AAE/D,MAAM,uCACX,6BAA6B,OAAO,CAAC,CAAC,EAAE,SAAS;AAM5C,MAAM,qCAAqC,EAAE,IAAI;", "names": []}