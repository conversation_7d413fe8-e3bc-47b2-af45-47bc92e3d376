"use strict";
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.blobLikeSchema = void 0;
exports.isBlobLike = isBlobLike;
const z = __importStar(require("zod"));
exports.blobLikeSchema = z.custom(isBlobLike, {
    message: "expected a Blob, File or Blob-like object",
    fatal: true,
});
function isBlobLike(val) {
    if (val instanceof Blob) {
        return true;
    }
    if (typeof val !== "object" || val == null || !(Symbol.toStringTag in val)) {
        return false;
    }
    const name = val[Symbol.toStringTag];
    if (typeof name !== "string") {
        return false;
    }
    if (name !== "Blob" && name !== "File") {
        return false;
    }
    return "stream" in val && typeof val.stream === "function";
}
//# sourceMappingURL=blobs.js.map