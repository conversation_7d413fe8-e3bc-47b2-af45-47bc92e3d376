{"interfaces": {"google.cloud.aiplatform.v1.FeaturestoreService": {"retry_codes": {"non_idempotent": [], "idempotent": ["DEADLINE_EXCEEDED", "UNAVAILABLE"]}, "retry_params": {"default": {"initial_retry_delay_millis": 100, "retry_delay_multiplier": 1.3, "max_retry_delay_millis": 60000, "initial_rpc_timeout_millis": 60000, "rpc_timeout_multiplier": 1, "max_rpc_timeout_millis": 60000, "total_timeout_millis": 600000}}, "methods": {"CreateFeaturestore": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "GetFeaturestore": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "ListFeaturestores": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "UpdateFeaturestore": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "DeleteFeaturestore": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "CreateEntityType": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "GetEntityType": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "ListEntityTypes": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "UpdateEntityType": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "DeleteEntityType": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "CreateFeature": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "BatchCreateFeatures": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "GetFeature": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "ListFeatures": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "UpdateFeature": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "DeleteFeature": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "ImportFeatureValues": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "BatchReadFeatureValues": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "ExportFeatureValues": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "DeleteFeatureValues": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "SearchFeatures": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}}}}}