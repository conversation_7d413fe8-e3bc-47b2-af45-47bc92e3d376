import { GENKIT_CLIENT_HEADER } from "genkit";
async function listModels(authClient, location, projectId) {
  const fetch = (await import("node-fetch")).default;
  const accessToken = await authClient.getAccessToken();
  const response = await fetch(
    `https://${location}-aiplatform.googleapis.com/v1beta1/publishers/google/models`,
    {
      method: "GET",
      headers: {
        Authorization: `Bearer ${accessToken}`,
        "x-goog-user-project": projectId,
        "Content-Type": "application/json",
        "X-Goog-Api-Client": GENKIT_CLIENT_HEADER
      }
    }
  );
  if (!response.ok) {
    const ee = await response.text();
    throw new Error(
      `Error from Vertex AI predict: HTTP ${response.status}: ${ee}`
    );
  }
  const modelResponse = await response.json();
  for (const m of modelResponse.publisherModels) {
    console.log(m.name);
  }
  return modelResponse.publisherModels;
}
export {
  listModels
};
//# sourceMappingURL=list-models.mjs.map