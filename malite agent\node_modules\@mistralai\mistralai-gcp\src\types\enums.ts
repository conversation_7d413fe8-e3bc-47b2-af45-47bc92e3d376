/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

declare const __brand: unique symbol;
export type Unrecognized<T> = T & { [__brand]: "unrecognized" };

export function catchUnrecognizedEnum<T>(value: T): Unrecognized<T> {
  return value as Unrecognized<T>;
}

type Prettify<T> = { [K in keyof T]: T[K] } & {};
export type ClosedEnum<T> = T[keyof T];
export type OpenEnum<T> =
  | Prettify<T[keyof T]>
  | Unrecognized<T[keyof T] extends number ? number : string>;
