"use strict";
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CompletionResponseStreamChoice$ = exports.CompletionResponseStreamChoice$outboundSchema = exports.CompletionResponseStreamChoice$inboundSchema = exports.FinishReason$ = exports.FinishReason$outboundSchema = exports.FinishReason$inboundSchema = exports.FinishReason = void 0;
exports.completionResponseStreamChoiceToJSON = completionResponseStreamChoiceToJSON;
exports.completionResponseStreamChoiceFromJSON = completionResponseStreamChoiceFromJSON;
const z = __importStar(require("zod"));
const primitives_js_1 = require("../../lib/primitives.js");
const schemas_js_1 = require("../../lib/schemas.js");
const enums_js_1 = require("../../types/enums.js");
const deltamessage_js_1 = require("./deltamessage.js");
exports.FinishReason = {
    Stop: "stop",
    Length: "length",
    Error: "error",
    ToolCalls: "tool_calls",
};
/** @internal */
exports.FinishReason$inboundSchema = z
    .union([
    z.nativeEnum(exports.FinishReason),
    z.string().transform(enums_js_1.catchUnrecognizedEnum),
]);
/** @internal */
exports.FinishReason$outboundSchema = z.union([
    z.nativeEnum(exports.FinishReason),
    z.string().and(z.custom()),
]);
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var FinishReason$;
(function (FinishReason$) {
    /** @deprecated use `FinishReason$inboundSchema` instead. */
    FinishReason$.inboundSchema = exports.FinishReason$inboundSchema;
    /** @deprecated use `FinishReason$outboundSchema` instead. */
    FinishReason$.outboundSchema = exports.FinishReason$outboundSchema;
})(FinishReason$ || (exports.FinishReason$ = FinishReason$ = {}));
/** @internal */
exports.CompletionResponseStreamChoice$inboundSchema = z.object({
    index: z.number().int(),
    delta: deltamessage_js_1.DeltaMessage$inboundSchema,
    finish_reason: z.nullable(exports.FinishReason$inboundSchema),
}).transform((v) => {
    return (0, primitives_js_1.remap)(v, {
        "finish_reason": "finishReason",
    });
});
/** @internal */
exports.CompletionResponseStreamChoice$outboundSchema = z.object({
    index: z.number().int(),
    delta: deltamessage_js_1.DeltaMessage$outboundSchema,
    finishReason: z.nullable(exports.FinishReason$outboundSchema),
}).transform((v) => {
    return (0, primitives_js_1.remap)(v, {
        finishReason: "finish_reason",
    });
});
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var CompletionResponseStreamChoice$;
(function (CompletionResponseStreamChoice$) {
    /** @deprecated use `CompletionResponseStreamChoice$inboundSchema` instead. */
    CompletionResponseStreamChoice$.inboundSchema = exports.CompletionResponseStreamChoice$inboundSchema;
    /** @deprecated use `CompletionResponseStreamChoice$outboundSchema` instead. */
    CompletionResponseStreamChoice$.outboundSchema = exports.CompletionResponseStreamChoice$outboundSchema;
})(CompletionResponseStreamChoice$ || (exports.CompletionResponseStreamChoice$ = CompletionResponseStreamChoice$ = {}));
function completionResponseStreamChoiceToJSON(completionResponseStreamChoice) {
    return JSON.stringify(exports.CompletionResponseStreamChoice$outboundSchema.parse(completionResponseStreamChoice));
}
function completionResponseStreamChoiceFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.CompletionResponseStreamChoice$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'CompletionResponseStreamChoice' from JSON`);
}
//# sourceMappingURL=completionresponsestreamchoice.js.map