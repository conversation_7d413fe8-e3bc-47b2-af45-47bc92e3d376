{"version": 3, "file": "hex-to-binary.js", "sourceRoot": "", "sources": ["../../../src/common/hex-to-binary.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,SAAS,QAAQ,CAAC,QAAgB;IAChC,MAAM;IACN,IAAI,QAAQ,IAAI,EAAE,IAAI,QAAQ,IAAI,EAAE,EAAE;QACpC,OAAO,QAAQ,GAAG,EAAE,CAAC;KACtB;IAED,MAAM;IACN,IAAI,QAAQ,IAAI,EAAE,IAAI,QAAQ,IAAI,GAAG,EAAE;QACrC,OAAO,QAAQ,GAAG,EAAE,CAAC;KACtB;IAED,MAAM;IACN,OAAO,QAAQ,GAAG,EAAE,CAAC;AACvB,CAAC;AAED,SAAgB,WAAW,CAAC,MAAc;IACxC,MAAM,GAAG,GAAG,IAAI,UAAU,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;IAC9C,IAAI,MAAM,GAAG,CAAC,CAAC;IAEf,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE;QACzC,MAAM,EAAE,GAAG,QAAQ,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1C,MAAM,EAAE,GAAG,QAAQ,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAC9C,GAAG,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC;KAChC;IAED,OAAO,GAAG,CAAC;AACb,CAAC;AAXD,kCAWC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nfunction intValue(charCode: number): number {\n  // 0-9\n  if (charCode >= 48 && charCode <= 57) {\n    return charCode - 48;\n  }\n\n  // a-f\n  if (charCode >= 97 && charCode <= 102) {\n    return charCode - 87;\n  }\n\n  // A-F\n  return charCode - 55;\n}\n\nexport function hexToBinary(hexStr: string): Uint8Array {\n  const buf = new Uint8Array(hexStr.length / 2);\n  let offset = 0;\n\n  for (let i = 0; i < hexStr.length; i += 2) {\n    const hi = intValue(hexStr.charCodeAt(i));\n    const lo = intValue(hexStr.charCodeAt(i + 1));\n    buf[offset++] = (hi << 4) | lo;\n  }\n\n  return buf;\n}\n"]}