{"version": 3, "file": "getMachineId-win.js", "sourceRoot": "", "sources": ["../../../../../../src/detectors/platform/node/machine-id/getMachineId-win.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEH,OAAO,KAAK,OAAO,MAAM,SAAS,CAAC;AACnC,OAAO,EAAE,SAAS,EAAE,MAAM,aAAa,CAAC;AACxC,OAAO,EAAE,IAAI,EAAE,MAAM,oBAAoB,CAAC;AAE1C,MAAM,UAAgB,YAAY;;;;;;oBAC1B,IAAI,GACR,4EAA4E,CAAC;oBAC3E,OAAO,GAAG,6BAA6B,CAAC;oBAC5C,IAAI,OAAO,CAAC,IAAI,KAAK,MAAM,IAAI,wBAAwB,IAAI,OAAO,CAAC,GAAG,EAAE;wBACtE,OAAO,GAAG,kCAAkC,GAAG,OAAO,CAAC;qBACxD;;;;oBAGgB,qBAAM,SAAS,CAAI,OAAO,SAAI,IAAM,CAAC,EAAA;;oBAA9C,MAAM,GAAG,SAAqC;oBAC9C,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;oBAC5C,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;wBACtB,sBAAO,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,EAAC;qBACxB;;;;oBAED,IAAI,CAAC,KAAK,CAAC,+BAA6B,GAAG,CAAC,CAAC;;wBAG/C,sBAAO,EAAE,EAAC;;;;CACX", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport * as process from 'process';\nimport { execAsync } from './execAsync';\nimport { diag } from '@opentelemetry/api';\n\nexport async function getMachineId(): Promise<string> {\n  const args =\n    'QUERY HKEY_LOCAL_MACHINE\\\\SOFTWARE\\\\Microsoft\\\\Cryptography /v MachineGuid';\n  let command = '%windir%\\\\System32\\\\REG.exe';\n  if (process.arch === 'ia32' && 'PROCESSOR_ARCHITEW6432' in process.env) {\n    command = '%windir%\\\\sysnative\\\\cmd.exe /c ' + command;\n  }\n\n  try {\n    const result = await execAsync(`${command} ${args}`);\n    const parts = result.stdout.split('REG_SZ');\n    if (parts.length === 2) {\n      return parts[1].trim();\n    }\n  } catch (e) {\n    diag.debug(`error reading machine id: ${e}`);\n  }\n\n  return '';\n}\n"]}