{"version": 3, "file": "BatchLogRecordProcessor.js", "sourceRoot": "", "sources": ["../../../../../src/platform/node/export/BatchLogRecordProcessor.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;;;;;;;;;;;;;AAGH,OAAO,EAAE,2BAA2B,EAAE,MAAM,6CAA6C,CAAC;AAE1F;IAA6C,2CAAyC;IAAtF;;IAEA,CAAC;IADW,4CAAU,GAApB,cAA8B,CAAC;IACjC,8BAAC;AAAD,CAAC,AAFD,CAA6C,2BAA2B,GAEvE", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport type { BufferConfig } from '../../../types';\nimport { BatchLogRecordProcessorBase } from '../../../export/BatchLogRecordProcessorBase';\n\nexport class BatchLogRecordProcessor extends BatchLogRecordProcessorBase<BufferConfig> {\n  protected onShutdown(): void {}\n}\n"]}