// import the Genkit, Google AI, and Vertex AI plugin libraries
import { googleAI } from '@genkit-ai/googleai';
import { vertexAI } from '@genkit-ai/vertexai'; // Import Vertex AI plugin
import { genkit } from 'genkit';
import dotenv from 'dotenv';

// Load environment variables from .env file
dotenv.config();

// Set GENKIT_ENV_DEV environment variable to enable the Developer UI
process.env.GENKIT_ENV_DEV = 'true';

// configure a Genkit instance
const ai = genkit({
  plugins: [
    googleAI(),
    vertexAI() // Add Vertex AI plugin
  ],
  model: vertexAI.model('gemini-1.0-pro'), // Set default model to a Vertex AI model (example)
});

async function main() {
  try {
    // make a generation request in Arabic
    const { text } = await ai.generate('مرحباً، قدم نفسك باللغة العربية وأخبرني كيف يمكنني البدء باستخدام Genkit.');
    console.log(text);
  } catch (error) {
    console.error('Error:', error.message);
  }
}

main();