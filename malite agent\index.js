/**
 * مشروع Malite Agent - تطبيق Genkit متقدم للذكاء الاصطناعي
 * يدعم نماذج Google AI و Vertex AI مع التركيز على اللغة العربية
 */

// استيراد المكتبات المطلوبة
import { googleAI } from '@genkit-ai/googleai';
import { vertexAI } from '@genkit-ai/vertexai';
import { genkit } from 'genkit';
import dotenv from 'dotenv';

// تحميل متغيرات البيئة من ملف .env
dotenv.config();

// تفعيل واجهة المطور Genkit
process.env.GENKIT_ENV_DEV = 'true';

// تكوين مثيل Genkit مع الإضافات المطلوبة
const ai = genkit({
  plugins: [
    googleAI(),
    vertexAI({ location: 'us-central1' })
  ],
  model: vertexAI.model('gemini-1.0-pro'),
});

/**
 * عرض معلومات المشروع
 */
function displayProjectInfo() {
  console.log('\n🚀 ===== مرحباً بك في مشروع Malite Agent =====\n');
  console.log('🤖 تطبيق Genkit متقدم للذكاء الاصطناعي');
  console.log('🌍 مع دعم كامل للغة العربية');
  console.log('⚡ يستخدم نماذج Google AI و Vertex AI');
  console.log('\n📊 واجهة المطور متاحة على: http://localhost:4000');
  console.log('📚 للمزيد من الأمثلة: npm run info\n');
}

/**
 * اختبار الاتصال مع نماذج الذكاء الاصطناعي
 */
async function testConnection() {
  console.log('🔍 اختبار الاتصال مع نماذج الذكاء الاصطناعي...\n');

  try {
    // اختبار نموذج Gemini Pro
    const { text } = await ai.generate({
      model: vertexAI.model('gemini-1.0-pro'),
      prompt: 'مرحباً، قدم نفسك باللغة العربية في جملتين فقط.',
      temperature: 0.7,
      maxOutputTokens: 100
    });

    console.log('✅ نجح الاتصال مع Gemini Pro');
    console.log('🤖 استجابة النموذج:');
    console.log(`"${text}"\n`);

    return true;
  } catch (error) {
    console.error('❌ فشل في الاتصال مع النموذج:', error.message);
    console.log('\n💡 تأكد من:');
    console.log('   • وجود مفتاح GEMINI_API_KEY في ملف .env');
    console.log('   • صحة مفتاح API');
    console.log('   • الاتصال بالإنترنت\n');
    return false;
  }
}

/**
 * عرض أمثلة سريعة للاستخدام
 */
async function showQuickExamples() {
  console.log('📝 ===== أمثلة سريعة للاستخدام =====\n');

  const examples = [
    {
      title: 'تلخيص النص',
      prompt: 'لخص هذا النص في جملة واحدة: الذكاء الاصطناعي هو تقنية متطورة تحاكي الذكاء البشري وتستخدم في مجالات متعددة مثل الطب والتعليم والأعمال.'
    },
    {
      title: 'الترجمة',
      prompt: 'ترجم إلى الإنجليزية: مرحباً، كيف حالك اليوم؟'
    },
    {
      title: 'الإبداع',
      prompt: 'اكتب بيت شعر عن الذكاء الاصطناعي'
    }
  ];

  for (const example of examples) {
    try {
      console.log(`🔹 ${example.title}:`);
      const { text } = await ai.generate({
        model: vertexAI.model('gemini-1.0-pro'),
        prompt: example.prompt,
        temperature: 0.7,
        maxOutputTokens: 150
      });
      console.log(`   ${text}\n`);
    } catch (error) {
      console.log(`   ❌ خطأ: ${error.message}\n`);
    }
  }
}

/**
 * عرض الأوامر المتاحة
 */
function showAvailableCommands() {
  console.log('🛠️ ===== الأوامر المتاحة =====\n');
  console.log('npm start                    # تشغيل التطبيق الرئيسي');
  console.log('npm run dev                  # تشغيل مع واجهة المطور');
  console.log('npm run vertex:arabic        # أمثلة عربية متقدمة');
  console.log('npm run vertex:practical     # تطبيقات عملية');
  console.log('npm run info                 # معلومات المشروع');
  console.log('npm run setup                # فحص الإعدادات\n');
}

/**
 * الدالة الرئيسية
 */
async function main() {
  try {
    displayProjectInfo();

    const connectionSuccess = await testConnection();

    if (connectionSuccess) {
      await showQuickExamples();
    }

    showAvailableCommands();

    console.log('✨ ===== انتهى التشغيل بنجاح =====\n');
    console.log('💡 لمزيد من الأمثلة المتقدمة، جرب:');
    console.log('   npm run vertex:arabic\n');

  } catch (error) {
    console.error('❌ خطأ عام في التطبيق:', error.message);
    console.log('\n🔧 للمساعدة في حل المشكلة:');
    console.log('   npm run setup\n');
  }
}

// تشغيل التطبيق
main();