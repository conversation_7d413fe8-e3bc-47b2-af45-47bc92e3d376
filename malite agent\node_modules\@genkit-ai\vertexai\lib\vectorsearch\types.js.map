{"version": 3, "sources": ["../../src/vectorsearch/types.ts"], "sourcesContent": ["/**\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { EmbedderReference, z } from 'genkit';\nimport { CommonPluginOptions } from '../common/types.js';\nimport { VectorSearchOptions } from './vector_search/index.js';\n\n/** Options specific to vector search configuration */\nexport interface VectorSearchOptionsConfig {\n  /** Configure Vertex AI vector search index options */\n  vectorSearchOptions?: VectorSearchOptions<z.ZodTypeAny, any, any>[];\n  embedder?: EmbedderReference;\n}\n\nexport interface PluginOptions\n  extends CommonPluginOptions,\n    VectorSearchOptionsConfig {}\n"], "mappings": ";;;;;;;;;;;;;;AAAA;AAAA;", "names": []}