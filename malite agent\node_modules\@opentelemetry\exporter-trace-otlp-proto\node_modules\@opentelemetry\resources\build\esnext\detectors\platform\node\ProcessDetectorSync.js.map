{"version": 3, "file": "ProcessDetectorSync.js", "sourceRoot": "", "sources": ["../../../../../src/detectors/platform/node/ProcessDetectorSync.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAEH,OAAO,EAAE,IAAI,EAAE,MAAM,oBAAoB,CAAC;AAC1C,OAAO,EACL,2BAA2B,EAC3B,gCAAgC,EAChC,mCAAmC,EACnC,mCAAmC,EACnC,yBAAyB,EACzB,uBAAuB,EACvB,uCAAuC,EACvC,gCAAgC,EAChC,mCAAmC,GACpC,MAAM,qCAAqC,CAAC;AAC7C,OAAO,EAAE,QAAQ,EAAE,MAAM,mBAAmB,CAAC;AAI7C,OAAO,KAAK,EAAE,MAAM,IAAI,CAAC;AAEzB;;;GAGG;AACH,MAAM,mBAAmB;IACvB,MAAM,CAAC,OAAiC;QACtC,MAAM,UAAU,GAAuB;YACrC,CAAC,uBAAuB,CAAC,EAAE,OAAO,CAAC,GAAG;YACtC,CAAC,mCAAmC,CAAC,EAAE,OAAO,CAAC,KAAK;YACpD,CAAC,mCAAmC,CAAC,EAAE,OAAO,CAAC,QAAQ;YACvD,CAAC,gCAAgC,CAAC,EAAE;gBAClC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC;gBACf,GAAG,OAAO,CAAC,QAAQ;gBACnB,GAAG,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;aACzB;YACD,CAAC,mCAAmC,CAAC,EAAE,OAAO,CAAC,QAAQ,CAAC,IAAI;YAC5D,CAAC,gCAAgC,CAAC,EAAE,QAAQ;YAC5C,CAAC,uCAAuC,CAAC,EAAE,SAAS;SACrD,CAAC;QAEF,IAAI,OAAO,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE;YAC3B,UAAU,CAAC,2BAA2B,CAAC,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;SAC3D;QAED,IAAI;YACF,MAAM,QAAQ,GAAG,EAAE,CAAC,QAAQ,EAAE,CAAC;YAC/B,UAAU,CAAC,yBAAyB,CAAC,GAAG,QAAQ,CAAC,QAAQ,CAAC;SAC3D;QAAC,OAAO,CAAC,EAAE;YACV,IAAI,CAAC,KAAK,CAAC,kCAAkC,CAAC,EAAE,CAAC,CAAC;SACnD;QAED,OAAO,IAAI,QAAQ,CAAC,UAAU,CAAC,CAAC;IAClC,CAAC;CACF;AAED,MAAM,CAAC,MAAM,mBAAmB,GAAG,IAAI,mBAAmB,EAAE,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { diag } from '@opentelemetry/api';\nimport {\n  SEMRESATTRS_PROCESS_COMMAND,\n  SEMRESATTRS_PROCESS_COMMAND_ARGS,\n  SEMRESATTRS_PROCESS_EXECUTABLE_NAME,\n  SEMRESATTRS_PROCESS_EXECUTABLE_PATH,\n  SEMRESATTRS_PROCESS_OWNER,\n  SEMRESATTRS_PROCESS_PID,\n  SEMRESATTRS_PROCESS_RUNTIME_DESCRIPTION,\n  SEMRESATTRS_PROCESS_RUNTIME_NAME,\n  SEMRESATTRS_PROCESS_RUNTIME_VERSION,\n} from '@opentelemetry/semantic-conventions';\nimport { Resource } from '../../../Resource';\nimport { DetectorSync, ResourceAttributes } from '../../../types';\nimport { ResourceDetectionConfig } from '../../../config';\nimport { IResource } from '../../../IResource';\nimport * as os from 'os';\n\n/**\n * ProcessDetectorSync will be used to detect the resources related current process running\n * and being instrumented from the NodeJS Process module.\n */\nclass ProcessDetectorSync implements DetectorSync {\n  detect(_config?: ResourceDetectionConfig): IResource {\n    const attributes: ResourceAttributes = {\n      [SEMRESATTRS_PROCESS_PID]: process.pid,\n      [SEMRESATTRS_PROCESS_EXECUTABLE_NAME]: process.title,\n      [SEMRESATTRS_PROCESS_EXECUTABLE_PATH]: process.execPath,\n      [SEMRESATTRS_PROCESS_COMMAND_ARGS]: [\n        process.argv[0],\n        ...process.execArgv,\n        ...process.argv.slice(1),\n      ],\n      [SEMRESATTRS_PROCESS_RUNTIME_VERSION]: process.versions.node,\n      [SEMRESATTRS_PROCESS_RUNTIME_NAME]: 'nodejs',\n      [SEMRESATTRS_PROCESS_RUNTIME_DESCRIPTION]: 'Node.js',\n    };\n\n    if (process.argv.length > 1) {\n      attributes[SEMRESATTRS_PROCESS_COMMAND] = process.argv[1];\n    }\n\n    try {\n      const userInfo = os.userInfo();\n      attributes[SEMRESATTRS_PROCESS_OWNER] = userInfo.username;\n    } catch (e) {\n      diag.debug(`error obtaining process owner: ${e}`);\n    }\n\n    return new Resource(attributes);\n  }\n}\n\nexport const processDetectorSync = new ProcessDetectorSync();\n"]}