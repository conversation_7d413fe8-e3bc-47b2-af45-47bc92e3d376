import { genkitPlugin } from "genkit/plugin";
import { getDerivedParams } from "../common/index.js";
import { vertexAiRerankers } from "./reranker.js";
function vertexAIRerankers(options) {
  return genkitPlugin("vertexAIRerankers", async (ai) => {
    const { projectId, location, authClient } = await getDerivedParams(options);
    await vertexAiRerankers(ai, {
      projectId,
      location,
      authClient,
      rerankOptions: options.rerankers.map(
        (o) => typeof o === "string" ? { model: o } : o
      )
    });
  });
}
export {
  vertexAIRerankers
};
//# sourceMappingURL=index.mjs.map