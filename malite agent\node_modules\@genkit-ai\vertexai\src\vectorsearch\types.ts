/**
 * Copyright 2024 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import { EmbedderReference, z } from 'genkit';
import { CommonPluginOptions } from '../common/types.js';
import { VectorSearchOptions } from './vector_search/index.js';

/** Options specific to vector search configuration */
export interface VectorSearchOptionsConfig {
  /** Configure Vertex AI vector search index options */
  vectorSearchOptions?: VectorSearchOptions<z.ZodTypeAny, any, any>[];
  embedder?: EmbedderReference;
}

export interface PluginOptions
  extends CommonPluginOptions,
    VectorSearchOptionsConfig {}
