"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
var upsert_datapoints_exports = {};
__export(upsert_datapoints_exports, {
  upsertDatapoints: () => upsertDatapoints
});
module.exports = __toCommonJS(upsert_datapoints_exports);
async function upsertDatapoints(params) {
  const { datapoints, authClient, projectId, location, indexId } = params;
  const accessToken = await authClient.getAccessToken();
  const url = `https://${location}-aiplatform.googleapis.com/v1/projects/${projectId}/locations/${location}/indexes/${indexId}:upsertDatapoints`;
  const requestBody = {
    datapoints: datapoints.map((dp) => {
      const newDp = {
        datapoint_id: dp.datapointId,
        feature_vector: dp.featureVector
      };
      if (dp.restricts) {
        newDp.restricts = dp.restricts?.map((r) => ({
          namespace: r.namespace,
          allow_list: r.allowList,
          deny_list: r.denyList
        })) || [];
      }
      if (dp.numericRestricts) {
        newDp.numeric_restricts = dp.numericRestricts?.map((nr) => {
          const newNR = {
            namespace: nr.namespace
          };
          if (nr.valueInt) {
            newNR.value_int = nr.valueInt;
          }
          if (nr.valueFloat) {
            newNR.value_float = nr.valueFloat;
          }
          if (nr.valueDouble) {
            newNR.value_double = nr.valueDouble;
          }
          return newNR;
        }) || [];
      }
      if (dp.crowdingTag) {
        newDp.crowding_tag = dp.crowdingTag;
      }
      return newDp;
    })
  };
  const response = await fetch(url, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${accessToken}`
    },
    body: JSON.stringify(requestBody)
  });
  if (!response.ok) {
    const errMsg = (await response.json()).error?.message || "";
    throw new Error(
      `Error upserting datapoints into index ${indexId}: ${response.statusText}. ${errMsg}`
    );
  }
}
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  upsertDatapoints
});
//# sourceMappingURL=upsert_datapoints.js.map