{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/index.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAgBH,0EAAyE;AAAhE,gIAAA,sBAAsB,OAAA;AAE/B,kDAY6B;AAV3B,2GAAA,aAAa,OAAA;AAcf,sDAA0E;AAAjE,4GAAA,YAAY,OAAA;AAErB,wFAGgD;AAF9C,8IAAA,6BAA6B,OAAA;AAI/B,0EAAyE;AAAhE,gIAAA,sBAAsB,OAAA;AAE/B,wEAAuE;AAA9D,8HAAA,qBAAqB,OAAA;AAI9B,+DAAwD;AAA/C,sHAAA,cAAc,OAAA;AAMvB,iDAAsE;AAA7D,8GAAA,aAAa,OAAA;AAEtB,kDAS4B;AAR1B,iHAAA,kBAAkB,OAAA;AAClB,iIAAA,kCAAkC,OAAA;AAClC,8HAAA,+BAA+B,OAAA;AAC/B,8GAAA,eAAe,OAAA;AACf,mHAAA,oBAAoB,OAAA;AACpB,mHAAA,oBAAoB,OAAA;AACpB,6GAAA,cAAc,OAAA;AACd,0GAAA,WAAW,OAAA;AAGb,oCAAgD;AAAvC,4FAAA,IAAI,OAAA;AAEb,iCAAuC;AAA9B,qGAAA,YAAY,OAAA", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { MetricDescriptor } from './export/MetricData';\n\nexport {\n  Sum,\n  LastValue,\n  Histogram,\n  ExponentialHistogram,\n} from './aggregator/types';\n\nexport {\n  AggregationSelector,\n  AggregationTemporalitySelector,\n} from './export/AggregationSelector';\n\nexport { AggregationTemporality } from './export/AggregationTemporality';\n\nexport {\n  DataPoint,\n  DataPointType,\n  SumMetricData,\n  GaugeMetricData,\n  HistogramMetricData,\n  ExponentialHistogramMetricData,\n  ResourceMetrics,\n  ScopeMetrics,\n  MetricData,\n  MetricDescriptor,\n  CollectionResult,\n} from './export/MetricData';\n\nexport { PushMetricExporter } from './export/MetricExporter';\n\nexport { MetricReader, MetricReaderOptions } from './export/MetricReader';\n\nexport {\n  PeriodicExportingMetricReader,\n  PeriodicExportingMetricReaderOptions,\n} from './export/PeriodicExportingMetricReader';\n\nexport { InMemoryMetricExporter } from './export/InMemoryMetricExporter';\n\nexport { ConsoleMetricExporter } from './export/ConsoleMetricExporter';\n\nexport { MetricCollectOptions, MetricProducer } from './export/MetricProducer';\n\nexport { InstrumentType } from './InstrumentDescriptor';\n/**\n * @deprecated Use {@link MetricDescriptor} instead.\n */\nexport type InstrumentDescriptor = MetricDescriptor;\n\nexport { MeterProvider, MeterProviderOptions } from './MeterProvider';\n\nexport {\n  DefaultAggregation,\n  ExplicitBucketHistogramAggregation,\n  ExponentialHistogramAggregation,\n  DropAggregation,\n  HistogramAggregation,\n  LastValueAggregation,\n  SumAggregation,\n  Aggregation,\n} from './view/Aggregation';\n\nexport { View, ViewOptions } from './view/View';\n\nexport { TimeoutError } from './utils';\n"]}