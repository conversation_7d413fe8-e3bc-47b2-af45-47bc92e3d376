{"name": "@anthropic-ai/vertex-sdk", "version": "0.4.3", "description": "The official TypeScript library for the Anthropic Vertex API", "author": "Anthropic <<EMAIL>>", "types": "./index.d.ts", "main": "./index.js", "type": "commonjs", "repository": "github:anthropics/anthropic-sdk-typescript", "license": "MIT", "packageManager": "yarn@1.22.21", "private": false, "scripts": {"test": "echo 'no tests defined yet' && exit 1", "build": "bash ./build", "format": "prettier --write --cache --cache-strategy metadata . !dist", "tsn": "ts-node -r tsconfig-paths/register", "lint": "eslint --ext ts,js .", "fix": "eslint --fix --ext ts,js ."}, "dependencies": {"@anthropic-ai/sdk": ">=0.14 <1", "google-auth-library": "^9.4.2"}, "imports": {"@anthropic-ai/vertex-sdk": ".", "@anthropic-ai/vertex-sdk/*": "./src/*"}, "exports": {".": {"require": {"types": "./index.d.ts", "default": "./index.js"}, "types": "./index.d.mts", "default": "./index.mjs"}, "./*.mjs": {"types": "./*.d.ts", "default": "./*.mjs"}, "./*.js": {"types": "./*.d.ts", "default": "./*.js"}, "./*": {"types": "./*.d.ts", "require": "./*.js", "default": "./*.mjs"}}}