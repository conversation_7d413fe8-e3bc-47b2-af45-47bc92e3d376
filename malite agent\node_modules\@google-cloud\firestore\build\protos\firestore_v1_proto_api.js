(e=>{"function"==typeof define&&define.amd?define(["protobufjs/minimal"],e):"function"==typeof require&&"object"==typeof module&&module&&module.exports&&(module.exports=e(require("protobufjs/minimal")))})(function(r){var e,t,o,n,C,i=r.util,a=r.roots.firestore_v1||(r.roots.firestore_v1={});function V(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function L(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function U(e){if(this.queries=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function B(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function J(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function Q(e){if(this.aggregateFields={},e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function M(e){if(this.fields={},e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function s(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function G(e){if(this.values=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function q(e){if(this.fields={},e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function Y(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function W(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function z(e){if(this.fieldPaths=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function H(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function K(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function X(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function Z(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function l(e,t,o){r.rpc.Service.call(this,e,t,o)}function $(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function u(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function ee(e){if(this.documents=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function te(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function oe(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function re(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function p(e){if(this.documents=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function ne(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function ie(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function ae(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function se(e){if(this.writes=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function le(e){if(this.writeResults=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function ue(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function c(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function g(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function f(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function pe(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function d(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function ce(e){if(this.partitions=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function ge(e){if(this.writes=[],this.labels={},e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function fe(e){if(this.writeResults=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function de(e){if(this.labels={},e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function y(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function m(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function ye(e){if(this.documents=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function me(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function be(e){if(this.targetIds=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function Oe(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function he(e){if(this.collectionIds=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function ve(e){if(this.writes=[],this.labels={},e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function Te(e){if(this.writeResults=[],this.status=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function b(e){if(this.from=[],this.orderBy=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function Se(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function je(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function Ee(e){if(this.filters=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function we(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function Ne(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function Pe(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function Re(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function De(e){if(this.fields=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function O(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function Ie(e){if(this.aggregations=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function h(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function Ae(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function ke(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function xe(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function Fe(e){if(this.values=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function _e(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function Ce(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function Ve(e){if(this.indexesUsed=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function Le(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function v(e){if(this.updateTransforms=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function Ue(e){if(this.fieldTransforms=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function T(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function Be(e){if(this.transformResults=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function Je(e){if(this.targetIds=[],this.removedTargetIds=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function Qe(e){if(this.removedTargetIds=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function Me(e){if(this.removedTargetIds=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function Ge(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function qe(e){if(this.rules=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function S(e){if(this.additionalBindings=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function Ye(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function We(e){if(this.destinations=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function j(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function E(e){if(this.methodSettings=[],this.codeownerGithubTeams=[],this.librarySettings=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function ze(e){if(this.serviceClassNames={},e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function He(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function Ke(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function Xe(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function Ze(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function $e(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function et(e){if(this.renamedServices={},this.renamedResources={},this.ignoredResources=[],this.forcedNamespaceAliases=[],this.handwrittenSignatures=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function tt(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function ot(e){if(this.renamedServices={},e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function rt(e){if(this.autoPopulatedFields=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function nt(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function it(e){if(this.methods=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function w(e){if(this.pattern=[],this.style=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function at(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function st(e){if(this.file=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function N(e){if(this.dependency=[],this.publicDependency=[],this.weakDependency=[],this.messageType=[],this.enumType=[],this.service=[],this.extension=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function P(e){if(this.field=[],this.extension=[],this.nestedType=[],this.enumType=[],this.extensionRange=[],this.oneofDecl=[],this.reservedRange=[],this.reservedName=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function lt(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function ut(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function pt(e){if(this.uninterpretedOption=[],this.declaration=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function ct(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function R(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function gt(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function ft(e){if(this.value=[],this.reservedRange=[],this.reservedName=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function dt(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function yt(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function mt(e){if(this.method=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function bt(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function D(e){if(this.uninterpretedOption=[],this[".google.api.resourceDefinition"]=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function I(e){if(this.uninterpretedOption=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function A(e){if(this.targets=[],this.editionDefaults=[],this.uninterpretedOption=[],this[".google.api.fieldBehavior"]=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function Ot(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function ht(e){if(this.uninterpretedOption=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function vt(e){if(this.uninterpretedOption=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function Tt(e){if(this.uninterpretedOption=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function St(e){if(this.uninterpretedOption=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function k(e){if(this.uninterpretedOption=[],this[".google.api.methodSignature"]=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function x(e){if(this.name=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function jt(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function F(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function Et(e){if(this.defaults=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function wt(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function Nt(e){if(this.location=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function Pt(e){if(this.path=[],this.span=[],this.leadingDetachedComments=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function Rt(e){if(this.annotation=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function Dt(e){if(this.path=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function It(e){if(this.fields={},e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function _(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function At(e){if(this.values=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function kt(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function xt(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function Ft(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function _t(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function Ct(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function Vt(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function Lt(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function Ut(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function Bt(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function Jt(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function Qt(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function Mt(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function Gt(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function qt(e){if(this.paths=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function Yt(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function Wt(e){if(this.details=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function zt(e,t,o){r.rpc.Service.call(this,e,t,o)}function Ht(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function Kt(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function Xt(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function Zt(e){if(this.operations=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function $t(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function eo(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function to(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function oo(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}return a.firestore=((o={}).BundledQuery=(V.prototype.parent="",V.prototype.structuredQuery=null,V.prototype.limitType=0,Object.defineProperty(V.prototype,"queryType",{get:i.oneOfGetter(t=["structuredQuery"]),set:i.oneOfSetter(t)}),V.fromObject=function(e){if(e instanceof a.firestore.BundledQuery)return e;var t=new a.firestore.BundledQuery;if(null!=e.parent&&(t.parent=String(e.parent)),null!=e.structuredQuery){if("object"!=typeof e.structuredQuery)throw TypeError(".firestore.BundledQuery.structuredQuery: object expected");t.structuredQuery=a.google.firestore.v1.StructuredQuery.fromObject(e.structuredQuery)}switch(e.limitType){default:"number"==typeof e.limitType&&(t.limitType=e.limitType);break;case"FIRST":case 0:t.limitType=0;break;case"LAST":case 1:t.limitType=1}return t},V.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.parent="",o.limitType=t.enums===String?"FIRST":0),null!=e.parent&&e.hasOwnProperty("parent")&&(o.parent=e.parent),null!=e.structuredQuery&&e.hasOwnProperty("structuredQuery")&&(o.structuredQuery=a.google.firestore.v1.StructuredQuery.toObject(e.structuredQuery,t),t.oneofs)&&(o.queryType="structuredQuery"),null!=e.limitType&&e.hasOwnProperty("limitType")&&(o.limitType=t.enums!==String||void 0===a.firestore.BundledQuery.LimitType[e.limitType]?e.limitType:a.firestore.BundledQuery.LimitType[e.limitType]),o},V.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},V.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/firestore.BundledQuery"},V.LimitType=(t={},(C=Object.create(t))[t[0]="FIRST"]="FIRST",C[t[1]="LAST"]="LAST",C),V),o.NamedQuery=(L.prototype.name="",L.prototype.bundledQuery=null,L.prototype.readTime=null,L.fromObject=function(e){if(e instanceof a.firestore.NamedQuery)return e;var t=new a.firestore.NamedQuery;if(null!=e.name&&(t.name=String(e.name)),null!=e.bundledQuery){if("object"!=typeof e.bundledQuery)throw TypeError(".firestore.NamedQuery.bundledQuery: object expected");t.bundledQuery=a.firestore.BundledQuery.fromObject(e.bundledQuery)}if(null!=e.readTime){if("object"!=typeof e.readTime)throw TypeError(".firestore.NamedQuery.readTime: object expected");t.readTime=a.google.protobuf.Timestamp.fromObject(e.readTime)}return t},L.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.name="",o.bundledQuery=null,o.readTime=null),null!=e.name&&e.hasOwnProperty("name")&&(o.name=e.name),null!=e.bundledQuery&&e.hasOwnProperty("bundledQuery")&&(o.bundledQuery=a.firestore.BundledQuery.toObject(e.bundledQuery,t)),null!=e.readTime&&e.hasOwnProperty("readTime")&&(o.readTime=a.google.protobuf.Timestamp.toObject(e.readTime,t)),o},L.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},L.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/firestore.NamedQuery"},L),o.BundledDocumentMetadata=(U.prototype.name="",U.prototype.readTime=null,U.prototype.exists=!1,U.prototype.queries=i.emptyArray,U.fromObject=function(e){if(e instanceof a.firestore.BundledDocumentMetadata)return e;var t=new a.firestore.BundledDocumentMetadata;if(null!=e.name&&(t.name=String(e.name)),null!=e.readTime){if("object"!=typeof e.readTime)throw TypeError(".firestore.BundledDocumentMetadata.readTime: object expected");t.readTime=a.google.protobuf.Timestamp.fromObject(e.readTime)}if(null!=e.exists&&(t.exists=Boolean(e.exists)),e.queries){if(!Array.isArray(e.queries))throw TypeError(".firestore.BundledDocumentMetadata.queries: array expected");t.queries=[];for(var o=0;o<e.queries.length;++o)t.queries[o]=String(e.queries[o])}return t},U.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.queries=[]),t.defaults&&(o.name="",o.readTime=null,o.exists=!1),null!=e.name&&e.hasOwnProperty("name")&&(o.name=e.name),null!=e.readTime&&e.hasOwnProperty("readTime")&&(o.readTime=a.google.protobuf.Timestamp.toObject(e.readTime,t)),null!=e.exists&&e.hasOwnProperty("exists")&&(o.exists=e.exists),e.queries&&e.queries.length){o.queries=[];for(var r=0;r<e.queries.length;++r)o.queries[r]=e.queries[r]}return o},U.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},U.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/firestore.BundledDocumentMetadata"},U),o.BundleMetadata=(B.prototype.id="",B.prototype.createTime=null,B.prototype.version=0,B.prototype.totalDocuments=0,B.prototype.totalBytes=i.Long?i.Long.fromBits(0,0,!0):0,B.fromObject=function(e){if(e instanceof a.firestore.BundleMetadata)return e;var t=new a.firestore.BundleMetadata;if(null!=e.id&&(t.id=String(e.id)),null!=e.createTime){if("object"!=typeof e.createTime)throw TypeError(".firestore.BundleMetadata.createTime: object expected");t.createTime=a.google.protobuf.Timestamp.fromObject(e.createTime)}return null!=e.version&&(t.version=e.version>>>0),null!=e.totalDocuments&&(t.totalDocuments=e.totalDocuments>>>0),null!=e.totalBytes&&(i.Long?(t.totalBytes=i.Long.fromValue(e.totalBytes)).unsigned=!0:"string"==typeof e.totalBytes?t.totalBytes=parseInt(e.totalBytes,10):"number"==typeof e.totalBytes?t.totalBytes=e.totalBytes:"object"==typeof e.totalBytes&&(t.totalBytes=new i.LongBits(e.totalBytes.low>>>0,e.totalBytes.high>>>0).toNumber(!0))),t},B.toObject=function(e,t){var o,r={};return(t=t||{}).defaults&&(r.id="",r.createTime=null,r.version=0,r.totalDocuments=0,i.Long?(o=new i.Long(0,0,!0),r.totalBytes=t.longs===String?o.toString():t.longs===Number?o.toNumber():o):r.totalBytes=t.longs===String?"0":0),null!=e.id&&e.hasOwnProperty("id")&&(r.id=e.id),null!=e.createTime&&e.hasOwnProperty("createTime")&&(r.createTime=a.google.protobuf.Timestamp.toObject(e.createTime,t)),null!=e.version&&e.hasOwnProperty("version")&&(r.version=e.version),null!=e.totalDocuments&&e.hasOwnProperty("totalDocuments")&&(r.totalDocuments=e.totalDocuments),null!=e.totalBytes&&e.hasOwnProperty("totalBytes")&&("number"==typeof e.totalBytes?r.totalBytes=t.longs===String?String(e.totalBytes):e.totalBytes:r.totalBytes=t.longs===String?i.Long.prototype.toString.call(e.totalBytes):t.longs===Number?new i.LongBits(e.totalBytes.low>>>0,e.totalBytes.high>>>0).toNumber(!0):e.totalBytes),r},B.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},B.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/firestore.BundleMetadata"},B),o.BundleElement=(J.prototype.metadata=null,J.prototype.namedQuery=null,J.prototype.documentMetadata=null,J.prototype.document=null,Object.defineProperty(J.prototype,"elementType",{get:i.oneOfGetter(t=["metadata","namedQuery","documentMetadata","document"]),set:i.oneOfSetter(t)}),J.fromObject=function(e){if(e instanceof a.firestore.BundleElement)return e;var t=new a.firestore.BundleElement;if(null!=e.metadata){if("object"!=typeof e.metadata)throw TypeError(".firestore.BundleElement.metadata: object expected");t.metadata=a.firestore.BundleMetadata.fromObject(e.metadata)}if(null!=e.namedQuery){if("object"!=typeof e.namedQuery)throw TypeError(".firestore.BundleElement.namedQuery: object expected");t.namedQuery=a.firestore.NamedQuery.fromObject(e.namedQuery)}if(null!=e.documentMetadata){if("object"!=typeof e.documentMetadata)throw TypeError(".firestore.BundleElement.documentMetadata: object expected");t.documentMetadata=a.firestore.BundledDocumentMetadata.fromObject(e.documentMetadata)}if(null!=e.document){if("object"!=typeof e.document)throw TypeError(".firestore.BundleElement.document: object expected");t.document=a.google.firestore.v1.Document.fromObject(e.document)}return t},J.toObject=function(e,t){t=t||{};var o={};return null!=e.metadata&&e.hasOwnProperty("metadata")&&(o.metadata=a.firestore.BundleMetadata.toObject(e.metadata,t),t.oneofs)&&(o.elementType="metadata"),null!=e.namedQuery&&e.hasOwnProperty("namedQuery")&&(o.namedQuery=a.firestore.NamedQuery.toObject(e.namedQuery,t),t.oneofs)&&(o.elementType="namedQuery"),null!=e.documentMetadata&&e.hasOwnProperty("documentMetadata")&&(o.documentMetadata=a.firestore.BundledDocumentMetadata.toObject(e.documentMetadata,t),t.oneofs)&&(o.elementType="documentMetadata"),null!=e.document&&e.hasOwnProperty("document")&&(o.document=a.google.firestore.v1.Document.toObject(e.document,t),t.oneofs)&&(o.elementType="document"),o},J.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},J.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/firestore.BundleElement"},J),o),a.google=((C={}).firestore=((t={}).v1=((o={}).AggregationResult=(Q.prototype.aggregateFields=i.emptyObject,Q.fromObject=function(e){if(e instanceof a.google.firestore.v1.AggregationResult)return e;var t=new a.google.firestore.v1.AggregationResult;if(e.aggregateFields){if("object"!=typeof e.aggregateFields)throw TypeError(".google.firestore.v1.AggregationResult.aggregateFields: object expected");t.aggregateFields={};for(var o=Object.keys(e.aggregateFields),r=0;r<o.length;++r){if("object"!=typeof e.aggregateFields[o[r]])throw TypeError(".google.firestore.v1.AggregationResult.aggregateFields: object expected");t.aggregateFields[o[r]]=a.google.firestore.v1.Value.fromObject(e.aggregateFields[o[r]])}}return t},Q.toObject=function(e,t){var o,r={};if(((t=t||{}).objects||t.defaults)&&(r.aggregateFields={}),e.aggregateFields&&(o=Object.keys(e.aggregateFields)).length){r.aggregateFields={};for(var n=0;n<o.length;++n)r.aggregateFields[o[n]]=a.google.firestore.v1.Value.toObject(e.aggregateFields[o[n]],t)}return r},Q.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},Q.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.v1.AggregationResult"},Q),o.Document=(M.prototype.name="",M.prototype.fields=i.emptyObject,M.prototype.createTime=null,M.prototype.updateTime=null,M.fromObject=function(e){if(e instanceof a.google.firestore.v1.Document)return e;var t=new a.google.firestore.v1.Document;if(null!=e.name&&(t.name=String(e.name)),e.fields){if("object"!=typeof e.fields)throw TypeError(".google.firestore.v1.Document.fields: object expected");t.fields={};for(var o=Object.keys(e.fields),r=0;r<o.length;++r){if("object"!=typeof e.fields[o[r]])throw TypeError(".google.firestore.v1.Document.fields: object expected");t.fields[o[r]]=a.google.firestore.v1.Value.fromObject(e.fields[o[r]])}}if(null!=e.createTime){if("object"!=typeof e.createTime)throw TypeError(".google.firestore.v1.Document.createTime: object expected");t.createTime=a.google.protobuf.Timestamp.fromObject(e.createTime)}if(null!=e.updateTime){if("object"!=typeof e.updateTime)throw TypeError(".google.firestore.v1.Document.updateTime: object expected");t.updateTime=a.google.protobuf.Timestamp.fromObject(e.updateTime)}return t},M.toObject=function(e,t){var o,r={};if(((t=t||{}).objects||t.defaults)&&(r.fields={}),t.defaults&&(r.name="",r.createTime=null,r.updateTime=null),null!=e.name&&e.hasOwnProperty("name")&&(r.name=e.name),e.fields&&(o=Object.keys(e.fields)).length){r.fields={};for(var n=0;n<o.length;++n)r.fields[o[n]]=a.google.firestore.v1.Value.toObject(e.fields[o[n]],t)}return null!=e.createTime&&e.hasOwnProperty("createTime")&&(r.createTime=a.google.protobuf.Timestamp.toObject(e.createTime,t)),null!=e.updateTime&&e.hasOwnProperty("updateTime")&&(r.updateTime=a.google.protobuf.Timestamp.toObject(e.updateTime,t)),r},M.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},M.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.v1.Document"},M),o.Value=(s.prototype.nullValue=null,s.prototype.booleanValue=null,s.prototype.integerValue=null,s.prototype.doubleValue=null,s.prototype.timestampValue=null,s.prototype.stringValue=null,s.prototype.bytesValue=null,s.prototype.referenceValue=null,s.prototype.geoPointValue=null,s.prototype.arrayValue=null,s.prototype.mapValue=null,Object.defineProperty(s.prototype,"valueType",{get:i.oneOfGetter(n=["nullValue","booleanValue","integerValue","doubleValue","timestampValue","stringValue","bytesValue","referenceValue","geoPointValue","arrayValue","mapValue"]),set:i.oneOfSetter(n)}),s.fromObject=function(e){if(e instanceof a.google.firestore.v1.Value)return e;var t=new a.google.firestore.v1.Value;switch(e.nullValue){default:"number"==typeof e.nullValue&&(t.nullValue=e.nullValue);break;case"NULL_VALUE":case 0:t.nullValue=0}if(null!=e.booleanValue&&(t.booleanValue=Boolean(e.booleanValue)),null!=e.integerValue&&(i.Long?(t.integerValue=i.Long.fromValue(e.integerValue)).unsigned=!1:"string"==typeof e.integerValue?t.integerValue=parseInt(e.integerValue,10):"number"==typeof e.integerValue?t.integerValue=e.integerValue:"object"==typeof e.integerValue&&(t.integerValue=new i.LongBits(e.integerValue.low>>>0,e.integerValue.high>>>0).toNumber())),null!=e.doubleValue&&(t.doubleValue=Number(e.doubleValue)),null!=e.timestampValue){if("object"!=typeof e.timestampValue)throw TypeError(".google.firestore.v1.Value.timestampValue: object expected");t.timestampValue=a.google.protobuf.Timestamp.fromObject(e.timestampValue)}if(null!=e.stringValue&&(t.stringValue=String(e.stringValue)),null!=e.bytesValue&&("string"==typeof e.bytesValue?i.base64.decode(e.bytesValue,t.bytesValue=i.newBuffer(i.base64.length(e.bytesValue)),0):0<=e.bytesValue.length&&(t.bytesValue=e.bytesValue)),null!=e.referenceValue&&(t.referenceValue=String(e.referenceValue)),null!=e.geoPointValue){if("object"!=typeof e.geoPointValue)throw TypeError(".google.firestore.v1.Value.geoPointValue: object expected");t.geoPointValue=a.google.type.LatLng.fromObject(e.geoPointValue)}if(null!=e.arrayValue){if("object"!=typeof e.arrayValue)throw TypeError(".google.firestore.v1.Value.arrayValue: object expected");t.arrayValue=a.google.firestore.v1.ArrayValue.fromObject(e.arrayValue)}if(null!=e.mapValue){if("object"!=typeof e.mapValue)throw TypeError(".google.firestore.v1.Value.mapValue: object expected");t.mapValue=a.google.firestore.v1.MapValue.fromObject(e.mapValue)}return t},s.toObject=function(e,t){t=t||{};var o={};return null!=e.booleanValue&&e.hasOwnProperty("booleanValue")&&(o.booleanValue=e.booleanValue,t.oneofs)&&(o.valueType="booleanValue"),null!=e.integerValue&&e.hasOwnProperty("integerValue")&&("number"==typeof e.integerValue?o.integerValue=t.longs===String?String(e.integerValue):e.integerValue:o.integerValue=t.longs===String?i.Long.prototype.toString.call(e.integerValue):t.longs===Number?new i.LongBits(e.integerValue.low>>>0,e.integerValue.high>>>0).toNumber():e.integerValue,t.oneofs)&&(o.valueType="integerValue"),null!=e.doubleValue&&e.hasOwnProperty("doubleValue")&&(o.doubleValue=t.json&&!isFinite(e.doubleValue)?String(e.doubleValue):e.doubleValue,t.oneofs)&&(o.valueType="doubleValue"),null!=e.referenceValue&&e.hasOwnProperty("referenceValue")&&(o.referenceValue=e.referenceValue,t.oneofs)&&(o.valueType="referenceValue"),null!=e.mapValue&&e.hasOwnProperty("mapValue")&&(o.mapValue=a.google.firestore.v1.MapValue.toObject(e.mapValue,t),t.oneofs)&&(o.valueType="mapValue"),null!=e.geoPointValue&&e.hasOwnProperty("geoPointValue")&&(o.geoPointValue=a.google.type.LatLng.toObject(e.geoPointValue,t),t.oneofs)&&(o.valueType="geoPointValue"),null!=e.arrayValue&&e.hasOwnProperty("arrayValue")&&(o.arrayValue=a.google.firestore.v1.ArrayValue.toObject(e.arrayValue,t),t.oneofs)&&(o.valueType="arrayValue"),null!=e.timestampValue&&e.hasOwnProperty("timestampValue")&&(o.timestampValue=a.google.protobuf.Timestamp.toObject(e.timestampValue,t),t.oneofs)&&(o.valueType="timestampValue"),null!=e.nullValue&&e.hasOwnProperty("nullValue")&&(o.nullValue=t.enums!==String||void 0===a.google.protobuf.NullValue[e.nullValue]?e.nullValue:a.google.protobuf.NullValue[e.nullValue],t.oneofs)&&(o.valueType="nullValue"),null!=e.stringValue&&e.hasOwnProperty("stringValue")&&(o.stringValue=e.stringValue,t.oneofs)&&(o.valueType="stringValue"),null!=e.bytesValue&&e.hasOwnProperty("bytesValue")&&(o.bytesValue=t.bytes===String?i.base64.encode(e.bytesValue,0,e.bytesValue.length):t.bytes===Array?Array.prototype.slice.call(e.bytesValue):e.bytesValue,t.oneofs)&&(o.valueType="bytesValue"),o},s.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},s.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.v1.Value"},s),o.ArrayValue=(G.prototype.values=i.emptyArray,G.fromObject=function(e){if(e instanceof a.google.firestore.v1.ArrayValue)return e;var t=new a.google.firestore.v1.ArrayValue;if(e.values){if(!Array.isArray(e.values))throw TypeError(".google.firestore.v1.ArrayValue.values: array expected");t.values=[];for(var o=0;o<e.values.length;++o){if("object"!=typeof e.values[o])throw TypeError(".google.firestore.v1.ArrayValue.values: object expected");t.values[o]=a.google.firestore.v1.Value.fromObject(e.values[o])}}return t},G.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.values=[]),e.values&&e.values.length){o.values=[];for(var r=0;r<e.values.length;++r)o.values[r]=a.google.firestore.v1.Value.toObject(e.values[r],t)}return o},G.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},G.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.v1.ArrayValue"},G),o.MapValue=(q.prototype.fields=i.emptyObject,q.fromObject=function(e){if(e instanceof a.google.firestore.v1.MapValue)return e;var t=new a.google.firestore.v1.MapValue;if(e.fields){if("object"!=typeof e.fields)throw TypeError(".google.firestore.v1.MapValue.fields: object expected");t.fields={};for(var o=Object.keys(e.fields),r=0;r<o.length;++r){if("object"!=typeof e.fields[o[r]])throw TypeError(".google.firestore.v1.MapValue.fields: object expected");t.fields[o[r]]=a.google.firestore.v1.Value.fromObject(e.fields[o[r]])}}return t},q.toObject=function(e,t){var o,r={};if(((t=t||{}).objects||t.defaults)&&(r.fields={}),e.fields&&(o=Object.keys(e.fields)).length){r.fields={};for(var n=0;n<o.length;++n)r.fields[o[n]]=a.google.firestore.v1.Value.toObject(e.fields[o[n]],t)}return r},q.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},q.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.v1.MapValue"},q),o.BitSequence=(Y.prototype.bitmap=i.newBuffer([]),Y.prototype.padding=0,Y.fromObject=function(e){var t;return e instanceof a.google.firestore.v1.BitSequence?e:(t=new a.google.firestore.v1.BitSequence,null!=e.bitmap&&("string"==typeof e.bitmap?i.base64.decode(e.bitmap,t.bitmap=i.newBuffer(i.base64.length(e.bitmap)),0):0<=e.bitmap.length&&(t.bitmap=e.bitmap)),null!=e.padding&&(t.padding=0|e.padding),t)},Y.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(t.bytes===String?o.bitmap="":(o.bitmap=[],t.bytes!==Array&&(o.bitmap=i.newBuffer(o.bitmap))),o.padding=0),null!=e.bitmap&&e.hasOwnProperty("bitmap")&&(o.bitmap=t.bytes===String?i.base64.encode(e.bitmap,0,e.bitmap.length):t.bytes===Array?Array.prototype.slice.call(e.bitmap):e.bitmap),null!=e.padding&&e.hasOwnProperty("padding")&&(o.padding=e.padding),o},Y.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},Y.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.v1.BitSequence"},Y),o.BloomFilter=(W.prototype.bits=null,W.prototype.hashCount=0,W.fromObject=function(e){if(e instanceof a.google.firestore.v1.BloomFilter)return e;var t=new a.google.firestore.v1.BloomFilter;if(null!=e.bits){if("object"!=typeof e.bits)throw TypeError(".google.firestore.v1.BloomFilter.bits: object expected");t.bits=a.google.firestore.v1.BitSequence.fromObject(e.bits)}return null!=e.hashCount&&(t.hashCount=0|e.hashCount),t},W.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.bits=null,o.hashCount=0),null!=e.bits&&e.hasOwnProperty("bits")&&(o.bits=a.google.firestore.v1.BitSequence.toObject(e.bits,t)),null!=e.hashCount&&e.hasOwnProperty("hashCount")&&(o.hashCount=e.hashCount),o},W.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},W.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.v1.BloomFilter"},W),o.DocumentMask=(z.prototype.fieldPaths=i.emptyArray,z.fromObject=function(e){if(e instanceof a.google.firestore.v1.DocumentMask)return e;var t=new a.google.firestore.v1.DocumentMask;if(e.fieldPaths){if(!Array.isArray(e.fieldPaths))throw TypeError(".google.firestore.v1.DocumentMask.fieldPaths: array expected");t.fieldPaths=[];for(var o=0;o<e.fieldPaths.length;++o)t.fieldPaths[o]=String(e.fieldPaths[o])}return t},z.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.fieldPaths=[]),e.fieldPaths&&e.fieldPaths.length){o.fieldPaths=[];for(var r=0;r<e.fieldPaths.length;++r)o.fieldPaths[r]=e.fieldPaths[r]}return o},z.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},z.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.v1.DocumentMask"},z),o.Precondition=(H.prototype.exists=null,H.prototype.updateTime=null,Object.defineProperty(H.prototype,"conditionType",{get:i.oneOfGetter(n=["exists","updateTime"]),set:i.oneOfSetter(n)}),H.fromObject=function(e){if(e instanceof a.google.firestore.v1.Precondition)return e;var t=new a.google.firestore.v1.Precondition;if(null!=e.exists&&(t.exists=Boolean(e.exists)),null!=e.updateTime){if("object"!=typeof e.updateTime)throw TypeError(".google.firestore.v1.Precondition.updateTime: object expected");t.updateTime=a.google.protobuf.Timestamp.fromObject(e.updateTime)}return t},H.toObject=function(e,t){t=t||{};var o={};return null!=e.exists&&e.hasOwnProperty("exists")&&(o.exists=e.exists,t.oneofs)&&(o.conditionType="exists"),null!=e.updateTime&&e.hasOwnProperty("updateTime")&&(o.updateTime=a.google.protobuf.Timestamp.toObject(e.updateTime,t),t.oneofs)&&(o.conditionType="updateTime"),o},H.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},H.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.v1.Precondition"},H),o.TransactionOptions=(K.prototype.readOnly=null,K.prototype.readWrite=null,Object.defineProperty(K.prototype,"mode",{get:i.oneOfGetter(n=["readOnly","readWrite"]),set:i.oneOfSetter(n)}),K.fromObject=function(e){if(e instanceof a.google.firestore.v1.TransactionOptions)return e;var t=new a.google.firestore.v1.TransactionOptions;if(null!=e.readOnly){if("object"!=typeof e.readOnly)throw TypeError(".google.firestore.v1.TransactionOptions.readOnly: object expected");t.readOnly=a.google.firestore.v1.TransactionOptions.ReadOnly.fromObject(e.readOnly)}if(null!=e.readWrite){if("object"!=typeof e.readWrite)throw TypeError(".google.firestore.v1.TransactionOptions.readWrite: object expected");t.readWrite=a.google.firestore.v1.TransactionOptions.ReadWrite.fromObject(e.readWrite)}return t},K.toObject=function(e,t){t=t||{};var o={};return null!=e.readOnly&&e.hasOwnProperty("readOnly")&&(o.readOnly=a.google.firestore.v1.TransactionOptions.ReadOnly.toObject(e.readOnly,t),t.oneofs)&&(o.mode="readOnly"),null!=e.readWrite&&e.hasOwnProperty("readWrite")&&(o.readWrite=a.google.firestore.v1.TransactionOptions.ReadWrite.toObject(e.readWrite,t),t.oneofs)&&(o.mode="readWrite"),o},K.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},K.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.v1.TransactionOptions"},K.ReadWrite=(X.prototype.retryTransaction=i.newBuffer([]),X.fromObject=function(e){var t;return e instanceof a.google.firestore.v1.TransactionOptions.ReadWrite?e:(t=new a.google.firestore.v1.TransactionOptions.ReadWrite,null!=e.retryTransaction&&("string"==typeof e.retryTransaction?i.base64.decode(e.retryTransaction,t.retryTransaction=i.newBuffer(i.base64.length(e.retryTransaction)),0):0<=e.retryTransaction.length&&(t.retryTransaction=e.retryTransaction)),t)},X.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(t.bytes===String?o.retryTransaction="":(o.retryTransaction=[],t.bytes!==Array&&(o.retryTransaction=i.newBuffer(o.retryTransaction)))),null!=e.retryTransaction&&e.hasOwnProperty("retryTransaction")&&(o.retryTransaction=t.bytes===String?i.base64.encode(e.retryTransaction,0,e.retryTransaction.length):t.bytes===Array?Array.prototype.slice.call(e.retryTransaction):e.retryTransaction),o},X.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},X.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.v1.TransactionOptions.ReadWrite"},X),K.ReadOnly=(Z.prototype.readTime=null,Object.defineProperty(Z.prototype,"consistencySelector",{get:i.oneOfGetter(n=["readTime"]),set:i.oneOfSetter(n)}),Z.fromObject=function(e){if(e instanceof a.google.firestore.v1.TransactionOptions.ReadOnly)return e;var t=new a.google.firestore.v1.TransactionOptions.ReadOnly;if(null!=e.readTime){if("object"!=typeof e.readTime)throw TypeError(".google.firestore.v1.TransactionOptions.ReadOnly.readTime: object expected");t.readTime=a.google.protobuf.Timestamp.fromObject(e.readTime)}return t},Z.toObject=function(e,t){t=t||{};var o={};return null!=e.readTime&&e.hasOwnProperty("readTime")&&(o.readTime=a.google.protobuf.Timestamp.toObject(e.readTime,t),t.oneofs)&&(o.consistencySelector="readTime"),o},Z.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},Z.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.v1.TransactionOptions.ReadOnly"},Z),K),o.Firestore=((l.prototype=Object.create(r.rpc.Service.prototype)).constructor=l,Object.defineProperty(l.prototype.getDocument=function e(t,o){return this.rpcCall(e,a.google.firestore.v1.GetDocumentRequest,a.google.firestore.v1.Document,t,o)},"name",{value:"GetDocument"}),Object.defineProperty(l.prototype.listDocuments=function e(t,o){return this.rpcCall(e,a.google.firestore.v1.ListDocumentsRequest,a.google.firestore.v1.ListDocumentsResponse,t,o)},"name",{value:"ListDocuments"}),Object.defineProperty(l.prototype.updateDocument=function e(t,o){return this.rpcCall(e,a.google.firestore.v1.UpdateDocumentRequest,a.google.firestore.v1.Document,t,o)},"name",{value:"UpdateDocument"}),Object.defineProperty(l.prototype.deleteDocument=function e(t,o){return this.rpcCall(e,a.google.firestore.v1.DeleteDocumentRequest,a.google.protobuf.Empty,t,o)},"name",{value:"DeleteDocument"}),Object.defineProperty(l.prototype.batchGetDocuments=function e(t,o){return this.rpcCall(e,a.google.firestore.v1.BatchGetDocumentsRequest,a.google.firestore.v1.BatchGetDocumentsResponse,t,o)},"name",{value:"BatchGetDocuments"}),Object.defineProperty(l.prototype.beginTransaction=function e(t,o){return this.rpcCall(e,a.google.firestore.v1.BeginTransactionRequest,a.google.firestore.v1.BeginTransactionResponse,t,o)},"name",{value:"BeginTransaction"}),Object.defineProperty(l.prototype.commit=function e(t,o){return this.rpcCall(e,a.google.firestore.v1.CommitRequest,a.google.firestore.v1.CommitResponse,t,o)},"name",{value:"Commit"}),Object.defineProperty(l.prototype.rollback=function e(t,o){return this.rpcCall(e,a.google.firestore.v1.RollbackRequest,a.google.protobuf.Empty,t,o)},"name",{value:"Rollback"}),Object.defineProperty(l.prototype.runQuery=function e(t,o){return this.rpcCall(e,a.google.firestore.v1.RunQueryRequest,a.google.firestore.v1.RunQueryResponse,t,o)},"name",{value:"RunQuery"}),Object.defineProperty(l.prototype.runAggregationQuery=function e(t,o){return this.rpcCall(e,a.google.firestore.v1.RunAggregationQueryRequest,a.google.firestore.v1.RunAggregationQueryResponse,t,o)},"name",{value:"RunAggregationQuery"}),Object.defineProperty(l.prototype.partitionQuery=function e(t,o){return this.rpcCall(e,a.google.firestore.v1.PartitionQueryRequest,a.google.firestore.v1.PartitionQueryResponse,t,o)},"name",{value:"PartitionQuery"}),Object.defineProperty(l.prototype.write=function e(t,o){return this.rpcCall(e,a.google.firestore.v1.WriteRequest,a.google.firestore.v1.WriteResponse,t,o)},"name",{value:"Write"}),Object.defineProperty(l.prototype.listen=function e(t,o){return this.rpcCall(e,a.google.firestore.v1.ListenRequest,a.google.firestore.v1.ListenResponse,t,o)},"name",{value:"Listen"}),Object.defineProperty(l.prototype.listCollectionIds=function e(t,o){return this.rpcCall(e,a.google.firestore.v1.ListCollectionIdsRequest,a.google.firestore.v1.ListCollectionIdsResponse,t,o)},"name",{value:"ListCollectionIds"}),Object.defineProperty(l.prototype.batchWrite=function e(t,o){return this.rpcCall(e,a.google.firestore.v1.BatchWriteRequest,a.google.firestore.v1.BatchWriteResponse,t,o)},"name",{value:"BatchWrite"}),Object.defineProperty(l.prototype.createDocument=function e(t,o){return this.rpcCall(e,a.google.firestore.v1.CreateDocumentRequest,a.google.firestore.v1.Document,t,o)},"name",{value:"CreateDocument"}),l),o.GetDocumentRequest=($.prototype.name="",$.prototype.mask=null,$.prototype.transaction=null,$.prototype.readTime=null,Object.defineProperty($.prototype,"consistencySelector",{get:i.oneOfGetter(n=["transaction","readTime"]),set:i.oneOfSetter(n)}),$.fromObject=function(e){if(e instanceof a.google.firestore.v1.GetDocumentRequest)return e;var t=new a.google.firestore.v1.GetDocumentRequest;if(null!=e.name&&(t.name=String(e.name)),null!=e.mask){if("object"!=typeof e.mask)throw TypeError(".google.firestore.v1.GetDocumentRequest.mask: object expected");t.mask=a.google.firestore.v1.DocumentMask.fromObject(e.mask)}if(null!=e.transaction&&("string"==typeof e.transaction?i.base64.decode(e.transaction,t.transaction=i.newBuffer(i.base64.length(e.transaction)),0):0<=e.transaction.length&&(t.transaction=e.transaction)),null!=e.readTime){if("object"!=typeof e.readTime)throw TypeError(".google.firestore.v1.GetDocumentRequest.readTime: object expected");t.readTime=a.google.protobuf.Timestamp.fromObject(e.readTime)}return t},$.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.name="",o.mask=null),null!=e.name&&e.hasOwnProperty("name")&&(o.name=e.name),null!=e.mask&&e.hasOwnProperty("mask")&&(o.mask=a.google.firestore.v1.DocumentMask.toObject(e.mask,t)),null!=e.transaction&&e.hasOwnProperty("transaction")&&(o.transaction=t.bytes===String?i.base64.encode(e.transaction,0,e.transaction.length):t.bytes===Array?Array.prototype.slice.call(e.transaction):e.transaction,t.oneofs)&&(o.consistencySelector="transaction"),null!=e.readTime&&e.hasOwnProperty("readTime")&&(o.readTime=a.google.protobuf.Timestamp.toObject(e.readTime,t),t.oneofs)&&(o.consistencySelector="readTime"),o},$.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},$.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.v1.GetDocumentRequest"},$),o.ListDocumentsRequest=(u.prototype.parent="",u.prototype.collectionId="",u.prototype.pageSize=0,u.prototype.pageToken="",u.prototype.orderBy="",u.prototype.mask=null,u.prototype.transaction=null,u.prototype.readTime=null,u.prototype.showMissing=!1,Object.defineProperty(u.prototype,"consistencySelector",{get:i.oneOfGetter(n=["transaction","readTime"]),set:i.oneOfSetter(n)}),u.fromObject=function(e){if(e instanceof a.google.firestore.v1.ListDocumentsRequest)return e;var t=new a.google.firestore.v1.ListDocumentsRequest;if(null!=e.parent&&(t.parent=String(e.parent)),null!=e.collectionId&&(t.collectionId=String(e.collectionId)),null!=e.pageSize&&(t.pageSize=0|e.pageSize),null!=e.pageToken&&(t.pageToken=String(e.pageToken)),null!=e.orderBy&&(t.orderBy=String(e.orderBy)),null!=e.mask){if("object"!=typeof e.mask)throw TypeError(".google.firestore.v1.ListDocumentsRequest.mask: object expected");t.mask=a.google.firestore.v1.DocumentMask.fromObject(e.mask)}if(null!=e.transaction&&("string"==typeof e.transaction?i.base64.decode(e.transaction,t.transaction=i.newBuffer(i.base64.length(e.transaction)),0):0<=e.transaction.length&&(t.transaction=e.transaction)),null!=e.readTime){if("object"!=typeof e.readTime)throw TypeError(".google.firestore.v1.ListDocumentsRequest.readTime: object expected");t.readTime=a.google.protobuf.Timestamp.fromObject(e.readTime)}return null!=e.showMissing&&(t.showMissing=Boolean(e.showMissing)),t},u.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.parent="",o.collectionId="",o.pageSize=0,o.pageToken="",o.orderBy="",o.mask=null,o.showMissing=!1),null!=e.parent&&e.hasOwnProperty("parent")&&(o.parent=e.parent),null!=e.collectionId&&e.hasOwnProperty("collectionId")&&(o.collectionId=e.collectionId),null!=e.pageSize&&e.hasOwnProperty("pageSize")&&(o.pageSize=e.pageSize),null!=e.pageToken&&e.hasOwnProperty("pageToken")&&(o.pageToken=e.pageToken),null!=e.orderBy&&e.hasOwnProperty("orderBy")&&(o.orderBy=e.orderBy),null!=e.mask&&e.hasOwnProperty("mask")&&(o.mask=a.google.firestore.v1.DocumentMask.toObject(e.mask,t)),null!=e.transaction&&e.hasOwnProperty("transaction")&&(o.transaction=t.bytes===String?i.base64.encode(e.transaction,0,e.transaction.length):t.bytes===Array?Array.prototype.slice.call(e.transaction):e.transaction,t.oneofs)&&(o.consistencySelector="transaction"),null!=e.readTime&&e.hasOwnProperty("readTime")&&(o.readTime=a.google.protobuf.Timestamp.toObject(e.readTime,t),t.oneofs)&&(o.consistencySelector="readTime"),null!=e.showMissing&&e.hasOwnProperty("showMissing")&&(o.showMissing=e.showMissing),o},u.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},u.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.v1.ListDocumentsRequest"},u),o.ListDocumentsResponse=(ee.prototype.documents=i.emptyArray,ee.prototype.nextPageToken="",ee.fromObject=function(e){if(e instanceof a.google.firestore.v1.ListDocumentsResponse)return e;var t=new a.google.firestore.v1.ListDocumentsResponse;if(e.documents){if(!Array.isArray(e.documents))throw TypeError(".google.firestore.v1.ListDocumentsResponse.documents: array expected");t.documents=[];for(var o=0;o<e.documents.length;++o){if("object"!=typeof e.documents[o])throw TypeError(".google.firestore.v1.ListDocumentsResponse.documents: object expected");t.documents[o]=a.google.firestore.v1.Document.fromObject(e.documents[o])}}return null!=e.nextPageToken&&(t.nextPageToken=String(e.nextPageToken)),t},ee.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.documents=[]),t.defaults&&(o.nextPageToken=""),e.documents&&e.documents.length){o.documents=[];for(var r=0;r<e.documents.length;++r)o.documents[r]=a.google.firestore.v1.Document.toObject(e.documents[r],t)}return null!=e.nextPageToken&&e.hasOwnProperty("nextPageToken")&&(o.nextPageToken=e.nextPageToken),o},ee.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},ee.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.v1.ListDocumentsResponse"},ee),o.CreateDocumentRequest=(te.prototype.parent="",te.prototype.collectionId="",te.prototype.documentId="",te.prototype.document=null,te.prototype.mask=null,te.fromObject=function(e){if(e instanceof a.google.firestore.v1.CreateDocumentRequest)return e;var t=new a.google.firestore.v1.CreateDocumentRequest;if(null!=e.parent&&(t.parent=String(e.parent)),null!=e.collectionId&&(t.collectionId=String(e.collectionId)),null!=e.documentId&&(t.documentId=String(e.documentId)),null!=e.document){if("object"!=typeof e.document)throw TypeError(".google.firestore.v1.CreateDocumentRequest.document: object expected");t.document=a.google.firestore.v1.Document.fromObject(e.document)}if(null!=e.mask){if("object"!=typeof e.mask)throw TypeError(".google.firestore.v1.CreateDocumentRequest.mask: object expected");t.mask=a.google.firestore.v1.DocumentMask.fromObject(e.mask)}return t},te.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.parent="",o.collectionId="",o.documentId="",o.document=null,o.mask=null),null!=e.parent&&e.hasOwnProperty("parent")&&(o.parent=e.parent),null!=e.collectionId&&e.hasOwnProperty("collectionId")&&(o.collectionId=e.collectionId),null!=e.documentId&&e.hasOwnProperty("documentId")&&(o.documentId=e.documentId),null!=e.document&&e.hasOwnProperty("document")&&(o.document=a.google.firestore.v1.Document.toObject(e.document,t)),null!=e.mask&&e.hasOwnProperty("mask")&&(o.mask=a.google.firestore.v1.DocumentMask.toObject(e.mask,t)),o},te.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},te.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.v1.CreateDocumentRequest"},te),o.UpdateDocumentRequest=(oe.prototype.document=null,oe.prototype.updateMask=null,oe.prototype.mask=null,oe.prototype.currentDocument=null,oe.fromObject=function(e){if(e instanceof a.google.firestore.v1.UpdateDocumentRequest)return e;var t=new a.google.firestore.v1.UpdateDocumentRequest;if(null!=e.document){if("object"!=typeof e.document)throw TypeError(".google.firestore.v1.UpdateDocumentRequest.document: object expected");t.document=a.google.firestore.v1.Document.fromObject(e.document)}if(null!=e.updateMask){if("object"!=typeof e.updateMask)throw TypeError(".google.firestore.v1.UpdateDocumentRequest.updateMask: object expected");t.updateMask=a.google.firestore.v1.DocumentMask.fromObject(e.updateMask)}if(null!=e.mask){if("object"!=typeof e.mask)throw TypeError(".google.firestore.v1.UpdateDocumentRequest.mask: object expected");t.mask=a.google.firestore.v1.DocumentMask.fromObject(e.mask)}if(null!=e.currentDocument){if("object"!=typeof e.currentDocument)throw TypeError(".google.firestore.v1.UpdateDocumentRequest.currentDocument: object expected");t.currentDocument=a.google.firestore.v1.Precondition.fromObject(e.currentDocument)}return t},oe.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.document=null,o.updateMask=null,o.mask=null,o.currentDocument=null),null!=e.document&&e.hasOwnProperty("document")&&(o.document=a.google.firestore.v1.Document.toObject(e.document,t)),null!=e.updateMask&&e.hasOwnProperty("updateMask")&&(o.updateMask=a.google.firestore.v1.DocumentMask.toObject(e.updateMask,t)),null!=e.mask&&e.hasOwnProperty("mask")&&(o.mask=a.google.firestore.v1.DocumentMask.toObject(e.mask,t)),null!=e.currentDocument&&e.hasOwnProperty("currentDocument")&&(o.currentDocument=a.google.firestore.v1.Precondition.toObject(e.currentDocument,t)),o},oe.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},oe.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.v1.UpdateDocumentRequest"},oe),o.DeleteDocumentRequest=(re.prototype.name="",re.prototype.currentDocument=null,re.fromObject=function(e){if(e instanceof a.google.firestore.v1.DeleteDocumentRequest)return e;var t=new a.google.firestore.v1.DeleteDocumentRequest;if(null!=e.name&&(t.name=String(e.name)),null!=e.currentDocument){if("object"!=typeof e.currentDocument)throw TypeError(".google.firestore.v1.DeleteDocumentRequest.currentDocument: object expected");t.currentDocument=a.google.firestore.v1.Precondition.fromObject(e.currentDocument)}return t},re.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.name="",o.currentDocument=null),null!=e.name&&e.hasOwnProperty("name")&&(o.name=e.name),null!=e.currentDocument&&e.hasOwnProperty("currentDocument")&&(o.currentDocument=a.google.firestore.v1.Precondition.toObject(e.currentDocument,t)),o},re.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},re.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.v1.DeleteDocumentRequest"},re),o.BatchGetDocumentsRequest=(p.prototype.database="",p.prototype.documents=i.emptyArray,p.prototype.mask=null,p.prototype.transaction=null,p.prototype.newTransaction=null,p.prototype.readTime=null,Object.defineProperty(p.prototype,"consistencySelector",{get:i.oneOfGetter(n=["transaction","newTransaction","readTime"]),set:i.oneOfSetter(n)}),p.fromObject=function(e){if(e instanceof a.google.firestore.v1.BatchGetDocumentsRequest)return e;var t=new a.google.firestore.v1.BatchGetDocumentsRequest;if(null!=e.database&&(t.database=String(e.database)),e.documents){if(!Array.isArray(e.documents))throw TypeError(".google.firestore.v1.BatchGetDocumentsRequest.documents: array expected");t.documents=[];for(var o=0;o<e.documents.length;++o)t.documents[o]=String(e.documents[o])}if(null!=e.mask){if("object"!=typeof e.mask)throw TypeError(".google.firestore.v1.BatchGetDocumentsRequest.mask: object expected");t.mask=a.google.firestore.v1.DocumentMask.fromObject(e.mask)}if(null!=e.transaction&&("string"==typeof e.transaction?i.base64.decode(e.transaction,t.transaction=i.newBuffer(i.base64.length(e.transaction)),0):0<=e.transaction.length&&(t.transaction=e.transaction)),null!=e.newTransaction){if("object"!=typeof e.newTransaction)throw TypeError(".google.firestore.v1.BatchGetDocumentsRequest.newTransaction: object expected");t.newTransaction=a.google.firestore.v1.TransactionOptions.fromObject(e.newTransaction)}if(null!=e.readTime){if("object"!=typeof e.readTime)throw TypeError(".google.firestore.v1.BatchGetDocumentsRequest.readTime: object expected");t.readTime=a.google.protobuf.Timestamp.fromObject(e.readTime)}return t},p.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.documents=[]),t.defaults&&(o.database="",o.mask=null),null!=e.database&&e.hasOwnProperty("database")&&(o.database=e.database),e.documents&&e.documents.length){o.documents=[];for(var r=0;r<e.documents.length;++r)o.documents[r]=e.documents[r]}return null!=e.mask&&e.hasOwnProperty("mask")&&(o.mask=a.google.firestore.v1.DocumentMask.toObject(e.mask,t)),null!=e.transaction&&e.hasOwnProperty("transaction")&&(o.transaction=t.bytes===String?i.base64.encode(e.transaction,0,e.transaction.length):t.bytes===Array?Array.prototype.slice.call(e.transaction):e.transaction,t.oneofs)&&(o.consistencySelector="transaction"),null!=e.newTransaction&&e.hasOwnProperty("newTransaction")&&(o.newTransaction=a.google.firestore.v1.TransactionOptions.toObject(e.newTransaction,t),t.oneofs)&&(o.consistencySelector="newTransaction"),null!=e.readTime&&e.hasOwnProperty("readTime")&&(o.readTime=a.google.protobuf.Timestamp.toObject(e.readTime,t),t.oneofs)&&(o.consistencySelector="readTime"),o},p.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},p.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.v1.BatchGetDocumentsRequest"},p),o.BatchGetDocumentsResponse=(ne.prototype.found=null,ne.prototype.missing=null,ne.prototype.transaction=i.newBuffer([]),ne.prototype.readTime=null,Object.defineProperty(ne.prototype,"result",{get:i.oneOfGetter(n=["found","missing"]),set:i.oneOfSetter(n)}),ne.fromObject=function(e){if(e instanceof a.google.firestore.v1.BatchGetDocumentsResponse)return e;var t=new a.google.firestore.v1.BatchGetDocumentsResponse;if(null!=e.found){if("object"!=typeof e.found)throw TypeError(".google.firestore.v1.BatchGetDocumentsResponse.found: object expected");t.found=a.google.firestore.v1.Document.fromObject(e.found)}if(null!=e.missing&&(t.missing=String(e.missing)),null!=e.transaction&&("string"==typeof e.transaction?i.base64.decode(e.transaction,t.transaction=i.newBuffer(i.base64.length(e.transaction)),0):0<=e.transaction.length&&(t.transaction=e.transaction)),null!=e.readTime){if("object"!=typeof e.readTime)throw TypeError(".google.firestore.v1.BatchGetDocumentsResponse.readTime: object expected");t.readTime=a.google.protobuf.Timestamp.fromObject(e.readTime)}return t},ne.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(t.bytes===String?o.transaction="":(o.transaction=[],t.bytes!==Array&&(o.transaction=i.newBuffer(o.transaction))),o.readTime=null),null!=e.found&&e.hasOwnProperty("found")&&(o.found=a.google.firestore.v1.Document.toObject(e.found,t),t.oneofs)&&(o.result="found"),null!=e.missing&&e.hasOwnProperty("missing")&&(o.missing=e.missing,t.oneofs)&&(o.result="missing"),null!=e.transaction&&e.hasOwnProperty("transaction")&&(o.transaction=t.bytes===String?i.base64.encode(e.transaction,0,e.transaction.length):t.bytes===Array?Array.prototype.slice.call(e.transaction):e.transaction),null!=e.readTime&&e.hasOwnProperty("readTime")&&(o.readTime=a.google.protobuf.Timestamp.toObject(e.readTime,t)),o},ne.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},ne.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.v1.BatchGetDocumentsResponse"},ne),o.BeginTransactionRequest=(ie.prototype.database="",ie.prototype.options=null,ie.fromObject=function(e){if(e instanceof a.google.firestore.v1.BeginTransactionRequest)return e;var t=new a.google.firestore.v1.BeginTransactionRequest;if(null!=e.database&&(t.database=String(e.database)),null!=e.options){if("object"!=typeof e.options)throw TypeError(".google.firestore.v1.BeginTransactionRequest.options: object expected");t.options=a.google.firestore.v1.TransactionOptions.fromObject(e.options)}return t},ie.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.database="",o.options=null),null!=e.database&&e.hasOwnProperty("database")&&(o.database=e.database),null!=e.options&&e.hasOwnProperty("options")&&(o.options=a.google.firestore.v1.TransactionOptions.toObject(e.options,t)),o},ie.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},ie.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.v1.BeginTransactionRequest"},ie),o.BeginTransactionResponse=(ae.prototype.transaction=i.newBuffer([]),ae.fromObject=function(e){var t;return e instanceof a.google.firestore.v1.BeginTransactionResponse?e:(t=new a.google.firestore.v1.BeginTransactionResponse,null!=e.transaction&&("string"==typeof e.transaction?i.base64.decode(e.transaction,t.transaction=i.newBuffer(i.base64.length(e.transaction)),0):0<=e.transaction.length&&(t.transaction=e.transaction)),t)},ae.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(t.bytes===String?o.transaction="":(o.transaction=[],t.bytes!==Array&&(o.transaction=i.newBuffer(o.transaction)))),null!=e.transaction&&e.hasOwnProperty("transaction")&&(o.transaction=t.bytes===String?i.base64.encode(e.transaction,0,e.transaction.length):t.bytes===Array?Array.prototype.slice.call(e.transaction):e.transaction),o},ae.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},ae.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.v1.BeginTransactionResponse"},ae),o.CommitRequest=(se.prototype.database="",se.prototype.writes=i.emptyArray,se.prototype.transaction=i.newBuffer([]),se.fromObject=function(e){if(e instanceof a.google.firestore.v1.CommitRequest)return e;var t=new a.google.firestore.v1.CommitRequest;if(null!=e.database&&(t.database=String(e.database)),e.writes){if(!Array.isArray(e.writes))throw TypeError(".google.firestore.v1.CommitRequest.writes: array expected");t.writes=[];for(var o=0;o<e.writes.length;++o){if("object"!=typeof e.writes[o])throw TypeError(".google.firestore.v1.CommitRequest.writes: object expected");t.writes[o]=a.google.firestore.v1.Write.fromObject(e.writes[o])}}return null!=e.transaction&&("string"==typeof e.transaction?i.base64.decode(e.transaction,t.transaction=i.newBuffer(i.base64.length(e.transaction)),0):0<=e.transaction.length&&(t.transaction=e.transaction)),t},se.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.writes=[]),t.defaults&&(o.database="",t.bytes===String?o.transaction="":(o.transaction=[],t.bytes!==Array&&(o.transaction=i.newBuffer(o.transaction)))),null!=e.database&&e.hasOwnProperty("database")&&(o.database=e.database),e.writes&&e.writes.length){o.writes=[];for(var r=0;r<e.writes.length;++r)o.writes[r]=a.google.firestore.v1.Write.toObject(e.writes[r],t)}return null!=e.transaction&&e.hasOwnProperty("transaction")&&(o.transaction=t.bytes===String?i.base64.encode(e.transaction,0,e.transaction.length):t.bytes===Array?Array.prototype.slice.call(e.transaction):e.transaction),o},se.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},se.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.v1.CommitRequest"},se),o.CommitResponse=(le.prototype.writeResults=i.emptyArray,le.prototype.commitTime=null,le.fromObject=function(e){if(e instanceof a.google.firestore.v1.CommitResponse)return e;var t=new a.google.firestore.v1.CommitResponse;if(e.writeResults){if(!Array.isArray(e.writeResults))throw TypeError(".google.firestore.v1.CommitResponse.writeResults: array expected");t.writeResults=[];for(var o=0;o<e.writeResults.length;++o){if("object"!=typeof e.writeResults[o])throw TypeError(".google.firestore.v1.CommitResponse.writeResults: object expected");t.writeResults[o]=a.google.firestore.v1.WriteResult.fromObject(e.writeResults[o])}}if(null!=e.commitTime){if("object"!=typeof e.commitTime)throw TypeError(".google.firestore.v1.CommitResponse.commitTime: object expected");t.commitTime=a.google.protobuf.Timestamp.fromObject(e.commitTime)}return t},le.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.writeResults=[]),t.defaults&&(o.commitTime=null),e.writeResults&&e.writeResults.length){o.writeResults=[];for(var r=0;r<e.writeResults.length;++r)o.writeResults[r]=a.google.firestore.v1.WriteResult.toObject(e.writeResults[r],t)}return null!=e.commitTime&&e.hasOwnProperty("commitTime")&&(o.commitTime=a.google.protobuf.Timestamp.toObject(e.commitTime,t)),o},le.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},le.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.v1.CommitResponse"},le),o.RollbackRequest=(ue.prototype.database="",ue.prototype.transaction=i.newBuffer([]),ue.fromObject=function(e){var t;return e instanceof a.google.firestore.v1.RollbackRequest?e:(t=new a.google.firestore.v1.RollbackRequest,null!=e.database&&(t.database=String(e.database)),null!=e.transaction&&("string"==typeof e.transaction?i.base64.decode(e.transaction,t.transaction=i.newBuffer(i.base64.length(e.transaction)),0):0<=e.transaction.length&&(t.transaction=e.transaction)),t)},ue.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.database="",t.bytes===String?o.transaction="":(o.transaction=[],t.bytes!==Array&&(o.transaction=i.newBuffer(o.transaction)))),null!=e.database&&e.hasOwnProperty("database")&&(o.database=e.database),null!=e.transaction&&e.hasOwnProperty("transaction")&&(o.transaction=t.bytes===String?i.base64.encode(e.transaction,0,e.transaction.length):t.bytes===Array?Array.prototype.slice.call(e.transaction):e.transaction),o},ue.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},ue.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.v1.RollbackRequest"},ue),o.RunQueryRequest=(c.prototype.parent="",c.prototype.structuredQuery=null,c.prototype.transaction=null,c.prototype.newTransaction=null,c.prototype.readTime=null,c.prototype.explainOptions=null,Object.defineProperty(c.prototype,"queryType",{get:i.oneOfGetter(n=["structuredQuery"]),set:i.oneOfSetter(n)}),Object.defineProperty(c.prototype,"consistencySelector",{get:i.oneOfGetter(n=["transaction","newTransaction","readTime"]),set:i.oneOfSetter(n)}),c.fromObject=function(e){if(e instanceof a.google.firestore.v1.RunQueryRequest)return e;var t=new a.google.firestore.v1.RunQueryRequest;if(null!=e.parent&&(t.parent=String(e.parent)),null!=e.structuredQuery){if("object"!=typeof e.structuredQuery)throw TypeError(".google.firestore.v1.RunQueryRequest.structuredQuery: object expected");t.structuredQuery=a.google.firestore.v1.StructuredQuery.fromObject(e.structuredQuery)}if(null!=e.transaction&&("string"==typeof e.transaction?i.base64.decode(e.transaction,t.transaction=i.newBuffer(i.base64.length(e.transaction)),0):0<=e.transaction.length&&(t.transaction=e.transaction)),null!=e.newTransaction){if("object"!=typeof e.newTransaction)throw TypeError(".google.firestore.v1.RunQueryRequest.newTransaction: object expected");t.newTransaction=a.google.firestore.v1.TransactionOptions.fromObject(e.newTransaction)}if(null!=e.readTime){if("object"!=typeof e.readTime)throw TypeError(".google.firestore.v1.RunQueryRequest.readTime: object expected");t.readTime=a.google.protobuf.Timestamp.fromObject(e.readTime)}if(null!=e.explainOptions){if("object"!=typeof e.explainOptions)throw TypeError(".google.firestore.v1.RunQueryRequest.explainOptions: object expected");t.explainOptions=a.google.firestore.v1.ExplainOptions.fromObject(e.explainOptions)}return t},c.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.parent="",o.explainOptions=null),null!=e.parent&&e.hasOwnProperty("parent")&&(o.parent=e.parent),null!=e.structuredQuery&&e.hasOwnProperty("structuredQuery")&&(o.structuredQuery=a.google.firestore.v1.StructuredQuery.toObject(e.structuredQuery,t),t.oneofs)&&(o.queryType="structuredQuery"),null!=e.transaction&&e.hasOwnProperty("transaction")&&(o.transaction=t.bytes===String?i.base64.encode(e.transaction,0,e.transaction.length):t.bytes===Array?Array.prototype.slice.call(e.transaction):e.transaction,t.oneofs)&&(o.consistencySelector="transaction"),null!=e.newTransaction&&e.hasOwnProperty("newTransaction")&&(o.newTransaction=a.google.firestore.v1.TransactionOptions.toObject(e.newTransaction,t),t.oneofs)&&(o.consistencySelector="newTransaction"),null!=e.readTime&&e.hasOwnProperty("readTime")&&(o.readTime=a.google.protobuf.Timestamp.toObject(e.readTime,t),t.oneofs)&&(o.consistencySelector="readTime"),null!=e.explainOptions&&e.hasOwnProperty("explainOptions")&&(o.explainOptions=a.google.firestore.v1.ExplainOptions.toObject(e.explainOptions,t)),o},c.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},c.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.v1.RunQueryRequest"},c),o.RunQueryResponse=(g.prototype.transaction=i.newBuffer([]),g.prototype.document=null,g.prototype.readTime=null,g.prototype.skippedResults=0,g.prototype.done=null,g.prototype.explainMetrics=null,Object.defineProperty(g.prototype,"continuationSelector",{get:i.oneOfGetter(n=["done"]),set:i.oneOfSetter(n)}),g.fromObject=function(e){if(e instanceof a.google.firestore.v1.RunQueryResponse)return e;var t=new a.google.firestore.v1.RunQueryResponse;if(null!=e.transaction&&("string"==typeof e.transaction?i.base64.decode(e.transaction,t.transaction=i.newBuffer(i.base64.length(e.transaction)),0):0<=e.transaction.length&&(t.transaction=e.transaction)),null!=e.document){if("object"!=typeof e.document)throw TypeError(".google.firestore.v1.RunQueryResponse.document: object expected");t.document=a.google.firestore.v1.Document.fromObject(e.document)}if(null!=e.readTime){if("object"!=typeof e.readTime)throw TypeError(".google.firestore.v1.RunQueryResponse.readTime: object expected");t.readTime=a.google.protobuf.Timestamp.fromObject(e.readTime)}if(null!=e.skippedResults&&(t.skippedResults=0|e.skippedResults),null!=e.done&&(t.done=Boolean(e.done)),null!=e.explainMetrics){if("object"!=typeof e.explainMetrics)throw TypeError(".google.firestore.v1.RunQueryResponse.explainMetrics: object expected");t.explainMetrics=a.google.firestore.v1.ExplainMetrics.fromObject(e.explainMetrics)}return t},g.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.document=null,t.bytes===String?o.transaction="":(o.transaction=[],t.bytes!==Array&&(o.transaction=i.newBuffer(o.transaction))),o.readTime=null,o.skippedResults=0,o.explainMetrics=null),null!=e.document&&e.hasOwnProperty("document")&&(o.document=a.google.firestore.v1.Document.toObject(e.document,t)),null!=e.transaction&&e.hasOwnProperty("transaction")&&(o.transaction=t.bytes===String?i.base64.encode(e.transaction,0,e.transaction.length):t.bytes===Array?Array.prototype.slice.call(e.transaction):e.transaction),null!=e.readTime&&e.hasOwnProperty("readTime")&&(o.readTime=a.google.protobuf.Timestamp.toObject(e.readTime,t)),null!=e.skippedResults&&e.hasOwnProperty("skippedResults")&&(o.skippedResults=e.skippedResults),null!=e.done&&e.hasOwnProperty("done")&&(o.done=e.done,t.oneofs)&&(o.continuationSelector="done"),null!=e.explainMetrics&&e.hasOwnProperty("explainMetrics")&&(o.explainMetrics=a.google.firestore.v1.ExplainMetrics.toObject(e.explainMetrics,t)),o},g.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},g.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.v1.RunQueryResponse"},g),o.RunAggregationQueryRequest=(f.prototype.parent="",f.prototype.structuredAggregationQuery=null,f.prototype.transaction=null,f.prototype.newTransaction=null,f.prototype.readTime=null,f.prototype.explainOptions=null,Object.defineProperty(f.prototype,"queryType",{get:i.oneOfGetter(n=["structuredAggregationQuery"]),set:i.oneOfSetter(n)}),Object.defineProperty(f.prototype,"consistencySelector",{get:i.oneOfGetter(n=["transaction","newTransaction","readTime"]),set:i.oneOfSetter(n)}),f.fromObject=function(e){if(e instanceof a.google.firestore.v1.RunAggregationQueryRequest)return e;var t=new a.google.firestore.v1.RunAggregationQueryRequest;if(null!=e.parent&&(t.parent=String(e.parent)),null!=e.structuredAggregationQuery){if("object"!=typeof e.structuredAggregationQuery)throw TypeError(".google.firestore.v1.RunAggregationQueryRequest.structuredAggregationQuery: object expected");t.structuredAggregationQuery=a.google.firestore.v1.StructuredAggregationQuery.fromObject(e.structuredAggregationQuery)}if(null!=e.transaction&&("string"==typeof e.transaction?i.base64.decode(e.transaction,t.transaction=i.newBuffer(i.base64.length(e.transaction)),0):0<=e.transaction.length&&(t.transaction=e.transaction)),null!=e.newTransaction){if("object"!=typeof e.newTransaction)throw TypeError(".google.firestore.v1.RunAggregationQueryRequest.newTransaction: object expected");t.newTransaction=a.google.firestore.v1.TransactionOptions.fromObject(e.newTransaction)}if(null!=e.readTime){if("object"!=typeof e.readTime)throw TypeError(".google.firestore.v1.RunAggregationQueryRequest.readTime: object expected");t.readTime=a.google.protobuf.Timestamp.fromObject(e.readTime)}if(null!=e.explainOptions){if("object"!=typeof e.explainOptions)throw TypeError(".google.firestore.v1.RunAggregationQueryRequest.explainOptions: object expected");t.explainOptions=a.google.firestore.v1.ExplainOptions.fromObject(e.explainOptions)}return t},f.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.parent="",o.explainOptions=null),null!=e.parent&&e.hasOwnProperty("parent")&&(o.parent=e.parent),null!=e.structuredAggregationQuery&&e.hasOwnProperty("structuredAggregationQuery")&&(o.structuredAggregationQuery=a.google.firestore.v1.StructuredAggregationQuery.toObject(e.structuredAggregationQuery,t),t.oneofs)&&(o.queryType="structuredAggregationQuery"),null!=e.transaction&&e.hasOwnProperty("transaction")&&(o.transaction=t.bytes===String?i.base64.encode(e.transaction,0,e.transaction.length):t.bytes===Array?Array.prototype.slice.call(e.transaction):e.transaction,t.oneofs)&&(o.consistencySelector="transaction"),null!=e.newTransaction&&e.hasOwnProperty("newTransaction")&&(o.newTransaction=a.google.firestore.v1.TransactionOptions.toObject(e.newTransaction,t),t.oneofs)&&(o.consistencySelector="newTransaction"),null!=e.readTime&&e.hasOwnProperty("readTime")&&(o.readTime=a.google.protobuf.Timestamp.toObject(e.readTime,t),t.oneofs)&&(o.consistencySelector="readTime"),null!=e.explainOptions&&e.hasOwnProperty("explainOptions")&&(o.explainOptions=a.google.firestore.v1.ExplainOptions.toObject(e.explainOptions,t)),o},f.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},f.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.v1.RunAggregationQueryRequest"},f),o.RunAggregationQueryResponse=(pe.prototype.result=null,pe.prototype.transaction=i.newBuffer([]),pe.prototype.readTime=null,pe.prototype.explainMetrics=null,pe.fromObject=function(e){if(e instanceof a.google.firestore.v1.RunAggregationQueryResponse)return e;var t=new a.google.firestore.v1.RunAggregationQueryResponse;if(null!=e.result){if("object"!=typeof e.result)throw TypeError(".google.firestore.v1.RunAggregationQueryResponse.result: object expected");t.result=a.google.firestore.v1.AggregationResult.fromObject(e.result)}if(null!=e.transaction&&("string"==typeof e.transaction?i.base64.decode(e.transaction,t.transaction=i.newBuffer(i.base64.length(e.transaction)),0):0<=e.transaction.length&&(t.transaction=e.transaction)),null!=e.readTime){if("object"!=typeof e.readTime)throw TypeError(".google.firestore.v1.RunAggregationQueryResponse.readTime: object expected");t.readTime=a.google.protobuf.Timestamp.fromObject(e.readTime)}if(null!=e.explainMetrics){if("object"!=typeof e.explainMetrics)throw TypeError(".google.firestore.v1.RunAggregationQueryResponse.explainMetrics: object expected");t.explainMetrics=a.google.firestore.v1.ExplainMetrics.fromObject(e.explainMetrics)}return t},pe.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.result=null,t.bytes===String?o.transaction="":(o.transaction=[],t.bytes!==Array&&(o.transaction=i.newBuffer(o.transaction))),o.readTime=null,o.explainMetrics=null),null!=e.result&&e.hasOwnProperty("result")&&(o.result=a.google.firestore.v1.AggregationResult.toObject(e.result,t)),null!=e.transaction&&e.hasOwnProperty("transaction")&&(o.transaction=t.bytes===String?i.base64.encode(e.transaction,0,e.transaction.length):t.bytes===Array?Array.prototype.slice.call(e.transaction):e.transaction),null!=e.readTime&&e.hasOwnProperty("readTime")&&(o.readTime=a.google.protobuf.Timestamp.toObject(e.readTime,t)),null!=e.explainMetrics&&e.hasOwnProperty("explainMetrics")&&(o.explainMetrics=a.google.firestore.v1.ExplainMetrics.toObject(e.explainMetrics,t)),o},pe.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},pe.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.v1.RunAggregationQueryResponse"},pe),o.PartitionQueryRequest=(d.prototype.parent="",d.prototype.structuredQuery=null,d.prototype.partitionCount=i.Long?i.Long.fromBits(0,0,!1):0,d.prototype.pageToken="",d.prototype.pageSize=0,d.prototype.readTime=null,Object.defineProperty(d.prototype,"queryType",{get:i.oneOfGetter(n=["structuredQuery"]),set:i.oneOfSetter(n)}),Object.defineProperty(d.prototype,"consistencySelector",{get:i.oneOfGetter(n=["readTime"]),set:i.oneOfSetter(n)}),d.fromObject=function(e){if(e instanceof a.google.firestore.v1.PartitionQueryRequest)return e;var t=new a.google.firestore.v1.PartitionQueryRequest;if(null!=e.parent&&(t.parent=String(e.parent)),null!=e.structuredQuery){if("object"!=typeof e.structuredQuery)throw TypeError(".google.firestore.v1.PartitionQueryRequest.structuredQuery: object expected");t.structuredQuery=a.google.firestore.v1.StructuredQuery.fromObject(e.structuredQuery)}if(null!=e.partitionCount&&(i.Long?(t.partitionCount=i.Long.fromValue(e.partitionCount)).unsigned=!1:"string"==typeof e.partitionCount?t.partitionCount=parseInt(e.partitionCount,10):"number"==typeof e.partitionCount?t.partitionCount=e.partitionCount:"object"==typeof e.partitionCount&&(t.partitionCount=new i.LongBits(e.partitionCount.low>>>0,e.partitionCount.high>>>0).toNumber())),null!=e.pageToken&&(t.pageToken=String(e.pageToken)),null!=e.pageSize&&(t.pageSize=0|e.pageSize),null!=e.readTime){if("object"!=typeof e.readTime)throw TypeError(".google.firestore.v1.PartitionQueryRequest.readTime: object expected");t.readTime=a.google.protobuf.Timestamp.fromObject(e.readTime)}return t},d.toObject=function(e,t){var o,r={};return(t=t||{}).defaults&&(r.parent="",i.Long?(o=new i.Long(0,0,!1),r.partitionCount=t.longs===String?o.toString():t.longs===Number?o.toNumber():o):r.partitionCount=t.longs===String?"0":0,r.pageToken="",r.pageSize=0),null!=e.parent&&e.hasOwnProperty("parent")&&(r.parent=e.parent),null!=e.structuredQuery&&e.hasOwnProperty("structuredQuery")&&(r.structuredQuery=a.google.firestore.v1.StructuredQuery.toObject(e.structuredQuery,t),t.oneofs)&&(r.queryType="structuredQuery"),null!=e.partitionCount&&e.hasOwnProperty("partitionCount")&&("number"==typeof e.partitionCount?r.partitionCount=t.longs===String?String(e.partitionCount):e.partitionCount:r.partitionCount=t.longs===String?i.Long.prototype.toString.call(e.partitionCount):t.longs===Number?new i.LongBits(e.partitionCount.low>>>0,e.partitionCount.high>>>0).toNumber():e.partitionCount),null!=e.pageToken&&e.hasOwnProperty("pageToken")&&(r.pageToken=e.pageToken),null!=e.pageSize&&e.hasOwnProperty("pageSize")&&(r.pageSize=e.pageSize),null!=e.readTime&&e.hasOwnProperty("readTime")&&(r.readTime=a.google.protobuf.Timestamp.toObject(e.readTime,t),t.oneofs)&&(r.consistencySelector="readTime"),r},d.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},d.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.v1.PartitionQueryRequest"},d),o.PartitionQueryResponse=(ce.prototype.partitions=i.emptyArray,ce.prototype.nextPageToken="",ce.fromObject=function(e){if(e instanceof a.google.firestore.v1.PartitionQueryResponse)return e;var t=new a.google.firestore.v1.PartitionQueryResponse;if(e.partitions){if(!Array.isArray(e.partitions))throw TypeError(".google.firestore.v1.PartitionQueryResponse.partitions: array expected");t.partitions=[];for(var o=0;o<e.partitions.length;++o){if("object"!=typeof e.partitions[o])throw TypeError(".google.firestore.v1.PartitionQueryResponse.partitions: object expected");t.partitions[o]=a.google.firestore.v1.Cursor.fromObject(e.partitions[o])}}return null!=e.nextPageToken&&(t.nextPageToken=String(e.nextPageToken)),t},ce.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.partitions=[]),t.defaults&&(o.nextPageToken=""),e.partitions&&e.partitions.length){o.partitions=[];for(var r=0;r<e.partitions.length;++r)o.partitions[r]=a.google.firestore.v1.Cursor.toObject(e.partitions[r],t)}return null!=e.nextPageToken&&e.hasOwnProperty("nextPageToken")&&(o.nextPageToken=e.nextPageToken),o},ce.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},ce.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.v1.PartitionQueryResponse"},ce),o.WriteRequest=(ge.prototype.database="",ge.prototype.streamId="",ge.prototype.writes=i.emptyArray,ge.prototype.streamToken=i.newBuffer([]),ge.prototype.labels=i.emptyObject,ge.fromObject=function(e){if(e instanceof a.google.firestore.v1.WriteRequest)return e;var t=new a.google.firestore.v1.WriteRequest;if(null!=e.database&&(t.database=String(e.database)),null!=e.streamId&&(t.streamId=String(e.streamId)),e.writes){if(!Array.isArray(e.writes))throw TypeError(".google.firestore.v1.WriteRequest.writes: array expected");t.writes=[];for(var o=0;o<e.writes.length;++o){if("object"!=typeof e.writes[o])throw TypeError(".google.firestore.v1.WriteRequest.writes: object expected");t.writes[o]=a.google.firestore.v1.Write.fromObject(e.writes[o])}}if(null!=e.streamToken&&("string"==typeof e.streamToken?i.base64.decode(e.streamToken,t.streamToken=i.newBuffer(i.base64.length(e.streamToken)),0):0<=e.streamToken.length&&(t.streamToken=e.streamToken)),e.labels){if("object"!=typeof e.labels)throw TypeError(".google.firestore.v1.WriteRequest.labels: object expected");t.labels={};for(var r=Object.keys(e.labels),o=0;o<r.length;++o)t.labels[r[o]]=String(e.labels[r[o]])}return t},ge.toObject=function(e,t){var o,r={};if(((t=t||{}).arrays||t.defaults)&&(r.writes=[]),(t.objects||t.defaults)&&(r.labels={}),t.defaults&&(r.database="",r.streamId="",t.bytes===String?r.streamToken="":(r.streamToken=[],t.bytes!==Array&&(r.streamToken=i.newBuffer(r.streamToken)))),null!=e.database&&e.hasOwnProperty("database")&&(r.database=e.database),null!=e.streamId&&e.hasOwnProperty("streamId")&&(r.streamId=e.streamId),e.writes&&e.writes.length){r.writes=[];for(var n=0;n<e.writes.length;++n)r.writes[n]=a.google.firestore.v1.Write.toObject(e.writes[n],t)}if(null!=e.streamToken&&e.hasOwnProperty("streamToken")&&(r.streamToken=t.bytes===String?i.base64.encode(e.streamToken,0,e.streamToken.length):t.bytes===Array?Array.prototype.slice.call(e.streamToken):e.streamToken),e.labels&&(o=Object.keys(e.labels)).length){r.labels={};for(n=0;n<o.length;++n)r.labels[o[n]]=e.labels[o[n]]}return r},ge.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},ge.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.v1.WriteRequest"},ge),o.WriteResponse=(fe.prototype.streamId="",fe.prototype.streamToken=i.newBuffer([]),fe.prototype.writeResults=i.emptyArray,fe.prototype.commitTime=null,fe.fromObject=function(e){if(e instanceof a.google.firestore.v1.WriteResponse)return e;var t=new a.google.firestore.v1.WriteResponse;if(null!=e.streamId&&(t.streamId=String(e.streamId)),null!=e.streamToken&&("string"==typeof e.streamToken?i.base64.decode(e.streamToken,t.streamToken=i.newBuffer(i.base64.length(e.streamToken)),0):0<=e.streamToken.length&&(t.streamToken=e.streamToken)),e.writeResults){if(!Array.isArray(e.writeResults))throw TypeError(".google.firestore.v1.WriteResponse.writeResults: array expected");t.writeResults=[];for(var o=0;o<e.writeResults.length;++o){if("object"!=typeof e.writeResults[o])throw TypeError(".google.firestore.v1.WriteResponse.writeResults: object expected");t.writeResults[o]=a.google.firestore.v1.WriteResult.fromObject(e.writeResults[o])}}if(null!=e.commitTime){if("object"!=typeof e.commitTime)throw TypeError(".google.firestore.v1.WriteResponse.commitTime: object expected");t.commitTime=a.google.protobuf.Timestamp.fromObject(e.commitTime)}return t},fe.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.writeResults=[]),t.defaults&&(o.streamId="",t.bytes===String?o.streamToken="":(o.streamToken=[],t.bytes!==Array&&(o.streamToken=i.newBuffer(o.streamToken))),o.commitTime=null),null!=e.streamId&&e.hasOwnProperty("streamId")&&(o.streamId=e.streamId),null!=e.streamToken&&e.hasOwnProperty("streamToken")&&(o.streamToken=t.bytes===String?i.base64.encode(e.streamToken,0,e.streamToken.length):t.bytes===Array?Array.prototype.slice.call(e.streamToken):e.streamToken),e.writeResults&&e.writeResults.length){o.writeResults=[];for(var r=0;r<e.writeResults.length;++r)o.writeResults[r]=a.google.firestore.v1.WriteResult.toObject(e.writeResults[r],t)}return null!=e.commitTime&&e.hasOwnProperty("commitTime")&&(o.commitTime=a.google.protobuf.Timestamp.toObject(e.commitTime,t)),o},fe.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},fe.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.v1.WriteResponse"},fe),o.ListenRequest=(de.prototype.database="",de.prototype.addTarget=null,de.prototype.removeTarget=null,de.prototype.labels=i.emptyObject,Object.defineProperty(de.prototype,"targetChange",{get:i.oneOfGetter(n=["addTarget","removeTarget"]),set:i.oneOfSetter(n)}),de.fromObject=function(e){if(e instanceof a.google.firestore.v1.ListenRequest)return e;var t=new a.google.firestore.v1.ListenRequest;if(null!=e.database&&(t.database=String(e.database)),null!=e.addTarget){if("object"!=typeof e.addTarget)throw TypeError(".google.firestore.v1.ListenRequest.addTarget: object expected");t.addTarget=a.google.firestore.v1.Target.fromObject(e.addTarget)}if(null!=e.removeTarget&&(t.removeTarget=0|e.removeTarget),e.labels){if("object"!=typeof e.labels)throw TypeError(".google.firestore.v1.ListenRequest.labels: object expected");t.labels={};for(var o=Object.keys(e.labels),r=0;r<o.length;++r)t.labels[o[r]]=String(e.labels[o[r]])}return t},de.toObject=function(e,t){var o,r={};if(((t=t||{}).objects||t.defaults)&&(r.labels={}),t.defaults&&(r.database=""),null!=e.database&&e.hasOwnProperty("database")&&(r.database=e.database),null!=e.addTarget&&e.hasOwnProperty("addTarget")&&(r.addTarget=a.google.firestore.v1.Target.toObject(e.addTarget,t),t.oneofs)&&(r.targetChange="addTarget"),null!=e.removeTarget&&e.hasOwnProperty("removeTarget")&&(r.removeTarget=e.removeTarget,t.oneofs)&&(r.targetChange="removeTarget"),e.labels&&(o=Object.keys(e.labels)).length){r.labels={};for(var n=0;n<o.length;++n)r.labels[o[n]]=e.labels[o[n]]}return r},de.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},de.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.v1.ListenRequest"},de),o.ListenResponse=(y.prototype.targetChange=null,y.prototype.documentChange=null,y.prototype.documentDelete=null,y.prototype.documentRemove=null,y.prototype.filter=null,Object.defineProperty(y.prototype,"responseType",{get:i.oneOfGetter(n=["targetChange","documentChange","documentDelete","documentRemove","filter"]),set:i.oneOfSetter(n)}),y.fromObject=function(e){if(e instanceof a.google.firestore.v1.ListenResponse)return e;var t=new a.google.firestore.v1.ListenResponse;if(null!=e.targetChange){if("object"!=typeof e.targetChange)throw TypeError(".google.firestore.v1.ListenResponse.targetChange: object expected");t.targetChange=a.google.firestore.v1.TargetChange.fromObject(e.targetChange)}if(null!=e.documentChange){if("object"!=typeof e.documentChange)throw TypeError(".google.firestore.v1.ListenResponse.documentChange: object expected");t.documentChange=a.google.firestore.v1.DocumentChange.fromObject(e.documentChange)}if(null!=e.documentDelete){if("object"!=typeof e.documentDelete)throw TypeError(".google.firestore.v1.ListenResponse.documentDelete: object expected");t.documentDelete=a.google.firestore.v1.DocumentDelete.fromObject(e.documentDelete)}if(null!=e.documentRemove){if("object"!=typeof e.documentRemove)throw TypeError(".google.firestore.v1.ListenResponse.documentRemove: object expected");t.documentRemove=a.google.firestore.v1.DocumentRemove.fromObject(e.documentRemove)}if(null!=e.filter){if("object"!=typeof e.filter)throw TypeError(".google.firestore.v1.ListenResponse.filter: object expected");t.filter=a.google.firestore.v1.ExistenceFilter.fromObject(e.filter)}return t},y.toObject=function(e,t){t=t||{};var o={};return null!=e.targetChange&&e.hasOwnProperty("targetChange")&&(o.targetChange=a.google.firestore.v1.TargetChange.toObject(e.targetChange,t),t.oneofs)&&(o.responseType="targetChange"),null!=e.documentChange&&e.hasOwnProperty("documentChange")&&(o.documentChange=a.google.firestore.v1.DocumentChange.toObject(e.documentChange,t),t.oneofs)&&(o.responseType="documentChange"),null!=e.documentDelete&&e.hasOwnProperty("documentDelete")&&(o.documentDelete=a.google.firestore.v1.DocumentDelete.toObject(e.documentDelete,t),t.oneofs)&&(o.responseType="documentDelete"),null!=e.filter&&e.hasOwnProperty("filter")&&(o.filter=a.google.firestore.v1.ExistenceFilter.toObject(e.filter,t),t.oneofs)&&(o.responseType="filter"),null!=e.documentRemove&&e.hasOwnProperty("documentRemove")&&(o.documentRemove=a.google.firestore.v1.DocumentRemove.toObject(e.documentRemove,t),t.oneofs)&&(o.responseType="documentRemove"),o},y.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},y.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.v1.ListenResponse"},y),o.Target=(m.prototype.query=null,m.prototype.documents=null,m.prototype.resumeToken=null,m.prototype.readTime=null,m.prototype.targetId=0,m.prototype.once=!1,m.prototype.expectedCount=null,Object.defineProperty(m.prototype,"targetType",{get:i.oneOfGetter(n=["query","documents"]),set:i.oneOfSetter(n)}),Object.defineProperty(m.prototype,"resumeType",{get:i.oneOfGetter(n=["resumeToken","readTime"]),set:i.oneOfSetter(n)}),m.fromObject=function(e){if(e instanceof a.google.firestore.v1.Target)return e;var t=new a.google.firestore.v1.Target;if(null!=e.query){if("object"!=typeof e.query)throw TypeError(".google.firestore.v1.Target.query: object expected");t.query=a.google.firestore.v1.Target.QueryTarget.fromObject(e.query)}if(null!=e.documents){if("object"!=typeof e.documents)throw TypeError(".google.firestore.v1.Target.documents: object expected");t.documents=a.google.firestore.v1.Target.DocumentsTarget.fromObject(e.documents)}if(null!=e.resumeToken&&("string"==typeof e.resumeToken?i.base64.decode(e.resumeToken,t.resumeToken=i.newBuffer(i.base64.length(e.resumeToken)),0):0<=e.resumeToken.length&&(t.resumeToken=e.resumeToken)),null!=e.readTime){if("object"!=typeof e.readTime)throw TypeError(".google.firestore.v1.Target.readTime: object expected");t.readTime=a.google.protobuf.Timestamp.fromObject(e.readTime)}if(null!=e.targetId&&(t.targetId=0|e.targetId),null!=e.once&&(t.once=Boolean(e.once)),null!=e.expectedCount){if("object"!=typeof e.expectedCount)throw TypeError(".google.firestore.v1.Target.expectedCount: object expected");t.expectedCount=a.google.protobuf.Int32Value.fromObject(e.expectedCount)}return t},m.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.targetId=0,o.once=!1,o.expectedCount=null),null!=e.query&&e.hasOwnProperty("query")&&(o.query=a.google.firestore.v1.Target.QueryTarget.toObject(e.query,t),t.oneofs)&&(o.targetType="query"),null!=e.documents&&e.hasOwnProperty("documents")&&(o.documents=a.google.firestore.v1.Target.DocumentsTarget.toObject(e.documents,t),t.oneofs)&&(o.targetType="documents"),null!=e.resumeToken&&e.hasOwnProperty("resumeToken")&&(o.resumeToken=t.bytes===String?i.base64.encode(e.resumeToken,0,e.resumeToken.length):t.bytes===Array?Array.prototype.slice.call(e.resumeToken):e.resumeToken,t.oneofs)&&(o.resumeType="resumeToken"),null!=e.targetId&&e.hasOwnProperty("targetId")&&(o.targetId=e.targetId),null!=e.once&&e.hasOwnProperty("once")&&(o.once=e.once),null!=e.readTime&&e.hasOwnProperty("readTime")&&(o.readTime=a.google.protobuf.Timestamp.toObject(e.readTime,t),t.oneofs)&&(o.resumeType="readTime"),null!=e.expectedCount&&e.hasOwnProperty("expectedCount")&&(o.expectedCount=a.google.protobuf.Int32Value.toObject(e.expectedCount,t)),o},m.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},m.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.v1.Target"},m.DocumentsTarget=(ye.prototype.documents=i.emptyArray,ye.fromObject=function(e){if(e instanceof a.google.firestore.v1.Target.DocumentsTarget)return e;var t=new a.google.firestore.v1.Target.DocumentsTarget;if(e.documents){if(!Array.isArray(e.documents))throw TypeError(".google.firestore.v1.Target.DocumentsTarget.documents: array expected");t.documents=[];for(var o=0;o<e.documents.length;++o)t.documents[o]=String(e.documents[o])}return t},ye.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.documents=[]),e.documents&&e.documents.length){o.documents=[];for(var r=0;r<e.documents.length;++r)o.documents[r]=e.documents[r]}return o},ye.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},ye.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.v1.Target.DocumentsTarget"},ye),m.QueryTarget=(me.prototype.parent="",me.prototype.structuredQuery=null,Object.defineProperty(me.prototype,"queryType",{get:i.oneOfGetter(n=["structuredQuery"]),set:i.oneOfSetter(n)}),me.fromObject=function(e){if(e instanceof a.google.firestore.v1.Target.QueryTarget)return e;var t=new a.google.firestore.v1.Target.QueryTarget;if(null!=e.parent&&(t.parent=String(e.parent)),null!=e.structuredQuery){if("object"!=typeof e.structuredQuery)throw TypeError(".google.firestore.v1.Target.QueryTarget.structuredQuery: object expected");t.structuredQuery=a.google.firestore.v1.StructuredQuery.fromObject(e.structuredQuery)}return t},me.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.parent=""),null!=e.parent&&e.hasOwnProperty("parent")&&(o.parent=e.parent),null!=e.structuredQuery&&e.hasOwnProperty("structuredQuery")&&(o.structuredQuery=a.google.firestore.v1.StructuredQuery.toObject(e.structuredQuery,t),t.oneofs)&&(o.queryType="structuredQuery"),o},me.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},me.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.v1.Target.QueryTarget"},me),m),o.TargetChange=(be.prototype.targetChangeType=0,be.prototype.targetIds=i.emptyArray,be.prototype.cause=null,be.prototype.resumeToken=i.newBuffer([]),be.prototype.readTime=null,be.fromObject=function(e){if(e instanceof a.google.firestore.v1.TargetChange)return e;var t=new a.google.firestore.v1.TargetChange;switch(e.targetChangeType){default:"number"==typeof e.targetChangeType&&(t.targetChangeType=e.targetChangeType);break;case"NO_CHANGE":case 0:t.targetChangeType=0;break;case"ADD":case 1:t.targetChangeType=1;break;case"REMOVE":case 2:t.targetChangeType=2;break;case"CURRENT":case 3:t.targetChangeType=3;break;case"RESET":case 4:t.targetChangeType=4}if(e.targetIds){if(!Array.isArray(e.targetIds))throw TypeError(".google.firestore.v1.TargetChange.targetIds: array expected");t.targetIds=[];for(var o=0;o<e.targetIds.length;++o)t.targetIds[o]=0|e.targetIds[o]}if(null!=e.cause){if("object"!=typeof e.cause)throw TypeError(".google.firestore.v1.TargetChange.cause: object expected");t.cause=a.google.rpc.Status.fromObject(e.cause)}if(null!=e.resumeToken&&("string"==typeof e.resumeToken?i.base64.decode(e.resumeToken,t.resumeToken=i.newBuffer(i.base64.length(e.resumeToken)),0):0<=e.resumeToken.length&&(t.resumeToken=e.resumeToken)),null!=e.readTime){if("object"!=typeof e.readTime)throw TypeError(".google.firestore.v1.TargetChange.readTime: object expected");t.readTime=a.google.protobuf.Timestamp.fromObject(e.readTime)}return t},be.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.targetIds=[]),t.defaults&&(o.targetChangeType=t.enums===String?"NO_CHANGE":0,o.cause=null,t.bytes===String?o.resumeToken="":(o.resumeToken=[],t.bytes!==Array&&(o.resumeToken=i.newBuffer(o.resumeToken))),o.readTime=null),null!=e.targetChangeType&&e.hasOwnProperty("targetChangeType")&&(o.targetChangeType=t.enums!==String||void 0===a.google.firestore.v1.TargetChange.TargetChangeType[e.targetChangeType]?e.targetChangeType:a.google.firestore.v1.TargetChange.TargetChangeType[e.targetChangeType]),e.targetIds&&e.targetIds.length){o.targetIds=[];for(var r=0;r<e.targetIds.length;++r)o.targetIds[r]=e.targetIds[r]}return null!=e.cause&&e.hasOwnProperty("cause")&&(o.cause=a.google.rpc.Status.toObject(e.cause,t)),null!=e.resumeToken&&e.hasOwnProperty("resumeToken")&&(o.resumeToken=t.bytes===String?i.base64.encode(e.resumeToken,0,e.resumeToken.length):t.bytes===Array?Array.prototype.slice.call(e.resumeToken):e.resumeToken),null!=e.readTime&&e.hasOwnProperty("readTime")&&(o.readTime=a.google.protobuf.Timestamp.toObject(e.readTime,t)),o},be.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},be.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.v1.TargetChange"},be.TargetChangeType=(n={},(e=Object.create(n))[n[0]="NO_CHANGE"]="NO_CHANGE",e[n[1]="ADD"]="ADD",e[n[2]="REMOVE"]="REMOVE",e[n[3]="CURRENT"]="CURRENT",e[n[4]="RESET"]="RESET",e),be),o.ListCollectionIdsRequest=(Oe.prototype.parent="",Oe.prototype.pageSize=0,Oe.prototype.pageToken="",Oe.prototype.readTime=null,Object.defineProperty(Oe.prototype,"consistencySelector",{get:i.oneOfGetter(n=["readTime"]),set:i.oneOfSetter(n)}),Oe.fromObject=function(e){if(e instanceof a.google.firestore.v1.ListCollectionIdsRequest)return e;var t=new a.google.firestore.v1.ListCollectionIdsRequest;if(null!=e.parent&&(t.parent=String(e.parent)),null!=e.pageSize&&(t.pageSize=0|e.pageSize),null!=e.pageToken&&(t.pageToken=String(e.pageToken)),null!=e.readTime){if("object"!=typeof e.readTime)throw TypeError(".google.firestore.v1.ListCollectionIdsRequest.readTime: object expected");t.readTime=a.google.protobuf.Timestamp.fromObject(e.readTime)}return t},Oe.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.parent="",o.pageSize=0,o.pageToken=""),null!=e.parent&&e.hasOwnProperty("parent")&&(o.parent=e.parent),null!=e.pageSize&&e.hasOwnProperty("pageSize")&&(o.pageSize=e.pageSize),null!=e.pageToken&&e.hasOwnProperty("pageToken")&&(o.pageToken=e.pageToken),null!=e.readTime&&e.hasOwnProperty("readTime")&&(o.readTime=a.google.protobuf.Timestamp.toObject(e.readTime,t),t.oneofs)&&(o.consistencySelector="readTime"),o},Oe.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},Oe.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.v1.ListCollectionIdsRequest"},Oe),o.ListCollectionIdsResponse=(he.prototype.collectionIds=i.emptyArray,he.prototype.nextPageToken="",he.fromObject=function(e){if(e instanceof a.google.firestore.v1.ListCollectionIdsResponse)return e;var t=new a.google.firestore.v1.ListCollectionIdsResponse;if(e.collectionIds){if(!Array.isArray(e.collectionIds))throw TypeError(".google.firestore.v1.ListCollectionIdsResponse.collectionIds: array expected");t.collectionIds=[];for(var o=0;o<e.collectionIds.length;++o)t.collectionIds[o]=String(e.collectionIds[o])}return null!=e.nextPageToken&&(t.nextPageToken=String(e.nextPageToken)),t},he.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.collectionIds=[]),t.defaults&&(o.nextPageToken=""),e.collectionIds&&e.collectionIds.length){o.collectionIds=[];for(var r=0;r<e.collectionIds.length;++r)o.collectionIds[r]=e.collectionIds[r]}return null!=e.nextPageToken&&e.hasOwnProperty("nextPageToken")&&(o.nextPageToken=e.nextPageToken),o},he.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},he.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.v1.ListCollectionIdsResponse"},he),o.BatchWriteRequest=(ve.prototype.database="",ve.prototype.writes=i.emptyArray,ve.prototype.labels=i.emptyObject,ve.fromObject=function(e){if(e instanceof a.google.firestore.v1.BatchWriteRequest)return e;var t=new a.google.firestore.v1.BatchWriteRequest;if(null!=e.database&&(t.database=String(e.database)),e.writes){if(!Array.isArray(e.writes))throw TypeError(".google.firestore.v1.BatchWriteRequest.writes: array expected");t.writes=[];for(var o=0;o<e.writes.length;++o){if("object"!=typeof e.writes[o])throw TypeError(".google.firestore.v1.BatchWriteRequest.writes: object expected");t.writes[o]=a.google.firestore.v1.Write.fromObject(e.writes[o])}}if(e.labels){if("object"!=typeof e.labels)throw TypeError(".google.firestore.v1.BatchWriteRequest.labels: object expected");t.labels={};for(var r=Object.keys(e.labels),o=0;o<r.length;++o)t.labels[r[o]]=String(e.labels[r[o]])}return t},ve.toObject=function(e,t){var o,r={};if(((t=t||{}).arrays||t.defaults)&&(r.writes=[]),(t.objects||t.defaults)&&(r.labels={}),t.defaults&&(r.database=""),null!=e.database&&e.hasOwnProperty("database")&&(r.database=e.database),e.writes&&e.writes.length){r.writes=[];for(var n=0;n<e.writes.length;++n)r.writes[n]=a.google.firestore.v1.Write.toObject(e.writes[n],t)}if(e.labels&&(o=Object.keys(e.labels)).length){r.labels={};for(n=0;n<o.length;++n)r.labels[o[n]]=e.labels[o[n]]}return r},ve.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},ve.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.v1.BatchWriteRequest"},ve),o.BatchWriteResponse=(Te.prototype.writeResults=i.emptyArray,Te.prototype.status=i.emptyArray,Te.fromObject=function(e){if(e instanceof a.google.firestore.v1.BatchWriteResponse)return e;var t=new a.google.firestore.v1.BatchWriteResponse;if(e.writeResults){if(!Array.isArray(e.writeResults))throw TypeError(".google.firestore.v1.BatchWriteResponse.writeResults: array expected");t.writeResults=[];for(var o=0;o<e.writeResults.length;++o){if("object"!=typeof e.writeResults[o])throw TypeError(".google.firestore.v1.BatchWriteResponse.writeResults: object expected");t.writeResults[o]=a.google.firestore.v1.WriteResult.fromObject(e.writeResults[o])}}if(e.status){if(!Array.isArray(e.status))throw TypeError(".google.firestore.v1.BatchWriteResponse.status: array expected");t.status=[];for(o=0;o<e.status.length;++o){if("object"!=typeof e.status[o])throw TypeError(".google.firestore.v1.BatchWriteResponse.status: object expected");t.status[o]=a.google.rpc.Status.fromObject(e.status[o])}}return t},Te.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.writeResults=[],o.status=[]),e.writeResults&&e.writeResults.length){o.writeResults=[];for(var r=0;r<e.writeResults.length;++r)o.writeResults[r]=a.google.firestore.v1.WriteResult.toObject(e.writeResults[r],t)}if(e.status&&e.status.length){o.status=[];for(r=0;r<e.status.length;++r)o.status[r]=a.google.rpc.Status.toObject(e.status[r],t)}return o},Te.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},Te.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.v1.BatchWriteResponse"},Te),o.StructuredQuery=(b.prototype.select=null,b.prototype.from=i.emptyArray,b.prototype.where=null,b.prototype.orderBy=i.emptyArray,b.prototype.startAt=null,b.prototype.endAt=null,b.prototype.offset=0,b.prototype.limit=null,b.prototype.findNearest=null,b.fromObject=function(e){if(e instanceof a.google.firestore.v1.StructuredQuery)return e;var t=new a.google.firestore.v1.StructuredQuery;if(null!=e.select){if("object"!=typeof e.select)throw TypeError(".google.firestore.v1.StructuredQuery.select: object expected");t.select=a.google.firestore.v1.StructuredQuery.Projection.fromObject(e.select)}if(e.from){if(!Array.isArray(e.from))throw TypeError(".google.firestore.v1.StructuredQuery.from: array expected");t.from=[];for(var o=0;o<e.from.length;++o){if("object"!=typeof e.from[o])throw TypeError(".google.firestore.v1.StructuredQuery.from: object expected");t.from[o]=a.google.firestore.v1.StructuredQuery.CollectionSelector.fromObject(e.from[o])}}if(null!=e.where){if("object"!=typeof e.where)throw TypeError(".google.firestore.v1.StructuredQuery.where: object expected");t.where=a.google.firestore.v1.StructuredQuery.Filter.fromObject(e.where)}if(e.orderBy){if(!Array.isArray(e.orderBy))throw TypeError(".google.firestore.v1.StructuredQuery.orderBy: array expected");t.orderBy=[];for(o=0;o<e.orderBy.length;++o){if("object"!=typeof e.orderBy[o])throw TypeError(".google.firestore.v1.StructuredQuery.orderBy: object expected");t.orderBy[o]=a.google.firestore.v1.StructuredQuery.Order.fromObject(e.orderBy[o])}}if(null!=e.startAt){if("object"!=typeof e.startAt)throw TypeError(".google.firestore.v1.StructuredQuery.startAt: object expected");t.startAt=a.google.firestore.v1.Cursor.fromObject(e.startAt)}if(null!=e.endAt){if("object"!=typeof e.endAt)throw TypeError(".google.firestore.v1.StructuredQuery.endAt: object expected");t.endAt=a.google.firestore.v1.Cursor.fromObject(e.endAt)}if(null!=e.offset&&(t.offset=0|e.offset),null!=e.limit){if("object"!=typeof e.limit)throw TypeError(".google.firestore.v1.StructuredQuery.limit: object expected");t.limit=a.google.protobuf.Int32Value.fromObject(e.limit)}if(null!=e.findNearest){if("object"!=typeof e.findNearest)throw TypeError(".google.firestore.v1.StructuredQuery.findNearest: object expected");t.findNearest=a.google.firestore.v1.StructuredQuery.FindNearest.fromObject(e.findNearest)}return t},b.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.from=[],o.orderBy=[]),t.defaults&&(o.select=null,o.where=null,o.limit=null,o.offset=0,o.startAt=null,o.endAt=null,o.findNearest=null),null!=e.select&&e.hasOwnProperty("select")&&(o.select=a.google.firestore.v1.StructuredQuery.Projection.toObject(e.select,t)),e.from&&e.from.length){o.from=[];for(var r=0;r<e.from.length;++r)o.from[r]=a.google.firestore.v1.StructuredQuery.CollectionSelector.toObject(e.from[r],t)}if(null!=e.where&&e.hasOwnProperty("where")&&(o.where=a.google.firestore.v1.StructuredQuery.Filter.toObject(e.where,t)),e.orderBy&&e.orderBy.length){o.orderBy=[];for(r=0;r<e.orderBy.length;++r)o.orderBy[r]=a.google.firestore.v1.StructuredQuery.Order.toObject(e.orderBy[r],t)}return null!=e.limit&&e.hasOwnProperty("limit")&&(o.limit=a.google.protobuf.Int32Value.toObject(e.limit,t)),null!=e.offset&&e.hasOwnProperty("offset")&&(o.offset=e.offset),null!=e.startAt&&e.hasOwnProperty("startAt")&&(o.startAt=a.google.firestore.v1.Cursor.toObject(e.startAt,t)),null!=e.endAt&&e.hasOwnProperty("endAt")&&(o.endAt=a.google.firestore.v1.Cursor.toObject(e.endAt,t)),null!=e.findNearest&&e.hasOwnProperty("findNearest")&&(o.findNearest=a.google.firestore.v1.StructuredQuery.FindNearest.toObject(e.findNearest,t)),o},b.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},b.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.v1.StructuredQuery"},b.CollectionSelector=(Se.prototype.collectionId="",Se.prototype.allDescendants=!1,Se.fromObject=function(e){var t;return e instanceof a.google.firestore.v1.StructuredQuery.CollectionSelector?e:(t=new a.google.firestore.v1.StructuredQuery.CollectionSelector,null!=e.collectionId&&(t.collectionId=String(e.collectionId)),null!=e.allDescendants&&(t.allDescendants=Boolean(e.allDescendants)),t)},Se.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.collectionId="",o.allDescendants=!1),null!=e.collectionId&&e.hasOwnProperty("collectionId")&&(o.collectionId=e.collectionId),null!=e.allDescendants&&e.hasOwnProperty("allDescendants")&&(o.allDescendants=e.allDescendants),o},Se.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},Se.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.v1.StructuredQuery.CollectionSelector"},Se),b.Filter=(je.prototype.compositeFilter=null,je.prototype.fieldFilter=null,je.prototype.unaryFilter=null,Object.defineProperty(je.prototype,"filterType",{get:i.oneOfGetter(e=["compositeFilter","fieldFilter","unaryFilter"]),set:i.oneOfSetter(e)}),je.fromObject=function(e){if(e instanceof a.google.firestore.v1.StructuredQuery.Filter)return e;var t=new a.google.firestore.v1.StructuredQuery.Filter;if(null!=e.compositeFilter){if("object"!=typeof e.compositeFilter)throw TypeError(".google.firestore.v1.StructuredQuery.Filter.compositeFilter: object expected");t.compositeFilter=a.google.firestore.v1.StructuredQuery.CompositeFilter.fromObject(e.compositeFilter)}if(null!=e.fieldFilter){if("object"!=typeof e.fieldFilter)throw TypeError(".google.firestore.v1.StructuredQuery.Filter.fieldFilter: object expected");t.fieldFilter=a.google.firestore.v1.StructuredQuery.FieldFilter.fromObject(e.fieldFilter)}if(null!=e.unaryFilter){if("object"!=typeof e.unaryFilter)throw TypeError(".google.firestore.v1.StructuredQuery.Filter.unaryFilter: object expected");t.unaryFilter=a.google.firestore.v1.StructuredQuery.UnaryFilter.fromObject(e.unaryFilter)}return t},je.toObject=function(e,t){t=t||{};var o={};return null!=e.compositeFilter&&e.hasOwnProperty("compositeFilter")&&(o.compositeFilter=a.google.firestore.v1.StructuredQuery.CompositeFilter.toObject(e.compositeFilter,t),t.oneofs)&&(o.filterType="compositeFilter"),null!=e.fieldFilter&&e.hasOwnProperty("fieldFilter")&&(o.fieldFilter=a.google.firestore.v1.StructuredQuery.FieldFilter.toObject(e.fieldFilter,t),t.oneofs)&&(o.filterType="fieldFilter"),null!=e.unaryFilter&&e.hasOwnProperty("unaryFilter")&&(o.unaryFilter=a.google.firestore.v1.StructuredQuery.UnaryFilter.toObject(e.unaryFilter,t),t.oneofs)&&(o.filterType="unaryFilter"),o},je.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},je.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.v1.StructuredQuery.Filter"},je),b.CompositeFilter=(Ee.prototype.op=0,Ee.prototype.filters=i.emptyArray,Ee.fromObject=function(e){if(e instanceof a.google.firestore.v1.StructuredQuery.CompositeFilter)return e;var t=new a.google.firestore.v1.StructuredQuery.CompositeFilter;switch(e.op){default:"number"==typeof e.op&&(t.op=e.op);break;case"OPERATOR_UNSPECIFIED":case 0:t.op=0;break;case"AND":case 1:t.op=1;break;case"OR":case 2:t.op=2}if(e.filters){if(!Array.isArray(e.filters))throw TypeError(".google.firestore.v1.StructuredQuery.CompositeFilter.filters: array expected");t.filters=[];for(var o=0;o<e.filters.length;++o){if("object"!=typeof e.filters[o])throw TypeError(".google.firestore.v1.StructuredQuery.CompositeFilter.filters: object expected");t.filters[o]=a.google.firestore.v1.StructuredQuery.Filter.fromObject(e.filters[o])}}return t},Ee.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.filters=[]),t.defaults&&(o.op=t.enums===String?"OPERATOR_UNSPECIFIED":0),null!=e.op&&e.hasOwnProperty("op")&&(o.op=t.enums!==String||void 0===a.google.firestore.v1.StructuredQuery.CompositeFilter.Operator[e.op]?e.op:a.google.firestore.v1.StructuredQuery.CompositeFilter.Operator[e.op]),e.filters&&e.filters.length){o.filters=[];for(var r=0;r<e.filters.length;++r)o.filters[r]=a.google.firestore.v1.StructuredQuery.Filter.toObject(e.filters[r],t)}return o},Ee.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},Ee.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.v1.StructuredQuery.CompositeFilter"},Ee.Operator=(e={},(n=Object.create(e))[e[0]="OPERATOR_UNSPECIFIED"]="OPERATOR_UNSPECIFIED",n[e[1]="AND"]="AND",n[e[2]="OR"]="OR",n),Ee),b.FieldFilter=(we.prototype.field=null,we.prototype.op=0,we.prototype.value=null,we.fromObject=function(e){if(e instanceof a.google.firestore.v1.StructuredQuery.FieldFilter)return e;var t=new a.google.firestore.v1.StructuredQuery.FieldFilter;if(null!=e.field){if("object"!=typeof e.field)throw TypeError(".google.firestore.v1.StructuredQuery.FieldFilter.field: object expected");t.field=a.google.firestore.v1.StructuredQuery.FieldReference.fromObject(e.field)}switch(e.op){default:"number"==typeof e.op&&(t.op=e.op);break;case"OPERATOR_UNSPECIFIED":case 0:t.op=0;break;case"LESS_THAN":case 1:t.op=1;break;case"LESS_THAN_OR_EQUAL":case 2:t.op=2;break;case"GREATER_THAN":case 3:t.op=3;break;case"GREATER_THAN_OR_EQUAL":case 4:t.op=4;break;case"EQUAL":case 5:t.op=5;break;case"NOT_EQUAL":case 6:t.op=6;break;case"ARRAY_CONTAINS":case 7:t.op=7;break;case"IN":case 8:t.op=8;break;case"ARRAY_CONTAINS_ANY":case 9:t.op=9;break;case"NOT_IN":case 10:t.op=10}if(null!=e.value){if("object"!=typeof e.value)throw TypeError(".google.firestore.v1.StructuredQuery.FieldFilter.value: object expected");t.value=a.google.firestore.v1.Value.fromObject(e.value)}return t},we.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.field=null,o.op=t.enums===String?"OPERATOR_UNSPECIFIED":0,o.value=null),null!=e.field&&e.hasOwnProperty("field")&&(o.field=a.google.firestore.v1.StructuredQuery.FieldReference.toObject(e.field,t)),null!=e.op&&e.hasOwnProperty("op")&&(o.op=t.enums!==String||void 0===a.google.firestore.v1.StructuredQuery.FieldFilter.Operator[e.op]?e.op:a.google.firestore.v1.StructuredQuery.FieldFilter.Operator[e.op]),null!=e.value&&e.hasOwnProperty("value")&&(o.value=a.google.firestore.v1.Value.toObject(e.value,t)),o},we.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},we.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.v1.StructuredQuery.FieldFilter"},we.Operator=(e={},(n=Object.create(e))[e[0]="OPERATOR_UNSPECIFIED"]="OPERATOR_UNSPECIFIED",n[e[1]="LESS_THAN"]="LESS_THAN",n[e[2]="LESS_THAN_OR_EQUAL"]="LESS_THAN_OR_EQUAL",n[e[3]="GREATER_THAN"]="GREATER_THAN",n[e[4]="GREATER_THAN_OR_EQUAL"]="GREATER_THAN_OR_EQUAL",n[e[5]="EQUAL"]="EQUAL",n[e[6]="NOT_EQUAL"]="NOT_EQUAL",n[e[7]="ARRAY_CONTAINS"]="ARRAY_CONTAINS",n[e[8]="IN"]="IN",n[e[9]="ARRAY_CONTAINS_ANY"]="ARRAY_CONTAINS_ANY",n[e[10]="NOT_IN"]="NOT_IN",n),we),b.UnaryFilter=(Ne.prototype.op=0,Ne.prototype.field=null,Object.defineProperty(Ne.prototype,"operandType",{get:i.oneOfGetter(e=["field"]),set:i.oneOfSetter(e)}),Ne.fromObject=function(e){if(e instanceof a.google.firestore.v1.StructuredQuery.UnaryFilter)return e;var t=new a.google.firestore.v1.StructuredQuery.UnaryFilter;switch(e.op){default:"number"==typeof e.op&&(t.op=e.op);break;case"OPERATOR_UNSPECIFIED":case 0:t.op=0;break;case"IS_NAN":case 2:t.op=2;break;case"IS_NULL":case 3:t.op=3;break;case"IS_NOT_NAN":case 4:t.op=4;break;case"IS_NOT_NULL":case 5:t.op=5}if(null!=e.field){if("object"!=typeof e.field)throw TypeError(".google.firestore.v1.StructuredQuery.UnaryFilter.field: object expected");t.field=a.google.firestore.v1.StructuredQuery.FieldReference.fromObject(e.field)}return t},Ne.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.op=t.enums===String?"OPERATOR_UNSPECIFIED":0),null!=e.op&&e.hasOwnProperty("op")&&(o.op=t.enums!==String||void 0===a.google.firestore.v1.StructuredQuery.UnaryFilter.Operator[e.op]?e.op:a.google.firestore.v1.StructuredQuery.UnaryFilter.Operator[e.op]),null!=e.field&&e.hasOwnProperty("field")&&(o.field=a.google.firestore.v1.StructuredQuery.FieldReference.toObject(e.field,t),t.oneofs)&&(o.operandType="field"),o},Ne.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},Ne.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.v1.StructuredQuery.UnaryFilter"},Ne.Operator=(e={},(n=Object.create(e))[e[0]="OPERATOR_UNSPECIFIED"]="OPERATOR_UNSPECIFIED",n[e[2]="IS_NAN"]="IS_NAN",n[e[3]="IS_NULL"]="IS_NULL",n[e[4]="IS_NOT_NAN"]="IS_NOT_NAN",n[e[5]="IS_NOT_NULL"]="IS_NOT_NULL",n),Ne),b.Order=(Pe.prototype.field=null,Pe.prototype.direction=0,Pe.fromObject=function(e){if(e instanceof a.google.firestore.v1.StructuredQuery.Order)return e;var t=new a.google.firestore.v1.StructuredQuery.Order;if(null!=e.field){if("object"!=typeof e.field)throw TypeError(".google.firestore.v1.StructuredQuery.Order.field: object expected");t.field=a.google.firestore.v1.StructuredQuery.FieldReference.fromObject(e.field)}switch(e.direction){default:"number"==typeof e.direction&&(t.direction=e.direction);break;case"DIRECTION_UNSPECIFIED":case 0:t.direction=0;break;case"ASCENDING":case 1:t.direction=1;break;case"DESCENDING":case 2:t.direction=2}return t},Pe.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.field=null,o.direction=t.enums===String?"DIRECTION_UNSPECIFIED":0),null!=e.field&&e.hasOwnProperty("field")&&(o.field=a.google.firestore.v1.StructuredQuery.FieldReference.toObject(e.field,t)),null!=e.direction&&e.hasOwnProperty("direction")&&(o.direction=t.enums!==String||void 0===a.google.firestore.v1.StructuredQuery.Direction[e.direction]?e.direction:a.google.firestore.v1.StructuredQuery.Direction[e.direction]),o},Pe.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},Pe.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.v1.StructuredQuery.Order"},Pe),b.Direction=(e={},(n=Object.create(e))[e[0]="DIRECTION_UNSPECIFIED"]="DIRECTION_UNSPECIFIED",n[e[1]="ASCENDING"]="ASCENDING",n[e[2]="DESCENDING"]="DESCENDING",n),b.FieldReference=(Re.prototype.fieldPath="",Re.fromObject=function(e){var t;return e instanceof a.google.firestore.v1.StructuredQuery.FieldReference?e:(t=new a.google.firestore.v1.StructuredQuery.FieldReference,null!=e.fieldPath&&(t.fieldPath=String(e.fieldPath)),t)},Re.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.fieldPath=""),null!=e.fieldPath&&e.hasOwnProperty("fieldPath")&&(o.fieldPath=e.fieldPath),o},Re.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},Re.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.v1.StructuredQuery.FieldReference"},Re),b.Projection=(De.prototype.fields=i.emptyArray,De.fromObject=function(e){if(e instanceof a.google.firestore.v1.StructuredQuery.Projection)return e;var t=new a.google.firestore.v1.StructuredQuery.Projection;if(e.fields){if(!Array.isArray(e.fields))throw TypeError(".google.firestore.v1.StructuredQuery.Projection.fields: array expected");t.fields=[];for(var o=0;o<e.fields.length;++o){if("object"!=typeof e.fields[o])throw TypeError(".google.firestore.v1.StructuredQuery.Projection.fields: object expected");t.fields[o]=a.google.firestore.v1.StructuredQuery.FieldReference.fromObject(e.fields[o])}}return t},De.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.fields=[]),e.fields&&e.fields.length){o.fields=[];for(var r=0;r<e.fields.length;++r)o.fields[r]=a.google.firestore.v1.StructuredQuery.FieldReference.toObject(e.fields[r],t)}return o},De.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},De.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.v1.StructuredQuery.Projection"},De),b.FindNearest=(O.prototype.vectorField=null,O.prototype.queryVector=null,O.prototype.distanceMeasure=0,O.prototype.limit=null,O.prototype.distanceResultField="",O.prototype.distanceThreshold=null,O.fromObject=function(e){if(e instanceof a.google.firestore.v1.StructuredQuery.FindNearest)return e;var t=new a.google.firestore.v1.StructuredQuery.FindNearest;if(null!=e.vectorField){if("object"!=typeof e.vectorField)throw TypeError(".google.firestore.v1.StructuredQuery.FindNearest.vectorField: object expected");t.vectorField=a.google.firestore.v1.StructuredQuery.FieldReference.fromObject(e.vectorField)}if(null!=e.queryVector){if("object"!=typeof e.queryVector)throw TypeError(".google.firestore.v1.StructuredQuery.FindNearest.queryVector: object expected");t.queryVector=a.google.firestore.v1.Value.fromObject(e.queryVector)}switch(e.distanceMeasure){default:"number"==typeof e.distanceMeasure&&(t.distanceMeasure=e.distanceMeasure);break;case"DISTANCE_MEASURE_UNSPECIFIED":case 0:t.distanceMeasure=0;break;case"EUCLIDEAN":case 1:t.distanceMeasure=1;break;case"COSINE":case 2:t.distanceMeasure=2;break;case"DOT_PRODUCT":case 3:t.distanceMeasure=3}if(null!=e.limit){if("object"!=typeof e.limit)throw TypeError(".google.firestore.v1.StructuredQuery.FindNearest.limit: object expected");t.limit=a.google.protobuf.Int32Value.fromObject(e.limit)}if(null!=e.distanceResultField&&(t.distanceResultField=String(e.distanceResultField)),null!=e.distanceThreshold){if("object"!=typeof e.distanceThreshold)throw TypeError(".google.firestore.v1.StructuredQuery.FindNearest.distanceThreshold: object expected");t.distanceThreshold=a.google.protobuf.DoubleValue.fromObject(e.distanceThreshold)}return t},O.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.vectorField=null,o.queryVector=null,o.distanceMeasure=t.enums===String?"DISTANCE_MEASURE_UNSPECIFIED":0,o.limit=null,o.distanceResultField="",o.distanceThreshold=null),null!=e.vectorField&&e.hasOwnProperty("vectorField")&&(o.vectorField=a.google.firestore.v1.StructuredQuery.FieldReference.toObject(e.vectorField,t)),null!=e.queryVector&&e.hasOwnProperty("queryVector")&&(o.queryVector=a.google.firestore.v1.Value.toObject(e.queryVector,t)),null!=e.distanceMeasure&&e.hasOwnProperty("distanceMeasure")&&(o.distanceMeasure=t.enums!==String||void 0===a.google.firestore.v1.StructuredQuery.FindNearest.DistanceMeasure[e.distanceMeasure]?e.distanceMeasure:a.google.firestore.v1.StructuredQuery.FindNearest.DistanceMeasure[e.distanceMeasure]),null!=e.limit&&e.hasOwnProperty("limit")&&(o.limit=a.google.protobuf.Int32Value.toObject(e.limit,t)),null!=e.distanceResultField&&e.hasOwnProperty("distanceResultField")&&(o.distanceResultField=e.distanceResultField),null!=e.distanceThreshold&&e.hasOwnProperty("distanceThreshold")&&(o.distanceThreshold=a.google.protobuf.DoubleValue.toObject(e.distanceThreshold,t)),o},O.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},O.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.v1.StructuredQuery.FindNearest"},O.DistanceMeasure=(e={},(n=Object.create(e))[e[0]="DISTANCE_MEASURE_UNSPECIFIED"]="DISTANCE_MEASURE_UNSPECIFIED",n[e[1]="EUCLIDEAN"]="EUCLIDEAN",n[e[2]="COSINE"]="COSINE",n[e[3]="DOT_PRODUCT"]="DOT_PRODUCT",n),O),b),o.StructuredAggregationQuery=(Ie.prototype.structuredQuery=null,Ie.prototype.aggregations=i.emptyArray,Object.defineProperty(Ie.prototype,"queryType",{get:i.oneOfGetter(e=["structuredQuery"]),set:i.oneOfSetter(e)}),Ie.fromObject=function(e){if(e instanceof a.google.firestore.v1.StructuredAggregationQuery)return e;var t=new a.google.firestore.v1.StructuredAggregationQuery;if(null!=e.structuredQuery){if("object"!=typeof e.structuredQuery)throw TypeError(".google.firestore.v1.StructuredAggregationQuery.structuredQuery: object expected");t.structuredQuery=a.google.firestore.v1.StructuredQuery.fromObject(e.structuredQuery)}if(e.aggregations){if(!Array.isArray(e.aggregations))throw TypeError(".google.firestore.v1.StructuredAggregationQuery.aggregations: array expected");t.aggregations=[];for(var o=0;o<e.aggregations.length;++o){if("object"!=typeof e.aggregations[o])throw TypeError(".google.firestore.v1.StructuredAggregationQuery.aggregations: object expected");t.aggregations[o]=a.google.firestore.v1.StructuredAggregationQuery.Aggregation.fromObject(e.aggregations[o])}}return t},Ie.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.aggregations=[]),null!=e.structuredQuery&&e.hasOwnProperty("structuredQuery")&&(o.structuredQuery=a.google.firestore.v1.StructuredQuery.toObject(e.structuredQuery,t),t.oneofs)&&(o.queryType="structuredQuery"),e.aggregations&&e.aggregations.length){o.aggregations=[];for(var r=0;r<e.aggregations.length;++r)o.aggregations[r]=a.google.firestore.v1.StructuredAggregationQuery.Aggregation.toObject(e.aggregations[r],t)}return o},Ie.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},Ie.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.v1.StructuredAggregationQuery"},Ie.Aggregation=(h.prototype.count=null,h.prototype.sum=null,h.prototype.avg=null,h.prototype.alias="",Object.defineProperty(h.prototype,"operator",{get:i.oneOfGetter(e=["count","sum","avg"]),set:i.oneOfSetter(e)}),h.fromObject=function(e){if(e instanceof a.google.firestore.v1.StructuredAggregationQuery.Aggregation)return e;var t=new a.google.firestore.v1.StructuredAggregationQuery.Aggregation;if(null!=e.count){if("object"!=typeof e.count)throw TypeError(".google.firestore.v1.StructuredAggregationQuery.Aggregation.count: object expected");t.count=a.google.firestore.v1.StructuredAggregationQuery.Aggregation.Count.fromObject(e.count)}if(null!=e.sum){if("object"!=typeof e.sum)throw TypeError(".google.firestore.v1.StructuredAggregationQuery.Aggregation.sum: object expected");t.sum=a.google.firestore.v1.StructuredAggregationQuery.Aggregation.Sum.fromObject(e.sum)}if(null!=e.avg){if("object"!=typeof e.avg)throw TypeError(".google.firestore.v1.StructuredAggregationQuery.Aggregation.avg: object expected");t.avg=a.google.firestore.v1.StructuredAggregationQuery.Aggregation.Avg.fromObject(e.avg)}return null!=e.alias&&(t.alias=String(e.alias)),t},h.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.alias=""),null!=e.count&&e.hasOwnProperty("count")&&(o.count=a.google.firestore.v1.StructuredAggregationQuery.Aggregation.Count.toObject(e.count,t),t.oneofs)&&(o.operator="count"),null!=e.sum&&e.hasOwnProperty("sum")&&(o.sum=a.google.firestore.v1.StructuredAggregationQuery.Aggregation.Sum.toObject(e.sum,t),t.oneofs)&&(o.operator="sum"),null!=e.avg&&e.hasOwnProperty("avg")&&(o.avg=a.google.firestore.v1.StructuredAggregationQuery.Aggregation.Avg.toObject(e.avg,t),t.oneofs)&&(o.operator="avg"),null!=e.alias&&e.hasOwnProperty("alias")&&(o.alias=e.alias),o},h.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},h.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.v1.StructuredAggregationQuery.Aggregation"},h.Count=(Ae.prototype.upTo=null,Ae.fromObject=function(e){if(e instanceof a.google.firestore.v1.StructuredAggregationQuery.Aggregation.Count)return e;var t=new a.google.firestore.v1.StructuredAggregationQuery.Aggregation.Count;if(null!=e.upTo){if("object"!=typeof e.upTo)throw TypeError(".google.firestore.v1.StructuredAggregationQuery.Aggregation.Count.upTo: object expected");t.upTo=a.google.protobuf.Int64Value.fromObject(e.upTo)}return t},Ae.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.upTo=null),null!=e.upTo&&e.hasOwnProperty("upTo")&&(o.upTo=a.google.protobuf.Int64Value.toObject(e.upTo,t)),o},Ae.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},Ae.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.v1.StructuredAggregationQuery.Aggregation.Count"},Ae),h.Sum=(ke.prototype.field=null,ke.fromObject=function(e){if(e instanceof a.google.firestore.v1.StructuredAggregationQuery.Aggregation.Sum)return e;var t=new a.google.firestore.v1.StructuredAggregationQuery.Aggregation.Sum;if(null!=e.field){if("object"!=typeof e.field)throw TypeError(".google.firestore.v1.StructuredAggregationQuery.Aggregation.Sum.field: object expected");t.field=a.google.firestore.v1.StructuredQuery.FieldReference.fromObject(e.field)}return t},ke.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.field=null),null!=e.field&&e.hasOwnProperty("field")&&(o.field=a.google.firestore.v1.StructuredQuery.FieldReference.toObject(e.field,t)),o},ke.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},ke.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.v1.StructuredAggregationQuery.Aggregation.Sum"},ke),h.Avg=(xe.prototype.field=null,xe.fromObject=function(e){if(e instanceof a.google.firestore.v1.StructuredAggregationQuery.Aggregation.Avg)return e;var t=new a.google.firestore.v1.StructuredAggregationQuery.Aggregation.Avg;if(null!=e.field){if("object"!=typeof e.field)throw TypeError(".google.firestore.v1.StructuredAggregationQuery.Aggregation.Avg.field: object expected");t.field=a.google.firestore.v1.StructuredQuery.FieldReference.fromObject(e.field)}return t},xe.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.field=null),null!=e.field&&e.hasOwnProperty("field")&&(o.field=a.google.firestore.v1.StructuredQuery.FieldReference.toObject(e.field,t)),o},xe.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},xe.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.v1.StructuredAggregationQuery.Aggregation.Avg"},xe),h),Ie),o.Cursor=(Fe.prototype.values=i.emptyArray,Fe.prototype.before=!1,Fe.fromObject=function(e){if(e instanceof a.google.firestore.v1.Cursor)return e;var t=new a.google.firestore.v1.Cursor;if(e.values){if(!Array.isArray(e.values))throw TypeError(".google.firestore.v1.Cursor.values: array expected");t.values=[];for(var o=0;o<e.values.length;++o){if("object"!=typeof e.values[o])throw TypeError(".google.firestore.v1.Cursor.values: object expected");t.values[o]=a.google.firestore.v1.Value.fromObject(e.values[o])}}return null!=e.before&&(t.before=Boolean(e.before)),t},Fe.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.values=[]),t.defaults&&(o.before=!1),e.values&&e.values.length){o.values=[];for(var r=0;r<e.values.length;++r)o.values[r]=a.google.firestore.v1.Value.toObject(e.values[r],t)}return null!=e.before&&e.hasOwnProperty("before")&&(o.before=e.before),o},Fe.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},Fe.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.v1.Cursor"},Fe),o.ExplainOptions=(_e.prototype.analyze=!1,_e.fromObject=function(e){var t;return e instanceof a.google.firestore.v1.ExplainOptions?e:(t=new a.google.firestore.v1.ExplainOptions,null!=e.analyze&&(t.analyze=Boolean(e.analyze)),t)},_e.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.analyze=!1),null!=e.analyze&&e.hasOwnProperty("analyze")&&(o.analyze=e.analyze),o},_e.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},_e.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.v1.ExplainOptions"},_e),o.ExplainMetrics=(Ce.prototype.planSummary=null,Ce.prototype.executionStats=null,Ce.fromObject=function(e){if(e instanceof a.google.firestore.v1.ExplainMetrics)return e;var t=new a.google.firestore.v1.ExplainMetrics;if(null!=e.planSummary){if("object"!=typeof e.planSummary)throw TypeError(".google.firestore.v1.ExplainMetrics.planSummary: object expected");t.planSummary=a.google.firestore.v1.PlanSummary.fromObject(e.planSummary)}if(null!=e.executionStats){if("object"!=typeof e.executionStats)throw TypeError(".google.firestore.v1.ExplainMetrics.executionStats: object expected");t.executionStats=a.google.firestore.v1.ExecutionStats.fromObject(e.executionStats)}return t},Ce.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.planSummary=null,o.executionStats=null),null!=e.planSummary&&e.hasOwnProperty("planSummary")&&(o.planSummary=a.google.firestore.v1.PlanSummary.toObject(e.planSummary,t)),null!=e.executionStats&&e.hasOwnProperty("executionStats")&&(o.executionStats=a.google.firestore.v1.ExecutionStats.toObject(e.executionStats,t)),o},Ce.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},Ce.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.v1.ExplainMetrics"},Ce),o.PlanSummary=(Ve.prototype.indexesUsed=i.emptyArray,Ve.fromObject=function(e){if(e instanceof a.google.firestore.v1.PlanSummary)return e;var t=new a.google.firestore.v1.PlanSummary;if(e.indexesUsed){if(!Array.isArray(e.indexesUsed))throw TypeError(".google.firestore.v1.PlanSummary.indexesUsed: array expected");t.indexesUsed=[];for(var o=0;o<e.indexesUsed.length;++o){if("object"!=typeof e.indexesUsed[o])throw TypeError(".google.firestore.v1.PlanSummary.indexesUsed: object expected");t.indexesUsed[o]=a.google.protobuf.Struct.fromObject(e.indexesUsed[o])}}return t},Ve.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.indexesUsed=[]),e.indexesUsed&&e.indexesUsed.length){o.indexesUsed=[];for(var r=0;r<e.indexesUsed.length;++r)o.indexesUsed[r]=a.google.protobuf.Struct.toObject(e.indexesUsed[r],t)}return o},Ve.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},Ve.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.v1.PlanSummary"},Ve),o.ExecutionStats=(Le.prototype.resultsReturned=i.Long?i.Long.fromBits(0,0,!1):0,Le.prototype.executionDuration=null,Le.prototype.readOperations=i.Long?i.Long.fromBits(0,0,!1):0,Le.prototype.debugStats=null,Le.fromObject=function(e){if(e instanceof a.google.firestore.v1.ExecutionStats)return e;var t=new a.google.firestore.v1.ExecutionStats;if(null!=e.resultsReturned&&(i.Long?(t.resultsReturned=i.Long.fromValue(e.resultsReturned)).unsigned=!1:"string"==typeof e.resultsReturned?t.resultsReturned=parseInt(e.resultsReturned,10):"number"==typeof e.resultsReturned?t.resultsReturned=e.resultsReturned:"object"==typeof e.resultsReturned&&(t.resultsReturned=new i.LongBits(e.resultsReturned.low>>>0,e.resultsReturned.high>>>0).toNumber())),null!=e.executionDuration){if("object"!=typeof e.executionDuration)throw TypeError(".google.firestore.v1.ExecutionStats.executionDuration: object expected");t.executionDuration=a.google.protobuf.Duration.fromObject(e.executionDuration)}if(null!=e.readOperations&&(i.Long?(t.readOperations=i.Long.fromValue(e.readOperations)).unsigned=!1:"string"==typeof e.readOperations?t.readOperations=parseInt(e.readOperations,10):"number"==typeof e.readOperations?t.readOperations=e.readOperations:"object"==typeof e.readOperations&&(t.readOperations=new i.LongBits(e.readOperations.low>>>0,e.readOperations.high>>>0).toNumber())),null!=e.debugStats){if("object"!=typeof e.debugStats)throw TypeError(".google.firestore.v1.ExecutionStats.debugStats: object expected");t.debugStats=a.google.protobuf.Struct.fromObject(e.debugStats)}return t},Le.toObject=function(e,t){var o,r={};return(t=t||{}).defaults&&(i.Long?(o=new i.Long(0,0,!1),r.resultsReturned=t.longs===String?o.toString():t.longs===Number?o.toNumber():o):r.resultsReturned=t.longs===String?"0":0,r.executionDuration=null,i.Long?(o=new i.Long(0,0,!1),r.readOperations=t.longs===String?o.toString():t.longs===Number?o.toNumber():o):r.readOperations=t.longs===String?"0":0,r.debugStats=null),null!=e.resultsReturned&&e.hasOwnProperty("resultsReturned")&&("number"==typeof e.resultsReturned?r.resultsReturned=t.longs===String?String(e.resultsReturned):e.resultsReturned:r.resultsReturned=t.longs===String?i.Long.prototype.toString.call(e.resultsReturned):t.longs===Number?new i.LongBits(e.resultsReturned.low>>>0,e.resultsReturned.high>>>0).toNumber():e.resultsReturned),null!=e.executionDuration&&e.hasOwnProperty("executionDuration")&&(r.executionDuration=a.google.protobuf.Duration.toObject(e.executionDuration,t)),null!=e.readOperations&&e.hasOwnProperty("readOperations")&&("number"==typeof e.readOperations?r.readOperations=t.longs===String?String(e.readOperations):e.readOperations:r.readOperations=t.longs===String?i.Long.prototype.toString.call(e.readOperations):t.longs===Number?new i.LongBits(e.readOperations.low>>>0,e.readOperations.high>>>0).toNumber():e.readOperations),null!=e.debugStats&&e.hasOwnProperty("debugStats")&&(r.debugStats=a.google.protobuf.Struct.toObject(e.debugStats,t)),r},Le.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},Le.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.v1.ExecutionStats"},Le),o.Write=(v.prototype.update=null,v.prototype.delete=null,v.prototype.transform=null,v.prototype.updateMask=null,v.prototype.updateTransforms=i.emptyArray,v.prototype.currentDocument=null,Object.defineProperty(v.prototype,"operation",{get:i.oneOfGetter(n=["update","delete","transform"]),set:i.oneOfSetter(n)}),v.fromObject=function(e){if(e instanceof a.google.firestore.v1.Write)return e;var t=new a.google.firestore.v1.Write;if(null!=e.update){if("object"!=typeof e.update)throw TypeError(".google.firestore.v1.Write.update: object expected");t.update=a.google.firestore.v1.Document.fromObject(e.update)}if(null!=e.delete&&(t.delete=String(e.delete)),null!=e.transform){if("object"!=typeof e.transform)throw TypeError(".google.firestore.v1.Write.transform: object expected");t.transform=a.google.firestore.v1.DocumentTransform.fromObject(e.transform)}if(null!=e.updateMask){if("object"!=typeof e.updateMask)throw TypeError(".google.firestore.v1.Write.updateMask: object expected");t.updateMask=a.google.firestore.v1.DocumentMask.fromObject(e.updateMask)}if(e.updateTransforms){if(!Array.isArray(e.updateTransforms))throw TypeError(".google.firestore.v1.Write.updateTransforms: array expected");t.updateTransforms=[];for(var o=0;o<e.updateTransforms.length;++o){if("object"!=typeof e.updateTransforms[o])throw TypeError(".google.firestore.v1.Write.updateTransforms: object expected");t.updateTransforms[o]=a.google.firestore.v1.DocumentTransform.FieldTransform.fromObject(e.updateTransforms[o])}}if(null!=e.currentDocument){if("object"!=typeof e.currentDocument)throw TypeError(".google.firestore.v1.Write.currentDocument: object expected");t.currentDocument=a.google.firestore.v1.Precondition.fromObject(e.currentDocument)}return t},v.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.updateTransforms=[]),t.defaults&&(o.updateMask=null,o.currentDocument=null),null!=e.update&&e.hasOwnProperty("update")&&(o.update=a.google.firestore.v1.Document.toObject(e.update,t),t.oneofs)&&(o.operation="update"),null!=e.delete&&e.hasOwnProperty("delete")&&(o.delete=e.delete,t.oneofs)&&(o.operation="delete"),null!=e.updateMask&&e.hasOwnProperty("updateMask")&&(o.updateMask=a.google.firestore.v1.DocumentMask.toObject(e.updateMask,t)),null!=e.currentDocument&&e.hasOwnProperty("currentDocument")&&(o.currentDocument=a.google.firestore.v1.Precondition.toObject(e.currentDocument,t)),null!=e.transform&&e.hasOwnProperty("transform")&&(o.transform=a.google.firestore.v1.DocumentTransform.toObject(e.transform,t),t.oneofs)&&(o.operation="transform"),e.updateTransforms&&e.updateTransforms.length){o.updateTransforms=[];for(var r=0;r<e.updateTransforms.length;++r)o.updateTransforms[r]=a.google.firestore.v1.DocumentTransform.FieldTransform.toObject(e.updateTransforms[r],t)}return o},v.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},v.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.v1.Write"},v),o.DocumentTransform=(Ue.prototype.document="",Ue.prototype.fieldTransforms=i.emptyArray,Ue.fromObject=function(e){if(e instanceof a.google.firestore.v1.DocumentTransform)return e;var t=new a.google.firestore.v1.DocumentTransform;if(null!=e.document&&(t.document=String(e.document)),e.fieldTransforms){if(!Array.isArray(e.fieldTransforms))throw TypeError(".google.firestore.v1.DocumentTransform.fieldTransforms: array expected");t.fieldTransforms=[];for(var o=0;o<e.fieldTransforms.length;++o){if("object"!=typeof e.fieldTransforms[o])throw TypeError(".google.firestore.v1.DocumentTransform.fieldTransforms: object expected");t.fieldTransforms[o]=a.google.firestore.v1.DocumentTransform.FieldTransform.fromObject(e.fieldTransforms[o])}}return t},Ue.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.fieldTransforms=[]),t.defaults&&(o.document=""),null!=e.document&&e.hasOwnProperty("document")&&(o.document=e.document),e.fieldTransforms&&e.fieldTransforms.length){o.fieldTransforms=[];for(var r=0;r<e.fieldTransforms.length;++r)o.fieldTransforms[r]=a.google.firestore.v1.DocumentTransform.FieldTransform.toObject(e.fieldTransforms[r],t)}return o},Ue.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},Ue.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.v1.DocumentTransform"},Ue.FieldTransform=(T.prototype.fieldPath="",T.prototype.setToServerValue=null,T.prototype.increment=null,T.prototype.maximum=null,T.prototype.minimum=null,T.prototype.appendMissingElements=null,T.prototype.removeAllFromArray=null,Object.defineProperty(T.prototype,"transformType",{get:i.oneOfGetter(e=["setToServerValue","increment","maximum","minimum","appendMissingElements","removeAllFromArray"]),set:i.oneOfSetter(e)}),T.fromObject=function(e){if(e instanceof a.google.firestore.v1.DocumentTransform.FieldTransform)return e;var t=new a.google.firestore.v1.DocumentTransform.FieldTransform;switch(null!=e.fieldPath&&(t.fieldPath=String(e.fieldPath)),e.setToServerValue){default:"number"==typeof e.setToServerValue&&(t.setToServerValue=e.setToServerValue);break;case"SERVER_VALUE_UNSPECIFIED":case 0:t.setToServerValue=0;break;case"REQUEST_TIME":case 1:t.setToServerValue=1}if(null!=e.increment){if("object"!=typeof e.increment)throw TypeError(".google.firestore.v1.DocumentTransform.FieldTransform.increment: object expected");t.increment=a.google.firestore.v1.Value.fromObject(e.increment)}if(null!=e.maximum){if("object"!=typeof e.maximum)throw TypeError(".google.firestore.v1.DocumentTransform.FieldTransform.maximum: object expected");t.maximum=a.google.firestore.v1.Value.fromObject(e.maximum)}if(null!=e.minimum){if("object"!=typeof e.minimum)throw TypeError(".google.firestore.v1.DocumentTransform.FieldTransform.minimum: object expected");t.minimum=a.google.firestore.v1.Value.fromObject(e.minimum)}if(null!=e.appendMissingElements){if("object"!=typeof e.appendMissingElements)throw TypeError(".google.firestore.v1.DocumentTransform.FieldTransform.appendMissingElements: object expected");t.appendMissingElements=a.google.firestore.v1.ArrayValue.fromObject(e.appendMissingElements)}if(null!=e.removeAllFromArray){if("object"!=typeof e.removeAllFromArray)throw TypeError(".google.firestore.v1.DocumentTransform.FieldTransform.removeAllFromArray: object expected");t.removeAllFromArray=a.google.firestore.v1.ArrayValue.fromObject(e.removeAllFromArray)}return t},T.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.fieldPath=""),null!=e.fieldPath&&e.hasOwnProperty("fieldPath")&&(o.fieldPath=e.fieldPath),null!=e.setToServerValue&&e.hasOwnProperty("setToServerValue")&&(o.setToServerValue=t.enums!==String||void 0===a.google.firestore.v1.DocumentTransform.FieldTransform.ServerValue[e.setToServerValue]?e.setToServerValue:a.google.firestore.v1.DocumentTransform.FieldTransform.ServerValue[e.setToServerValue],t.oneofs)&&(o.transformType="setToServerValue"),null!=e.increment&&e.hasOwnProperty("increment")&&(o.increment=a.google.firestore.v1.Value.toObject(e.increment,t),t.oneofs)&&(o.transformType="increment"),null!=e.maximum&&e.hasOwnProperty("maximum")&&(o.maximum=a.google.firestore.v1.Value.toObject(e.maximum,t),t.oneofs)&&(o.transformType="maximum"),null!=e.minimum&&e.hasOwnProperty("minimum")&&(o.minimum=a.google.firestore.v1.Value.toObject(e.minimum,t),t.oneofs)&&(o.transformType="minimum"),null!=e.appendMissingElements&&e.hasOwnProperty("appendMissingElements")&&(o.appendMissingElements=a.google.firestore.v1.ArrayValue.toObject(e.appendMissingElements,t),t.oneofs)&&(o.transformType="appendMissingElements"),null!=e.removeAllFromArray&&e.hasOwnProperty("removeAllFromArray")&&(o.removeAllFromArray=a.google.firestore.v1.ArrayValue.toObject(e.removeAllFromArray,t),t.oneofs)&&(o.transformType="removeAllFromArray"),o},T.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},T.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.v1.DocumentTransform.FieldTransform"},T.ServerValue=(e={},(n=Object.create(e))[e[0]="SERVER_VALUE_UNSPECIFIED"]="SERVER_VALUE_UNSPECIFIED",n[e[1]="REQUEST_TIME"]="REQUEST_TIME",n),T),Ue),o.WriteResult=(Be.prototype.updateTime=null,Be.prototype.transformResults=i.emptyArray,Be.fromObject=function(e){if(e instanceof a.google.firestore.v1.WriteResult)return e;var t=new a.google.firestore.v1.WriteResult;if(null!=e.updateTime){if("object"!=typeof e.updateTime)throw TypeError(".google.firestore.v1.WriteResult.updateTime: object expected");t.updateTime=a.google.protobuf.Timestamp.fromObject(e.updateTime)}if(e.transformResults){if(!Array.isArray(e.transformResults))throw TypeError(".google.firestore.v1.WriteResult.transformResults: array expected");t.transformResults=[];for(var o=0;o<e.transformResults.length;++o){if("object"!=typeof e.transformResults[o])throw TypeError(".google.firestore.v1.WriteResult.transformResults: object expected");t.transformResults[o]=a.google.firestore.v1.Value.fromObject(e.transformResults[o])}}return t},Be.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.transformResults=[]),t.defaults&&(o.updateTime=null),null!=e.updateTime&&e.hasOwnProperty("updateTime")&&(o.updateTime=a.google.protobuf.Timestamp.toObject(e.updateTime,t)),e.transformResults&&e.transformResults.length){o.transformResults=[];for(var r=0;r<e.transformResults.length;++r)o.transformResults[r]=a.google.firestore.v1.Value.toObject(e.transformResults[r],t)}return o},Be.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},Be.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.v1.WriteResult"},Be),o.DocumentChange=(Je.prototype.document=null,Je.prototype.targetIds=i.emptyArray,Je.prototype.removedTargetIds=i.emptyArray,Je.fromObject=function(e){if(e instanceof a.google.firestore.v1.DocumentChange)return e;var t=new a.google.firestore.v1.DocumentChange;if(null!=e.document){if("object"!=typeof e.document)throw TypeError(".google.firestore.v1.DocumentChange.document: object expected");t.document=a.google.firestore.v1.Document.fromObject(e.document)}if(e.targetIds){if(!Array.isArray(e.targetIds))throw TypeError(".google.firestore.v1.DocumentChange.targetIds: array expected");t.targetIds=[];for(var o=0;o<e.targetIds.length;++o)t.targetIds[o]=0|e.targetIds[o]}if(e.removedTargetIds){if(!Array.isArray(e.removedTargetIds))throw TypeError(".google.firestore.v1.DocumentChange.removedTargetIds: array expected");t.removedTargetIds=[];for(o=0;o<e.removedTargetIds.length;++o)t.removedTargetIds[o]=0|e.removedTargetIds[o]}return t},Je.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.targetIds=[],o.removedTargetIds=[]),t.defaults&&(o.document=null),null!=e.document&&e.hasOwnProperty("document")&&(o.document=a.google.firestore.v1.Document.toObject(e.document,t)),e.targetIds&&e.targetIds.length){o.targetIds=[];for(var r=0;r<e.targetIds.length;++r)o.targetIds[r]=e.targetIds[r]}if(e.removedTargetIds&&e.removedTargetIds.length){o.removedTargetIds=[];for(r=0;r<e.removedTargetIds.length;++r)o.removedTargetIds[r]=e.removedTargetIds[r]}return o},Je.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},Je.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.v1.DocumentChange"},Je),o.DocumentDelete=(Qe.prototype.document="",Qe.prototype.removedTargetIds=i.emptyArray,Qe.prototype.readTime=null,Qe.fromObject=function(e){if(e instanceof a.google.firestore.v1.DocumentDelete)return e;var t=new a.google.firestore.v1.DocumentDelete;if(null!=e.document&&(t.document=String(e.document)),e.removedTargetIds){if(!Array.isArray(e.removedTargetIds))throw TypeError(".google.firestore.v1.DocumentDelete.removedTargetIds: array expected");t.removedTargetIds=[];for(var o=0;o<e.removedTargetIds.length;++o)t.removedTargetIds[o]=0|e.removedTargetIds[o]}if(null!=e.readTime){if("object"!=typeof e.readTime)throw TypeError(".google.firestore.v1.DocumentDelete.readTime: object expected");t.readTime=a.google.protobuf.Timestamp.fromObject(e.readTime)}return t},Qe.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.removedTargetIds=[]),t.defaults&&(o.document="",o.readTime=null),null!=e.document&&e.hasOwnProperty("document")&&(o.document=e.document),null!=e.readTime&&e.hasOwnProperty("readTime")&&(o.readTime=a.google.protobuf.Timestamp.toObject(e.readTime,t)),e.removedTargetIds&&e.removedTargetIds.length){o.removedTargetIds=[];for(var r=0;r<e.removedTargetIds.length;++r)o.removedTargetIds[r]=e.removedTargetIds[r]}return o},Qe.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},Qe.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.v1.DocumentDelete"},Qe),o.DocumentRemove=(Me.prototype.document="",Me.prototype.removedTargetIds=i.emptyArray,Me.prototype.readTime=null,Me.fromObject=function(e){if(e instanceof a.google.firestore.v1.DocumentRemove)return e;var t=new a.google.firestore.v1.DocumentRemove;if(null!=e.document&&(t.document=String(e.document)),e.removedTargetIds){if(!Array.isArray(e.removedTargetIds))throw TypeError(".google.firestore.v1.DocumentRemove.removedTargetIds: array expected");t.removedTargetIds=[];for(var o=0;o<e.removedTargetIds.length;++o)t.removedTargetIds[o]=0|e.removedTargetIds[o]}if(null!=e.readTime){if("object"!=typeof e.readTime)throw TypeError(".google.firestore.v1.DocumentRemove.readTime: object expected");t.readTime=a.google.protobuf.Timestamp.fromObject(e.readTime)}return t},Me.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.removedTargetIds=[]),t.defaults&&(o.document="",o.readTime=null),null!=e.document&&e.hasOwnProperty("document")&&(o.document=e.document),e.removedTargetIds&&e.removedTargetIds.length){o.removedTargetIds=[];for(var r=0;r<e.removedTargetIds.length;++r)o.removedTargetIds[r]=e.removedTargetIds[r]}return null!=e.readTime&&e.hasOwnProperty("readTime")&&(o.readTime=a.google.protobuf.Timestamp.toObject(e.readTime,t)),o},Me.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},Me.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.v1.DocumentRemove"},Me),o.ExistenceFilter=(Ge.prototype.targetId=0,Ge.prototype.count=0,Ge.prototype.unchangedNames=null,Ge.fromObject=function(e){if(e instanceof a.google.firestore.v1.ExistenceFilter)return e;var t=new a.google.firestore.v1.ExistenceFilter;if(null!=e.targetId&&(t.targetId=0|e.targetId),null!=e.count&&(t.count=0|e.count),null!=e.unchangedNames){if("object"!=typeof e.unchangedNames)throw TypeError(".google.firestore.v1.ExistenceFilter.unchangedNames: object expected");t.unchangedNames=a.google.firestore.v1.BloomFilter.fromObject(e.unchangedNames)}return t},Ge.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.targetId=0,o.count=0,o.unchangedNames=null),null!=e.targetId&&e.hasOwnProperty("targetId")&&(o.targetId=e.targetId),null!=e.count&&e.hasOwnProperty("count")&&(o.count=e.count),null!=e.unchangedNames&&e.hasOwnProperty("unchangedNames")&&(o.unchangedNames=a.google.firestore.v1.BloomFilter.toObject(e.unchangedNames,t)),o},Ge.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},Ge.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.v1.ExistenceFilter"},Ge),o),t),C.api=((e={}).FieldBehavior=(n={},(o=Object.create(n))[n[0]="FIELD_BEHAVIOR_UNSPECIFIED"]="FIELD_BEHAVIOR_UNSPECIFIED",o[n[1]="OPTIONAL"]="OPTIONAL",o[n[2]="REQUIRED"]="REQUIRED",o[n[3]="OUTPUT_ONLY"]="OUTPUT_ONLY",o[n[4]="INPUT_ONLY"]="INPUT_ONLY",o[n[5]="IMMUTABLE"]="IMMUTABLE",o[n[6]="UNORDERED_LIST"]="UNORDERED_LIST",o[n[7]="NON_EMPTY_DEFAULT"]="NON_EMPTY_DEFAULT",o[n[8]="IDENTIFIER"]="IDENTIFIER",o),e.Http=(qe.prototype.rules=i.emptyArray,qe.prototype.fullyDecodeReservedExpansion=!1,qe.fromObject=function(e){if(e instanceof a.google.api.Http)return e;var t=new a.google.api.Http;if(e.rules){if(!Array.isArray(e.rules))throw TypeError(".google.api.Http.rules: array expected");t.rules=[];for(var o=0;o<e.rules.length;++o){if("object"!=typeof e.rules[o])throw TypeError(".google.api.Http.rules: object expected");t.rules[o]=a.google.api.HttpRule.fromObject(e.rules[o])}}return null!=e.fullyDecodeReservedExpansion&&(t.fullyDecodeReservedExpansion=Boolean(e.fullyDecodeReservedExpansion)),t},qe.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.rules=[]),t.defaults&&(o.fullyDecodeReservedExpansion=!1),e.rules&&e.rules.length){o.rules=[];for(var r=0;r<e.rules.length;++r)o.rules[r]=a.google.api.HttpRule.toObject(e.rules[r],t)}return null!=e.fullyDecodeReservedExpansion&&e.hasOwnProperty("fullyDecodeReservedExpansion")&&(o.fullyDecodeReservedExpansion=e.fullyDecodeReservedExpansion),o},qe.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},qe.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.api.Http"},qe),e.HttpRule=(S.prototype.selector="",S.prototype.get=null,S.prototype.put=null,S.prototype.post=null,S.prototype.delete=null,S.prototype.patch=null,S.prototype.custom=null,S.prototype.body="",S.prototype.responseBody="",S.prototype.additionalBindings=i.emptyArray,Object.defineProperty(S.prototype,"pattern",{get:i.oneOfGetter(n=["get","put","post","delete","patch","custom"]),set:i.oneOfSetter(n)}),S.fromObject=function(e){if(e instanceof a.google.api.HttpRule)return e;var t=new a.google.api.HttpRule;if(null!=e.selector&&(t.selector=String(e.selector)),null!=e.get&&(t.get=String(e.get)),null!=e.put&&(t.put=String(e.put)),null!=e.post&&(t.post=String(e.post)),null!=e.delete&&(t.delete=String(e.delete)),null!=e.patch&&(t.patch=String(e.patch)),null!=e.custom){if("object"!=typeof e.custom)throw TypeError(".google.api.HttpRule.custom: object expected");t.custom=a.google.api.CustomHttpPattern.fromObject(e.custom)}if(null!=e.body&&(t.body=String(e.body)),null!=e.responseBody&&(t.responseBody=String(e.responseBody)),e.additionalBindings){if(!Array.isArray(e.additionalBindings))throw TypeError(".google.api.HttpRule.additionalBindings: array expected");t.additionalBindings=[];for(var o=0;o<e.additionalBindings.length;++o){if("object"!=typeof e.additionalBindings[o])throw TypeError(".google.api.HttpRule.additionalBindings: object expected");t.additionalBindings[o]=a.google.api.HttpRule.fromObject(e.additionalBindings[o])}}return t},S.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.additionalBindings=[]),t.defaults&&(o.selector="",o.body="",o.responseBody=""),null!=e.selector&&e.hasOwnProperty("selector")&&(o.selector=e.selector),null!=e.get&&e.hasOwnProperty("get")&&(o.get=e.get,t.oneofs)&&(o.pattern="get"),null!=e.put&&e.hasOwnProperty("put")&&(o.put=e.put,t.oneofs)&&(o.pattern="put"),null!=e.post&&e.hasOwnProperty("post")&&(o.post=e.post,t.oneofs)&&(o.pattern="post"),null!=e.delete&&e.hasOwnProperty("delete")&&(o.delete=e.delete,t.oneofs)&&(o.pattern="delete"),null!=e.patch&&e.hasOwnProperty("patch")&&(o.patch=e.patch,t.oneofs)&&(o.pattern="patch"),null!=e.body&&e.hasOwnProperty("body")&&(o.body=e.body),null!=e.custom&&e.hasOwnProperty("custom")&&(o.custom=a.google.api.CustomHttpPattern.toObject(e.custom,t),t.oneofs)&&(o.pattern="custom"),e.additionalBindings&&e.additionalBindings.length){o.additionalBindings=[];for(var r=0;r<e.additionalBindings.length;++r)o.additionalBindings[r]=a.google.api.HttpRule.toObject(e.additionalBindings[r],t)}return null!=e.responseBody&&e.hasOwnProperty("responseBody")&&(o.responseBody=e.responseBody),o},S.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},S.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.api.HttpRule"},S),e.CustomHttpPattern=(Ye.prototype.kind="",Ye.prototype.path="",Ye.fromObject=function(e){var t;return e instanceof a.google.api.CustomHttpPattern?e:(t=new a.google.api.CustomHttpPattern,null!=e.kind&&(t.kind=String(e.kind)),null!=e.path&&(t.path=String(e.path)),t)},Ye.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.kind="",o.path=""),null!=e.kind&&e.hasOwnProperty("kind")&&(o.kind=e.kind),null!=e.path&&e.hasOwnProperty("path")&&(o.path=e.path),o},Ye.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},Ye.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.api.CustomHttpPattern"},Ye),e.CommonLanguageSettings=(We.prototype.referenceDocsUri="",We.prototype.destinations=i.emptyArray,We.prototype.selectiveGapicGeneration=null,We.fromObject=function(e){if(e instanceof a.google.api.CommonLanguageSettings)return e;var t=new a.google.api.CommonLanguageSettings;if(null!=e.referenceDocsUri&&(t.referenceDocsUri=String(e.referenceDocsUri)),e.destinations){if(!Array.isArray(e.destinations))throw TypeError(".google.api.CommonLanguageSettings.destinations: array expected");t.destinations=[];for(var o=0;o<e.destinations.length;++o)switch(e.destinations[o]){default:if("number"==typeof e.destinations[o]){t.destinations[o]=e.destinations[o];break}case"CLIENT_LIBRARY_DESTINATION_UNSPECIFIED":case 0:t.destinations[o]=0;break;case"GITHUB":case 10:t.destinations[o]=10;break;case"PACKAGE_MANAGER":case 20:t.destinations[o]=20}}if(null!=e.selectiveGapicGeneration){if("object"!=typeof e.selectiveGapicGeneration)throw TypeError(".google.api.CommonLanguageSettings.selectiveGapicGeneration: object expected");t.selectiveGapicGeneration=a.google.api.SelectiveGapicGeneration.fromObject(e.selectiveGapicGeneration)}return t},We.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.destinations=[]),t.defaults&&(o.referenceDocsUri="",o.selectiveGapicGeneration=null),null!=e.referenceDocsUri&&e.hasOwnProperty("referenceDocsUri")&&(o.referenceDocsUri=e.referenceDocsUri),e.destinations&&e.destinations.length){o.destinations=[];for(var r=0;r<e.destinations.length;++r)o.destinations[r]=t.enums!==String||void 0===a.google.api.ClientLibraryDestination[e.destinations[r]]?e.destinations[r]:a.google.api.ClientLibraryDestination[e.destinations[r]]}return null!=e.selectiveGapicGeneration&&e.hasOwnProperty("selectiveGapicGeneration")&&(o.selectiveGapicGeneration=a.google.api.SelectiveGapicGeneration.toObject(e.selectiveGapicGeneration,t)),o},We.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},We.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.api.CommonLanguageSettings"},We),e.ClientLibrarySettings=(j.prototype.version="",j.prototype.launchStage=0,j.prototype.restNumericEnums=!1,j.prototype.javaSettings=null,j.prototype.cppSettings=null,j.prototype.phpSettings=null,j.prototype.pythonSettings=null,j.prototype.nodeSettings=null,j.prototype.dotnetSettings=null,j.prototype.rubySettings=null,j.prototype.goSettings=null,j.fromObject=function(e){if(e instanceof a.google.api.ClientLibrarySettings)return e;var t=new a.google.api.ClientLibrarySettings;switch(null!=e.version&&(t.version=String(e.version)),e.launchStage){default:"number"==typeof e.launchStage&&(t.launchStage=e.launchStage);break;case"LAUNCH_STAGE_UNSPECIFIED":case 0:t.launchStage=0;break;case"UNIMPLEMENTED":case 6:t.launchStage=6;break;case"PRELAUNCH":case 7:t.launchStage=7;break;case"EARLY_ACCESS":case 1:t.launchStage=1;break;case"ALPHA":case 2:t.launchStage=2;break;case"BETA":case 3:t.launchStage=3;break;case"GA":case 4:t.launchStage=4;break;case"DEPRECATED":case 5:t.launchStage=5}if(null!=e.restNumericEnums&&(t.restNumericEnums=Boolean(e.restNumericEnums)),null!=e.javaSettings){if("object"!=typeof e.javaSettings)throw TypeError(".google.api.ClientLibrarySettings.javaSettings: object expected");t.javaSettings=a.google.api.JavaSettings.fromObject(e.javaSettings)}if(null!=e.cppSettings){if("object"!=typeof e.cppSettings)throw TypeError(".google.api.ClientLibrarySettings.cppSettings: object expected");t.cppSettings=a.google.api.CppSettings.fromObject(e.cppSettings)}if(null!=e.phpSettings){if("object"!=typeof e.phpSettings)throw TypeError(".google.api.ClientLibrarySettings.phpSettings: object expected");t.phpSettings=a.google.api.PhpSettings.fromObject(e.phpSettings)}if(null!=e.pythonSettings){if("object"!=typeof e.pythonSettings)throw TypeError(".google.api.ClientLibrarySettings.pythonSettings: object expected");t.pythonSettings=a.google.api.PythonSettings.fromObject(e.pythonSettings)}if(null!=e.nodeSettings){if("object"!=typeof e.nodeSettings)throw TypeError(".google.api.ClientLibrarySettings.nodeSettings: object expected");t.nodeSettings=a.google.api.NodeSettings.fromObject(e.nodeSettings)}if(null!=e.dotnetSettings){if("object"!=typeof e.dotnetSettings)throw TypeError(".google.api.ClientLibrarySettings.dotnetSettings: object expected");t.dotnetSettings=a.google.api.DotnetSettings.fromObject(e.dotnetSettings)}if(null!=e.rubySettings){if("object"!=typeof e.rubySettings)throw TypeError(".google.api.ClientLibrarySettings.rubySettings: object expected");t.rubySettings=a.google.api.RubySettings.fromObject(e.rubySettings)}if(null!=e.goSettings){if("object"!=typeof e.goSettings)throw TypeError(".google.api.ClientLibrarySettings.goSettings: object expected");t.goSettings=a.google.api.GoSettings.fromObject(e.goSettings)}return t},j.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.version="",o.launchStage=t.enums===String?"LAUNCH_STAGE_UNSPECIFIED":0,o.restNumericEnums=!1,o.javaSettings=null,o.cppSettings=null,o.phpSettings=null,o.pythonSettings=null,o.nodeSettings=null,o.dotnetSettings=null,o.rubySettings=null,o.goSettings=null),null!=e.version&&e.hasOwnProperty("version")&&(o.version=e.version),null!=e.launchStage&&e.hasOwnProperty("launchStage")&&(o.launchStage=t.enums!==String||void 0===a.google.api.LaunchStage[e.launchStage]?e.launchStage:a.google.api.LaunchStage[e.launchStage]),null!=e.restNumericEnums&&e.hasOwnProperty("restNumericEnums")&&(o.restNumericEnums=e.restNumericEnums),null!=e.javaSettings&&e.hasOwnProperty("javaSettings")&&(o.javaSettings=a.google.api.JavaSettings.toObject(e.javaSettings,t)),null!=e.cppSettings&&e.hasOwnProperty("cppSettings")&&(o.cppSettings=a.google.api.CppSettings.toObject(e.cppSettings,t)),null!=e.phpSettings&&e.hasOwnProperty("phpSettings")&&(o.phpSettings=a.google.api.PhpSettings.toObject(e.phpSettings,t)),null!=e.pythonSettings&&e.hasOwnProperty("pythonSettings")&&(o.pythonSettings=a.google.api.PythonSettings.toObject(e.pythonSettings,t)),null!=e.nodeSettings&&e.hasOwnProperty("nodeSettings")&&(o.nodeSettings=a.google.api.NodeSettings.toObject(e.nodeSettings,t)),null!=e.dotnetSettings&&e.hasOwnProperty("dotnetSettings")&&(o.dotnetSettings=a.google.api.DotnetSettings.toObject(e.dotnetSettings,t)),null!=e.rubySettings&&e.hasOwnProperty("rubySettings")&&(o.rubySettings=a.google.api.RubySettings.toObject(e.rubySettings,t)),null!=e.goSettings&&e.hasOwnProperty("goSettings")&&(o.goSettings=a.google.api.GoSettings.toObject(e.goSettings,t)),o},j.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},j.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.api.ClientLibrarySettings"},j),e.Publishing=(E.prototype.methodSettings=i.emptyArray,E.prototype.newIssueUri="",E.prototype.documentationUri="",E.prototype.apiShortName="",E.prototype.githubLabel="",E.prototype.codeownerGithubTeams=i.emptyArray,E.prototype.docTagPrefix="",E.prototype.organization=0,E.prototype.librarySettings=i.emptyArray,E.prototype.protoReferenceDocumentationUri="",E.prototype.restReferenceDocumentationUri="",E.fromObject=function(e){if(e instanceof a.google.api.Publishing)return e;var t=new a.google.api.Publishing;if(e.methodSettings){if(!Array.isArray(e.methodSettings))throw TypeError(".google.api.Publishing.methodSettings: array expected");t.methodSettings=[];for(var o=0;o<e.methodSettings.length;++o){if("object"!=typeof e.methodSettings[o])throw TypeError(".google.api.Publishing.methodSettings: object expected");t.methodSettings[o]=a.google.api.MethodSettings.fromObject(e.methodSettings[o])}}if(null!=e.newIssueUri&&(t.newIssueUri=String(e.newIssueUri)),null!=e.documentationUri&&(t.documentationUri=String(e.documentationUri)),null!=e.apiShortName&&(t.apiShortName=String(e.apiShortName)),null!=e.githubLabel&&(t.githubLabel=String(e.githubLabel)),e.codeownerGithubTeams){if(!Array.isArray(e.codeownerGithubTeams))throw TypeError(".google.api.Publishing.codeownerGithubTeams: array expected");t.codeownerGithubTeams=[];for(o=0;o<e.codeownerGithubTeams.length;++o)t.codeownerGithubTeams[o]=String(e.codeownerGithubTeams[o])}switch(null!=e.docTagPrefix&&(t.docTagPrefix=String(e.docTagPrefix)),e.organization){default:"number"==typeof e.organization&&(t.organization=e.organization);break;case"CLIENT_LIBRARY_ORGANIZATION_UNSPECIFIED":case 0:t.organization=0;break;case"CLOUD":case 1:t.organization=1;break;case"ADS":case 2:t.organization=2;break;case"PHOTOS":case 3:t.organization=3;break;case"STREET_VIEW":case 4:t.organization=4;break;case"SHOPPING":case 5:t.organization=5;break;case"GEO":case 6:t.organization=6;break;case"GENERATIVE_AI":case 7:t.organization=7}if(e.librarySettings){if(!Array.isArray(e.librarySettings))throw TypeError(".google.api.Publishing.librarySettings: array expected");t.librarySettings=[];for(o=0;o<e.librarySettings.length;++o){if("object"!=typeof e.librarySettings[o])throw TypeError(".google.api.Publishing.librarySettings: object expected");t.librarySettings[o]=a.google.api.ClientLibrarySettings.fromObject(e.librarySettings[o])}}return null!=e.protoReferenceDocumentationUri&&(t.protoReferenceDocumentationUri=String(e.protoReferenceDocumentationUri)),null!=e.restReferenceDocumentationUri&&(t.restReferenceDocumentationUri=String(e.restReferenceDocumentationUri)),t},E.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.methodSettings=[],o.codeownerGithubTeams=[],o.librarySettings=[]),t.defaults&&(o.newIssueUri="",o.documentationUri="",o.apiShortName="",o.githubLabel="",o.docTagPrefix="",o.organization=t.enums===String?"CLIENT_LIBRARY_ORGANIZATION_UNSPECIFIED":0,o.protoReferenceDocumentationUri="",o.restReferenceDocumentationUri=""),e.methodSettings&&e.methodSettings.length){o.methodSettings=[];for(var r=0;r<e.methodSettings.length;++r)o.methodSettings[r]=a.google.api.MethodSettings.toObject(e.methodSettings[r],t)}if(null!=e.newIssueUri&&e.hasOwnProperty("newIssueUri")&&(o.newIssueUri=e.newIssueUri),null!=e.documentationUri&&e.hasOwnProperty("documentationUri")&&(o.documentationUri=e.documentationUri),null!=e.apiShortName&&e.hasOwnProperty("apiShortName")&&(o.apiShortName=e.apiShortName),null!=e.githubLabel&&e.hasOwnProperty("githubLabel")&&(o.githubLabel=e.githubLabel),e.codeownerGithubTeams&&e.codeownerGithubTeams.length){o.codeownerGithubTeams=[];for(r=0;r<e.codeownerGithubTeams.length;++r)o.codeownerGithubTeams[r]=e.codeownerGithubTeams[r]}if(null!=e.docTagPrefix&&e.hasOwnProperty("docTagPrefix")&&(o.docTagPrefix=e.docTagPrefix),null!=e.organization&&e.hasOwnProperty("organization")&&(o.organization=t.enums!==String||void 0===a.google.api.ClientLibraryOrganization[e.organization]?e.organization:a.google.api.ClientLibraryOrganization[e.organization]),e.librarySettings&&e.librarySettings.length){o.librarySettings=[];for(r=0;r<e.librarySettings.length;++r)o.librarySettings[r]=a.google.api.ClientLibrarySettings.toObject(e.librarySettings[r],t)}return null!=e.protoReferenceDocumentationUri&&e.hasOwnProperty("protoReferenceDocumentationUri")&&(o.protoReferenceDocumentationUri=e.protoReferenceDocumentationUri),null!=e.restReferenceDocumentationUri&&e.hasOwnProperty("restReferenceDocumentationUri")&&(o.restReferenceDocumentationUri=e.restReferenceDocumentationUri),o},E.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},E.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.api.Publishing"},E),e.JavaSettings=(ze.prototype.libraryPackage="",ze.prototype.serviceClassNames=i.emptyObject,ze.prototype.common=null,ze.fromObject=function(e){if(e instanceof a.google.api.JavaSettings)return e;var t=new a.google.api.JavaSettings;if(null!=e.libraryPackage&&(t.libraryPackage=String(e.libraryPackage)),e.serviceClassNames){if("object"!=typeof e.serviceClassNames)throw TypeError(".google.api.JavaSettings.serviceClassNames: object expected");t.serviceClassNames={};for(var o=Object.keys(e.serviceClassNames),r=0;r<o.length;++r)t.serviceClassNames[o[r]]=String(e.serviceClassNames[o[r]])}if(null!=e.common){if("object"!=typeof e.common)throw TypeError(".google.api.JavaSettings.common: object expected");t.common=a.google.api.CommonLanguageSettings.fromObject(e.common)}return t},ze.toObject=function(e,t){var o,r={};if(((t=t||{}).objects||t.defaults)&&(r.serviceClassNames={}),t.defaults&&(r.libraryPackage="",r.common=null),null!=e.libraryPackage&&e.hasOwnProperty("libraryPackage")&&(r.libraryPackage=e.libraryPackage),e.serviceClassNames&&(o=Object.keys(e.serviceClassNames)).length){r.serviceClassNames={};for(var n=0;n<o.length;++n)r.serviceClassNames[o[n]]=e.serviceClassNames[o[n]]}return null!=e.common&&e.hasOwnProperty("common")&&(r.common=a.google.api.CommonLanguageSettings.toObject(e.common,t)),r},ze.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},ze.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.api.JavaSettings"},ze),e.CppSettings=(He.prototype.common=null,He.fromObject=function(e){if(e instanceof a.google.api.CppSettings)return e;var t=new a.google.api.CppSettings;if(null!=e.common){if("object"!=typeof e.common)throw TypeError(".google.api.CppSettings.common: object expected");t.common=a.google.api.CommonLanguageSettings.fromObject(e.common)}return t},He.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.common=null),null!=e.common&&e.hasOwnProperty("common")&&(o.common=a.google.api.CommonLanguageSettings.toObject(e.common,t)),o},He.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},He.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.api.CppSettings"},He),e.PhpSettings=(Ke.prototype.common=null,Ke.fromObject=function(e){if(e instanceof a.google.api.PhpSettings)return e;var t=new a.google.api.PhpSettings;if(null!=e.common){if("object"!=typeof e.common)throw TypeError(".google.api.PhpSettings.common: object expected");t.common=a.google.api.CommonLanguageSettings.fromObject(e.common)}return t},Ke.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.common=null),null!=e.common&&e.hasOwnProperty("common")&&(o.common=a.google.api.CommonLanguageSettings.toObject(e.common,t)),o},Ke.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},Ke.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.api.PhpSettings"},Ke),e.PythonSettings=(Xe.prototype.common=null,Xe.prototype.experimentalFeatures=null,Xe.fromObject=function(e){if(e instanceof a.google.api.PythonSettings)return e;var t=new a.google.api.PythonSettings;if(null!=e.common){if("object"!=typeof e.common)throw TypeError(".google.api.PythonSettings.common: object expected");t.common=a.google.api.CommonLanguageSettings.fromObject(e.common)}if(null!=e.experimentalFeatures){if("object"!=typeof e.experimentalFeatures)throw TypeError(".google.api.PythonSettings.experimentalFeatures: object expected");t.experimentalFeatures=a.google.api.PythonSettings.ExperimentalFeatures.fromObject(e.experimentalFeatures)}return t},Xe.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.common=null,o.experimentalFeatures=null),null!=e.common&&e.hasOwnProperty("common")&&(o.common=a.google.api.CommonLanguageSettings.toObject(e.common,t)),null!=e.experimentalFeatures&&e.hasOwnProperty("experimentalFeatures")&&(o.experimentalFeatures=a.google.api.PythonSettings.ExperimentalFeatures.toObject(e.experimentalFeatures,t)),o},Xe.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},Xe.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.api.PythonSettings"},Xe.ExperimentalFeatures=(Ze.prototype.restAsyncIoEnabled=!1,Ze.prototype.protobufPythonicTypesEnabled=!1,Ze.fromObject=function(e){var t;return e instanceof a.google.api.PythonSettings.ExperimentalFeatures?e:(t=new a.google.api.PythonSettings.ExperimentalFeatures,null!=e.restAsyncIoEnabled&&(t.restAsyncIoEnabled=Boolean(e.restAsyncIoEnabled)),null!=e.protobufPythonicTypesEnabled&&(t.protobufPythonicTypesEnabled=Boolean(e.protobufPythonicTypesEnabled)),t)},Ze.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.restAsyncIoEnabled=!1,o.protobufPythonicTypesEnabled=!1),null!=e.restAsyncIoEnabled&&e.hasOwnProperty("restAsyncIoEnabled")&&(o.restAsyncIoEnabled=e.restAsyncIoEnabled),null!=e.protobufPythonicTypesEnabled&&e.hasOwnProperty("protobufPythonicTypesEnabled")&&(o.protobufPythonicTypesEnabled=e.protobufPythonicTypesEnabled),o},Ze.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},Ze.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.api.PythonSettings.ExperimentalFeatures"},Ze),Xe),e.NodeSettings=($e.prototype.common=null,$e.fromObject=function(e){if(e instanceof a.google.api.NodeSettings)return e;var t=new a.google.api.NodeSettings;if(null!=e.common){if("object"!=typeof e.common)throw TypeError(".google.api.NodeSettings.common: object expected");t.common=a.google.api.CommonLanguageSettings.fromObject(e.common)}return t},$e.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.common=null),null!=e.common&&e.hasOwnProperty("common")&&(o.common=a.google.api.CommonLanguageSettings.toObject(e.common,t)),o},$e.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},$e.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.api.NodeSettings"},$e),e.DotnetSettings=(et.prototype.common=null,et.prototype.renamedServices=i.emptyObject,et.prototype.renamedResources=i.emptyObject,et.prototype.ignoredResources=i.emptyArray,et.prototype.forcedNamespaceAliases=i.emptyArray,et.prototype.handwrittenSignatures=i.emptyArray,et.fromObject=function(e){if(e instanceof a.google.api.DotnetSettings)return e;var t=new a.google.api.DotnetSettings;if(null!=e.common){if("object"!=typeof e.common)throw TypeError(".google.api.DotnetSettings.common: object expected");t.common=a.google.api.CommonLanguageSettings.fromObject(e.common)}if(e.renamedServices){if("object"!=typeof e.renamedServices)throw TypeError(".google.api.DotnetSettings.renamedServices: object expected");t.renamedServices={};for(var o=Object.keys(e.renamedServices),r=0;r<o.length;++r)t.renamedServices[o[r]]=String(e.renamedServices[o[r]])}if(e.renamedResources){if("object"!=typeof e.renamedResources)throw TypeError(".google.api.DotnetSettings.renamedResources: object expected");t.renamedResources={};for(o=Object.keys(e.renamedResources),r=0;r<o.length;++r)t.renamedResources[o[r]]=String(e.renamedResources[o[r]])}if(e.ignoredResources){if(!Array.isArray(e.ignoredResources))throw TypeError(".google.api.DotnetSettings.ignoredResources: array expected");t.ignoredResources=[];for(r=0;r<e.ignoredResources.length;++r)t.ignoredResources[r]=String(e.ignoredResources[r])}if(e.forcedNamespaceAliases){if(!Array.isArray(e.forcedNamespaceAliases))throw TypeError(".google.api.DotnetSettings.forcedNamespaceAliases: array expected");t.forcedNamespaceAliases=[];for(r=0;r<e.forcedNamespaceAliases.length;++r)t.forcedNamespaceAliases[r]=String(e.forcedNamespaceAliases[r])}if(e.handwrittenSignatures){if(!Array.isArray(e.handwrittenSignatures))throw TypeError(".google.api.DotnetSettings.handwrittenSignatures: array expected");t.handwrittenSignatures=[];for(r=0;r<e.handwrittenSignatures.length;++r)t.handwrittenSignatures[r]=String(e.handwrittenSignatures[r])}return t},et.toObject=function(e,t){var o,r={};if(((t=t||{}).arrays||t.defaults)&&(r.ignoredResources=[],r.forcedNamespaceAliases=[],r.handwrittenSignatures=[]),(t.objects||t.defaults)&&(r.renamedServices={},r.renamedResources={}),t.defaults&&(r.common=null),null!=e.common&&e.hasOwnProperty("common")&&(r.common=a.google.api.CommonLanguageSettings.toObject(e.common,t)),e.renamedServices&&(o=Object.keys(e.renamedServices)).length){r.renamedServices={};for(var n=0;n<o.length;++n)r.renamedServices[o[n]]=e.renamedServices[o[n]]}if(e.renamedResources&&(o=Object.keys(e.renamedResources)).length){r.renamedResources={};for(n=0;n<o.length;++n)r.renamedResources[o[n]]=e.renamedResources[o[n]]}if(e.ignoredResources&&e.ignoredResources.length){r.ignoredResources=[];for(n=0;n<e.ignoredResources.length;++n)r.ignoredResources[n]=e.ignoredResources[n]}if(e.forcedNamespaceAliases&&e.forcedNamespaceAliases.length){r.forcedNamespaceAliases=[];for(n=0;n<e.forcedNamespaceAliases.length;++n)r.forcedNamespaceAliases[n]=e.forcedNamespaceAliases[n]}if(e.handwrittenSignatures&&e.handwrittenSignatures.length){r.handwrittenSignatures=[];for(n=0;n<e.handwrittenSignatures.length;++n)r.handwrittenSignatures[n]=e.handwrittenSignatures[n]}return r},et.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},et.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.api.DotnetSettings"},et),e.RubySettings=(tt.prototype.common=null,tt.fromObject=function(e){if(e instanceof a.google.api.RubySettings)return e;var t=new a.google.api.RubySettings;if(null!=e.common){if("object"!=typeof e.common)throw TypeError(".google.api.RubySettings.common: object expected");t.common=a.google.api.CommonLanguageSettings.fromObject(e.common)}return t},tt.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.common=null),null!=e.common&&e.hasOwnProperty("common")&&(o.common=a.google.api.CommonLanguageSettings.toObject(e.common,t)),o},tt.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},tt.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.api.RubySettings"},tt),e.GoSettings=(ot.prototype.common=null,ot.prototype.renamedServices=i.emptyObject,ot.fromObject=function(e){if(e instanceof a.google.api.GoSettings)return e;var t=new a.google.api.GoSettings;if(null!=e.common){if("object"!=typeof e.common)throw TypeError(".google.api.GoSettings.common: object expected");t.common=a.google.api.CommonLanguageSettings.fromObject(e.common)}if(e.renamedServices){if("object"!=typeof e.renamedServices)throw TypeError(".google.api.GoSettings.renamedServices: object expected");t.renamedServices={};for(var o=Object.keys(e.renamedServices),r=0;r<o.length;++r)t.renamedServices[o[r]]=String(e.renamedServices[o[r]])}return t},ot.toObject=function(e,t){var o,r={};if(((t=t||{}).objects||t.defaults)&&(r.renamedServices={}),t.defaults&&(r.common=null),null!=e.common&&e.hasOwnProperty("common")&&(r.common=a.google.api.CommonLanguageSettings.toObject(e.common,t)),e.renamedServices&&(o=Object.keys(e.renamedServices)).length){r.renamedServices={};for(var n=0;n<o.length;++n)r.renamedServices[o[n]]=e.renamedServices[o[n]]}return r},ot.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},ot.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.api.GoSettings"},ot),e.MethodSettings=(rt.prototype.selector="",rt.prototype.longRunning=null,rt.prototype.autoPopulatedFields=i.emptyArray,rt.fromObject=function(e){if(e instanceof a.google.api.MethodSettings)return e;var t=new a.google.api.MethodSettings;if(null!=e.selector&&(t.selector=String(e.selector)),null!=e.longRunning){if("object"!=typeof e.longRunning)throw TypeError(".google.api.MethodSettings.longRunning: object expected");t.longRunning=a.google.api.MethodSettings.LongRunning.fromObject(e.longRunning)}if(e.autoPopulatedFields){if(!Array.isArray(e.autoPopulatedFields))throw TypeError(".google.api.MethodSettings.autoPopulatedFields: array expected");t.autoPopulatedFields=[];for(var o=0;o<e.autoPopulatedFields.length;++o)t.autoPopulatedFields[o]=String(e.autoPopulatedFields[o])}return t},rt.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.autoPopulatedFields=[]),t.defaults&&(o.selector="",o.longRunning=null),null!=e.selector&&e.hasOwnProperty("selector")&&(o.selector=e.selector),null!=e.longRunning&&e.hasOwnProperty("longRunning")&&(o.longRunning=a.google.api.MethodSettings.LongRunning.toObject(e.longRunning,t)),e.autoPopulatedFields&&e.autoPopulatedFields.length){o.autoPopulatedFields=[];for(var r=0;r<e.autoPopulatedFields.length;++r)o.autoPopulatedFields[r]=e.autoPopulatedFields[r]}return o},rt.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},rt.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.api.MethodSettings"},rt.LongRunning=(nt.prototype.initialPollDelay=null,nt.prototype.pollDelayMultiplier=0,nt.prototype.maxPollDelay=null,nt.prototype.totalPollTimeout=null,nt.fromObject=function(e){if(e instanceof a.google.api.MethodSettings.LongRunning)return e;var t=new a.google.api.MethodSettings.LongRunning;if(null!=e.initialPollDelay){if("object"!=typeof e.initialPollDelay)throw TypeError(".google.api.MethodSettings.LongRunning.initialPollDelay: object expected");t.initialPollDelay=a.google.protobuf.Duration.fromObject(e.initialPollDelay)}if(null!=e.pollDelayMultiplier&&(t.pollDelayMultiplier=Number(e.pollDelayMultiplier)),null!=e.maxPollDelay){if("object"!=typeof e.maxPollDelay)throw TypeError(".google.api.MethodSettings.LongRunning.maxPollDelay: object expected");t.maxPollDelay=a.google.protobuf.Duration.fromObject(e.maxPollDelay)}if(null!=e.totalPollTimeout){if("object"!=typeof e.totalPollTimeout)throw TypeError(".google.api.MethodSettings.LongRunning.totalPollTimeout: object expected");t.totalPollTimeout=a.google.protobuf.Duration.fromObject(e.totalPollTimeout)}return t},nt.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.initialPollDelay=null,o.pollDelayMultiplier=0,o.maxPollDelay=null,o.totalPollTimeout=null),null!=e.initialPollDelay&&e.hasOwnProperty("initialPollDelay")&&(o.initialPollDelay=a.google.protobuf.Duration.toObject(e.initialPollDelay,t)),null!=e.pollDelayMultiplier&&e.hasOwnProperty("pollDelayMultiplier")&&(o.pollDelayMultiplier=t.json&&!isFinite(e.pollDelayMultiplier)?String(e.pollDelayMultiplier):e.pollDelayMultiplier),null!=e.maxPollDelay&&e.hasOwnProperty("maxPollDelay")&&(o.maxPollDelay=a.google.protobuf.Duration.toObject(e.maxPollDelay,t)),null!=e.totalPollTimeout&&e.hasOwnProperty("totalPollTimeout")&&(o.totalPollTimeout=a.google.protobuf.Duration.toObject(e.totalPollTimeout,t)),o},nt.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},nt.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.api.MethodSettings.LongRunning"},nt),rt),e.ClientLibraryOrganization=(o={},(n=Object.create(o))[o[0]="CLIENT_LIBRARY_ORGANIZATION_UNSPECIFIED"]="CLIENT_LIBRARY_ORGANIZATION_UNSPECIFIED",n[o[1]="CLOUD"]="CLOUD",n[o[2]="ADS"]="ADS",n[o[3]="PHOTOS"]="PHOTOS",n[o[4]="STREET_VIEW"]="STREET_VIEW",n[o[5]="SHOPPING"]="SHOPPING",n[o[6]="GEO"]="GEO",n[o[7]="GENERATIVE_AI"]="GENERATIVE_AI",n),e.ClientLibraryDestination=(o={},(n=Object.create(o))[o[0]="CLIENT_LIBRARY_DESTINATION_UNSPECIFIED"]="CLIENT_LIBRARY_DESTINATION_UNSPECIFIED",n[o[10]="GITHUB"]="GITHUB",n[o[20]="PACKAGE_MANAGER"]="PACKAGE_MANAGER",n),e.SelectiveGapicGeneration=(it.prototype.methods=i.emptyArray,it.prototype.generateOmittedAsInternal=!1,it.fromObject=function(e){if(e instanceof a.google.api.SelectiveGapicGeneration)return e;var t=new a.google.api.SelectiveGapicGeneration;if(e.methods){if(!Array.isArray(e.methods))throw TypeError(".google.api.SelectiveGapicGeneration.methods: array expected");t.methods=[];for(var o=0;o<e.methods.length;++o)t.methods[o]=String(e.methods[o])}return null!=e.generateOmittedAsInternal&&(t.generateOmittedAsInternal=Boolean(e.generateOmittedAsInternal)),t},it.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.methods=[]),t.defaults&&(o.generateOmittedAsInternal=!1),e.methods&&e.methods.length){o.methods=[];for(var r=0;r<e.methods.length;++r)o.methods[r]=e.methods[r]}return null!=e.generateOmittedAsInternal&&e.hasOwnProperty("generateOmittedAsInternal")&&(o.generateOmittedAsInternal=e.generateOmittedAsInternal),o},it.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},it.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.api.SelectiveGapicGeneration"},it),e.LaunchStage=(o={},(n=Object.create(o))[o[0]="LAUNCH_STAGE_UNSPECIFIED"]="LAUNCH_STAGE_UNSPECIFIED",n[o[6]="UNIMPLEMENTED"]="UNIMPLEMENTED",n[o[7]="PRELAUNCH"]="PRELAUNCH",n[o[1]="EARLY_ACCESS"]="EARLY_ACCESS",n[o[2]="ALPHA"]="ALPHA",n[o[3]="BETA"]="BETA",n[o[4]="GA"]="GA",n[o[5]="DEPRECATED"]="DEPRECATED",n),e.ResourceDescriptor=(w.prototype.type="",w.prototype.pattern=i.emptyArray,w.prototype.nameField="",w.prototype.history=0,w.prototype.plural="",w.prototype.singular="",w.prototype.style=i.emptyArray,w.fromObject=function(e){if(e instanceof a.google.api.ResourceDescriptor)return e;var t=new a.google.api.ResourceDescriptor;if(null!=e.type&&(t.type=String(e.type)),e.pattern){if(!Array.isArray(e.pattern))throw TypeError(".google.api.ResourceDescriptor.pattern: array expected");t.pattern=[];for(var o=0;o<e.pattern.length;++o)t.pattern[o]=String(e.pattern[o])}switch(null!=e.nameField&&(t.nameField=String(e.nameField)),e.history){default:"number"==typeof e.history&&(t.history=e.history);break;case"HISTORY_UNSPECIFIED":case 0:t.history=0;break;case"ORIGINALLY_SINGLE_PATTERN":case 1:t.history=1;break;case"FUTURE_MULTI_PATTERN":case 2:t.history=2}if(null!=e.plural&&(t.plural=String(e.plural)),null!=e.singular&&(t.singular=String(e.singular)),e.style){if(!Array.isArray(e.style))throw TypeError(".google.api.ResourceDescriptor.style: array expected");t.style=[];for(o=0;o<e.style.length;++o)switch(e.style[o]){default:if("number"==typeof e.style[o]){t.style[o]=e.style[o];break}case"STYLE_UNSPECIFIED":case 0:t.style[o]=0;break;case"DECLARATIVE_FRIENDLY":case 1:t.style[o]=1}}return t},w.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.pattern=[],o.style=[]),t.defaults&&(o.type="",o.nameField="",o.history=t.enums===String?"HISTORY_UNSPECIFIED":0,o.plural="",o.singular=""),null!=e.type&&e.hasOwnProperty("type")&&(o.type=e.type),e.pattern&&e.pattern.length){o.pattern=[];for(var r=0;r<e.pattern.length;++r)o.pattern[r]=e.pattern[r]}if(null!=e.nameField&&e.hasOwnProperty("nameField")&&(o.nameField=e.nameField),null!=e.history&&e.hasOwnProperty("history")&&(o.history=t.enums!==String||void 0===a.google.api.ResourceDescriptor.History[e.history]?e.history:a.google.api.ResourceDescriptor.History[e.history]),null!=e.plural&&e.hasOwnProperty("plural")&&(o.plural=e.plural),null!=e.singular&&e.hasOwnProperty("singular")&&(o.singular=e.singular),e.style&&e.style.length){o.style=[];for(r=0;r<e.style.length;++r)o.style[r]=t.enums!==String||void 0===a.google.api.ResourceDescriptor.Style[e.style[r]]?e.style[r]:a.google.api.ResourceDescriptor.Style[e.style[r]]}return o},w.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},w.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.api.ResourceDescriptor"},w.History=(o={},(n=Object.create(o))[o[0]="HISTORY_UNSPECIFIED"]="HISTORY_UNSPECIFIED",n[o[1]="ORIGINALLY_SINGLE_PATTERN"]="ORIGINALLY_SINGLE_PATTERN",n[o[2]="FUTURE_MULTI_PATTERN"]="FUTURE_MULTI_PATTERN",n),w.Style=(o={},(n=Object.create(o))[o[0]="STYLE_UNSPECIFIED"]="STYLE_UNSPECIFIED",n[o[1]="DECLARATIVE_FRIENDLY"]="DECLARATIVE_FRIENDLY",n),w),e.ResourceReference=(at.prototype.type="",at.prototype.childType="",at.fromObject=function(e){var t;return e instanceof a.google.api.ResourceReference?e:(t=new a.google.api.ResourceReference,null!=e.type&&(t.type=String(e.type)),null!=e.childType&&(t.childType=String(e.childType)),t)},at.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.type="",o.childType=""),null!=e.type&&e.hasOwnProperty("type")&&(o.type=e.type),null!=e.childType&&e.hasOwnProperty("childType")&&(o.childType=e.childType),o},at.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},at.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.api.ResourceReference"},at),e),C.protobuf=((t={}).FileDescriptorSet=(st.prototype.file=i.emptyArray,st.fromObject=function(e){if(e instanceof a.google.protobuf.FileDescriptorSet)return e;var t=new a.google.protobuf.FileDescriptorSet;if(e.file){if(!Array.isArray(e.file))throw TypeError(".google.protobuf.FileDescriptorSet.file: array expected");t.file=[];for(var o=0;o<e.file.length;++o){if("object"!=typeof e.file[o])throw TypeError(".google.protobuf.FileDescriptorSet.file: object expected");t.file[o]=a.google.protobuf.FileDescriptorProto.fromObject(e.file[o])}}return t},st.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.file=[]),e.file&&e.file.length){o.file=[];for(var r=0;r<e.file.length;++r)o.file[r]=a.google.protobuf.FileDescriptorProto.toObject(e.file[r],t)}return o},st.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},st.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.FileDescriptorSet"},st),t.Edition=(o={},(n=Object.create(o))[o[0]="EDITION_UNKNOWN"]="EDITION_UNKNOWN",n[o[998]="EDITION_PROTO2"]="EDITION_PROTO2",n[o[999]="EDITION_PROTO3"]="EDITION_PROTO3",n[o[1e3]="EDITION_2023"]="EDITION_2023",n[o[1001]="EDITION_2024"]="EDITION_2024",n[o[1]="EDITION_1_TEST_ONLY"]="EDITION_1_TEST_ONLY",n[o[2]="EDITION_2_TEST_ONLY"]="EDITION_2_TEST_ONLY",n[o[99997]="EDITION_99997_TEST_ONLY"]="EDITION_99997_TEST_ONLY",n[o[99998]="EDITION_99998_TEST_ONLY"]="EDITION_99998_TEST_ONLY",n[o[99999]="EDITION_99999_TEST_ONLY"]="EDITION_99999_TEST_ONLY",n[o[2147483647]="EDITION_MAX"]="EDITION_MAX",n),t.FileDescriptorProto=(N.prototype.name="",N.prototype.package="",N.prototype.dependency=i.emptyArray,N.prototype.publicDependency=i.emptyArray,N.prototype.weakDependency=i.emptyArray,N.prototype.messageType=i.emptyArray,N.prototype.enumType=i.emptyArray,N.prototype.service=i.emptyArray,N.prototype.extension=i.emptyArray,N.prototype.options=null,N.prototype.sourceCodeInfo=null,N.prototype.syntax="",N.prototype.edition=0,N.fromObject=function(e){if(e instanceof a.google.protobuf.FileDescriptorProto)return e;var t=new a.google.protobuf.FileDescriptorProto;if(null!=e.name&&(t.name=String(e.name)),null!=e.package&&(t.package=String(e.package)),e.dependency){if(!Array.isArray(e.dependency))throw TypeError(".google.protobuf.FileDescriptorProto.dependency: array expected");t.dependency=[];for(var o=0;o<e.dependency.length;++o)t.dependency[o]=String(e.dependency[o])}if(e.publicDependency){if(!Array.isArray(e.publicDependency))throw TypeError(".google.protobuf.FileDescriptorProto.publicDependency: array expected");t.publicDependency=[];for(o=0;o<e.publicDependency.length;++o)t.publicDependency[o]=0|e.publicDependency[o]}if(e.weakDependency){if(!Array.isArray(e.weakDependency))throw TypeError(".google.protobuf.FileDescriptorProto.weakDependency: array expected");t.weakDependency=[];for(o=0;o<e.weakDependency.length;++o)t.weakDependency[o]=0|e.weakDependency[o]}if(e.messageType){if(!Array.isArray(e.messageType))throw TypeError(".google.protobuf.FileDescriptorProto.messageType: array expected");t.messageType=[];for(o=0;o<e.messageType.length;++o){if("object"!=typeof e.messageType[o])throw TypeError(".google.protobuf.FileDescriptorProto.messageType: object expected");t.messageType[o]=a.google.protobuf.DescriptorProto.fromObject(e.messageType[o])}}if(e.enumType){if(!Array.isArray(e.enumType))throw TypeError(".google.protobuf.FileDescriptorProto.enumType: array expected");t.enumType=[];for(o=0;o<e.enumType.length;++o){if("object"!=typeof e.enumType[o])throw TypeError(".google.protobuf.FileDescriptorProto.enumType: object expected");t.enumType[o]=a.google.protobuf.EnumDescriptorProto.fromObject(e.enumType[o])}}if(e.service){if(!Array.isArray(e.service))throw TypeError(".google.protobuf.FileDescriptorProto.service: array expected");t.service=[];for(o=0;o<e.service.length;++o){if("object"!=typeof e.service[o])throw TypeError(".google.protobuf.FileDescriptorProto.service: object expected");t.service[o]=a.google.protobuf.ServiceDescriptorProto.fromObject(e.service[o])}}if(e.extension){if(!Array.isArray(e.extension))throw TypeError(".google.protobuf.FileDescriptorProto.extension: array expected");t.extension=[];for(o=0;o<e.extension.length;++o){if("object"!=typeof e.extension[o])throw TypeError(".google.protobuf.FileDescriptorProto.extension: object expected");t.extension[o]=a.google.protobuf.FieldDescriptorProto.fromObject(e.extension[o])}}if(null!=e.options){if("object"!=typeof e.options)throw TypeError(".google.protobuf.FileDescriptorProto.options: object expected");t.options=a.google.protobuf.FileOptions.fromObject(e.options)}if(null!=e.sourceCodeInfo){if("object"!=typeof e.sourceCodeInfo)throw TypeError(".google.protobuf.FileDescriptorProto.sourceCodeInfo: object expected");t.sourceCodeInfo=a.google.protobuf.SourceCodeInfo.fromObject(e.sourceCodeInfo)}switch(null!=e.syntax&&(t.syntax=String(e.syntax)),e.edition){default:"number"==typeof e.edition&&(t.edition=e.edition);break;case"EDITION_UNKNOWN":case 0:t.edition=0;break;case"EDITION_PROTO2":case 998:t.edition=998;break;case"EDITION_PROTO3":case 999:t.edition=999;break;case"EDITION_2023":case 1e3:t.edition=1e3;break;case"EDITION_2024":case 1001:t.edition=1001;break;case"EDITION_1_TEST_ONLY":case 1:t.edition=1;break;case"EDITION_2_TEST_ONLY":case 2:t.edition=2;break;case"EDITION_99997_TEST_ONLY":case 99997:t.edition=99997;break;case"EDITION_99998_TEST_ONLY":case 99998:t.edition=99998;break;case"EDITION_99999_TEST_ONLY":case 99999:t.edition=99999;break;case"EDITION_MAX":case 2147483647:t.edition=2147483647}return t},N.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.dependency=[],o.messageType=[],o.enumType=[],o.service=[],o.extension=[],o.publicDependency=[],o.weakDependency=[]),t.defaults&&(o.name="",o.package="",o.options=null,o.sourceCodeInfo=null,o.syntax="",o.edition=t.enums===String?"EDITION_UNKNOWN":0),null!=e.name&&e.hasOwnProperty("name")&&(o.name=e.name),null!=e.package&&e.hasOwnProperty("package")&&(o.package=e.package),e.dependency&&e.dependency.length){o.dependency=[];for(var r=0;r<e.dependency.length;++r)o.dependency[r]=e.dependency[r]}if(e.messageType&&e.messageType.length){o.messageType=[];for(r=0;r<e.messageType.length;++r)o.messageType[r]=a.google.protobuf.DescriptorProto.toObject(e.messageType[r],t)}if(e.enumType&&e.enumType.length){o.enumType=[];for(r=0;r<e.enumType.length;++r)o.enumType[r]=a.google.protobuf.EnumDescriptorProto.toObject(e.enumType[r],t)}if(e.service&&e.service.length){o.service=[];for(r=0;r<e.service.length;++r)o.service[r]=a.google.protobuf.ServiceDescriptorProto.toObject(e.service[r],t)}if(e.extension&&e.extension.length){o.extension=[];for(r=0;r<e.extension.length;++r)o.extension[r]=a.google.protobuf.FieldDescriptorProto.toObject(e.extension[r],t)}if(null!=e.options&&e.hasOwnProperty("options")&&(o.options=a.google.protobuf.FileOptions.toObject(e.options,t)),null!=e.sourceCodeInfo&&e.hasOwnProperty("sourceCodeInfo")&&(o.sourceCodeInfo=a.google.protobuf.SourceCodeInfo.toObject(e.sourceCodeInfo,t)),e.publicDependency&&e.publicDependency.length){o.publicDependency=[];for(r=0;r<e.publicDependency.length;++r)o.publicDependency[r]=e.publicDependency[r]}if(e.weakDependency&&e.weakDependency.length){o.weakDependency=[];for(r=0;r<e.weakDependency.length;++r)o.weakDependency[r]=e.weakDependency[r]}return null!=e.syntax&&e.hasOwnProperty("syntax")&&(o.syntax=e.syntax),null!=e.edition&&e.hasOwnProperty("edition")&&(o.edition=t.enums!==String||void 0===a.google.protobuf.Edition[e.edition]?e.edition:a.google.protobuf.Edition[e.edition]),o},N.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},N.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.FileDescriptorProto"},N),t.DescriptorProto=(P.prototype.name="",P.prototype.field=i.emptyArray,P.prototype.extension=i.emptyArray,P.prototype.nestedType=i.emptyArray,P.prototype.enumType=i.emptyArray,P.prototype.extensionRange=i.emptyArray,P.prototype.oneofDecl=i.emptyArray,P.prototype.options=null,P.prototype.reservedRange=i.emptyArray,P.prototype.reservedName=i.emptyArray,P.fromObject=function(e){if(e instanceof a.google.protobuf.DescriptorProto)return e;var t=new a.google.protobuf.DescriptorProto;if(null!=e.name&&(t.name=String(e.name)),e.field){if(!Array.isArray(e.field))throw TypeError(".google.protobuf.DescriptorProto.field: array expected");t.field=[];for(var o=0;o<e.field.length;++o){if("object"!=typeof e.field[o])throw TypeError(".google.protobuf.DescriptorProto.field: object expected");t.field[o]=a.google.protobuf.FieldDescriptorProto.fromObject(e.field[o])}}if(e.extension){if(!Array.isArray(e.extension))throw TypeError(".google.protobuf.DescriptorProto.extension: array expected");t.extension=[];for(o=0;o<e.extension.length;++o){if("object"!=typeof e.extension[o])throw TypeError(".google.protobuf.DescriptorProto.extension: object expected");t.extension[o]=a.google.protobuf.FieldDescriptorProto.fromObject(e.extension[o])}}if(e.nestedType){if(!Array.isArray(e.nestedType))throw TypeError(".google.protobuf.DescriptorProto.nestedType: array expected");t.nestedType=[];for(o=0;o<e.nestedType.length;++o){if("object"!=typeof e.nestedType[o])throw TypeError(".google.protobuf.DescriptorProto.nestedType: object expected");t.nestedType[o]=a.google.protobuf.DescriptorProto.fromObject(e.nestedType[o])}}if(e.enumType){if(!Array.isArray(e.enumType))throw TypeError(".google.protobuf.DescriptorProto.enumType: array expected");t.enumType=[];for(o=0;o<e.enumType.length;++o){if("object"!=typeof e.enumType[o])throw TypeError(".google.protobuf.DescriptorProto.enumType: object expected");t.enumType[o]=a.google.protobuf.EnumDescriptorProto.fromObject(e.enumType[o])}}if(e.extensionRange){if(!Array.isArray(e.extensionRange))throw TypeError(".google.protobuf.DescriptorProto.extensionRange: array expected");t.extensionRange=[];for(o=0;o<e.extensionRange.length;++o){if("object"!=typeof e.extensionRange[o])throw TypeError(".google.protobuf.DescriptorProto.extensionRange: object expected");t.extensionRange[o]=a.google.protobuf.DescriptorProto.ExtensionRange.fromObject(e.extensionRange[o])}}if(e.oneofDecl){if(!Array.isArray(e.oneofDecl))throw TypeError(".google.protobuf.DescriptorProto.oneofDecl: array expected");t.oneofDecl=[];for(o=0;o<e.oneofDecl.length;++o){if("object"!=typeof e.oneofDecl[o])throw TypeError(".google.protobuf.DescriptorProto.oneofDecl: object expected");t.oneofDecl[o]=a.google.protobuf.OneofDescriptorProto.fromObject(e.oneofDecl[o])}}if(null!=e.options){if("object"!=typeof e.options)throw TypeError(".google.protobuf.DescriptorProto.options: object expected");t.options=a.google.protobuf.MessageOptions.fromObject(e.options)}if(e.reservedRange){if(!Array.isArray(e.reservedRange))throw TypeError(".google.protobuf.DescriptorProto.reservedRange: array expected");t.reservedRange=[];for(o=0;o<e.reservedRange.length;++o){if("object"!=typeof e.reservedRange[o])throw TypeError(".google.protobuf.DescriptorProto.reservedRange: object expected");t.reservedRange[o]=a.google.protobuf.DescriptorProto.ReservedRange.fromObject(e.reservedRange[o])}}if(e.reservedName){if(!Array.isArray(e.reservedName))throw TypeError(".google.protobuf.DescriptorProto.reservedName: array expected");t.reservedName=[];for(o=0;o<e.reservedName.length;++o)t.reservedName[o]=String(e.reservedName[o])}return t},P.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.field=[],o.nestedType=[],o.enumType=[],o.extensionRange=[],o.extension=[],o.oneofDecl=[],o.reservedRange=[],o.reservedName=[]),t.defaults&&(o.name="",o.options=null),null!=e.name&&e.hasOwnProperty("name")&&(o.name=e.name),e.field&&e.field.length){o.field=[];for(var r=0;r<e.field.length;++r)o.field[r]=a.google.protobuf.FieldDescriptorProto.toObject(e.field[r],t)}if(e.nestedType&&e.nestedType.length){o.nestedType=[];for(r=0;r<e.nestedType.length;++r)o.nestedType[r]=a.google.protobuf.DescriptorProto.toObject(e.nestedType[r],t)}if(e.enumType&&e.enumType.length){o.enumType=[];for(r=0;r<e.enumType.length;++r)o.enumType[r]=a.google.protobuf.EnumDescriptorProto.toObject(e.enumType[r],t)}if(e.extensionRange&&e.extensionRange.length){o.extensionRange=[];for(r=0;r<e.extensionRange.length;++r)o.extensionRange[r]=a.google.protobuf.DescriptorProto.ExtensionRange.toObject(e.extensionRange[r],t)}if(e.extension&&e.extension.length){o.extension=[];for(r=0;r<e.extension.length;++r)o.extension[r]=a.google.protobuf.FieldDescriptorProto.toObject(e.extension[r],t)}if(null!=e.options&&e.hasOwnProperty("options")&&(o.options=a.google.protobuf.MessageOptions.toObject(e.options,t)),e.oneofDecl&&e.oneofDecl.length){o.oneofDecl=[];for(r=0;r<e.oneofDecl.length;++r)o.oneofDecl[r]=a.google.protobuf.OneofDescriptorProto.toObject(e.oneofDecl[r],t)}if(e.reservedRange&&e.reservedRange.length){o.reservedRange=[];for(r=0;r<e.reservedRange.length;++r)o.reservedRange[r]=a.google.protobuf.DescriptorProto.ReservedRange.toObject(e.reservedRange[r],t)}if(e.reservedName&&e.reservedName.length){o.reservedName=[];for(r=0;r<e.reservedName.length;++r)o.reservedName[r]=e.reservedName[r]}return o},P.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},P.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.DescriptorProto"},P.ExtensionRange=(lt.prototype.start=0,lt.prototype.end=0,lt.prototype.options=null,lt.fromObject=function(e){if(e instanceof a.google.protobuf.DescriptorProto.ExtensionRange)return e;var t=new a.google.protobuf.DescriptorProto.ExtensionRange;if(null!=e.start&&(t.start=0|e.start),null!=e.end&&(t.end=0|e.end),null!=e.options){if("object"!=typeof e.options)throw TypeError(".google.protobuf.DescriptorProto.ExtensionRange.options: object expected");t.options=a.google.protobuf.ExtensionRangeOptions.fromObject(e.options)}return t},lt.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.start=0,o.end=0,o.options=null),null!=e.start&&e.hasOwnProperty("start")&&(o.start=e.start),null!=e.end&&e.hasOwnProperty("end")&&(o.end=e.end),null!=e.options&&e.hasOwnProperty("options")&&(o.options=a.google.protobuf.ExtensionRangeOptions.toObject(e.options,t)),o},lt.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},lt.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.DescriptorProto.ExtensionRange"},lt),P.ReservedRange=(ut.prototype.start=0,ut.prototype.end=0,ut.fromObject=function(e){var t;return e instanceof a.google.protobuf.DescriptorProto.ReservedRange?e:(t=new a.google.protobuf.DescriptorProto.ReservedRange,null!=e.start&&(t.start=0|e.start),null!=e.end&&(t.end=0|e.end),t)},ut.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.start=0,o.end=0),null!=e.start&&e.hasOwnProperty("start")&&(o.start=e.start),null!=e.end&&e.hasOwnProperty("end")&&(o.end=e.end),o},ut.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},ut.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.DescriptorProto.ReservedRange"},ut),P),t.ExtensionRangeOptions=(pt.prototype.uninterpretedOption=i.emptyArray,pt.prototype.declaration=i.emptyArray,pt.prototype.features=null,pt.prototype.verification=1,pt.fromObject=function(e){if(e instanceof a.google.protobuf.ExtensionRangeOptions)return e;var t=new a.google.protobuf.ExtensionRangeOptions;if(e.uninterpretedOption){if(!Array.isArray(e.uninterpretedOption))throw TypeError(".google.protobuf.ExtensionRangeOptions.uninterpretedOption: array expected");t.uninterpretedOption=[];for(var o=0;o<e.uninterpretedOption.length;++o){if("object"!=typeof e.uninterpretedOption[o])throw TypeError(".google.protobuf.ExtensionRangeOptions.uninterpretedOption: object expected");t.uninterpretedOption[o]=a.google.protobuf.UninterpretedOption.fromObject(e.uninterpretedOption[o])}}if(e.declaration){if(!Array.isArray(e.declaration))throw TypeError(".google.protobuf.ExtensionRangeOptions.declaration: array expected");t.declaration=[];for(o=0;o<e.declaration.length;++o){if("object"!=typeof e.declaration[o])throw TypeError(".google.protobuf.ExtensionRangeOptions.declaration: object expected");t.declaration[o]=a.google.protobuf.ExtensionRangeOptions.Declaration.fromObject(e.declaration[o])}}if(null!=e.features){if("object"!=typeof e.features)throw TypeError(".google.protobuf.ExtensionRangeOptions.features: object expected");t.features=a.google.protobuf.FeatureSet.fromObject(e.features)}switch(e.verification){case"DECLARATION":case 0:t.verification=0;break;default:"number"==typeof e.verification&&(t.verification=e.verification);break;case"UNVERIFIED":case 1:t.verification=1}return t},pt.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.declaration=[],o.uninterpretedOption=[]),t.defaults&&(o.verification=t.enums===String?"UNVERIFIED":1,o.features=null),e.declaration&&e.declaration.length){o.declaration=[];for(var r=0;r<e.declaration.length;++r)o.declaration[r]=a.google.protobuf.ExtensionRangeOptions.Declaration.toObject(e.declaration[r],t)}if(null!=e.verification&&e.hasOwnProperty("verification")&&(o.verification=t.enums!==String||void 0===a.google.protobuf.ExtensionRangeOptions.VerificationState[e.verification]?e.verification:a.google.protobuf.ExtensionRangeOptions.VerificationState[e.verification]),null!=e.features&&e.hasOwnProperty("features")&&(o.features=a.google.protobuf.FeatureSet.toObject(e.features,t)),e.uninterpretedOption&&e.uninterpretedOption.length){o.uninterpretedOption=[];for(r=0;r<e.uninterpretedOption.length;++r)o.uninterpretedOption[r]=a.google.protobuf.UninterpretedOption.toObject(e.uninterpretedOption[r],t)}return o},pt.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},pt.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.ExtensionRangeOptions"},pt.Declaration=(ct.prototype.number=0,ct.prototype.fullName="",ct.prototype.type="",ct.prototype.reserved=!1,ct.prototype.repeated=!1,ct.fromObject=function(e){var t;return e instanceof a.google.protobuf.ExtensionRangeOptions.Declaration?e:(t=new a.google.protobuf.ExtensionRangeOptions.Declaration,null!=e.number&&(t.number=0|e.number),null!=e.fullName&&(t.fullName=String(e.fullName)),null!=e.type&&(t.type=String(e.type)),null!=e.reserved&&(t.reserved=Boolean(e.reserved)),null!=e.repeated&&(t.repeated=Boolean(e.repeated)),t)},ct.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.number=0,o.fullName="",o.type="",o.reserved=!1,o.repeated=!1),null!=e.number&&e.hasOwnProperty("number")&&(o.number=e.number),null!=e.fullName&&e.hasOwnProperty("fullName")&&(o.fullName=e.fullName),null!=e.type&&e.hasOwnProperty("type")&&(o.type=e.type),null!=e.reserved&&e.hasOwnProperty("reserved")&&(o.reserved=e.reserved),null!=e.repeated&&e.hasOwnProperty("repeated")&&(o.repeated=e.repeated),o},ct.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},ct.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.ExtensionRangeOptions.Declaration"},ct),pt.VerificationState=(o={},(n=Object.create(o))[o[0]="DECLARATION"]="DECLARATION",n[o[1]="UNVERIFIED"]="UNVERIFIED",n),pt),t.FieldDescriptorProto=(R.prototype.name="",R.prototype.number=0,R.prototype.label=1,R.prototype.type=1,R.prototype.typeName="",R.prototype.extendee="",R.prototype.defaultValue="",R.prototype.oneofIndex=0,R.prototype.jsonName="",R.prototype.options=null,R.prototype.proto3Optional=!1,R.fromObject=function(e){if(e instanceof a.google.protobuf.FieldDescriptorProto)return e;var t=new a.google.protobuf.FieldDescriptorProto;switch(null!=e.name&&(t.name=String(e.name)),null!=e.number&&(t.number=0|e.number),e.label){default:"number"==typeof e.label&&(t.label=e.label);break;case"LABEL_OPTIONAL":case 1:t.label=1;break;case"LABEL_REPEATED":case 3:t.label=3;break;case"LABEL_REQUIRED":case 2:t.label=2}switch(e.type){default:"number"==typeof e.type&&(t.type=e.type);break;case"TYPE_DOUBLE":case 1:t.type=1;break;case"TYPE_FLOAT":case 2:t.type=2;break;case"TYPE_INT64":case 3:t.type=3;break;case"TYPE_UINT64":case 4:t.type=4;break;case"TYPE_INT32":case 5:t.type=5;break;case"TYPE_FIXED64":case 6:t.type=6;break;case"TYPE_FIXED32":case 7:t.type=7;break;case"TYPE_BOOL":case 8:t.type=8;break;case"TYPE_STRING":case 9:t.type=9;break;case"TYPE_GROUP":case 10:t.type=10;break;case"TYPE_MESSAGE":case 11:t.type=11;break;case"TYPE_BYTES":case 12:t.type=12;break;case"TYPE_UINT32":case 13:t.type=13;break;case"TYPE_ENUM":case 14:t.type=14;break;case"TYPE_SFIXED32":case 15:t.type=15;break;case"TYPE_SFIXED64":case 16:t.type=16;break;case"TYPE_SINT32":case 17:t.type=17;break;case"TYPE_SINT64":case 18:t.type=18}if(null!=e.typeName&&(t.typeName=String(e.typeName)),null!=e.extendee&&(t.extendee=String(e.extendee)),null!=e.defaultValue&&(t.defaultValue=String(e.defaultValue)),null!=e.oneofIndex&&(t.oneofIndex=0|e.oneofIndex),null!=e.jsonName&&(t.jsonName=String(e.jsonName)),null!=e.options){if("object"!=typeof e.options)throw TypeError(".google.protobuf.FieldDescriptorProto.options: object expected");t.options=a.google.protobuf.FieldOptions.fromObject(e.options)}return null!=e.proto3Optional&&(t.proto3Optional=Boolean(e.proto3Optional)),t},R.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.name="",o.extendee="",o.number=0,o.label=t.enums===String?"LABEL_OPTIONAL":1,o.type=t.enums===String?"TYPE_DOUBLE":1,o.typeName="",o.defaultValue="",o.options=null,o.oneofIndex=0,o.jsonName="",o.proto3Optional=!1),null!=e.name&&e.hasOwnProperty("name")&&(o.name=e.name),null!=e.extendee&&e.hasOwnProperty("extendee")&&(o.extendee=e.extendee),null!=e.number&&e.hasOwnProperty("number")&&(o.number=e.number),null!=e.label&&e.hasOwnProperty("label")&&(o.label=t.enums!==String||void 0===a.google.protobuf.FieldDescriptorProto.Label[e.label]?e.label:a.google.protobuf.FieldDescriptorProto.Label[e.label]),null!=e.type&&e.hasOwnProperty("type")&&(o.type=t.enums!==String||void 0===a.google.protobuf.FieldDescriptorProto.Type[e.type]?e.type:a.google.protobuf.FieldDescriptorProto.Type[e.type]),null!=e.typeName&&e.hasOwnProperty("typeName")&&(o.typeName=e.typeName),null!=e.defaultValue&&e.hasOwnProperty("defaultValue")&&(o.defaultValue=e.defaultValue),null!=e.options&&e.hasOwnProperty("options")&&(o.options=a.google.protobuf.FieldOptions.toObject(e.options,t)),null!=e.oneofIndex&&e.hasOwnProperty("oneofIndex")&&(o.oneofIndex=e.oneofIndex),null!=e.jsonName&&e.hasOwnProperty("jsonName")&&(o.jsonName=e.jsonName),null!=e.proto3Optional&&e.hasOwnProperty("proto3Optional")&&(o.proto3Optional=e.proto3Optional),o},R.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},R.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.FieldDescriptorProto"},R.Type=(o={},(n=Object.create(o))[o[1]="TYPE_DOUBLE"]="TYPE_DOUBLE",n[o[2]="TYPE_FLOAT"]="TYPE_FLOAT",n[o[3]="TYPE_INT64"]="TYPE_INT64",n[o[4]="TYPE_UINT64"]="TYPE_UINT64",n[o[5]="TYPE_INT32"]="TYPE_INT32",n[o[6]="TYPE_FIXED64"]="TYPE_FIXED64",n[o[7]="TYPE_FIXED32"]="TYPE_FIXED32",n[o[8]="TYPE_BOOL"]="TYPE_BOOL",n[o[9]="TYPE_STRING"]="TYPE_STRING",n[o[10]="TYPE_GROUP"]="TYPE_GROUP",n[o[11]="TYPE_MESSAGE"]="TYPE_MESSAGE",n[o[12]="TYPE_BYTES"]="TYPE_BYTES",n[o[13]="TYPE_UINT32"]="TYPE_UINT32",n[o[14]="TYPE_ENUM"]="TYPE_ENUM",n[o[15]="TYPE_SFIXED32"]="TYPE_SFIXED32",n[o[16]="TYPE_SFIXED64"]="TYPE_SFIXED64",n[o[17]="TYPE_SINT32"]="TYPE_SINT32",n[o[18]="TYPE_SINT64"]="TYPE_SINT64",n),R.Label=(o={},(n=Object.create(o))[o[1]="LABEL_OPTIONAL"]="LABEL_OPTIONAL",n[o[3]="LABEL_REPEATED"]="LABEL_REPEATED",n[o[2]="LABEL_REQUIRED"]="LABEL_REQUIRED",n),R),t.OneofDescriptorProto=(gt.prototype.name="",gt.prototype.options=null,gt.fromObject=function(e){if(e instanceof a.google.protobuf.OneofDescriptorProto)return e;var t=new a.google.protobuf.OneofDescriptorProto;if(null!=e.name&&(t.name=String(e.name)),null!=e.options){if("object"!=typeof e.options)throw TypeError(".google.protobuf.OneofDescriptorProto.options: object expected");t.options=a.google.protobuf.OneofOptions.fromObject(e.options)}return t},gt.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.name="",o.options=null),null!=e.name&&e.hasOwnProperty("name")&&(o.name=e.name),null!=e.options&&e.hasOwnProperty("options")&&(o.options=a.google.protobuf.OneofOptions.toObject(e.options,t)),o},gt.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},gt.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.OneofDescriptorProto"},gt),t.EnumDescriptorProto=(ft.prototype.name="",ft.prototype.value=i.emptyArray,ft.prototype.options=null,ft.prototype.reservedRange=i.emptyArray,ft.prototype.reservedName=i.emptyArray,ft.fromObject=function(e){if(e instanceof a.google.protobuf.EnumDescriptorProto)return e;var t=new a.google.protobuf.EnumDescriptorProto;if(null!=e.name&&(t.name=String(e.name)),e.value){if(!Array.isArray(e.value))throw TypeError(".google.protobuf.EnumDescriptorProto.value: array expected");t.value=[];for(var o=0;o<e.value.length;++o){if("object"!=typeof e.value[o])throw TypeError(".google.protobuf.EnumDescriptorProto.value: object expected");t.value[o]=a.google.protobuf.EnumValueDescriptorProto.fromObject(e.value[o])}}if(null!=e.options){if("object"!=typeof e.options)throw TypeError(".google.protobuf.EnumDescriptorProto.options: object expected");t.options=a.google.protobuf.EnumOptions.fromObject(e.options)}if(e.reservedRange){if(!Array.isArray(e.reservedRange))throw TypeError(".google.protobuf.EnumDescriptorProto.reservedRange: array expected");t.reservedRange=[];for(o=0;o<e.reservedRange.length;++o){if("object"!=typeof e.reservedRange[o])throw TypeError(".google.protobuf.EnumDescriptorProto.reservedRange: object expected");t.reservedRange[o]=a.google.protobuf.EnumDescriptorProto.EnumReservedRange.fromObject(e.reservedRange[o])}}if(e.reservedName){if(!Array.isArray(e.reservedName))throw TypeError(".google.protobuf.EnumDescriptorProto.reservedName: array expected");t.reservedName=[];for(o=0;o<e.reservedName.length;++o)t.reservedName[o]=String(e.reservedName[o])}return t},ft.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.value=[],o.reservedRange=[],o.reservedName=[]),t.defaults&&(o.name="",o.options=null),null!=e.name&&e.hasOwnProperty("name")&&(o.name=e.name),e.value&&e.value.length){o.value=[];for(var r=0;r<e.value.length;++r)o.value[r]=a.google.protobuf.EnumValueDescriptorProto.toObject(e.value[r],t)}if(null!=e.options&&e.hasOwnProperty("options")&&(o.options=a.google.protobuf.EnumOptions.toObject(e.options,t)),e.reservedRange&&e.reservedRange.length){o.reservedRange=[];for(r=0;r<e.reservedRange.length;++r)o.reservedRange[r]=a.google.protobuf.EnumDescriptorProto.EnumReservedRange.toObject(e.reservedRange[r],t)}if(e.reservedName&&e.reservedName.length){o.reservedName=[];for(r=0;r<e.reservedName.length;++r)o.reservedName[r]=e.reservedName[r]}return o},ft.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},ft.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.EnumDescriptorProto"},ft.EnumReservedRange=(dt.prototype.start=0,dt.prototype.end=0,dt.fromObject=function(e){var t;return e instanceof a.google.protobuf.EnumDescriptorProto.EnumReservedRange?e:(t=new a.google.protobuf.EnumDescriptorProto.EnumReservedRange,null!=e.start&&(t.start=0|e.start),null!=e.end&&(t.end=0|e.end),t)},dt.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.start=0,o.end=0),null!=e.start&&e.hasOwnProperty("start")&&(o.start=e.start),null!=e.end&&e.hasOwnProperty("end")&&(o.end=e.end),o},dt.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},dt.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.EnumDescriptorProto.EnumReservedRange"},dt),ft),t.EnumValueDescriptorProto=(yt.prototype.name="",yt.prototype.number=0,yt.prototype.options=null,yt.fromObject=function(e){if(e instanceof a.google.protobuf.EnumValueDescriptorProto)return e;var t=new a.google.protobuf.EnumValueDescriptorProto;if(null!=e.name&&(t.name=String(e.name)),null!=e.number&&(t.number=0|e.number),null!=e.options){if("object"!=typeof e.options)throw TypeError(".google.protobuf.EnumValueDescriptorProto.options: object expected");t.options=a.google.protobuf.EnumValueOptions.fromObject(e.options)}return t},yt.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.name="",o.number=0,o.options=null),null!=e.name&&e.hasOwnProperty("name")&&(o.name=e.name),null!=e.number&&e.hasOwnProperty("number")&&(o.number=e.number),null!=e.options&&e.hasOwnProperty("options")&&(o.options=a.google.protobuf.EnumValueOptions.toObject(e.options,t)),o},yt.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},yt.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.EnumValueDescriptorProto"},yt),t.ServiceDescriptorProto=(mt.prototype.name="",mt.prototype.method=i.emptyArray,mt.prototype.options=null,mt.fromObject=function(e){if(e instanceof a.google.protobuf.ServiceDescriptorProto)return e;var t=new a.google.protobuf.ServiceDescriptorProto;if(null!=e.name&&(t.name=String(e.name)),e.method){if(!Array.isArray(e.method))throw TypeError(".google.protobuf.ServiceDescriptorProto.method: array expected");t.method=[];for(var o=0;o<e.method.length;++o){if("object"!=typeof e.method[o])throw TypeError(".google.protobuf.ServiceDescriptorProto.method: object expected");t.method[o]=a.google.protobuf.MethodDescriptorProto.fromObject(e.method[o])}}if(null!=e.options){if("object"!=typeof e.options)throw TypeError(".google.protobuf.ServiceDescriptorProto.options: object expected");t.options=a.google.protobuf.ServiceOptions.fromObject(e.options)}return t},mt.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.method=[]),t.defaults&&(o.name="",o.options=null),null!=e.name&&e.hasOwnProperty("name")&&(o.name=e.name),e.method&&e.method.length){o.method=[];for(var r=0;r<e.method.length;++r)o.method[r]=a.google.protobuf.MethodDescriptorProto.toObject(e.method[r],t)}return null!=e.options&&e.hasOwnProperty("options")&&(o.options=a.google.protobuf.ServiceOptions.toObject(e.options,t)),o},mt.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},mt.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.ServiceDescriptorProto"},mt),t.MethodDescriptorProto=(bt.prototype.name="",bt.prototype.inputType="",bt.prototype.outputType="",bt.prototype.options=null,bt.prototype.clientStreaming=!1,bt.prototype.serverStreaming=!1,bt.fromObject=function(e){if(e instanceof a.google.protobuf.MethodDescriptorProto)return e;var t=new a.google.protobuf.MethodDescriptorProto;if(null!=e.name&&(t.name=String(e.name)),null!=e.inputType&&(t.inputType=String(e.inputType)),null!=e.outputType&&(t.outputType=String(e.outputType)),null!=e.options){if("object"!=typeof e.options)throw TypeError(".google.protobuf.MethodDescriptorProto.options: object expected");t.options=a.google.protobuf.MethodOptions.fromObject(e.options)}return null!=e.clientStreaming&&(t.clientStreaming=Boolean(e.clientStreaming)),null!=e.serverStreaming&&(t.serverStreaming=Boolean(e.serverStreaming)),t},bt.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.name="",o.inputType="",o.outputType="",o.options=null,o.clientStreaming=!1,o.serverStreaming=!1),null!=e.name&&e.hasOwnProperty("name")&&(o.name=e.name),null!=e.inputType&&e.hasOwnProperty("inputType")&&(o.inputType=e.inputType),null!=e.outputType&&e.hasOwnProperty("outputType")&&(o.outputType=e.outputType),null!=e.options&&e.hasOwnProperty("options")&&(o.options=a.google.protobuf.MethodOptions.toObject(e.options,t)),null!=e.clientStreaming&&e.hasOwnProperty("clientStreaming")&&(o.clientStreaming=e.clientStreaming),null!=e.serverStreaming&&e.hasOwnProperty("serverStreaming")&&(o.serverStreaming=e.serverStreaming),o},bt.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},bt.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.MethodDescriptorProto"},bt),t.FileOptions=(D.prototype.javaPackage="",D.prototype.javaOuterClassname="",D.prototype.javaMultipleFiles=!1,D.prototype.javaGenerateEqualsAndHash=!1,D.prototype.javaStringCheckUtf8=!1,D.prototype.optimizeFor=1,D.prototype.goPackage="",D.prototype.ccGenericServices=!1,D.prototype.javaGenericServices=!1,D.prototype.pyGenericServices=!1,D.prototype.deprecated=!1,D.prototype.ccEnableArenas=!0,D.prototype.objcClassPrefix="",D.prototype.csharpNamespace="",D.prototype.swiftPrefix="",D.prototype.phpClassPrefix="",D.prototype.phpNamespace="",D.prototype.phpMetadataNamespace="",D.prototype.rubyPackage="",D.prototype.features=null,D.prototype.uninterpretedOption=i.emptyArray,D.prototype[".google.api.resourceDefinition"]=i.emptyArray,D.fromObject=function(e){if(e instanceof a.google.protobuf.FileOptions)return e;var t=new a.google.protobuf.FileOptions;switch(null!=e.javaPackage&&(t.javaPackage=String(e.javaPackage)),null!=e.javaOuterClassname&&(t.javaOuterClassname=String(e.javaOuterClassname)),null!=e.javaMultipleFiles&&(t.javaMultipleFiles=Boolean(e.javaMultipleFiles)),null!=e.javaGenerateEqualsAndHash&&(t.javaGenerateEqualsAndHash=Boolean(e.javaGenerateEqualsAndHash)),null!=e.javaStringCheckUtf8&&(t.javaStringCheckUtf8=Boolean(e.javaStringCheckUtf8)),e.optimizeFor){default:"number"==typeof e.optimizeFor&&(t.optimizeFor=e.optimizeFor);break;case"SPEED":case 1:t.optimizeFor=1;break;case"CODE_SIZE":case 2:t.optimizeFor=2;break;case"LITE_RUNTIME":case 3:t.optimizeFor=3}if(null!=e.goPackage&&(t.goPackage=String(e.goPackage)),null!=e.ccGenericServices&&(t.ccGenericServices=Boolean(e.ccGenericServices)),null!=e.javaGenericServices&&(t.javaGenericServices=Boolean(e.javaGenericServices)),null!=e.pyGenericServices&&(t.pyGenericServices=Boolean(e.pyGenericServices)),null!=e.deprecated&&(t.deprecated=Boolean(e.deprecated)),null!=e.ccEnableArenas&&(t.ccEnableArenas=Boolean(e.ccEnableArenas)),null!=e.objcClassPrefix&&(t.objcClassPrefix=String(e.objcClassPrefix)),null!=e.csharpNamespace&&(t.csharpNamespace=String(e.csharpNamespace)),null!=e.swiftPrefix&&(t.swiftPrefix=String(e.swiftPrefix)),null!=e.phpClassPrefix&&(t.phpClassPrefix=String(e.phpClassPrefix)),null!=e.phpNamespace&&(t.phpNamespace=String(e.phpNamespace)),null!=e.phpMetadataNamespace&&(t.phpMetadataNamespace=String(e.phpMetadataNamespace)),null!=e.rubyPackage&&(t.rubyPackage=String(e.rubyPackage)),null!=e.features){if("object"!=typeof e.features)throw TypeError(".google.protobuf.FileOptions.features: object expected");t.features=a.google.protobuf.FeatureSet.fromObject(e.features)}if(e.uninterpretedOption){if(!Array.isArray(e.uninterpretedOption))throw TypeError(".google.protobuf.FileOptions.uninterpretedOption: array expected");t.uninterpretedOption=[];for(var o=0;o<e.uninterpretedOption.length;++o){if("object"!=typeof e.uninterpretedOption[o])throw TypeError(".google.protobuf.FileOptions.uninterpretedOption: object expected");t.uninterpretedOption[o]=a.google.protobuf.UninterpretedOption.fromObject(e.uninterpretedOption[o])}}if(e[".google.api.resourceDefinition"]){if(!Array.isArray(e[".google.api.resourceDefinition"]))throw TypeError(".google.protobuf.FileOptions..google.api.resourceDefinition: array expected");t[".google.api.resourceDefinition"]=[];for(o=0;o<e[".google.api.resourceDefinition"].length;++o){if("object"!=typeof e[".google.api.resourceDefinition"][o])throw TypeError(".google.protobuf.FileOptions..google.api.resourceDefinition: object expected");t[".google.api.resourceDefinition"][o]=a.google.api.ResourceDescriptor.fromObject(e[".google.api.resourceDefinition"][o])}}return t},D.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.uninterpretedOption=[],o[".google.api.resourceDefinition"]=[]),t.defaults&&(o.javaPackage="",o.javaOuterClassname="",o.optimizeFor=t.enums===String?"SPEED":1,o.javaMultipleFiles=!1,o.goPackage="",o.ccGenericServices=!1,o.javaGenericServices=!1,o.pyGenericServices=!1,o.javaGenerateEqualsAndHash=!1,o.deprecated=!1,o.javaStringCheckUtf8=!1,o.ccEnableArenas=!0,o.objcClassPrefix="",o.csharpNamespace="",o.swiftPrefix="",o.phpClassPrefix="",o.phpNamespace="",o.phpMetadataNamespace="",o.rubyPackage="",o.features=null),null!=e.javaPackage&&e.hasOwnProperty("javaPackage")&&(o.javaPackage=e.javaPackage),null!=e.javaOuterClassname&&e.hasOwnProperty("javaOuterClassname")&&(o.javaOuterClassname=e.javaOuterClassname),null!=e.optimizeFor&&e.hasOwnProperty("optimizeFor")&&(o.optimizeFor=t.enums!==String||void 0===a.google.protobuf.FileOptions.OptimizeMode[e.optimizeFor]?e.optimizeFor:a.google.protobuf.FileOptions.OptimizeMode[e.optimizeFor]),null!=e.javaMultipleFiles&&e.hasOwnProperty("javaMultipleFiles")&&(o.javaMultipleFiles=e.javaMultipleFiles),null!=e.goPackage&&e.hasOwnProperty("goPackage")&&(o.goPackage=e.goPackage),null!=e.ccGenericServices&&e.hasOwnProperty("ccGenericServices")&&(o.ccGenericServices=e.ccGenericServices),null!=e.javaGenericServices&&e.hasOwnProperty("javaGenericServices")&&(o.javaGenericServices=e.javaGenericServices),null!=e.pyGenericServices&&e.hasOwnProperty("pyGenericServices")&&(o.pyGenericServices=e.pyGenericServices),null!=e.javaGenerateEqualsAndHash&&e.hasOwnProperty("javaGenerateEqualsAndHash")&&(o.javaGenerateEqualsAndHash=e.javaGenerateEqualsAndHash),null!=e.deprecated&&e.hasOwnProperty("deprecated")&&(o.deprecated=e.deprecated),null!=e.javaStringCheckUtf8&&e.hasOwnProperty("javaStringCheckUtf8")&&(o.javaStringCheckUtf8=e.javaStringCheckUtf8),null!=e.ccEnableArenas&&e.hasOwnProperty("ccEnableArenas")&&(o.ccEnableArenas=e.ccEnableArenas),null!=e.objcClassPrefix&&e.hasOwnProperty("objcClassPrefix")&&(o.objcClassPrefix=e.objcClassPrefix),null!=e.csharpNamespace&&e.hasOwnProperty("csharpNamespace")&&(o.csharpNamespace=e.csharpNamespace),null!=e.swiftPrefix&&e.hasOwnProperty("swiftPrefix")&&(o.swiftPrefix=e.swiftPrefix),null!=e.phpClassPrefix&&e.hasOwnProperty("phpClassPrefix")&&(o.phpClassPrefix=e.phpClassPrefix),null!=e.phpNamespace&&e.hasOwnProperty("phpNamespace")&&(o.phpNamespace=e.phpNamespace),null!=e.phpMetadataNamespace&&e.hasOwnProperty("phpMetadataNamespace")&&(o.phpMetadataNamespace=e.phpMetadataNamespace),null!=e.rubyPackage&&e.hasOwnProperty("rubyPackage")&&(o.rubyPackage=e.rubyPackage),null!=e.features&&e.hasOwnProperty("features")&&(o.features=a.google.protobuf.FeatureSet.toObject(e.features,t)),e.uninterpretedOption&&e.uninterpretedOption.length){o.uninterpretedOption=[];for(var r=0;r<e.uninterpretedOption.length;++r)o.uninterpretedOption[r]=a.google.protobuf.UninterpretedOption.toObject(e.uninterpretedOption[r],t)}if(e[".google.api.resourceDefinition"]&&e[".google.api.resourceDefinition"].length){o[".google.api.resourceDefinition"]=[];for(r=0;r<e[".google.api.resourceDefinition"].length;++r)o[".google.api.resourceDefinition"][r]=a.google.api.ResourceDescriptor.toObject(e[".google.api.resourceDefinition"][r],t)}return o},D.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},D.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.FileOptions"},D.OptimizeMode=(o={},(n=Object.create(o))[o[1]="SPEED"]="SPEED",n[o[2]="CODE_SIZE"]="CODE_SIZE",n[o[3]="LITE_RUNTIME"]="LITE_RUNTIME",n),D),t.MessageOptions=(I.prototype.messageSetWireFormat=!1,I.prototype.noStandardDescriptorAccessor=!1,I.prototype.deprecated=!1,I.prototype.mapEntry=!1,I.prototype.deprecatedLegacyJsonFieldConflicts=!1,I.prototype.features=null,I.prototype.uninterpretedOption=i.emptyArray,I.prototype[".google.api.resource"]=null,I.fromObject=function(e){if(e instanceof a.google.protobuf.MessageOptions)return e;var t=new a.google.protobuf.MessageOptions;if(null!=e.messageSetWireFormat&&(t.messageSetWireFormat=Boolean(e.messageSetWireFormat)),null!=e.noStandardDescriptorAccessor&&(t.noStandardDescriptorAccessor=Boolean(e.noStandardDescriptorAccessor)),null!=e.deprecated&&(t.deprecated=Boolean(e.deprecated)),null!=e.mapEntry&&(t.mapEntry=Boolean(e.mapEntry)),null!=e.deprecatedLegacyJsonFieldConflicts&&(t.deprecatedLegacyJsonFieldConflicts=Boolean(e.deprecatedLegacyJsonFieldConflicts)),null!=e.features){if("object"!=typeof e.features)throw TypeError(".google.protobuf.MessageOptions.features: object expected");t.features=a.google.protobuf.FeatureSet.fromObject(e.features)}if(e.uninterpretedOption){if(!Array.isArray(e.uninterpretedOption))throw TypeError(".google.protobuf.MessageOptions.uninterpretedOption: array expected");t.uninterpretedOption=[];for(var o=0;o<e.uninterpretedOption.length;++o){if("object"!=typeof e.uninterpretedOption[o])throw TypeError(".google.protobuf.MessageOptions.uninterpretedOption: object expected");t.uninterpretedOption[o]=a.google.protobuf.UninterpretedOption.fromObject(e.uninterpretedOption[o])}}if(null!=e[".google.api.resource"]){if("object"!=typeof e[".google.api.resource"])throw TypeError(".google.protobuf.MessageOptions..google.api.resource: object expected");t[".google.api.resource"]=a.google.api.ResourceDescriptor.fromObject(e[".google.api.resource"])}return t},I.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.uninterpretedOption=[]),t.defaults&&(o.messageSetWireFormat=!1,o.noStandardDescriptorAccessor=!1,o.deprecated=!1,o.mapEntry=!1,o.deprecatedLegacyJsonFieldConflicts=!1,o.features=null,o[".google.api.resource"]=null),null!=e.messageSetWireFormat&&e.hasOwnProperty("messageSetWireFormat")&&(o.messageSetWireFormat=e.messageSetWireFormat),null!=e.noStandardDescriptorAccessor&&e.hasOwnProperty("noStandardDescriptorAccessor")&&(o.noStandardDescriptorAccessor=e.noStandardDescriptorAccessor),null!=e.deprecated&&e.hasOwnProperty("deprecated")&&(o.deprecated=e.deprecated),null!=e.mapEntry&&e.hasOwnProperty("mapEntry")&&(o.mapEntry=e.mapEntry),null!=e.deprecatedLegacyJsonFieldConflicts&&e.hasOwnProperty("deprecatedLegacyJsonFieldConflicts")&&(o.deprecatedLegacyJsonFieldConflicts=e.deprecatedLegacyJsonFieldConflicts),null!=e.features&&e.hasOwnProperty("features")&&(o.features=a.google.protobuf.FeatureSet.toObject(e.features,t)),e.uninterpretedOption&&e.uninterpretedOption.length){o.uninterpretedOption=[];for(var r=0;r<e.uninterpretedOption.length;++r)o.uninterpretedOption[r]=a.google.protobuf.UninterpretedOption.toObject(e.uninterpretedOption[r],t)}return null!=e[".google.api.resource"]&&e.hasOwnProperty(".google.api.resource")&&(o[".google.api.resource"]=a.google.api.ResourceDescriptor.toObject(e[".google.api.resource"],t)),o},I.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},I.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.MessageOptions"},I),t.FieldOptions=(A.prototype.ctype=0,A.prototype.packed=!1,A.prototype.jstype=0,A.prototype.lazy=!1,A.prototype.unverifiedLazy=!1,A.prototype.deprecated=!1,A.prototype.weak=!1,A.prototype.debugRedact=!1,A.prototype.retention=0,A.prototype.targets=i.emptyArray,A.prototype.editionDefaults=i.emptyArray,A.prototype.features=null,A.prototype.uninterpretedOption=i.emptyArray,A.prototype[".google.api.fieldBehavior"]=i.emptyArray,A.prototype[".google.api.resourceReference"]=null,A.fromObject=function(e){if(e instanceof a.google.protobuf.FieldOptions)return e;var t=new a.google.protobuf.FieldOptions;switch(e.ctype){default:"number"==typeof e.ctype&&(t.ctype=e.ctype);break;case"STRING":case 0:t.ctype=0;break;case"CORD":case 1:t.ctype=1;break;case"STRING_PIECE":case 2:t.ctype=2}switch(null!=e.packed&&(t.packed=Boolean(e.packed)),e.jstype){default:"number"==typeof e.jstype&&(t.jstype=e.jstype);break;case"JS_NORMAL":case 0:t.jstype=0;break;case"JS_STRING":case 1:t.jstype=1;break;case"JS_NUMBER":case 2:t.jstype=2}switch(null!=e.lazy&&(t.lazy=Boolean(e.lazy)),null!=e.unverifiedLazy&&(t.unverifiedLazy=Boolean(e.unverifiedLazy)),null!=e.deprecated&&(t.deprecated=Boolean(e.deprecated)),null!=e.weak&&(t.weak=Boolean(e.weak)),null!=e.debugRedact&&(t.debugRedact=Boolean(e.debugRedact)),e.retention){default:"number"==typeof e.retention&&(t.retention=e.retention);break;case"RETENTION_UNKNOWN":case 0:t.retention=0;break;case"RETENTION_RUNTIME":case 1:t.retention=1;break;case"RETENTION_SOURCE":case 2:t.retention=2}if(e.targets){if(!Array.isArray(e.targets))throw TypeError(".google.protobuf.FieldOptions.targets: array expected");t.targets=[];for(var o=0;o<e.targets.length;++o)switch(e.targets[o]){default:if("number"==typeof e.targets[o]){t.targets[o]=e.targets[o];break}case"TARGET_TYPE_UNKNOWN":case 0:t.targets[o]=0;break;case"TARGET_TYPE_FILE":case 1:t.targets[o]=1;break;case"TARGET_TYPE_EXTENSION_RANGE":case 2:t.targets[o]=2;break;case"TARGET_TYPE_MESSAGE":case 3:t.targets[o]=3;break;case"TARGET_TYPE_FIELD":case 4:t.targets[o]=4;break;case"TARGET_TYPE_ONEOF":case 5:t.targets[o]=5;break;case"TARGET_TYPE_ENUM":case 6:t.targets[o]=6;break;case"TARGET_TYPE_ENUM_ENTRY":case 7:t.targets[o]=7;break;case"TARGET_TYPE_SERVICE":case 8:t.targets[o]=8;break;case"TARGET_TYPE_METHOD":case 9:t.targets[o]=9}}if(e.editionDefaults){if(!Array.isArray(e.editionDefaults))throw TypeError(".google.protobuf.FieldOptions.editionDefaults: array expected");t.editionDefaults=[];for(o=0;o<e.editionDefaults.length;++o){if("object"!=typeof e.editionDefaults[o])throw TypeError(".google.protobuf.FieldOptions.editionDefaults: object expected");t.editionDefaults[o]=a.google.protobuf.FieldOptions.EditionDefault.fromObject(e.editionDefaults[o])}}if(null!=e.features){if("object"!=typeof e.features)throw TypeError(".google.protobuf.FieldOptions.features: object expected");t.features=a.google.protobuf.FeatureSet.fromObject(e.features)}if(e.uninterpretedOption){if(!Array.isArray(e.uninterpretedOption))throw TypeError(".google.protobuf.FieldOptions.uninterpretedOption: array expected");t.uninterpretedOption=[];for(o=0;o<e.uninterpretedOption.length;++o){if("object"!=typeof e.uninterpretedOption[o])throw TypeError(".google.protobuf.FieldOptions.uninterpretedOption: object expected");t.uninterpretedOption[o]=a.google.protobuf.UninterpretedOption.fromObject(e.uninterpretedOption[o])}}if(e[".google.api.fieldBehavior"]){if(!Array.isArray(e[".google.api.fieldBehavior"]))throw TypeError(".google.protobuf.FieldOptions..google.api.fieldBehavior: array expected");t[".google.api.fieldBehavior"]=[];for(o=0;o<e[".google.api.fieldBehavior"].length;++o)switch(e[".google.api.fieldBehavior"][o]){default:if("number"==typeof e[".google.api.fieldBehavior"][o]){t[".google.api.fieldBehavior"][o]=e[".google.api.fieldBehavior"][o];break}case"FIELD_BEHAVIOR_UNSPECIFIED":case 0:t[".google.api.fieldBehavior"][o]=0;break;case"OPTIONAL":case 1:t[".google.api.fieldBehavior"][o]=1;break;case"REQUIRED":case 2:t[".google.api.fieldBehavior"][o]=2;break;case"OUTPUT_ONLY":case 3:t[".google.api.fieldBehavior"][o]=3;break;case"INPUT_ONLY":case 4:t[".google.api.fieldBehavior"][o]=4;break;case"IMMUTABLE":case 5:t[".google.api.fieldBehavior"][o]=5;break;case"UNORDERED_LIST":case 6:t[".google.api.fieldBehavior"][o]=6;break;case"NON_EMPTY_DEFAULT":case 7:t[".google.api.fieldBehavior"][o]=7;break;case"IDENTIFIER":case 8:t[".google.api.fieldBehavior"][o]=8}}if(null!=e[".google.api.resourceReference"]){if("object"!=typeof e[".google.api.resourceReference"])throw TypeError(".google.protobuf.FieldOptions..google.api.resourceReference: object expected");t[".google.api.resourceReference"]=a.google.api.ResourceReference.fromObject(e[".google.api.resourceReference"])}return t},A.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.targets=[],o.editionDefaults=[],o.uninterpretedOption=[],o[".google.api.fieldBehavior"]=[]),t.defaults&&(o.ctype=t.enums===String?"STRING":0,o.packed=!1,o.deprecated=!1,o.lazy=!1,o.jstype=t.enums===String?"JS_NORMAL":0,o.weak=!1,o.unverifiedLazy=!1,o.debugRedact=!1,o.retention=t.enums===String?"RETENTION_UNKNOWN":0,o.features=null,o[".google.api.resourceReference"]=null),null!=e.ctype&&e.hasOwnProperty("ctype")&&(o.ctype=t.enums!==String||void 0===a.google.protobuf.FieldOptions.CType[e.ctype]?e.ctype:a.google.protobuf.FieldOptions.CType[e.ctype]),null!=e.packed&&e.hasOwnProperty("packed")&&(o.packed=e.packed),null!=e.deprecated&&e.hasOwnProperty("deprecated")&&(o.deprecated=e.deprecated),null!=e.lazy&&e.hasOwnProperty("lazy")&&(o.lazy=e.lazy),null!=e.jstype&&e.hasOwnProperty("jstype")&&(o.jstype=t.enums!==String||void 0===a.google.protobuf.FieldOptions.JSType[e.jstype]?e.jstype:a.google.protobuf.FieldOptions.JSType[e.jstype]),null!=e.weak&&e.hasOwnProperty("weak")&&(o.weak=e.weak),null!=e.unverifiedLazy&&e.hasOwnProperty("unverifiedLazy")&&(o.unverifiedLazy=e.unverifiedLazy),null!=e.debugRedact&&e.hasOwnProperty("debugRedact")&&(o.debugRedact=e.debugRedact),null!=e.retention&&e.hasOwnProperty("retention")&&(o.retention=t.enums!==String||void 0===a.google.protobuf.FieldOptions.OptionRetention[e.retention]?e.retention:a.google.protobuf.FieldOptions.OptionRetention[e.retention]),e.targets&&e.targets.length){o.targets=[];for(var r=0;r<e.targets.length;++r)o.targets[r]=t.enums!==String||void 0===a.google.protobuf.FieldOptions.OptionTargetType[e.targets[r]]?e.targets[r]:a.google.protobuf.FieldOptions.OptionTargetType[e.targets[r]]}if(e.editionDefaults&&e.editionDefaults.length){o.editionDefaults=[];for(r=0;r<e.editionDefaults.length;++r)o.editionDefaults[r]=a.google.protobuf.FieldOptions.EditionDefault.toObject(e.editionDefaults[r],t)}if(null!=e.features&&e.hasOwnProperty("features")&&(o.features=a.google.protobuf.FeatureSet.toObject(e.features,t)),e.uninterpretedOption&&e.uninterpretedOption.length){o.uninterpretedOption=[];for(r=0;r<e.uninterpretedOption.length;++r)o.uninterpretedOption[r]=a.google.protobuf.UninterpretedOption.toObject(e.uninterpretedOption[r],t)}if(e[".google.api.fieldBehavior"]&&e[".google.api.fieldBehavior"].length){o[".google.api.fieldBehavior"]=[];for(r=0;r<e[".google.api.fieldBehavior"].length;++r)o[".google.api.fieldBehavior"][r]=t.enums!==String||void 0===a.google.api.FieldBehavior[e[".google.api.fieldBehavior"][r]]?e[".google.api.fieldBehavior"][r]:a.google.api.FieldBehavior[e[".google.api.fieldBehavior"][r]]}return null!=e[".google.api.resourceReference"]&&e.hasOwnProperty(".google.api.resourceReference")&&(o[".google.api.resourceReference"]=a.google.api.ResourceReference.toObject(e[".google.api.resourceReference"],t)),o},A.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},A.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.FieldOptions"},A.CType=(o={},(n=Object.create(o))[o[0]="STRING"]="STRING",n[o[1]="CORD"]="CORD",n[o[2]="STRING_PIECE"]="STRING_PIECE",n),A.JSType=(o={},(n=Object.create(o))[o[0]="JS_NORMAL"]="JS_NORMAL",n[o[1]="JS_STRING"]="JS_STRING",n[o[2]="JS_NUMBER"]="JS_NUMBER",n),A.OptionRetention=(o={},(n=Object.create(o))[o[0]="RETENTION_UNKNOWN"]="RETENTION_UNKNOWN",n[o[1]="RETENTION_RUNTIME"]="RETENTION_RUNTIME",n[o[2]="RETENTION_SOURCE"]="RETENTION_SOURCE",n),A.OptionTargetType=(o={},(n=Object.create(o))[o[0]="TARGET_TYPE_UNKNOWN"]="TARGET_TYPE_UNKNOWN",n[o[1]="TARGET_TYPE_FILE"]="TARGET_TYPE_FILE",n[o[2]="TARGET_TYPE_EXTENSION_RANGE"]="TARGET_TYPE_EXTENSION_RANGE",n[o[3]="TARGET_TYPE_MESSAGE"]="TARGET_TYPE_MESSAGE",n[o[4]="TARGET_TYPE_FIELD"]="TARGET_TYPE_FIELD",n[o[5]="TARGET_TYPE_ONEOF"]="TARGET_TYPE_ONEOF",n[o[6]="TARGET_TYPE_ENUM"]="TARGET_TYPE_ENUM",n[o[7]="TARGET_TYPE_ENUM_ENTRY"]="TARGET_TYPE_ENUM_ENTRY",n[o[8]="TARGET_TYPE_SERVICE"]="TARGET_TYPE_SERVICE",n[o[9]="TARGET_TYPE_METHOD"]="TARGET_TYPE_METHOD",n),A.EditionDefault=(Ot.prototype.edition=0,Ot.prototype.value="",Ot.fromObject=function(e){if(e instanceof a.google.protobuf.FieldOptions.EditionDefault)return e;var t=new a.google.protobuf.FieldOptions.EditionDefault;switch(e.edition){default:"number"==typeof e.edition&&(t.edition=e.edition);break;case"EDITION_UNKNOWN":case 0:t.edition=0;break;case"EDITION_PROTO2":case 998:t.edition=998;break;case"EDITION_PROTO3":case 999:t.edition=999;break;case"EDITION_2023":case 1e3:t.edition=1e3;break;case"EDITION_2024":case 1001:t.edition=1001;break;case"EDITION_1_TEST_ONLY":case 1:t.edition=1;break;case"EDITION_2_TEST_ONLY":case 2:t.edition=2;break;case"EDITION_99997_TEST_ONLY":case 99997:t.edition=99997;break;case"EDITION_99998_TEST_ONLY":case 99998:t.edition=99998;break;case"EDITION_99999_TEST_ONLY":case 99999:t.edition=99999;break;case"EDITION_MAX":case 2147483647:t.edition=2147483647}return null!=e.value&&(t.value=String(e.value)),t},Ot.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.value="",o.edition=t.enums===String?"EDITION_UNKNOWN":0),null!=e.value&&e.hasOwnProperty("value")&&(o.value=e.value),null!=e.edition&&e.hasOwnProperty("edition")&&(o.edition=t.enums!==String||void 0===a.google.protobuf.Edition[e.edition]?e.edition:a.google.protobuf.Edition[e.edition]),o},Ot.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},Ot.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.FieldOptions.EditionDefault"},Ot),A),t.OneofOptions=(ht.prototype.features=null,ht.prototype.uninterpretedOption=i.emptyArray,ht.fromObject=function(e){if(e instanceof a.google.protobuf.OneofOptions)return e;var t=new a.google.protobuf.OneofOptions;if(null!=e.features){if("object"!=typeof e.features)throw TypeError(".google.protobuf.OneofOptions.features: object expected");t.features=a.google.protobuf.FeatureSet.fromObject(e.features)}if(e.uninterpretedOption){if(!Array.isArray(e.uninterpretedOption))throw TypeError(".google.protobuf.OneofOptions.uninterpretedOption: array expected");t.uninterpretedOption=[];for(var o=0;o<e.uninterpretedOption.length;++o){if("object"!=typeof e.uninterpretedOption[o])throw TypeError(".google.protobuf.OneofOptions.uninterpretedOption: object expected");t.uninterpretedOption[o]=a.google.protobuf.UninterpretedOption.fromObject(e.uninterpretedOption[o])}}return t},ht.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.uninterpretedOption=[]),t.defaults&&(o.features=null),null!=e.features&&e.hasOwnProperty("features")&&(o.features=a.google.protobuf.FeatureSet.toObject(e.features,t)),e.uninterpretedOption&&e.uninterpretedOption.length){o.uninterpretedOption=[];for(var r=0;r<e.uninterpretedOption.length;++r)o.uninterpretedOption[r]=a.google.protobuf.UninterpretedOption.toObject(e.uninterpretedOption[r],t)}return o},ht.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},ht.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.OneofOptions"},ht),t.EnumOptions=(vt.prototype.allowAlias=!1,vt.prototype.deprecated=!1,vt.prototype.deprecatedLegacyJsonFieldConflicts=!1,vt.prototype.features=null,vt.prototype.uninterpretedOption=i.emptyArray,vt.fromObject=function(e){if(e instanceof a.google.protobuf.EnumOptions)return e;var t=new a.google.protobuf.EnumOptions;if(null!=e.allowAlias&&(t.allowAlias=Boolean(e.allowAlias)),null!=e.deprecated&&(t.deprecated=Boolean(e.deprecated)),null!=e.deprecatedLegacyJsonFieldConflicts&&(t.deprecatedLegacyJsonFieldConflicts=Boolean(e.deprecatedLegacyJsonFieldConflicts)),null!=e.features){if("object"!=typeof e.features)throw TypeError(".google.protobuf.EnumOptions.features: object expected");t.features=a.google.protobuf.FeatureSet.fromObject(e.features)}if(e.uninterpretedOption){if(!Array.isArray(e.uninterpretedOption))throw TypeError(".google.protobuf.EnumOptions.uninterpretedOption: array expected");t.uninterpretedOption=[];for(var o=0;o<e.uninterpretedOption.length;++o){if("object"!=typeof e.uninterpretedOption[o])throw TypeError(".google.protobuf.EnumOptions.uninterpretedOption: object expected");t.uninterpretedOption[o]=a.google.protobuf.UninterpretedOption.fromObject(e.uninterpretedOption[o])}}return t},vt.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.uninterpretedOption=[]),t.defaults&&(o.allowAlias=!1,o.deprecated=!1,o.deprecatedLegacyJsonFieldConflicts=!1,o.features=null),null!=e.allowAlias&&e.hasOwnProperty("allowAlias")&&(o.allowAlias=e.allowAlias),null!=e.deprecated&&e.hasOwnProperty("deprecated")&&(o.deprecated=e.deprecated),null!=e.deprecatedLegacyJsonFieldConflicts&&e.hasOwnProperty("deprecatedLegacyJsonFieldConflicts")&&(o.deprecatedLegacyJsonFieldConflicts=e.deprecatedLegacyJsonFieldConflicts),null!=e.features&&e.hasOwnProperty("features")&&(o.features=a.google.protobuf.FeatureSet.toObject(e.features,t)),e.uninterpretedOption&&e.uninterpretedOption.length){o.uninterpretedOption=[];for(var r=0;r<e.uninterpretedOption.length;++r)o.uninterpretedOption[r]=a.google.protobuf.UninterpretedOption.toObject(e.uninterpretedOption[r],t)}return o},vt.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},vt.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.EnumOptions"},vt),t.EnumValueOptions=(Tt.prototype.deprecated=!1,Tt.prototype.features=null,Tt.prototype.debugRedact=!1,Tt.prototype.uninterpretedOption=i.emptyArray,Tt.fromObject=function(e){if(e instanceof a.google.protobuf.EnumValueOptions)return e;var t=new a.google.protobuf.EnumValueOptions;if(null!=e.deprecated&&(t.deprecated=Boolean(e.deprecated)),null!=e.features){if("object"!=typeof e.features)throw TypeError(".google.protobuf.EnumValueOptions.features: object expected");t.features=a.google.protobuf.FeatureSet.fromObject(e.features)}if(null!=e.debugRedact&&(t.debugRedact=Boolean(e.debugRedact)),e.uninterpretedOption){if(!Array.isArray(e.uninterpretedOption))throw TypeError(".google.protobuf.EnumValueOptions.uninterpretedOption: array expected");t.uninterpretedOption=[];for(var o=0;o<e.uninterpretedOption.length;++o){if("object"!=typeof e.uninterpretedOption[o])throw TypeError(".google.protobuf.EnumValueOptions.uninterpretedOption: object expected");t.uninterpretedOption[o]=a.google.protobuf.UninterpretedOption.fromObject(e.uninterpretedOption[o])}}return t},Tt.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.uninterpretedOption=[]),t.defaults&&(o.deprecated=!1,o.features=null,o.debugRedact=!1),null!=e.deprecated&&e.hasOwnProperty("deprecated")&&(o.deprecated=e.deprecated),null!=e.features&&e.hasOwnProperty("features")&&(o.features=a.google.protobuf.FeatureSet.toObject(e.features,t)),null!=e.debugRedact&&e.hasOwnProperty("debugRedact")&&(o.debugRedact=e.debugRedact),e.uninterpretedOption&&e.uninterpretedOption.length){o.uninterpretedOption=[];for(var r=0;r<e.uninterpretedOption.length;++r)o.uninterpretedOption[r]=a.google.protobuf.UninterpretedOption.toObject(e.uninterpretedOption[r],t)}return o},Tt.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},Tt.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.EnumValueOptions"},Tt),t.ServiceOptions=(St.prototype.features=null,St.prototype.deprecated=!1,St.prototype.uninterpretedOption=i.emptyArray,St.prototype[".google.api.defaultHost"]="",St.prototype[".google.api.oauthScopes"]="",St.prototype[".google.api.apiVersion"]="",St.fromObject=function(e){if(e instanceof a.google.protobuf.ServiceOptions)return e;var t=new a.google.protobuf.ServiceOptions;if(null!=e.features){if("object"!=typeof e.features)throw TypeError(".google.protobuf.ServiceOptions.features: object expected");t.features=a.google.protobuf.FeatureSet.fromObject(e.features)}if(null!=e.deprecated&&(t.deprecated=Boolean(e.deprecated)),e.uninterpretedOption){if(!Array.isArray(e.uninterpretedOption))throw TypeError(".google.protobuf.ServiceOptions.uninterpretedOption: array expected");t.uninterpretedOption=[];for(var o=0;o<e.uninterpretedOption.length;++o){if("object"!=typeof e.uninterpretedOption[o])throw TypeError(".google.protobuf.ServiceOptions.uninterpretedOption: object expected");t.uninterpretedOption[o]=a.google.protobuf.UninterpretedOption.fromObject(e.uninterpretedOption[o])}}return null!=e[".google.api.defaultHost"]&&(t[".google.api.defaultHost"]=String(e[".google.api.defaultHost"])),null!=e[".google.api.oauthScopes"]&&(t[".google.api.oauthScopes"]=String(e[".google.api.oauthScopes"])),null!=e[".google.api.apiVersion"]&&(t[".google.api.apiVersion"]=String(e[".google.api.apiVersion"])),t},St.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.uninterpretedOption=[]),t.defaults&&(o.deprecated=!1,o.features=null,o[".google.api.defaultHost"]="",o[".google.api.oauthScopes"]="",o[".google.api.apiVersion"]=""),null!=e.deprecated&&e.hasOwnProperty("deprecated")&&(o.deprecated=e.deprecated),null!=e.features&&e.hasOwnProperty("features")&&(o.features=a.google.protobuf.FeatureSet.toObject(e.features,t)),e.uninterpretedOption&&e.uninterpretedOption.length){o.uninterpretedOption=[];for(var r=0;r<e.uninterpretedOption.length;++r)o.uninterpretedOption[r]=a.google.protobuf.UninterpretedOption.toObject(e.uninterpretedOption[r],t)}return null!=e[".google.api.defaultHost"]&&e.hasOwnProperty(".google.api.defaultHost")&&(o[".google.api.defaultHost"]=e[".google.api.defaultHost"]),null!=e[".google.api.oauthScopes"]&&e.hasOwnProperty(".google.api.oauthScopes")&&(o[".google.api.oauthScopes"]=e[".google.api.oauthScopes"]),null!=e[".google.api.apiVersion"]&&e.hasOwnProperty(".google.api.apiVersion")&&(o[".google.api.apiVersion"]=e[".google.api.apiVersion"]),o},St.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},St.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.ServiceOptions"},St),t.MethodOptions=(k.prototype.deprecated=!1,k.prototype.idempotencyLevel=0,k.prototype.features=null,k.prototype.uninterpretedOption=i.emptyArray,k.prototype[".google.api.http"]=null,k.prototype[".google.api.methodSignature"]=i.emptyArray,k.prototype[".google.longrunning.operationInfo"]=null,k.fromObject=function(e){if(e instanceof a.google.protobuf.MethodOptions)return e;var t=new a.google.protobuf.MethodOptions;switch(null!=e.deprecated&&(t.deprecated=Boolean(e.deprecated)),e.idempotencyLevel){default:"number"==typeof e.idempotencyLevel&&(t.idempotencyLevel=e.idempotencyLevel);break;case"IDEMPOTENCY_UNKNOWN":case 0:t.idempotencyLevel=0;break;case"NO_SIDE_EFFECTS":case 1:t.idempotencyLevel=1;break;case"IDEMPOTENT":case 2:t.idempotencyLevel=2}if(null!=e.features){if("object"!=typeof e.features)throw TypeError(".google.protobuf.MethodOptions.features: object expected");t.features=a.google.protobuf.FeatureSet.fromObject(e.features)}if(e.uninterpretedOption){if(!Array.isArray(e.uninterpretedOption))throw TypeError(".google.protobuf.MethodOptions.uninterpretedOption: array expected");t.uninterpretedOption=[];for(var o=0;o<e.uninterpretedOption.length;++o){if("object"!=typeof e.uninterpretedOption[o])throw TypeError(".google.protobuf.MethodOptions.uninterpretedOption: object expected");t.uninterpretedOption[o]=a.google.protobuf.UninterpretedOption.fromObject(e.uninterpretedOption[o])}}if(null!=e[".google.api.http"]){if("object"!=typeof e[".google.api.http"])throw TypeError(".google.protobuf.MethodOptions..google.api.http: object expected");t[".google.api.http"]=a.google.api.HttpRule.fromObject(e[".google.api.http"])}if(e[".google.api.methodSignature"]){if(!Array.isArray(e[".google.api.methodSignature"]))throw TypeError(".google.protobuf.MethodOptions..google.api.methodSignature: array expected");t[".google.api.methodSignature"]=[];for(o=0;o<e[".google.api.methodSignature"].length;++o)t[".google.api.methodSignature"][o]=String(e[".google.api.methodSignature"][o])}if(null!=e[".google.longrunning.operationInfo"]){if("object"!=typeof e[".google.longrunning.operationInfo"])throw TypeError(".google.protobuf.MethodOptions..google.longrunning.operationInfo: object expected");t[".google.longrunning.operationInfo"]=a.google.longrunning.OperationInfo.fromObject(e[".google.longrunning.operationInfo"])}return t},k.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.uninterpretedOption=[],o[".google.api.methodSignature"]=[]),t.defaults&&(o.deprecated=!1,o.idempotencyLevel=t.enums===String?"IDEMPOTENCY_UNKNOWN":0,o.features=null,o[".google.longrunning.operationInfo"]=null,o[".google.api.http"]=null),null!=e.deprecated&&e.hasOwnProperty("deprecated")&&(o.deprecated=e.deprecated),null!=e.idempotencyLevel&&e.hasOwnProperty("idempotencyLevel")&&(o.idempotencyLevel=t.enums!==String||void 0===a.google.protobuf.MethodOptions.IdempotencyLevel[e.idempotencyLevel]?e.idempotencyLevel:a.google.protobuf.MethodOptions.IdempotencyLevel[e.idempotencyLevel]),null!=e.features&&e.hasOwnProperty("features")&&(o.features=a.google.protobuf.FeatureSet.toObject(e.features,t)),e.uninterpretedOption&&e.uninterpretedOption.length){o.uninterpretedOption=[];for(var r=0;r<e.uninterpretedOption.length;++r)o.uninterpretedOption[r]=a.google.protobuf.UninterpretedOption.toObject(e.uninterpretedOption[r],t)}if(null!=e[".google.longrunning.operationInfo"]&&e.hasOwnProperty(".google.longrunning.operationInfo")&&(o[".google.longrunning.operationInfo"]=a.google.longrunning.OperationInfo.toObject(e[".google.longrunning.operationInfo"],t)),e[".google.api.methodSignature"]&&e[".google.api.methodSignature"].length){o[".google.api.methodSignature"]=[];for(r=0;r<e[".google.api.methodSignature"].length;++r)o[".google.api.methodSignature"][r]=e[".google.api.methodSignature"][r]}return null!=e[".google.api.http"]&&e.hasOwnProperty(".google.api.http")&&(o[".google.api.http"]=a.google.api.HttpRule.toObject(e[".google.api.http"],t)),o},k.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},k.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.MethodOptions"},k.IdempotencyLevel=(o={},(n=Object.create(o))[o[0]="IDEMPOTENCY_UNKNOWN"]="IDEMPOTENCY_UNKNOWN",n[o[1]="NO_SIDE_EFFECTS"]="NO_SIDE_EFFECTS",n[o[2]="IDEMPOTENT"]="IDEMPOTENT",n),k),t.UninterpretedOption=(x.prototype.name=i.emptyArray,x.prototype.identifierValue="",x.prototype.positiveIntValue=i.Long?i.Long.fromBits(0,0,!0):0,x.prototype.negativeIntValue=i.Long?i.Long.fromBits(0,0,!1):0,x.prototype.doubleValue=0,x.prototype.stringValue=i.newBuffer([]),x.prototype.aggregateValue="",x.fromObject=function(e){if(e instanceof a.google.protobuf.UninterpretedOption)return e;var t=new a.google.protobuf.UninterpretedOption;if(e.name){if(!Array.isArray(e.name))throw TypeError(".google.protobuf.UninterpretedOption.name: array expected");t.name=[];for(var o=0;o<e.name.length;++o){if("object"!=typeof e.name[o])throw TypeError(".google.protobuf.UninterpretedOption.name: object expected");t.name[o]=a.google.protobuf.UninterpretedOption.NamePart.fromObject(e.name[o])}}return null!=e.identifierValue&&(t.identifierValue=String(e.identifierValue)),null!=e.positiveIntValue&&(i.Long?(t.positiveIntValue=i.Long.fromValue(e.positiveIntValue)).unsigned=!0:"string"==typeof e.positiveIntValue?t.positiveIntValue=parseInt(e.positiveIntValue,10):"number"==typeof e.positiveIntValue?t.positiveIntValue=e.positiveIntValue:"object"==typeof e.positiveIntValue&&(t.positiveIntValue=new i.LongBits(e.positiveIntValue.low>>>0,e.positiveIntValue.high>>>0).toNumber(!0))),null!=e.negativeIntValue&&(i.Long?(t.negativeIntValue=i.Long.fromValue(e.negativeIntValue)).unsigned=!1:"string"==typeof e.negativeIntValue?t.negativeIntValue=parseInt(e.negativeIntValue,10):"number"==typeof e.negativeIntValue?t.negativeIntValue=e.negativeIntValue:"object"==typeof e.negativeIntValue&&(t.negativeIntValue=new i.LongBits(e.negativeIntValue.low>>>0,e.negativeIntValue.high>>>0).toNumber())),null!=e.doubleValue&&(t.doubleValue=Number(e.doubleValue)),null!=e.stringValue&&("string"==typeof e.stringValue?i.base64.decode(e.stringValue,t.stringValue=i.newBuffer(i.base64.length(e.stringValue)),0):0<=e.stringValue.length&&(t.stringValue=e.stringValue)),null!=e.aggregateValue&&(t.aggregateValue=String(e.aggregateValue)),t},x.toObject=function(e,t){var o,r={};if(((t=t||{}).arrays||t.defaults)&&(r.name=[]),t.defaults&&(r.identifierValue="",i.Long?(o=new i.Long(0,0,!0),r.positiveIntValue=t.longs===String?o.toString():t.longs===Number?o.toNumber():o):r.positiveIntValue=t.longs===String?"0":0,i.Long?(o=new i.Long(0,0,!1),r.negativeIntValue=t.longs===String?o.toString():t.longs===Number?o.toNumber():o):r.negativeIntValue=t.longs===String?"0":0,r.doubleValue=0,t.bytes===String?r.stringValue="":(r.stringValue=[],t.bytes!==Array&&(r.stringValue=i.newBuffer(r.stringValue))),r.aggregateValue=""),e.name&&e.name.length){r.name=[];for(var n=0;n<e.name.length;++n)r.name[n]=a.google.protobuf.UninterpretedOption.NamePart.toObject(e.name[n],t)}return null!=e.identifierValue&&e.hasOwnProperty("identifierValue")&&(r.identifierValue=e.identifierValue),null!=e.positiveIntValue&&e.hasOwnProperty("positiveIntValue")&&("number"==typeof e.positiveIntValue?r.positiveIntValue=t.longs===String?String(e.positiveIntValue):e.positiveIntValue:r.positiveIntValue=t.longs===String?i.Long.prototype.toString.call(e.positiveIntValue):t.longs===Number?new i.LongBits(e.positiveIntValue.low>>>0,e.positiveIntValue.high>>>0).toNumber(!0):e.positiveIntValue),null!=e.negativeIntValue&&e.hasOwnProperty("negativeIntValue")&&("number"==typeof e.negativeIntValue?r.negativeIntValue=t.longs===String?String(e.negativeIntValue):e.negativeIntValue:r.negativeIntValue=t.longs===String?i.Long.prototype.toString.call(e.negativeIntValue):t.longs===Number?new i.LongBits(e.negativeIntValue.low>>>0,e.negativeIntValue.high>>>0).toNumber():e.negativeIntValue),null!=e.doubleValue&&e.hasOwnProperty("doubleValue")&&(r.doubleValue=t.json&&!isFinite(e.doubleValue)?String(e.doubleValue):e.doubleValue),null!=e.stringValue&&e.hasOwnProperty("stringValue")&&(r.stringValue=t.bytes===String?i.base64.encode(e.stringValue,0,e.stringValue.length):t.bytes===Array?Array.prototype.slice.call(e.stringValue):e.stringValue),null!=e.aggregateValue&&e.hasOwnProperty("aggregateValue")&&(r.aggregateValue=e.aggregateValue),r},x.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},x.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.UninterpretedOption"},x.NamePart=(jt.prototype.namePart="",jt.prototype.isExtension=!1,jt.fromObject=function(e){var t;return e instanceof a.google.protobuf.UninterpretedOption.NamePart?e:(t=new a.google.protobuf.UninterpretedOption.NamePart,null!=e.namePart&&(t.namePart=String(e.namePart)),null!=e.isExtension&&(t.isExtension=Boolean(e.isExtension)),t)},jt.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.namePart="",o.isExtension=!1),null!=e.namePart&&e.hasOwnProperty("namePart")&&(o.namePart=e.namePart),null!=e.isExtension&&e.hasOwnProperty("isExtension")&&(o.isExtension=e.isExtension),o},jt.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},jt.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.UninterpretedOption.NamePart"},jt),x),t.FeatureSet=(F.prototype.fieldPresence=0,F.prototype.enumType=0,F.prototype.repeatedFieldEncoding=0,F.prototype.utf8Validation=0,F.prototype.messageEncoding=0,F.prototype.jsonFormat=0,F.fromObject=function(e){if(e instanceof a.google.protobuf.FeatureSet)return e;var t=new a.google.protobuf.FeatureSet;switch(e.fieldPresence){default:"number"==typeof e.fieldPresence&&(t.fieldPresence=e.fieldPresence);break;case"FIELD_PRESENCE_UNKNOWN":case 0:t.fieldPresence=0;break;case"EXPLICIT":case 1:t.fieldPresence=1;break;case"IMPLICIT":case 2:t.fieldPresence=2;break;case"LEGACY_REQUIRED":case 3:t.fieldPresence=3}switch(e.enumType){default:"number"==typeof e.enumType&&(t.enumType=e.enumType);break;case"ENUM_TYPE_UNKNOWN":case 0:t.enumType=0;break;case"OPEN":case 1:t.enumType=1;break;case"CLOSED":case 2:t.enumType=2}switch(e.repeatedFieldEncoding){default:"number"==typeof e.repeatedFieldEncoding&&(t.repeatedFieldEncoding=e.repeatedFieldEncoding);break;case"REPEATED_FIELD_ENCODING_UNKNOWN":case 0:t.repeatedFieldEncoding=0;break;case"PACKED":case 1:t.repeatedFieldEncoding=1;break;case"EXPANDED":case 2:t.repeatedFieldEncoding=2}switch(e.utf8Validation){default:"number"==typeof e.utf8Validation&&(t.utf8Validation=e.utf8Validation);break;case"UTF8_VALIDATION_UNKNOWN":case 0:t.utf8Validation=0;break;case"VERIFY":case 2:t.utf8Validation=2;break;case"NONE":case 3:t.utf8Validation=3}switch(e.messageEncoding){default:"number"==typeof e.messageEncoding&&(t.messageEncoding=e.messageEncoding);break;case"MESSAGE_ENCODING_UNKNOWN":case 0:t.messageEncoding=0;break;case"LENGTH_PREFIXED":case 1:t.messageEncoding=1;break;case"DELIMITED":case 2:t.messageEncoding=2}switch(e.jsonFormat){default:"number"==typeof e.jsonFormat&&(t.jsonFormat=e.jsonFormat);break;case"JSON_FORMAT_UNKNOWN":case 0:t.jsonFormat=0;break;case"ALLOW":case 1:t.jsonFormat=1;break;case"LEGACY_BEST_EFFORT":case 2:t.jsonFormat=2}return t},F.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.fieldPresence=t.enums===String?"FIELD_PRESENCE_UNKNOWN":0,o.enumType=t.enums===String?"ENUM_TYPE_UNKNOWN":0,o.repeatedFieldEncoding=t.enums===String?"REPEATED_FIELD_ENCODING_UNKNOWN":0,o.utf8Validation=t.enums===String?"UTF8_VALIDATION_UNKNOWN":0,o.messageEncoding=t.enums===String?"MESSAGE_ENCODING_UNKNOWN":0,o.jsonFormat=t.enums===String?"JSON_FORMAT_UNKNOWN":0),null!=e.fieldPresence&&e.hasOwnProperty("fieldPresence")&&(o.fieldPresence=t.enums!==String||void 0===a.google.protobuf.FeatureSet.FieldPresence[e.fieldPresence]?e.fieldPresence:a.google.protobuf.FeatureSet.FieldPresence[e.fieldPresence]),null!=e.enumType&&e.hasOwnProperty("enumType")&&(o.enumType=t.enums!==String||void 0===a.google.protobuf.FeatureSet.EnumType[e.enumType]?e.enumType:a.google.protobuf.FeatureSet.EnumType[e.enumType]),null!=e.repeatedFieldEncoding&&e.hasOwnProperty("repeatedFieldEncoding")&&(o.repeatedFieldEncoding=t.enums!==String||void 0===a.google.protobuf.FeatureSet.RepeatedFieldEncoding[e.repeatedFieldEncoding]?e.repeatedFieldEncoding:a.google.protobuf.FeatureSet.RepeatedFieldEncoding[e.repeatedFieldEncoding]),null!=e.utf8Validation&&e.hasOwnProperty("utf8Validation")&&(o.utf8Validation=t.enums!==String||void 0===a.google.protobuf.FeatureSet.Utf8Validation[e.utf8Validation]?e.utf8Validation:a.google.protobuf.FeatureSet.Utf8Validation[e.utf8Validation]),null!=e.messageEncoding&&e.hasOwnProperty("messageEncoding")&&(o.messageEncoding=t.enums!==String||void 0===a.google.protobuf.FeatureSet.MessageEncoding[e.messageEncoding]?e.messageEncoding:a.google.protobuf.FeatureSet.MessageEncoding[e.messageEncoding]),null!=e.jsonFormat&&e.hasOwnProperty("jsonFormat")&&(o.jsonFormat=t.enums!==String||void 0===a.google.protobuf.FeatureSet.JsonFormat[e.jsonFormat]?e.jsonFormat:a.google.protobuf.FeatureSet.JsonFormat[e.jsonFormat]),o},F.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},F.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.FeatureSet"},F.FieldPresence=(o={},(n=Object.create(o))[o[0]="FIELD_PRESENCE_UNKNOWN"]="FIELD_PRESENCE_UNKNOWN",n[o[1]="EXPLICIT"]="EXPLICIT",n[o[2]="IMPLICIT"]="IMPLICIT",n[o[3]="LEGACY_REQUIRED"]="LEGACY_REQUIRED",n),F.EnumType=(o={},(n=Object.create(o))[o[0]="ENUM_TYPE_UNKNOWN"]="ENUM_TYPE_UNKNOWN",n[o[1]="OPEN"]="OPEN",n[o[2]="CLOSED"]="CLOSED",n),F.RepeatedFieldEncoding=(o={},(n=Object.create(o))[o[0]="REPEATED_FIELD_ENCODING_UNKNOWN"]="REPEATED_FIELD_ENCODING_UNKNOWN",n[o[1]="PACKED"]="PACKED",n[o[2]="EXPANDED"]="EXPANDED",n),F.Utf8Validation=(o={},(n=Object.create(o))[o[0]="UTF8_VALIDATION_UNKNOWN"]="UTF8_VALIDATION_UNKNOWN",n[o[2]="VERIFY"]="VERIFY",n[o[3]="NONE"]="NONE",n),F.MessageEncoding=(o={},(n=Object.create(o))[o[0]="MESSAGE_ENCODING_UNKNOWN"]="MESSAGE_ENCODING_UNKNOWN",n[o[1]="LENGTH_PREFIXED"]="LENGTH_PREFIXED",n[o[2]="DELIMITED"]="DELIMITED",n),F.JsonFormat=(o={},(n=Object.create(o))[o[0]="JSON_FORMAT_UNKNOWN"]="JSON_FORMAT_UNKNOWN",n[o[1]="ALLOW"]="ALLOW",n[o[2]="LEGACY_BEST_EFFORT"]="LEGACY_BEST_EFFORT",n),F),t.FeatureSetDefaults=(Et.prototype.defaults=i.emptyArray,Et.prototype.minimumEdition=0,Et.prototype.maximumEdition=0,Et.fromObject=function(e){if(e instanceof a.google.protobuf.FeatureSetDefaults)return e;var t=new a.google.protobuf.FeatureSetDefaults;if(e.defaults){if(!Array.isArray(e.defaults))throw TypeError(".google.protobuf.FeatureSetDefaults.defaults: array expected");t.defaults=[];for(var o=0;o<e.defaults.length;++o){if("object"!=typeof e.defaults[o])throw TypeError(".google.protobuf.FeatureSetDefaults.defaults: object expected");t.defaults[o]=a.google.protobuf.FeatureSetDefaults.FeatureSetEditionDefault.fromObject(e.defaults[o])}}switch(e.minimumEdition){default:"number"==typeof e.minimumEdition&&(t.minimumEdition=e.minimumEdition);break;case"EDITION_UNKNOWN":case 0:t.minimumEdition=0;break;case"EDITION_PROTO2":case 998:t.minimumEdition=998;break;case"EDITION_PROTO3":case 999:t.minimumEdition=999;break;case"EDITION_2023":case 1e3:t.minimumEdition=1e3;break;case"EDITION_2024":case 1001:t.minimumEdition=1001;break;case"EDITION_1_TEST_ONLY":case 1:t.minimumEdition=1;break;case"EDITION_2_TEST_ONLY":case 2:t.minimumEdition=2;break;case"EDITION_99997_TEST_ONLY":case 99997:t.minimumEdition=99997;break;case"EDITION_99998_TEST_ONLY":case 99998:t.minimumEdition=99998;break;case"EDITION_99999_TEST_ONLY":case 99999:t.minimumEdition=99999;break;case"EDITION_MAX":case 2147483647:t.minimumEdition=2147483647}switch(e.maximumEdition){default:"number"==typeof e.maximumEdition&&(t.maximumEdition=e.maximumEdition);break;case"EDITION_UNKNOWN":case 0:t.maximumEdition=0;break;case"EDITION_PROTO2":case 998:t.maximumEdition=998;break;case"EDITION_PROTO3":case 999:t.maximumEdition=999;break;case"EDITION_2023":case 1e3:t.maximumEdition=1e3;break;case"EDITION_2024":case 1001:t.maximumEdition=1001;break;case"EDITION_1_TEST_ONLY":case 1:t.maximumEdition=1;break;case"EDITION_2_TEST_ONLY":case 2:t.maximumEdition=2;break;case"EDITION_99997_TEST_ONLY":case 99997:t.maximumEdition=99997;break;case"EDITION_99998_TEST_ONLY":case 99998:t.maximumEdition=99998;break;case"EDITION_99999_TEST_ONLY":case 99999:t.maximumEdition=99999;break;case"EDITION_MAX":case 2147483647:t.maximumEdition=2147483647}return t},Et.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.defaults=[]),t.defaults&&(o.minimumEdition=t.enums===String?"EDITION_UNKNOWN":0,o.maximumEdition=t.enums===String?"EDITION_UNKNOWN":0),e.defaults&&e.defaults.length){o.defaults=[];for(var r=0;r<e.defaults.length;++r)o.defaults[r]=a.google.protobuf.FeatureSetDefaults.FeatureSetEditionDefault.toObject(e.defaults[r],t)}return null!=e.minimumEdition&&e.hasOwnProperty("minimumEdition")&&(o.minimumEdition=t.enums!==String||void 0===a.google.protobuf.Edition[e.minimumEdition]?e.minimumEdition:a.google.protobuf.Edition[e.minimumEdition]),null!=e.maximumEdition&&e.hasOwnProperty("maximumEdition")&&(o.maximumEdition=t.enums!==String||void 0===a.google.protobuf.Edition[e.maximumEdition]?e.maximumEdition:a.google.protobuf.Edition[e.maximumEdition]),o},Et.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},Et.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.FeatureSetDefaults"},Et.FeatureSetEditionDefault=(wt.prototype.edition=0,wt.prototype.features=null,wt.fromObject=function(e){if(e instanceof a.google.protobuf.FeatureSetDefaults.FeatureSetEditionDefault)return e;var t=new a.google.protobuf.FeatureSetDefaults.FeatureSetEditionDefault;switch(e.edition){default:"number"==typeof e.edition&&(t.edition=e.edition);break;case"EDITION_UNKNOWN":case 0:t.edition=0;break;case"EDITION_PROTO2":case 998:t.edition=998;break;case"EDITION_PROTO3":case 999:t.edition=999;break;case"EDITION_2023":case 1e3:t.edition=1e3;break;case"EDITION_2024":case 1001:t.edition=1001;break;case"EDITION_1_TEST_ONLY":case 1:t.edition=1;break;case"EDITION_2_TEST_ONLY":case 2:t.edition=2;break;case"EDITION_99997_TEST_ONLY":case 99997:t.edition=99997;break;case"EDITION_99998_TEST_ONLY":case 99998:t.edition=99998;break;case"EDITION_99999_TEST_ONLY":case 99999:t.edition=99999;break;case"EDITION_MAX":case 2147483647:t.edition=2147483647}if(null!=e.features){if("object"!=typeof e.features)throw TypeError(".google.protobuf.FeatureSetDefaults.FeatureSetEditionDefault.features: object expected");t.features=a.google.protobuf.FeatureSet.fromObject(e.features)}return t},wt.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.features=null,o.edition=t.enums===String?"EDITION_UNKNOWN":0),null!=e.features&&e.hasOwnProperty("features")&&(o.features=a.google.protobuf.FeatureSet.toObject(e.features,t)),null!=e.edition&&e.hasOwnProperty("edition")&&(o.edition=t.enums!==String||void 0===a.google.protobuf.Edition[e.edition]?e.edition:a.google.protobuf.Edition[e.edition]),o},wt.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},wt.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.FeatureSetDefaults.FeatureSetEditionDefault"},wt),Et),t.SourceCodeInfo=(Nt.prototype.location=i.emptyArray,Nt.fromObject=function(e){if(e instanceof a.google.protobuf.SourceCodeInfo)return e;var t=new a.google.protobuf.SourceCodeInfo;if(e.location){if(!Array.isArray(e.location))throw TypeError(".google.protobuf.SourceCodeInfo.location: array expected");t.location=[];for(var o=0;o<e.location.length;++o){if("object"!=typeof e.location[o])throw TypeError(".google.protobuf.SourceCodeInfo.location: object expected");t.location[o]=a.google.protobuf.SourceCodeInfo.Location.fromObject(e.location[o])}}return t},Nt.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.location=[]),e.location&&e.location.length){o.location=[];for(var r=0;r<e.location.length;++r)o.location[r]=a.google.protobuf.SourceCodeInfo.Location.toObject(e.location[r],t)}return o},Nt.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},Nt.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.SourceCodeInfo"},Nt.Location=(Pt.prototype.path=i.emptyArray,Pt.prototype.span=i.emptyArray,Pt.prototype.leadingComments="",Pt.prototype.trailingComments="",Pt.prototype.leadingDetachedComments=i.emptyArray,Pt.fromObject=function(e){if(e instanceof a.google.protobuf.SourceCodeInfo.Location)return e;var t=new a.google.protobuf.SourceCodeInfo.Location;if(e.path){if(!Array.isArray(e.path))throw TypeError(".google.protobuf.SourceCodeInfo.Location.path: array expected");t.path=[];for(var o=0;o<e.path.length;++o)t.path[o]=0|e.path[o]}if(e.span){if(!Array.isArray(e.span))throw TypeError(".google.protobuf.SourceCodeInfo.Location.span: array expected");t.span=[];for(o=0;o<e.span.length;++o)t.span[o]=0|e.span[o]}if(null!=e.leadingComments&&(t.leadingComments=String(e.leadingComments)),null!=e.trailingComments&&(t.trailingComments=String(e.trailingComments)),e.leadingDetachedComments){if(!Array.isArray(e.leadingDetachedComments))throw TypeError(".google.protobuf.SourceCodeInfo.Location.leadingDetachedComments: array expected");t.leadingDetachedComments=[];for(o=0;o<e.leadingDetachedComments.length;++o)t.leadingDetachedComments[o]=String(e.leadingDetachedComments[o])}return t},Pt.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.path=[],o.span=[],o.leadingDetachedComments=[]),t.defaults&&(o.leadingComments="",o.trailingComments=""),e.path&&e.path.length){o.path=[];for(var r=0;r<e.path.length;++r)o.path[r]=e.path[r]}if(e.span&&e.span.length){o.span=[];for(r=0;r<e.span.length;++r)o.span[r]=e.span[r]}if(null!=e.leadingComments&&e.hasOwnProperty("leadingComments")&&(o.leadingComments=e.leadingComments),null!=e.trailingComments&&e.hasOwnProperty("trailingComments")&&(o.trailingComments=e.trailingComments),e.leadingDetachedComments&&e.leadingDetachedComments.length){o.leadingDetachedComments=[];for(r=0;r<e.leadingDetachedComments.length;++r)o.leadingDetachedComments[r]=e.leadingDetachedComments[r]}return o},Pt.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},Pt.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.SourceCodeInfo.Location"},Pt),Nt),t.GeneratedCodeInfo=(Rt.prototype.annotation=i.emptyArray,Rt.fromObject=function(e){if(e instanceof a.google.protobuf.GeneratedCodeInfo)return e;var t=new a.google.protobuf.GeneratedCodeInfo;if(e.annotation){if(!Array.isArray(e.annotation))throw TypeError(".google.protobuf.GeneratedCodeInfo.annotation: array expected");t.annotation=[];for(var o=0;o<e.annotation.length;++o){if("object"!=typeof e.annotation[o])throw TypeError(".google.protobuf.GeneratedCodeInfo.annotation: object expected");t.annotation[o]=a.google.protobuf.GeneratedCodeInfo.Annotation.fromObject(e.annotation[o])}}return t},Rt.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.annotation=[]),e.annotation&&e.annotation.length){o.annotation=[];for(var r=0;r<e.annotation.length;++r)o.annotation[r]=a.google.protobuf.GeneratedCodeInfo.Annotation.toObject(e.annotation[r],t)}return o},Rt.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},Rt.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.GeneratedCodeInfo"},Rt.Annotation=(Dt.prototype.path=i.emptyArray,Dt.prototype.sourceFile="",Dt.prototype.begin=0,Dt.prototype.end=0,Dt.prototype.semantic=0,Dt.fromObject=function(e){if(e instanceof a.google.protobuf.GeneratedCodeInfo.Annotation)return e;var t=new a.google.protobuf.GeneratedCodeInfo.Annotation;if(e.path){if(!Array.isArray(e.path))throw TypeError(".google.protobuf.GeneratedCodeInfo.Annotation.path: array expected");t.path=[];for(var o=0;o<e.path.length;++o)t.path[o]=0|e.path[o]}switch(null!=e.sourceFile&&(t.sourceFile=String(e.sourceFile)),null!=e.begin&&(t.begin=0|e.begin),null!=e.end&&(t.end=0|e.end),e.semantic){default:"number"==typeof e.semantic&&(t.semantic=e.semantic);break;case"NONE":case 0:t.semantic=0;break;case"SET":case 1:t.semantic=1;break;case"ALIAS":case 2:t.semantic=2}return t},Dt.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.path=[]),t.defaults&&(o.sourceFile="",o.begin=0,o.end=0,o.semantic=t.enums===String?"NONE":0),e.path&&e.path.length){o.path=[];for(var r=0;r<e.path.length;++r)o.path[r]=e.path[r]}return null!=e.sourceFile&&e.hasOwnProperty("sourceFile")&&(o.sourceFile=e.sourceFile),null!=e.begin&&e.hasOwnProperty("begin")&&(o.begin=e.begin),null!=e.end&&e.hasOwnProperty("end")&&(o.end=e.end),null!=e.semantic&&e.hasOwnProperty("semantic")&&(o.semantic=t.enums!==String||void 0===a.google.protobuf.GeneratedCodeInfo.Annotation.Semantic[e.semantic]?e.semantic:a.google.protobuf.GeneratedCodeInfo.Annotation.Semantic[e.semantic]),o},Dt.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},Dt.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.GeneratedCodeInfo.Annotation"},Dt.Semantic=(o={},(n=Object.create(o))[o[0]="NONE"]="NONE",n[o[1]="SET"]="SET",n[o[2]="ALIAS"]="ALIAS",n),Dt),Rt),t.Struct=(It.prototype.fields=i.emptyObject,It.fromObject=function(e){if(e instanceof a.google.protobuf.Struct)return e;var t=new a.google.protobuf.Struct;if(e.fields){if("object"!=typeof e.fields)throw TypeError(".google.protobuf.Struct.fields: object expected");t.fields={};for(var o=Object.keys(e.fields),r=0;r<o.length;++r){if("object"!=typeof e.fields[o[r]])throw TypeError(".google.protobuf.Struct.fields: object expected");t.fields[o[r]]=a.google.protobuf.Value.fromObject(e.fields[o[r]])}}return t},It.toObject=function(e,t){var o,r={};if(((t=t||{}).objects||t.defaults)&&(r.fields={}),e.fields&&(o=Object.keys(e.fields)).length){r.fields={};for(var n=0;n<o.length;++n)r.fields[o[n]]=a.google.protobuf.Value.toObject(e.fields[o[n]],t)}return r},It.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},It.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.Struct"},It),t.Value=(_.prototype.nullValue=null,_.prototype.numberValue=null,_.prototype.stringValue=null,_.prototype.boolValue=null,_.prototype.structValue=null,_.prototype.listValue=null,Object.defineProperty(_.prototype,"kind",{get:i.oneOfGetter(o=["nullValue","numberValue","stringValue","boolValue","structValue","listValue"]),set:i.oneOfSetter(o)}),_.fromObject=function(e){if(e instanceof a.google.protobuf.Value)return e;var t=new a.google.protobuf.Value;switch(e.nullValue){default:"number"==typeof e.nullValue&&(t.nullValue=e.nullValue);break;case"NULL_VALUE":case 0:t.nullValue=0}if(null!=e.numberValue&&(t.numberValue=Number(e.numberValue)),null!=e.stringValue&&(t.stringValue=String(e.stringValue)),null!=e.boolValue&&(t.boolValue=Boolean(e.boolValue)),null!=e.structValue){if("object"!=typeof e.structValue)throw TypeError(".google.protobuf.Value.structValue: object expected");t.structValue=a.google.protobuf.Struct.fromObject(e.structValue)}if(null!=e.listValue){if("object"!=typeof e.listValue)throw TypeError(".google.protobuf.Value.listValue: object expected");t.listValue=a.google.protobuf.ListValue.fromObject(e.listValue)}return t},_.toObject=function(e,t){t=t||{};var o={};return null!=e.nullValue&&e.hasOwnProperty("nullValue")&&(o.nullValue=t.enums!==String||void 0===a.google.protobuf.NullValue[e.nullValue]?e.nullValue:a.google.protobuf.NullValue[e.nullValue],t.oneofs)&&(o.kind="nullValue"),null!=e.numberValue&&e.hasOwnProperty("numberValue")&&(o.numberValue=t.json&&!isFinite(e.numberValue)?String(e.numberValue):e.numberValue,t.oneofs)&&(o.kind="numberValue"),null!=e.stringValue&&e.hasOwnProperty("stringValue")&&(o.stringValue=e.stringValue,t.oneofs)&&(o.kind="stringValue"),null!=e.boolValue&&e.hasOwnProperty("boolValue")&&(o.boolValue=e.boolValue,t.oneofs)&&(o.kind="boolValue"),null!=e.structValue&&e.hasOwnProperty("structValue")&&(o.structValue=a.google.protobuf.Struct.toObject(e.structValue,t),t.oneofs)&&(o.kind="structValue"),null!=e.listValue&&e.hasOwnProperty("listValue")&&(o.listValue=a.google.protobuf.ListValue.toObject(e.listValue,t),t.oneofs)&&(o.kind="listValue"),o},_.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},_.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.Value"},_),t.NullValue=(n={},(o=Object.create(n))[n[0]="NULL_VALUE"]="NULL_VALUE",o),t.ListValue=(At.prototype.values=i.emptyArray,At.fromObject=function(e){if(e instanceof a.google.protobuf.ListValue)return e;var t=new a.google.protobuf.ListValue;if(e.values){if(!Array.isArray(e.values))throw TypeError(".google.protobuf.ListValue.values: array expected");t.values=[];for(var o=0;o<e.values.length;++o){if("object"!=typeof e.values[o])throw TypeError(".google.protobuf.ListValue.values: object expected");t.values[o]=a.google.protobuf.Value.fromObject(e.values[o])}}return t},At.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.values=[]),e.values&&e.values.length){o.values=[];for(var r=0;r<e.values.length;++r)o.values[r]=a.google.protobuf.Value.toObject(e.values[r],t)}return o},At.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},At.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.ListValue"},At),t.Timestamp=(kt.prototype.seconds=i.Long?i.Long.fromBits(0,0,!1):0,kt.prototype.nanos=0,kt.fromObject=function(e){var t;return e instanceof a.google.protobuf.Timestamp?e:(t=new a.google.protobuf.Timestamp,null!=e.seconds&&(i.Long?(t.seconds=i.Long.fromValue(e.seconds)).unsigned=!1:"string"==typeof e.seconds?t.seconds=parseInt(e.seconds,10):"number"==typeof e.seconds?t.seconds=e.seconds:"object"==typeof e.seconds&&(t.seconds=new i.LongBits(e.seconds.low>>>0,e.seconds.high>>>0).toNumber())),null!=e.nanos&&(t.nanos=0|e.nanos),t)},kt.toObject=function(e,t){var o,r={};return(t=t||{}).defaults&&(i.Long?(o=new i.Long(0,0,!1),r.seconds=t.longs===String?o.toString():t.longs===Number?o.toNumber():o):r.seconds=t.longs===String?"0":0,r.nanos=0),null!=e.seconds&&e.hasOwnProperty("seconds")&&("number"==typeof e.seconds?r.seconds=t.longs===String?String(e.seconds):e.seconds:r.seconds=t.longs===String?i.Long.prototype.toString.call(e.seconds):t.longs===Number?new i.LongBits(e.seconds.low>>>0,e.seconds.high>>>0).toNumber():e.seconds),null!=e.nanos&&e.hasOwnProperty("nanos")&&(r.nanos=e.nanos),r},kt.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},kt.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.Timestamp"},kt),t.Duration=(xt.prototype.seconds=i.Long?i.Long.fromBits(0,0,!1):0,xt.prototype.nanos=0,xt.fromObject=function(e){var t;return e instanceof a.google.protobuf.Duration?e:(t=new a.google.protobuf.Duration,null!=e.seconds&&(i.Long?(t.seconds=i.Long.fromValue(e.seconds)).unsigned=!1:"string"==typeof e.seconds?t.seconds=parseInt(e.seconds,10):"number"==typeof e.seconds?t.seconds=e.seconds:"object"==typeof e.seconds&&(t.seconds=new i.LongBits(e.seconds.low>>>0,e.seconds.high>>>0).toNumber())),null!=e.nanos&&(t.nanos=0|e.nanos),t)},xt.toObject=function(e,t){var o,r={};return(t=t||{}).defaults&&(i.Long?(o=new i.Long(0,0,!1),r.seconds=t.longs===String?o.toString():t.longs===Number?o.toNumber():o):r.seconds=t.longs===String?"0":0,r.nanos=0),null!=e.seconds&&e.hasOwnProperty("seconds")&&("number"==typeof e.seconds?r.seconds=t.longs===String?String(e.seconds):e.seconds:r.seconds=t.longs===String?i.Long.prototype.toString.call(e.seconds):t.longs===Number?new i.LongBits(e.seconds.low>>>0,e.seconds.high>>>0).toNumber():e.seconds),null!=e.nanos&&e.hasOwnProperty("nanos")&&(r.nanos=e.nanos),r},xt.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},xt.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.Duration"},xt),t.DoubleValue=(Ft.prototype.value=0,Ft.fromObject=function(e){var t;return e instanceof a.google.protobuf.DoubleValue?e:(t=new a.google.protobuf.DoubleValue,null!=e.value&&(t.value=Number(e.value)),t)},Ft.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.value=0),null!=e.value&&e.hasOwnProperty("value")&&(o.value=t.json&&!isFinite(e.value)?String(e.value):e.value),o},Ft.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},Ft.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.DoubleValue"},Ft),t.FloatValue=(_t.prototype.value=0,_t.fromObject=function(e){var t;return e instanceof a.google.protobuf.FloatValue?e:(t=new a.google.protobuf.FloatValue,null!=e.value&&(t.value=Number(e.value)),t)},_t.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.value=0),null!=e.value&&e.hasOwnProperty("value")&&(o.value=t.json&&!isFinite(e.value)?String(e.value):e.value),o},_t.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},_t.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.FloatValue"},_t),t.Int64Value=(Ct.prototype.value=i.Long?i.Long.fromBits(0,0,!1):0,Ct.fromObject=function(e){var t;return e instanceof a.google.protobuf.Int64Value?e:(t=new a.google.protobuf.Int64Value,null!=e.value&&(i.Long?(t.value=i.Long.fromValue(e.value)).unsigned=!1:"string"==typeof e.value?t.value=parseInt(e.value,10):"number"==typeof e.value?t.value=e.value:"object"==typeof e.value&&(t.value=new i.LongBits(e.value.low>>>0,e.value.high>>>0).toNumber())),t)},Ct.toObject=function(e,t){var o,r={};return(t=t||{}).defaults&&(i.Long?(o=new i.Long(0,0,!1),r.value=t.longs===String?o.toString():t.longs===Number?o.toNumber():o):r.value=t.longs===String?"0":0),null!=e.value&&e.hasOwnProperty("value")&&("number"==typeof e.value?r.value=t.longs===String?String(e.value):e.value:r.value=t.longs===String?i.Long.prototype.toString.call(e.value):t.longs===Number?new i.LongBits(e.value.low>>>0,e.value.high>>>0).toNumber():e.value),r},Ct.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},Ct.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.Int64Value"},Ct),t.UInt64Value=(Vt.prototype.value=i.Long?i.Long.fromBits(0,0,!0):0,Vt.fromObject=function(e){var t;return e instanceof a.google.protobuf.UInt64Value?e:(t=new a.google.protobuf.UInt64Value,null!=e.value&&(i.Long?(t.value=i.Long.fromValue(e.value)).unsigned=!0:"string"==typeof e.value?t.value=parseInt(e.value,10):"number"==typeof e.value?t.value=e.value:"object"==typeof e.value&&(t.value=new i.LongBits(e.value.low>>>0,e.value.high>>>0).toNumber(!0))),t)},Vt.toObject=function(e,t){var o,r={};return(t=t||{}).defaults&&(i.Long?(o=new i.Long(0,0,!0),r.value=t.longs===String?o.toString():t.longs===Number?o.toNumber():o):r.value=t.longs===String?"0":0),null!=e.value&&e.hasOwnProperty("value")&&("number"==typeof e.value?r.value=t.longs===String?String(e.value):e.value:r.value=t.longs===String?i.Long.prototype.toString.call(e.value):t.longs===Number?new i.LongBits(e.value.low>>>0,e.value.high>>>0).toNumber(!0):e.value),r},Vt.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},Vt.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.UInt64Value"},Vt),t.Int32Value=(Lt.prototype.value=0,Lt.fromObject=function(e){var t;return e instanceof a.google.protobuf.Int32Value?e:(t=new a.google.protobuf.Int32Value,null!=e.value&&(t.value=0|e.value),t)},Lt.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.value=0),null!=e.value&&e.hasOwnProperty("value")&&(o.value=e.value),o},Lt.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},Lt.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.Int32Value"},Lt),t.UInt32Value=(Ut.prototype.value=0,Ut.fromObject=function(e){var t;return e instanceof a.google.protobuf.UInt32Value?e:(t=new a.google.protobuf.UInt32Value,null!=e.value&&(t.value=e.value>>>0),t)},Ut.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.value=0),null!=e.value&&e.hasOwnProperty("value")&&(o.value=e.value),o},Ut.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},Ut.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.UInt32Value"},Ut),t.BoolValue=(Bt.prototype.value=!1,Bt.fromObject=function(e){var t;return e instanceof a.google.protobuf.BoolValue?e:(t=new a.google.protobuf.BoolValue,null!=e.value&&(t.value=Boolean(e.value)),t)},Bt.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.value=!1),null!=e.value&&e.hasOwnProperty("value")&&(o.value=e.value),o},Bt.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},Bt.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.BoolValue"},Bt),t.StringValue=(Jt.prototype.value="",Jt.fromObject=function(e){var t;return e instanceof a.google.protobuf.StringValue?e:(t=new a.google.protobuf.StringValue,null!=e.value&&(t.value=String(e.value)),t)},Jt.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.value=""),null!=e.value&&e.hasOwnProperty("value")&&(o.value=e.value),o},Jt.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},Jt.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.StringValue"},Jt),t.BytesValue=(Qt.prototype.value=i.newBuffer([]),Qt.fromObject=function(e){var t;return e instanceof a.google.protobuf.BytesValue?e:(t=new a.google.protobuf.BytesValue,null!=e.value&&("string"==typeof e.value?i.base64.decode(e.value,t.value=i.newBuffer(i.base64.length(e.value)),0):0<=e.value.length&&(t.value=e.value)),t)},Qt.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(t.bytes===String?o.value="":(o.value=[],t.bytes!==Array&&(o.value=i.newBuffer(o.value)))),null!=e.value&&e.hasOwnProperty("value")&&(o.value=t.bytes===String?i.base64.encode(e.value,0,e.value.length):t.bytes===Array?Array.prototype.slice.call(e.value):e.value),o},Qt.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},Qt.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.BytesValue"},Qt),t.Empty=(Mt.fromObject=function(e){return e instanceof a.google.protobuf.Empty?e:new a.google.protobuf.Empty},Mt.toObject=function(){return{}},Mt.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},Mt.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.Empty"},Mt),t.Any=(Gt.prototype.type_url="",Gt.prototype.value=i.newBuffer([]),Gt.fromObject=function(e){var t;return e instanceof a.google.protobuf.Any?e:(t=new a.google.protobuf.Any,null!=e.type_url&&(t.type_url=String(e.type_url)),null!=e.value&&("string"==typeof e.value?i.base64.decode(e.value,t.value=i.newBuffer(i.base64.length(e.value)),0):0<=e.value.length&&(t.value=e.value)),t)},Gt.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.type_url="",t.bytes===String?o.value="":(o.value=[],t.bytes!==Array&&(o.value=i.newBuffer(o.value)))),null!=e.type_url&&e.hasOwnProperty("type_url")&&(o.type_url=e.type_url),null!=e.value&&e.hasOwnProperty("value")&&(o.value=t.bytes===String?i.base64.encode(e.value,0,e.value.length):t.bytes===Array?Array.prototype.slice.call(e.value):e.value),o},Gt.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},Gt.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.Any"},Gt),t.FieldMask=(qt.prototype.paths=i.emptyArray,qt.fromObject=function(e){if(e instanceof a.google.protobuf.FieldMask)return e;var t=new a.google.protobuf.FieldMask;if(e.paths){if(!Array.isArray(e.paths))throw TypeError(".google.protobuf.FieldMask.paths: array expected");t.paths=[];for(var o=0;o<e.paths.length;++o)t.paths[o]=String(e.paths[o])}return t},qt.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.paths=[]),e.paths&&e.paths.length){o.paths=[];for(var r=0;r<e.paths.length;++r)o.paths[r]=e.paths[r]}return o},qt.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},qt.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.FieldMask"},qt),t),C.type=((e={}).LatLng=(Yt.prototype.latitude=0,Yt.prototype.longitude=0,Yt.fromObject=function(e){var t;return e instanceof a.google.type.LatLng?e:(t=new a.google.type.LatLng,null!=e.latitude&&(t.latitude=Number(e.latitude)),null!=e.longitude&&(t.longitude=Number(e.longitude)),t)},Yt.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.latitude=0,o.longitude=0),null!=e.latitude&&e.hasOwnProperty("latitude")&&(o.latitude=t.json&&!isFinite(e.latitude)?String(e.latitude):e.latitude),null!=e.longitude&&e.hasOwnProperty("longitude")&&(o.longitude=t.json&&!isFinite(e.longitude)?String(e.longitude):e.longitude),o},Yt.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},Yt.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.type.LatLng"},Yt),e.DayOfWeek=(n={},(o=Object.create(n))[n[0]="DAY_OF_WEEK_UNSPECIFIED"]="DAY_OF_WEEK_UNSPECIFIED",o[n[1]="MONDAY"]="MONDAY",o[n[2]="TUESDAY"]="TUESDAY",o[n[3]="WEDNESDAY"]="WEDNESDAY",o[n[4]="THURSDAY"]="THURSDAY",o[n[5]="FRIDAY"]="FRIDAY",o[n[6]="SATURDAY"]="SATURDAY",o[n[7]="SUNDAY"]="SUNDAY",o),e),C.rpc=((t={}).Status=(Wt.prototype.code=0,Wt.prototype.message="",Wt.prototype.details=i.emptyArray,Wt.fromObject=function(e){if(e instanceof a.google.rpc.Status)return e;var t=new a.google.rpc.Status;if(null!=e.code&&(t.code=0|e.code),null!=e.message&&(t.message=String(e.message)),e.details){if(!Array.isArray(e.details))throw TypeError(".google.rpc.Status.details: array expected");t.details=[];for(var o=0;o<e.details.length;++o){if("object"!=typeof e.details[o])throw TypeError(".google.rpc.Status.details: object expected");t.details[o]=a.google.protobuf.Any.fromObject(e.details[o])}}return t},Wt.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.details=[]),t.defaults&&(o.code=0,o.message=""),null!=e.code&&e.hasOwnProperty("code")&&(o.code=e.code),null!=e.message&&e.hasOwnProperty("message")&&(o.message=e.message),e.details&&e.details.length){o.details=[];for(var r=0;r<e.details.length;++r)o.details[r]=a.google.protobuf.Any.toObject(e.details[r],t)}return o},Wt.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},Wt.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.rpc.Status"},Wt),t),C.longrunning=((n={}).Operations=((zt.prototype=Object.create(r.rpc.Service.prototype)).constructor=zt,Object.defineProperty(zt.prototype.listOperations=function e(t,o){return this.rpcCall(e,a.google.longrunning.ListOperationsRequest,a.google.longrunning.ListOperationsResponse,t,o)},"name",{value:"ListOperations"}),Object.defineProperty(zt.prototype.getOperation=function e(t,o){return this.rpcCall(e,a.google.longrunning.GetOperationRequest,a.google.longrunning.Operation,t,o)},"name",{value:"GetOperation"}),Object.defineProperty(zt.prototype.deleteOperation=function e(t,o){return this.rpcCall(e,a.google.longrunning.DeleteOperationRequest,a.google.protobuf.Empty,t,o)},"name",{value:"DeleteOperation"}),Object.defineProperty(zt.prototype.cancelOperation=function e(t,o){return this.rpcCall(e,a.google.longrunning.CancelOperationRequest,a.google.protobuf.Empty,t,o)},"name",{value:"CancelOperation"}),Object.defineProperty(zt.prototype.waitOperation=function e(t,o){return this.rpcCall(e,a.google.longrunning.WaitOperationRequest,a.google.longrunning.Operation,t,o)},"name",{value:"WaitOperation"}),zt),n.Operation=(Ht.prototype.name="",Ht.prototype.metadata=null,Ht.prototype.done=!1,Ht.prototype.error=null,Ht.prototype.response=null,Object.defineProperty(Ht.prototype,"result",{get:i.oneOfGetter(o=["error","response"]),set:i.oneOfSetter(o)}),Ht.fromObject=function(e){if(e instanceof a.google.longrunning.Operation)return e;var t=new a.google.longrunning.Operation;if(null!=e.name&&(t.name=String(e.name)),null!=e.metadata){if("object"!=typeof e.metadata)throw TypeError(".google.longrunning.Operation.metadata: object expected");t.metadata=a.google.protobuf.Any.fromObject(e.metadata)}if(null!=e.done&&(t.done=Boolean(e.done)),null!=e.error){if("object"!=typeof e.error)throw TypeError(".google.longrunning.Operation.error: object expected");t.error=a.google.rpc.Status.fromObject(e.error)}if(null!=e.response){if("object"!=typeof e.response)throw TypeError(".google.longrunning.Operation.response: object expected");t.response=a.google.protobuf.Any.fromObject(e.response)}return t},Ht.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.name="",o.metadata=null,o.done=!1),null!=e.name&&e.hasOwnProperty("name")&&(o.name=e.name),null!=e.metadata&&e.hasOwnProperty("metadata")&&(o.metadata=a.google.protobuf.Any.toObject(e.metadata,t)),null!=e.done&&e.hasOwnProperty("done")&&(o.done=e.done),null!=e.error&&e.hasOwnProperty("error")&&(o.error=a.google.rpc.Status.toObject(e.error,t),t.oneofs)&&(o.result="error"),null!=e.response&&e.hasOwnProperty("response")&&(o.response=a.google.protobuf.Any.toObject(e.response,t),t.oneofs)&&(o.result="response"),o},Ht.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},Ht.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.longrunning.Operation"},Ht),n.GetOperationRequest=(Kt.prototype.name="",Kt.fromObject=function(e){var t;return e instanceof a.google.longrunning.GetOperationRequest?e:(t=new a.google.longrunning.GetOperationRequest,null!=e.name&&(t.name=String(e.name)),t)},Kt.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.name=""),null!=e.name&&e.hasOwnProperty("name")&&(o.name=e.name),o},Kt.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},Kt.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.longrunning.GetOperationRequest"},Kt),n.ListOperationsRequest=(Xt.prototype.name="",Xt.prototype.filter="",Xt.prototype.pageSize=0,Xt.prototype.pageToken="",Xt.fromObject=function(e){var t;return e instanceof a.google.longrunning.ListOperationsRequest?e:(t=new a.google.longrunning.ListOperationsRequest,null!=e.name&&(t.name=String(e.name)),null!=e.filter&&(t.filter=String(e.filter)),null!=e.pageSize&&(t.pageSize=0|e.pageSize),null!=e.pageToken&&(t.pageToken=String(e.pageToken)),t)},Xt.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.filter="",o.pageSize=0,o.pageToken="",o.name=""),null!=e.filter&&e.hasOwnProperty("filter")&&(o.filter=e.filter),null!=e.pageSize&&e.hasOwnProperty("pageSize")&&(o.pageSize=e.pageSize),null!=e.pageToken&&e.hasOwnProperty("pageToken")&&(o.pageToken=e.pageToken),null!=e.name&&e.hasOwnProperty("name")&&(o.name=e.name),o},Xt.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},Xt.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.longrunning.ListOperationsRequest"},Xt),n.ListOperationsResponse=(Zt.prototype.operations=i.emptyArray,Zt.prototype.nextPageToken="",Zt.fromObject=function(e){if(e instanceof a.google.longrunning.ListOperationsResponse)return e;var t=new a.google.longrunning.ListOperationsResponse;if(e.operations){if(!Array.isArray(e.operations))throw TypeError(".google.longrunning.ListOperationsResponse.operations: array expected");t.operations=[];for(var o=0;o<e.operations.length;++o){if("object"!=typeof e.operations[o])throw TypeError(".google.longrunning.ListOperationsResponse.operations: object expected");t.operations[o]=a.google.longrunning.Operation.fromObject(e.operations[o])}}return null!=e.nextPageToken&&(t.nextPageToken=String(e.nextPageToken)),t},Zt.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.operations=[]),t.defaults&&(o.nextPageToken=""),e.operations&&e.operations.length){o.operations=[];for(var r=0;r<e.operations.length;++r)o.operations[r]=a.google.longrunning.Operation.toObject(e.operations[r],t)}return null!=e.nextPageToken&&e.hasOwnProperty("nextPageToken")&&(o.nextPageToken=e.nextPageToken),o},Zt.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},Zt.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.longrunning.ListOperationsResponse"},Zt),n.CancelOperationRequest=($t.prototype.name="",$t.fromObject=function(e){var t;return e instanceof a.google.longrunning.CancelOperationRequest?e:(t=new a.google.longrunning.CancelOperationRequest,null!=e.name&&(t.name=String(e.name)),t)},$t.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.name=""),null!=e.name&&e.hasOwnProperty("name")&&(o.name=e.name),o},$t.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},$t.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.longrunning.CancelOperationRequest"},$t),n.DeleteOperationRequest=(eo.prototype.name="",eo.fromObject=function(e){var t;return e instanceof a.google.longrunning.DeleteOperationRequest?e:(t=new a.google.longrunning.DeleteOperationRequest,null!=e.name&&(t.name=String(e.name)),t)},eo.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.name=""),null!=e.name&&e.hasOwnProperty("name")&&(o.name=e.name),o},eo.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},eo.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.longrunning.DeleteOperationRequest"},eo),n.WaitOperationRequest=(to.prototype.name="",to.prototype.timeout=null,to.fromObject=function(e){if(e instanceof a.google.longrunning.WaitOperationRequest)return e;var t=new a.google.longrunning.WaitOperationRequest;if(null!=e.name&&(t.name=String(e.name)),null!=e.timeout){if("object"!=typeof e.timeout)throw TypeError(".google.longrunning.WaitOperationRequest.timeout: object expected");t.timeout=a.google.protobuf.Duration.fromObject(e.timeout)}return t},to.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.name="",o.timeout=null),null!=e.name&&e.hasOwnProperty("name")&&(o.name=e.name),null!=e.timeout&&e.hasOwnProperty("timeout")&&(o.timeout=a.google.protobuf.Duration.toObject(e.timeout,t)),o},to.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},to.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.longrunning.WaitOperationRequest"},to),n.OperationInfo=(oo.prototype.responseType="",oo.prototype.metadataType="",oo.fromObject=function(e){var t;return e instanceof a.google.longrunning.OperationInfo?e:(t=new a.google.longrunning.OperationInfo,null!=e.responseType&&(t.responseType=String(e.responseType)),null!=e.metadataType&&(t.metadataType=String(e.metadataType)),t)},oo.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.responseType="",o.metadataType=""),null!=e.responseType&&e.hasOwnProperty("responseType")&&(o.responseType=e.responseType),null!=e.metadataType&&e.hasOwnProperty("metadataType")&&(o.metadataType=e.metadataType),o},oo.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},oo.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.longrunning.OperationInfo"},oo),n),C),a});