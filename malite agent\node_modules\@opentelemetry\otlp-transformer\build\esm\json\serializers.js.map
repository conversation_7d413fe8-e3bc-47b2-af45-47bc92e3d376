{"version": 3, "file": "serializers.js", "sourceRoot": "", "sources": ["../../../src/json/serializers.ts"], "names": [], "mappings": "AAkBA,OAAO,EAAE,+BAA+B,EAAE,MAAM,UAAU,CAAC;AAE3D,OAAO,EAAE,iCAAiC,EAAE,MAAM,YAAY,CAAC;AAI/D,OAAO,EAAE,8BAA8B,EAAE,MAAM,SAAS,CAAC;AAEzD,MAAM,CAAC,IAAM,mBAAmB,GAG5B;IACF,gBAAgB,EAAE,UAAC,GAAmB;QACpC,IAAM,OAAO,GAAG,+BAA+B,CAAC,GAAG,EAAE;YACnD,MAAM,EAAE,IAAI;YACZ,WAAW,EAAE,KAAK;SACnB,CAAC,CAAC;QACH,IAAM,OAAO,GAAG,IAAI,WAAW,EAAE,CAAC;QAClC,OAAO,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC;IACjD,CAAC;IACD,mBAAmB,EAAE,UAAC,GAAe;QACnC,IAAM,OAAO,GAAG,IAAI,WAAW,EAAE,CAAC;QAClC,OAAO,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,CAAgC,CAAC;IACxE,CAAC;CACF,CAAC;AAEF,MAAM,CAAC,IAAM,qBAAqB,GAG9B;IACF,gBAAgB,EAAE,UAAC,GAAsB;QACvC,IAAM,OAAO,GAAG,iCAAiC,CAAC,GAAG,EAAE;YACrD,WAAW,EAAE,KAAK;SACnB,CAAC,CAAC;QACH,IAAM,OAAO,GAAG,IAAI,WAAW,EAAE,CAAC;QAClC,OAAO,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC;IACjD,CAAC;IACD,mBAAmB,EAAE,UAAC,GAAe;QACnC,IAAM,OAAO,GAAG,IAAI,WAAW,EAAE,CAAC;QAClC,OAAO,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,CAAkC,CAAC;IAC1E,CAAC;CACF,CAAC;AAEF,MAAM,CAAC,IAAM,kBAAkB,GAG3B;IACF,gBAAgB,EAAE,UAAC,GAAwB;QACzC,IAAM,OAAO,GAAG,8BAA8B,CAAC,GAAG,EAAE;YAClD,MAAM,EAAE,IAAI;YACZ,WAAW,EAAE,KAAK;SACnB,CAAC,CAAC;QACH,IAAM,OAAO,GAAG,IAAI,WAAW,EAAE,CAAC;QAClC,OAAO,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC;IACjD,CAAC;IACD,mBAAmB,EAAE,UAAC,GAAe;QACnC,IAAM,OAAO,GAAG,IAAI,WAAW,EAAE,CAAC;QAClC,OAAO,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,CAA+B,CAAC;IACvE,CAAC;CACF,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { ISerializer } from '../common/i-serializer';\nimport { ReadableSpan } from '@opentelemetry/sdk-trace-base';\nimport { IExportTraceServiceResponse } from '../trace/types';\nimport { createExportTraceServiceRequest } from '../trace';\nimport { ResourceMetrics } from '@opentelemetry/sdk-metrics';\nimport { createExportMetricsServiceRequest } from '../metrics';\nimport { ReadableLogRecord } from '@opentelemetry/sdk-logs';\nimport { IExportMetricsServiceResponse } from '../metrics/types';\nimport { IExportLogsServiceResponse } from '../logs/types';\nimport { createExportLogsServiceRequest } from '../logs';\n\nexport const JsonTraceSerializer: ISerializer<\n  ReadableSpan[],\n  IExportTraceServiceResponse\n> = {\n  serializeRequest: (arg: ReadableSpan[]) => {\n    const request = createExportTraceServiceRequest(arg, {\n      useHex: true,\n      useLongBits: false,\n    });\n    const encoder = new TextEncoder();\n    return encoder.encode(JSON.stringify(request));\n  },\n  deserializeResponse: (arg: Uint8Array) => {\n    const decoder = new TextDecoder();\n    return JSON.parse(decoder.decode(arg)) as IExportTraceServiceResponse;\n  },\n};\n\nexport const JsonMetricsSerializer: ISerializer<\n  ResourceMetrics[],\n  IExportMetricsServiceResponse\n> = {\n  serializeRequest: (arg: ResourceMetrics[]) => {\n    const request = createExportMetricsServiceRequest(arg, {\n      useLongBits: false,\n    });\n    const encoder = new TextEncoder();\n    return encoder.encode(JSON.stringify(request));\n  },\n  deserializeResponse: (arg: Uint8Array) => {\n    const decoder = new TextDecoder();\n    return JSON.parse(decoder.decode(arg)) as IExportMetricsServiceResponse;\n  },\n};\n\nexport const JsonLogsSerializer: ISerializer<\n  ReadableLogRecord[],\n  IExportLogsServiceResponse\n> = {\n  serializeRequest: (arg: ReadableLogRecord[]) => {\n    const request = createExportLogsServiceRequest(arg, {\n      useHex: true,\n      useLongBits: false,\n    });\n    const encoder = new TextEncoder();\n    return encoder.encode(JSON.stringify(request));\n  },\n  deserializeResponse: (arg: Uint8Array) => {\n    const decoder = new TextDecoder();\n    return JSON.parse(decoder.decode(arg)) as IExportLogsServiceResponse;\n  },\n};\n"]}