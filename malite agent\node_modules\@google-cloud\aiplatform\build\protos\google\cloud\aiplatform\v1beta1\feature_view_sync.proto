// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.aiplatform.v1beta1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/timestamp.proto";
import "google/rpc/status.proto";
import "google/type/interval.proto";

option csharp_namespace = "Google.Cloud.AIPlatform.V1Beta1";
option go_package = "cloud.google.com/go/aiplatform/apiv1beta1/aiplatformpb;aiplatformpb";
option java_multiple_files = true;
option java_outer_classname = "FeatureViewSyncProto";
option java_package = "com.google.cloud.aiplatform.v1beta1";
option php_namespace = "Google\\Cloud\\AIPlatform\\V1beta1";
option ruby_package = "Google::Cloud::Aiplatform::V1beta1";

// FeatureViewSync is a representation of sync operation which copies data from
// data source to Feature View in Online Store.
message FeatureViewSync {
  option (google.api.resource) = {
    type: "aiplatform.googleapis.com/FeatureViewSync"
    pattern: "projects/{project}/locations/{location}/featureOnlineStores/{feature_online_store}/featureViews/{feature_view}/featureViewSyncs/feature_view_sync"
  };

  // Summary from the Sync job. For continuous syncs, the summary is updated
  // periodically. For batch syncs, it gets updated on completion of the sync.
  message SyncSummary {
    // Output only. Total number of rows synced.
    int64 row_synced = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. BigQuery slot milliseconds consumed for the sync job.
    int64 total_slot = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Lower bound of the system time watermark for the sync job. This is only
    // set for continuously syncing feature views.
    google.protobuf.Timestamp system_watermark_time = 5;
  }

  // Identifier. Name of the FeatureViewSync. Format:
  // `projects/{project}/locations/{location}/featureOnlineStores/{feature_online_store}/featureViews/{feature_view}/featureViewSyncs/{feature_view_sync}`
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // Output only. Time when this FeatureViewSync is created. Creation of a
  // FeatureViewSync means that the job is pending / waiting for sufficient
  // resources but may not have started the actual data transfer yet.
  google.protobuf.Timestamp create_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Time when this FeatureViewSync is finished.
  google.type.Interval run_time = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Final status of the FeatureViewSync.
  google.rpc.Status final_status = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Summary of the sync job.
  SyncSummary sync_summary = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Reserved for future use.
  bool satisfies_pzs = 7 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Reserved for future use.
  bool satisfies_pzi = 8 [(google.api.field_behavior) = OUTPUT_ONLY];
}
