{"interfaces": {"google.cloud.aiplatform.v1beta1.FeatureRegistryService": {"retry_codes": {"non_idempotent": [], "idempotent": ["DEADLINE_EXCEEDED", "UNAVAILABLE"]}, "retry_params": {"default": {"initial_retry_delay_millis": 100, "retry_delay_multiplier": 1.3, "max_retry_delay_millis": 60000, "initial_rpc_timeout_millis": 60000, "rpc_timeout_multiplier": 1, "max_rpc_timeout_millis": 60000, "total_timeout_millis": 600000}}, "methods": {"CreateFeatureGroup": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "GetFeatureGroup": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "ListFeatureGroups": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "UpdateFeatureGroup": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "DeleteFeatureGroup": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "CreateFeature": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "BatchCreateFeatures": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "GetFeature": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "ListFeatures": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "UpdateFeature": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "DeleteFeature": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "CreateFeatureMonitor": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "GetFeatureMonitor": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "ListFeatureMonitors": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "UpdateFeatureMonitor": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "DeleteFeatureMonitor": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "CreateFeatureMonitorJob": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "GetFeatureMonitorJob": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "ListFeatureMonitorJobs": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}}}}}