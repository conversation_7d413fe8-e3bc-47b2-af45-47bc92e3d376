{"version": 3, "sources": ["../../src/context-caching/types.ts"], "sourcesContent": ["/**\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { z } from 'genkit';\n\nexport const cacheConfigSchema = z.union([\n  z.boolean(),\n  z.object({ ttlSeconds: z.number().optional() }).passthrough(),\n]);\n\nexport type CacheConfig = z.infer<typeof cacheConfigSchema>;\n\nexport const cacheConfigDetailsSchema = z.object({\n  cacheConfig: cacheConfigSchema,\n  endOfCachedContents: z.number(),\n});\n\nexport type CacheConfigDetails = z.infer<typeof cacheConfigDetailsSchema>;\n"], "mappings": "AAgBA,SAAS,SAAS;AAEX,MAAM,oBAAoB,EAAE,MAAM;AAAA,EACvC,EAAE,QAAQ;AAAA,EACV,EAAE,OAAO,EAAE,YAAY,EAAE,OAAO,EAAE,SAAS,EAAE,CAAC,EAAE,YAAY;AAC9D,CAAC;AAIM,MAAM,2BAA2B,EAAE,OAAO;AAAA,EAC/C,aAAa;AAAA,EACb,qBAAqB,EAAE,OAAO;AAChC,CAAC;", "names": []}