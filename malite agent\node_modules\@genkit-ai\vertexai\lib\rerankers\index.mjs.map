{"version": 3, "sources": ["../../src/rerankers/index.ts"], "sourcesContent": ["/**\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Genkit } from 'genkit';\nimport { GenkitPlugin, genkitPlugin } from 'genkit/plugin';\nimport { CommonPluginOptions } from '../common/types.js';\nimport { RerankerOptions } from './types.js';\n\nimport { getDerivedParams } from '../common/index.js';\n\nimport { vertexAiRerankers } from './reranker.js';\n\nexport interface PluginOptions extends CommonPluginOptions, RerankerOptions {}\n\n/**\n * Add Google Cloud Vertex AI Rerankers API to Genkit.\n */\nexport function vertexAIRerankers(options: PluginOptions): GenkitPlugin {\n  return genkitPlugin('vertexAIRerankers', async (ai: Genkit) => {\n    const { projectId, location, authClient } = await getDerivedParams(options);\n\n    await vertexAiRerankers(ai, {\n      projectId,\n      location,\n      authClient,\n      rerankOptions: options.rerankers.map((o) =>\n        typeof o === 'string' ? { model: o } : o\n      ),\n    });\n  });\n}\n"], "mappings": "AAiBA,SAAuB,oBAAoB;AAI3C,SAAS,wBAAwB;AAEjC,SAAS,yBAAyB;AAO3B,SAAS,kBAAkB,SAAsC;AACtE,SAAO,aAAa,qBAAqB,OAAO,OAAe;AAC7D,UAAM,EAAE,WAAW,UAAU,WAAW,IAAI,MAAM,iBAAiB,OAAO;AAE1E,UAAM,kBAAkB,IAAI;AAAA,MAC1B;AAAA,MACA;AAAA,MACA;AAAA,MACA,eAAe,QAAQ,UAAU;AAAA,QAAI,CAAC,MACpC,OAAO,MAAM,WAAW,EAAE,OAAO,EAAE,IAAI;AAAA,MACzC;AAAA,IACF,CAAC;AAAA,EACH,CAAC;AACH;", "names": []}