{"version": 3, "file": "getMachineId-unsupported.js", "sourceRoot": "", "sources": ["../../../../../../src/detectors/platform/node/machine-id/getMachineId-unsupported.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,4CAA0C;AAEnC,KAAK,UAAU,YAAY;IAChC,UAAI,CAAC,KAAK,CAAC,iDAAiD,CAAC,CAAC;IAC9D,OAAO,EAAE,CAAC;AACZ,CAAC;AAHD,oCAGC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { diag } from '@opentelemetry/api';\n\nexport async function getMachineId(): Promise<string> {\n  diag.debug('could not read machine-id: unsupported platform');\n  return '';\n}\n"]}