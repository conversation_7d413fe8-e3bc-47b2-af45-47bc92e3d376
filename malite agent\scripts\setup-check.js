#!/usr/bin/env node

/**
 * فحص إعدادات المشروع والتأكد من جاهزيته للتشغيل
 */

import { existsSync, readFileSync } from 'fs';
import { fileURLToPath } from 'url';
import path from 'path';
import dotenv from 'dotenv';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// تحميل متغيرات البيئة
dotenv.config({ path: path.join(__dirname, '..', '.env') });

console.log('\n🔍 ===== فحص إعدادات مشروع Malite Agent =====\n');

let allChecksPass = true;

// فحص ملف package.json
console.log('📦 فحص ملف package.json...');
const packagePath = path.join(__dirname, '..', 'package.json');
if (existsSync(packagePath)) {
  try {
    const packageInfo = JSON.parse(readFileSync(packagePath, 'utf8'));
    console.log('   ✅ ملف package.json موجود وصالح');
    console.log(`   📋 اسم المشروع: ${packageInfo.name}`);
    console.log(`   🔢 الإصدار: ${packageInfo.version}`);
  } catch (error) {
    console.log('   ❌ خطأ في قراءة ملف package.json');
    allChecksPass = false;
  }
} else {
  console.log('   ❌ ملف package.json غير موجود');
  allChecksPass = false;
}

// فحص ملف .env
console.log('\n🔐 فحص ملف .env...');
const envPath = path.join(__dirname, '..', '.env');
if (existsSync(envPath)) {
  console.log('   ✅ ملف .env موجود');
  
  // فحص متغيرات البيئة المطلوبة
  const requiredEnvVars = ['GEMINI_API_KEY'];
  
  requiredEnvVars.forEach(varName => {
    if (process.env[varName]) {
      console.log(`   ✅ ${varName}: موجود`);
    } else {
      console.log(`   ❌ ${varName}: غير موجود أو فارغ`);
      allChecksPass = false;
    }
  });
} else {
  console.log('   ❌ ملف .env غير موجود');
  console.log('   💡 قم بإنشاء ملف .env وإضافة GEMINI_API_KEY');
  allChecksPass = false;
}

// فحص مجلد node_modules
console.log('\n📚 فحص التبعيات...');
const nodeModulesPath = path.join(__dirname, '..', 'node_modules');
if (existsSync(nodeModulesPath)) {
  console.log('   ✅ مجلد node_modules موجود');
  
  // فحص التبعيات الأساسية
  const requiredDeps = [
    '@genkit-ai/googleai',
    '@genkit-ai/vertexai',
    'genkit',
    'dotenv'
  ];
  
  requiredDeps.forEach(dep => {
    const depPath = path.join(nodeModulesPath, dep);
    if (existsSync(depPath)) {
      console.log(`   ✅ ${dep}: مثبت`);
    } else {
      console.log(`   ❌ ${dep}: غير مثبت`);
      allChecksPass = false;
    }
  });
} else {
  console.log('   ❌ مجلد node_modules غير موجود');
  console.log('   💡 قم بتشغيل: npm install');
  allChecksPass = false;
}

// فحص الملفات الأساسية
console.log('\n📄 فحص الملفات الأساسية...');
const requiredFiles = [
  'index.js',
  'README.md',
  'vertex-ai/index.js',
  'vertex-ai/config.js',
  'vertex-ai/utils.js'
];

requiredFiles.forEach(file => {
  const filePath = path.join(__dirname, '..', file);
  if (existsSync(filePath)) {
    console.log(`   ✅ ${file}: موجود`);
  } else {
    console.log(`   ❌ ${file}: غير موجود`);
    allChecksPass = false;
  }
});

// فحص مجلد .genkit
console.log('\n🛠️ فحص مجلد Genkit...');
const genkitPath = path.join(__dirname, '..', '.genkit');
if (existsSync(genkitPath)) {
  console.log('   ✅ مجلد .genkit موجود');
  console.log('   📊 واجهة المطور جاهزة للاستخدام');
} else {
  console.log('   ⚠️ مجلد .genkit غير موجود');
  console.log('   💡 سيتم إنشاؤه عند أول تشغيل');
}

// النتيجة النهائية
console.log('\n🎯 ===== نتيجة الفحص =====\n');

if (allChecksPass) {
  console.log('🎉 تهانينا! المشروع جاهز للتشغيل');
  console.log('\n🚀 يمكنك الآن تشغيل:');
  console.log('   npm start           # للتشغيل الأساسي');
  console.log('   npm run dev         # لواجهة المطور');
  console.log('   npm run vertex:arabic # للأمثلة العربية');
} else {
  console.log('⚠️ يوجد مشاكل تحتاج إلى حل قبل التشغيل');
  console.log('\n🔧 خطوات الإصلاح:');
  console.log('1. تأكد من تثبيت Node.js (الإصدار 18 أو أعلى)');
  console.log('2. قم بتشغيل: npm install');
  console.log('3. أنشئ ملف .env وأضف GEMINI_API_KEY');
  console.log('4. قم بتشغيل هذا الفحص مرة أخرى: npm run setup');
}

console.log('\n✨ ===== انتهى الفحص =====\n');

process.exit(allChecksPass ? 0 : 1);
