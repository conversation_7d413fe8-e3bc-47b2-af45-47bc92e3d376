import type { LogRecordExporter } from './../../../export/LogRecordExporter';
import type { BatchLogRecordProcessorBrowserConfig } from '../../../types';
import { BatchLogRecordProcessorBase } from '../../../export/BatchLogRecordProcessorBase';
export declare class BatchLogRecordProcessor extends BatchLogRecordProcessorBase<BatchLogRecordProcessorBrowserConfig> {
    private _visibilityChangeListener?;
    private _pageHideListener?;
    constructor(exporter: LogRecordExporter, config?: BatchLogRecordProcessorBrowserConfig);
    protected onShutdown(): void;
    private _onInit;
}
//# sourceMappingURL=BatchLogRecordProcessor.d.ts.map