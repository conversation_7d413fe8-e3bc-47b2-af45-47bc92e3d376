{"version": 3, "sources": ["../src/index.ts"], "sourcesContent": ["/**\n * @license\n *\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * @module /\n */\n\nimport {\n  embedderRef,\n  EmbedderReference,\n  Genkit,\n  modelActionMetadata,\n  modelRef,\n  ModelReference,\n  z,\n} from 'genkit';\nimport { GenkitPlugin, genkitPlugin } from 'genkit/plugin';\nimport { ActionType } from 'genkit/registry';\nimport { getDerivedParams } from './common/index.js';\nimport { PluginOptions } from './common/types.js';\nimport {\n  defineVertexAIEmbedder,\n  multimodalEmbedding001,\n  SUPPORTED_EMBEDDER_MODELS,\n  textEmbedding004,\n  textEmbedding005,\n  textEmbeddingGecko003,\n  textEmbeddingGeckoMultilingual001,\n  textMultilingualEmbedding002,\n  VertexEmbeddingConfig,\n  VertexEmbeddingConfigSchema,\n} from './embedder.js';\nimport {\n  defineGeminiKnownModel,\n  defineGeminiModel,\n  gemini,\n  gemini10Pro,\n  gemini15Flash,\n  gemini15Pro,\n  gemini20Flash,\n  gemini20Flash001,\n  gemini20FlashLite,\n  gemini20FlashLitePreview0205,\n  gemini20ProExp0205,\n  gemini25FlashPreview0417,\n  gemini25ProExp0325,\n  gemini25ProPreview0325,\n  GeminiConfigSchema,\n  SafetySettingsSchema,\n  SUPPORTED_GEMINI_MODELS,\n  type GeminiConfig,\n  type GeminiVersionString,\n} from './gemini.js';\nimport {\n  ACTUAL_IMAGEN_MODELS,\n  defineImagenModel,\n  GENERIC_IMAGEN_INFO,\n  imagen2,\n  imagen3,\n  imagen3Fast,\n  ImagenConfigSchema,\n  SUPPORTED_IMAGEN_MODELS,\n} from './imagen.js';\nimport { listModels } from './list-models.js';\nexport { type PluginOptions } from './common/types.js';\nexport {\n  gemini,\n  gemini10Pro,\n  gemini15Flash,\n  gemini15Pro,\n  gemini20Flash,\n  gemini20Flash001,\n  gemini20FlashLite,\n  gemini20FlashLitePreview0205,\n  gemini20ProExp0205,\n  gemini25FlashPreview0417,\n  gemini25ProExp0325,\n  gemini25ProPreview0325,\n  GeminiConfigSchema,\n  imagen2,\n  imagen3,\n  imagen3Fast,\n  ImagenConfigSchema,\n  multimodalEmbedding001,\n  SafetySettingsSchema,\n  textEmbedding004,\n  textEmbedding005,\n  textEmbeddingGecko003,\n  textEmbeddingGeckoMultilingual001,\n  textMultilingualEmbedding002,\n  type GeminiConfig,\n  type GeminiVersionString,\n};\n\nasync function initializer(ai: Genkit, options?: PluginOptions) {\n  const { projectId, location, vertexClientFactory, authClient } =\n    await getDerivedParams(options);\n\n  Object.keys(SUPPORTED_IMAGEN_MODELS).map((name) =>\n    defineImagenModel(ai, name, authClient, { projectId, location })\n  );\n  Object.keys(SUPPORTED_GEMINI_MODELS).map((name) =>\n    defineGeminiKnownModel(\n      ai,\n      name,\n      vertexClientFactory,\n      {\n        projectId,\n        location,\n      },\n      options?.experimental_debugTraces\n    )\n  );\n  if (options?.models) {\n    for (const modelOrRef of options?.models) {\n      const modelName =\n        typeof modelOrRef === 'string'\n          ? modelOrRef\n          : // strip out the `vertexai/` prefix\n            modelOrRef.name.split('/')[1];\n      const modelRef =\n        typeof modelOrRef === 'string' ? gemini(modelOrRef) : modelOrRef;\n      defineGeminiModel({\n        ai,\n        modelName: modelRef.name,\n        version: modelName,\n        modelInfo: modelRef.info,\n        vertexClientFactory,\n        options: {\n          projectId,\n          location,\n        },\n        debugTraces: options.experimental_debugTraces,\n      });\n    }\n  }\n\n  Object.keys(SUPPORTED_EMBEDDER_MODELS).map((name) =>\n    defineVertexAIEmbedder(ai, name, authClient, { projectId, location })\n  );\n}\n\nasync function resolver(\n  ai: Genkit,\n  actionType: ActionType,\n  actionName: string,\n  options?: PluginOptions\n) {\n  // TODO: also support other actions like 'embedder'\n  switch (actionType) {\n    case 'model':\n      await resolveModel(ai, actionName, options);\n      break;\n    case 'embedder':\n      await resolveEmbedder(ai, actionName, options);\n      break;\n    default:\n    // no-op\n  }\n}\n\nasync function resolveModel(\n  ai: Genkit,\n  actionName: string,\n  options?: PluginOptions\n) {\n  const { projectId, location, vertexClientFactory, authClient } =\n    await getDerivedParams(options);\n\n  if (actionName.startsWith('imagen')) {\n    defineImagenModel(ai, actionName, authClient, { projectId, location });\n    return;\n  }\n\n  const modelRef = gemini(actionName);\n  defineGeminiModel({\n    ai,\n    modelName: modelRef.name,\n    version: actionName,\n    modelInfo: modelRef.info,\n    vertexClientFactory,\n    options: {\n      projectId,\n      location,\n    },\n    debugTraces: options?.experimental_debugTraces,\n  });\n}\n\nasync function resolveEmbedder(\n  ai: Genkit,\n  actionName: string,\n  options?: PluginOptions\n) {\n  const { projectId, location, authClient } = await getDerivedParams(options);\n\n  defineVertexAIEmbedder(ai, actionName, authClient, { projectId, location });\n}\n\n// Vertex AI list models still returns these and the API does not indicate in any way\n// that those models are not served anymore.\nconst KNOWN_DECOMISSIONED_MODELS = [\n  'gemini-pro-vision',\n  'gemini-pro',\n  'gemini-ultra',\n  'gemini-ultra-vision',\n];\n\nasync function listActions(options?: PluginOptions) {\n  const { location, projectId, authClient } = await getDerivedParams(options);\n  const models = await listModels(authClient, location, projectId);\n  // Vertex has a lot of models, and no way to figure out the \"type\" of the model...\n  // so, for list actions we only fetch known model \"families\".\n  return [\n    // Gemini\n    ...models\n      .filter(\n        (m) =>\n          m.name.includes('gemini') &&\n          !KNOWN_DECOMISSIONED_MODELS.includes(m.name.split('/').at(-1)!)\n      )\n      .map((m) => {\n        const ref = gemini(m.name.split('/').at(-1)!);\n\n        return modelActionMetadata({\n          name: ref.name,\n          info: ref.info,\n          configSchema: GeminiConfigSchema,\n        });\n      }),\n    // Imagen\n    ...models\n      .filter((m) => m.name.includes('imagen'))\n      .map((m) => {\n        const name = m.name.split('/').at(-1)!;\n\n        return modelActionMetadata({\n          name: 'vertexai/' + name,\n          info: {\n            ...GENERIC_IMAGEN_INFO,\n            label: `Vertex AI - ${name}`,\n          },\n          configSchema: ImagenConfigSchema,\n        });\n      }),\n  ];\n}\n\n/**\n * Add Google Cloud Vertex AI to Genkit. Includes Gemini and Imagen models and text embedder.\n */\nfunction vertexAIPlugin(options?: PluginOptions): GenkitPlugin {\n  let listActionsCache;\n  return genkitPlugin(\n    'vertexai',\n    async (ai: Genkit) => await initializer(ai, options),\n    async (ai: Genkit, actionType: ActionType, actionName: string) =>\n      await resolver(ai, actionType, actionName, options),\n    async () => {\n      if (listActionsCache) return listActionsCache;\n      listActionsCache = await listActions(options);\n      return listActionsCache;\n    }\n  );\n}\n\nexport type VertexAIPlugin = {\n  (params?: PluginOptions): GenkitPlugin;\n  model(\n    name: keyof typeof SUPPORTED_GEMINI_MODELS | (`gemini-${string}` & {}),\n    config?: z.infer<typeof GeminiConfigSchema>\n  ): ModelReference<typeof GeminiConfigSchema>;\n  model(\n    name: keyof typeof ACTUAL_IMAGEN_MODELS | (`imagen${string}` & {}),\n    config?: z.infer<typeof ImagenConfigSchema>\n  ): ModelReference<typeof ImagenConfigSchema>;\n  model(name: string, config?: any): ModelReference<z.ZodTypeAny>;\n  embedder(\n    name: string,\n    config?: VertexEmbeddingConfig\n  ): EmbedderReference<typeof VertexEmbeddingConfigSchema>;\n};\n\n/**\n * Google Cloud Vertex AI plugin for Genkit.\n * Includes Gemini and Imagen models and text embedder.\n */\nexport const vertexAI = vertexAIPlugin as VertexAIPlugin;\n// provide generic implementation for the model function overloads.\n(vertexAI as any).model = (\n  name: string,\n  config?: any\n): ModelReference<z.ZodTypeAny> => {\n  if (name.startsWith('imagen')) {\n    return modelRef({\n      name: `vertexai/${name}`,\n      config,\n      configSchema: ImagenConfigSchema,\n    });\n  }\n  return modelRef({\n    name: `vertexai/${name}`,\n    config,\n    configSchema: GeminiConfigSchema,\n  });\n};\nvertexAI.embedder = (\n  name: string,\n  config?: VertexEmbeddingConfig\n): EmbedderReference<typeof VertexEmbeddingConfigSchema> => {\n  return embedderRef({\n    name: `vertexai/${name}`,\n    config,\n    configSchema: VertexEmbeddingConfigSchema,\n  });\n};\n\nexport default vertexAI;\n"], "mappings": "AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAsBA;AAAA,EACE;AAAA,EAGA;AAAA,EACA;AAAA,OAGK;AACP,SAAuB,oBAAoB;AAE3C,SAAS,wBAAwB;AAEjC;AAAA,EACE;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EAEA;AAAA,OACK;AACP;AAAA,EACE;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,OAGK;AACP;AAAA,EAEE;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,OACK;AACP,SAAS,kBAAkB;AA+B3B,eAAe,YAAY,IAAY,SAAyB;AAC9D,QAAM,EAAE,WAAW,UAAU,qBAAqB,WAAW,IAC3D,MAAM,iBAAiB,OAAO;AAEhC,SAAO,KAAK,uBAAuB,EAAE;AAAA,IAAI,CAAC,SACxC,kBAAkB,IAAI,MAAM,YAAY,EAAE,WAAW,SAAS,CAAC;AAAA,EACjE;AACA,SAAO,KAAK,uBAAuB,EAAE;AAAA,IAAI,CAAC,SACxC;AAAA,MACE;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,MACF;AAAA,MACA,SAAS;AAAA,IACX;AAAA,EACF;AACA,MAAI,SAAS,QAAQ;AACnB,eAAW,cAAc,SAAS,QAAQ;AACxC,YAAM,YACJ,OAAO,eAAe,WAClB;AAAA;AAAA,QAEA,WAAW,KAAK,MAAM,GAAG,EAAE,CAAC;AAAA;AAClC,YAAMA,YACJ,OAAO,eAAe,WAAW,OAAO,UAAU,IAAI;AACxD,wBAAkB;AAAA,QAChB;AAAA,QACA,WAAWA,UAAS;AAAA,QACpB,SAAS;AAAA,QACT,WAAWA,UAAS;AAAA,QACpB;AAAA,QACA,SAAS;AAAA,UACP;AAAA,UACA;AAAA,QACF;AAAA,QACA,aAAa,QAAQ;AAAA,MACvB,CAAC;AAAA,IACH;AAAA,EACF;AAEA,SAAO,KAAK,yBAAyB,EAAE;AAAA,IAAI,CAAC,SAC1C,uBAAuB,IAAI,MAAM,YAAY,EAAE,WAAW,SAAS,CAAC;AAAA,EACtE;AACF;AAEA,eAAe,SACb,IACA,YACA,YACA,SACA;AAEA,UAAQ,YAAY;AAAA,IAClB,KAAK;AACH,YAAM,aAAa,IAAI,YAAY,OAAO;AAC1C;AAAA,IACF,KAAK;AACH,YAAM,gBAAgB,IAAI,YAAY,OAAO;AAC7C;AAAA,IACF;AAAA,EAEF;AACF;AAEA,eAAe,aACb,IACA,YACA,SACA;AACA,QAAM,EAAE,WAAW,UAAU,qBAAqB,WAAW,IAC3D,MAAM,iBAAiB,OAAO;AAEhC,MAAI,WAAW,WAAW,QAAQ,GAAG;AACnC,sBAAkB,IAAI,YAAY,YAAY,EAAE,WAAW,SAAS,CAAC;AACrE;AAAA,EACF;AAEA,QAAMA,YAAW,OAAO,UAAU;AAClC,oBAAkB;AAAA,IAChB;AAAA,IACA,WAAWA,UAAS;AAAA,IACpB,SAAS;AAAA,IACT,WAAWA,UAAS;AAAA,IACpB;AAAA,IACA,SAAS;AAAA,MACP;AAAA,MACA;AAAA,IACF;AAAA,IACA,aAAa,SAAS;AAAA,EACxB,CAAC;AACH;AAEA,eAAe,gBACb,IACA,YACA,SACA;AACA,QAAM,EAAE,WAAW,UAAU,WAAW,IAAI,MAAM,iBAAiB,OAAO;AAE1E,yBAAuB,IAAI,YAAY,YAAY,EAAE,WAAW,SAAS,CAAC;AAC5E;AAIA,MAAM,6BAA6B;AAAA,EACjC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAEA,eAAe,YAAY,SAAyB;AAClD,QAAM,EAAE,UAAU,WAAW,WAAW,IAAI,MAAM,iBAAiB,OAAO;AAC1E,QAAM,SAAS,MAAM,WAAW,YAAY,UAAU,SAAS;AAG/D,SAAO;AAAA;AAAA,IAEL,GAAG,OACA;AAAA,MACC,CAAC,MACC,EAAE,KAAK,SAAS,QAAQ,KACxB,CAAC,2BAA2B,SAAS,EAAE,KAAK,MAAM,GAAG,EAAE,GAAG,EAAE,CAAE;AAAA,IAClE,EACC,IAAI,CAAC,MAAM;AACV,YAAM,MAAM,OAAO,EAAE,KAAK,MAAM,GAAG,EAAE,GAAG,EAAE,CAAE;AAE5C,aAAO,oBAAoB;AAAA,QACzB,MAAM,IAAI;AAAA,QACV,MAAM,IAAI;AAAA,QACV,cAAc;AAAA,MAChB,CAAC;AAAA,IACH,CAAC;AAAA;AAAA,IAEH,GAAG,OACA,OAAO,CAAC,MAAM,EAAE,KAAK,SAAS,QAAQ,CAAC,EACvC,IAAI,CAAC,MAAM;AACV,YAAM,OAAO,EAAE,KAAK,MAAM,GAAG,EAAE,GAAG,EAAE;AAEpC,aAAO,oBAAoB;AAAA,QACzB,MAAM,cAAc;AAAA,QACpB,MAAM;AAAA,UACJ,GAAG;AAAA,UACH,OAAO,eAAe,IAAI;AAAA,QAC5B;AAAA,QACA,cAAc;AAAA,MAChB,CAAC;AAAA,IACH,CAAC;AAAA,EACL;AACF;AAKA,SAAS,eAAe,SAAuC;AAC7D,MAAI;AACJ,SAAO;AAAA,IACL;AAAA,IACA,OAAO,OAAe,MAAM,YAAY,IAAI,OAAO;AAAA,IACnD,OAAO,IAAY,YAAwB,eACzC,MAAM,SAAS,IAAI,YAAY,YAAY,OAAO;AAAA,IACpD,YAAY;AACV,UAAI,iBAAkB,QAAO;AAC7B,yBAAmB,MAAM,YAAY,OAAO;AAC5C,aAAO;AAAA,IACT;AAAA,EACF;AACF;AAuBO,MAAM,WAAW;AAEvB,SAAiB,QAAQ,CACxB,MACA,WACiC;AACjC,MAAI,KAAK,WAAW,QAAQ,GAAG;AAC7B,WAAO,SAAS;AAAA,MACd,MAAM,YAAY,IAAI;AAAA,MACtB;AAAA,MACA,cAAc;AAAA,IAChB,CAAC;AAAA,EACH;AACA,SAAO,SAAS;AAAA,IACd,MAAM,YAAY,IAAI;AAAA,IACtB;AAAA,IACA,cAAc;AAAA,EAChB,CAAC;AACH;AACA,SAAS,WAAW,CAClB,MACA,WAC0D;AAC1D,SAAO,YAAY;AAAA,IACjB,MAAM,YAAY,IAAI;AAAA,IACtB;AAAA,IACA,cAAc;AAAA,EAChB,CAAC;AACH;AAEA,IAAO,cAAQ;", "names": ["modelRef"]}