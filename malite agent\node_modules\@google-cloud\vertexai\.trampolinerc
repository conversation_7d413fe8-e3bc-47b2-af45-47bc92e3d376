# Copyright 2020 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Template for .trampolinerc

# Add required env vars here.
required_envvars+=(
)

# Add env vars which are passed down into the container here.
pass_down_envvars+=(
    "AUTORELEASE_PR"
    "VERSION"
)

# Prevent unintentional override on the default image.
if [[ "${TRAMPOLINE_IMAGE_UPLOAD:-false}" == "true" ]] && \
   [[ -z "${TRAMPOLINE_IMAGE:-}" ]]; then
   echo "Please set TRAMPOLINE_IMAGE if you want to upload the Docker image."
   exit 1
fi

# Define the default value if it makes sense.
if [[ -z "${TRAMPOLINE_IMAGE_UPLOAD:-}" ]]; then
    TRAMPOLINE_IMAGE_UPLOAD=""
fi

if [[ -z "${TRAMPOLINE_IMAGE:-}" ]]; then
    TRAMPOLINE_IMAGE=""
fi

if [[ -z "${TRAMPOLINE_DOCKERFILE:-}" ]]; then
    TRAMPOLINE_DOCKERFILE=""
fi

if [[ -z "${TRAMPOLINE_BUILD_FILE:-}" ]]; then
    TRAMPOLINE_BUILD_FILE=""
fi

# Secret Manager secrets.
source ${PROJECT_ROOT}/.kokoro/populate-secrets.sh