{"interfaces": {"google.cloud.aiplatform.v1beta1.TensorboardService": {"retry_codes": {"non_idempotent": [], "idempotent": ["DEADLINE_EXCEEDED", "UNAVAILABLE"]}, "retry_params": {"default": {"initial_retry_delay_millis": 100, "retry_delay_multiplier": 1.3, "max_retry_delay_millis": 60000, "initial_rpc_timeout_millis": 60000, "rpc_timeout_multiplier": 1, "max_rpc_timeout_millis": 60000, "total_timeout_millis": 600000}}, "methods": {"CreateTensorboard": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "GetTensorboard": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "UpdateTensorboard": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "ListTensorboards": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "DeleteTensorboard": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "ReadTensorboardUsage": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "ReadTensorboardSize": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "CreateTensorboardExperiment": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "GetTensorboardExperiment": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "UpdateTensorboardExperiment": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "ListTensorboardExperiments": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "DeleteTensorboardExperiment": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "CreateTensorboardRun": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "BatchCreateTensorboardRuns": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "GetTensorboardRun": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "UpdateTensorboardRun": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "ListTensorboardRuns": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "DeleteTensorboardRun": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "BatchCreateTensorboardTimeSeries": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "CreateTensorboardTimeSeries": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "GetTensorboardTimeSeries": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "UpdateTensorboardTimeSeries": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "ListTensorboardTimeSeries": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "DeleteTensorboardTimeSeries": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "BatchReadTensorboardTimeSeriesData": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "ReadTensorboardTimeSeriesData": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "ReadTensorboardBlobData": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "WriteTensorboardExperimentData": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "WriteTensorboardRunData": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "ExportTensorboardTimeSeriesData": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}}}}}