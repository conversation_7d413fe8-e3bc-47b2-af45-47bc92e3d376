"use strict";
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FIMCompletionStreamRequest$ = exports.FIMCompletionStreamRequest$outboundSchema = exports.FIMCompletionStreamRequest$inboundSchema = exports.FIMCompletionStreamRequestStop$ = exports.FIMCompletionStreamRequestStop$outboundSchema = exports.FIMCompletionStreamRequestStop$inboundSchema = void 0;
exports.fimCompletionStreamRequestStopToJSON = fimCompletionStreamRequestStopToJSON;
exports.fimCompletionStreamRequestStopFromJSON = fimCompletionStreamRequestStopFromJSON;
exports.fimCompletionStreamRequestToJSON = fimCompletionStreamRequestToJSON;
exports.fimCompletionStreamRequestFromJSON = fimCompletionStreamRequestFromJSON;
const z = __importStar(require("zod"));
const primitives_js_1 = require("../../lib/primitives.js");
const schemas_js_1 = require("../../lib/schemas.js");
/** @internal */
exports.FIMCompletionStreamRequestStop$inboundSchema = z.union([z.string(), z.array(z.string())]);
/** @internal */
exports.FIMCompletionStreamRequestStop$outboundSchema = z.union([z.string(), z.array(z.string())]);
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var FIMCompletionStreamRequestStop$;
(function (FIMCompletionStreamRequestStop$) {
    /** @deprecated use `FIMCompletionStreamRequestStop$inboundSchema` instead. */
    FIMCompletionStreamRequestStop$.inboundSchema = exports.FIMCompletionStreamRequestStop$inboundSchema;
    /** @deprecated use `FIMCompletionStreamRequestStop$outboundSchema` instead. */
    FIMCompletionStreamRequestStop$.outboundSchema = exports.FIMCompletionStreamRequestStop$outboundSchema;
})(FIMCompletionStreamRequestStop$ || (exports.FIMCompletionStreamRequestStop$ = FIMCompletionStreamRequestStop$ = {}));
function fimCompletionStreamRequestStopToJSON(fimCompletionStreamRequestStop) {
    return JSON.stringify(exports.FIMCompletionStreamRequestStop$outboundSchema.parse(fimCompletionStreamRequestStop));
}
function fimCompletionStreamRequestStopFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.FIMCompletionStreamRequestStop$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'FIMCompletionStreamRequestStop' from JSON`);
}
/** @internal */
exports.FIMCompletionStreamRequest$inboundSchema = z.object({
    model: z.string(),
    temperature: z.nullable(z.number()).optional(),
    top_p: z.number().default(1),
    max_tokens: z.nullable(z.number().int()).optional(),
    stream: z.boolean().default(true),
    stop: z.union([z.string(), z.array(z.string())]).optional(),
    random_seed: z.nullable(z.number().int()).optional(),
    prompt: z.string(),
    suffix: z.nullable(z.string()).optional(),
    min_tokens: z.nullable(z.number().int()).optional(),
}).transform((v) => {
    return (0, primitives_js_1.remap)(v, {
        "top_p": "topP",
        "max_tokens": "maxTokens",
        "random_seed": "randomSeed",
        "min_tokens": "minTokens",
    });
});
/** @internal */
exports.FIMCompletionStreamRequest$outboundSchema = z.object({
    model: z.string(),
    temperature: z.nullable(z.number()).optional(),
    topP: z.number().default(1),
    maxTokens: z.nullable(z.number().int()).optional(),
    stream: z.boolean().default(true),
    stop: z.union([z.string(), z.array(z.string())]).optional(),
    randomSeed: z.nullable(z.number().int()).optional(),
    prompt: z.string(),
    suffix: z.nullable(z.string()).optional(),
    minTokens: z.nullable(z.number().int()).optional(),
}).transform((v) => {
    return (0, primitives_js_1.remap)(v, {
        topP: "top_p",
        maxTokens: "max_tokens",
        randomSeed: "random_seed",
        minTokens: "min_tokens",
    });
});
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var FIMCompletionStreamRequest$;
(function (FIMCompletionStreamRequest$) {
    /** @deprecated use `FIMCompletionStreamRequest$inboundSchema` instead. */
    FIMCompletionStreamRequest$.inboundSchema = exports.FIMCompletionStreamRequest$inboundSchema;
    /** @deprecated use `FIMCompletionStreamRequest$outboundSchema` instead. */
    FIMCompletionStreamRequest$.outboundSchema = exports.FIMCompletionStreamRequest$outboundSchema;
})(FIMCompletionStreamRequest$ || (exports.FIMCompletionStreamRequest$ = FIMCompletionStreamRequest$ = {}));
function fimCompletionStreamRequestToJSON(fimCompletionStreamRequest) {
    return JSON.stringify(exports.FIMCompletionStreamRequest$outboundSchema.parse(fimCompletionStreamRequest));
}
function fimCompletionStreamRequestFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.FIMCompletionStreamRequest$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'FIMCompletionStreamRequest' from JSON`);
}
//# sourceMappingURL=fimcompletionstreamrequest.js.map