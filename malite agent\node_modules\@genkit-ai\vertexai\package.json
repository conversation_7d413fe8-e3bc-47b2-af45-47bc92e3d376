{"name": "@genkit-ai/vertexai", "description": "Genkit AI framework plugin for Google Cloud Vertex AI APIs including Gemini APIs, Imagen, and more.", "keywords": ["genkit", "genkit-plugin", "genkit-embedder", "genkit-model", "google cloud", "vertex ai", "imagen", "image-generation", "gemini", "google gemini", "google ai", "ai", "genai", "generative-ai"], "version": "1.10.0", "type": "commonjs", "repository": {"type": "git", "url": "https://github.com/firebase/genkit.git", "directory": "js/plugins/vertexai"}, "author": "genkit", "license": "Apache-2.0", "dependencies": {"@anthropic-ai/sdk": "^0.24.3", "@anthropic-ai/vertex-sdk": "^0.4.0", "@google-cloud/aiplatform": "^3.23.0", "@mistralai/mistralai-gcp": "^1.3.5", "@google-cloud/vertexai": "^1.9.3", "google-auth-library": "^9.14.2", "googleapis": "^140.0.1", "node-fetch": "^3.3.2", "openai": "^4.52.7"}, "peerDependencies": {"genkit": "^1.10.0"}, "optionalDependencies": {"@google-cloud/bigquery": "^7.8.0", "firebase-admin": ">=12.2"}, "devDependencies": {"@types/node": "^20.11.16", "google-gax": "^4.4.1", "npm-run-all": "^4.1.5", "rimraf": "^6.0.1", "tsup": "^8.3.5", "tsx": "^4.19.2", "typescript": "^4.9.0"}, "types": "./lib/index.d.ts", "exports": {".": {"require": "./lib/index.js", "import": "./lib/index.mjs", "types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./rerankers": {"require": "./lib/rerankers/index.js", "import": "./lib/rerankers/index.mjs", "types": "./lib/rerankers/index.d.ts", "default": "./lib/rerankers/index.js"}, "./evaluation": {"require": "./lib/evaluation/index.js", "import": "./lib/evaluation/index.mjs", "types": "./lib/evaluation/index.d.ts", "default": "./lib/evaluation/index.js"}, "./modelgarden": {"require": "./lib/modelgarden/index.js", "import": "./lib/modelgarden/index.mjs", "types": "./lib/modelgarden/index.d.ts", "default": "./lib/modelgarden/index.js"}, "./vectorsearch": {"require": "./lib/vectorsearch/index.js", "import": "./lib/vectorsearch/index.mjs", "types": "./lib/vectorsearch/index.d.ts", "default": "./lib/vectorsearch/index.js"}}, "typesVersions": {"*": {"rerankers": ["./lib/rerankers/index"], "evaluation": ["./lib/evaluation/index"], "modelgarden": ["./lib/modelgarden/index"], "vectorsearch": ["./lib/vectorsearch/index"]}}, "scripts": {"check": "tsc", "compile": "tsup-node", "build:clean": "rimraf ./lib", "build": "npm-run-all build:clean check compile", "build:watch": "tsup-node --watch", "test": "tsx --test ./tests/*_test.ts ./tests/**/*_test.ts"}}