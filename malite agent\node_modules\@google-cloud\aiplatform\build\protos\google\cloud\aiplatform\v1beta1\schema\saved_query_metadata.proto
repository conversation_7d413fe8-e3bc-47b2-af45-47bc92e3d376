// Copyright 2020 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.aiplatform.v1beta1.schema;

import "google/cloud/aiplatform/v1beta1/schema/annotation_spec_color.proto";
import "google/api/annotations.proto";

option go_package = "google.golang.org/genproto/googleapis/cloud/aiplatform/v1beta1/schema;schema";
option java_multiple_files = true;
option java_outer_classname = "SavedQueryMetadataProto";
option java_package = "com.google.cloud.aiplatform.v1beta1.schema";

// The metadata of SavedQuery contains TextSentiment Annotations.
message TextSentimentSavedQueryMetadata {
  // The maximum sentiment of sentiment Anntoation in this SavedQuery.
  int32 sentiment_max = 1;
}

message VisualInspectionClassificationLabelSavedQueryMetadata {
  // Whether or not the classification label is multi_label.
  bool multi_label = 1;
}

message VisualInspectionMaskSavedQueryMetadata {
  // The mapping between color and AnnotationSpec for this SavedQuery.
  repeated AnnotationSpecColor color_map = 2;
}
