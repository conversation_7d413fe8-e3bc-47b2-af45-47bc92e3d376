"use strict";
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ImageURLChunk$ = exports.ImageURLChunk$outboundSchema = exports.ImageURLChunk$inboundSchema = exports.ImageURLChunkType$ = exports.ImageURLChunkType$outboundSchema = exports.ImageURLChunkType$inboundSchema = exports.ImageURLChunkImageURL$ = exports.ImageURLChunkImageURL$outboundSchema = exports.ImageURLChunkImageURL$inboundSchema = exports.ImageURLChunkType = void 0;
exports.imageURLChunkImageURLToJSON = imageURLChunkImageURLToJSON;
exports.imageURLChunkImageURLFromJSON = imageURLChunkImageURLFromJSON;
exports.imageURLChunkToJSON = imageURLChunkToJSON;
exports.imageURLChunkFromJSON = imageURLChunkFromJSON;
const z = __importStar(require("zod"));
const primitives_js_1 = require("../../lib/primitives.js");
const schemas_js_1 = require("../../lib/schemas.js");
const imageurl_js_1 = require("./imageurl.js");
exports.ImageURLChunkType = {
    ImageUrl: "image_url",
};
/** @internal */
exports.ImageURLChunkImageURL$inboundSchema = z.union([imageurl_js_1.ImageURL$inboundSchema, z.string()]);
/** @internal */
exports.ImageURLChunkImageURL$outboundSchema = z.union([imageurl_js_1.ImageURL$outboundSchema, z.string()]);
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var ImageURLChunkImageURL$;
(function (ImageURLChunkImageURL$) {
    /** @deprecated use `ImageURLChunkImageURL$inboundSchema` instead. */
    ImageURLChunkImageURL$.inboundSchema = exports.ImageURLChunkImageURL$inboundSchema;
    /** @deprecated use `ImageURLChunkImageURL$outboundSchema` instead. */
    ImageURLChunkImageURL$.outboundSchema = exports.ImageURLChunkImageURL$outboundSchema;
})(ImageURLChunkImageURL$ || (exports.ImageURLChunkImageURL$ = ImageURLChunkImageURL$ = {}));
function imageURLChunkImageURLToJSON(imageURLChunkImageURL) {
    return JSON.stringify(exports.ImageURLChunkImageURL$outboundSchema.parse(imageURLChunkImageURL));
}
function imageURLChunkImageURLFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.ImageURLChunkImageURL$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'ImageURLChunkImageURL' from JSON`);
}
/** @internal */
exports.ImageURLChunkType$inboundSchema = z.nativeEnum(exports.ImageURLChunkType);
/** @internal */
exports.ImageURLChunkType$outboundSchema = exports.ImageURLChunkType$inboundSchema;
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var ImageURLChunkType$;
(function (ImageURLChunkType$) {
    /** @deprecated use `ImageURLChunkType$inboundSchema` instead. */
    ImageURLChunkType$.inboundSchema = exports.ImageURLChunkType$inboundSchema;
    /** @deprecated use `ImageURLChunkType$outboundSchema` instead. */
    ImageURLChunkType$.outboundSchema = exports.ImageURLChunkType$outboundSchema;
})(ImageURLChunkType$ || (exports.ImageURLChunkType$ = ImageURLChunkType$ = {}));
/** @internal */
exports.ImageURLChunk$inboundSchema = z.object({
    image_url: z.union([imageurl_js_1.ImageURL$inboundSchema, z.string()]),
    type: exports.ImageURLChunkType$inboundSchema.default("image_url"),
}).transform((v) => {
    return (0, primitives_js_1.remap)(v, {
        "image_url": "imageUrl",
    });
});
/** @internal */
exports.ImageURLChunk$outboundSchema = z.object({
    imageUrl: z.union([imageurl_js_1.ImageURL$outboundSchema, z.string()]),
    type: exports.ImageURLChunkType$outboundSchema.default("image_url"),
}).transform((v) => {
    return (0, primitives_js_1.remap)(v, {
        imageUrl: "image_url",
    });
});
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var ImageURLChunk$;
(function (ImageURLChunk$) {
    /** @deprecated use `ImageURLChunk$inboundSchema` instead. */
    ImageURLChunk$.inboundSchema = exports.ImageURLChunk$inboundSchema;
    /** @deprecated use `ImageURLChunk$outboundSchema` instead. */
    ImageURLChunk$.outboundSchema = exports.ImageURLChunk$outboundSchema;
})(ImageURLChunk$ || (exports.ImageURLChunk$ = ImageURLChunk$ = {}));
function imageURLChunkToJSON(imageURLChunk) {
    return JSON.stringify(exports.ImageURLChunk$outboundSchema.parse(imageURLChunk));
}
function imageURLChunkFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.ImageURLChunk$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'ImageURLChunk' from JSON`);
}
//# sourceMappingURL=imageurlchunk.js.map