{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../../../src/detectors/platform/browser/index.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,+CAA8C;AAArC,4GAAA,YAAY,OAAA;AACrB,uDAAsD;AAA7C,oHAAA,gBAAgB,OAAA;AACzB,2CAA0C;AAAjC,wGAAA,UAAU,OAAA;AACnB,mDAAkD;AAAzC,gHAAA,cAAc,OAAA;AACvB,qDAAoD;AAA3C,kHAAA,eAAe,OAAA;AACxB,6DAA4D;AAAnD,0HAAA,mBAAmB,OAAA;AAC5B,iFAAgF;AAAvE,8IAAA,6BAA6B,OAAA", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport { hostDetector } from './HostDetector';\nexport { hostDetectorSync } from './HostDetectorSync';\nexport { osDetector } from './OSDetector';\nexport { osDetectorSync } from './OSDetectorSync';\nexport { processDetector } from './ProcessDetector';\nexport { processDetectorSync } from './ProcessDetectorSync';\nexport { serviceInstanceIdDetectorSync } from './ServiceInstanceIdDetectorSync';\n"]}