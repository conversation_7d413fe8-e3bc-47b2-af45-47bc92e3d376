before_action {
  fetch_keystore {
    keystore_resource {
      keystore_config_id: 73713
      keyname: "docuploader_service_account"
    }
  }
}

before_action {
  fetch_keystore {
    keystore_resource {
      keystore_config_id: 73713
      keyname: "google-cloud-npm-token-1"
    }
  }
}

env_vars: {
  key: "SECRET_MANAGER_KEYS"
  value: "releasetool-publish-reporter-app,releasetool-publish-reporter-googleapis-installation,releasetool-publish-reporter-pem"
}

# Download trampoline resources.
gfile_resources: "/bigstore/cloud-devrel-kokoro-resources/trampoline"

# Use the trampoline script to run in docker.
build_file: "nodejs-vertexai/.kokoro/trampoline_v2.sh"

# Configure the docker image for kokoro-trampoline.
env_vars: {
    key: "TRAMPOLINE_IMAGE"
    value: "us-central1-docker.pkg.dev/cloud-sdk-release-custom-pool/release-images/node18"
}

env_vars: {
    key: "TRAMPOLINE_BUILD_FILE"
    value: "github/nodejs-vertexai/.kokoro/publish.sh"
}

# Store the packages we uploaded to npmjs.org and their corresponding
# package-lock.jsons in Placer.  That way, we have a record of exactly
# what we published, and which version of which tools we used to publish
# it, which we can use to generate SBOMs and attestations.
action {
  define_artifacts {
    regex: "github/**/*.tgz"
    regex: "github/**/package-lock.json"
    strip_prefix: "github"
  }
}