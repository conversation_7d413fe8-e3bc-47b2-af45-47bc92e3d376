{"version": 3, "file": "RegistrationConflicts.js", "sourceRoot": "", "sources": ["../../../src/view/RegistrationConflicts.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAKH,MAAM,UAAU,yBAAyB,CACvC,QAA8B,EAC9B,eAAqC;IAErC,IAAI,eAAe,GAAG,EAAE,CAAC;IACzB,IAAI,QAAQ,CAAC,IAAI,KAAK,eAAe,CAAC,IAAI,EAAE;QAC1C,eAAe,IAAI,aAAa,QAAQ,CAAC,IAAI,qBAAqB,eAAe,CAAC,IAAI,KAAK,CAAC;KAC7F;IACD,IAAI,QAAQ,CAAC,IAAI,KAAK,eAAe,CAAC,IAAI,EAAE;QAC1C,eAAe,IAAI,aAAa,QAAQ,CAAC,IAAI,qBAAqB,eAAe,CAAC,IAAI,KAAK,CAAC;KAC7F;IACD,IAAI,QAAQ,CAAC,SAAS,KAAK,eAAe,CAAC,SAAS,EAAE;QACpD,eAAe,IAAI,mBAAmB,QAAQ,CAAC,SAAS,qBAAqB,eAAe,CAAC,SAAS,KAAK,CAAC;KAC7G;IACD,IAAI,QAAQ,CAAC,WAAW,KAAK,eAAe,CAAC,WAAW,EAAE;QACxD,eAAe,IAAI,oBAAoB,QAAQ,CAAC,WAAW,qBAAqB,eAAe,CAAC,WAAW,KAAK,CAAC;KAClH;IAED,OAAO,eAAe,CAAC;AACzB,CAAC;AAED,MAAM,UAAU,oCAAoC,CAClD,QAA8B,EAC9B,eAAqC;IAErC,OAAO,sBAAsB,QAAQ,CAAC,SAAS,kEAAkE,eAAe,CAAC,IAAI,GAAG,CAAC;AAC3I,CAAC;AAED,MAAM,UAAU,+BAA+B,CAC7C,QAA8B,EAC9B,eAAqC;IAErC,OAAO,iBAAiB,QAAQ,CAAC,IAAI,kEAAkE,eAAe,CAAC,IAAI,GAAG,CAAC;AACjI,CAAC;AAED,MAAM,UAAU,+BAA+B,CAC7C,QAA8B,EAC9B,eAAqC;IAErC,MAAM,QAAQ,GAA+B;QAC3C,IAAI,EAAE,eAAe,CAAC,IAAI;QAC1B,IAAI,EAAE,eAAe,CAAC,IAAI;QAC1B,IAAI,EAAE,eAAe,CAAC,IAAI;KAC3B,CAAC;IAEF,MAAM,cAAc,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;IAEhD,OAAO,iDAAiD,QAAQ,CAAC,IAAI,6BAA6B,cAAc,GAAG,CAAC;AACtH,CAAC;AAED,MAAM,UAAU,8BAA8B,CAC5C,QAA8B,EAC9B,eAAqC;IAErC,MAAM,QAAQ,GAA+B;QAC3C,IAAI,EAAE,eAAe,CAAC,IAAI;QAC1B,IAAI,EAAE,eAAe,CAAC,IAAI;QAC1B,IAAI,EAAE,eAAe,CAAC,IAAI;KAC3B,CAAC;IAEF,MAAM,cAAc,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;IAEhD,OAAO,iDAAiD,QAAQ,CAAC,IAAI,6BAA6B,cAAc;+CACnE,QAAQ,CAAC,IAAI,qBAAqB,QAAQ,CAAC,WAAW,4BAA4B,cAAc;+CAChG,eAAe,CAAC,IAAI,qBAAqB,QAAQ,CAAC,WAAW,4BAA4B,cAAc,EAAE,CAAC;AACzJ,CAAC;AAED,MAAM,UAAU,2BAA2B,CACzC,QAA8B,EAC9B,eAAqC;IAErC,6CAA6C;IAC7C,IAAI,QAAQ,CAAC,SAAS,KAAK,eAAe,CAAC,SAAS,EAAE;QACpD,OAAO,oCAAoC,CAAC,QAAQ,EAAE,eAAe,CAAC,CAAC;KACxE;IAED,IAAI,QAAQ,CAAC,IAAI,KAAK,eAAe,CAAC,IAAI,EAAE;QAC1C,OAAO,+BAA+B,CAAC,QAAQ,EAAE,eAAe,CAAC,CAAC;KACnE;IAED,0CAA0C;IAC1C,IAAI,QAAQ,CAAC,IAAI,KAAK,eAAe,CAAC,IAAI,EAAE;QAC1C,gEAAgE;QAChE,OAAO,+BAA+B,CAAC,QAAQ,EAAE,eAAe,CAAC,CAAC;KACnE;IAED,IAAI,QAAQ,CAAC,WAAW,KAAK,eAAe,CAAC,WAAW,EAAE;QACxD,OAAO,8BAA8B,CAAC,QAAQ,EAAE,eAAe,CAAC,CAAC;KAClE;IAED,OAAO,EAAE,CAAC;AACZ,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { InstrumentSelectorCriteria } from './InstrumentSelector';\nimport { InstrumentDescriptor } from '../InstrumentDescriptor';\n\nexport function getIncompatibilityDetails(\n  existing: InstrumentDescriptor,\n  otherDescriptor: InstrumentDescriptor\n) {\n  let incompatibility = '';\n  if (existing.unit !== otherDescriptor.unit) {\n    incompatibility += `\\t- Unit '${existing.unit}' does not match '${otherDescriptor.unit}'\\n`;\n  }\n  if (existing.type !== otherDescriptor.type) {\n    incompatibility += `\\t- Type '${existing.type}' does not match '${otherDescriptor.type}'\\n`;\n  }\n  if (existing.valueType !== otherDescriptor.valueType) {\n    incompatibility += `\\t- Value Type '${existing.valueType}' does not match '${otherDescriptor.valueType}'\\n`;\n  }\n  if (existing.description !== otherDescriptor.description) {\n    incompatibility += `\\t- Description '${existing.description}' does not match '${otherDescriptor.description}'\\n`;\n  }\n\n  return incompatibility;\n}\n\nexport function getValueTypeConflictResolutionRecipe(\n  existing: InstrumentDescriptor,\n  otherDescriptor: InstrumentDescriptor\n) {\n  return `\\t- use valueType '${existing.valueType}' on instrument creation or use an instrument name other than '${otherDescriptor.name}'`;\n}\n\nexport function getUnitConflictResolutionRecipe(\n  existing: InstrumentDescriptor,\n  otherDescriptor: InstrumentDescriptor\n) {\n  return `\\t- use unit '${existing.unit}' on instrument creation or use an instrument name other than '${otherDescriptor.name}'`;\n}\n\nexport function getTypeConflictResolutionRecipe(\n  existing: InstrumentDescriptor,\n  otherDescriptor: InstrumentDescriptor\n) {\n  const selector: InstrumentSelectorCriteria = {\n    name: otherDescriptor.name,\n    type: otherDescriptor.type,\n    unit: otherDescriptor.unit,\n  };\n\n  const selectorString = JSON.stringify(selector);\n\n  return `\\t- create a new view with a name other than '${existing.name}' and InstrumentSelector '${selectorString}'`;\n}\n\nexport function getDescriptionResolutionRecipe(\n  existing: InstrumentDescriptor,\n  otherDescriptor: InstrumentDescriptor\n): string {\n  const selector: InstrumentSelectorCriteria = {\n    name: otherDescriptor.name,\n    type: otherDescriptor.type,\n    unit: otherDescriptor.unit,\n  };\n\n  const selectorString = JSON.stringify(selector);\n\n  return `\\t- create a new view with a name other than '${existing.name}' and InstrumentSelector '${selectorString}'\n    \\t- OR - create a new view with the name ${existing.name} and description '${existing.description}' and InstrumentSelector ${selectorString}\n    \\t- OR - create a new view with the name ${otherDescriptor.name} and description '${existing.description}' and InstrumentSelector ${selectorString}`;\n}\n\nexport function getConflictResolutionRecipe(\n  existing: InstrumentDescriptor,\n  otherDescriptor: InstrumentDescriptor\n): string {\n  // Conflicts that cannot be solved via views.\n  if (existing.valueType !== otherDescriptor.valueType) {\n    return getValueTypeConflictResolutionRecipe(existing, otherDescriptor);\n  }\n\n  if (existing.unit !== otherDescriptor.unit) {\n    return getUnitConflictResolutionRecipe(existing, otherDescriptor);\n  }\n\n  // Conflicts that can be solved via views.\n  if (existing.type !== otherDescriptor.type) {\n    // this will automatically solve possible description conflicts.\n    return getTypeConflictResolutionRecipe(existing, otherDescriptor);\n  }\n\n  if (existing.description !== otherDescriptor.description) {\n    return getDescriptionResolutionRecipe(existing, otherDescriptor);\n  }\n\n  return '';\n}\n"]}