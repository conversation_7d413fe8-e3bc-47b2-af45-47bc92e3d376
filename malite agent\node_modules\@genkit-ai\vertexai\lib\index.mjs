/**
 * @license
 *
 * Copyright 2024 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
import {
  embedderRef,
  modelActionMetadata,
  modelRef
} from "genkit";
import { genkitPlugin } from "genkit/plugin";
import { getDerivedParams } from "./common/index.js";
import {
  defineVertexAIEmbedder,
  multimodalEmbedding001,
  SUPPORTED_EMBEDDER_MODELS,
  textEmbedding004,
  textEmbedding005,
  textEmbeddingGecko003,
  textEmbeddingGeckoMultilingual001,
  textMultilingualEmbedding002,
  VertexEmbeddingConfigSchema
} from "./embedder.js";
import {
  defineGeminiKnownModel,
  defineGeminiModel,
  gemini,
  gemini10Pro,
  gemini15Flash,
  gemini15Pro,
  gemini20Flash,
  gemini20Flash001,
  gemini20FlashLite,
  gemini20FlashLitePreview0205,
  gemini20ProExp0205,
  gemini25FlashPreview0417,
  gemini25ProExp0325,
  gemini25ProPreview0325,
  GeminiConfigSchema,
  SafetySettingsSchema,
  SUPPORTED_GEMINI_MODELS
} from "./gemini.js";
import {
  defineImagenModel,
  GENERIC_IMAGEN_INFO,
  imagen2,
  imagen3,
  imagen3Fast,
  ImagenConfigSchema,
  SUPPORTED_IMAGEN_MODELS
} from "./imagen.js";
import { listModels } from "./list-models.js";
async function initializer(ai, options) {
  const { projectId, location, vertexClientFactory, authClient } = await getDerivedParams(options);
  Object.keys(SUPPORTED_IMAGEN_MODELS).map(
    (name) => defineImagenModel(ai, name, authClient, { projectId, location })
  );
  Object.keys(SUPPORTED_GEMINI_MODELS).map(
    (name) => defineGeminiKnownModel(
      ai,
      name,
      vertexClientFactory,
      {
        projectId,
        location
      },
      options?.experimental_debugTraces
    )
  );
  if (options?.models) {
    for (const modelOrRef of options?.models) {
      const modelName = typeof modelOrRef === "string" ? modelOrRef : (
        // strip out the `vertexai/` prefix
        modelOrRef.name.split("/")[1]
      );
      const modelRef2 = typeof modelOrRef === "string" ? gemini(modelOrRef) : modelOrRef;
      defineGeminiModel({
        ai,
        modelName: modelRef2.name,
        version: modelName,
        modelInfo: modelRef2.info,
        vertexClientFactory,
        options: {
          projectId,
          location
        },
        debugTraces: options.experimental_debugTraces
      });
    }
  }
  Object.keys(SUPPORTED_EMBEDDER_MODELS).map(
    (name) => defineVertexAIEmbedder(ai, name, authClient, { projectId, location })
  );
}
async function resolver(ai, actionType, actionName, options) {
  switch (actionType) {
    case "model":
      await resolveModel(ai, actionName, options);
      break;
    case "embedder":
      await resolveEmbedder(ai, actionName, options);
      break;
    default:
  }
}
async function resolveModel(ai, actionName, options) {
  const { projectId, location, vertexClientFactory, authClient } = await getDerivedParams(options);
  if (actionName.startsWith("imagen")) {
    defineImagenModel(ai, actionName, authClient, { projectId, location });
    return;
  }
  const modelRef2 = gemini(actionName);
  defineGeminiModel({
    ai,
    modelName: modelRef2.name,
    version: actionName,
    modelInfo: modelRef2.info,
    vertexClientFactory,
    options: {
      projectId,
      location
    },
    debugTraces: options?.experimental_debugTraces
  });
}
async function resolveEmbedder(ai, actionName, options) {
  const { projectId, location, authClient } = await getDerivedParams(options);
  defineVertexAIEmbedder(ai, actionName, authClient, { projectId, location });
}
const KNOWN_DECOMISSIONED_MODELS = [
  "gemini-pro-vision",
  "gemini-pro",
  "gemini-ultra",
  "gemini-ultra-vision"
];
async function listActions(options) {
  const { location, projectId, authClient } = await getDerivedParams(options);
  const models = await listModels(authClient, location, projectId);
  return [
    // Gemini
    ...models.filter(
      (m) => m.name.includes("gemini") && !KNOWN_DECOMISSIONED_MODELS.includes(m.name.split("/").at(-1))
    ).map((m) => {
      const ref = gemini(m.name.split("/").at(-1));
      return modelActionMetadata({
        name: ref.name,
        info: ref.info,
        configSchema: GeminiConfigSchema
      });
    }),
    // Imagen
    ...models.filter((m) => m.name.includes("imagen")).map((m) => {
      const name = m.name.split("/").at(-1);
      return modelActionMetadata({
        name: "vertexai/" + name,
        info: {
          ...GENERIC_IMAGEN_INFO,
          label: `Vertex AI - ${name}`
        },
        configSchema: ImagenConfigSchema
      });
    })
  ];
}
function vertexAIPlugin(options) {
  let listActionsCache;
  return genkitPlugin(
    "vertexai",
    async (ai) => await initializer(ai, options),
    async (ai, actionType, actionName) => await resolver(ai, actionType, actionName, options),
    async () => {
      if (listActionsCache) return listActionsCache;
      listActionsCache = await listActions(options);
      return listActionsCache;
    }
  );
}
const vertexAI = vertexAIPlugin;
vertexAI.model = (name, config) => {
  if (name.startsWith("imagen")) {
    return modelRef({
      name: `vertexai/${name}`,
      config,
      configSchema: ImagenConfigSchema
    });
  }
  return modelRef({
    name: `vertexai/${name}`,
    config,
    configSchema: GeminiConfigSchema
  });
};
vertexAI.embedder = (name, config) => {
  return embedderRef({
    name: `vertexai/${name}`,
    config,
    configSchema: VertexEmbeddingConfigSchema
  });
};
var src_default = vertexAI;
export {
  GeminiConfigSchema,
  ImagenConfigSchema,
  SafetySettingsSchema,
  src_default as default,
  gemini,
  gemini10Pro,
  gemini15Flash,
  gemini15Pro,
  gemini20Flash,
  gemini20Flash001,
  gemini20FlashLite,
  gemini20FlashLitePreview0205,
  gemini20ProExp0205,
  gemini25FlashPreview0417,
  gemini25ProExp0325,
  gemini25ProPreview0325,
  imagen2,
  imagen3,
  imagen3Fast,
  multimodalEmbedding001,
  textEmbedding004,
  textEmbedding005,
  textEmbeddingGecko003,
  textEmbeddingGeckoMultilingual001,
  textMultilingualEmbedding002,
  vertexAI
};
//# sourceMappingURL=index.mjs.map