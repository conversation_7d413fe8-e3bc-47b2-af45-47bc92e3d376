// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.aiplatform.v1beta1.schema.predict.instance;


option csharp_namespace = "Google.Cloud.AIPlatform.V1Beta1.Schema.Predict.Instance";
option go_package = "cloud.google.com/go/aiplatform/apiv1beta1/schema/predict/instance/instancepb;instancepb";
option java_multiple_files = true;
option java_outer_classname = "VideoObjectTrackingPredictionInstanceProto";
option java_package = "com.google.cloud.aiplatform.v1beta1.schema.predict.instance";
option php_namespace = "Google\\Cloud\\AIPlatform\\V1beta1\\Schema\\Predict\\Instance";
option ruby_package = "Google::Cloud::AIPlatform::V1beta1::Schema::Predict::Instance";

// Prediction input format for Video Object Tracking.
message VideoObjectTrackingPredictionInstance {
  // The Google Cloud Storage location of the video on which to perform the
  // prediction.
  string content = 1;

  // The MIME type of the content of the video. Only the following are
  // supported: video/mp4 video/avi video/quicktime
  string mime_type = 2;

  // The beginning, inclusive, of the video's time segment on which to perform
  // the prediction. Expressed as a number of seconds as measured from the
  // start of the video, with "s" appended at the end. Fractions are allowed,
  // up to a microsecond precision.
  string time_segment_start = 3;

  // The end, exclusive, of the video's time segment on which to perform
  // the prediction. Expressed as a number of seconds as measured from the
  // start of the video, with "s" appended at the end. Fractions are allowed,
  // up to a microsecond precision, and "inf" or "Infinity" is allowed, which
  // means the end of the video.
  string time_segment_end = 4;
}
