{"version": 3, "file": "sdks.js", "sourceRoot": "", "sources": ["../src/lib/sdks.ts"], "names": [], "mappings": ";AAAA;;GAEG;;;;;;;;;;;;;;;AAEH,gDAA6C;AAE7C,8EAM8C;AAC9C,0CAAiD;AACjD,2CAA6C;AAC7C,2CAA6E;AAC7E,iDAA4C;AAC5C,uCAOmB;AAEnB,6CAAkD;AAyClD,MAAM,EAAE,GAAY,OAAO,UAAU,KAAK,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,UAAU,CAAC;AAC1E,MAAM,aAAa,GAAG,OAAO,EAAE,KAAK,QAAQ;OACvC,EAAE,IAAI,IAAI;OACV,eAAe,IAAI,EAAE;OACrB,OAAO,EAAE,CAAC,eAAe,CAAC,KAAK,UAAU,CAAC;AAC/C,MAAM,aAAa,GAAG,aAAa;OAC9B,CAAC,OAAO,SAAS,KAAK,WAAW,IAAI,eAAe,IAAI,SAAS,CAAC;OAClE,CAAC,OAAO,MAAM,KAAK,QAAQ,IAAI,OAAO,MAAM,CAAC,QAAQ,KAAK,WAAW,CAAC,CAAC;AAE5E,MAAa,SAAS;IAOpB,YAAY,UAAsB,EAAE;QAN3B,wCAAwB;QACxB,mCAAiB;QACjB,oCAA6B;QAKpC,MAAM,GAAG,GAAG,OAAkB,CAAC;QAC/B,IACE,OAAO,GAAG,KAAK,QAAQ;eACpB,GAAG,IAAI,IAAI;eACX,OAAO,IAAI,GAAG;eACd,GAAG,CAAC,KAAK,YAAY,mBAAQ,EAChC,CAAC;YACD,uBAAA,IAAI,oBAAU,GAAG,CAAC,KAAK,MAAA,CAAC;QAC1B,CAAC;aAAM,CAAC;YACN,uBAAA,IAAI,oBAAU,IAAI,mBAAQ,EAAE,MAAA,CAAC;QAC/B,CAAC;QACD,IAAI,CAAC,QAAQ,GAAG,EAAE,GAAG,OAAO,EAAE,KAAK,EAAE,uBAAA,IAAI,wBAAO,EAAE,CAAC;QAEnD,MAAM,GAAG,GAAG,IAAA,gCAAoB,EAAC,OAAO,CAAC,CAAC;QAC1C,IAAI,GAAG,EAAE,CAAC;YACR,GAAG,CAAC,QAAQ,GAAG,GAAG,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC;QACxD,CAAC;QACD,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,GAAG,uBAAA,IAAI,wBAAO,CAAC,OAAO,CAAC;YAC9C,OAAO,EAAE,GAAG;YACZ,MAAM,EAAE,OAAO,CAAC,UAAU,IAAI,IAAI,oBAAU,EAAE;SAC/C,CAAC,CAAC;QACH,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;QACxB,uBAAA,IAAI,yBAAe,MAAM,MAAA,CAAC;QAC1B,uBAAA,IAAI,qBAAW,OAAO,CAAC,WAAW,MAAA,CAAC;IACrC,CAAC;IAEM,cAAc,CACnB,OAAoB,EACpB,IAAmB,EACnB,OAAwB;QAExB,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,IAAI,CAAC;QAEnE,MAAM,IAAI,GAAG,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,QAAQ,CAAC;QAC3C,IAAI,CAAC,IAAI,EAAE,CAAC;YACV,OAAO,IAAA,WAAG,EAAC,IAAI,yCAAmB,CAAC,oCAAoC,CAAC,CAAC,CAAC;QAC5E,CAAC;QACD,MAAM,MAAM,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,CAAC;QAC7B,MAAM,QAAQ,GAAG,IAAI,GAAG,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;QAEvC,IAAI,IAAI,EAAE,CAAC;YACT,MAAM,CAAC,QAAQ,IAAI,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC;YAC5D,MAAM,CAAC,QAAQ,IAAI,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;QAC3D,CAAC;QAED,IAAI,UAAU,GAAG,KAAK,IAAI,EAAE,CAAC;QAE7B,MAAM,QAAQ,GAAa,EAAE,CAAC;QAC9B,KAAK,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,QAAQ,EAAE,WAAW,IAAI,EAAE,CAAC,EAAE,CAAC;YACjE,MAAM,CAAC,GAAG,IAAA,yBAAU,EAAC,CAAC,EAAE,CAAC,EAAE,EAAE,YAAY,EAAE,SAAS,EAAE,CAAC,CAAC;YACxD,IAAI,OAAO,CAAC,KAAK,WAAW,EAAE,CAAC;gBAC7B,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACnB,CAAC;QACH,CAAC;QACD,IAAI,QAAQ,CAAC,MAAM,EAAE,CAAC;YACpB,UAAU,IAAI,IAAI,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC;QACzC,CAAC;QAED,IAAI,UAAU,EAAE,CAAC;YACf,MAAM,CAAC,GAAG,UAAU,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC;YACxE,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC,EAAE,CAAC;QAC1B,CAAC;QAED,MAAM,OAAO,GAAG,IAAI,OAAO,CAAC,SAAS,CAAC,CAAC;QAEvC,MAAM,QAAQ,GAAG,QAAQ,EAAE,KAAK,CAAC,QAAQ,CAAC;QAC1C,MAAM,QAAQ,GAAG,QAAQ,EAAE,KAAK,CAAC,QAAQ,CAAC;QAC1C,IAAI,QAAQ,IAAI,IAAI,IAAI,QAAQ,IAAI,IAAI,EAAE,CAAC;YACzC,MAAM,OAAO,GAAG,IAAA,0BAAc,EAC5B,CAAC,QAAQ,IAAI,EAAE,EAAE,QAAQ,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAC3C,CAAC;YACF,OAAO,CAAC,GAAG,CAAC,eAAe,EAAE,SAAS,OAAO,EAAE,CAAC,CAAC;QACnD,CAAC;QAED,MAAM,eAAe,GAAG,IAAI,OAAO,CAAC,QAAQ,EAAE,OAAO,IAAI,EAAE,CAAC,CAAC;QAC7D,KAAK,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,eAAe,EAAE,CAAC;YACrC,OAAO,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACpB,CAAC;QAED,IAAI,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;QACzC,KAAK,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,QAAQ,EAAE,OAAO,IAAI,EAAE,CAAC,EAAE,CAAC;YAC7D,MAAM,IAAI,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC;QAC1B,CAAC;QACD,MAAM,GAAG,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;QAC5D,OAAO,CAAC,GAAG,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;QAE9B,MAAM,WAAW,GAAG,IAAI,OAAO,CAAC,OAAO,EAAE,YAAY,EAAE,OAAO,CAAC,CAAC;QAChE,KAAK,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,WAAW,EAAE,CAAC;YACjC,OAAO,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACpB,CAAC;QAED,yEAAyE;QACzE,uEAAuE;QACvE,IAAI,CAAC,aAAa,EAAE,CAAC;YACnB,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,IAAI,YAAY,EAAE,wBAAY,CAAC,SAAS,CAAC,CAAC;QACrE,CAAC;QAED,IAAI,YAAY,GAAG,OAAO,EAAE,YAAY,CAAC;QACzC,IAAI,CAAC,YAAY,EAAE,MAAM,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,GAAG,CAAC,EAAE,CAAC;YAClE,MAAM,aAAa,GAAG,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAC1D,IAAI,CAAC,YAAY,EAAE,CAAC;gBAClB,YAAY,GAAG,EAAE,MAAM,EAAE,aAAa,EAAE,CAAC;YAC3C,CAAC;iBAAM,CAAC;gBACN,YAAY,CAAC,MAAM,GAAG,aAAa,CAAC;YACtC,CAAC;QACH,CAAC;QAED,IAAI,IAAI,CAAC,IAAI,YAAY,cAAc,EAAE,CAAC;YACxC,IAAI,CAAC,YAAY,EAAE,CAAC;gBAClB,YAAY,GAAG,EAAE,CAAC;YACpB,CAAC;YACD,MAAM,CAAC,MAAM,CAAC,YAAY,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC,CAAC;QAClD,CAAC;QAED,IAAI,KAAK,CAAC;QACV,IAAI,CAAC;YACH,KAAK,GAAG,uBAAA,IAAI,wBAAO,CAAC,mBAAmB,CAAC,OAAO,EAAE;gBAC/C,GAAG,EAAE,MAAM;gBACX,OAAO,EAAE;oBACP,GAAG,YAAY;oBACf,IAAI,EAAE,IAAI,CAAC,IAAI,IAAI,IAAI;oBACvB,OAAO;oBACP,MAAM;iBACP;aACF,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,GAAY,EAAE,CAAC;YACtB,OAAO,IAAA,WAAG,EACR,IAAI,2CAAqB,CAAC,uCAAuC,EAAE;gBACjE,KAAK,EAAE,GAAG;aACX,CAAC,CACH,CAAC;QACJ,CAAC;QAED,OAAO,IAAA,UAAE,EAAC,IAAI,OAAO,CAAC,KAAK,CAAC,GAAG,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC;IACnD,CAAC;IAEM,KAAK,CAAC,GAAG,CACd,OAAgB,EAChB,OAKC;QAUD,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,GAAG,OAAO,CAAC;QAExC,OAAO,IAAA,kBAAK,EACV,KAAK,IAAI,EAAE;YACT,MAAM,GAAG,GAAG,MAAM,uBAAA,IAAI,wBAAO,CAAC,aAAa,CAAC,OAAO,EAAE,OAAO,CAAC,KAAK,EAAE,CAAC,CAAC;YACtE,MAAM,UAAU,CAAC,uBAAA,IAAI,yBAAQ,EAAE,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE,CAC9C,uBAAA,IAAI,yBAAQ,EAAE,GAAG,CAAC,wBAAwB,EAAE,CAAC,CAAC,CAC/C,CAAC;YAEF,IAAI,QAAQ,GAAG,MAAM,uBAAA,IAAI,6BAAY,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;YAEnD,IAAI,CAAC;gBACH,IAAI,IAAA,yBAAe,EAAC,QAAQ,EAAE,UAAU,CAAC,EAAE,CAAC;oBAC1C,MAAM,MAAM,GAAG,MAAM,uBAAA,IAAI,wBAAO,CAAC,UAAU,CACzC,OAAO,EACP,QAAQ,EACR,IAAI,CACL,CAAC;oBACF,IAAI,MAAM,CAAC,KAAK,EAAE,CAAC;wBACjB,MAAM,MAAM,CAAC,KAAK,CAAC;oBACrB,CAAC;oBACD,QAAQ,GAAG,MAAM,CAAC,QAAQ,IAAI,QAAQ,CAAC;gBACzC,CAAC;qBAAM,CAAC;oBACN,QAAQ,GAAG,MAAM,uBAAA,IAAI,wBAAO,CAAC,YAAY,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;gBAC/D,CAAC;YACH,CAAC;oBAAS,CAAC;gBACT,MAAM,WAAW,CAAC,uBAAA,IAAI,yBAAQ,EAAE,QAAQ,EAAE,GAAG,CAAC;qBAC3C,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,uBAAA,IAAI,yBAAQ,EAAE,GAAG,CAAC,yBAAyB,EAAE,CAAC,CAAC,CAAC,CAAC;YACjE,CAAC;YAED,OAAO,QAAQ,CAAC;QAClB,CAAC,EACD,EAAE,MAAM,EAAE,OAAO,CAAC,WAAW,EAAE,WAAW,EAAE,OAAO,CAAC,UAAU,EAAE,CACjE,CAAC,IAAI,CACJ,CAAC,CAAC,EAAE,EAAE,CAAC,IAAA,UAAE,EAAC,CAAC,CAAC,EACZ,CAAC,GAAG,EAAE,EAAE;YACN,QAAQ,IAAI,EAAE,CAAC;gBACb,KAAK,IAAA,sBAAY,EAAC,GAAG,CAAC;oBACpB,OAAO,IAAA,WAAG,EACR,IAAI,yCAAmB,CAAC,2BAA2B,EAAE;wBACnD,KAAK,EAAE,GAAG;qBACX,CAAC,CACH,CAAC;gBACJ,KAAK,IAAA,wBAAc,EAAC,GAAG,CAAC;oBACtB,OAAO,IAAA,WAAG,EACR,IAAI,yCAAmB,CAAC,mBAAmB,EAAE,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,CAC7D,CAAC;gBACJ,KAAK,IAAA,2BAAiB,EAAC,GAAG,CAAC;oBACzB,OAAO,IAAA,WAAG,EACR,IAAI,qCAAe,CAAC,wBAAwB,EAAE,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,CAC9D,CAAC;gBACJ;oBACE,OAAO,IAAA,WAAG,EACR,IAAI,2CAAqB,CAAC,8BAA8B,EAAE;wBACxD,KAAK,EAAE,GAAG;qBACX,CAAC,CACH,CAAC;YACN,CAAC;QACH,CAAC,CACF,CAAC;IACJ,CAAC;CACF;AA9ND,8BA8NC;;AAED,MAAM,qBAAqB,GAAG,mCAAmC,CAAC;AAClE,KAAK,UAAU,UAAU,CAAC,MAA0B,EAAE,GAAY;IAChE,IAAI,CAAC,MAAM,EAAE,CAAC;QACZ,OAAO;IACT,CAAC;IAED,MAAM,WAAW,GAAG,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;IACpD,MAAM,EAAE,GAAG,WAAW,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;IAE5C,MAAM,CAAC,KAAK,CAAC,cAAc,GAAG,CAAC,MAAM,IAAI,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC;IAEpD,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;IACzB,KAAK,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,GAAG,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC;QAC3C,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;IAC3B,CAAC;IACD,MAAM,CAAC,QAAQ,EAAE,CAAC;IAElB,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;IACtB,QAAQ,IAAI,EAAE,CAAC;QACb,KAAK,qBAAqB,CAAC,IAAI,CAAC,EAAE,CAAC;YACjC,MAAM,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC;YACrC,MAAM;QACR,KAAK,EAAE,CAAC,UAAU,CAAC,OAAO,CAAC;YACzB,MAAM,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC;YACrC,MAAM;QACR,KAAK,EAAE,KAAK,qBAAqB,CAAC,CAAC,CAAC;YAClC,MAAM,IAAI,GAAG,MAAM,GAAG,CAAC,KAAK,EAAE,CAAC,QAAQ,EAAE,CAAC;YAC1C,KAAK,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,IAAI,EAAE,CAAC;gBAC1B,MAAM,MAAM,GAAG,CAAC,YAAY,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;gBAChD,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,MAAM,EAAE,CAAC,CAAC;YAChC,CAAC;YACD,MAAM;QACR,CAAC;QACD;YACE,MAAM,CAAC,GAAG,CAAC,IAAI,WAAW,GAAG,CAAC,CAAC;YAC/B,MAAM;IACV,CAAC;IACD,MAAM,CAAC,QAAQ,EAAE,CAAC;IAElB,MAAM,CAAC,QAAQ,EAAE,CAAC;AACpB,CAAC;AAED,KAAK,UAAU,WAAW,CACxB,MAA0B,EAC1B,GAAa,EACb,GAAY;IAEZ,IAAI,CAAC,MAAM,EAAE,CAAC;QACZ,OAAO;IACT,CAAC;IAED,MAAM,WAAW,GAAG,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;IACpD,MAAM,EAAE,GAAG,WAAW,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;IAE5C,MAAM,CAAC,KAAK,CAAC,eAAe,GAAG,CAAC,MAAM,IAAI,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC;IACrD,MAAM,CAAC,GAAG,CAAC,cAAc,EAAE,GAAG,CAAC,MAAM,EAAE,GAAG,CAAC,UAAU,CAAC,CAAC;IAEvD,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;IACzB,KAAK,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,GAAG,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC;QAC3C,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;IAC3B,CAAC;IACD,MAAM,CAAC,QAAQ,EAAE,CAAC;IAElB,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;IACtB,QAAQ,IAAI,EAAE,CAAC;QACb,KAAK,IAAA,0BAAgB,EAAC,GAAG,EAAE,kBAAkB,CAAC;eACzC,qBAAqB,CAAC,IAAI,CAAC,EAAE,CAAC;YACjC,MAAM,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC;YACrC,MAAM;QACR,KAAK,IAAA,0BAAgB,EAAC,GAAG,EAAE,mBAAmB,CAAC;YAC7C,MAAM,CAAC,GAAG,CAAC,IAAI,WAAW,GAAG,CAAC,CAAC;YAC/B,MAAM;QACR,KAAK,IAAA,0BAAgB,EAAC,GAAG,EAAE,QAAQ,CAAC;YAClC,MAAM,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC;YACrC,MAAM;QACR,KAAK,IAAA,0BAAgB,EAAC,GAAG,EAAE,qBAAqB,CAAC,CAAC,CAAC,CAAC;YAClD,MAAM,IAAI,GAAG,MAAM,GAAG,CAAC,KAAK,EAAE,CAAC,QAAQ,EAAE,CAAC;YAC1C,KAAK,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,IAAI,EAAE,CAAC;gBAC1B,MAAM,MAAM,GAAG,CAAC,YAAY,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;gBAChD,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,MAAM,EAAE,CAAC,CAAC;YAChC,CAAC;YACD,MAAM;QACR,CAAC;QACD;YACE,MAAM,CAAC,GAAG,CAAC,IAAI,WAAW,GAAG,CAAC,CAAC;YAC/B,MAAM;IACV,CAAC;IACD,MAAM,CAAC,QAAQ,EAAE,CAAC;IAElB,MAAM,CAAC,QAAQ,EAAE,CAAC;AACpB,CAAC"}