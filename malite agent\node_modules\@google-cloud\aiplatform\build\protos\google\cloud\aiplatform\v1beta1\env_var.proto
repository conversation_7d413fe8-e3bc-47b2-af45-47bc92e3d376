// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.aiplatform.v1beta1;

import "google/api/field_behavior.proto";

option csharp_namespace = "Google.Cloud.AIPlatform.V1Beta1";
option go_package = "cloud.google.com/go/aiplatform/apiv1beta1/aiplatformpb;aiplatformpb";
option java_multiple_files = true;
option java_outer_classname = "EnvVarProto";
option java_package = "com.google.cloud.aiplatform.v1beta1";
option php_namespace = "Google\\Cloud\\AIPlatform\\V1beta1";
option ruby_package = "Google::Cloud::AIPlatform::V1beta1";

// Represents an environment variable present in a Container or Python Module.
message EnvVar {
  // Required. Name of the environment variable. Must be a valid C identifier.
  string name = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. Variables that reference a $(VAR_NAME) are expanded
  // using the previous defined environment variables in the container and
  // any service environment variables. If a variable cannot be resolved,
  // the reference in the input string will be unchanged. The $(VAR_NAME)
  // syntax can be escaped with a double $$, ie: $$(VAR_NAME). Escaped
  // references will never be expanded, regardless of whether the variable
  // exists or not.
  string value = 2 [(google.api.field_behavior) = REQUIRED];
}
