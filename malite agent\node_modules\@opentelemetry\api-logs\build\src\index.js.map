{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/index.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;;;;;;;;;;;AAEH,iDAA+B;AAC/B,yDAAuC;AACvC,oDAAkC;AAClC,wDAAsC;AACtC,mDAAiC;AACjC,+CAA6B;AAC7B,uDAAqC;AAErC,qCAAqC;AACxB,QAAA,IAAI,GAAG,cAAO,CAAC,WAAW,EAAE,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport * from './types/Logger';\nexport * from './types/LoggerProvider';\nexport * from './types/LogRecord';\nexport * from './types/LoggerOptions';\nexport * from './types/AnyValue';\nexport * from './NoopLogger';\nexport * from './NoopLoggerProvider';\n\nimport { LogsAPI } from './api/logs';\nexport const logs = LogsAPI.getInstance();\n"]}