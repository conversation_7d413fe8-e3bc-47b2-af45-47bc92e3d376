// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.aiplatform.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.AIPlatform.V1";
option go_package = "cloud.google.com/go/aiplatform/apiv1/aiplatformpb;aiplatformpb";
option java_multiple_files = true;
option java_outer_classname = "MetadataSchemaProto";
option java_package = "com.google.cloud.aiplatform.v1";
option php_namespace = "Google\\Cloud\\AIPlatform\\V1";
option ruby_package = "Google::Cloud::AIPlatform::V1";

// Instance of a general MetadataSchema.
message MetadataSchema {
  option (google.api.resource) = {
    type: "aiplatform.googleapis.com/MetadataSchema"
    pattern: "projects/{project}/locations/{location}/metadataStores/{metadata_store}/metadataSchemas/{metadata_schema}"
  };

  // Describes the type of the MetadataSchema.
  enum MetadataSchemaType {
    // Unspecified type for the MetadataSchema.
    METADATA_SCHEMA_TYPE_UNSPECIFIED = 0;

    // A type indicating that the MetadataSchema will be used by Artifacts.
    ARTIFACT_TYPE = 1;

    // A typee indicating that the MetadataSchema will be used by Executions.
    EXECUTION_TYPE = 2;

    // A state indicating that the MetadataSchema will be used by Contexts.
    CONTEXT_TYPE = 3;
  }

  // Output only. The resource name of the MetadataSchema.
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // The version of the MetadataSchema. The version's format must match
  // the following regular expression: `^[0-9]+[.][0-9]+[.][0-9]+$`, which would
  // allow to order/compare different versions. Example: 1.0.0, 1.0.1, etc.
  string schema_version = 2;

  // Required. The raw YAML string representation of the MetadataSchema. The
  // combination of [MetadataSchema.version] and the schema name given by
  // `title` in [MetadataSchema.schema] must be unique within a MetadataStore.
  //
  // The schema is defined as an OpenAPI 3.0.2
  // [MetadataSchema
  // Object](https://github.com/OAI/OpenAPI-Specification/blob/master/versions/3.0.2.md#schemaObject)
  string schema = 3 [(google.api.field_behavior) = REQUIRED];

  // The type of the MetadataSchema. This is a property that identifies which
  // metadata types will use the MetadataSchema.
  MetadataSchemaType schema_type = 4;

  // Output only. Timestamp when this MetadataSchema was created.
  google.protobuf.Timestamp create_time = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Description of the Metadata Schema
  string description = 6;
}
