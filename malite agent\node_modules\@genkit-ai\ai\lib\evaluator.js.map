{"version": 3, "sources": ["../src/evaluator.ts"], "sourcesContent": ["/**\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Action, defineAction, z } from '@genkit-ai/core';\nimport { logger } from '@genkit-ai/core/logging';\nimport { Registry } from '@genkit-ai/core/registry';\nimport { toJsonSchema } from '@genkit-ai/core/schema';\nimport { SPAN_TYPE_ATTR, runInNewSpan } from '@genkit-ai/core/tracing';\nimport { randomUUID } from 'crypto';\n\nexport const ATTR_PREFIX = 'genkit';\nexport const SPAN_STATE_ATTR = ATTR_PREFIX + ':state';\n\nexport const BaseDataPointSchema = z.object({\n  input: z.unknown(),\n  output: z.unknown().optional(),\n  context: z.array(z.unknown()).optional(),\n  reference: z.unknown().optional(),\n  testCaseId: z.string().optional(),\n  traceIds: z.array(z.string()).optional(),\n});\n\n// DataPoint that is to be used for actions. This needs testCaseId to be present.\nexport const BaseEvalDataPointSchema = BaseDataPointSchema.extend({\n  testCaseId: z.string(),\n});\nexport type BaseEvalDataPoint = z.infer<typeof BaseEvalDataPointSchema>;\n\nconst EvalStatusEnumSchema = z.enum(['UNKNOWN', 'PASS', 'FAIL']);\n\n/** Enum that indicates if an evaluation has passed or failed */\nexport enum EvalStatusEnum {\n  UNKNOWN = 'UNKNOWN',\n  PASS = 'PASS',\n  FAIL = 'FAIL',\n}\n\nexport const ScoreSchema = z.object({\n  id: z\n    .string()\n    .describe(\n      'Optional ID to differentiate different scores if applying in a single evaluation'\n    )\n    .optional(),\n  score: z.union([z.number(), z.string(), z.boolean()]).optional(),\n  status: EvalStatusEnumSchema.optional(),\n  error: z.string().optional(),\n  details: z\n    .object({\n      reasoning: z.string().optional(),\n    })\n    .passthrough()\n    .optional(),\n});\n\n// Update genkit-tools/src/utils/evals.ts if you change this value\nexport const EVALUATOR_METADATA_KEY_DISPLAY_NAME = 'evaluatorDisplayName';\nexport const EVALUATOR_METADATA_KEY_DEFINITION = 'evaluatorDefinition';\nexport const EVALUATOR_METADATA_KEY_IS_BILLED = 'evaluatorIsBilled';\n\nexport type Score = z.infer<typeof ScoreSchema>;\nexport type BaseDataPoint = z.infer<typeof BaseDataPointSchema>;\nexport type Dataset<\n  DataPoint extends typeof BaseDataPointSchema = typeof BaseDataPointSchema,\n> = Array<z.infer<DataPoint>>;\n\nexport const EvalResponseSchema = z.object({\n  sampleIndex: z.number().optional(),\n  testCaseId: z.string(),\n  traceId: z.string().optional(),\n  spanId: z.string().optional(),\n  evaluation: z.union([ScoreSchema, z.array(ScoreSchema)]),\n});\nexport type EvalResponse = z.infer<typeof EvalResponseSchema>;\n\nexport const EvalResponsesSchema = z.array(EvalResponseSchema);\nexport type EvalResponses = z.infer<typeof EvalResponsesSchema>;\n\nexport type EvaluatorFn<\n  EvalDataPoint extends\n    typeof BaseEvalDataPointSchema = typeof BaseEvalDataPointSchema,\n  CustomOptions extends z.ZodTypeAny = z.ZodTypeAny,\n> = (\n  input: z.infer<EvalDataPoint>,\n  evaluatorOptions?: z.infer<CustomOptions>\n) => Promise<EvalResponse>;\n\nexport type EvaluatorAction<\n  DataPoint extends typeof BaseDataPointSchema = typeof BaseDataPointSchema,\n  CustomOptions extends z.ZodTypeAny = z.ZodTypeAny,\n> = Action<typeof EvalRequestSchema, typeof EvalResponsesSchema> & {\n  __dataPointType?: DataPoint;\n  __configSchema?: CustomOptions;\n};\n\nfunction withMetadata<\n  DataPoint extends typeof BaseDataPointSchema = typeof BaseDataPointSchema,\n  CustomOptions extends z.ZodTypeAny = z.ZodTypeAny,\n>(\n  evaluator: Action<typeof EvalRequestSchema, typeof EvalResponsesSchema>,\n  dataPointType?: DataPoint,\n  configSchema?: CustomOptions\n): EvaluatorAction<DataPoint, CustomOptions> {\n  const withMeta = evaluator as EvaluatorAction<DataPoint, CustomOptions>;\n  withMeta.__dataPointType = dataPointType;\n  withMeta.__configSchema = configSchema;\n  return withMeta;\n}\n\nconst EvalRequestSchema = z.object({\n  dataset: z.array(BaseDataPointSchema),\n  evalRunId: z.string(),\n  options: z.unknown(),\n});\n\nexport interface EvaluatorParams<\n  DataPoint extends typeof BaseDataPointSchema = typeof BaseDataPointSchema,\n  CustomOptions extends z.ZodTypeAny = z.ZodTypeAny,\n> {\n  evaluator: EvaluatorArgument<DataPoint, CustomOptions>;\n  dataset: Dataset<DataPoint>;\n  evalRunId?: string;\n  options?: z.infer<CustomOptions>;\n}\n\n/**\n * Creates evaluator action for the provided {@link EvaluatorFn} implementation.\n */\nexport function defineEvaluator<\n  DataPoint extends typeof BaseDataPointSchema = typeof BaseDataPointSchema,\n  EvalDataPoint extends\n    typeof BaseEvalDataPointSchema = typeof BaseEvalDataPointSchema,\n  EvaluatorOptions extends z.ZodTypeAny = z.ZodTypeAny,\n>(\n  registry: Registry,\n  options: {\n    name: string;\n    displayName: string;\n    definition: string;\n    dataPointType?: DataPoint;\n    configSchema?: EvaluatorOptions;\n    isBilled?: boolean;\n  },\n  runner: EvaluatorFn<EvalDataPoint, EvaluatorOptions>\n) {\n  const evalMetadata = {};\n  evalMetadata[EVALUATOR_METADATA_KEY_IS_BILLED] =\n    options.isBilled == undefined ? true : options.isBilled;\n  evalMetadata[EVALUATOR_METADATA_KEY_DISPLAY_NAME] = options.displayName;\n  evalMetadata[EVALUATOR_METADATA_KEY_DEFINITION] = options.definition;\n  if (options.configSchema) {\n    evalMetadata['customOptions'] = toJsonSchema({\n      schema: options.configSchema,\n    });\n  }\n  const evaluator = defineAction(\n    registry,\n    {\n      actionType: 'evaluator',\n      name: options.name,\n      inputSchema: EvalRequestSchema.extend({\n        dataset: options.dataPointType\n          ? z.array(options.dataPointType)\n          : z.array(BaseDataPointSchema),\n        options: options.configSchema ?? z.unknown(),\n        evalRunId: z.string(),\n      }),\n      outputSchema: EvalResponsesSchema,\n      metadata: {\n        type: 'evaluator',\n        evaluator: evalMetadata,\n      },\n    },\n    async (i) => {\n      let evalResponses: EvalResponses = [];\n      for (let index = 0; index < i.dataset.length; index++) {\n        const datapoint: BaseEvalDataPoint = {\n          ...i.dataset[index],\n          testCaseId: i.dataset[index].testCaseId ?? randomUUID(),\n        };\n        try {\n          await runInNewSpan(\n            registry,\n            {\n              metadata: {\n                name: `Test Case ${datapoint.testCaseId}`,\n                metadata: { 'evaluator:evalRunId': i.evalRunId },\n              },\n              labels: {\n                [SPAN_TYPE_ATTR]: 'evaluator',\n              },\n            },\n            async (metadata, otSpan) => {\n              const spanId = otSpan.spanContext().spanId;\n              const traceId = otSpan.spanContext().traceId;\n              try {\n                metadata.input = {\n                  input: datapoint.input,\n                  output: datapoint.output,\n                  context: datapoint.context,\n                };\n                const testCaseOutput = await runner(datapoint, i.options);\n                testCaseOutput.sampleIndex = index;\n                testCaseOutput.spanId = spanId;\n                testCaseOutput.traceId = traceId;\n                metadata.output = testCaseOutput;\n                evalResponses.push(testCaseOutput);\n                return testCaseOutput;\n              } catch (e) {\n                evalResponses.push({\n                  sampleIndex: index,\n                  spanId,\n                  traceId,\n                  testCaseId: datapoint.testCaseId,\n                  evaluation: {\n                    error: `Evaluation of test case ${datapoint.testCaseId} failed: \\n${(e as Error).stack}`,\n                    status: EvalStatusEnum.FAIL,\n                  },\n                });\n                // Throw to mark the span as failed.\n                throw e;\n              }\n            }\n          );\n        } catch (e) {\n          logger.error(\n            `Evaluation of test case ${datapoint.testCaseId} failed: \\n${(e as Error).stack}`\n          );\n          continue;\n        }\n      }\n      return evalResponses;\n    }\n  );\n  const ewm = withMetadata(\n    evaluator as any as Action<\n      typeof EvalRequestSchema,\n      typeof EvalResponsesSchema\n    >,\n    options.dataPointType,\n    options.configSchema\n  );\n  return ewm;\n}\n\nexport type EvaluatorArgument<\n  DataPoint extends typeof BaseDataPointSchema = typeof BaseDataPointSchema,\n  CustomOptions extends z.ZodTypeAny = z.ZodTypeAny,\n> =\n  | string\n  | EvaluatorAction<DataPoint, CustomOptions>\n  | EvaluatorReference<CustomOptions>;\n\n/**\n * A veneer for interacting with evaluators.\n */\nexport async function evaluate<\n  DataPoint extends typeof BaseDataPointSchema = typeof BaseDataPointSchema,\n  CustomOptions extends z.ZodTypeAny = z.ZodTypeAny,\n>(\n  registry: Registry,\n  params: EvaluatorParams<DataPoint, CustomOptions>\n): Promise<EvalResponses> {\n  let evaluator: EvaluatorAction<DataPoint, CustomOptions>;\n  if (typeof params.evaluator === 'string') {\n    evaluator = await registry.lookupAction(`/evaluator/${params.evaluator}`);\n  } else if (Object.hasOwnProperty.call(params.evaluator, 'info')) {\n    evaluator = await registry.lookupAction(\n      `/evaluator/${params.evaluator.name}`\n    );\n  } else {\n    evaluator = params.evaluator as EvaluatorAction<DataPoint, CustomOptions>;\n  }\n  if (!evaluator) {\n    throw new Error('Unable to utilize the provided evaluator');\n  }\n  return (await evaluator({\n    dataset: params.dataset,\n    options: params.options,\n    evalRunId: params.evalRunId ?? randomUUID(),\n  })) as EvalResponses;\n}\n\nexport const EvaluatorInfoSchema = z.object({\n  /** Friendly label for this evaluator */\n  label: z.string().optional(),\n  metrics: z.array(z.string()),\n});\nexport type EvaluatorInfo = z.infer<typeof EvaluatorInfoSchema>;\n\nexport interface EvaluatorReference<CustomOptions extends z.ZodTypeAny> {\n  name: string;\n  configSchema?: CustomOptions;\n  info?: EvaluatorInfo;\n}\n\n/**\n * Helper method to configure a {@link EvaluatorReference} to a plugin.\n */\nexport function evaluatorRef<\n  CustomOptionsSchema extends z.ZodTypeAny = z.ZodTypeAny,\n>(\n  options: EvaluatorReference<CustomOptionsSchema>\n): EvaluatorReference<CustomOptionsSchema> {\n  return { ...options };\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAgBA,kBAAwC;AACxC,qBAAuB;AAEvB,oBAA6B;AAC7B,qBAA6C;AAC7C,oBAA2B;AAEpB,MAAM,cAAc;AACpB,MAAM,kBAAkB,cAAc;AAEtC,MAAM,sBAAsB,cAAE,OAAO;AAAA,EAC1C,OAAO,cAAE,QAAQ;AAAA,EACjB,QAAQ,cAAE,QAAQ,EAAE,SAAS;AAAA,EAC7B,SAAS,cAAE,MAAM,cAAE,QAAQ,CAAC,EAAE,SAAS;AAAA,EACvC,WAAW,cAAE,QAAQ,EAAE,SAAS;AAAA,EAChC,YAAY,cAAE,OAAO,EAAE,SAAS;AAAA,EAChC,UAAU,cAAE,MAAM,cAAE,OAAO,CAAC,EAAE,SAAS;AACzC,CAAC;AAGM,MAAM,0BAA0B,oBAAoB,OAAO;AAAA,EAChE,YAAY,cAAE,OAAO;AACvB,CAAC;AAGD,MAAM,uBAAuB,cAAE,KAAK,CAAC,WAAW,QAAQ,MAAM,CAAC;AAGxD,IAAK,iBAAL,kBAAKA,oBAAL;AACL,EAAAA,gBAAA,aAAU;AACV,EAAAA,gBAAA,UAAO;AACP,EAAAA,gBAAA,UAAO;AAHG,SAAAA;AAAA,GAAA;AAML,MAAM,cAAc,cAAE,OAAO;AAAA,EAClC,IAAI,cACD,OAAO,EACP;AAAA,IACC;AAAA,EACF,EACC,SAAS;AAAA,EACZ,OAAO,cAAE,MAAM,CAAC,cAAE,OAAO,GAAG,cAAE,OAAO,GAAG,cAAE,QAAQ,CAAC,CAAC,EAAE,SAAS;AAAA,EAC/D,QAAQ,qBAAqB,SAAS;AAAA,EACtC,OAAO,cAAE,OAAO,EAAE,SAAS;AAAA,EAC3B,SAAS,cACN,OAAO;AAAA,IACN,WAAW,cAAE,OAAO,EAAE,SAAS;AAAA,EACjC,CAAC,EACA,YAAY,EACZ,SAAS;AACd,CAAC;AAGM,MAAM,sCAAsC;AAC5C,MAAM,oCAAoC;AAC1C,MAAM,mCAAmC;AAQzC,MAAM,qBAAqB,cAAE,OAAO;AAAA,EACzC,aAAa,cAAE,OAAO,EAAE,SAAS;AAAA,EACjC,YAAY,cAAE,OAAO;AAAA,EACrB,SAAS,cAAE,OAAO,EAAE,SAAS;AAAA,EAC7B,QAAQ,cAAE,OAAO,EAAE,SAAS;AAAA,EAC5B,YAAY,cAAE,MAAM,CAAC,aAAa,cAAE,MAAM,WAAW,CAAC,CAAC;AACzD,CAAC;AAGM,MAAM,sBAAsB,cAAE,MAAM,kBAAkB;AAoB7D,SAAS,aAIP,WACA,eACA,cAC2C;AAC3C,QAAM,WAAW;AACjB,WAAS,kBAAkB;AAC3B,WAAS,iBAAiB;AAC1B,SAAO;AACT;AAEA,MAAM,oBAAoB,cAAE,OAAO;AAAA,EACjC,SAAS,cAAE,MAAM,mBAAmB;AAAA,EACpC,WAAW,cAAE,OAAO;AAAA,EACpB,SAAS,cAAE,QAAQ;AACrB,CAAC;AAeM,SAAS,gBAMd,UACA,SAQA,QACA;AACA,QAAM,eAAe,CAAC;AACtB,eAAa,gCAAgC,IAC3C,QAAQ,YAAY,SAAY,OAAO,QAAQ;AACjD,eAAa,mCAAmC,IAAI,QAAQ;AAC5D,eAAa,iCAAiC,IAAI,QAAQ;AAC1D,MAAI,QAAQ,cAAc;AACxB,iBAAa,eAAe,QAAI,4BAAa;AAAA,MAC3C,QAAQ,QAAQ;AAAA,IAClB,CAAC;AAAA,EACH;AACA,QAAM,gBAAY;AAAA,IAChB;AAAA,IACA;AAAA,MACE,YAAY;AAAA,MACZ,MAAM,QAAQ;AAAA,MACd,aAAa,kBAAkB,OAAO;AAAA,QACpC,SAAS,QAAQ,gBACb,cAAE,MAAM,QAAQ,aAAa,IAC7B,cAAE,MAAM,mBAAmB;AAAA,QAC/B,SAAS,QAAQ,gBAAgB,cAAE,QAAQ;AAAA,QAC3C,WAAW,cAAE,OAAO;AAAA,MACtB,CAAC;AAAA,MACD,cAAc;AAAA,MACd,UAAU;AAAA,QACR,MAAM;AAAA,QACN,WAAW;AAAA,MACb;AAAA,IACF;AAAA,IACA,OAAO,MAAM;AACX,UAAI,gBAA+B,CAAC;AACpC,eAAS,QAAQ,GAAG,QAAQ,EAAE,QAAQ,QAAQ,SAAS;AACrD,cAAM,YAA+B;AAAA,UACnC,GAAG,EAAE,QAAQ,KAAK;AAAA,UAClB,YAAY,EAAE,QAAQ,KAAK,EAAE,kBAAc,0BAAW;AAAA,QACxD;AACA,YAAI;AACF,oBAAM;AAAA,YACJ;AAAA,YACA;AAAA,cACE,UAAU;AAAA,gBACR,MAAM,aAAa,UAAU,UAAU;AAAA,gBACvC,UAAU,EAAE,uBAAuB,EAAE,UAAU;AAAA,cACjD;AAAA,cACA,QAAQ;AAAA,gBACN,CAAC,6BAAc,GAAG;AAAA,cACpB;AAAA,YACF;AAAA,YACA,OAAO,UAAU,WAAW;AAC1B,oBAAM,SAAS,OAAO,YAAY,EAAE;AACpC,oBAAM,UAAU,OAAO,YAAY,EAAE;AACrC,kBAAI;AACF,yBAAS,QAAQ;AAAA,kBACf,OAAO,UAAU;AAAA,kBACjB,QAAQ,UAAU;AAAA,kBAClB,SAAS,UAAU;AAAA,gBACrB;AACA,sBAAM,iBAAiB,MAAM,OAAO,WAAW,EAAE,OAAO;AACxD,+BAAe,cAAc;AAC7B,+BAAe,SAAS;AACxB,+BAAe,UAAU;AACzB,yBAAS,SAAS;AAClB,8BAAc,KAAK,cAAc;AACjC,uBAAO;AAAA,cACT,SAAS,GAAG;AACV,8BAAc,KAAK;AAAA,kBACjB,aAAa;AAAA,kBACb;AAAA,kBACA;AAAA,kBACA,YAAY,UAAU;AAAA,kBACtB,YAAY;AAAA,oBACV,OAAO,2BAA2B,UAAU,UAAU;AAAA,EAAe,EAAY,KAAK;AAAA,oBACtF,QAAQ;AAAA,kBACV;AAAA,gBACF,CAAC;AAED,sBAAM;AAAA,cACR;AAAA,YACF;AAAA,UACF;AAAA,QACF,SAAS,GAAG;AACV,gCAAO;AAAA,YACL,2BAA2B,UAAU,UAAU;AAAA,EAAe,EAAY,KAAK;AAAA,UACjF;AACA;AAAA,QACF;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAAA,EACF;AACA,QAAM,MAAM;AAAA,IACV;AAAA,IAIA,QAAQ;AAAA,IACR,QAAQ;AAAA,EACV;AACA,SAAO;AACT;AAaA,eAAsB,SAIpB,UACA,QACwB;AACxB,MAAI;AACJ,MAAI,OAAO,OAAO,cAAc,UAAU;AACxC,gBAAY,MAAM,SAAS,aAAa,cAAc,OAAO,SAAS,EAAE;AAAA,EAC1E,WAAW,OAAO,eAAe,KAAK,OAAO,WAAW,MAAM,GAAG;AAC/D,gBAAY,MAAM,SAAS;AAAA,MACzB,cAAc,OAAO,UAAU,IAAI;AAAA,IACrC;AAAA,EACF,OAAO;AACL,gBAAY,OAAO;AAAA,EACrB;AACA,MAAI,CAAC,WAAW;AACd,UAAM,IAAI,MAAM,0CAA0C;AAAA,EAC5D;AACA,SAAQ,MAAM,UAAU;AAAA,IACtB,SAAS,OAAO;AAAA,IAChB,SAAS,OAAO;AAAA,IAChB,WAAW,OAAO,iBAAa,0BAAW;AAAA,EAC5C,CAAC;AACH;AAEO,MAAM,sBAAsB,cAAE,OAAO;AAAA;AAAA,EAE1C,OAAO,cAAE,OAAO,EAAE,SAAS;AAAA,EAC3B,SAAS,cAAE,MAAM,cAAE,OAAO,CAAC;AAC7B,CAAC;AAYM,SAAS,aAGd,SACyC;AACzC,SAAO,EAAE,GAAG,QAAQ;AACtB;", "names": ["EvalStatusEnum"]}