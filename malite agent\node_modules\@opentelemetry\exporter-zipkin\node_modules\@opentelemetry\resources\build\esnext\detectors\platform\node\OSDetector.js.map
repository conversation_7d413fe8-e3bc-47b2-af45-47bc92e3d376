{"version": 3, "file": "OSDetector.js", "sourceRoot": "", "sources": ["../../../../../src/detectors/platform/node/OSDetector.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAKH,OAAO,EAAE,cAAc,EAAE,MAAM,kBAAkB,CAAC;AAElD;;;GAGG;AACH,MAAM,UAAU;IACd,MAAM,CAAC,OAAiC;QACtC,OAAO,OAAO,CAAC,OAAO,CAAC,cAAc,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC;IACzD,CAAC;CACF;AAED,MAAM,CAAC,MAAM,UAAU,GAAG,IAAI,UAAU,EAAE,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Detector } from '../../../types';\nimport { ResourceDetectionConfig } from '../../../config';\nimport { IResource } from '../../../IResource';\nimport { osDetectorSync } from './OSDetectorSync';\n\n/**\n * OSDetector detects the resources related to the operating system (OS) on\n * which the process represented by this resource is running.\n */\nclass OSDetector implements Detector {\n  detect(_config?: ResourceDetectionConfig): Promise<IResource> {\n    return Promise.resolve(osDetectorSync.detect(_config));\n  }\n}\n\nexport const osDetector = new OSDetector();\n"]}