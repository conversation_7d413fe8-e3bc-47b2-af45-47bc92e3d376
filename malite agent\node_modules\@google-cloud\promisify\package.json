{"name": "@google-cloud/promisify", "version": "4.0.0", "description": "A simple utility for promisifying functions and classes.", "main": "build/src/index.js", "types": "build/src/index.d.ts", "repository": "googleapis/nodejs-promisify", "scripts": {"test": "c8 mocha build/test", "lint": "gts check", "compile": "tsc -p .", "fix": "gts fix", "prepare": "npm run compile", "pretest": "npm run compile", "docs": "compodoc src/", "presystem-test": "npm run compile", "samples-test": "cd samples/ && npm link ../ && npm test && cd ../", "system-test": "mocha build/system-test", "docs-test": "linkinator docs", "predocs-test": "npm run docs", "prelint": "cd samples; npm link ../; npm install", "clean": "gts clean", "precompile": "gts clean"}, "keywords": [], "files": ["build/src", "!build/src/**/*.map"], "author": "Google Inc.", "license": "Apache-2.0", "devDependencies": {"@compodoc/compodoc": "^1.1.9", "@types/mocha": "^9.0.0", "@types/node": "^20.4.8", "@types/sinon": "^10.0.0", "c8": "^8.0.1", "chai": "^4.2.0", "codecov": "^3.0.4", "gts": "^5.0.0", "hard-rejection": "^2.1.0", "linkinator": "^5.0.1", "mocha": "^8.0.0", "sinon": "^15.0.0", "typescript": "^5.1.6"}, "engines": {"node": ">=14"}}