import { GoogleAuth } from 'google-auth-library';

/**
 * Copyright 2024 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

/**
 * Retrieves an access token using the provided GoogleAuth client.
 *
 * @param {GoogleAuth} auth - The GoogleAuth client.
 * @returns {Promise<string | null>} - A promise that resolves to the access token.
 */
declare function getAccessToken(auth: GoogleAuth): Promise<string | null>;
/**
 * Retrieves the project number for a given project ID.
 *
 * This function sends a request to the Google Cloud Resource Manager API to
 * fetch the project number for the specified project ID.
 *
 * @param {string} projectId - The ID of the Google Cloud project.
 * @returns {Promise<string>} - A promise that resolves to the project number.
 * @throws {Error} - Throws an error if the project number cannot be fetched.
 */
declare function getProjectNumber(projectId: string): Promise<string>;

export { getAccessToken, getProjectNumber };
