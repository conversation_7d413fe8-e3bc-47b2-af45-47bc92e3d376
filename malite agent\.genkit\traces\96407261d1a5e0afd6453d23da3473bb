{"traceId": "96407261d1a5e0afd6453d23da3473bb", "spans": {"482442887ed19dd9": {"spanId": "482442887ed19dd9", "traceId": "96407261d1a5e0afd6453d23da3473bb", "parentSpanId": "b6dc82ee104245eb", "startTime": 1748148492214, "endTime": 1748148495617.6023, "attributes": {"genkit:type": "action", "genkit:metadata:subtype": "model", "genkit:name": "vertexai/gemini-1.0-pro", "genkit:path": "/{generate,t:util}/{vertexai/gemini-1.0-pro,t:action,s:model}", "genkit:input": "{\"messages\":[{\"role\":\"user\",\"content\":[{\"text\":\"مرحباً، قدم نفسك باللغة العربية وأخبرني كيف يمكنني البدء باستخدام Genkit.\"}]}],\"config\":{},\"tools\":[],\"output\":{}}", "genkit:state": "error"}, "displayName": "vertexai/gemini-1.0-pro", "links": [], "instrumentationLibrary": {"name": "genkit-tracer", "version": "v1"}, "spanKind": "INTERNAL", "sameProcessAsParentSpan": {"value": true}, "status": {"code": 2, "message": "[VertexAI.ClientError]: got status: 404 Not Found. {\"error\":{\"code\":404,\"message\":\"Publisher Model `projects/376309378697/locations/us-central1/publishers/google/models/gemini-1.0-pro` was not found or your project does not have access to it. Please ensure you are using a valid model version. For more information, see: https://cloud.google.com/vertex-ai/generative-ai/docs/learn/model-versions\",\"status\":\"NOT_FOUND\"}}"}, "timeEvents": {"timeEvent": [{"time": 1748148495617.072, "annotation": {"attributes": {"exception.type": "ClientError", "exception.message": "[VertexAI.ClientError]: got status: 404 Not Found. {\"error\":{\"code\":404,\"message\":\"Publisher Model `projects/376309378697/locations/us-central1/publishers/google/models/gemini-1.0-pro` was not found or your project does not have access to it. Please ensure you are using a valid model version. For more information, see: https://cloud.google.com/vertex-ai/generative-ai/docs/learn/model-versions\",\"status\":\"NOT_FOUND\"}}", "exception.stacktrace": "ClientError: [VertexAI.ClientError]: got status: 404 Not Found. {\"error\":{\"code\":404,\"message\":\"Publisher Model `projects/376309378697/locations/us-central1/publishers/google/models/gemini-1.0-pro` was not found or your project does not have access to it. Please ensure you are using a valid model version. For more information, see: https://cloud.google.com/vertex-ai/generative-ai/docs/learn/model-versions\",\"status\":\"NOT_FOUND\"}}\n    at throwErrorIfNotOK (C:\\Users\\<USER>\\malite agent\\node_modules\\@google-cloud\\vertexai\\build\\src\\functions\\post_fetch_processing.js:32:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async generateContent (C:\\Users\\<USER>\\malite agent\\node_modules\\@google-cloud\\vertexai\\build\\src\\functions\\generate_content.js:59:5)\n    at async ChatSessionPreview.sendMessage (C:\\Users\\<USER>\\malite agent\\node_modules\\@google-cloud\\vertexai\\build\\src\\models\\chat_session.js:242:39)\n    at async callGemini (C:\\Users\\<USER>\\malite agent\\node_modules\\@genkit-ai\\vertexai\\lib\\gemini.js:908:26)\n    at async C:\\Users\\<USER>\\malite agent\\node_modules\\@genkit-ai\\vertexai\\lib\\gemini.js:952:11\n    at async C:\\Users\\<USER>\\malite agent\\node_modules\\@genkit-ai\\core\\lib\\action.js:210:14\n    at async C:\\Users\\<USER>\\malite agent\\node_modules\\@genkit-ai\\core\\lib\\action.js:131:27\n    at async C:\\Users\\<USER>\\malite agent\\node_modules\\@genkit-ai\\core\\lib\\tracing\\instrumentation.js:65:16\n    at async C:\\Users\\<USER>\\malite agent\\node_modules\\@genkit-ai\\core\\lib\\tracing\\instrumentation.js:88:24"}, "description": "exception"}}]}}, "b6dc82ee104245eb": {"spanId": "b6dc82ee104245eb", "traceId": "96407261d1a5e0afd6453d23da3473bb", "startTime": 1748148492032, "endTime": 1748148495624.3037, "attributes": {"genkit:type": "util", "genkit:name": "generate", "genkit:isRoot": true, "genkit:path": "/{generate,t:util}", "genkit:input": "{\"model\":\"vertexai/gemini-1.0-pro\",\"messages\":[{\"role\":\"user\",\"content\":[{\"text\":\"مرحباً، قدم نفسك باللغة العربية وأخبرني كيف يمكنني البدء باستخدام Genkit.\"}]}],\"config\":{}}", "genkit:state": "error"}, "displayName": "generate", "links": [], "instrumentationLibrary": {"name": "genkit-tracer", "version": "v1"}, "spanKind": "INTERNAL", "sameProcessAsParentSpan": {"value": true}, "status": {"code": 2, "message": "[VertexAI.ClientError]: got status: 404 Not Found. {\"error\":{\"code\":404,\"message\":\"Publisher Model `projects/376309378697/locations/us-central1/publishers/google/models/gemini-1.0-pro` was not found or your project does not have access to it. Please ensure you are using a valid model version. For more information, see: https://cloud.google.com/vertex-ai/generative-ai/docs/learn/model-versions\",\"status\":\"NOT_FOUND\"}}"}, "timeEvents": {"timeEvent": [{"time": 1748148495623.855, "annotation": {"attributes": {"exception.type": "ClientError", "exception.message": "[VertexAI.ClientError]: got status: 404 Not Found. {\"error\":{\"code\":404,\"message\":\"Publisher Model `projects/376309378697/locations/us-central1/publishers/google/models/gemini-1.0-pro` was not found or your project does not have access to it. Please ensure you are using a valid model version. For more information, see: https://cloud.google.com/vertex-ai/generative-ai/docs/learn/model-versions\",\"status\":\"NOT_FOUND\"}}", "exception.stacktrace": "ClientError: [VertexAI.ClientError]: got status: 404 Not Found. {\"error\":{\"code\":404,\"message\":\"Publisher Model `projects/376309378697/locations/us-central1/publishers/google/models/gemini-1.0-pro` was not found or your project does not have access to it. Please ensure you are using a valid model version. For more information, see: https://cloud.google.com/vertex-ai/generative-ai/docs/learn/model-versions\",\"status\":\"NOT_FOUND\"}}\n    at throwErrorIfNotOK (C:\\Users\\<USER>\\malite agent\\node_modules\\@google-cloud\\vertexai\\build\\src\\functions\\post_fetch_processing.js:32:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async generateContent (C:\\Users\\<USER>\\malite agent\\node_modules\\@google-cloud\\vertexai\\build\\src\\functions\\generate_content.js:59:5)\n    at async ChatSessionPreview.sendMessage (C:\\Users\\<USER>\\malite agent\\node_modules\\@google-cloud\\vertexai\\build\\src\\models\\chat_session.js:242:39)\n    at async callGemini (C:\\Users\\<USER>\\malite agent\\node_modules\\@genkit-ai\\vertexai\\lib\\gemini.js:908:26)\n    at async C:\\Users\\<USER>\\malite agent\\node_modules\\@genkit-ai\\vertexai\\lib\\gemini.js:952:11\n    at async C:\\Users\\<USER>\\malite agent\\node_modules\\@genkit-ai\\core\\lib\\action.js:210:14\n    at async C:\\Users\\<USER>\\malite agent\\node_modules\\@genkit-ai\\core\\lib\\action.js:131:27\n    at async C:\\Users\\<USER>\\malite agent\\node_modules\\@genkit-ai\\core\\lib\\tracing\\instrumentation.js:65:16\n    at async C:\\Users\\<USER>\\malite agent\\node_modules\\@genkit-ai\\core\\lib\\tracing\\instrumentation.js:88:24"}, "description": "exception"}}]}}}, "displayName": "generate", "startTime": 1748148492032, "endTime": 1748148495624.3037}