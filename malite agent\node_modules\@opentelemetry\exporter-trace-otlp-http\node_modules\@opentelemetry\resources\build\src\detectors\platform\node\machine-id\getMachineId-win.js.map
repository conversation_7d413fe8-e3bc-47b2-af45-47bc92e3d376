{"version": 3, "file": "getMachineId-win.js", "sourceRoot": "", "sources": ["../../../../../../src/detectors/platform/node/machine-id/getMachineId-win.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,mCAAmC;AACnC,2CAAwC;AACxC,4CAA0C;AAEnC,KAAK,UAAU,YAAY;IAChC,MAAM,IAAI,GACR,4EAA4E,CAAC;IAC/E,IAAI,OAAO,GAAG,6BAA6B,CAAC;IAC5C,IAAI,OAAO,CAAC,IAAI,KAAK,MAAM,IAAI,wBAAwB,IAAI,OAAO,CAAC,GAAG,EAAE;QACtE,OAAO,GAAG,kCAAkC,GAAG,OAAO,CAAC;KACxD;IAED,IAAI;QACF,MAAM,MAAM,GAAG,MAAM,IAAA,qBAAS,EAAC,GAAG,OAAO,IAAI,IAAI,EAAE,CAAC,CAAC;QACrD,MAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;QAC5C,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;YACtB,OAAO,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;SACxB;KACF;IAAC,OAAO,CAAC,EAAE;QACV,UAAI,CAAC,KAAK,CAAC,6BAA6B,CAAC,EAAE,CAAC,CAAC;KAC9C;IAED,OAAO,EAAE,CAAC;AACZ,CAAC;AAnBD,oCAmBC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport * as process from 'process';\nimport { execAsync } from './execAsync';\nimport { diag } from '@opentelemetry/api';\n\nexport async function getMachineId(): Promise<string> {\n  const args =\n    'QUERY HKEY_LOCAL_MACHINE\\\\SOFTWARE\\\\Microsoft\\\\Cryptography /v MachineGuid';\n  let command = '%windir%\\\\System32\\\\REG.exe';\n  if (process.arch === 'ia32' && 'PROCESSOR_ARCHITEW6432' in process.env) {\n    command = '%windir%\\\\sysnative\\\\cmd.exe /c ' + command;\n  }\n\n  try {\n    const result = await execAsync(`${command} ${args}`);\n    const parts = result.stdout.split('REG_SZ');\n    if (parts.length === 2) {\n      return parts[1].trim();\n    }\n  } catch (e) {\n    diag.debug(`error reading machine id: ${e}`);\n  }\n\n  return '';\n}\n"]}