{"interfaces": {"google.cloud.aiplatform.v1.JobService": {"retry_codes": {"non_idempotent": [], "idempotent": ["DEADLINE_EXCEEDED", "UNAVAILABLE"]}, "retry_params": {"default": {"initial_retry_delay_millis": 100, "retry_delay_multiplier": 1.3, "max_retry_delay_millis": 60000, "initial_rpc_timeout_millis": 60000, "rpc_timeout_multiplier": 1, "max_rpc_timeout_millis": 60000, "total_timeout_millis": 600000}}, "methods": {"CreateCustomJob": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "GetCustomJob": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "ListCustomJobs": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "DeleteCustomJob": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "CancelCustomJob": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "CreateDataLabelingJob": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "GetDataLabelingJob": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "ListDataLabelingJobs": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "DeleteDataLabelingJob": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "CancelDataLabelingJob": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "CreateHyperparameterTuningJob": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "GetHyperparameterTuningJob": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "ListHyperparameterTuningJobs": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "DeleteHyperparameterTuningJob": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "CancelHyperparameterTuningJob": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "CreateNasJob": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "GetNasJob": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "ListNasJobs": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "DeleteNasJob": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "CancelNasJob": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "GetNasTrialDetail": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "ListNasTrialDetails": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "CreateBatchPredictionJob": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "GetBatchPredictionJob": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "ListBatchPredictionJobs": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "DeleteBatchPredictionJob": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "CancelBatchPredictionJob": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "CreateModelDeploymentMonitoringJob": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "SearchModelDeploymentMonitoringStatsAnomalies": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "GetModelDeploymentMonitoringJob": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "ListModelDeploymentMonitoringJobs": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "UpdateModelDeploymentMonitoringJob": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "DeleteModelDeploymentMonitoringJob": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "PauseModelDeploymentMonitoringJob": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "ResumeModelDeploymentMonitoringJob": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}}}}}