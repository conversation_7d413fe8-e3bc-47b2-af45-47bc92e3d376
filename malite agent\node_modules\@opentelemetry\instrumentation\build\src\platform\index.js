"use strict";
/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.normalize = exports.InstrumentationBase = void 0;
var node_1 = require("./node");
Object.defineProperty(exports, "InstrumentationBase", { enumerable: true, get: function () { return node_1.InstrumentationBase; } });
Object.defineProperty(exports, "normalize", { enumerable: true, get: function () { return node_1.normalize; } });
//# sourceMappingURL=index.js.map