{"version": 3, "file": "TemporalMetricProcessor.js", "sourceRoot": "", "sources": ["../../../src/state/TemporalMetricProcessor.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUH,OAAO,EAAE,sBAAsB,EAAE,MAAM,kCAAkC,CAAC;AAG1E,OAAO,EAAE,gBAAgB,EAAE,MAAM,WAAW,CAAC;AAoB7C;;;;;GAKG;AACH;IAUE,iCACU,WAA0B,EAClC,gBAAyC;QAF3C,iBAOC;QANS,gBAAW,GAAX,WAAW,CAAe;QAV5B,6BAAwB,GAAG,IAAI,GAAG,EAGvC,CAAC;QACI,mBAAc,GAAG,IAAI,GAAG,EAG7B,CAAC;QAMF,gBAAgB,CAAC,OAAO,CAAC,UAAA,MAAM;YAC7B,KAAI,CAAC,wBAAwB,CAAC,GAAG,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;;OAQG;IACH,8CAAY,GAAZ,UACE,SAAgC,EAChC,oBAA0C,EAC1C,oBAAyC,EACzC,cAAsB;QAEtB,IAAI,CAAC,mBAAmB,CAAC,oBAAoB,CAAC,CAAC;QAC/C,IAAM,uBAAuB,GAC3B,IAAI,CAAC,iCAAiC,CAAC,SAAS,CAAC,CAAC;QAEpD,IAAI,MAAM,GAAG,uBAAuB,CAAC;QACrC,IAAI,sBAA8C,CAAC;QACnD,8BAA8B;QAC9B,IAAI,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE;YACtC,oEAAoE;YACpE,IAAM,IAAI,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,SAAS,CAAE,CAAC;YACjD,IAAM,kBAAkB,GAAG,IAAI,CAAC,cAAc,CAAC;YAC/C,sBAAsB,GAAG,IAAI,CAAC,sBAAsB,CAAC;YAErD,sFAAsF;YACtF,kDAAkD;YAClD,+EAA+E;YAC/E,yEAAyE;YACzE,uEAAuE;YACvE,oFAAoF;YACpF,yEAAyE;YACzE,yCAAyC;YACzC,sFAAsF;YACtF,+CAA+C;YAC/C,oFAAoF;YACpF,sFAAsF;YACtF,IAAI,sBAAsB,KAAK,sBAAsB,CAAC,UAAU,EAAE;gBAChE,4FAA4F;gBAC5F,qCAAqC;gBACrC,MAAM,GAAG,uBAAuB,CAAC,KAAK,CACpC,IAAI,CAAC,aAAa,EAClB,uBAAuB,EACvB,IAAI,CAAC,WAAW,CACjB,CAAC;aACH;iBAAM;gBACL,MAAM,GAAG,uBAAuB,CAAC,kBAAkB,CACjD,IAAI,CAAC,aAAa,EAClB,uBAAuB,EACvB,kBAAkB,CACnB,CAAC;aACH;SACF;aAAM;YACL,4EAA4E;YAC5E,sBAAsB,GAAG,SAAS,CAAC,4BAA4B,CAC7D,oBAAoB,CAAC,IAAI,CAC1B,CAAC;SACH;QAED,kDAAkD;QAClD,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,SAAS,EAAE;YACjC,aAAa,EAAE,MAAM;YACrB,cAAc,gBAAA;YACd,sBAAsB,wBAAA;SACvB,CAAC,CAAC;QAEH,IAAM,mBAAmB,GAAG,kCAAkC,CAAC,MAAM,CAAC,CAAC;QAEvE,gEAAgE;QAChE,IAAI,mBAAmB,CAAC,MAAM,KAAK,CAAC,EAAE;YACpC,OAAO,SAAS,CAAC;SAClB;QAED,OAAO,IAAI,CAAC,WAAW,CAAC,YAAY,CAClC,oBAAoB,EACpB,sBAAsB,EACtB,mBAAmB;QACnB,aAAa,CAAC,cAAc,CAC7B,CAAC;IACJ,CAAC;IAEO,qDAAmB,GAA3B,UAA4B,mBAAwC;;QAClE,IAAM,oBAAoB,GAAG,IAAI,CAAC,wBAAwB,CAAC,IAAI,EAAE,CAAC;;YAClE,KAAwB,IAAA,yBAAA,SAAA,oBAAoB,CAAA,0DAAA,4FAAE;gBAAzC,IAAM,SAAS,iCAAA;gBAClB,IAAI,KAAK,GAAG,IAAI,CAAC,wBAAwB,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;gBACzD,IAAI,KAAK,KAAK,SAAS,EAAE;oBACvB,KAAK,GAAG,EAAE,CAAC;oBACX,IAAI,CAAC,wBAAwB,CAAC,GAAG,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;iBACrD;gBACD,KAAK,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;aACjC;;;;;;;;;IACH,CAAC;IAEO,mEAAiC,GAAzC,UAA0C,SAAgC;;QACxE,IAAI,MAAM,GAAG,IAAI,gBAAgB,EAAK,CAAC;QACvC,IAAM,cAAc,GAAG,IAAI,CAAC,wBAAwB,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QACpE,IAAI,CAAC,wBAAwB,CAAC,GAAG,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;QACjD,IAAI,cAAc,KAAK,SAAS,EAAE;YAChC,OAAO,MAAM,CAAC;SACf;;YACD,KAAiB,IAAA,mBAAA,SAAA,cAAc,CAAA,8CAAA,0EAAE;gBAA5B,IAAM,IAAE,2BAAA;gBACX,MAAM,GAAG,uBAAuB,CAAC,KAAK,CAAC,MAAM,EAAE,IAAE,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;aACtE;;;;;;;;;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAEM,6BAAK,GAAZ,UACE,IAAyB,EACzB,OAA4B,EAC5B,UAAyB;QAEzB,IAAM,MAAM,GAAG,IAAI,CAAC;QACpB,IAAM,QAAQ,GAAG,OAAO,CAAC,OAAO,EAAE,CAAC;QACnC,IAAI,IAAI,GAAG,QAAQ,CAAC,IAAI,EAAE,CAAC;QAC3B,OAAO,IAAI,CAAC,IAAI,KAAK,IAAI,EAAE;YACnB,IAAA,KAAA,OAAsB,IAAI,CAAC,KAAK,IAAA,EAA/B,GAAG,QAAA,EAAE,MAAM,QAAA,EAAE,IAAI,QAAc,CAAC;YACvC,IAAI,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,EAAE;gBACvB,IAAM,gBAAgB,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;gBAC7C,yDAAyD;gBACzD,oEAAoE;gBACpE,IAAM,YAAY,GAAG,UAAU,CAAC,KAAK,CAAC,gBAAiB,EAAE,MAAM,CAAC,CAAC;gBACjE,MAAM,CAAC,GAAG,CAAC,GAAG,EAAE,YAAY,EAAE,IAAI,CAAC,CAAC;aACrC;iBAAM;gBACL,MAAM,CAAC,GAAG,CAAC,GAAG,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;aAC/B;YAED,IAAI,GAAG,QAAQ,CAAC,IAAI,EAAE,CAAC;SACxB;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;;OAGG;IACI,0CAAkB,GAAzB,UACE,IAAyB,EACzB,OAA4B,EAC5B,kBAA0B;;;YAE1B,KAA0B,IAAA,KAAA,SAAA,IAAI,CAAC,IAAI,EAAE,CAAA,gBAAA,4BAAE;gBAA5B,IAAA,KAAA,mBAAW,EAAV,GAAG,QAAA,EAAE,IAAI,QAAA;gBACnB,IAAM,mBAAmB,GAAG,OAAO,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;gBACnD,mBAAmB,aAAnB,mBAAmB,uBAAnB,mBAAmB,CAAE,YAAY,CAAC,kBAAkB,CAAC,CAAC;aACvD;;;;;;;;;QACD,OAAO,OAAO,CAAC;IACjB,CAAC;IACH,8BAAC;AAAD,CAAC,AAxKD,IAwKC;;AAED,mFAAmF;AACnF,SAAS,kCAAkC,CACzC,GAAwB;IAExB,OAAO,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,CAAuC,CAAC;AACzE,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { HrTime } from '@opentelemetry/api';\nimport {\n  Accumulation,\n  AccumulationRecord,\n  Aggregator,\n} from '../aggregator/types';\nimport { MetricData } from '../export/MetricData';\nimport { InstrumentDescriptor } from '../InstrumentDescriptor';\nimport { AggregationTemporality } from '../export/AggregationTemporality';\nimport { Maybe } from '../utils';\nimport { MetricCollectorHandle } from './MetricCollector';\nimport { AttributeHashMap } from './HashMap';\n\n/**\n * Remembers what was presented to a specific exporter.\n */\ninterface LastReportedHistory<T extends Maybe<Accumulation>> {\n  /**\n   * The last accumulation of metric data.\n   */\n  accumulations: AttributeHashMap<T>;\n  /**\n   * The timestamp the data was reported.\n   */\n  collectionTime: HrTime;\n  /**\n   * The AggregationTemporality used to aggregate reports.\n   */\n  aggregationTemporality: AggregationTemporality;\n}\n\n/**\n * Internal interface.\n *\n * Provides unique reporting for each collector. Allows synchronous collection\n * of metrics and reports given temporality values.\n */\nexport class TemporalMetricProcessor<T extends Maybe<Accumulation>> {\n  private _unreportedAccumulations = new Map<\n    MetricCollectorHandle,\n    AttributeHashMap<T>[]\n  >();\n  private _reportHistory = new Map<\n    MetricCollectorHandle,\n    LastReportedHistory<T>\n  >();\n\n  constructor(\n    private _aggregator: Aggregator<T>,\n    collectorHandles: MetricCollectorHandle[]\n  ) {\n    collectorHandles.forEach(handle => {\n      this._unreportedAccumulations.set(handle, []);\n    });\n  }\n\n  /**\n   * Builds the {@link MetricData} streams to report against a specific MetricCollector.\n   * @param collector The information of the MetricCollector.\n   * @param collectors The registered collectors.\n   * @param instrumentDescriptor The instrumentation descriptor that these metrics generated with.\n   * @param currentAccumulations The current accumulation of metric data from instruments.\n   * @param collectionTime The current collection timestamp.\n   * @returns The {@link MetricData} points or `null`.\n   */\n  buildMetrics(\n    collector: MetricCollectorHandle,\n    instrumentDescriptor: InstrumentDescriptor,\n    currentAccumulations: AttributeHashMap<T>,\n    collectionTime: HrTime\n  ): Maybe<MetricData> {\n    this._stashAccumulations(currentAccumulations);\n    const unreportedAccumulations =\n      this._getMergedUnreportedAccumulations(collector);\n\n    let result = unreportedAccumulations;\n    let aggregationTemporality: AggregationTemporality;\n    // Check our last report time.\n    if (this._reportHistory.has(collector)) {\n      // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n      const last = this._reportHistory.get(collector)!;\n      const lastCollectionTime = last.collectionTime;\n      aggregationTemporality = last.aggregationTemporality;\n\n      // Use aggregation temporality + instrument to determine if we do a merge or a diff of\n      // previous. We have the following four scenarios:\n      // 1. Cumulative Aggregation (temporality) + Delta recording (sync instrument).\n      //    Here we merge with our last record to get a cumulative aggregation.\n      // 2. Cumulative Aggregation + Cumulative recording (async instrument).\n      //    Cumulative records are converted to delta recording with DeltaMetricProcessor.\n      //    Here we merge with our last record to get a cumulative aggregation.\n      // 3. Delta Aggregation + Delta recording\n      //    Calibrate the startTime of metric streams to be the reader's lastCollectionTime.\n      // 4. Delta Aggregation + Cumulative recording.\n      //    Cumulative records are converted to delta recording with DeltaMetricProcessor.\n      //    Calibrate the startTime of metric streams to be the reader's lastCollectionTime.\n      if (aggregationTemporality === AggregationTemporality.CUMULATIVE) {\n        // We need to make sure the current delta recording gets merged into the previous cumulative\n        // for the next cumulative recording.\n        result = TemporalMetricProcessor.merge(\n          last.accumulations,\n          unreportedAccumulations,\n          this._aggregator\n        );\n      } else {\n        result = TemporalMetricProcessor.calibrateStartTime(\n          last.accumulations,\n          unreportedAccumulations,\n          lastCollectionTime\n        );\n      }\n    } else {\n      // Call into user code to select aggregation temporality for the instrument.\n      aggregationTemporality = collector.selectAggregationTemporality(\n        instrumentDescriptor.type\n      );\n    }\n\n    // Update last reported (cumulative) accumulation.\n    this._reportHistory.set(collector, {\n      accumulations: result,\n      collectionTime,\n      aggregationTemporality,\n    });\n\n    const accumulationRecords = AttributesMapToAccumulationRecords(result);\n\n    // do not convert to metric data if there is nothing to convert.\n    if (accumulationRecords.length === 0) {\n      return undefined;\n    }\n\n    return this._aggregator.toMetricData(\n      instrumentDescriptor,\n      aggregationTemporality,\n      accumulationRecords,\n      /* endTime */ collectionTime\n    );\n  }\n\n  private _stashAccumulations(currentAccumulation: AttributeHashMap<T>) {\n    const registeredCollectors = this._unreportedAccumulations.keys();\n    for (const collector of registeredCollectors) {\n      let stash = this._unreportedAccumulations.get(collector);\n      if (stash === undefined) {\n        stash = [];\n        this._unreportedAccumulations.set(collector, stash);\n      }\n      stash.push(currentAccumulation);\n    }\n  }\n\n  private _getMergedUnreportedAccumulations(collector: MetricCollectorHandle) {\n    let result = new AttributeHashMap<T>();\n    const unreportedList = this._unreportedAccumulations.get(collector);\n    this._unreportedAccumulations.set(collector, []);\n    if (unreportedList === undefined) {\n      return result;\n    }\n    for (const it of unreportedList) {\n      result = TemporalMetricProcessor.merge(result, it, this._aggregator);\n    }\n    return result;\n  }\n\n  static merge<T extends Maybe<Accumulation>>(\n    last: AttributeHashMap<T>,\n    current: AttributeHashMap<T>,\n    aggregator: Aggregator<T>\n  ) {\n    const result = last;\n    const iterator = current.entries();\n    let next = iterator.next();\n    while (next.done !== true) {\n      const [key, record, hash] = next.value;\n      if (last.has(key, hash)) {\n        const lastAccumulation = last.get(key, hash);\n        // last.has() returned true, lastAccumulation is present.\n        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n        const accumulation = aggregator.merge(lastAccumulation!, record);\n        result.set(key, accumulation, hash);\n      } else {\n        result.set(key, record, hash);\n      }\n\n      next = iterator.next();\n    }\n    return result;\n  }\n\n  /**\n   * Calibrate the reported metric streams' startTime to lastCollectionTime. Leaves\n   * the new stream to be the initial observation time unchanged.\n   */\n  static calibrateStartTime<T extends Maybe<Accumulation>>(\n    last: AttributeHashMap<T>,\n    current: AttributeHashMap<T>,\n    lastCollectionTime: HrTime\n  ) {\n    for (const [key, hash] of last.keys()) {\n      const currentAccumulation = current.get(key, hash);\n      currentAccumulation?.setStartTime(lastCollectionTime);\n    }\n    return current;\n  }\n}\n\n// TypeScript complains about converting 3 elements tuple to AccumulationRecord<T>.\nfunction AttributesMapToAccumulationRecords<T>(\n  map: AttributeHashMap<T>\n): AccumulationRecord<T>[] {\n  return Array.from(map.entries()) as unknown as AccumulationRecord<T>[];\n}\n"]}