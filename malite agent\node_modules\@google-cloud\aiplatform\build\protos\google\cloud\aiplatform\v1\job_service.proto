// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.aiplatform.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/aiplatform/v1/batch_prediction_job.proto";
import "google/cloud/aiplatform/v1/custom_job.proto";
import "google/cloud/aiplatform/v1/data_labeling_job.proto";
import "google/cloud/aiplatform/v1/hyperparameter_tuning_job.proto";
import "google/cloud/aiplatform/v1/model_deployment_monitoring_job.proto";
import "google/cloud/aiplatform/v1/nas_job.proto";
import "google/cloud/aiplatform/v1/operation.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.AIPlatform.V1";
option go_package = "cloud.google.com/go/aiplatform/apiv1/aiplatformpb;aiplatformpb";
option java_multiple_files = true;
option java_outer_classname = "JobServiceProto";
option java_package = "com.google.cloud.aiplatform.v1";
option php_namespace = "Google\\Cloud\\AIPlatform\\V1";
option ruby_package = "Google::Cloud::AIPlatform::V1";

// A service for creating and managing Vertex AI's jobs.
service JobService {
  option (google.api.default_host) = "aiplatform.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform,"
      "https://www.googleapis.com/auth/cloud-platform.read-only";

  // Creates a CustomJob. A created CustomJob right away
  // will be attempted to be run.
  rpc CreateCustomJob(CreateCustomJobRequest) returns (CustomJob) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/customJobs"
      body: "custom_job"
    };
    option (google.api.method_signature) = "parent,custom_job";
  }

  // Gets a CustomJob.
  rpc GetCustomJob(GetCustomJobRequest) returns (CustomJob) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/customJobs/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists CustomJobs in a Location.
  rpc ListCustomJobs(ListCustomJobsRequest) returns (ListCustomJobsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/customJobs"
    };
    option (google.api.method_signature) = "parent";
  }

  // Deletes a CustomJob.
  rpc DeleteCustomJob(DeleteCustomJobRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/customJobs/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "DeleteOperationMetadata"
    };
  }

  // Cancels a CustomJob.
  // Starts asynchronous cancellation on the CustomJob. The server
  // makes a best effort to cancel the job, but success is not
  // guaranteed. Clients can use
  // [JobService.GetCustomJob][google.cloud.aiplatform.v1.JobService.GetCustomJob]
  // or other methods to check whether the cancellation succeeded or whether the
  // job completed despite cancellation. On successful cancellation,
  // the CustomJob is not deleted; instead it becomes a job with
  // a [CustomJob.error][google.cloud.aiplatform.v1.CustomJob.error] value with
  // a [google.rpc.Status.code][google.rpc.Status.code] of 1, corresponding to
  // `Code.CANCELLED`, and
  // [CustomJob.state][google.cloud.aiplatform.v1.CustomJob.state] is set to
  // `CANCELLED`.
  rpc CancelCustomJob(CancelCustomJobRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/customJobs/*}:cancel"
      body: "*"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a DataLabelingJob.
  rpc CreateDataLabelingJob(CreateDataLabelingJobRequest)
      returns (DataLabelingJob) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/dataLabelingJobs"
      body: "data_labeling_job"
    };
    option (google.api.method_signature) = "parent,data_labeling_job";
  }

  // Gets a DataLabelingJob.
  rpc GetDataLabelingJob(GetDataLabelingJobRequest) returns (DataLabelingJob) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/dataLabelingJobs/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists DataLabelingJobs in a Location.
  rpc ListDataLabelingJobs(ListDataLabelingJobsRequest)
      returns (ListDataLabelingJobsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/dataLabelingJobs"
    };
    option (google.api.method_signature) = "parent";
  }

  // Deletes a DataLabelingJob.
  rpc DeleteDataLabelingJob(DeleteDataLabelingJobRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/dataLabelingJobs/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "DeleteOperationMetadata"
    };
  }

  // Cancels a DataLabelingJob. Success of cancellation is not guaranteed.
  rpc CancelDataLabelingJob(CancelDataLabelingJobRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/dataLabelingJobs/*}:cancel"
      body: "*"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a HyperparameterTuningJob
  rpc CreateHyperparameterTuningJob(CreateHyperparameterTuningJobRequest)
      returns (HyperparameterTuningJob) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/hyperparameterTuningJobs"
      body: "hyperparameter_tuning_job"
    };
    option (google.api.method_signature) = "parent,hyperparameter_tuning_job";
  }

  // Gets a HyperparameterTuningJob
  rpc GetHyperparameterTuningJob(GetHyperparameterTuningJobRequest)
      returns (HyperparameterTuningJob) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/hyperparameterTuningJobs/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists HyperparameterTuningJobs in a Location.
  rpc ListHyperparameterTuningJobs(ListHyperparameterTuningJobsRequest)
      returns (ListHyperparameterTuningJobsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/hyperparameterTuningJobs"
    };
    option (google.api.method_signature) = "parent";
  }

  // Deletes a HyperparameterTuningJob.
  rpc DeleteHyperparameterTuningJob(DeleteHyperparameterTuningJobRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/hyperparameterTuningJobs/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "DeleteOperationMetadata"
    };
  }

  // Cancels a HyperparameterTuningJob.
  // Starts asynchronous cancellation on the HyperparameterTuningJob. The server
  // makes a best effort to cancel the job, but success is not
  // guaranteed. Clients can use
  // [JobService.GetHyperparameterTuningJob][google.cloud.aiplatform.v1.JobService.GetHyperparameterTuningJob]
  // or other methods to check whether the cancellation succeeded or whether the
  // job completed despite cancellation. On successful cancellation,
  // the HyperparameterTuningJob is not deleted; instead it becomes a job with
  // a
  // [HyperparameterTuningJob.error][google.cloud.aiplatform.v1.HyperparameterTuningJob.error]
  // value with a [google.rpc.Status.code][google.rpc.Status.code] of 1,
  // corresponding to `Code.CANCELLED`, and
  // [HyperparameterTuningJob.state][google.cloud.aiplatform.v1.HyperparameterTuningJob.state]
  // is set to `CANCELLED`.
  rpc CancelHyperparameterTuningJob(CancelHyperparameterTuningJobRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/hyperparameterTuningJobs/*}:cancel"
      body: "*"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a NasJob
  rpc CreateNasJob(CreateNasJobRequest) returns (NasJob) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/nasJobs"
      body: "nas_job"
    };
    option (google.api.method_signature) = "parent,nas_job";
  }

  // Gets a NasJob
  rpc GetNasJob(GetNasJobRequest) returns (NasJob) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/nasJobs/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists NasJobs in a Location.
  rpc ListNasJobs(ListNasJobsRequest) returns (ListNasJobsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/nasJobs"
    };
    option (google.api.method_signature) = "parent";
  }

  // Deletes a NasJob.
  rpc DeleteNasJob(DeleteNasJobRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/nasJobs/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "DeleteOperationMetadata"
    };
  }

  // Cancels a NasJob.
  // Starts asynchronous cancellation on the NasJob. The server
  // makes a best effort to cancel the job, but success is not
  // guaranteed. Clients can use
  // [JobService.GetNasJob][google.cloud.aiplatform.v1.JobService.GetNasJob] or
  // other methods to check whether the cancellation succeeded or whether the
  // job completed despite cancellation. On successful cancellation,
  // the NasJob is not deleted; instead it becomes a job with
  // a [NasJob.error][google.cloud.aiplatform.v1.NasJob.error] value with a
  // [google.rpc.Status.code][google.rpc.Status.code] of 1, corresponding to
  // `Code.CANCELLED`, and
  // [NasJob.state][google.cloud.aiplatform.v1.NasJob.state] is set to
  // `CANCELLED`.
  rpc CancelNasJob(CancelNasJobRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/nasJobs/*}:cancel"
      body: "*"
    };
    option (google.api.method_signature) = "name";
  }

  // Gets a NasTrialDetail.
  rpc GetNasTrialDetail(GetNasTrialDetailRequest) returns (NasTrialDetail) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/nasJobs/*/nasTrialDetails/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // List top NasTrialDetails of a NasJob.
  rpc ListNasTrialDetails(ListNasTrialDetailsRequest)
      returns (ListNasTrialDetailsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*/nasJobs/*}/nasTrialDetails"
    };
    option (google.api.method_signature) = "parent";
  }

  // Creates a BatchPredictionJob. A BatchPredictionJob once created will
  // right away be attempted to start.
  rpc CreateBatchPredictionJob(CreateBatchPredictionJobRequest)
      returns (BatchPredictionJob) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/batchPredictionJobs"
      body: "batch_prediction_job"
    };
    option (google.api.method_signature) = "parent,batch_prediction_job";
  }

  // Gets a BatchPredictionJob
  rpc GetBatchPredictionJob(GetBatchPredictionJobRequest)
      returns (BatchPredictionJob) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/batchPredictionJobs/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists BatchPredictionJobs in a Location.
  rpc ListBatchPredictionJobs(ListBatchPredictionJobsRequest)
      returns (ListBatchPredictionJobsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/batchPredictionJobs"
    };
    option (google.api.method_signature) = "parent";
  }

  // Deletes a BatchPredictionJob. Can only be called on jobs that already
  // finished.
  rpc DeleteBatchPredictionJob(DeleteBatchPredictionJobRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/batchPredictionJobs/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "DeleteOperationMetadata"
    };
  }

  // Cancels a BatchPredictionJob.
  //
  // Starts asynchronous cancellation on the BatchPredictionJob. The server
  // makes the best effort to cancel the job, but success is not
  // guaranteed. Clients can use
  // [JobService.GetBatchPredictionJob][google.cloud.aiplatform.v1.JobService.GetBatchPredictionJob]
  // or other methods to check whether the cancellation succeeded or whether the
  // job completed despite cancellation. On a successful cancellation,
  // the BatchPredictionJob is not deleted;instead its
  // [BatchPredictionJob.state][google.cloud.aiplatform.v1.BatchPredictionJob.state]
  // is set to `CANCELLED`. Any files already outputted by the job are not
  // deleted.
  rpc CancelBatchPredictionJob(CancelBatchPredictionJobRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/batchPredictionJobs/*}:cancel"
      body: "*"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a ModelDeploymentMonitoringJob. It will run periodically on a
  // configured interval.
  rpc CreateModelDeploymentMonitoringJob(
      CreateModelDeploymentMonitoringJobRequest)
      returns (ModelDeploymentMonitoringJob) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/modelDeploymentMonitoringJobs"
      body: "model_deployment_monitoring_job"
    };
    option (google.api.method_signature) =
        "parent,model_deployment_monitoring_job";
  }

  // Searches Model Monitoring Statistics generated within a given time window.
  rpc SearchModelDeploymentMonitoringStatsAnomalies(
      SearchModelDeploymentMonitoringStatsAnomaliesRequest)
      returns (SearchModelDeploymentMonitoringStatsAnomaliesResponse) {
    option (google.api.http) = {
      post: "/v1/{model_deployment_monitoring_job=projects/*/locations/*/modelDeploymentMonitoringJobs/*}:searchModelDeploymentMonitoringStatsAnomalies"
      body: "*"
    };
    option (google.api.method_signature) =
        "model_deployment_monitoring_job,deployed_model_id";
  }

  // Gets a ModelDeploymentMonitoringJob.
  rpc GetModelDeploymentMonitoringJob(GetModelDeploymentMonitoringJobRequest)
      returns (ModelDeploymentMonitoringJob) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/modelDeploymentMonitoringJobs/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists ModelDeploymentMonitoringJobs in a Location.
  rpc ListModelDeploymentMonitoringJobs(
      ListModelDeploymentMonitoringJobsRequest)
      returns (ListModelDeploymentMonitoringJobsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/modelDeploymentMonitoringJobs"
    };
    option (google.api.method_signature) = "parent";
  }

  // Updates a ModelDeploymentMonitoringJob.
  rpc UpdateModelDeploymentMonitoringJob(
      UpdateModelDeploymentMonitoringJobRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{model_deployment_monitoring_job.name=projects/*/locations/*/modelDeploymentMonitoringJobs/*}"
      body: "model_deployment_monitoring_job"
    };
    option (google.api.method_signature) =
        "model_deployment_monitoring_job,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "ModelDeploymentMonitoringJob"
      metadata_type: "UpdateModelDeploymentMonitoringJobOperationMetadata"
    };
  }

  // Deletes a ModelDeploymentMonitoringJob.
  rpc DeleteModelDeploymentMonitoringJob(
      DeleteModelDeploymentMonitoringJobRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/modelDeploymentMonitoringJobs/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "DeleteOperationMetadata"
    };
  }

  // Pauses a ModelDeploymentMonitoringJob. If the job is running, the server
  // makes a best effort to cancel the job. Will mark
  // [ModelDeploymentMonitoringJob.state][google.cloud.aiplatform.v1.ModelDeploymentMonitoringJob.state]
  // to 'PAUSED'.
  rpc PauseModelDeploymentMonitoringJob(
      PauseModelDeploymentMonitoringJobRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/modelDeploymentMonitoringJobs/*}:pause"
      body: "*"
    };
    option (google.api.method_signature) = "name";
  }

  // Resumes a paused ModelDeploymentMonitoringJob. It will start to run from
  // next scheduled time. A deleted ModelDeploymentMonitoringJob can't be
  // resumed.
  rpc ResumeModelDeploymentMonitoringJob(
      ResumeModelDeploymentMonitoringJobRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/modelDeploymentMonitoringJobs/*}:resume"
      body: "*"
    };
    option (google.api.method_signature) = "name";
  }
}

// Request message for
// [JobService.CreateCustomJob][google.cloud.aiplatform.v1.JobService.CreateCustomJob].
message CreateCustomJobRequest {
  // Required. The resource name of the Location to create the CustomJob in.
  // Format: `projects/{project}/locations/{location}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Required. The CustomJob to create.
  CustomJob custom_job = 2 [(google.api.field_behavior) = REQUIRED];
}

// Request message for
// [JobService.GetCustomJob][google.cloud.aiplatform.v1.JobService.GetCustomJob].
message GetCustomJobRequest {
  // Required. The name of the CustomJob resource.
  // Format:
  // `projects/{project}/locations/{location}/customJobs/{custom_job}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/CustomJob"
    }
  ];
}

// Request message for
// [JobService.ListCustomJobs][google.cloud.aiplatform.v1.JobService.ListCustomJobs].
message ListCustomJobsRequest {
  // Required. The resource name of the Location to list the CustomJobs from.
  // Format: `projects/{project}/locations/{location}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // The standard list filter.
  //
  // Supported fields:
  //
  //   * `display_name` supports `=`, `!=` comparisons, and `:` wildcard.
  //   * `state` supports `=`, `!=` comparisons.
  //   * `create_time` supports `=`, `!=`,`<`, `<=`,`>`, `>=` comparisons.
  //     `create_time` must be in RFC 3339 format.
  //   * `labels` supports general map functions that is:
  //     `labels.key=value` - key:value equality
  //     `labels.key:* - key existence
  //
  // Some examples of using the filter are:
  //
  //   * `state="JOB_STATE_SUCCEEDED" AND display_name:"my_job_*"`
  //   * `state!="JOB_STATE_FAILED" OR display_name="my_job"`
  //   * `NOT display_name="my_job"`
  //   * `create_time>"2021-05-18T00:00:00Z"`
  //   * `labels.keyA=valueA`
  //   * `labels.keyB:*`
  string filter = 2;

  // The standard list page size.
  int32 page_size = 3;

  // The standard list page token.
  // Typically obtained via
  // [ListCustomJobsResponse.next_page_token][google.cloud.aiplatform.v1.ListCustomJobsResponse.next_page_token]
  // of the previous
  // [JobService.ListCustomJobs][google.cloud.aiplatform.v1.JobService.ListCustomJobs]
  // call.
  string page_token = 4;

  // Mask specifying which fields to read.
  google.protobuf.FieldMask read_mask = 5;
}

// Response message for
// [JobService.ListCustomJobs][google.cloud.aiplatform.v1.JobService.ListCustomJobs]
message ListCustomJobsResponse {
  // List of CustomJobs in the requested page.
  repeated CustomJob custom_jobs = 1;

  // A token to retrieve the next page of results.
  // Pass to
  // [ListCustomJobsRequest.page_token][google.cloud.aiplatform.v1.ListCustomJobsRequest.page_token]
  // to obtain that page.
  string next_page_token = 2;
}

// Request message for
// [JobService.DeleteCustomJob][google.cloud.aiplatform.v1.JobService.DeleteCustomJob].
message DeleteCustomJobRequest {
  // Required. The name of the CustomJob resource to be deleted.
  // Format:
  // `projects/{project}/locations/{location}/customJobs/{custom_job}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/CustomJob"
    }
  ];
}

// Request message for
// [JobService.CancelCustomJob][google.cloud.aiplatform.v1.JobService.CancelCustomJob].
message CancelCustomJobRequest {
  // Required. The name of the CustomJob to cancel.
  // Format:
  // `projects/{project}/locations/{location}/customJobs/{custom_job}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/CustomJob"
    }
  ];
}

// Request message for
// [JobService.CreateDataLabelingJob][google.cloud.aiplatform.v1.JobService.CreateDataLabelingJob].
message CreateDataLabelingJobRequest {
  // Required. The parent of the DataLabelingJob.
  // Format: `projects/{project}/locations/{location}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Required. The DataLabelingJob to create.
  DataLabelingJob data_labeling_job = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Request message for
// [JobService.GetDataLabelingJob][google.cloud.aiplatform.v1.JobService.GetDataLabelingJob].
message GetDataLabelingJobRequest {
  // Required. The name of the DataLabelingJob.
  // Format:
  // `projects/{project}/locations/{location}/dataLabelingJobs/{data_labeling_job}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/DataLabelingJob"
    }
  ];
}

// Request message for
// [JobService.ListDataLabelingJobs][google.cloud.aiplatform.v1.JobService.ListDataLabelingJobs].
message ListDataLabelingJobsRequest {
  // Required. The parent of the DataLabelingJob.
  // Format: `projects/{project}/locations/{location}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // The standard list filter.
  //
  // Supported fields:
  //
  //   * `display_name` supports `=`, `!=` comparisons, and `:` wildcard.
  //   * `state` supports `=`, `!=` comparisons.
  //   * `create_time` supports `=`, `!=`,`<`, `<=`,`>`, `>=` comparisons.
  //     `create_time` must be in RFC 3339 format.
  //   * `labels` supports general map functions that is:
  //     `labels.key=value` - key:value equality
  //     `labels.key:* - key existence
  //
  // Some examples of using the filter are:
  //
  //   * `state="JOB_STATE_SUCCEEDED" AND display_name:"my_job_*"`
  //   * `state!="JOB_STATE_FAILED" OR display_name="my_job"`
  //   * `NOT display_name="my_job"`
  //   * `create_time>"2021-05-18T00:00:00Z"`
  //   * `labels.keyA=valueA`
  //   * `labels.keyB:*`
  string filter = 2;

  // The standard list page size.
  int32 page_size = 3;

  // The standard list page token.
  string page_token = 4;

  // Mask specifying which fields to read. FieldMask represents a set of
  // symbolic field paths. For example, the mask can be `paths: "name"`. The
  // "name" here is a field in DataLabelingJob.
  // If this field is not set, all fields of the DataLabelingJob are returned.
  google.protobuf.FieldMask read_mask = 5;

  // A comma-separated list of fields to order by, sorted in ascending order by
  // default.
  // Use `desc` after a field name for descending.
  string order_by = 6;
}

// Response message for
// [JobService.ListDataLabelingJobs][google.cloud.aiplatform.v1.JobService.ListDataLabelingJobs].
message ListDataLabelingJobsResponse {
  // A list of DataLabelingJobs that matches the specified filter in the
  // request.
  repeated DataLabelingJob data_labeling_jobs = 1;

  // The standard List next-page token.
  string next_page_token = 2;
}

// Request message for
// [JobService.DeleteDataLabelingJob][google.cloud.aiplatform.v1.JobService.DeleteDataLabelingJob].
message DeleteDataLabelingJobRequest {
  // Required. The name of the DataLabelingJob to be deleted.
  // Format:
  // `projects/{project}/locations/{location}/dataLabelingJobs/{data_labeling_job}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/DataLabelingJob"
    }
  ];
}

// Request message for
// [JobService.CancelDataLabelingJob][google.cloud.aiplatform.v1.JobService.CancelDataLabelingJob].
message CancelDataLabelingJobRequest {
  // Required. The name of the DataLabelingJob.
  // Format:
  // `projects/{project}/locations/{location}/dataLabelingJobs/{data_labeling_job}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/DataLabelingJob"
    }
  ];
}

// Request message for
// [JobService.CreateHyperparameterTuningJob][google.cloud.aiplatform.v1.JobService.CreateHyperparameterTuningJob].
message CreateHyperparameterTuningJobRequest {
  // Required. The resource name of the Location to create the
  // HyperparameterTuningJob in. Format:
  // `projects/{project}/locations/{location}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Required. The HyperparameterTuningJob to create.
  HyperparameterTuningJob hyperparameter_tuning_job = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Request message for
// [JobService.GetHyperparameterTuningJob][google.cloud.aiplatform.v1.JobService.GetHyperparameterTuningJob].
message GetHyperparameterTuningJobRequest {
  // Required. The name of the HyperparameterTuningJob resource.
  // Format:
  // `projects/{project}/locations/{location}/hyperparameterTuningJobs/{hyperparameter_tuning_job}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/HyperparameterTuningJob"
    }
  ];
}

// Request message for
// [JobService.ListHyperparameterTuningJobs][google.cloud.aiplatform.v1.JobService.ListHyperparameterTuningJobs].
message ListHyperparameterTuningJobsRequest {
  // Required. The resource name of the Location to list the
  // HyperparameterTuningJobs from. Format:
  // `projects/{project}/locations/{location}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // The standard list filter.
  //
  // Supported fields:
  //
  //   * `display_name` supports `=`, `!=` comparisons, and `:` wildcard.
  //   * `state` supports `=`, `!=` comparisons.
  //   * `create_time` supports `=`, `!=`,`<`, `<=`,`>`, `>=` comparisons.
  //     `create_time` must be in RFC 3339 format.
  //   * `labels` supports general map functions that is:
  //     `labels.key=value` - key:value equality
  //     `labels.key:* - key existence
  //
  // Some examples of using the filter are:
  //
  //   * `state="JOB_STATE_SUCCEEDED" AND display_name:"my_job_*"`
  //   * `state!="JOB_STATE_FAILED" OR display_name="my_job"`
  //   * `NOT display_name="my_job"`
  //   * `create_time>"2021-05-18T00:00:00Z"`
  //   * `labels.keyA=valueA`
  //   * `labels.keyB:*`
  string filter = 2;

  // The standard list page size.
  int32 page_size = 3;

  // The standard list page token.
  // Typically obtained via
  // [ListHyperparameterTuningJobsResponse.next_page_token][google.cloud.aiplatform.v1.ListHyperparameterTuningJobsResponse.next_page_token]
  // of the previous
  // [JobService.ListHyperparameterTuningJobs][google.cloud.aiplatform.v1.JobService.ListHyperparameterTuningJobs]
  // call.
  string page_token = 4;

  // Mask specifying which fields to read.
  google.protobuf.FieldMask read_mask = 5;
}

// Response message for
// [JobService.ListHyperparameterTuningJobs][google.cloud.aiplatform.v1.JobService.ListHyperparameterTuningJobs]
message ListHyperparameterTuningJobsResponse {
  // List of HyperparameterTuningJobs in the requested page.
  // [HyperparameterTuningJob.trials][google.cloud.aiplatform.v1.HyperparameterTuningJob.trials]
  // of the jobs will be not be returned.
  repeated HyperparameterTuningJob hyperparameter_tuning_jobs = 1;

  // A token to retrieve the next page of results.
  // Pass to
  // [ListHyperparameterTuningJobsRequest.page_token][google.cloud.aiplatform.v1.ListHyperparameterTuningJobsRequest.page_token]
  // to obtain that page.
  string next_page_token = 2;
}

// Request message for
// [JobService.DeleteHyperparameterTuningJob][google.cloud.aiplatform.v1.JobService.DeleteHyperparameterTuningJob].
message DeleteHyperparameterTuningJobRequest {
  // Required. The name of the HyperparameterTuningJob resource to be deleted.
  // Format:
  // `projects/{project}/locations/{location}/hyperparameterTuningJobs/{hyperparameter_tuning_job}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/HyperparameterTuningJob"
    }
  ];
}

// Request message for
// [JobService.CancelHyperparameterTuningJob][google.cloud.aiplatform.v1.JobService.CancelHyperparameterTuningJob].
message CancelHyperparameterTuningJobRequest {
  // Required. The name of the HyperparameterTuningJob to cancel.
  // Format:
  // `projects/{project}/locations/{location}/hyperparameterTuningJobs/{hyperparameter_tuning_job}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/HyperparameterTuningJob"
    }
  ];
}

// Request message for
// [JobService.CreateNasJob][google.cloud.aiplatform.v1.JobService.CreateNasJob].
message CreateNasJobRequest {
  // Required. The resource name of the Location to create the NasJob in.
  // Format: `projects/{project}/locations/{location}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Required. The NasJob to create.
  NasJob nas_job = 2 [(google.api.field_behavior) = REQUIRED];
}

// Request message for
// [JobService.GetNasJob][google.cloud.aiplatform.v1.JobService.GetNasJob].
message GetNasJobRequest {
  // Required. The name of the NasJob resource.
  // Format:
  // `projects/{project}/locations/{location}/nasJobs/{nas_job}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/NasJob"
    }
  ];
}

// Request message for
// [JobService.ListNasJobs][google.cloud.aiplatform.v1.JobService.ListNasJobs].
message ListNasJobsRequest {
  // Required. The resource name of the Location to list the NasJobs
  // from. Format: `projects/{project}/locations/{location}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // The standard list filter.
  //
  // Supported fields:
  //
  //   * `display_name` supports `=`, `!=` comparisons, and `:` wildcard.
  //   * `state` supports `=`, `!=` comparisons.
  //   * `create_time` supports `=`, `!=`,`<`, `<=`,`>`, `>=` comparisons.
  //     `create_time` must be in RFC 3339 format.
  //   * `labels` supports general map functions that is:
  //     `labels.key=value` - key:value equality
  //     `labels.key:* - key existence
  //
  // Some examples of using the filter are:
  //
  //   * `state="JOB_STATE_SUCCEEDED" AND display_name:"my_job_*"`
  //   * `state!="JOB_STATE_FAILED" OR display_name="my_job"`
  //   * `NOT display_name="my_job"`
  //   * `create_time>"2021-05-18T00:00:00Z"`
  //   * `labels.keyA=valueA`
  //   * `labels.keyB:*`
  string filter = 2;

  // The standard list page size.
  int32 page_size = 3;

  // The standard list page token.
  // Typically obtained via
  // [ListNasJobsResponse.next_page_token][google.cloud.aiplatform.v1.ListNasJobsResponse.next_page_token]
  // of the previous
  // [JobService.ListNasJobs][google.cloud.aiplatform.v1.JobService.ListNasJobs]
  // call.
  string page_token = 4;

  // Mask specifying which fields to read.
  google.protobuf.FieldMask read_mask = 5;
}

// Response message for
// [JobService.ListNasJobs][google.cloud.aiplatform.v1.JobService.ListNasJobs]
message ListNasJobsResponse {
  // List of NasJobs in the requested page.
  // [NasJob.nas_job_output][google.cloud.aiplatform.v1.NasJob.nas_job_output]
  // of the jobs will not be returned.
  repeated NasJob nas_jobs = 1;

  // A token to retrieve the next page of results.
  // Pass to
  // [ListNasJobsRequest.page_token][google.cloud.aiplatform.v1.ListNasJobsRequest.page_token]
  // to obtain that page.
  string next_page_token = 2;
}

// Request message for
// [JobService.DeleteNasJob][google.cloud.aiplatform.v1.JobService.DeleteNasJob].
message DeleteNasJobRequest {
  // Required. The name of the NasJob resource to be deleted.
  // Format:
  // `projects/{project}/locations/{location}/nasJobs/{nas_job}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/NasJob"
    }
  ];
}

// Request message for
// [JobService.CancelNasJob][google.cloud.aiplatform.v1.JobService.CancelNasJob].
message CancelNasJobRequest {
  // Required. The name of the NasJob to cancel.
  // Format:
  // `projects/{project}/locations/{location}/nasJobs/{nas_job}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/NasJob"
    }
  ];
}

// Request message for
// [JobService.GetNasTrialDetail][google.cloud.aiplatform.v1.JobService.GetNasTrialDetail].
message GetNasTrialDetailRequest {
  // Required. The name of the NasTrialDetail resource.
  // Format:
  // `projects/{project}/locations/{location}/nasJobs/{nas_job}/nasTrialDetails/{nas_trial_detail}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/NasTrialDetail"
    }
  ];
}

// Request message for
// [JobService.ListNasTrialDetails][google.cloud.aiplatform.v1.JobService.ListNasTrialDetails].
message ListNasTrialDetailsRequest {
  // Required. The name of the NasJob resource.
  // Format:
  // `projects/{project}/locations/{location}/nasJobs/{nas_job}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/NasJob"
    }
  ];

  // The standard list page size.
  int32 page_size = 2;

  // The standard list page token.
  // Typically obtained via
  // [ListNasTrialDetailsResponse.next_page_token][google.cloud.aiplatform.v1.ListNasTrialDetailsResponse.next_page_token]
  // of the previous
  // [JobService.ListNasTrialDetails][google.cloud.aiplatform.v1.JobService.ListNasTrialDetails]
  // call.
  string page_token = 3;
}

// Response message for
// [JobService.ListNasTrialDetails][google.cloud.aiplatform.v1.JobService.ListNasTrialDetails]
message ListNasTrialDetailsResponse {
  // List of top NasTrials in the requested page.
  repeated NasTrialDetail nas_trial_details = 1;

  // A token to retrieve the next page of results.
  // Pass to
  // [ListNasTrialDetailsRequest.page_token][google.cloud.aiplatform.v1.ListNasTrialDetailsRequest.page_token]
  // to obtain that page.
  string next_page_token = 2;
}

// Request message for
// [JobService.CreateBatchPredictionJob][google.cloud.aiplatform.v1.JobService.CreateBatchPredictionJob].
message CreateBatchPredictionJobRequest {
  // Required. The resource name of the Location to create the
  // BatchPredictionJob in. Format: `projects/{project}/locations/{location}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Required. The BatchPredictionJob to create.
  BatchPredictionJob batch_prediction_job = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Request message for
// [JobService.GetBatchPredictionJob][google.cloud.aiplatform.v1.JobService.GetBatchPredictionJob].
message GetBatchPredictionJobRequest {
  // Required. The name of the BatchPredictionJob resource.
  // Format:
  // `projects/{project}/locations/{location}/batchPredictionJobs/{batch_prediction_job}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/BatchPredictionJob"
    }
  ];
}

// Request message for
// [JobService.ListBatchPredictionJobs][google.cloud.aiplatform.v1.JobService.ListBatchPredictionJobs].
message ListBatchPredictionJobsRequest {
  // Required. The resource name of the Location to list the BatchPredictionJobs
  // from. Format: `projects/{project}/locations/{location}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // The standard list filter.
  //
  // Supported fields:
  //
  //   * `display_name` supports `=`, `!=` comparisons, and `:` wildcard.
  //   * `model_display_name` supports `=`, `!=` comparisons.
  //   * `state` supports `=`, `!=` comparisons.
  //   * `create_time` supports `=`, `!=`,`<`, `<=`,`>`, `>=` comparisons.
  //     `create_time` must be in RFC 3339 format.
  //   * `labels` supports general map functions that is:
  //     `labels.key=value` - key:value equality
  //     `labels.key:* - key existence
  //
  // Some examples of using the filter are:
  //
  //   * `state="JOB_STATE_SUCCEEDED" AND display_name:"my_job_*"`
  //   * `state!="JOB_STATE_FAILED" OR display_name="my_job"`
  //   * `NOT display_name="my_job"`
  //   * `create_time>"2021-05-18T00:00:00Z"`
  //   * `labels.keyA=valueA`
  //   * `labels.keyB:*`
  string filter = 2;

  // The standard list page size.
  int32 page_size = 3;

  // The standard list page token.
  // Typically obtained via
  // [ListBatchPredictionJobsResponse.next_page_token][google.cloud.aiplatform.v1.ListBatchPredictionJobsResponse.next_page_token]
  // of the previous
  // [JobService.ListBatchPredictionJobs][google.cloud.aiplatform.v1.JobService.ListBatchPredictionJobs]
  // call.
  string page_token = 4;

  // Mask specifying which fields to read.
  google.protobuf.FieldMask read_mask = 5;
}

// Response message for
// [JobService.ListBatchPredictionJobs][google.cloud.aiplatform.v1.JobService.ListBatchPredictionJobs]
message ListBatchPredictionJobsResponse {
  // List of BatchPredictionJobs in the requested page.
  repeated BatchPredictionJob batch_prediction_jobs = 1;

  // A token to retrieve the next page of results.
  // Pass to
  // [ListBatchPredictionJobsRequest.page_token][google.cloud.aiplatform.v1.ListBatchPredictionJobsRequest.page_token]
  // to obtain that page.
  string next_page_token = 2;
}

// Request message for
// [JobService.DeleteBatchPredictionJob][google.cloud.aiplatform.v1.JobService.DeleteBatchPredictionJob].
message DeleteBatchPredictionJobRequest {
  // Required. The name of the BatchPredictionJob resource to be deleted.
  // Format:
  // `projects/{project}/locations/{location}/batchPredictionJobs/{batch_prediction_job}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/BatchPredictionJob"
    }
  ];
}

// Request message for
// [JobService.CancelBatchPredictionJob][google.cloud.aiplatform.v1.JobService.CancelBatchPredictionJob].
message CancelBatchPredictionJobRequest {
  // Required. The name of the BatchPredictionJob to cancel.
  // Format:
  // `projects/{project}/locations/{location}/batchPredictionJobs/{batch_prediction_job}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/BatchPredictionJob"
    }
  ];
}

// Request message for
// [JobService.CreateModelDeploymentMonitoringJob][google.cloud.aiplatform.v1.JobService.CreateModelDeploymentMonitoringJob].
message CreateModelDeploymentMonitoringJobRequest {
  // Required. The parent of the ModelDeploymentMonitoringJob.
  // Format: `projects/{project}/locations/{location}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Required. The ModelDeploymentMonitoringJob to create
  ModelDeploymentMonitoringJob model_deployment_monitoring_job = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Request message for
// [JobService.SearchModelDeploymentMonitoringStatsAnomalies][google.cloud.aiplatform.v1.JobService.SearchModelDeploymentMonitoringStatsAnomalies].
message SearchModelDeploymentMonitoringStatsAnomaliesRequest {
  // Stats requested for specific objective.
  message StatsAnomaliesObjective {
    ModelDeploymentMonitoringObjectiveType type = 1;

    // If set, all attribution scores between
    // [SearchModelDeploymentMonitoringStatsAnomaliesRequest.start_time][google.cloud.aiplatform.v1.SearchModelDeploymentMonitoringStatsAnomaliesRequest.start_time]
    // and
    // [SearchModelDeploymentMonitoringStatsAnomaliesRequest.end_time][google.cloud.aiplatform.v1.SearchModelDeploymentMonitoringStatsAnomaliesRequest.end_time]
    // are fetched, and page token doesn't take effect in this case. Only used
    // to retrieve attribution score for the top Features which has the highest
    // attribution score in the latest monitoring run.
    int32 top_feature_count = 4;
  }

  // Required. ModelDeploymentMonitoring Job resource name.
  // Format:
  // `projects/{project}/locations/{location}/modelDeploymentMonitoringJobs/{model_deployment_monitoring_job}`
  string model_deployment_monitoring_job = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/ModelDeploymentMonitoringJob"
    }
  ];

  // Required. The DeployedModel ID of the
  // [ModelDeploymentMonitoringObjectiveConfig.deployed_model_id].
  string deployed_model_id = 2 [(google.api.field_behavior) = REQUIRED];

  // The feature display name. If specified, only return the stats belonging to
  // this feature. Format:
  // [ModelMonitoringStatsAnomalies.FeatureHistoricStatsAnomalies.feature_display_name][google.cloud.aiplatform.v1.ModelMonitoringStatsAnomalies.FeatureHistoricStatsAnomalies.feature_display_name],
  // example: "user_destination".
  string feature_display_name = 3;

  // Required. Objectives of the stats to retrieve.
  repeated StatsAnomaliesObjective objectives = 4
      [(google.api.field_behavior) = REQUIRED];

  // The standard list page size.
  int32 page_size = 5;

  // A page token received from a previous
  // [JobService.SearchModelDeploymentMonitoringStatsAnomalies][google.cloud.aiplatform.v1.JobService.SearchModelDeploymentMonitoringStatsAnomalies]
  // call.
  string page_token = 6;

  // The earliest timestamp of stats being generated.
  // If not set, indicates fetching stats till the earliest possible one.
  google.protobuf.Timestamp start_time = 7;

  // The latest timestamp of stats being generated.
  // If not set, indicates feching stats till the latest possible one.
  google.protobuf.Timestamp end_time = 8;
}

// Response message for
// [JobService.SearchModelDeploymentMonitoringStatsAnomalies][google.cloud.aiplatform.v1.JobService.SearchModelDeploymentMonitoringStatsAnomalies].
message SearchModelDeploymentMonitoringStatsAnomaliesResponse {
  // Stats retrieved for requested objectives.
  // There are at most 1000
  // [ModelMonitoringStatsAnomalies.FeatureHistoricStatsAnomalies.prediction_stats][google.cloud.aiplatform.v1.ModelMonitoringStatsAnomalies.FeatureHistoricStatsAnomalies.prediction_stats]
  // in the response.
  repeated ModelMonitoringStatsAnomalies monitoring_stats = 1;

  // The page token that can be used by the next
  // [JobService.SearchModelDeploymentMonitoringStatsAnomalies][google.cloud.aiplatform.v1.JobService.SearchModelDeploymentMonitoringStatsAnomalies]
  // call.
  string next_page_token = 2;
}

// Request message for
// [JobService.GetModelDeploymentMonitoringJob][google.cloud.aiplatform.v1.JobService.GetModelDeploymentMonitoringJob].
message GetModelDeploymentMonitoringJobRequest {
  // Required. The resource name of the ModelDeploymentMonitoringJob.
  // Format:
  // `projects/{project}/locations/{location}/modelDeploymentMonitoringJobs/{model_deployment_monitoring_job}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/ModelDeploymentMonitoringJob"
    }
  ];
}

// Request message for
// [JobService.ListModelDeploymentMonitoringJobs][google.cloud.aiplatform.v1.JobService.ListModelDeploymentMonitoringJobs].
message ListModelDeploymentMonitoringJobsRequest {
  // Required. The parent of the ModelDeploymentMonitoringJob.
  // Format: `projects/{project}/locations/{location}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // The standard list filter.
  //
  // Supported fields:
  //
  //   * `display_name` supports `=`, `!=` comparisons, and `:` wildcard.
  //   * `state` supports `=`, `!=` comparisons.
  //   * `create_time` supports `=`, `!=`,`<`, `<=`,`>`, `>=` comparisons.
  //     `create_time` must be in RFC 3339 format.
  //   * `labels` supports general map functions that is:
  //     `labels.key=value` - key:value equality
  //     `labels.key:* - key existence
  //
  // Some examples of using the filter are:
  //
  //   * `state="JOB_STATE_SUCCEEDED" AND display_name:"my_job_*"`
  //   * `state!="JOB_STATE_FAILED" OR display_name="my_job"`
  //   * `NOT display_name="my_job"`
  //   * `create_time>"2021-05-18T00:00:00Z"`
  //   * `labels.keyA=valueA`
  //   * `labels.keyB:*`
  string filter = 2;

  // The standard list page size.
  int32 page_size = 3;

  // The standard list page token.
  string page_token = 4;

  // Mask specifying which fields to read
  google.protobuf.FieldMask read_mask = 5;
}

// Response message for
// [JobService.ListModelDeploymentMonitoringJobs][google.cloud.aiplatform.v1.JobService.ListModelDeploymentMonitoringJobs].
message ListModelDeploymentMonitoringJobsResponse {
  // A list of ModelDeploymentMonitoringJobs that matches the specified filter
  // in the request.
  repeated ModelDeploymentMonitoringJob model_deployment_monitoring_jobs = 1;

  // The standard List next-page token.
  string next_page_token = 2;
}

// Request message for
// [JobService.UpdateModelDeploymentMonitoringJob][google.cloud.aiplatform.v1.JobService.UpdateModelDeploymentMonitoringJob].
message UpdateModelDeploymentMonitoringJobRequest {
  // Required. The model monitoring configuration which replaces the resource on
  // the server.
  ModelDeploymentMonitoringJob model_deployment_monitoring_job = 1
      [(google.api.field_behavior) = REQUIRED];

  // Required. The update mask is used to specify the fields to be overwritten
  // in the ModelDeploymentMonitoringJob resource by the update. The fields
  // specified in the update_mask are relative to the resource, not the full
  // request. A field will be overwritten if it is in the mask. If the user does
  // not provide a mask then only the non-empty fields present in the request
  // will be overwritten. Set the update_mask to `*` to override all fields. For
  // the objective config, the user can either provide the update mask for
  // model_deployment_monitoring_objective_configs or any combination of its
  // nested fields, such as:
  // model_deployment_monitoring_objective_configs.objective_config.training_dataset.
  //
  // Updatable fields:
  //
  //   * `display_name`
  //   * `model_deployment_monitoring_schedule_config`
  //   * `model_monitoring_alert_config`
  //   * `logging_sampling_strategy`
  //   * `labels`
  //   * `log_ttl`
  //   * `enable_monitoring_pipeline_logs`
  // .  and
  //   * `model_deployment_monitoring_objective_configs`
  // .  or
  //   * `model_deployment_monitoring_objective_configs.objective_config.training_dataset`
  //   * `model_deployment_monitoring_objective_configs.objective_config.training_prediction_skew_detection_config`
  //   * `model_deployment_monitoring_objective_configs.objective_config.prediction_drift_detection_config`
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Request message for
// [JobService.DeleteModelDeploymentMonitoringJob][google.cloud.aiplatform.v1.JobService.DeleteModelDeploymentMonitoringJob].
message DeleteModelDeploymentMonitoringJobRequest {
  // Required. The resource name of the model monitoring job to delete.
  // Format:
  // `projects/{project}/locations/{location}/modelDeploymentMonitoringJobs/{model_deployment_monitoring_job}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/ModelDeploymentMonitoringJob"
    }
  ];
}

// Request message for
// [JobService.PauseModelDeploymentMonitoringJob][google.cloud.aiplatform.v1.JobService.PauseModelDeploymentMonitoringJob].
message PauseModelDeploymentMonitoringJobRequest {
  // Required. The resource name of the ModelDeploymentMonitoringJob to pause.
  // Format:
  // `projects/{project}/locations/{location}/modelDeploymentMonitoringJobs/{model_deployment_monitoring_job}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/ModelDeploymentMonitoringJob"
    }
  ];
}

// Request message for
// [JobService.ResumeModelDeploymentMonitoringJob][google.cloud.aiplatform.v1.JobService.ResumeModelDeploymentMonitoringJob].
message ResumeModelDeploymentMonitoringJobRequest {
  // Required. The resource name of the ModelDeploymentMonitoringJob to resume.
  // Format:
  // `projects/{project}/locations/{location}/modelDeploymentMonitoringJobs/{model_deployment_monitoring_job}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/ModelDeploymentMonitoringJob"
    }
  ];
}

// Runtime operation information for
// [JobService.UpdateModelDeploymentMonitoringJob][google.cloud.aiplatform.v1.JobService.UpdateModelDeploymentMonitoringJob].
message UpdateModelDeploymentMonitoringJobOperationMetadata {
  // The operation generic information.
  GenericOperationMetadata generic_metadata = 1;
}
