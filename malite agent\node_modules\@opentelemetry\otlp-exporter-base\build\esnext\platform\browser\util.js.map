{"version": 3, "file": "util.js", "sourceRoot": "", "sources": ["../../../../src/platform/browser/util.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AACH,OAAO,EAAE,IAAI,EAAE,MAAM,oBAAoB,CAAC;AAC1C,OAAO,EAAE,iBAAiB,EAAE,MAAM,aAAa,CAAC;AAChD,OAAO,EACL,2BAA2B,EAC3B,8BAA8B,EAC9B,iCAAiC,EACjC,0BAA0B,EAC1B,iBAAiB,EACjB,sBAAsB,GACvB,MAAM,YAAY,CAAC;AAEpB;;;;;;;GAOG;AACH,MAAM,UAAU,cAAc,CAC5B,IAAgB,EAChB,GAAW,EACX,eAAgC,EAChC,SAAqB,EACrB,OAA2C;IAE3C,IAAI,SAAS,CAAC,UAAU,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,CAAC,IAAI,CAAC,EAAE,eAAe,CAAC,CAAC,EAAE;QAChE,IAAI,CAAC,KAAK,CAAC,uBAAuB,EAAE,IAAI,CAAC,CAAC;QAC1C,SAAS,EAAE,CAAC;KACb;SAAM;QACL,MAAM,KAAK,GAAG,IAAI,iBAAiB,CAAC,4BAA4B,IAAI,EAAE,CAAC,CAAC;QACxE,OAAO,CAAC,KAAK,CAAC,CAAC;KAChB;AACH,CAAC;AAED;;;;;;;;GAQG;AACH,MAAM,UAAU,WAAW,CACzB,IAAgB,EAChB,GAAW,EACX,OAA+B,EAC/B,eAAuB,EACvB,SAAqB,EACrB,OAA2C;IAE3C,IAAI,UAAyC,CAAC;IAC9C,IAAI,GAAmB,CAAC;IACxB,IAAI,cAAc,GAAG,KAAK,CAAC;IAE3B,MAAM,aAAa,GAAG,UAAU,CAAC,GAAG,EAAE;QACpC,YAAY,CAAC,UAAU,CAAC,CAAC;QACzB,cAAc,GAAG,IAAI,CAAC;QAEtB,IAAI,GAAG,CAAC,UAAU,KAAK,cAAc,CAAC,IAAI,EAAE;YAC1C,MAAM,GAAG,GAAG,IAAI,iBAAiB,CAAC,iBAAiB,CAAC,CAAC;YACrD,OAAO,CAAC,GAAG,CAAC,CAAC;SACd;aAAM;YACL,GAAG,CAAC,KAAK,EAAE,CAAC;SACb;IACH,CAAC,EAAE,eAAe,CAAC,CAAC;IAEpB,MAAM,aAAa,GAAG,CACpB,OAAO,GAAG,2BAA2B,EACrC,QAAQ,GAAG,8BAA8B,EACzC,EAAE;QACF,GAAG,GAAG,IAAI,cAAc,EAAE,CAAC;QAC3B,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;QAEtB,MAAM,cAAc,GAAG;YACrB,MAAM,EAAE,kBAAkB;YAC1B,cAAc,EAAE,kBAAkB;SACnC,CAAC;QAEF,MAAM,CAAC,OAAO,iCACT,cAAc,GACd,OAAO,EACV,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE;YACpB,GAAG,CAAC,gBAAgB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAC7B,CAAC,CAAC,CAAC;QAEH,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAEf,GAAG,CAAC,kBAAkB,GAAG,GAAG,EAAE;YAC5B,IAAI,GAAG,CAAC,UAAU,KAAK,cAAc,CAAC,IAAI,IAAI,cAAc,KAAK,KAAK,EAAE;gBACtE,IAAI,GAAG,CAAC,MAAM,IAAI,GAAG,IAAI,GAAG,CAAC,MAAM,IAAI,GAAG,EAAE;oBAC1C,IAAI,CAAC,KAAK,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC;oBAChC,SAAS,EAAE,CAAC;oBACZ,YAAY,CAAC,aAAa,CAAC,CAAC;oBAC5B,YAAY,CAAC,UAAU,CAAC,CAAC;iBAC1B;qBAAM,IAAI,GAAG,CAAC,MAAM,IAAI,iBAAiB,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,OAAO,GAAG,CAAC,EAAE;oBACrE,IAAI,SAAiB,CAAC;oBACtB,QAAQ,GAAG,iCAAiC,GAAG,QAAQ,CAAC;oBAExD,uDAAuD;oBACvD,IAAI,GAAG,CAAC,iBAAiB,CAAC,aAAa,CAAC,EAAE;wBACxC,SAAS,GAAG,sBAAsB,CAChC,GAAG,CAAC,iBAAiB,CAAC,aAAa,CAAE,CACtC,CAAC;qBACH;yBAAM;wBACL,kCAAkC;wBAClC,SAAS,GAAG,IAAI,CAAC,KAAK,CACpB,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,0BAA0B,GAAG,QAAQ,CAAC,GAAG,QAAQ,CACnE,CAAC;qBACH;oBAED,UAAU,GAAG,UAAU,CAAC,GAAG,EAAE;wBAC3B,aAAa,CAAC,OAAO,GAAG,CAAC,EAAE,QAAQ,CAAC,CAAC;oBACvC,CAAC,EAAE,SAAS,CAAC,CAAC;iBACf;qBAAM;oBACL,MAAM,KAAK,GAAG,IAAI,iBAAiB,CACjC,sCAAsC,GAAG,CAAC,MAAM,GAAG,EACnD,GAAG,CAAC,MAAM,CACX,CAAC;oBACF,OAAO,CAAC,KAAK,CAAC,CAAC;oBACf,YAAY,CAAC,aAAa,CAAC,CAAC;oBAC5B,YAAY,CAAC,UAAU,CAAC,CAAC;iBAC1B;aACF;QACH,CAAC,CAAC;QAEF,GAAG,CAAC,OAAO,GAAG,GAAG,EAAE;YACjB,IAAI,cAAc,EAAE;gBAClB,MAAM,GAAG,GAAG,IAAI,iBAAiB,CAAC,iBAAiB,CAAC,CAAC;gBACrD,OAAO,CAAC,GAAG,CAAC,CAAC;aACd;YACD,YAAY,CAAC,aAAa,CAAC,CAAC;YAC5B,YAAY,CAAC,UAAU,CAAC,CAAC;QAC3B,CAAC,CAAC;QAEF,GAAG,CAAC,OAAO,GAAG,GAAG,EAAE;YACjB,IAAI,cAAc,EAAE;gBAClB,MAAM,GAAG,GAAG,IAAI,iBAAiB,CAAC,iBAAiB,CAAC,CAAC;gBACrD,OAAO,CAAC,GAAG,CAAC,CAAC;aACd;YACD,YAAY,CAAC,aAAa,CAAC,CAAC;YAC5B,YAAY,CAAC,UAAU,CAAC,CAAC;QAC3B,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF,aAAa,EAAE,CAAC;AAClB,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { diag } from '@opentelemetry/api';\nimport { OTLPExporterError } from '../../types';\nimport {\n  DEFAULT_EXPORT_MAX_ATTEMPTS,\n  DEFAULT_EXPORT_INITIAL_BACKOFF,\n  DEFAULT_EXPORT_BACKOFF_MULTIPLIER,\n  DEFAULT_EXPORT_MAX_BACKOFF,\n  isExportRetryable,\n  parseRetryAfterToMills,\n} from '../../util';\n\n/**\n * Send metrics/spans using browser navigator.sendBeacon\n * @param body\n * @param url\n * @param blobPropertyBag\n * @param onSuccess\n * @param onError\n */\nexport function sendWithBeacon(\n  body: Uint8Array,\n  url: string,\n  blobPropertyBag: BlobPropertyBag,\n  onSuccess: () => void,\n  onError: (error: OTLPExporterError) => void\n): void {\n  if (navigator.sendBeacon(url, new Blob([body], blobPropertyBag))) {\n    diag.debug('sendBeacon - can send', body);\n    onSuccess();\n  } else {\n    const error = new OTLPExporterError(`sendBeacon - cannot send ${body}`);\n    onError(error);\n  }\n}\n\n/**\n * function to send metrics/spans using browser XMLHttpRequest\n *     used when navigator.sendBeacon is not available\n * @param body\n * @param url\n * @param headers\n * @param onSuccess\n * @param onError\n */\nexport function sendWithXhr(\n  body: Uint8Array,\n  url: string,\n  headers: Record<string, string>,\n  exporterTimeout: number,\n  onSuccess: () => void,\n  onError: (error: OTLPExporterError) => void\n): void {\n  let retryTimer: ReturnType<typeof setTimeout>;\n  let xhr: XMLHttpRequest;\n  let reqIsDestroyed = false;\n\n  const exporterTimer = setTimeout(() => {\n    clearTimeout(retryTimer);\n    reqIsDestroyed = true;\n\n    if (xhr.readyState === XMLHttpRequest.DONE) {\n      const err = new OTLPExporterError('Request Timeout');\n      onError(err);\n    } else {\n      xhr.abort();\n    }\n  }, exporterTimeout);\n\n  const sendWithRetry = (\n    retries = DEFAULT_EXPORT_MAX_ATTEMPTS,\n    minDelay = DEFAULT_EXPORT_INITIAL_BACKOFF\n  ) => {\n    xhr = new XMLHttpRequest();\n    xhr.open('POST', url);\n\n    const defaultHeaders = {\n      Accept: 'application/json',\n      'Content-Type': 'application/json',\n    };\n\n    Object.entries({\n      ...defaultHeaders,\n      ...headers,\n    }).forEach(([k, v]) => {\n      xhr.setRequestHeader(k, v);\n    });\n\n    xhr.send(body);\n\n    xhr.onreadystatechange = () => {\n      if (xhr.readyState === XMLHttpRequest.DONE && reqIsDestroyed === false) {\n        if (xhr.status >= 200 && xhr.status <= 299) {\n          diag.debug('xhr success', body);\n          onSuccess();\n          clearTimeout(exporterTimer);\n          clearTimeout(retryTimer);\n        } else if (xhr.status && isExportRetryable(xhr.status) && retries > 0) {\n          let retryTime: number;\n          minDelay = DEFAULT_EXPORT_BACKOFF_MULTIPLIER * minDelay;\n\n          // retry after interval specified in Retry-After header\n          if (xhr.getResponseHeader('Retry-After')) {\n            retryTime = parseRetryAfterToMills(\n              xhr.getResponseHeader('Retry-After')!\n            );\n          } else {\n            // exponential backoff with jitter\n            retryTime = Math.round(\n              Math.random() * (DEFAULT_EXPORT_MAX_BACKOFF - minDelay) + minDelay\n            );\n          }\n\n          retryTimer = setTimeout(() => {\n            sendWithRetry(retries - 1, minDelay);\n          }, retryTime);\n        } else {\n          const error = new OTLPExporterError(\n            `Failed to export with XHR (status: ${xhr.status})`,\n            xhr.status\n          );\n          onError(error);\n          clearTimeout(exporterTimer);\n          clearTimeout(retryTimer);\n        }\n      }\n    };\n\n    xhr.onabort = () => {\n      if (reqIsDestroyed) {\n        const err = new OTLPExporterError('Request Timeout');\n        onError(err);\n      }\n      clearTimeout(exporterTimer);\n      clearTimeout(retryTimer);\n    };\n\n    xhr.onerror = () => {\n      if (reqIsDestroyed) {\n        const err = new OTLPExporterError('Request Timeout');\n        onError(err);\n      }\n      clearTimeout(exporterTimer);\n      clearTimeout(retryTimer);\n    };\n  };\n\n  sendWithRetry();\n}\n"]}