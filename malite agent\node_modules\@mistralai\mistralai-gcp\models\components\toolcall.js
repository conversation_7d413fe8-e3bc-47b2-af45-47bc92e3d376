"use strict";
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ToolCall$ = exports.ToolCall$outboundSchema = exports.ToolCall$inboundSchema = void 0;
exports.toolCallToJSON = toolCallToJSON;
exports.toolCallFromJSON = toolCallFromJSON;
const z = __importStar(require("zod"));
const schemas_js_1 = require("../../lib/schemas.js");
const functioncall_js_1 = require("./functioncall.js");
const tooltypes_js_1 = require("./tooltypes.js");
/** @internal */
exports.ToolCall$inboundSchema = z.object({
    id: z.string().default("null"),
    type: tooltypes_js_1.ToolTypes$inboundSchema.optional(),
    function: functioncall_js_1.FunctionCall$inboundSchema,
    index: z.number().int().default(0),
});
/** @internal */
exports.ToolCall$outboundSchema = z.object({
    id: z.string().default("null"),
    type: tooltypes_js_1.ToolTypes$outboundSchema.optional(),
    function: functioncall_js_1.FunctionCall$outboundSchema,
    index: z.number().int().default(0),
});
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var ToolCall$;
(function (ToolCall$) {
    /** @deprecated use `ToolCall$inboundSchema` instead. */
    ToolCall$.inboundSchema = exports.ToolCall$inboundSchema;
    /** @deprecated use `ToolCall$outboundSchema` instead. */
    ToolCall$.outboundSchema = exports.ToolCall$outboundSchema;
})(ToolCall$ || (exports.ToolCall$ = ToolCall$ = {}));
function toolCallToJSON(toolCall) {
    return JSON.stringify(exports.ToolCall$outboundSchema.parse(toolCall));
}
function toolCallFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.ToolCall$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'ToolCall' from JSON`);
}
//# sourceMappingURL=toolcall.js.map