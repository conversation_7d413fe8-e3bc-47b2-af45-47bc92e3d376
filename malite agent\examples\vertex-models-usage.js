/**
 * أمثلة على استخدام النماذج المختلفة من Vertex AI Model Garden
 * 
 * يوضح هذا الملف كيفية استخدام النماذج المختلفة المتاحة في Vertex AI
 * بما في ذلك النماذج الحديثة مثل Claude 3.5 Sonnet و Llama 3.2 وغيرها
 */

import { getModel, listAvailableModels, getModelInfo } from '../vertex-ai/config.js';
import { generate } from '@genkit-ai/ai';

// عرض جميع النماذج المتاحة
console.log('النماذج المتاحة:', listAvailableModels());
console.log('معلومات النماذج:', getModelInfo());

/**
 * مثال 1: استخدام نماذج Gemini المختلفة
 */
async function useGeminiModels() {
  console.log('\n=== استخدام نماذج Gemini ===');
  
  // Gemini 1.5 Pro - للمهام المعقدة
  const { model: geminiPro, settings: geminiProSettings } = getModel('text', 'gemini.pro15');
  const proResponse = await generate({
    model: geminiPro,
    prompt: 'اكتب مقالاً قصيراً عن الذكاء الاصطناعي',
    config: geminiProSettings,
  });
  console.log('Gemini Pro Response:', proResponse.text);
  
  // Gemini 1.5 Flash - للمهام السريعة
  const { model: geminiFlash, settings: geminiFlashSettings } = getModel('text', 'gemini.flash');
  const flashResponse = await generate({
    model: geminiFlash,
    prompt: 'لخص هذا النص في جملة واحدة: الذكاء الاصطناعي تقنية متطورة تحاكي الذكاء البشري',
    config: geminiFlashSettings,
  });
  console.log('Gemini Flash Response:', flashResponse.text);
}

/**
 * مثال 2: استخدام نماذج Claude الحديثة
 */
async function useClaudeModels() {
  console.log('\n=== استخدام نماذج Claude ===');
  
  // Claude 3.5 Sonnet - الأحدث والأقوى
  const { model: claudeSonnet35, settings: claudeSettings } = getModel('text', 'claude.sonnet35v2', {
    temperature: 0.3,
    maxOutputTokens: 500,
  });
  
  const claudeResponse = await generate({
    model: claudeSonnet35,
    prompt: 'اشرح مفهوم التعلم العميق بطريقة بسيطة',
    config: claudeSettings,
  });
  console.log('Claude 3.5 Sonnet Response:', claudeResponse.text);
  
  // Claude 3.5 Haiku - سريع وفعال
  const { model: claudeHaiku35, settings: haikuSettings } = getModel('text', 'claude.haiku35');
  const haikuResponse = await generate({
    model: claudeHaiku35,
    prompt: 'ما هي فوائد استخدام الذكاء الاصطناعي؟',
    config: haikuSettings,
  });
  console.log('Claude 3.5 Haiku Response:', haikuResponse.text);
}

/**
 * مثال 3: استخدام نماذج Llama الحديثة
 */
async function useLlamaModels() {
  console.log('\n=== استخدام نماذج Llama ===');
  
  // Llama 3.1 405B - النموذج الأكبر
  const { model: llama405b, settings: llamaSettings } = getModel('text', 'llama.llama31_405b', {
    temperature: 0.7,
    maxOutputTokens: 1000,
  });
  
  const llamaResponse = await generate({
    model: llama405b,
    prompt: 'اكتب كود Python بسيط لحساب الأرقام الأولية',
    config: llamaSettings,
  });
  console.log('Llama 3.1 405B Response:', llamaResponse.text);
  
  // Llama 3.2 Vision - للصور والنصوص
  const { model: llamaVision, settings: visionSettings } = getModel('vision', 'llama32Vision90b');
  // ملاحظة: يتطلب إضافة صورة للاختبار الفعلي
  console.log('Llama 3.2 Vision متاح للاستخدام مع الصور');
}

/**
 * مثال 4: استخدام نماذج Mistral
 */
async function useMistralModels() {
  console.log('\n=== استخدام نماذج Mistral ===');
  
  // Mistral Large - للمهام المعقدة
  const { model: mistralLarge, settings: mistralSettings } = getModel('text', 'mistral.mistralLarge');
  const mistralResponse = await generate({
    model: mistralLarge,
    prompt: 'ما هي أفضل الممارسات في البرمجة؟',
    config: mistralSettings,
  });
  console.log('Mistral Large Response:', mistralResponse.text);
  
  // Codestral - للبرمجة
  const { model: codestral, settings: codeSettings } = getModel('text', 'mistral.codestral');
  const codeResponse = await generate({
    model: codestral,
    prompt: 'اكتب دالة JavaScript لفرز مصفوفة من الأرقام',
    config: codeSettings,
  });
  console.log('Codestral Response:', codeResponse.text);
}

/**
 * مثال 5: استخدام النماذج المتخصصة
 */
async function useSpecializedModels() {
  console.log('\n=== استخدام النماذج المتخصصة ===');
  
  // Codey - لتوليد الكود
  const { model: codey, settings: codeySettings } = getModel('specialized', 'codey');
  const codeyResponse = await generate({
    model: codey,
    prompt: 'اكتب كلاس Python لإدارة قاعدة البيانات',
    config: codeySettings,
  });
  console.log('Codey Response:', codeyResponse.text);
  
  // CodeGemma - نموذج Google للكود
  const { model: codeGemma, settings: gemmaSettings } = getModel('specialized', 'codeGemma');
  const gemmaResponse = await generate({
    model: codeGemma,
    prompt: 'اشرح هذا الكود: function fibonacci(n) { return n <= 1 ? n : fibonacci(n-1) + fibonacci(n-2); }',
    config: gemmaSettings,
  });
  console.log('CodeGemma Response:', gemmaResponse.text);
}

/**
 * مثال 6: استخدام نماذج التضمين
 */
async function useEmbeddingModels() {
  console.log('\n=== استخدام نماذج التضمين ===');
  
  // Text Embedding - للنصوص العربية والإنجليزية
  const { model: textEmbedding } = getModel('embedding', 'textEmbeddingMultilingual');
  
  // ملاحظة: نماذج التضمين تتطلب API مختلف
  console.log('نموذج التضمين متعدد اللغات متاح للاستخدام');
  
  // Multimodal Embedding - للنصوص والصور
  const { model: multimodalEmbedding } = getModel('embedding', 'multimodalEmbedding');
  console.log('نموذج التضمين متعدد الوسائط متاح للاستخدام');
}

/**
 * مثال 7: مقارنة أداء النماذج المختلفة
 */
async function compareModels() {
  console.log('\n=== مقارنة النماذج ===');
  
  const prompt = 'اكتب قصة قصيرة عن المستقبل في 100 كلمة';
  const models = [
    { type: 'text', name: 'gemini.flash', label: 'Gemini Flash' },
    { type: 'text', name: 'claude.sonnet35v2', label: 'Claude 3.5 Sonnet v2' },
    { type: 'text', name: 'llama.llama31_70b', label: 'Llama 3.1 70B' },
    { type: 'text', name: 'mistral.mistralLarge', label: 'Mistral Large' },
  ];
  
  for (const modelConfig of models) {
    try {
      const startTime = Date.now();
      const { model, settings } = getModel(modelConfig.type, modelConfig.name, {
        maxOutputTokens: 200,
        temperature: 0.7,
      });
      
      const response = await generate({
        model,
        prompt,
        config: settings,
      });
      
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      console.log(`\n${modelConfig.label}:`);
      console.log(`الوقت: ${duration}ms`);
      console.log(`الاستجابة: ${response.text.substring(0, 150)}...`);
      
    } catch (error) {
      console.log(`خطأ في ${modelConfig.label}:`, error.message);
    }
  }
}

/**
 * تشغيل جميع الأمثلة
 */
async function runAllExamples() {
  try {
    await useGeminiModels();
    await useClaudeModels();
    await useLlamaModels();
    await useMistralModels();
    await useSpecializedModels();
    await useEmbeddingModels();
    await compareModels();
  } catch (error) {
    console.error('خطأ في تشغيل الأمثلة:', error);
  }
}

// تشغيل الأمثلة إذا تم استدعاء الملف مباشرة
if (import.meta.url === `file://${process.argv[1]}`) {
  runAllExamples();
}

export {
  useGeminiModels,
  useClaudeModels,
  useLlamaModels,
  useMistralModels,
  useSpecializedModels,
  useEmbeddingModels,
  compareModels,
  runAllExamples,
};
