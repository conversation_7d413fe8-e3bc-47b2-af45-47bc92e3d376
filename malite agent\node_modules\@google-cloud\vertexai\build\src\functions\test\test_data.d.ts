/**
 * @license
 * Copyright 2024 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
import { CountTokensResponse, GenerateContentResponse } from '../../types/content';
export declare const STREAM_RESPONSE_CHUNKS_1: GenerateContentResponse[];
export declare const AGGREGATED_RESPONSE_STREAM_RESPONSE_CHUNKS_1: GenerateContentResponse;
export declare const STREAM_RESPONSE_CHUNKS_2: GenerateContentResponse[];
export declare const AGGREGATED_RESPONSE_STREAM_RESPONSE_CHUNKS_2: GenerateContentResponse;
export declare const STREAM_RESPONSE_CHUNKS_3: GenerateContentResponse[];
export declare const AGGREGATED_RESPONSE_STREAM_RESPONSE_CHUNKS_3: GenerateContentResponse;
export declare const STREAM_RESPONSE_CHUNKS_4: GenerateContentResponse[];
export declare const AGGREGATED_RESPONSE_STREAM_RESPONSE_CHUNKS_4: GenerateContentResponse;
export declare const STREAM_RESPONSE_CHUNKS_5: GenerateContentResponse[];
export declare const AGGREGATED_RESPONSE_STREAM_RESPONSE_CHUNKS_5: GenerateContentResponse;
export declare const STREAM_RESPONSE_CHUNKS_6: GenerateContentResponse[];
export declare const AGGREGATED_RESPONSE_STREAM_RESPONSE_CHUNKS_6: GenerateContentResponse;
export declare const UNARY_RESPONSE_1: GenerateContentResponse;
export declare const UNARY_RESPONSE_MISSING_ROLE_INDEX: GenerateContentResponse;
export declare const COUNT_TOKENS_RESPONSE_1: CountTokensResponse;
