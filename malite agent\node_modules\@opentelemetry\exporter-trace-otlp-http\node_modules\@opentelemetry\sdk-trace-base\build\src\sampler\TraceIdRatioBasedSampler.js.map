{"version": 3, "file": "TraceIdRatioBasedSampler.js", "sourceRoot": "", "sources": ["../../../src/sampler/TraceIdRatioBasedSampler.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,4CAAoD;AACpD,wCAAuE;AAEvE,2FAA2F;AAC3F,MAAa,wBAAwB;IAGnC,YAA6B,SAAiB,CAAC;QAAlB,WAAM,GAAN,MAAM,CAAY;QAC7C,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;QACtC,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,GAAG,UAAU,CAAC,CAAC;IAC1D,CAAC;IAED,YAAY,CAAC,OAAgB,EAAE,OAAe;QAC5C,OAAO;YACL,QAAQ,EACN,IAAA,oBAAc,EAAC,OAAO,CAAC,IAAI,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,GAAG,IAAI,CAAC,WAAW;gBACrE,CAAC,CAAC,0BAAgB,CAAC,kBAAkB;gBACrC,CAAC,CAAC,0BAAgB,CAAC,UAAU;SAClC,CAAC;IACJ,CAAC;IAED,QAAQ;QACN,OAAO,qBAAqB,IAAI,CAAC,MAAM,GAAG,CAAC;IAC7C,CAAC;IAEO,UAAU,CAAC,KAAa;QAC9B,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,CAAC,KAAK,CAAC;YAAE,OAAO,CAAC,CAAC;QACxD,OAAO,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;IACjD,CAAC;IAEO,WAAW,CAAC,OAAe;QACjC,IAAI,YAAY,GAAG,CAAC,CAAC;QACrB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;YAC3C,MAAM,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;YAClB,MAAM,IAAI,GAAG,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,EAAE,GAAG,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;YACvD,YAAY,GAAG,CAAC,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC;SAC5C;QACD,OAAO,YAAY,CAAC;IACtB,CAAC;CACF;AAnCD,4DAmCC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { isValidTraceId } from '@opentelemetry/api';\nimport { Sampler, SamplingDecision, SamplingResult } from '../Sampler';\n\n/** Sampler that samples a given fraction of traces based of trace id deterministically. */\nexport class TraceIdRatioBasedSampler implements Sampler {\n  private _upperBound: number;\n\n  constructor(private readonly _ratio: number = 0) {\n    this._ratio = this._normalize(_ratio);\n    this._upperBound = Math.floor(this._ratio * 0xffffffff);\n  }\n\n  shouldSample(context: unknown, traceId: string): SamplingResult {\n    return {\n      decision:\n        isValidTraceId(traceId) && this._accumulate(traceId) < this._upperBound\n          ? SamplingDecision.RECORD_AND_SAMPLED\n          : SamplingDecision.NOT_RECORD,\n    };\n  }\n\n  toString(): string {\n    return `TraceIdRatioBased{${this._ratio}}`;\n  }\n\n  private _normalize(ratio: number): number {\n    if (typeof ratio !== 'number' || isNaN(ratio)) return 0;\n    return ratio >= 1 ? 1 : ratio <= 0 ? 0 : ratio;\n  }\n\n  private _accumulate(traceId: string): number {\n    let accumulation = 0;\n    for (let i = 0; i < traceId.length / 8; i++) {\n      const pos = i * 8;\n      const part = parseInt(traceId.slice(pos, pos + 8), 16);\n      accumulation = (accumulation ^ part) >>> 0;\n    }\n    return accumulation;\n  }\n}\n"]}