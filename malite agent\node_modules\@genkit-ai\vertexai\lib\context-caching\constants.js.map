{"version": 3, "sources": ["../../src/context-caching/constants.ts"], "sourcesContent": ["/**\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport const CONTEXT_CACHE_SUPPORTED_MODELS = [\n  'gemini-1.5-flash-001',\n  'gemini-1.5-pro-001',\n];\n\nexport const INVALID_ARGUMENT_MESSAGES = {\n  modelVersion: `Model version is required for context caching, supported only in ${CONTEXT_CACHE_SUPPORTED_MODELS.join(',')} models.`,\n  tools: 'Context caching cannot be used simultaneously with tools.',\n  codeExecution:\n    'Context caching cannot be used simultaneously with code execution.',\n};\n\nexport const DEFAULT_TTL = 300;\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAgBO,MAAM,iCAAiC;AAAA,EAC5C;AAAA,EACA;AACF;AAEO,MAAM,4BAA4B;AAAA,EACvC,cAAc,oEAAoE,+BAA+B,KAAK,GAAG,CAAC;AAAA,EAC1H,OAAO;AAAA,EACP,eACE;AACJ;AAEO,MAAM,cAAc;", "names": []}