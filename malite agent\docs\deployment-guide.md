# دليل النشر على Google Cloud Platform

هذا الدليل يوضح كيفية نشر تطبيق Malite Agent على Google Cloud Platform باستخدام طرق مختلفة.

## المتطلبات الأساسية

### 1. إ<PERSON>داد Google Cloud

```bash
# تثبيت Google Cloud CLI
# للويندوز: تحميل من https://cloud.google.com/sdk/docs/install
# للماك: brew install google-cloud-sdk
# للينكس: curl https://sdk.cloud.google.com | bash

# تسجيل الدخول
gcloud auth login

# إنشاء مشروع جديد (أو استخدام موجود)
gcloud projects create malite-agent-project --name="Malite Agent"

# تعيين المشروع الافتراضي
gcloud config set project malite-agent-project

# تفعيل APIs المطلوبة
gcloud services enable appengine.googleapis.com
gcloud services enable cloudbuild.googleapis.com
gcloud services enable run.googleapis.com
gcloud services enable aiplatform.googleapis.com
gcloud services enable secretmanager.googleapis.com
```

### 2. إعداد المتغيرات البيئية

```bash
# تعيين متغيرات البيئة
export PROJECT_ID="malite-agent-project"
export REGION="us-central1"
export SERVICE_NAME="malite-agent"
```

### 3. إنشاء Service Account

```bash
# إنشاء Service Account
gcloud iam service-accounts create malite-agent-sa \
    --display-name="Malite Agent Service Account"

# منح الصلاحيات المطلوبة
gcloud projects add-iam-policy-binding $PROJECT_ID \
    --member="serviceAccount:malite-agent-sa@$PROJECT_ID.iam.gserviceaccount.com" \
    --role="roles/aiplatform.user"

gcloud projects add-iam-policy-binding $PROJECT_ID \
    --member="serviceAccount:malite-agent-sa@$PROJECT_ID.iam.gserviceaccount.com" \
    --role="roles/secretmanager.secretAccessor"

# إنشاء وتحميل مفتاح Service Account
gcloud iam service-accounts keys create ./service-account-key.json \
    --iam-account=malite-agent-sa@$PROJECT_ID.iam.gserviceaccount.com
```

## طرق النشر

### الطريقة 1: App Engine (الأسهل)

#### الخطوات:

1. **إعداد App Engine:**
```bash
# إنشاء تطبيق App Engine
gcloud app create --region=$REGION
```

2. **تحديث ملف app.yaml:**
```yaml
# تأكد من تحديث PROJECT_ID في app.yaml
env_variables:
  GOOGLE_CLOUD_PROJECT: "malite-agent-project"
  VERTEX_AI_LOCATION: "us-central1"
```

3. **النشر:**
```bash
# النشر المباشر
npm run deploy:gcp

# أو باستخدام gcloud مباشرة
gcloud app deploy --quiet
```

4. **فتح التطبيق:**
```bash
gcloud app browse
```

#### المزايا:
- ✅ سهل الإعداد والنشر
- ✅ توسع تلقائي
- ✅ إدارة مبسطة
- ✅ SSL مجاني

#### العيوب:
- ❌ أقل مرونة في التحكم
- ❌ قيود على وقت التشغيل

### الطريقة 2: Cloud Run (الأفضل)

#### الخطوات:

1. **بناء ونشر باستخدام Cloud Build:**
```bash
# النشر المباشر من الكود المصدري
npm run deploy:cloud-run

# أو باستخدام gcloud مباشرة
gcloud run deploy malite-agent \
    --source . \
    --region $REGION \
    --allow-unauthenticated \
    --memory 2Gi \
    --cpu 1 \
    --max-instances 10 \
    --set-env-vars NODE_ENV=production,GENKIT_ENV=production
```

2. **أو بناء Docker محلياً:**
```bash
# بناء الصورة
npm run docker:build

# رفع الصورة إلى Container Registry
docker tag malite-agent gcr.io/$PROJECT_ID/malite-agent
docker push gcr.io/$PROJECT_ID/malite-agent

# النشر
gcloud run deploy malite-agent \
    --image gcr.io/$PROJECT_ID/malite-agent \
    --region $REGION \
    --allow-unauthenticated
```

#### المزايا:
- ✅ مرونة عالية
- ✅ دعم كامل للحاويات
- ✅ توسع سريع (من 0 إلى 1000 instance)
- ✅ دفع حسب الاستخدام
- ✅ دعم HTTPS تلقائي

#### العيوب:
- ❌ يتطلب معرفة بـ Docker
- ❌ إعداد أكثر تعقيداً

### الطريقة 3: Cloud Build التلقائي

#### إعداد CI/CD Pipeline:

1. **ربط المستودع:**
```bash
# ربط GitHub أو Cloud Source Repository
gcloud builds triggers create github \
    --repo-name=malite-agent \
    --repo-owner=YOUR_USERNAME \
    --branch-pattern="^main$" \
    --build-config=cloudbuild.yaml
```

2. **تشغيل البناء:**
```bash
# تشغيل البناء يدوياً
gcloud builds submit --config cloudbuild.yaml .
```

#### المزايا:
- ✅ نشر تلقائي عند كل commit
- ✅ اختبارات تلقائية
- ✅ إدارة الإصدارات

## إعداد الأسرار (Secrets)

### 1. إنشاء الأسرار:

```bash
# إنشاء سر لمفتاح Service Account
gcloud secrets create vertex-ai-key --data-file=service-account-key.json

# إنشاء سر لمفتاح Gemini API (إذا كنت تستخدمه)
echo "YOUR_GEMINI_API_KEY" | gcloud secrets create gemini-api-key --data-file=-
```

### 2. منح الصلاحيات:

```bash
# منح صلاحية الوصول للأسرار
gcloud secrets add-iam-policy-binding vertex-ai-key \
    --member="serviceAccount:malite-agent-sa@$PROJECT_ID.iam.gserviceaccount.com" \
    --role="roles/secretmanager.secretAccessor"
```

### 3. استخدام الأسرار في التطبيق:

```bash
# في Cloud Run
gcloud run deploy malite-agent \
    --set-env-vars GOOGLE_APPLICATION_CREDENTIALS=/secrets/vertex-ai-key \
    --set-secrets /secrets/vertex-ai-key=vertex-ai-key:latest
```

## مراقبة التطبيق

### 1. السجلات:

```bash
# عرض سجلات App Engine
gcloud app logs tail -s default

# عرض سجلات Cloud Run
gcloud run services logs read malite-agent --region=$REGION
```

### 2. المراقبة:

```bash
# فتح Cloud Monitoring
gcloud monitoring dashboards list
```

### 3. التنبيهات:

```bash
# إنشاء تنبيه للأخطاء
gcloud alpha monitoring policies create --policy-from-file=monitoring-policy.yaml
```

## اختبار النشر

### 1. فحص الصحة:

```bash
# الحصول على URL التطبيق
APP_URL=$(gcloud run services describe malite-agent --region=$REGION --format="value(status.url)")

# فحص الصحة
curl $APP_URL/health
```

### 2. اختبار API:

```bash
# اختبار API الدردشة
curl -X POST $APP_URL/api/chat \
  -H "Content-Type: application/json" \
  -d '{"message": "مرحباً، كيف حالك؟"}'

# اختبار قائمة النماذج
curl $APP_URL/api/models
```

## استكشاف الأخطاء

### مشاكل شائعة:

1. **خطأ في الصلاحيات:**
```bash
# التحقق من الصلاحيات
gcloud projects get-iam-policy $PROJECT_ID
```

2. **مشاكل في متغيرات البيئة:**
```bash
# التحقق من متغيرات البيئة في Cloud Run
gcloud run services describe malite-agent --region=$REGION
```

3. **مشاكل في الذاكرة:**
```bash
# زيادة الذاكرة المخصصة
gcloud run services update malite-agent --memory=4Gi --region=$REGION
```

### أوامر مفيدة للتشخيص:

```bash
# عرض معلومات الخدمة
gcloud run services describe malite-agent --region=$REGION

# عرض السجلات الحديثة
gcloud run services logs read malite-agent --region=$REGION --limit=50

# عرض استخدام الموارد
gcloud monitoring metrics list --filter="resource.type=cloud_run_revision"
```

## التحسين والأمان

### 1. تحسين الأداء:
- استخدام التخزين المؤقت
- ضغط الاستجابات
- تحسين حجم الصورة

### 2. الأمان:
- تفعيل HTTPS فقط
- استخدام IAM للتحكم في الوصول
- تشفير البيانات الحساسة

### 3. التكلفة:
- مراقبة الاستخدام
- تعيين حدود للموارد
- استخدام التوسع التلقائي

## الدعم والمساعدة

- [وثائق Google Cloud](https://cloud.google.com/docs)
- [وثائق Genkit](https://firebase.google.com/docs/genkit)
- [مجتمع Google Cloud](https://cloud.google.com/community)

---

**ملاحظة**: تأكد من مراجعة التكاليف المتوقعة قبل النشر، خاصة عند استخدام نماذج Vertex AI الكبيرة.
