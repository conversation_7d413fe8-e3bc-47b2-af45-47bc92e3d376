import { Detector } from '../../../types';
import { ResourceDetectionConfig } from '../../../config';
import { IResource } from '../../../IResource';
/**
 * HostDetector detects the resources related to the host current process is
 * running on. Currently only non-cloud-based attributes are included.
 */
declare class HostDetector implements Detector {
    detect(_config?: ResourceDetectionConfig): Promise<IResource>;
}
export declare const hostDetector: HostDetector;
export {};
//# sourceMappingURL=HostDetector.d.ts.map