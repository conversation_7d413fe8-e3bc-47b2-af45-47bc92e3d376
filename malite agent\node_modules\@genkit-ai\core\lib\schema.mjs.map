{"version": 3, "sources": ["../src/schema.ts"], "sourcesContent": ["/**\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport Ajv, { ErrorObject, JSONSchemaType } from 'ajv';\nimport addFormats from 'ajv-formats';\nimport { z } from 'zod';\nimport zodToJsonSchema from 'zod-to-json-schema';\nimport { GenkitError } from './error.js';\nimport { Registry } from './registry.js';\nconst ajv = new Ajv();\naddFormats(ajv);\n\nexport { z }; // provide a consistent zod to use throughout genkit\n\n/**\n * JSON schema.\n */\nexport type JSONSchema = JSONSchemaType<any> | any;\n\nconst jsonSchemas = new WeakMap<z.ZodTypeAny, JSONSchema>();\nconst validators = new WeakMap<JSONSchema, ReturnType<typeof ajv.compile>>();\n\n/**\n * Wrapper object for various ways schema can be provided.\n */\nexport interface ProvidedSchema {\n  jsonSchema?: JSONSchema;\n  schema?: z.ZodTypeAny;\n}\n\n/**\n * Schema validation error.\n */\nexport class ValidationError extends GenkitError {\n  constructor({\n    data,\n    errors,\n    schema,\n  }: {\n    data: any;\n    errors: ValidationErrorDetail[];\n    schema: JSONSchema;\n  }) {\n    super({\n      status: 'INVALID_ARGUMENT',\n      message: `Schema validation failed. Parse Errors:\\n\\n${errors.map((e) => `- ${e.path}: ${e.message}`).join('\\n')}\\n\\nProvided data:\\n\\n${JSON.stringify(data, null, 2)}\\n\\nRequired JSON schema:\\n\\n${JSON.stringify(schema, null, 2)}`,\n      detail: { errors, schema },\n    });\n  }\n}\n\n/**\n * Convertes a Zod schema into a JSON schema, utilizing an in-memory cache for known objects.\n * @param options Provide a json schema and/or zod schema. JSON schema has priority.\n * @returns A JSON schema.\n */\nexport function toJsonSchema({\n  jsonSchema,\n  schema,\n}: ProvidedSchema): JSONSchema | undefined {\n  // if neither jsonSchema or schema is present return undefined\n  if (!jsonSchema && !schema) return null;\n  if (jsonSchema) return jsonSchema;\n  if (jsonSchemas.has(schema!)) return jsonSchemas.get(schema!)!;\n  const outSchema = zodToJsonSchema(schema!, {\n    $refStrategy: 'none',\n    removeAdditionalStrategy: 'strict',\n  });\n  jsonSchemas.set(schema!, outSchema as JSONSchema);\n  return outSchema as JSONSchema;\n}\n\n/**\n * Schema validation error details.\n */\nexport interface ValidationErrorDetail {\n  path: string;\n  message: string;\n}\n\nfunction toErrorDetail(error: ErrorObject): ValidationErrorDetail {\n  return {\n    path: error.instancePath.substring(1).replace(/\\//g, '.') || '(root)',\n    message: error.message!,\n  };\n}\n\n/**\n * Validation response.\n */\nexport type ValidationResponse =\n  | { valid: true; errors: never }\n  | { valid: false; errors: ErrorObject[] };\n\n/**\n * Validates the provided data against the provided schema.\n */\nexport function validateSchema(\n  data: unknown,\n  options: ProvidedSchema\n): { valid: boolean; errors?: any[]; schema: JSONSchema } {\n  const toValidate = toJsonSchema(options);\n  if (!toValidate) {\n    return { valid: true, schema: toValidate };\n  }\n  const validator = validators.get(toValidate) || ajv.compile(toValidate);\n  const valid = validator(data) as boolean;\n  const errors = validator.errors?.map((e) => e);\n  return { valid, errors: errors?.map(toErrorDetail), schema: toValidate };\n}\n\n/**\n * Parses raw data object agaisnt the provided schema.\n */\nexport function parseSchema<T = unknown>(\n  data: unknown,\n  options: ProvidedSchema\n): T {\n  const { valid, errors, schema } = validateSchema(data, options);\n  if (!valid) throw new ValidationError({ data, errors: errors!, schema });\n  return data as T;\n}\n\n/**\n * Registers provided schema as a named schema object in the Genkit registry.\n *\n * @hidden\n */\nexport function defineSchema<T extends z.ZodTypeAny>(\n  registry: Registry,\n  name: string,\n  schema: T\n): T {\n  registry.registerSchema(name, { schema });\n  return schema;\n}\n\n/**\n * Registers provided JSON schema as a named schema object in the Genkit registry.\n *\n * @hidden\n */\nexport function defineJsonSchema(\n  registry: Registry,\n  name: string,\n  jsonSchema: JSONSchema\n) {\n  registry.registerSchema(name, { jsonSchema });\n  return jsonSchema;\n}\n"], "mappings": "AAgBA,OAAO,SAA0C;AACjD,OAAO,gBAAgB;AACvB,SAAS,SAAS;AAClB,OAAO,qBAAqB;AAC5B,SAAS,mBAAmB;AAE5B,MAAM,MAAM,IAAI,IAAI;AACpB,WAAW,GAAG;AASd,MAAM,cAAc,oBAAI,QAAkC;AAC1D,MAAM,aAAa,oBAAI,QAAoD;AAapE,MAAM,wBAAwB,YAAY;AAAA,EAC/C,YAAY;AAAA,IACV;AAAA,IACA;AAAA,IACA;AAAA,EACF,GAIG;AACD,UAAM;AAAA,MACJ,QAAQ;AAAA,MACR,SAAS;AAAA;AAAA,EAA8C,OAAO,IAAI,CAAC,MAAM,KAAK,EAAE,IAAI,KAAK,EAAE,OAAO,EAAE,EAAE,KAAK,IAAI,CAAC;AAAA;AAAA;AAAA;AAAA,EAAyB,KAAK,UAAU,MAAM,MAAM,CAAC,CAAC;AAAA;AAAA;AAAA;AAAA,EAAgC,KAAK,UAAU,QAAQ,MAAM,CAAC,CAAC;AAAA,MACrO,QAAQ,EAAE,QAAQ,OAAO;AAAA,IAC3B,CAAC;AAAA,EACH;AACF;AAOO,SAAS,aAAa;AAAA,EAC3B;AAAA,EACA;AACF,GAA2C;AAEzC,MAAI,CAAC,cAAc,CAAC,OAAQ,QAAO;AACnC,MAAI,WAAY,QAAO;AACvB,MAAI,YAAY,IAAI,MAAO,EAAG,QAAO,YAAY,IAAI,MAAO;AAC5D,QAAM,YAAY,gBAAgB,QAAS;AAAA,IACzC,cAAc;AAAA,IACd,0BAA0B;AAAA,EAC5B,CAAC;AACD,cAAY,IAAI,QAAS,SAAuB;AAChD,SAAO;AACT;AAUA,SAAS,cAAc,OAA2C;AAChE,SAAO;AAAA,IACL,MAAM,MAAM,aAAa,UAAU,CAAC,EAAE,QAAQ,OAAO,GAAG,KAAK;AAAA,IAC7D,SAAS,MAAM;AAAA,EACjB;AACF;AAYO,SAAS,eACd,MACA,SACwD;AACxD,QAAM,aAAa,aAAa,OAAO;AACvC,MAAI,CAAC,YAAY;AACf,WAAO,EAAE,OAAO,MAAM,QAAQ,WAAW;AAAA,EAC3C;AACA,QAAM,YAAY,WAAW,IAAI,UAAU,KAAK,IAAI,QAAQ,UAAU;AACtE,QAAM,QAAQ,UAAU,IAAI;AAC5B,QAAM,SAAS,UAAU,QAAQ,IAAI,CAAC,MAAM,CAAC;AAC7C,SAAO,EAAE,OAAO,QAAQ,QAAQ,IAAI,aAAa,GAAG,QAAQ,WAAW;AACzE;AAKO,SAAS,YACd,MACA,SACG;AACH,QAAM,EAAE,OAAO,QAAQ,OAAO,IAAI,eAAe,MAAM,OAAO;AAC9D,MAAI,CAAC,MAAO,OAAM,IAAI,gBAAgB,EAAE,MAAM,QAAiB,OAAO,CAAC;AACvE,SAAO;AACT;AAOO,SAAS,aACd,UACA,MACA,QACG;AACH,WAAS,eAAe,MAAM,EAAE,OAAO,CAAC;AACxC,SAAO;AACT;AAOO,SAAS,iBACd,UACA,MACA,YACA;AACA,WAAS,eAAe,MAAM,EAAE,WAAW,CAAC;AAC5C,SAAO;AACT;", "names": []}