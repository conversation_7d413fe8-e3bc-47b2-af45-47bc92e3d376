{"version": 3, "file": "EnvDetector.js", "sourceRoot": "", "sources": ["../../../src/detectors/EnvDetector.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAKH,OAAO,EAAE,eAAe,EAAE,MAAM,mBAAmB,CAAC;AAEpD;;;GAGG;AACH,MAAM,WAAW;IACf;;;;;;OAMG;IACH,MAAM,CAAC,MAAgC;QACrC,OAAO,OAAO,CAAC,OAAO,CAAC,eAAe,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC;IACzD,CAAC;CACF;AAED,MAAM,CAAC,MAAM,WAAW,GAAG,IAAI,WAAW,EAAE,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Detector } from '../types';\nimport { ResourceDetectionConfig } from '../config';\nimport { IResource } from '../IResource';\nimport { envDetectorSync } from './EnvDetectorSync';\n\n/**\n * EnvDetector can be used to detect the presence of and create a Resource\n * from the OTEL_RESOURCE_ATTRIBUTES environment variable.\n */\nclass EnvDetector implements Detector {\n  /**\n   * Returns a {@link Resource} populated with attributes from the\n   * OTEL_RESOURCE_ATTRIBUTES environment variable. Note this is an async\n   * function to conform to the Detector interface.\n   *\n   * @param config The resource detection config\n   */\n  detect(config?: ResourceDetectionConfig): Promise<IResource> {\n    return Promise.resolve(envDetectorSync.detect(config));\n  }\n}\n\nexport const envDetector = new EnvDetector();\n"]}