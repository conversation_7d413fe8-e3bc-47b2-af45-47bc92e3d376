body {
	font-family: 'Roboto', sans-serif;
	font-display: fallback;
	background: url('cloud_bg.svg');
	background-size: cover;
	background-position: center;
	line-height: 1.6;
 }

h1 {
	font-size: 24px;
	font-weight: bold;
	margin-bottom: 0px;
}

h2 {
	font-size: 18px;
	font-weight: normal;
	margin-top: 4px;
}

h3 {
	font-size: 20px;
	margin-bottom: 0px;
}

em {
	font-weight: 600;
	font-style: normal;
}

.next {
    font-size: 15px;
	margin-top: 8px;
	margin-bottom: 8px;
}

.container {
	max-width: 700px;
	margin-left: auto;
	margin-right: auto;
} 

.hero {
	margin-top: 172px;
}

.hero .logo {
	width: 140px;
	float: left;
	margin-right: 32px;
}

.hero .message {
	font-size: 15px;
	text-align: center;
	margin-bottom: 24px;
}

.details {
	clear: both;
	height: 400px;
	line-height: 1.8;
}

.callout {
	font-size: 14px;
	color: #1967D2;
	font-weight: 600;
	padding: 16px 24px 16px 40px;
	background: url('lightbulb_icon.svg') #E8F0FE ;
	background-position: 16px 16px;
	background-repeat: no-repeat;
	line-height: 1.6;
}

code {
	font-family: 'Roboto Mono', Courier, monospace;
	color: #A30038;
	background-color: #F8F8F8;
	border: 1px solid #DDD;
	border-radius: 2px;
	padding: 0 6px;
	font-weight: 500;
}

.cta {
	padding-bottom: 52px;
}

.cta a {
	color: white;
	text-decoration: none;
	background-color: rgb(14, 101, 241);
	padding: 8px 12px;
	margin: 8px;
	min-height: 36px;
	border-radius: 2px;
	font-weight: 600;
}

.cta a:hover {
    background-color: rgb(66, 133, 244, 0.9);
}

@media (max-width: 600px) {
    .hero .logo {
        float: initial;
        display: block;
        margin-left: auto;
        margin-right: auto;
    }

	.container {
        margin-top: 0;
	}

    .hero {
        margin-top: 0;
        margin-bottom: 24px;
	}
}
@media (prefers-color-scheme: dark) {
    body {
        background: black url('dark_bg.svg');
        color:white;
    }
	a {
		color: #8AB4F8;
	}
	a:visited {
		color: #b48af8;
	}
	.cta a {
		color: white;
	}
}

