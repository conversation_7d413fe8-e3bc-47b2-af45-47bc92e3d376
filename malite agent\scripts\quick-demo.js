#!/usr/bin/env node

/**
 * عرض توضيحي سريع لإمكانيات مشروع Malite Agent
 */

import { googleAI } from '@genkit-ai/googleai';
import { vertexAI } from '@genkit-ai/vertexai';
import { genkit } from 'genkit';
import dotenv from 'dotenv';

// تحميل متغيرات البيئة
dotenv.config();

// تكوين Genkit
const ai = genkit({
  plugins: [
    googleAI(),
    vertexAI({ location: 'us-central1' })
  ],
  model: vertexAI.model('gemini-1.0-pro'),
});

console.log('\n🎬 ===== عرض توضيحي سريع - Malite Agent =====\n');

/**
 * عرض إمكانيات الذكاء الاصطناعي باللغة العربية
 */
async function runQuickDemo() {
  const demos = [
    {
      title: '🤖 التعريف بالذكاء الاصطناعي',
      prompt: 'عرف الذكاء الاصطناعي في جملتين بسيطتين',
      icon: '🧠'
    },
    {
      title: '📝 تلخيص النصوص',
      prompt: 'لخص هذا النص: تطبيقات الذكاء الاصطناعي تشمل الطب والتعليم والنقل والتمويل وتساعد في تحسين الكفاءة وتقليل الأخطاء البشرية وتوفير حلول مبتكرة للمشاكل المعقدة',
      icon: '📄'
    },
    {
      title: '🌍 الترجمة الفورية',
      prompt: 'ترجم إلى الإنجليزية: التكنولوجيا تغير العالم',
      icon: '🔄'
    },
    {
      title: '💡 الإبداع والابتكار',
      prompt: 'اقترح 3 أفكار مبتكرة لاستخدام الذكاء الاصطناعي في التعليم',
      icon: '🚀'
    },
    {
      title: '🎭 الكتابة الإبداعية',
      prompt: 'اكتب قصة قصيرة من 3 جمل عن روبوت يتعلم اللغة العربية',
      icon: '📚'
    }
  ];

  for (let i = 0; i < demos.length; i++) {
    const demo = demos[i];
    
    console.log(`${demo.icon} ${demo.title}`);
    console.log('─'.repeat(50));
    
    try {
      const { text } = await ai.generate({
        model: vertexAI.model('gemini-1.0-pro'),
        prompt: demo.prompt,
        temperature: 0.7,
        maxOutputTokens: 200
      });
      
      console.log(`📤 الطلب: ${demo.prompt}`);
      console.log(`📥 الاستجابة: ${text}`);
      
    } catch (error) {
      console.log(`❌ خطأ: ${error.message}`);
    }
    
    console.log('\n');
    
    // توقف قصير بين العروض
    if (i < demos.length - 1) {
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  }
}

/**
 * عرض إحصائيات سريعة
 */
function showStats() {
  console.log('📊 ===== إحصائيات المشروع =====\n');
  console.log('🔹 عدد النماذج المدعومة: 8+ نماذج');
  console.log('🔹 اللغات المدعومة: العربية + متعدد اللغات');
  console.log('🔹 أنواع المهام: نصوص، صور، ترجمة، تحليل');
  console.log('🔹 واجهة المطور: متاحة على المنفذ 4000');
  console.log('🔹 أمثلة متقدمة: 9 ملفات متخصصة\n');
}

/**
 * عرض الخطوات التالية
 */
function showNextSteps() {
  console.log('🎯 ===== الخطوات التالية =====\n');
  console.log('1️⃣ جرب الأمثلة المتقدمة:');
  console.log('   npm run vertex:arabic        # أمثلة عربية شاملة');
  console.log('   npm run vertex:practical     # تطبيقات عملية');
  console.log('   npm run vertex:multimodal    # معالجة الصور والنصوص');
  
  console.log('\n2️⃣ استخدم واجهة المطور:');
  console.log('   npm run dev                  # تشغيل واجهة المطور');
  console.log('   http://localhost:4000        # فتح الواجهة');
  
  console.log('\n3️⃣ طور تطبيقك الخاص:');
  console.log('   • استخدم الأمثلة كنقطة بداية');
  console.log('   • اقرأ التوثيق في README.md');
  console.log('   • جرب النماذج المختلفة');
  
  console.log('\n4️⃣ احصل على المساعدة:');
  console.log('   npm run info                 # معلومات المشروع');
  console.log('   npm run setup                # فحص الإعدادات\n');
}

/**
 * الدالة الرئيسية
 */
async function main() {
  try {
    await runQuickDemo();
    showStats();
    showNextSteps();
    
    console.log('✨ ===== انتهى العرض التوضيحي =====\n');
    console.log('🎉 شكراً لك على تجربة Malite Agent!');
    console.log('💫 نتطلع لرؤية ما ستبنيه معنا\n');
    
  } catch (error) {
    console.error('❌ خطأ في العرض التوضيحي:', error.message);
    console.log('\n🔧 تأكد من:');
    console.log('• وجود مفتاح GEMINI_API_KEY في ملف .env');
    console.log('• الاتصال بالإنترنت');
    console.log('• تشغيل: npm run setup للفحص\n');
  }
}

// تشغيل العرض التوضيحي
main();
