import * as Core from '@anthropic-ai/sdk/core';
import * as Resources from '@anthropic-ai/sdk/resources/index';
import * as API from '@anthropic-ai/sdk/index';
import { type RequestInit } from '@anthropic-ai/sdk/_shims/index';
import { GoogleAuth } from 'google-auth-library';
export type ClientOptions = Omit<API.ClientOptions, 'apiKey' | 'authToken'> & {
    region?: string | null | undefined;
    projectId?: string | null | undefined;
    accessToken?: string | null | undefined;
    /**
     * Override the default google auth config using the
     * [google-auth-library](https://www.npmjs.com/package/google-auth-library) package.
     *
     * Note that you'll likely have to set `scopes`, e.g.
     * ```ts
     * new GoogleAuth({ scopes: 'https://www.googleapis.com/auth/cloud-platform' })
     * ```
     */
    googleAuth?: GoogleAuth | null | undefined;
};
export declare class AnthropicVertex extends Core.APIClient {
    region: string;
    projectId: string | null;
    accessToken: string | null;
    private _options;
    private _auth;
    private _authClientPromise;
    /**
     * API Client for interfacing with the Anthropic Vertex API.
     *
     * @param {string | null} opts.accessToken
     * @param {string | null} opts.projectId
     * @param {GoogleAuth} opts.googleAuth - Override the default google auth config
     * @param {string | null} [opts.region=process.env['CLOUD_ML_REGION']]
     * @param {string} [opts.baseURL=process.env['ANTHROPIC_VERTEX__BASE_URL'] ?? https://${region}-aiplatform.googleapis.com/v1] - Override the default base URL for the API.
     * @param {number} [opts.timeout=10 minutes] - The maximum amount of time (in milliseconds) the client will wait for a response before timing out.
     * @param {number} [opts.httpAgent] - An HTTP agent used to manage HTTP(s) connections.
     * @param {Core.Fetch} [opts.fetch] - Specify a custom `fetch` function implementation.
     * @param {number} [opts.maxRetries=2] - The maximum number of times the client will retry a request.
     * @param {Core.Headers} opts.defaultHeaders - Default headers to include with every request to the API.
     * @param {Core.DefaultQuery} opts.defaultQuery - Default query parameters to include with every request to the API.
     */
    constructor({ baseURL, region, projectId, ...opts }?: ClientOptions);
    messages: Resources.Messages;
    protected defaultQuery(): Core.DefaultQuery | undefined;
    protected defaultHeaders(opts: Core.FinalRequestOptions): Core.Headers;
    protected prepareOptions(options: Core.FinalRequestOptions): Promise<void>;
    buildRequest(options: Core.FinalRequestOptions<unknown>): {
        req: RequestInit;
        url: string;
        timeout: number;
    };
}
//# sourceMappingURL=client.d.ts.map