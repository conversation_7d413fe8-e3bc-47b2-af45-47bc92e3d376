#!/usr/bin/env node

/**
 * معلومات المشروع وطرق التشغيل
 * يعرض معلومات شاملة عن مشروع Malite Agent
 */

import { readFileSync } from 'fs';
import { fileURLToPath } from 'url';
import path from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// قراءة معلومات المشروع من package.json
const packagePath = path.join(__dirname, '..', 'package.json');
const packageInfo = JSON.parse(readFileSync(packagePath, 'utf8'));

console.log('\n🚀 ===== معلومات مشروع Malite Agent =====\n');

console.log(`📦 اسم المشروع: ${packageInfo.name}`);
console.log(`📋 الوصف: ${packageInfo.description}`);
console.log(`🔢 الإصدار: ${packageInfo.version}`);
console.log(`👨‍💻 المطور: ${packageInfo.author}`);
console.log(`📄 الرخصة: ${packageInfo.license}`);

console.log('\n🛠️ ===== طرق تشغيل التطبيق =====\n');

console.log('🔹 التشغيل الأساسي:');
console.log('   npm start                    # تشغيل التطبيق الرئيسي');
console.log('   npm run dev                  # تشغيل مع واجهة المطور');
console.log('   npm run dev-open             # تشغيل مع فتح واجهة المطور تلقائياً');

console.log('\n🔹 أمثلة Vertex AI:');
console.log('   npm run vertex:basic         # الأمثلة الأساسية');
console.log('   npm run vertex:arabic        # الأمثلة العربية');
console.log('   npm run vertex:advanced      # الميزات المتقدمة');
console.log('   npm run vertex:practical     # التطبيقات العملية');
console.log('   npm run vertex:multimodal    # معالجة الوسائط المتعددة');
console.log('   npm run vertex:generative    # توليد النصوص الإبداعية');
console.log('   npm run vertex:nlp           # معالجة اللغة الطبيعية المتقدمة');

console.log('\n🔹 أوامر مجمعة:');
console.log('   npm run vertex:all           # تشغيل الأمثلة الأساسية');
console.log('   npm run demo                 # عرض توضيحي سريع');
console.log('   npm test                     # اختبار المشروع');

console.log('\n🔹 أدوات المساعدة:');
console.log('   npm run info                 # عرض معلومات المشروع (هذا الأمر)');
console.log('   npm run setup                # فحص إعدادات المشروع');

console.log('\n📚 ===== التبعيات المثبتة =====\n');

Object.entries(packageInfo.dependencies).forEach(([name, version]) => {
  console.log(`   ${name}: ${version}`);
});

console.log('\n🌐 ===== الروابط المفيدة =====\n');

console.log('📖 توثيق Genkit: https://genkit.dev/docs/');
console.log('🔗 Vertex AI Plugin: https://genkit.dev/docs/plugins/vertex-ai/');
console.log('☁️ Google Cloud Console: https://console.cloud.google.com/');
console.log('🤖 Vertex AI: https://cloud.google.com/vertex-ai/docs');

console.log('\n💡 ===== نصائح الاستخدام =====\n');

console.log('• تأكد من وجود مفتاح GEMINI_API_KEY في ملف .env');
console.log('• استخدم npm run dev لتشغيل واجهة المطور والتصحيح');
console.log('• جرب npm run vertex:arabic للأمثلة العربية المتقدمة');
console.log('• استخدم npm run setup للتحقق من صحة الإعدادات');

console.log('\n✨ ===== انتهى عرض المعلومات =====\n');
