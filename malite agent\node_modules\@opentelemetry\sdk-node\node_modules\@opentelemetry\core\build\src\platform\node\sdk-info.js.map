{"version": 3, "file": "sdk-info.js", "sourceRoot": "", "sources": ["../../../../src/platform/node/sdk-info.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,2CAAwC;AACxC,8EAM6C;AAE7C,0CAA0C;AAC7B,QAAA,QAAQ,GAAG;IACtB,CAAC,qDAA8B,CAAC,EAAE,eAAe;IACjD,CAAC,uDAAgC,CAAC,EAAE,MAAM;IAC1C,CAAC,yDAAkC,CAAC,EAAE,wDAAiC;IACvE,CAAC,wDAAiC,CAAC,EAAE,iBAAO;CAC7C,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { VERSION } from '../../version';\nimport {\n  SEMRESATTRS_TELEMETRY_SDK_NAME,\n  SEMRESATTRS_PROCESS_RUNTIME_NAME,\n  SEMRESATTRS_TELEMETRY_SDK_LANGUAGE,\n  TELEMETRYSDKLANGUAGEVALUES_NODEJS,\n  SEMRESATTRS_TELEMETRY_SDK_VERSION,\n} from '@opentelemetry/semantic-conventions';\n\n/** Constants describing the SDK in use */\nexport const SDK_INFO = {\n  [SEMRESATTRS_TELEMETRY_SDK_NAME]: 'opentelemetry',\n  [SEMRESATTRS_PROCESS_RUNTIME_NAME]: 'node',\n  [SEMRESATTRS_TELEMETRY_SDK_LANGUAGE]: TELEMETRYSDKLANGUAGEVALUES_NODEJS,\n  [SEMRESATTRS_TELEMETRY_SDK_VERSION]: VERSION,\n};\n"]}