{"version": 3, "file": "ConsoleMetricExporter.js", "sourceRoot": "", "sources": ["../../../src/export/ConsoleMetricExporter.ts"], "names": [], "mappings": ";;;;;;;;;;;AAAA;;;;;;;;;;;;;;GAcG;AACH,OAAO,EAAgB,gBAAgB,EAAE,MAAM,qBAAqB,CAAC;AAKrE,OAAO,EAEL,wCAAwC,GACzC,MAAM,uBAAuB,CAAC;AAM/B,+BAA+B;AAC/B;IAIE,+BAAY,OAAsC;;QAHxC,cAAS,GAAG,KAAK,CAAC;QAI1B,IAAI,CAAC,oBAAoB;YACvB,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,mBAAmB,mCAAI,wCAAwC,CAAC;IAC7E,CAAC;IAED,sCAAM,GAAN,UACE,OAAwB,EACxB,cAA8C;QAE9C,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,uFAAuF;YACvF,YAAY,CAAC,cAAc,EAAE,EAAE,IAAI,EAAE,gBAAgB,CAAC,MAAM,EAAE,CAAC,CAAC;YAChE,OAAO;SACR;QAED,OAAO,qBAAqB,CAAC,YAAY,CAAC,OAAO,EAAE,cAAc,CAAC,CAAC;IACrE,CAAC;IAED,0CAAU,GAAV;QACE,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;IAC3B,CAAC;IAED,4DAA4B,GAA5B,UACE,eAA+B;QAE/B,OAAO,IAAI,CAAC,oBAAoB,CAAC,eAAe,CAAC,CAAC;IACpD,CAAC;IAED,wCAAQ,GAAR;QACE,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QACtB,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;IAC3B,CAAC;IAEc,kCAAY,GAA3B,UACE,OAAwB,EACxB,IAAoC;;;YAEpC,KAA2B,IAAA,KAAA,SAAA,OAAO,CAAC,YAAY,CAAA,gBAAA,4BAAE;gBAA5C,IAAM,YAAY,WAAA;;oBACrB,KAAqB,IAAA,oBAAA,SAAA,YAAY,CAAC,OAAO,CAAA,CAAA,gBAAA,4BAAE;wBAAtC,IAAM,MAAM,WAAA;wBACf,OAAO,CAAC,GAAG,CACT;4BACE,UAAU,EAAE,MAAM,CAAC,UAAU;4BAC7B,aAAa,EAAE,MAAM,CAAC,aAAa;4BACnC,UAAU,EAAE,MAAM,CAAC,UAAU;yBAC9B,EACD,EAAE,KAAK,EAAE,IAAI,EAAE,CAChB,CAAC;qBACH;;;;;;;;;aACF;;;;;;;;;QAED,IAAI,CAAC,EAAE,IAAI,EAAE,gBAAgB,CAAC,OAAO,EAAE,CAAC,CAAC;IAC3C,CAAC;IACH,4BAAC;AAAD,CAAC,AAxDD,IAwDC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { ExportResult, ExportResultCode } from '@opentelemetry/core';\nimport { InstrumentType } from '../InstrumentDescriptor';\nimport { AggregationTemporality } from './AggregationTemporality';\nimport { ResourceMetrics } from './MetricData';\nimport { PushMetricExporter } from './MetricExporter';\nimport {\n  AggregationTemporalitySelector,\n  DEFAULT_AGGREGATION_TEMPORALITY_SELECTOR,\n} from './AggregationSelector';\n\ninterface ConsoleMetricExporterOptions {\n  temporalitySelector?: AggregationTemporalitySelector;\n}\n\n/* eslint-disable no-console */\nexport class ConsoleMetricExporter implements PushMetricExporter {\n  protected _shutdown = false;\n  protected _temporalitySelector: AggregationTemporalitySelector;\n\n  constructor(options?: ConsoleMetricExporterOptions) {\n    this._temporalitySelector =\n      options?.temporalitySelector ?? DEFAULT_AGGREGATION_TEMPORALITY_SELECTOR;\n  }\n\n  export(\n    metrics: ResourceMetrics,\n    resultCallback: (result: ExportResult) => void\n  ): void {\n    if (this._shutdown) {\n      // If the exporter is shutting down, by spec, we need to return FAILED as export result\n      setImmediate(resultCallback, { code: ExportResultCode.FAILED });\n      return;\n    }\n\n    return ConsoleMetricExporter._sendMetrics(metrics, resultCallback);\n  }\n\n  forceFlush(): Promise<void> {\n    return Promise.resolve();\n  }\n\n  selectAggregationTemporality(\n    _instrumentType: InstrumentType\n  ): AggregationTemporality {\n    return this._temporalitySelector(_instrumentType);\n  }\n\n  shutdown(): Promise<void> {\n    this._shutdown = true;\n    return Promise.resolve();\n  }\n\n  private static _sendMetrics(\n    metrics: ResourceMetrics,\n    done: (result: ExportResult) => void\n  ): void {\n    for (const scopeMetrics of metrics.scopeMetrics) {\n      for (const metric of scopeMetrics.metrics) {\n        console.dir(\n          {\n            descriptor: metric.descriptor,\n            dataPointType: metric.dataPointType,\n            dataPoints: metric.dataPoints,\n          },\n          { depth: null }\n        );\n      }\n    }\n\n    done({ code: ExportResultCode.SUCCESS });\n  }\n}\n"]}