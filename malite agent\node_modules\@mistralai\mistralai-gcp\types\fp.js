"use strict";
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.OK = OK;
exports.ERR = ERR;
exports.unwrap = unwrap;
exports.unwrapAsync = unwrapAsync;
function OK(value) {
    return { ok: true, value };
}
function ERR(error) {
    return { ok: false, error };
}
/**
 * unwrap is a convenience function for extracting a value from a result or
 * throwing if there was an error.
 */
function unwrap(r) {
    if (!r.ok) {
        throw r.error;
    }
    return r.value;
}
/**
 * unwrapAsync is a convenience function for resolving a value from a Promise
 * of a result or rejecting if an error occurred.
 */
async function unwrapAsync(pr) {
    const r = await pr;
    if (!r.ok) {
        throw r.error;
    }
    return r.value;
}
//# sourceMappingURL=fp.js.map