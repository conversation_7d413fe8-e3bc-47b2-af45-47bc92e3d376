# 🚀 مشروع Malite Agent - Genkit للذكاء الاصطناعي

مشروع متقدم يستخدم **Google Genkit** مع **Vertex AI** لتطوير تطبيقات الذكاء الاصطناعي باللغة العربية.

## ✨ المميزات الرئيسية

- 🌍 **دعم كامل للغة العربية** - جميع الأمثلة والتوثيق
- 🤖 **نماذج متعددة** - Gemini, <PERSON>, PaLM, Llama
- 🛠️ **واجهة مطور تفاعلية** - Genkit Developer UI
- 📚 **أمثلة شاملة** - 13+ ملف متخصص
- ⚡ **أداء متقدم** - معالجة أخطاء وأدوات مساعدة
- 🎯 **جاهز للإنتاج** - بنية منظمة وقابلة للتوسع

## 📋 المتطلبات

- **Node.js** (الإصدار 18 أو أعلى)
- **مفتاح Google AI API** (مطلوب)
- **حساب Google Cloud** (اختياري لـ Vertex AI)

## 🔧 التثبيت والإعداد

### 1. تثبيت التبعيات
```bash
npm install
```

### 2. إعداد متغيرات البيئة
أنشئ ملف `.env` وأضف:
```env
GEMINI_API_KEY=your_api_key_here
```

### 3. فحص الإعدادات
```bash
npm run setup
```

## 🚀 طرق التشغيل

### 🔹 التشغيل الأساسي
```bash
# تشغيل التطبيق الرئيسي
npm start

# عرض توضيحي سريع
npm run demo

# عرض معلومات المشروع
npm run info
```

### 🔹 واجهة المطور
```bash
# تشغيل مع واجهة المطور
npm run dev

# تشغيل مع فتح المتصفح تلقائياً
npm run dev-open
```
**واجهة المطور متاحة على:** http://localhost:4000

### 🔹 الأمثلة المتخصصة
```bash
# أمثلة عربية متقدمة
npm run vertex:arabic

# تطبيقات عملية
npm run vertex:practical

# معالجة الوسائط المتعددة
npm run vertex:multimodal

# ميزات متقدمة
npm run vertex:advanced

# الأمثلة الأساسية
npm run vertex:basic

# تشغيل مجموعة من الأمثلة
npm run vertex:all
```

### 🔹 أوامر إضافية
```bash
# فحص صحة الإعدادات
npm run setup

# عرض توضيحي كامل
npm run demo:full

# اختبار المشروع
npm test
```

## 📁 هيكل المشروع

```
malite agent/
├── 📄 index.js                    # التطبيق الرئيسي
├── 📦 package.json                # إعدادات المشروع
├── 🔐 .env                        # متغيرات البيئة
├── 📚 README.md                   # التوثيق
├── 🛠️ scripts/                    # أدوات مساعدة
│   ├── project-info.js           # معلومات المشروع
│   ├── setup-check.js            # فحص الإعدادات
│   └── quick-demo.js              # عرض توضيحي
├── 🤖 vertex-ai/                  # أمثلة متقدمة
│   ├── index.js                   # أمثلة أساسية
│   ├── arabic-examples.js         # أمثلة عربية
│   ├── practical-applications.js # تطبيقات عملية
│   ├── advanced-features.js       # ميزات متقدمة
│   ├── multimodal-examples.js     # وسائط متعددة
│   ├── config.js                  # تكوين النماذج
│   ├── utils.js                   # أدوات مساعدة
│   ├── error-handler.js           # معالجة الأخطاء
│   └── [5+ ملفات أخرى]           # أمثلة متخصصة
└── 🗂️ .genkit/                    # بيانات Genkit
    ├── traces/                    # سجلات التتبع
    └── datasets/                  # مجموعات البيانات
```

## 🤖 النماذج المدعومة

| النموذج | الوصف | الاستخدام |
|---------|--------|-----------|
| **Gemini Pro** | نموذج متقدم للنصوص | `gemini-1.0-pro` |
| **Gemini Pro Vision** | معالجة الصور والنصوص | `gemini-1.0-pro-vision` |
| **Gemini 1.5 Pro** | أحدث إصدار | `gemini-1.5-pro` |
| **Claude Haiku** | سريع وفعال | `claude-3-haiku` |
| **Claude Sonnet** | متوازن | `claude-3-sonnet` |
| **Claude Opus** | الأكثر تقدماً | `claude-3-opus` |
| **PaLM Text** | توليد النصوص | `text-bison` |
| **Llama 3** | مفتوح المصدر | `llama3-70b` |

## 📚 أمثلة الاستخدام

### مثال بسيط
```javascript
import { genkit } from 'genkit';
import { vertexAI } from '@genkit-ai/vertexai';

const ai = genkit({
  plugins: [vertexAI()],
  model: vertexAI.model('gemini-1.0-pro'),
});

const { text } = await ai.generate('مرحباً، كيف حالك؟');
console.log(text);
```

### مثال متقدم
```javascript
const response = await ai.generate({
  model: vertexAI.model('gemini-1.0-pro'),
  prompt: 'لخص هذا النص...',
  temperature: 0.7,
  maxOutputTokens: 200
});
```

## 🎯 التطبيقات العملية

- **💬 روبوت محادثة عربي** - دردشة تفاعلية
- **📝 تلخيص النصوص** - تلخيص المقالات والوثائق
- **🔄 الترجمة الفورية** - ترجمة متعددة اللغات
- **😊 تحليل المشاعر** - تحليل النصوص العربية
- **🖼️ وصف الصور** - تحليل الصور باللغة العربية
- **🎨 الكتابة الإبداعية** - توليد المحتوى الإبداعي

## 🔧 استكشاف الأخطاء

### مشاكل شائعة وحلولها

**❌ خطأ في مفتاح API:**
```bash
npm run setup  # للتحقق من الإعدادات
```

**❌ مشكلة في التبعيات:**
```bash
rm -rf node_modules package-lock.json
npm install
```

**❌ مشكلة في واجهة المطور:**
```bash
npm run dev-open  # فتح تلقائي للمتصفح
```

## 📖 التوثيق والمراجع

- 📚 [توثيق Genkit الرسمي](https://genkit.dev/docs/)
- 🔗 [Vertex AI Plugin](https://genkit.dev/docs/plugins/vertex-ai/)
- ☁️ [Google Cloud Console](https://console.cloud.google.com/)
- 🤖 [Vertex AI Documentation](https://cloud.google.com/vertex-ai/docs)

## 🤝 المساهمة

نرحب بالمساهمات! يرجى:

1. فتح **Issue** لمناقشة التغييرات
2. إنشاء **Fork** من المشروع
3. إرسال **Pull Request** مع الوصف

## 📄 الرخصة

هذا المشروع مرخص تحت **MIT License** - انظر ملف [LICENSE](LICENSE) للتفاصيل.

## 🙏 شكر وتقدير

- **Google Genkit Team** - لإطار العمل الرائع
- **Google Cloud** - لخدمات Vertex AI
- **المجتمع العربي** - للدعم والمساهمات

---

**💫 مع تحيات فريق Malite Agent**

*"نحو مستقبل أفضل للذكاء الاصطناعي باللغة العربية"*
