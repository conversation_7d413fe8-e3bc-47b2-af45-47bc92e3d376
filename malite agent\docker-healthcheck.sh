#!/bin/sh

# Docker Health Check Script لتطبيق Malite Agent
# يتحقق من صحة التطبيق داخل الحاوية

set -e

# إعدادات فحص الصحة
HOST=${HOST:-localhost}
PORT=${PORT:-8080}
TIMEOUT=${HEALTH_CHECK_TIMEOUT:-5}

# فحص الاتصال بالخدمة
check_service() {
    if curl -f -s --max-time $TIMEOUT "http://$HOST:$PORT/health" > /dev/null 2>&1; then
        echo "✅ Service is healthy"
        return 0
    else
        echo "❌ Service health check failed"
        return 1
    fi
}

# فحص استخدام الذاكرة (بسيط)
check_memory() {
    # التحقق من أن العملية تعمل
    if pgrep -f "node.*server.js" > /dev/null; then
        echo "✅ Node.js process is running"
        return 0
    else
        echo "❌ Node.js process not found"
        return 1
    fi
}

# تشغيل جميع الفحوصات
main() {
    echo "🏥 Running Docker health check..."
    
    if check_memory && check_service; then
        echo "✅ All health checks passed"
        exit 0
    else
        echo "❌ Health check failed"
        exit 1
    fi
}

# تشغيل الفحص
main
