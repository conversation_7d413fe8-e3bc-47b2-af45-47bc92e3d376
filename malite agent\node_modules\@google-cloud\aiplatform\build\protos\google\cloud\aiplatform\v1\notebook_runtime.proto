// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.aiplatform.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/aiplatform/v1/encryption_spec.proto";
import "google/cloud/aiplatform/v1/machine_resources.proto";
import "google/cloud/aiplatform/v1/network_spec.proto";
import "google/cloud/aiplatform/v1/notebook_euc_config.proto";
import "google/cloud/aiplatform/v1/notebook_idle_shutdown_config.proto";
import "google/cloud/aiplatform/v1/notebook_runtime_template_ref.proto";
import "google/cloud/aiplatform/v1/notebook_software_config.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.AIPlatform.V1";
option go_package = "cloud.google.com/go/aiplatform/apiv1/aiplatformpb;aiplatformpb";
option java_multiple_files = true;
option java_outer_classname = "NotebookRuntimeProto";
option java_package = "com.google.cloud.aiplatform.v1";
option php_namespace = "Google\\Cloud\\AIPlatform\\V1";
option ruby_package = "Google::Cloud::AIPlatform::V1";

// Represents a notebook runtime type.
enum NotebookRuntimeType {
  // Unspecified notebook runtime type, NotebookRuntimeType will default to
  // USER_DEFINED.
  NOTEBOOK_RUNTIME_TYPE_UNSPECIFIED = 0;

  // runtime or template with coustomized configurations from user.
  USER_DEFINED = 1;

  // runtime or template with system defined configurations.
  ONE_CLICK = 2;
}

// A template that specifies runtime configurations such as machine type,
// runtime version, network configurations, etc.
// Multiple runtimes can be created from a runtime template.
message NotebookRuntimeTemplate {
  option (google.api.resource) = {
    type: "aiplatform.googleapis.com/NotebookRuntimeTemplate"
    pattern: "projects/{project}/locations/{location}/notebookRuntimeTemplates/{notebook_runtime_template}"
  };

  // The resource name of the NotebookRuntimeTemplate.
  string name = 1;

  // Required. The display name of the NotebookRuntimeTemplate.
  // The name can be up to 128 characters long and can consist of any UTF-8
  // characters.
  string display_name = 2 [(google.api.field_behavior) = REQUIRED];

  // The description of the NotebookRuntimeTemplate.
  string description = 3;

  // Output only. Deprecated: This field has no behavior. Use
  // notebook_runtime_type = 'ONE_CLICK' instead.
  //
  // The default template to use if not specified.
  bool is_default = 4
      [deprecated = true, (google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Immutable. The specification of a single machine for the
  // template.
  MachineSpec machine_spec = 5 [
    (google.api.field_behavior) = OPTIONAL,
    (google.api.field_behavior) = IMMUTABLE
  ];

  // Optional. The specification of [persistent
  // disk][https://cloud.google.com/compute/docs/disks/persistent-disks]
  // attached to the runtime as data disk storage.
  PersistentDiskSpec data_persistent_disk_spec = 8
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. Network spec.
  NetworkSpec network_spec = 12 [(google.api.field_behavior) = OPTIONAL];

  // Deprecated: This field is ignored and the "Vertex AI Notebook Service
  // Account"
  // (<EMAIL>) is
  // used for the runtime workload identity.
  // See
  // https://cloud.google.com/iam/docs/service-agents#vertex-ai-notebook-service-account
  // for more details.
  // For NotebookExecutionJob, use NotebookExecutionJob.service_account instead.
  //
  // The service account that the runtime workload runs as.
  // You can use any service account within the same project, but you
  // must have the service account user permission to use the instance.
  //
  // If not specified, the [Compute Engine default service
  // account](https://cloud.google.com/compute/docs/access/service-accounts#default_service_account)
  // is used.
  string service_account = 13 [deprecated = true];

  // Used to perform consistent read-modify-write updates. If not set, a blind
  // "overwrite" update happens.
  string etag = 14;

  // The labels with user-defined metadata to organize the
  // NotebookRuntimeTemplates.
  //
  // Label keys and values can be no longer than 64 characters
  // (Unicode codepoints), can only contain lowercase letters, numeric
  // characters, underscores and dashes. International characters are allowed.
  //
  // See https://goo.gl/xmQnxf for more information and examples of labels.
  map<string, string> labels = 15;

  // The idle shutdown configuration of NotebookRuntimeTemplate. This config
  // will only be set when idle shutdown is enabled.
  NotebookIdleShutdownConfig idle_shutdown_config = 17;

  // EUC configuration of the NotebookRuntimeTemplate.
  NotebookEucConfig euc_config = 18;

  // Output only. Timestamp when this NotebookRuntimeTemplate was created.
  google.protobuf.Timestamp create_time = 10
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Timestamp when this NotebookRuntimeTemplate was most recently
  // updated.
  google.protobuf.Timestamp update_time = 11
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Immutable. The type of the notebook runtime template.
  NotebookRuntimeType notebook_runtime_type = 19 [
    (google.api.field_behavior) = OPTIONAL,
    (google.api.field_behavior) = IMMUTABLE
  ];

  // Optional. Immutable. Runtime Shielded VM spec.
  ShieldedVmConfig shielded_vm_config = 20 [
    (google.api.field_behavior) = OPTIONAL,
    (google.api.field_behavior) = IMMUTABLE
  ];

  // Optional. The Compute Engine tags to add to runtime (see [Tagging
  // instances](https://cloud.google.com/vpc/docs/add-remove-network-tags)).
  repeated string network_tags = 21 [(google.api.field_behavior) = OPTIONAL];

  // Customer-managed encryption key spec for the notebook runtime.
  EncryptionSpec encryption_spec = 23;

  // Optional. The notebook software configuration of the notebook runtime.
  NotebookSoftwareConfig software_config = 24
      [(google.api.field_behavior) = OPTIONAL];
}

// A runtime is a virtual machine allocated to a particular user for a
// particular Notebook file on temporary basis with lifetime limited to 24
// hours.
message NotebookRuntime {
  option (google.api.resource) = {
    type: "aiplatform.googleapis.com/NotebookRuntime"
    pattern: "projects/{project}/locations/{location}/notebookRuntimes/{notebook_runtime}"
  };

  // The substate of the NotebookRuntime to display health information.
  enum HealthState {
    // Unspecified health state.
    HEALTH_STATE_UNSPECIFIED = 0;

    // NotebookRuntime is in healthy state. Applies to ACTIVE state.
    HEALTHY = 1;

    // NotebookRuntime is in unhealthy state. Applies to ACTIVE state.
    UNHEALTHY = 2;
  }

  // The substate of the NotebookRuntime to display state of runtime.
  // The resource of NotebookRuntime is in ACTIVE state for these sub state.
  enum RuntimeState {
    // Unspecified runtime state.
    RUNTIME_STATE_UNSPECIFIED = 0;

    // NotebookRuntime is in running state.
    RUNNING = 1;

    // NotebookRuntime is in starting state.
    BEING_STARTED = 2;

    // NotebookRuntime is in stopping state.
    BEING_STOPPED = 3;

    // NotebookRuntime is in stopped state.
    STOPPED = 4;

    // NotebookRuntime is in upgrading state. It is in the middle of upgrading
    // process.
    BEING_UPGRADED = 5;

    // NotebookRuntime was unable to start/stop properly.
    ERROR = 100;

    // NotebookRuntime is in invalid state. Cannot be recovered.
    INVALID = 101;
  }

  // Output only. The resource name of the NotebookRuntime.
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. The user email of the NotebookRuntime.
  string runtime_user = 2 [(google.api.field_behavior) = REQUIRED];

  // Output only. The pointer to NotebookRuntimeTemplate this NotebookRuntime is
  // created from.
  NotebookRuntimeTemplateRef notebook_runtime_template_ref = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The proxy endpoint used to access the NotebookRuntime.
  string proxy_uri = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Timestamp when this NotebookRuntime was created.
  google.protobuf.Timestamp create_time = 6
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Timestamp when this NotebookRuntime was most recently updated.
  google.protobuf.Timestamp update_time = 7
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The health state of the NotebookRuntime.
  HealthState health_state = 8 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. The display name of the NotebookRuntime.
  // The name can be up to 128 characters long and can consist of any UTF-8
  // characters.
  string display_name = 10 [(google.api.field_behavior) = REQUIRED];

  // The description of the NotebookRuntime.
  string description = 11;

  // Output only. Deprecated: This field is no longer used and the "Vertex AI
  // Notebook Service Account"
  // (<EMAIL>) is
  // used for the runtime workload identity.
  // See
  // https://cloud.google.com/iam/docs/service-agents#vertex-ai-notebook-service-account
  // for more details.
  //
  // The service account that the NotebookRuntime workload runs as.
  string service_account = 13 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The runtime (instance) state of the NotebookRuntime.
  RuntimeState runtime_state = 14 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Whether NotebookRuntime is upgradable.
  bool is_upgradable = 15 [(google.api.field_behavior) = OUTPUT_ONLY];

  // The labels with user-defined metadata to organize your
  // NotebookRuntime.
  //
  // Label keys and values can be no longer than 64 characters
  // (Unicode codepoints), can only contain lowercase letters, numeric
  // characters, underscores and dashes. International characters are allowed.
  // No more than 64 user labels can be associated with one NotebookRuntime
  // (System labels are excluded).
  //
  // See https://goo.gl/xmQnxf for more information and examples of labels.
  // System reserved label keys are prefixed with "aiplatform.googleapis.com/"
  // and are immutable. Following system labels exist for NotebookRuntime:
  //
  // * "aiplatform.googleapis.com/notebook_runtime_gce_instance_id": output
  // only, its value is the Compute Engine instance id.
  // * "aiplatform.googleapis.com/colab_enterprise_entry_service": its value is
  // either "bigquery" or "vertex"; if absent, it should be "vertex". This is to
  // describe the entry service, either BigQuery or Vertex.
  map<string, string> labels = 16;

  // Output only. Timestamp when this NotebookRuntime will be expired:
  // 1. System Predefined NotebookRuntime: 24 hours after creation. After
  // expiration, system predifined runtime will be deleted.
  // 2. User created NotebookRuntime: 6 months after last upgrade. After
  // expiration, user created runtime will be stopped and allowed for upgrade.
  google.protobuf.Timestamp expiration_time = 17
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The VM os image version of NotebookRuntime.
  string version = 18 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The type of the notebook runtime.
  NotebookRuntimeType notebook_runtime_type = 19
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The specification of a single machine used by the notebook
  // runtime.
  MachineSpec machine_spec = 20 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The specification of [persistent
  // disk][https://cloud.google.com/compute/docs/disks/persistent-disks]
  // attached to the notebook runtime as data disk storage.
  PersistentDiskSpec data_persistent_disk_spec = 21
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Network spec of the notebook runtime.
  NetworkSpec network_spec = 22 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The idle shutdown configuration of the notebook runtime.
  NotebookIdleShutdownConfig idle_shutdown_config = 23
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. EUC configuration of the notebook runtime.
  NotebookEucConfig euc_config = 24 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Runtime Shielded VM spec.
  ShieldedVmConfig shielded_vm_config = 32
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. The Compute Engine tags to add to runtime (see [Tagging
  // instances](https://cloud.google.com/vpc/docs/add-remove-network-tags)).
  repeated string network_tags = 25 [(google.api.field_behavior) = OPTIONAL];

  // Output only. Software config of the notebook runtime.
  NotebookSoftwareConfig software_config = 31
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Customer-managed encryption key spec for the notebook runtime.
  EncryptionSpec encryption_spec = 28
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Reserved for future use.
  bool satisfies_pzs = 29 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Reserved for future use.
  bool satisfies_pzi = 30 [(google.api.field_behavior) = OUTPUT_ONLY];
}
