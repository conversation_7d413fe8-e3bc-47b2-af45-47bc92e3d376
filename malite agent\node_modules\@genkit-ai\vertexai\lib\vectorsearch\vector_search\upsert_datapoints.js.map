{"version": 3, "sources": ["../../../src/vectorsearch/vector_search/upsert_datapoints.ts"], "sourcesContent": ["/**\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { GoogleAuth } from 'google-auth-library';\nimport { IIndexDatapoint } from './types';\n\ninterface UpsertDatapointsParams {\n  datapoints: IIndexDatapoint[];\n  authClient: GoogleAuth;\n  projectId: string;\n  location: string;\n  indexId: string;\n}\n\n/**\n * Upserts datapoints into a specified index.\n *\n * This function sends a request to the Google AI Platform to upsert datapoints\n * into a specified index using the provided parameters.\n *\n * @param {UpsertDatapointsParams} params - The parameters required to upsert datapoints.\n * @param {IIndexDatapoint[]} params.datapoints - The datapoints to be upserted.\n * @param {GoogleAuth} params.authClient - The GoogleAuth client for authorization.\n * @param {string} params.projectId - The ID of the Google Cloud project.\n * @param {string} params.location - The location of the AI Platform index.\n * @param {string} params.indexId - The ID of the index.\n * @returns {Promise<void>} - A promise that resolves when the upsert is complete.\n * @throws {Error} - Throws an error if the upsert fails.\n */\nexport async function upsertDatapoints(\n  params: UpsertDatapointsParams\n): Promise<void> {\n  const { datapoints, authClient, projectId, location, indexId } = params;\n  const accessToken = await authClient.getAccessToken();\n  const url = `https://${location}-aiplatform.googleapis.com/v1/projects/${projectId}/locations/${location}/indexes/${indexId}:upsertDatapoints`;\n\n  const requestBody = {\n    datapoints: datapoints.map((dp) => {\n      const newDp: Record<string, unknown> = {\n        datapoint_id: dp.datapointId,\n        feature_vector: dp.featureVector,\n      };\n      if (dp.restricts) {\n        newDp.restricts =\n          dp.restricts?.map((r) => ({\n            namespace: r.namespace,\n            allow_list: r.allowList,\n            deny_list: r.denyList,\n          })) || [];\n      }\n      if (dp.numericRestricts) {\n        newDp.numeric_restricts =\n          dp.numericRestricts?.map((nr) => {\n            const newNR: Record<string, unknown> = {\n              namespace: nr.namespace,\n            };\n            if (nr.valueInt) {\n              newNR.value_int = nr.valueInt;\n            }\n            if (nr.valueFloat) {\n              newNR.value_float = nr.valueFloat;\n            }\n            if (nr.valueDouble) {\n              newNR.value_double = nr.valueDouble;\n            }\n            return newNR;\n          }) || [];\n      }\n      if (dp.crowdingTag) {\n        newDp.crowding_tag = dp.crowdingTag;\n      }\n      return newDp;\n    }),\n  };\n\n  const response = await fetch(url, {\n    method: 'POST',\n    headers: {\n      'Content-Type': 'application/json',\n      Authorization: `Bearer ${accessToken}`,\n    },\n    body: JSON.stringify(requestBody),\n  });\n\n  if (!response.ok) {\n    const errMsg = (await response.json()).error?.message || '';\n    throw new Error(\n      `Error upserting datapoints into index ${indexId}: ${response.statusText}. ${errMsg}`\n    );\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AA0CA,eAAsB,iBACpB,QACe;AACf,QAAM,EAAE,YAAY,YAAY,WAAW,UAAU,QAAQ,IAAI;AACjE,QAAM,cAAc,MAAM,WAAW,eAAe;AACpD,QAAM,MAAM,WAAW,QAAQ,0CAA0C,SAAS,cAAc,QAAQ,YAAY,OAAO;AAE3H,QAAM,cAAc;AAAA,IAClB,YAAY,WAAW,IAAI,CAAC,OAAO;AACjC,YAAM,QAAiC;AAAA,QACrC,cAAc,GAAG;AAAA,QACjB,gBAAgB,GAAG;AAAA,MACrB;AACA,UAAI,GAAG,WAAW;AAChB,cAAM,YACJ,GAAG,WAAW,IAAI,CAAC,OAAO;AAAA,UACxB,WAAW,EAAE;AAAA,UACb,YAAY,EAAE;AAAA,UACd,WAAW,EAAE;AAAA,QACf,EAAE,KAAK,CAAC;AAAA,MACZ;AACA,UAAI,GAAG,kBAAkB;AACvB,cAAM,oBACJ,GAAG,kBAAkB,IAAI,CAAC,OAAO;AAC/B,gBAAM,QAAiC;AAAA,YACrC,WAAW,GAAG;AAAA,UAChB;AACA,cAAI,GAAG,UAAU;AACf,kBAAM,YAAY,GAAG;AAAA,UACvB;AACA,cAAI,GAAG,YAAY;AACjB,kBAAM,cAAc,GAAG;AAAA,UACzB;AACA,cAAI,GAAG,aAAa;AAClB,kBAAM,eAAe,GAAG;AAAA,UAC1B;AACA,iBAAO;AAAA,QACT,CAAC,KAAK,CAAC;AAAA,MACX;AACA,UAAI,GAAG,aAAa;AAClB,cAAM,eAAe,GAAG;AAAA,MAC1B;AACA,aAAO;AAAA,IACT,CAAC;AAAA,EACH;AAEA,QAAM,WAAW,MAAM,MAAM,KAAK;AAAA,IAChC,QAAQ;AAAA,IACR,SAAS;AAAA,MACP,gBAAgB;AAAA,MAChB,eAAe,UAAU,WAAW;AAAA,IACtC;AAAA,IACA,MAAM,KAAK,UAAU,WAAW;AAAA,EAClC,CAAC;AAED,MAAI,CAAC,SAAS,IAAI;AAChB,UAAM,UAAU,MAAM,SAAS,KAAK,GAAG,OAAO,WAAW;AACzD,UAAM,IAAI;AAAA,MACR,yCAAyC,OAAO,KAAK,SAAS,UAAU,KAAK,MAAM;AAAA,IACrF;AAAA,EACF;AACF;", "names": []}