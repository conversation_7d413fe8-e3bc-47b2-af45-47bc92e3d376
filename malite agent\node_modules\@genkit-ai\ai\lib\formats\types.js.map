{"version": 3, "sources": ["../../src/formats/types.ts"], "sourcesContent": ["/**\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { JSONSchema } from '@genkit-ai/core';\nimport { GenerateResponseChunk } from '../generate.js';\nimport { Message } from '../message.js';\nimport { ModelRequest } from '../model.js';\n\nexport type OutputContentTypes = 'application/json' | 'text/plain';\n\nexport interface Formatter<O = unknown, CO = unknown> {\n  name: string;\n  config: ModelRequest['output'] & {\n    defaultInstructions?: false;\n  };\n  handler: (schema?: JSONSchema) => {\n    parseMessage(message: Message): O;\n    parseChunk?: (chunk: GenerateResponseChunk) => CO;\n    instructions?: string;\n  };\n}\n"], "mappings": ";;;;;;;;;;;;;;AAAA;AAAA;", "names": []}