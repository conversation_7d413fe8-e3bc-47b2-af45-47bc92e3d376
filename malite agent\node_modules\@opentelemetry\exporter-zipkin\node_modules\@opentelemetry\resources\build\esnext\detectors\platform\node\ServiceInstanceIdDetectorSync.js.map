{"version": 3, "file": "ServiceInstanceIdDetectorSync.js", "sourceRoot": "", "sources": ["../../../../../src/detectors/platform/node/ServiceInstanceIdDetectorSync.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAEH,OAAO,EAAE,+BAA+B,EAAE,MAAM,qCAAqC,CAAC;AACtF,OAAO,EAAE,QAAQ,EAAE,MAAM,mBAAmB,CAAC;AAG7C,OAAO,EAAE,UAAU,EAAE,MAAM,QAAQ,CAAC;AAEpC;;GAEG;AACH,MAAM,6BAA6B;IACjC,MAAM,CAAC,OAAiC;QACtC,MAAM,UAAU,GAAuB;YACrC,CAAC,+BAA+B,CAAC,EAAE,UAAU,EAAE;SAChD,CAAC;QAEF,OAAO,IAAI,QAAQ,CAAC,UAAU,CAAC,CAAC;IAClC,CAAC;CACF;AAED;;GAEG;AACH,MAAM,CAAC,MAAM,6BAA6B,GACxC,IAAI,6BAA6B,EAAE,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { SEMRESATTRS_SERVICE_INSTANCE_ID } from '@opentelemetry/semantic-conventions';\nimport { Resource } from '../../../Resource';\nimport { DetectorSync, ResourceAttributes } from '../../../types';\nimport { ResourceDetectionConfig } from '../../../config';\nimport { randomUUID } from 'crypto';\n\n/**\n * ServiceInstanceIdDetectorSync detects the resources related to the service instance ID.\n */\nclass ServiceInstanceIdDetectorSync implements DetectorSync {\n  detect(_config?: ResourceDetectionConfig): Resource {\n    const attributes: ResourceAttributes = {\n      [SEMRESATTRS_SERVICE_INSTANCE_ID]: randomUUID(),\n    };\n\n    return new Resource(attributes);\n  }\n}\n\n/**\n * @experimental\n */\nexport const serviceInstanceIdDetectorSync =\n  new ServiceInstanceIdDetectorSync();\n"]}