import { Exception } from '@opentelemetry/api';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from './types';
/**
 * Set the global error handler
 * @param {<PERSON>rror<PERSON><PERSON><PERSON>} handler
 */
export declare function setGlobalErrorHandler(handler: ErrorHandler): void;
/**
 * Return the global error handler
 * @param {Exception} ex
 */
export declare function globalErrorHandler(ex: Exception): void;
//# sourceMappingURL=global-error-handler.d.ts.map