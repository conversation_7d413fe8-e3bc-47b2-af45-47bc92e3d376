# دليل استخدام نماذج Vertex AI Model Garden

هذا الدليل يوضح كيفية استخدام النماذج المختلفة المتاحة في Vertex AI Model Garden مع مشروع Malite Agent.

## النماذج المتاحة

### 1. نماذج النصوص (Text Models)

#### نماذج Gemini (Google)
- **gemini.pro** - Gemini 1.0 Pro (الأساسي)
- **gemini.ultra** - Gemini 1.0 Ultra (الأقوى)
- **gemini.flash** - Gemini 1.5 Flash (سريع)
- **gemini.pro15** - Gemini 1.5 Pro (متطور)
- **gemini.flash8b** - Gemini 1.5 Flash 8B (خفيف)
- **gemini.pro002** - Gemini 1.5 Pro 002 (الأحدث)

#### نماذج Claude (Anthropic)
- **claude.haiku** - <PERSON> 3 Haiku (سريع ورخيص)
- **claude.sonnet** - <PERSON> 3 Sonnet (متوازن)
- **claude.opus** - <PERSON> 3 Opus (الأقوى)
- **claude.sonnet35** - <PERSON> 3.5 Sonnet (محسن)
- **claude.sonnet35v2** - Claude 3.5 Sonnet v2 (الأحدث)
- **claude.haiku35** - Claude 3.5 Haiku (سريع ومحسن)

#### نماذج Llama (Meta)
- **llama.llama3_8b** - Llama 3 8B
- **llama.llama3_70b** - Llama 3 70B
- **llama.llama31_8b** - Llama 3.1 8B
- **llama.llama31_70b** - Llama 3.1 70B
- **llama.llama31_405b** - Llama 3.1 405B (الأكبر)
- **llama.llama32_1b** - Llama 3.2 1B (صغير)
- **llama.llama32_3b** - Llama 3.2 3B

#### نماذج Mistral (Mistral AI)
- **mistral.mistral7b** - Mistral 7B
- **mistral.mixtral8x7b** - Mixtral 8x7B
- **mistral.mixtral8x22b** - Mixtral 8x22B
- **mistral.mistralLarge** - Mistral Large
- **mistral.mistralNemo** - Mistral Nemo
- **mistral.codestral** - Codestral (للبرمجة)

#### نماذج Cohere
- **cohere.command** - Command
- **cohere.commandLight** - Command Light
- **cohere.commandR** - Command R
- **cohere.commandRPlus** - Command R+

### 2. نماذج الرؤية (Vision Models)

- **geminiVision** - Gemini 1.0 Pro Vision
- **geminiVision15** - Gemini 1.5 Pro Vision
- **geminiFlashVision** - Gemini 1.5 Flash Vision
- **claudeSonnetVision** - Claude 3 Sonnet Vision
- **claudeOpusVision** - Claude 3 Opus Vision
- **claudeHaikuVision** - Claude 3 Haiku Vision
- **llama32Vision11b** - Llama 3.2 11B Vision
- **llama32Vision90b** - Llama 3.2 90B Vision

### 3. نماذج التضمين (Embedding Models)

- **textEmbedding** - Text Embedding Gecko
- **textEmbedding004** - Text Embedding Gecko 004
- **textEmbeddingMultilingual** - Text Embedding Multilingual
- **multimodalEmbedding** - Multimodal Embedding
- **textEmbeddingPreview** - Text Embedding Preview
- **textMultilingualEmbedding** - Text Multilingual Embedding 002

### 4. النماذج المتخصصة (Specialized Models)

#### توليد الكود
- **codey** - Code Bison
- **codeyChat** - Codechat Bison
- **codeGemma** - CodeGemma 7B

#### توليد الصور
- **imagen2** - Imagen 2.0
- **imagen3** - Imagen 3.0

#### معالجة الصوت
- **chirp** - Chirp (التعرف على الكلام)
- **speechT5** - SpeechT5

#### أخرى
- **translate** - نموذج الترجمة
- **pegasus** - نموذج التلخيص

## كيفية الاستخدام

### الاستخدام الأساسي

```javascript
import { getModel } from './vertex-ai/config.js';
import { generate } from '@genkit-ai/ai';

// الحصول على نموذج مع الإعدادات الافتراضية
const { model, settings } = getModel('text', 'gemini.pro15');

// استخدام النموذج
const response = await generate({
  model,
  prompt: 'مرحباً، كيف يمكنني مساعدتك؟',
  config: settings,
});

console.log(response.text);
```

### الاستخدام مع إعدادات مخصصة

```javascript
// إعدادات مخصصة
const { model, settings } = getModel('text', 'claude.sonnet35v2', {
  temperature: 0.3,
  maxOutputTokens: 2000,
  topP: 0.8,
});

const response = await generate({
  model,
  prompt: 'اكتب مقالاً عن الذكاء الاصطناعي',
  config: settings,
});
```

### استخدام نماذج الرؤية

```javascript
const { model, settings } = getModel('vision', 'geminiVision15');

const response = await generate({
  model,
  prompt: [
    { text: 'صف هذه الصورة' },
    { media: { url: 'path/to/image.jpg' } }
  ],
  config: settings,
});
```

### استخدام النماذج المتخصصة

```javascript
// لتوليد الكود
const { model, settings } = getModel('specialized', 'codey');

const codeResponse = await generate({
  model,
  prompt: 'اكتب دالة Python لحساب الأرقام الأولية',
  config: settings,
});
```

## الدوال المساعدة

### عرض النماذج المتاحة

```javascript
import { listAvailableModels } from './vertex-ai/config.js';

const availableModels = listAvailableModels();
console.log(availableModels);
```

### التحقق من توفر نموذج

```javascript
import { isModelAvailable } from './vertex-ai/config.js';

if (isModelAvailable('text', 'gemini.pro15')) {
  console.log('النموذج متوفر');
}
```

### الحصول على معلومات النماذج

```javascript
import { getModelInfo } from './vertex-ai/config.js';

const info = getModelInfo();
console.log('إجمالي النماذج:', info.totalModels);
console.log('المزودون:', info.providers);
console.log('القدرات:', info.capabilities);
```

## أفضل الممارسات

### 1. اختيار النموذج المناسب

- **للمهام السريعة**: استخدم `gemini.flash` أو `claude.haiku35`
- **للمهام المعقدة**: استخدم `claude.sonnet35v2` أو `gemini.pro15`
- **للبرمجة**: استخدم `mistral.codestral` أو `specialized.codey`
- **للصور**: استخدم `geminiVision15` أو `llama32Vision90b`

### 2. تحسين الأداء

```javascript
// للاستجابات السريعة
const fastSettings = {
  temperature: 0.3,
  maxOutputTokens: 500,
  topP: 0.8,
};

// للإبداع والتنوع
const creativeSettings = {
  temperature: 0.9,
  maxOutputTokens: 2000,
  topP: 0.95,
};
```

### 3. معالجة الأخطاء

```javascript
try {
  const { model, settings } = getModel('text', 'gemini.pro15');
  const response = await generate({ model, prompt: 'مرحباً', config: settings });
  console.log(response.text);
} catch (error) {
  console.error('خطأ في استخدام النموذج:', error.message);
}
```

## أمثلة متقدمة

راجع ملف `examples/vertex-models-usage.js` للحصول على أمثلة شاملة تتضمن:

- استخدام نماذج مختلفة
- مقارنة الأداء
- معالجة الصور والنصوص
- استخدام النماذج المتخصصة

## الدعم والمساعدة

للحصول على المساعدة أو الإبلاغ عن مشاكل:

1. راجع الوثائق الرسمية لـ Vertex AI
2. تحقق من ملفات الأمثلة
3. استخدم الدوال المساعدة للتشخيص

## التحديثات

يتم تحديث قائمة النماذج بانتظام لتشمل أحدث النماذج المتاحة في Vertex AI Model Garden.
