/**
 * خادم الإنتاج لتطبيق Malite Agent
 * محسن للنشر على Google Cloud Platform
 */

import express from 'express';
import { googleAI } from '@genkit-ai/googleai';
import { vertexAI } from '@genkit-ai/vertexai';
import { genkit } from 'genkit';
import { startFlowsServer } from '@genkit-ai/flow';
import dotenv from 'dotenv';
import { getModel } from './vertex-ai/config.js';
import { runHealthChecks } from './healthcheck.js';

// تحميل متغيرات البيئة
dotenv.config();

// إعدادات الخادم
const PORT = process.env.PORT || 8080;
const HOST = process.env.HOST || '0.0.0.0';
const isProduction = process.env.NODE_ENV === 'production';

// تكوين Genkit
const ai = genkit({
  plugins: [
    googleAI(),
    vertexAI({ 
      location: process.env.VERTEX_AI_LOCATION || 'us-central1',
      projectId: process.env.GOOGLE_CLOUD_PROJECT 
    })
  ],
  model: vertexAI.model('gemini-1.5-pro'),
});

// إنشاء تطبيق Express
const app = express();

// Middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// إعداد CORS للإنتاج
app.use((req, res, next) => {
  if (isProduction) {
    res.header('Access-Control-Allow-Origin', process.env.ALLOWED_ORIGINS || '*');
  } else {
    res.header('Access-Control-Allow-Origin', '*');
  }
  res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization');
  next();
});

// Middleware للأمان
app.use((req, res, next) => {
  res.header('X-Content-Type-Options', 'nosniff');
  res.header('X-Frame-Options', 'DENY');
  res.header('X-XSS-Protection', '1; mode=block');
  if (isProduction) {
    res.header('Strict-Transport-Security', 'max-age=31536000; includeSubDomains');
  }
  next();
});

// الصفحة الرئيسية
app.get('/', (req, res) => {
  res.json({
    name: 'Malite Agent',
    version: '2.0.0',
    description: 'تطبيق Genkit متقدم للذكاء الاصطناعي مع دعم كامل للغة العربية',
    status: 'running',
    environment: process.env.NODE_ENV || 'development',
    endpoints: {
      health: '/health',
      chat: '/api/chat',
      models: '/api/models',
      generate: '/api/generate'
    },
    documentation: 'https://github.com/your-repo/malite-agent'
  });
});

// فحص الصحة
app.get('/health', async (req, res) => {
  try {
    const healthResults = await runHealthChecks();
    const isHealthy = healthResults.every(result => result.status === 'OK');
    
    res.status(isHealthy ? 200 : 503).json({
      status: isHealthy ? 'healthy' : 'unhealthy',
      timestamp: new Date().toISOString(),
      checks: healthResults,
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      version: '2.0.0'
    });
  } catch (error) {
    res.status(503).json({
      status: 'unhealthy',
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// API للحصول على قائمة النماذج المتاحة
app.get('/api/models', (req, res) => {
  try {
    const { listAvailableModels, getModelInfo } = await import('./vertex-ai/config.js');
    const availableModels = listAvailableModels();
    const modelInfo = getModelInfo();
    
    res.json({
      success: true,
      data: {
        models: availableModels,
        info: modelInfo,
        total: Object.values(availableModels).flat().length
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// API للدردشة
app.post('/api/chat', async (req, res) => {
  try {
    const { message, modelType = 'text', modelName = 'gemini.pro15', settings = {} } = req.body;
    
    if (!message) {
      return res.status(400).json({
        success: false,
        error: 'رسالة مطلوبة'
      });
    }
    
    const { model, settings: modelSettings } = getModel(modelType, modelName, settings);
    
    const response = await ai.generate({
      model,
      prompt: message,
      config: modelSettings
    });
    
    res.json({
      success: true,
      data: {
        response: response.text,
        model: `${modelType}/${modelName}`,
        settings: modelSettings,
        timestamp: new Date().toISOString()
      }
    });
    
  } catch (error) {
    console.error('خطأ في API الدردشة:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// API لتوليد النصوص
app.post('/api/generate', async (req, res) => {
  try {
    const { 
      prompt, 
      modelType = 'text', 
      modelName = 'gemini.pro15', 
      settings = {},
      stream = false 
    } = req.body;
    
    if (!prompt) {
      return res.status(400).json({
        success: false,
        error: 'النص المطلوب توليده مطلوب'
      });
    }
    
    const { model, settings: modelSettings } = getModel(modelType, modelName, settings);
    
    if (stream) {
      // دعم التدفق المباشر
      res.writeHead(200, {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive'
      });
      
      // TODO: تنفيذ التدفق المباشر
      res.write(`data: ${JSON.stringify({ error: 'التدفق المباشر غير مدعوم حالياً' })}\n\n`);
      res.end();
    } else {
      const response = await ai.generate({
        model,
        prompt,
        config: modelSettings
      });
      
      res.json({
        success: true,
        data: {
          text: response.text,
          model: `${modelType}/${modelName}`,
          settings: modelSettings,
          metadata: {
            promptLength: prompt.length,
            responseLength: response.text.length,
            timestamp: new Date().toISOString()
          }
        }
      });
    }
    
  } catch (error) {
    console.error('خطأ في API التوليد:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// معالج الأخطاء العام
app.use((error, req, res, next) => {
  console.error('خطأ غير متوقع:', error);
  res.status(500).json({
    success: false,
    error: isProduction ? 'خطأ داخلي في الخادم' : error.message
  });
});

// معالج الطرق غير الموجودة
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    error: 'الطريق غير موجود',
    availableEndpoints: [
      'GET /',
      'GET /health',
      'GET /api/models',
      'POST /api/chat',
      'POST /api/generate'
    ]
  });
});

// بدء الخادم
async function startServer() {
  try {
    // بدء خادم Genkit Flows إذا لم نكن في الإنتاج
    if (!isProduction) {
      console.log('🔧 بدء خادم Genkit Flows...');
      await startFlowsServer({
        port: 3400,
        cors: {
          origin: true,
        },
      });
      console.log('✅ خادم Genkit Flows يعمل على المنفذ 3400');
    }
    
    // بدء الخادم الرئيسي
    const server = app.listen(PORT, HOST, () => {
      console.log('\n🚀 ===== خادم Malite Agent =====');
      console.log(`🌐 الخادم يعمل على: http://${HOST}:${PORT}`);
      console.log(`🏥 فحص الصحة: http://${HOST}:${PORT}/health`);
      console.log(`🤖 API الدردشة: http://${HOST}:${PORT}/api/chat`);
      console.log(`📝 API التوليد: http://${HOST}:${PORT}/api/generate`);
      console.log(`🔧 البيئة: ${process.env.NODE_ENV || 'development'}`);
      
      if (!isProduction) {
        console.log(`🛠️ واجهة المطور: http://localhost:3400`);
      }
      
      console.log('✅ الخادم جاهز لاستقبال الطلبات\n');
    });
    
    // معالجة إيقاف الخادم بأمان
    process.on('SIGTERM', () => {
      console.log('📴 تلقي إشارة SIGTERM، إيقاف الخادم...');
      server.close(() => {
        console.log('✅ تم إيقاف الخادم بأمان');
        process.exit(0);
      });
    });
    
    process.on('SIGINT', () => {
      console.log('📴 تلقي إشارة SIGINT، إيقاف الخادم...');
      server.close(() => {
        console.log('✅ تم إيقاف الخادم بأمان');
        process.exit(0);
      });
    });
    
  } catch (error) {
    console.error('❌ فشل في بدء الخادم:', error);
    process.exit(1);
  }
}

// بدء الخادم
startServer();

export { app };
