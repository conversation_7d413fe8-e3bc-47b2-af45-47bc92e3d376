{"version": 3, "sources": ["../../src/common/index.ts"], "sourcesContent": ["/**\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { VertexAI } from '@google-cloud/vertexai';\nimport { GenerateRequest } from 'genkit/model';\nimport { GoogleAuth } from 'google-auth-library';\nimport { GeminiConfigSchema } from '../gemini.js';\nimport { CLOUD_PLATFORM_OAUTH_SCOPE } from './constants.js';\nimport type { PluginOptions } from './types.js';\nexport type { PluginOptions };\n\ninterface DerivedParams {\n  location: string;\n  projectId: string;\n  vertexClientFactory: (\n    request: GenerateRequest<typeof GeminiConfigSchema>\n  ) => VertexAI;\n  authClient: GoogleAuth;\n}\n\nfunction parseFirebaseProjectId(): string | undefined {\n  if (!process.env.FIREBASE_CONFIG) return undefined;\n  try {\n    return JSON.parse(process.env.FIREBASE_CONFIG).projectId as string;\n  } catch {\n    return undefined;\n  }\n}\n\n/** @hidden */\nexport function __setFakeDerivedParams(params: any) {\n  __fake_getDerivedParams = params;\n}\nlet __fake_getDerivedParams: any;\n\nexport async function getDerivedParams(\n  options?: PluginOptions\n): Promise<DerivedParams> {\n  if (__fake_getDerivedParams) {\n    return __fake_getDerivedParams;\n  }\n\n  let authOptions = options?.googleAuth;\n  let authClient: GoogleAuth;\n  const providedProjectId =\n    options?.projectId ||\n    process.env.GCLOUD_PROJECT ||\n    parseFirebaseProjectId();\n  if (process.env.GCLOUD_SERVICE_ACCOUNT_CREDS) {\n    const serviceAccountCreds = JSON.parse(\n      process.env.GCLOUD_SERVICE_ACCOUNT_CREDS\n    );\n    authOptions = {\n      credentials: serviceAccountCreds,\n      scopes: [CLOUD_PLATFORM_OAUTH_SCOPE],\n      projectId: providedProjectId,\n    };\n    authClient = new GoogleAuth(authOptions);\n  } else {\n    authClient = new GoogleAuth(\n      authOptions ?? {\n        scopes: [CLOUD_PLATFORM_OAUTH_SCOPE],\n        projectId: providedProjectId,\n      }\n    );\n  }\n\n  const projectId = options?.projectId || (await authClient.getProjectId());\n  const location = options?.location || 'us-central1';\n\n  if (!location) {\n    throw new Error(\n      `VertexAI Plugin is missing the 'location' configuration. Please set the 'GCLOUD_LOCATION' environment variable or explicitly pass 'location' into genkit config.`\n    );\n  }\n  if (!projectId) {\n    throw new Error(\n      `VertexAI Plugin is missing the 'project' configuration. Please set the 'GCLOUD_PROJECT' environment variable or explicitly pass 'project' into genkit config.`\n    );\n  }\n\n  const vertexClientFactoryCache: Record<string, VertexAI> = {};\n  const vertexClientFactory = (\n    request: GenerateRequest<typeof GeminiConfigSchema>\n  ): VertexAI => {\n    const requestLocation = request.config?.location || location;\n    if (!vertexClientFactoryCache[requestLocation]) {\n      vertexClientFactoryCache[requestLocation] = new VertexAI({\n        project: projectId,\n        location: requestLocation,\n        googleAuthOptions: { projectId: providedProjectId, ...authOptions },\n      });\n    }\n    return vertexClientFactoryCache[requestLocation];\n  };\n\n  return {\n    location,\n    projectId,\n    vertexClientFactory,\n    authClient,\n  };\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAgBA,sBAAyB;AAEzB,iCAA2B;AAE3B,uBAA2C;AAa3C,SAAS,yBAA6C;AACpD,MAAI,CAAC,QAAQ,IAAI,gBAAiB,QAAO;AACzC,MAAI;AACF,WAAO,KAAK,MAAM,QAAQ,IAAI,eAAe,EAAE;AAAA,EACjD,QAAQ;AACN,WAAO;AAAA,EACT;AACF;AAGO,SAAS,uBAAuB,QAAa;AAClD,4BAA0B;AAC5B;AACA,IAAI;AAEJ,eAAsB,iBACpB,SACwB;AACxB,MAAI,yBAAyB;AAC3B,WAAO;AAAA,EACT;AAEA,MAAI,cAAc,SAAS;AAC3B,MAAI;AACJ,QAAM,oBACJ,SAAS,aACT,QAAQ,IAAI,kBACZ,uBAAuB;AACzB,MAAI,QAAQ,IAAI,8BAA8B;AAC5C,UAAM,sBAAsB,KAAK;AAAA,MAC/B,QAAQ,IAAI;AAAA,IACd;AACA,kBAAc;AAAA,MACZ,aAAa;AAAA,MACb,QAAQ,CAAC,2CAA0B;AAAA,MACnC,WAAW;AAAA,IACb;AACA,iBAAa,IAAI,sCAAW,WAAW;AAAA,EACzC,OAAO;AACL,iBAAa,IAAI;AAAA,MACf,eAAe;AAAA,QACb,QAAQ,CAAC,2CAA0B;AAAA,QACnC,WAAW;AAAA,MACb;AAAA,IACF;AAAA,EACF;AAEA,QAAM,YAAY,SAAS,aAAc,MAAM,WAAW,aAAa;AACvE,QAAM,WAAW,SAAS,YAAY;AAEtC,MAAI,CAAC,UAAU;AACb,UAAM,IAAI;AAAA,MACR;AAAA,IACF;AAAA,EACF;AACA,MAAI,CAAC,WAAW;AACd,UAAM,IAAI;AAAA,MACR;AAAA,IACF;AAAA,EACF;AAEA,QAAM,2BAAqD,CAAC;AAC5D,QAAM,sBAAsB,CAC1B,YACa;AACb,UAAM,kBAAkB,QAAQ,QAAQ,YAAY;AACpD,QAAI,CAAC,yBAAyB,eAAe,GAAG;AAC9C,+BAAyB,eAAe,IAAI,IAAI,yBAAS;AAAA,QACvD,SAAS;AAAA,QACT,UAAU;AAAA,QACV,mBAAmB,EAAE,WAAW,mBAAmB,GAAG,YAAY;AAAA,MACpE,CAAC;AAAA,IACH;AACA,WAAO,yBAAyB,eAAe;AAAA,EACjD;AAEA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;", "names": []}