{"version": 3, "sources": ["../src/generate.ts"], "sourcesContent": ["/**\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  Action,\n  ActionContext,\n  GenkitError,\n  StreamingCallback,\n  runWithContext,\n  runWithStreamingCallback,\n  sentinelNoopStreamingCallback,\n  z,\n} from '@genkit-ai/core';\nimport { Channel } from '@genkit-ai/core/async';\nimport { Registry } from '@genkit-ai/core/registry';\nimport { toJsonSchema } from '@genkit-ai/core/schema';\nimport { DocumentData } from './document.js';\nimport {\n  injectInstructions,\n  resolveFormat,\n  resolveInstructions,\n} from './formats/index.js';\nimport {\n  generateHelper,\n  shouldInjectFormatInstructions,\n} from './generate/action.js';\nimport { GenerateResponseChunk } from './generate/chunk.js';\nimport { GenerateResponse } from './generate/response.js';\nimport { Message } from './message.js';\nimport {\n  GenerateActionOptions,\n  GenerateRequest,\n  GenerationCommonConfigSchema,\n  MessageData,\n  ModelArgument,\n  ModelMiddleware,\n  Part,\n  ToolRequestPart,\n  ToolResponsePart,\n  resolveModel,\n} from './model.js';\nimport { ExecutablePrompt } from './prompt.js';\nimport { ToolArgument, resolveTools, toToolDefinition } from './tool.js';\nexport { GenerateResponse, GenerateResponseChunk };\n\n/** Specifies how tools should be called by the model. */\nexport type ToolChoice = 'auto' | 'required' | 'none';\n\nexport interface OutputOptions<O extends z.ZodTypeAny = z.ZodTypeAny> {\n  format?: string;\n  contentType?: string;\n  instructions?: boolean | string;\n  schema?: O;\n  jsonSchema?: any;\n  constrained?: boolean;\n}\n\n/** ResumeOptions configure how to resume generation after an interrupt. */\nexport interface ResumeOptions {\n  /**\n   * respond should contain a single or list of `toolResponse` parts corresponding\n   * to interrupt `toolRequest` parts from the most recent model message. Each\n   * entry must have a matching `name` and `ref` (if supplied) for its `toolRequest`\n   * counterpart.\n   *\n   * Tools have a `.respond` helper method to construct a reply ToolResponse and validate\n   * the data against its schema. Call `myTool.respond(interruptToolRequest, yourReplyData)`.\n   */\n  respond?: ToolResponsePart | ToolResponsePart[];\n  /**\n   * restart will run a tool again with additionally supplied metadata passed through as\n   * a `resumed` option in the second argument. This allows for scenarios like conditionally\n   * requesting confirmation of an LLM's tool request.\n   *\n   * Tools have a `.restart` helper method to construct a restart ToolRequest. Call\n   * `myTool.restart(interruptToolRequest, resumeMetadata)`.\n   *\n   */\n  restart?: ToolRequestPart | ToolRequestPart[];\n  /** Additional metadata to annotate the created tool message with in the \"resume\" key. */\n  metadata?: Record<string, any>;\n}\n\nexport interface GenerateOptions<\n  O extends z.ZodTypeAny = z.ZodTypeAny,\n  CustomOptions extends z.ZodTypeAny = z.ZodTypeAny,\n> {\n  /** A model name (e.g. `vertexai/gemini-1.0-pro`) or reference. */\n  model?: ModelArgument<CustomOptions>;\n  /** The system prompt to be included in the generate request. Can be a string for a simple text prompt or one or more parts for multi-modal prompts (subject to model support). */\n  system?: string | Part | Part[];\n  /** The prompt for which to generate a response. Can be a string for a simple text prompt or one or more parts for multi-modal prompts. */\n  prompt?: string | Part | Part[];\n  /** Retrieved documents to be used as context for this generation. */\n  docs?: DocumentData[];\n  /** Conversation messages (history) for multi-turn prompting when supported by the underlying model. */\n  messages?: (MessageData & { content: Part[] | string | (string | Part)[] })[];\n  /** List of registered tool names or actions to treat as a tool for this generation if supported by the underlying model. */\n  tools?: ToolArgument[];\n  /** Specifies how tools should be called by the model.  */\n  toolChoice?: ToolChoice;\n  /** Configuration for the generation request. */\n  config?: z.infer<CustomOptions>;\n  /** Configuration for the desired output of the request. Defaults to the model's default output if unspecified. */\n  output?: OutputOptions<O>;\n  /**\n   * resume provides convenient capabilities for continuing generation\n   * after an interrupt is triggered. Example:\n   *\n   * ```ts\n   * const myInterrupt = ai.defineInterrupt({...});\n   *\n   * const response = await ai.generate({\n   *   tools: [myInterrupt],\n   *   prompt: \"Call myInterrupt\",\n   * });\n   *\n   * const interrupt = response.interrupts[0];\n   *\n   * const resumedResponse = await ai.generate({\n   *   messages: response.messages,\n   *   resume: myInterrupt.respond(interrupt, {note: \"this is the reply data\"}),\n   * });\n   * ```\n   *\n   * @beta\n   */\n  resume?: ResumeOptions;\n  /** When true, return tool calls for manual processing instead of automatically resolving them. */\n  returnToolRequests?: boolean;\n  /** Maximum number of tool call iterations that can be performed in a single generate call (default 5). */\n  maxTurns?: number;\n  /** When provided, models supporting streaming will call the provided callback with chunks as generation progresses. */\n  onChunk?: StreamingCallback<GenerateResponseChunk>;\n  /**\n   * When provided, models supporting streaming will call the provided callback with chunks as generation progresses.\n   *\n   * @deprecated use {@link onChunk} instead.\n   */\n  streamingCallback?: StreamingCallback<GenerateResponseChunk>;\n  /** Middleware to be used with this model call. */\n  use?: ModelMiddleware[];\n  /** Additional context (data, like e.g. auth) to be passed down to tools, prompts and other sub actions. */\n  context?: ActionContext;\n}\n\nexport async function toGenerateRequest(\n  registry: Registry,\n  options: GenerateOptions\n): Promise<GenerateRequest> {\n  let messages: MessageData[] = [];\n  if (options.system) {\n    messages.push({\n      role: 'system',\n      content: Message.parseContent(options.system),\n    });\n  }\n  if (options.messages) {\n    messages.push(...options.messages.map((m) => Message.parseData(m)));\n  }\n  if (options.prompt) {\n    messages.push({\n      role: 'user',\n      content: Message.parseContent(options.prompt),\n    });\n  }\n  if (messages.length === 0) {\n    throw new GenkitError({\n      status: 'INVALID_ARGUMENT',\n      message: 'at least one message is required in generate request',\n    });\n  }\n  if (\n    options.resume &&\n    !(\n      messages.at(-1)?.role === 'model' &&\n      messages.at(-1)?.content.find((p) => !!p.toolRequest)\n    )\n  ) {\n    throw new GenkitError({\n      status: 'FAILED_PRECONDITION',\n      message: `Last message must be a 'model' role with at least one tool request to 'resume' generation.`,\n      detail: messages.at(-1),\n    });\n  }\n  let tools: Action<any, any>[] | undefined;\n  if (options.tools) {\n    tools = await resolveTools(registry, options.tools);\n  }\n\n  const resolvedSchema = toJsonSchema({\n    schema: options.output?.schema,\n    jsonSchema: options.output?.jsonSchema,\n  });\n\n  const resolvedFormat = await resolveFormat(registry, options.output);\n  const instructions = resolveInstructions(\n    resolvedFormat,\n    resolvedSchema,\n    options?.output?.instructions\n  );\n\n  const out = {\n    messages: shouldInjectFormatInstructions(\n      resolvedFormat?.config,\n      options.output\n    )\n      ? injectInstructions(messages, instructions)\n      : messages,\n    config: options.config,\n    docs: options.docs,\n    tools: tools?.map(toToolDefinition) || [],\n    output: {\n      ...(resolvedFormat?.config || {}),\n      ...options.output,\n      schema: resolvedSchema,\n    },\n  } as GenerateRequest;\n  if (!out?.output?.schema) delete out?.output?.schema;\n  return out;\n}\n\nexport class GenerationResponseError extends GenkitError {\n  detail: {\n    response: GenerateResponse;\n    [otherDetails: string]: any;\n  };\n\n  constructor(\n    response: GenerateResponse<any>,\n    message: string,\n    status?: GenkitError['status'],\n    detail?: Record<string, any>\n  ) {\n    super({\n      status: status || 'FAILED_PRECONDITION',\n      message,\n    });\n    this.detail = { response, ...detail };\n  }\n}\n\nasync function toolsToActionRefs(\n  registry: Registry,\n  toolOpt?: ToolArgument[]\n): Promise<string[] | undefined> {\n  if (!toolOpt) return;\n\n  let tools: string[] = [];\n\n  for (const t of toolOpt) {\n    if (typeof t === 'string') {\n      tools.push(await resolveFullToolName(registry, t));\n    } else if ((t as Action).__action) {\n      tools.push(\n        `/${(t as Action).__action.metadata?.type}/${(t as Action).__action.name}`\n      );\n    } else if (typeof (t as ExecutablePrompt).asTool === 'function') {\n      const promptToolAction = await (t as ExecutablePrompt).asTool();\n      tools.push(`/prompt/${promptToolAction.__action.name}`);\n    } else if (t.name) {\n      tools.push(await resolveFullToolName(registry, t.name));\n    } else {\n      throw new Error(`Unable to determine type of tool: ${JSON.stringify(t)}`);\n    }\n  }\n  return tools;\n}\n\nfunction messagesFromOptions(options: GenerateOptions): MessageData[] {\n  const messages: MessageData[] = [];\n  if (options.system) {\n    messages.push({\n      role: 'system',\n      content: Message.parseContent(options.system),\n    });\n  }\n  if (options.messages) {\n    messages.push(...options.messages);\n  }\n  if (options.prompt) {\n    messages.push({\n      role: 'user',\n      content: Message.parseContent(options.prompt),\n    });\n  }\n  if (messages.length === 0) {\n    throw new GenkitError({\n      status: 'INVALID_ARGUMENT',\n      message: 'at least one message is required in generate request',\n    });\n  }\n  return messages;\n}\n\n/** A GenerationBlockedError is thrown when a generation is blocked. */\nexport class GenerationBlockedError extends GenerationResponseError {}\n\n/**\n * Generate calls a generative model based on the provided prompt and configuration. If\n * `history` is provided, the generation will include a conversation history in its\n * request. If `tools` are provided, the generate method will automatically resolve\n * tool calls returned from the model unless `returnToolRequests` is set to `true`.\n *\n * See `GenerateOptions` for detailed information about available options.\n *\n * @param options The options for this generation request.\n * @returns The generated response based on the provided parameters.\n */\nexport async function generate<\n  O extends z.ZodTypeAny = z.ZodTypeAny,\n  CustomOptions extends z.ZodTypeAny = typeof GenerationCommonConfigSchema,\n>(\n  registry: Registry,\n  options:\n    | GenerateOptions<O, CustomOptions>\n    | PromiseLike<GenerateOptions<O, CustomOptions>>\n): Promise<GenerateResponse<z.infer<O>>> {\n  const resolvedOptions: GenerateOptions<O, CustomOptions> = {\n    ...(await Promise.resolve(options)),\n  };\n  const resolvedFormat = await resolveFormat(registry, resolvedOptions.output);\n\n  registry = maybeRegisterDynamicTools(registry, resolvedOptions);\n\n  const params = await toGenerateActionOptions(registry, resolvedOptions);\n\n  const tools = await toolsToActionRefs(registry, resolvedOptions.tools);\n  return await runWithStreamingCallback(\n    registry,\n    stripNoop(resolvedOptions.onChunk ?? resolvedOptions.streamingCallback),\n    async () => {\n      const response = await runWithContext(\n        registry,\n        resolvedOptions.context,\n        () =>\n          generateHelper(registry, {\n            rawRequest: params,\n            middleware: resolvedOptions.use,\n          })\n      );\n      const request = await toGenerateRequest(registry, {\n        ...resolvedOptions,\n        tools,\n      });\n      return new GenerateResponse<O>(response, {\n        request: response.request ?? request,\n        parser: resolvedFormat?.handler(request.output?.schema).parseMessage,\n      });\n    }\n  );\n}\n\nfunction maybeRegisterDynamicTools<\n  O extends z.ZodTypeAny = z.ZodTypeAny,\n  CustomOptions extends z.ZodTypeAny = typeof GenerationCommonConfigSchema,\n>(registry: Registry, options: GenerateOptions<O, CustomOptions>): Registry {\n  let hasDynamicTools = false;\n  options?.tools?.forEach((t) => {\n    if (\n      (t as Action).__action &&\n      (t as Action).__action.metadata?.type === 'tool' &&\n      (t as Action).__action.metadata?.dynamic\n    ) {\n      if (!hasDynamicTools) {\n        hasDynamicTools = true;\n        // Create a temporary registry with dynamic tools for the duration of this\n        // generate request.\n        registry = Registry.withParent(registry);\n      }\n      registry.registerAction('tool', t as Action);\n    }\n  });\n  return registry;\n}\n\nexport async function toGenerateActionOptions<\n  O extends z.ZodTypeAny = z.ZodTypeAny,\n  CustomOptions extends z.ZodTypeAny = typeof GenerationCommonConfigSchema,\n>(\n  registry: Registry,\n  options: GenerateOptions<O, CustomOptions>\n): Promise<GenerateActionOptions> {\n  const resolvedModel = await resolveModel(registry, options.model);\n  const tools = await toolsToActionRefs(registry, options.tools);\n  const messages: MessageData[] = messagesFromOptions(options);\n\n  const resolvedSchema = toJsonSchema({\n    schema: options.output?.schema,\n    jsonSchema: options.output?.jsonSchema,\n  });\n\n  // If is schema is set but format is not explicitly set, default to `json` format.\n  if (\n    (options.output?.schema || options.output?.jsonSchema) &&\n    !options.output?.format\n  ) {\n    options.output.format = 'json';\n  }\n\n  const params: GenerateActionOptions = {\n    model: resolvedModel.modelAction.__action.name,\n    docs: options.docs,\n    messages: messages,\n    tools,\n    toolChoice: options.toolChoice,\n    config: {\n      version: resolvedModel.version,\n      ...stripUndefinedOptions(resolvedModel.config),\n      ...stripUndefinedOptions(options.config),\n    },\n    output: options.output && {\n      ...options.output,\n      format: options.output.format,\n      jsonSchema: resolvedSchema,\n    },\n    // coerce reply and restart into arrays for the action schema\n    resume: options.resume && {\n      respond: [options.resume.respond || []].flat(),\n      restart: [options.resume.restart || []].flat(),\n      metadata: options.resume.metadata,\n    },\n    returnToolRequests: options.returnToolRequests,\n    maxTurns: options.maxTurns,\n  };\n  // if config is empty and it was not explicitly passed in, we delete it, don't want {}\n  if (Object.keys(params.config).length === 0 && !options.config) {\n    delete params.config;\n  }\n  return params;\n}\n\n/**\n * Check if the callback is a noop callback and return undefined -- downstream models\n * expect undefined if no streaming is requested.\n */\nfunction stripNoop<T>(\n  callback: StreamingCallback<T> | undefined\n): StreamingCallback<T> | undefined {\n  if (callback === sentinelNoopStreamingCallback) {\n    return undefined;\n  }\n  return callback;\n}\n\nfunction stripUndefinedOptions(input?: any): any {\n  if (!input) return input;\n  const copy = { ...input };\n  Object.keys(input).forEach((key) => {\n    if (copy[key] === undefined) {\n      delete copy[key];\n    }\n  });\n  return copy;\n}\n\nasync function resolveFullToolName(\n  registry: Registry,\n  name: string\n): Promise<string> {\n  if (await registry.lookupAction(`/tool/${name}`)) {\n    return `/tool/${name}`;\n  } else if (await registry.lookupAction(`/prompt/${name}`)) {\n    return `/prompt/${name}`;\n  } else {\n    throw new Error(`Unable to determine type of of tool: ${name}`);\n  }\n}\n\nexport type GenerateStreamOptions<\n  O extends z.ZodTypeAny = z.ZodTypeAny,\n  CustomOptions extends z.ZodTypeAny = typeof GenerationCommonConfigSchema,\n> = Omit<GenerateOptions<O, CustomOptions>, 'streamingCallback'>;\n\nexport interface GenerateStreamResponse<O extends z.ZodTypeAny = z.ZodTypeAny> {\n  get stream(): AsyncIterable<GenerateResponseChunk>;\n  get response(): Promise<GenerateResponse<O>>;\n}\n\nexport function generateStream<\n  O extends z.ZodTypeAny = z.ZodTypeAny,\n  CustomOptions extends z.ZodTypeAny = typeof GenerationCommonConfigSchema,\n>(\n  registry: Registry,\n  options:\n    | GenerateOptions<O, CustomOptions>\n    | PromiseLike<GenerateOptions<O, CustomOptions>>\n): GenerateStreamResponse<O> {\n  let channel = new Channel<GenerateResponseChunk>();\n\n  const generated = Promise.resolve(options).then((resolvedOptions) =>\n    generate<O, CustomOptions>(registry, {\n      ...resolvedOptions,\n      onChunk: (chunk) => channel.send(chunk),\n    })\n  );\n  generated.then(\n    () => channel.close(),\n    (err) => channel.error(err)\n  );\n\n  return {\n    response: generated,\n    stream: channel,\n  };\n}\n\nexport function tagAsPreamble(msgs?: MessageData[]): MessageData[] | undefined {\n  if (!msgs) {\n    return undefined;\n  }\n  return msgs.map((m) => ({\n    ...m,\n    metadata: {\n      ...m.metadata,\n      preamble: true,\n    },\n  }));\n}\n"], "mappings": "AAgBA;AAAA,EAGE;AAAA,EAEA;AAAA,EACA;AAAA,EACA;AAAA,OAEK;AACP,SAAS,eAAe;AACxB,SAAS,gBAAgB;AACzB,SAAS,oBAAoB;AAE7B;AAAA,EACE;AAAA,EACA;AAAA,EACA;AAAA,OACK;AACP;AAAA,EACE;AAAA,EACA;AAAA,OACK;AACP,SAAS,6BAA6B;AACtC,SAAS,wBAAwB;AACjC,SAAS,eAAe;AACxB;AAAA,EAUE;AAAA,OACK;AAEP,SAAuB,cAAc,wBAAwB;AAwG7D,eAAsB,kBACpB,UACA,SAC0B;AAC1B,MAAI,WAA0B,CAAC;AAC/B,MAAI,QAAQ,QAAQ;AAClB,aAAS,KAAK;AAAA,MACZ,MAAM;AAAA,MACN,SAAS,QAAQ,aAAa,QAAQ,MAAM;AAAA,IAC9C,CAAC;AAAA,EACH;AACA,MAAI,QAAQ,UAAU;AACpB,aAAS,KAAK,GAAG,QAAQ,SAAS,IAAI,CAAC,MAAM,QAAQ,UAAU,CAAC,CAAC,CAAC;AAAA,EACpE;AACA,MAAI,QAAQ,QAAQ;AAClB,aAAS,KAAK;AAAA,MACZ,MAAM;AAAA,MACN,SAAS,QAAQ,aAAa,QAAQ,MAAM;AAAA,IAC9C,CAAC;AAAA,EACH;AACA,MAAI,SAAS,WAAW,GAAG;AACzB,UAAM,IAAI,YAAY;AAAA,MACpB,QAAQ;AAAA,MACR,SAAS;AAAA,IACX,CAAC;AAAA,EACH;AACA,MACE,QAAQ,UACR,EACE,SAAS,GAAG,EAAE,GAAG,SAAS,WAC1B,SAAS,GAAG,EAAE,GAAG,QAAQ,KAAK,CAAC,MAAM,CAAC,CAAC,EAAE,WAAW,IAEtD;AACA,UAAM,IAAI,YAAY;AAAA,MACpB,QAAQ;AAAA,MACR,SAAS;AAAA,MACT,QAAQ,SAAS,GAAG,EAAE;AAAA,IACxB,CAAC;AAAA,EACH;AACA,MAAI;AACJ,MAAI,QAAQ,OAAO;AACjB,YAAQ,MAAM,aAAa,UAAU,QAAQ,KAAK;AAAA,EACpD;AAEA,QAAM,iBAAiB,aAAa;AAAA,IAClC,QAAQ,QAAQ,QAAQ;AAAA,IACxB,YAAY,QAAQ,QAAQ;AAAA,EAC9B,CAAC;AAED,QAAM,iBAAiB,MAAM,cAAc,UAAU,QAAQ,MAAM;AACnE,QAAM,eAAe;AAAA,IACnB;AAAA,IACA;AAAA,IACA,SAAS,QAAQ;AAAA,EACnB;AAEA,QAAM,MAAM;AAAA,IACV,UAAU;AAAA,MACR,gBAAgB;AAAA,MAChB,QAAQ;AAAA,IACV,IACI,mBAAmB,UAAU,YAAY,IACzC;AAAA,IACJ,QAAQ,QAAQ;AAAA,IAChB,MAAM,QAAQ;AAAA,IACd,OAAO,OAAO,IAAI,gBAAgB,KAAK,CAAC;AAAA,IACxC,QAAQ;AAAA,MACN,GAAI,gBAAgB,UAAU,CAAC;AAAA,MAC/B,GAAG,QAAQ;AAAA,MACX,QAAQ;AAAA,IACV;AAAA,EACF;AACA,MAAI,CAAC,KAAK,QAAQ,OAAQ,QAAO,KAAK,QAAQ;AAC9C,SAAO;AACT;AAEO,MAAM,gCAAgC,YAAY;AAAA,EACvD;AAAA,EAKA,YACE,UACA,SACA,QACA,QACA;AACA,UAAM;AAAA,MACJ,QAAQ,UAAU;AAAA,MAClB;AAAA,IACF,CAAC;AACD,SAAK,SAAS,EAAE,UAAU,GAAG,OAAO;AAAA,EACtC;AACF;AAEA,eAAe,kBACb,UACA,SAC+B;AAC/B,MAAI,CAAC,QAAS;AAEd,MAAI,QAAkB,CAAC;AAEvB,aAAW,KAAK,SAAS;AACvB,QAAI,OAAO,MAAM,UAAU;AACzB,YAAM,KAAK,MAAM,oBAAoB,UAAU,CAAC,CAAC;AAAA,IACnD,WAAY,EAAa,UAAU;AACjC,YAAM;AAAA,QACJ,IAAK,EAAa,SAAS,UAAU,IAAI,IAAK,EAAa,SAAS,IAAI;AAAA,MAC1E;AAAA,IACF,WAAW,OAAQ,EAAuB,WAAW,YAAY;AAC/D,YAAM,mBAAmB,MAAO,EAAuB,OAAO;AAC9D,YAAM,KAAK,WAAW,iBAAiB,SAAS,IAAI,EAAE;AAAA,IACxD,WAAW,EAAE,MAAM;AACjB,YAAM,KAAK,MAAM,oBAAoB,UAAU,EAAE,IAAI,CAAC;AAAA,IACxD,OAAO;AACL,YAAM,IAAI,MAAM,qCAAqC,KAAK,UAAU,CAAC,CAAC,EAAE;AAAA,IAC1E;AAAA,EACF;AACA,SAAO;AACT;AAEA,SAAS,oBAAoB,SAAyC;AACpE,QAAM,WAA0B,CAAC;AACjC,MAAI,QAAQ,QAAQ;AAClB,aAAS,KAAK;AAAA,MACZ,MAAM;AAAA,MACN,SAAS,QAAQ,aAAa,QAAQ,MAAM;AAAA,IAC9C,CAAC;AAAA,EACH;AACA,MAAI,QAAQ,UAAU;AACpB,aAAS,KAAK,GAAG,QAAQ,QAAQ;AAAA,EACnC;AACA,MAAI,QAAQ,QAAQ;AAClB,aAAS,KAAK;AAAA,MACZ,MAAM;AAAA,MACN,SAAS,QAAQ,aAAa,QAAQ,MAAM;AAAA,IAC9C,CAAC;AAAA,EACH;AACA,MAAI,SAAS,WAAW,GAAG;AACzB,UAAM,IAAI,YAAY;AAAA,MACpB,QAAQ;AAAA,MACR,SAAS;AAAA,IACX,CAAC;AAAA,EACH;AACA,SAAO;AACT;AAGO,MAAM,+BAA+B,wBAAwB;AAAC;AAarE,eAAsB,SAIpB,UACA,SAGuC;AACvC,QAAM,kBAAqD;AAAA,IACzD,GAAI,MAAM,QAAQ,QAAQ,OAAO;AAAA,EACnC;AACA,QAAM,iBAAiB,MAAM,cAAc,UAAU,gBAAgB,MAAM;AAE3E,aAAW,0BAA0B,UAAU,eAAe;AAE9D,QAAM,SAAS,MAAM,wBAAwB,UAAU,eAAe;AAEtE,QAAM,QAAQ,MAAM,kBAAkB,UAAU,gBAAgB,KAAK;AACrE,SAAO,MAAM;AAAA,IACX;AAAA,IACA,UAAU,gBAAgB,WAAW,gBAAgB,iBAAiB;AAAA,IACtE,YAAY;AACV,YAAM,WAAW,MAAM;AAAA,QACrB;AAAA,QACA,gBAAgB;AAAA,QAChB,MACE,eAAe,UAAU;AAAA,UACvB,YAAY;AAAA,UACZ,YAAY,gBAAgB;AAAA,QAC9B,CAAC;AAAA,MACL;AACA,YAAM,UAAU,MAAM,kBAAkB,UAAU;AAAA,QAChD,GAAG;AAAA,QACH;AAAA,MACF,CAAC;AACD,aAAO,IAAI,iBAAoB,UAAU;AAAA,QACvC,SAAS,SAAS,WAAW;AAAA,QAC7B,QAAQ,gBAAgB,QAAQ,QAAQ,QAAQ,MAAM,EAAE;AAAA,MAC1D,CAAC;AAAA,IACH;AAAA,EACF;AACF;AAEA,SAAS,0BAGP,UAAoB,SAAsD;AAC1E,MAAI,kBAAkB;AACtB,WAAS,OAAO,QAAQ,CAAC,MAAM;AAC7B,QACG,EAAa,YACb,EAAa,SAAS,UAAU,SAAS,UACzC,EAAa,SAAS,UAAU,SACjC;AACA,UAAI,CAAC,iBAAiB;AACpB,0BAAkB;AAGlB,mBAAW,SAAS,WAAW,QAAQ;AAAA,MACzC;AACA,eAAS,eAAe,QAAQ,CAAW;AAAA,IAC7C;AAAA,EACF,CAAC;AACD,SAAO;AACT;AAEA,eAAsB,wBAIpB,UACA,SACgC;AAChC,QAAM,gBAAgB,MAAM,aAAa,UAAU,QAAQ,KAAK;AAChE,QAAM,QAAQ,MAAM,kBAAkB,UAAU,QAAQ,KAAK;AAC7D,QAAM,WAA0B,oBAAoB,OAAO;AAE3D,QAAM,iBAAiB,aAAa;AAAA,IAClC,QAAQ,QAAQ,QAAQ;AAAA,IACxB,YAAY,QAAQ,QAAQ;AAAA,EAC9B,CAAC;AAGD,OACG,QAAQ,QAAQ,UAAU,QAAQ,QAAQ,eAC3C,CAAC,QAAQ,QAAQ,QACjB;AACA,YAAQ,OAAO,SAAS;AAAA,EAC1B;AAEA,QAAM,SAAgC;AAAA,IACpC,OAAO,cAAc,YAAY,SAAS;AAAA,IAC1C,MAAM,QAAQ;AAAA,IACd;AAAA,IACA;AAAA,IACA,YAAY,QAAQ;AAAA,IACpB,QAAQ;AAAA,MACN,SAAS,cAAc;AAAA,MACvB,GAAG,sBAAsB,cAAc,MAAM;AAAA,MAC7C,GAAG,sBAAsB,QAAQ,MAAM;AAAA,IACzC;AAAA,IACA,QAAQ,QAAQ,UAAU;AAAA,MACxB,GAAG,QAAQ;AAAA,MACX,QAAQ,QAAQ,OAAO;AAAA,MACvB,YAAY;AAAA,IACd;AAAA;AAAA,IAEA,QAAQ,QAAQ,UAAU;AAAA,MACxB,SAAS,CAAC,QAAQ,OAAO,WAAW,CAAC,CAAC,EAAE,KAAK;AAAA,MAC7C,SAAS,CAAC,QAAQ,OAAO,WAAW,CAAC,CAAC,EAAE,KAAK;AAAA,MAC7C,UAAU,QAAQ,OAAO;AAAA,IAC3B;AAAA,IACA,oBAAoB,QAAQ;AAAA,IAC5B,UAAU,QAAQ;AAAA,EACpB;AAEA,MAAI,OAAO,KAAK,OAAO,MAAM,EAAE,WAAW,KAAK,CAAC,QAAQ,QAAQ;AAC9D,WAAO,OAAO;AAAA,EAChB;AACA,SAAO;AACT;AAMA,SAAS,UACP,UACkC;AAClC,MAAI,aAAa,+BAA+B;AAC9C,WAAO;AAAA,EACT;AACA,SAAO;AACT;AAEA,SAAS,sBAAsB,OAAkB;AAC/C,MAAI,CAAC,MAAO,QAAO;AACnB,QAAM,OAAO,EAAE,GAAG,MAAM;AACxB,SAAO,KAAK,KAAK,EAAE,QAAQ,CAAC,QAAQ;AAClC,QAAI,KAAK,GAAG,MAAM,QAAW;AAC3B,aAAO,KAAK,GAAG;AAAA,IACjB;AAAA,EACF,CAAC;AACD,SAAO;AACT;AAEA,eAAe,oBACb,UACA,MACiB;AACjB,MAAI,MAAM,SAAS,aAAa,SAAS,IAAI,EAAE,GAAG;AAChD,WAAO,SAAS,IAAI;AAAA,EACtB,WAAW,MAAM,SAAS,aAAa,WAAW,IAAI,EAAE,GAAG;AACzD,WAAO,WAAW,IAAI;AAAA,EACxB,OAAO;AACL,UAAM,IAAI,MAAM,wCAAwC,IAAI,EAAE;AAAA,EAChE;AACF;AAYO,SAAS,eAId,UACA,SAG2B;AAC3B,MAAI,UAAU,IAAI,QAA+B;AAEjD,QAAM,YAAY,QAAQ,QAAQ,OAAO,EAAE;AAAA,IAAK,CAAC,oBAC/C,SAA2B,UAAU;AAAA,MACnC,GAAG;AAAA,MACH,SAAS,CAAC,UAAU,QAAQ,KAAK,KAAK;AAAA,IACxC,CAAC;AAAA,EACH;AACA,YAAU;AAAA,IACR,MAAM,QAAQ,MAAM;AAAA,IACpB,CAAC,QAAQ,QAAQ,MAAM,GAAG;AAAA,EAC5B;AAEA,SAAO;AAAA,IACL,UAAU;AAAA,IACV,QAAQ;AAAA,EACV;AACF;AAEO,SAAS,cAAc,MAAiD;AAC7E,MAAI,CAAC,MAAM;AACT,WAAO;AAAA,EACT;AACA,SAAO,KAAK,IAAI,CAAC,OAAO;AAAA,IACtB,GAAG;AAAA,IACH,UAAU;AAAA,MACR,GAAG,EAAE;AAAA,MACL,UAAU;AAAA,IACZ;AAAA,EACF,EAAE;AACJ;", "names": []}