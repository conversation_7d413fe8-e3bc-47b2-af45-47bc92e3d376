// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.aiplatform.v1;

import "google/cloud/aiplatform/v1/artifact.proto";
import "google/cloud/aiplatform/v1/event.proto";
import "google/cloud/aiplatform/v1/execution.proto";

option csharp_namespace = "Google.Cloud.AIPlatform.V1";
option go_package = "cloud.google.com/go/aiplatform/apiv1/aiplatformpb;aiplatformpb";
option java_multiple_files = true;
option java_outer_classname = "LineageSubgraphProto";
option java_package = "com.google.cloud.aiplatform.v1";
option php_namespace = "Google\\Cloud\\AIPlatform\\V1";
option ruby_package = "Google::Cloud::AIPlatform::V1";

// A subgraph of the overall lineage graph. Event edges connect Artifact and
// Execution nodes.
message LineageSubgraph {
  // The Artifact nodes in the subgraph.
  repeated Artifact artifacts = 1;

  // The Execution nodes in the subgraph.
  repeated Execution executions = 2;

  // The Event edges between Artifacts and Executions in the subgraph.
  repeated Event events = 3;
}
