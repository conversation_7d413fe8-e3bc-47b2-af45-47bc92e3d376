"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
var firestore_exports = {};
__export(firestore_exports, {
  getFirestoreDocumentIndexer: () => getFirestoreDocumentIndexer,
  getFirestoreDocumentRetriever: () => getFirestoreDocumentRetriever
});
module.exports = __toCommonJS(firestore_exports);
var import_genkit = require("genkit");
const getFirestoreDocumentRetriever = (db, collectionName) => {
  const firestoreRetriever = async (neighbors) => {
    const docs = [];
    for (const neighbor of neighbors) {
      const docRef = db.collection(collectionName).doc(neighbor.datapoint?.datapointId);
      const docSnapshot = await docRef.get();
      if (docSnapshot.exists) {
        const docData = { ...docSnapshot.data() };
        docData.metadata = { ...docData.metadata, ...neighbor };
        const parsedDocData = import_genkit.DocumentDataSchema.safeParse(docData);
        if (parsedDocData.success) {
          docs.push(new import_genkit.Document(parsedDocData.data));
        }
      }
    }
    return docs;
  };
  return firestoreRetriever;
};
const getFirestoreDocumentIndexer = (db, collectionName) => {
  const firestoreIndexer = async (docs) => {
    const batch = db.batch();
    const ids = [];
    docs.forEach((doc) => {
      const docRef = db.collection(collectionName).doc();
      batch.set(docRef, {
        content: doc.content,
        metadata: doc.metadata || null
      });
      ids.push(docRef.id);
    });
    await batch.commit();
    return ids;
  };
  return firestoreIndexer;
};
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  getFirestoreDocumentIndexer,
  getFirestoreDocumentRetriever
});
//# sourceMappingURL=firestore.js.map