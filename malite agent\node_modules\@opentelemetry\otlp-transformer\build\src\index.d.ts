export { OtlpEncodingOptions, IKeyValueList, IKeyValue, IInstrumentationScope, IArrayValue, LongBits, IAnyValue, Fixed64, } from './common/types';
export { SpanContextEncodeFunction, toLongBits, OptionalSpanContextEncodeFunction, getOtlpEncoder, Encoder, HrTimeEncodeFunction, encodeAsLongBits, encodeAsString, hrTimeToNanos, } from './common';
export { IExportMetricsPartialSuccess, IValueAtQuantile, ISummaryDataPoint, ISummary, ISum, IScopeMetrics, IResourceMetrics, INumberDataPoint, IHistogramDataPoint, IHistogram, IExponentialHistogramDataPoint, IExponentialHistogram, IMetric, IGauge, IExemplar, EAggregationTemporality, IExportMetricsServiceRequest, IExportMetricsServiceResponse, IBuckets, } from './metrics/types';
export { IResource } from './resource/types';
export { IExportTracePartialSuccess, IStatus, EStatusCode, ILink, IEvent, IScopeSpans, ISpan, IResourceSpans, ESpanKind, IExportTraceServiceResponse, IExportTraceServiceRequest, } from './trace/types';
export { IExportLogsServiceResponse, IScopeLogs, IExportLogsServiceRequest, IResourceLogs, ILogRecord, IExportLogsPartialSuccess, ESeverityNumber, } from './logs/types';
export { createExportTraceServiceRequest } from './trace';
export { createExportMetricsServiceRequest } from './metrics';
export { createExportLogsServiceRequest } from './logs';
export { ProtobufLogsSerializer, ProtobufMetricsSerializer, ProtobufTraceSerializer, } from './protobuf/serializers';
export { JsonTraceSerializer, JsonLogsSerializer, JsonMetricsSerializer, } from './json/serializers';
export { ISerializer } from './common/i-serializer';
//# sourceMappingURL=index.d.ts.map