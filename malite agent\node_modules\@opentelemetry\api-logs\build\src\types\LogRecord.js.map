{"version": 3, "file": "LogRecord.js", "sourceRoot": "", "sources": ["../../../src/types/LogRecord.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAQH,IAAY,cA0BX;AA1BD,WAAY,cAAc;IACxB,iEAAe,CAAA;IACf,qDAAS,CAAA;IACT,uDAAU,CAAA;IACV,uDAAU,CAAA;IACV,uDAAU,CAAA;IACV,qDAAS,CAAA;IACT,uDAAU,CAAA;IACV,uDAAU,CAAA;IACV,uDAAU,CAAA;IACV,mDAAQ,CAAA;IACR,sDAAU,CAAA;IACV,sDAAU,CAAA;IACV,sDAAU,CAAA;IACV,oDAAS,CAAA;IACT,sDAAU,CAAA;IACV,sDAAU,CAAA;IACV,sDAAU,CAAA;IACV,sDAAU,CAAA;IACV,wDAAW,CAAA;IACX,wDAAW,CAAA;IACX,wDAAW,CAAA;IACX,sDAAU,CAAA;IACV,wDAAW,CAAA;IACX,wDAAW,CAAA;IACX,wDAAW,CAAA;AACb,CAAC,EA1BW,cAAc,GAAd,sBAAc,KAAd,sBAAc,QA0BzB", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Context, TimeInput } from '@opentelemetry/api';\nimport { AnyValue, AnyValueMap } from './AnyValue';\n\nexport type LogBody = AnyValue;\nexport type LogAttributes = AnyValueMap;\n\nexport enum SeverityNumber {\n  UNSPECIFIED = 0,\n  TRACE = 1,\n  TRACE2 = 2,\n  TRACE3 = 3,\n  TRACE4 = 4,\n  DEBUG = 5,\n  DEBUG2 = 6,\n  DEBUG3 = 7,\n  DEBUG4 = 8,\n  INFO = 9,\n  INFO2 = 10,\n  INFO3 = 11,\n  INFO4 = 12,\n  WARN = 13,\n  WARN2 = 14,\n  WARN3 = 15,\n  WARN4 = 16,\n  ERROR = 17,\n  ERROR2 = 18,\n  ERROR3 = 19,\n  ERROR4 = 20,\n  FATAL = 21,\n  FATAL2 = 22,\n  FATAL3 = 23,\n  FATAL4 = 24,\n}\n\nexport interface LogRecord {\n  /**\n   * The time when the log record occurred as UNIX Epoch time in nanoseconds.\n   */\n  timestamp?: TimeInput;\n\n  /**\n   * Time when the event was observed by the collection system.\n   */\n  observedTimestamp?: TimeInput;\n\n  /**\n   * Numerical value of the severity.\n   */\n  severityNumber?: SeverityNumber;\n\n  /**\n   * The severity text.\n   */\n  severityText?: string;\n\n  /**\n   * A value containing the body of the log record.\n   */\n  body?: LogBody;\n\n  /**\n   * Attributes that define the log record.\n   */\n  attributes?: LogAttributes;\n\n  /**\n   * The Context associated with the LogRecord.\n   */\n  context?: Context;\n}\n"]}