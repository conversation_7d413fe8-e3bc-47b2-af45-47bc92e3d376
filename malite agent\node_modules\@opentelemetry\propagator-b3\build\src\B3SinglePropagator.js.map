{"version": 3, "file": "B3SinglePropagator.js", "sourceRoot": "", "sources": ["../../src/B3SinglePropagator.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,4CAU4B;AAC5B,8CAA0D;AAC1D,qCAA6C;AAC7C,2CAAgD;AAEhD,MAAM,gBAAgB,GACpB,qFAAqF,CAAC;AACxF,MAAM,OAAO,GAAG,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;AAC/B,MAAM,cAAc,GAAG,IAAI,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;AAC3C,MAAM,WAAW,GAAG,GAAG,CAAC;AAExB,SAAS,mBAAmB,CAAC,OAAe;IAC1C,OAAO,OAAO,CAAC,MAAM,KAAK,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,OAAO,GAAG,OAAO,EAAE,CAAC;AAClE,CAAC;AAED,SAAS,mBAAmB,CAAC,aAAiC;IAC5D,IAAI,aAAa,IAAI,cAAc,CAAC,GAAG,CAAC,aAAa,CAAC,EAAE;QACtD,OAAO,gBAAU,CAAC,OAAO,CAAC;KAC3B;IACD,OAAO,gBAAU,CAAC,IAAI,CAAC;AACzB,CAAC;AAED;;;GAGG;AACH,MAAa,kBAAkB;IAC7B,MAAM,CAAC,OAAgB,EAAE,OAAgB,EAAE,MAAqB;QAC9D,MAAM,WAAW,GAAG,WAAK,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;QAClD,IACE,CAAC,WAAW;YACZ,CAAC,IAAA,wBAAkB,EAAC,WAAW,CAAC;YAChC,IAAA,0BAAmB,EAAC,OAAO,CAAC;YAE5B,OAAO;QAET,MAAM,aAAa,GACjB,OAAO,CAAC,QAAQ,CAAC,0BAAiB,CAAC,IAAI,WAAW,CAAC,UAAU,GAAG,GAAG,CAAC;QACtE,MAAM,KAAK,GAAG,GAAG,WAAW,CAAC,OAAO,IAAI,WAAW,CAAC,MAAM,IAAI,aAAa,EAAE,CAAC;QAC9E,MAAM,CAAC,GAAG,CAAC,OAAO,EAAE,6BAAiB,EAAE,KAAK,CAAC,CAAC;IAChD,CAAC;IAED,OAAO,CAAC,OAAgB,EAAE,OAAgB,EAAE,MAAqB;QAC/D,MAAM,MAAM,GAAG,MAAM,CAAC,GAAG,CAAC,OAAO,EAAE,6BAAiB,CAAC,CAAC;QACtD,MAAM,SAAS,GAAG,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;QAC7D,IAAI,OAAO,SAAS,KAAK,QAAQ;YAAE,OAAO,OAAO,CAAC;QAElD,MAAM,KAAK,GAAG,SAAS,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC;QAChD,IAAI,CAAC,KAAK;YAAE,OAAO,OAAO,CAAC;QAE3B,MAAM,CAAC,EAAE,gBAAgB,EAAE,MAAM,EAAE,aAAa,CAAC,GAAG,KAAK,CAAC;QAC1D,MAAM,OAAO,GAAG,mBAAmB,CAAC,gBAAgB,CAAC,CAAC;QAEtD,IAAI,CAAC,IAAA,oBAAc,EAAC,OAAO,CAAC,IAAI,CAAC,IAAA,mBAAa,EAAC,MAAM,CAAC;YAAE,OAAO,OAAO,CAAC;QAEvE,MAAM,UAAU,GAAG,mBAAmB,CAAC,aAAa,CAAC,CAAC;QAEtD,IAAI,aAAa,KAAK,WAAW,EAAE;YACjC,OAAO,GAAG,OAAO,CAAC,QAAQ,CAAC,0BAAiB,EAAE,aAAa,CAAC,CAAC;SAC9D;QAED,OAAO,WAAK,CAAC,cAAc,CAAC,OAAO,EAAE;YACnC,OAAO;YACP,MAAM;YACN,QAAQ,EAAE,IAAI;YACd,UAAU;SACX,CAAC,CAAC;IACL,CAAC;IAED,MAAM;QACJ,OAAO,CAAC,6BAAiB,CAAC,CAAC;IAC7B,CAAC;CACF;AA9CD,gDA8CC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  Context,\n  isSpanContextValid,\n  isValidSpanId,\n  isValidTraceId,\n  TextMapGetter,\n  TextMapPropagator,\n  TextMapSetter,\n  trace,\n  TraceFlags,\n} from '@opentelemetry/api';\nimport { isTracingSuppressed } from '@opentelemetry/core';\nimport { B3_DEBUG_FLAG_KEY } from './common';\nimport { B3_CONTEXT_HEADER } from './constants';\n\nconst B3_CONTEXT_REGEX =\n  /((?:[0-9a-f]{16}){1,2})-([0-9a-f]{16})(?:-([01d](?![0-9a-f])))?(?:-([0-9a-f]{16}))?/;\nconst PADDING = '0'.repeat(16);\nconst SAMPLED_VALUES = new Set(['d', '1']);\nconst DEBUG_STATE = 'd';\n\nfunction convertToTraceId128(traceId: string): string {\n  return traceId.length === 32 ? traceId : `${PADDING}${traceId}`;\n}\n\nfunction convertToTraceFlags(samplingState: string | undefined): TraceFlags {\n  if (samplingState && SAMPLED_VALUES.has(samplingState)) {\n    return TraceFlags.SAMPLED;\n  }\n  return TraceFlags.NONE;\n}\n\n/**\n * Propagator for the B3 single-header HTTP format.\n * Based on: https://github.com/openzipkin/b3-propagation\n */\nexport class B3SinglePropagator implements TextMapPropagator {\n  inject(context: Context, carrier: unknown, setter: TextMapSetter): void {\n    const spanContext = trace.getSpanContext(context);\n    if (\n      !spanContext ||\n      !isSpanContextValid(spanContext) ||\n      isTracingSuppressed(context)\n    )\n      return;\n\n    const samplingState =\n      context.getValue(B3_DEBUG_FLAG_KEY) || spanContext.traceFlags & 0x1;\n    const value = `${spanContext.traceId}-${spanContext.spanId}-${samplingState}`;\n    setter.set(carrier, B3_CONTEXT_HEADER, value);\n  }\n\n  extract(context: Context, carrier: unknown, getter: TextMapGetter): Context {\n    const header = getter.get(carrier, B3_CONTEXT_HEADER);\n    const b3Context = Array.isArray(header) ? header[0] : header;\n    if (typeof b3Context !== 'string') return context;\n\n    const match = b3Context.match(B3_CONTEXT_REGEX);\n    if (!match) return context;\n\n    const [, extractedTraceId, spanId, samplingState] = match;\n    const traceId = convertToTraceId128(extractedTraceId);\n\n    if (!isValidTraceId(traceId) || !isValidSpanId(spanId)) return context;\n\n    const traceFlags = convertToTraceFlags(samplingState);\n\n    if (samplingState === DEBUG_STATE) {\n      context = context.setValue(B3_DEBUG_FLAG_KEY, samplingState);\n    }\n\n    return trace.setSpanContext(context, {\n      traceId,\n      spanId,\n      isRemote: true,\n      traceFlags,\n    });\n  }\n\n  fields(): string[] {\n    return [B3_CONTEXT_HEADER];\n  }\n}\n"]}