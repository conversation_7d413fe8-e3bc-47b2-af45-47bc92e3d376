import { DetectorSync } from '../../../types';
import { ResourceDetectionConfig } from '../../../config';
import { IResource } from '../../../IResource';
/**
 * ProcessDetectorSync will be used to detect the resources related current process running
 * and being instrumented from the NodeJS Process module.
 */
declare class ProcessDetectorSync implements DetectorSync {
    detect(_config?: ResourceDetectionConfig): IResource;
}
export declare const processDetectorSync: ProcessDetectorSync;
export {};
//# sourceMappingURL=ProcessDetectorSync.d.ts.map