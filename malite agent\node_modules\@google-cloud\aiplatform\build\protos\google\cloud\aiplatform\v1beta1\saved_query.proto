// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.aiplatform.v1beta1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/struct.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.AIPlatform.V1Beta1";
option go_package = "cloud.google.com/go/aiplatform/apiv1beta1/aiplatformpb;aiplatformpb";
option java_multiple_files = true;
option java_outer_classname = "SavedQueryProto";
option java_package = "com.google.cloud.aiplatform.v1beta1";
option php_namespace = "Google\\Cloud\\AIPlatform\\V1beta1";
option ruby_package = "Google::Cloud::AIPlatform::V1beta1";

// A SavedQuery is a view of the dataset. It references a subset of annotations
// by problem type and filters.
message SavedQuery {
  option (google.api.resource) = {
    type: "aiplatform.googleapis.com/SavedQuery"
    pattern: "projects/{project}/locations/{location}/datasets/{dataset}/savedQueries/{saved_query}"
  };

  // Output only. Resource name of the SavedQuery.
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. The user-defined name of the SavedQuery.
  // The name can be up to 128 characters long and can consist of any UTF-8
  // characters.
  string display_name = 2 [(google.api.field_behavior) = REQUIRED];

  // Some additional information about the SavedQuery.
  google.protobuf.Value metadata = 12;

  // Output only. Timestamp when this SavedQuery was created.
  google.protobuf.Timestamp create_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Timestamp when SavedQuery was last updated.
  google.protobuf.Timestamp update_time = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Filters on the Annotations in the dataset.
  string annotation_filter = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. Problem type of the SavedQuery.
  // Allowed values:
  //
  // * IMAGE_CLASSIFICATION_SINGLE_LABEL
  // * IMAGE_CLASSIFICATION_MULTI_LABEL
  // * IMAGE_BOUNDING_POLY
  // * IMAGE_BOUNDING_BOX
  // * TEXT_CLASSIFICATION_SINGLE_LABEL
  // * TEXT_CLASSIFICATION_MULTI_LABEL
  // * TEXT_EXTRACTION
  // * TEXT_SENTIMENT
  // * VIDEO_CLASSIFICATION
  // * VIDEO_OBJECT_TRACKING
  string problem_type = 6 [(google.api.field_behavior) = REQUIRED];

  // Output only. Number of AnnotationSpecs in the context of the SavedQuery.
  int32 annotation_spec_count = 10 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Used to perform a consistent read-modify-write update. If not set, a blind
  // "overwrite" update happens.
  string etag = 8;

  // Output only. If the Annotations belonging to the SavedQuery can be used for
  // AutoML training.
  bool support_automl_training = 9 [(google.api.field_behavior) = OUTPUT_ONLY];
}
