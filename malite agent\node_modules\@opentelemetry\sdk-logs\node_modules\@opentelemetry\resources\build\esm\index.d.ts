export { Resource } from './Resource';
export { IResource } from './IResource';
export { defaultServiceName } from './platform';
export { DetectorSync, ResourceAttributes, Detector } from './types';
export { ResourceDetectionConfig } from './config';
export { browserDetector, browserDetectorSync, envDetector, envDetectorSync, hostDetector, hostDetectorSync, osDetector, osDetectorSync, processDetector, processDetectorSync, serviceInstanceIdDetectorSync, } from './detectors';
export { detectResourcesSync, detectResources } from './detect-resources';
//# sourceMappingURL=index.d.ts.map