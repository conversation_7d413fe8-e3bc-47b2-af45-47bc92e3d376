"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AnthropicVertex = void 0;
const Core = __importStar(require("@anthropic-ai/sdk/core"));
const Resources = __importStar(require("@anthropic-ai/sdk/resources/index"));
const google_auth_library_1 = require("google-auth-library");
const DEFAULT_VERSION = 'vertex-2023-10-16';
class AnthropicVertex extends Core.APIClient {
    /**
     * API Client for interfacing with the Anthropic Vertex API.
     *
     * @param {string | null} opts.accessToken
     * @param {string | null} opts.projectId
     * @param {GoogleAuth} opts.googleAuth - Override the default google auth config
     * @param {string | null} [opts.region=process.env['CLOUD_ML_REGION']]
     * @param {string} [opts.baseURL=process.env['ANTHROPIC_VERTEX__BASE_URL'] ?? https://${region}-aiplatform.googleapis.com/v1] - Override the default base URL for the API.
     * @param {number} [opts.timeout=10 minutes] - The maximum amount of time (in milliseconds) the client will wait for a response before timing out.
     * @param {number} [opts.httpAgent] - An HTTP agent used to manage HTTP(s) connections.
     * @param {Core.Fetch} [opts.fetch] - Specify a custom `fetch` function implementation.
     * @param {number} [opts.maxRetries=2] - The maximum number of times the client will retry a request.
     * @param {Core.Headers} opts.defaultHeaders - Default headers to include with every request to the API.
     * @param {Core.DefaultQuery} opts.defaultQuery - Default query parameters to include with every request to the API.
     */
    constructor({ baseURL = Core.readEnv('ANTHROPIC_VERTEX_BASE_URL'), region = Core.readEnv('CLOUD_ML_REGION') ?? null, projectId = Core.readEnv('ANTHROPIC_VERTEX_PROJECT_ID') ?? null, ...opts } = {}) {
        if (!region) {
            throw new Error('No region was given. The client should be instantiated with the `region` option or the `CLOUD_ML_REGION` environment variable should be set.');
        }
        const options = {
            ...opts,
            baseURL: baseURL || `https://${region}-aiplatform.googleapis.com/v1`,
        };
        super({
            baseURL: options.baseURL,
            timeout: options.timeout ?? 600000 /* 10 minutes */,
            httpAgent: options.httpAgent,
            maxRetries: options.maxRetries,
            fetch: options.fetch,
        });
        this.messages = new Resources.Messages(this);
        this._options = options;
        this.region = region;
        this.projectId = projectId;
        this.accessToken = options.accessToken ?? null;
        this._auth =
            options.googleAuth ?? new google_auth_library_1.GoogleAuth({ scopes: 'https://www.googleapis.com/auth/cloud-platform' });
        this._authClientPromise = this._auth.getClient();
    }
    defaultQuery() {
        return this._options.defaultQuery;
    }
    defaultHeaders(opts) {
        return {
            ...super.defaultHeaders(opts),
            ...this._options.defaultHeaders,
        };
    }
    async prepareOptions(options) {
        const authClient = await this._authClientPromise;
        const authHeaders = await authClient.getRequestHeaders();
        const projectId = authClient.projectId ?? authHeaders['x-goog-user-project'];
        if (!this.projectId && projectId) {
            this.projectId = projectId;
        }
        options.headers = { ...authHeaders, ...options.headers };
    }
    buildRequest(options) {
        if (Core.isObj(options.body)) {
            if (!options.body['anthropic_version']) {
                options.body['anthropic_version'] = DEFAULT_VERSION;
            }
        }
        if (options.path === '/v1/messages' && options.method === 'post') {
            if (!this.projectId) {
                throw new Error('No projectId was given and it could not be resolved from credentials. The client should be instantiated with the `projectId` option or the `ANTHROPIC_VERTEX_PROJECT_ID` environment variable should be set.');
            }
            if (!Core.isObj(options.body)) {
                throw new Error('Expected request body to be an object for post /v1/messages');
            }
            const model = options.body['model'];
            options.body['model'] = undefined;
            const stream = options.body['stream'] ?? false;
            const specifier = stream ? 'streamRawPredict' : 'rawPredict';
            options.path = `/projects/${this.projectId}/locations/${this.region}/publishers/anthropic/models/${model}:${specifier}`;
        }
        return super.buildRequest(options);
    }
}
exports.AnthropicVertex = AnthropicVertex;
//# sourceMappingURL=client.js.map