{"interfaces": {"google.cloud.aiplatform.v1.ModelService": {"retry_codes": {"non_idempotent": [], "idempotent": ["DEADLINE_EXCEEDED", "UNAVAILABLE"]}, "retry_params": {"default": {"initial_retry_delay_millis": 100, "retry_delay_multiplier": 1.3, "max_retry_delay_millis": 60000, "initial_rpc_timeout_millis": 60000, "rpc_timeout_multiplier": 1, "max_rpc_timeout_millis": 60000, "total_timeout_millis": 600000}}, "methods": {"UploadModel": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "GetModel": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "ListModels": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "ListModelVersions": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "ListModelVersionCheckpoints": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "UpdateModel": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "UpdateExplanationDataset": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "DeleteModel": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "DeleteModelVersion": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "MergeVersionAliases": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "ExportModel": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "CopyModel": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "ImportModelEvaluation": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "BatchImportModelEvaluationSlices": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "BatchImportEvaluatedAnnotations": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "GetModelEvaluation": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "ListModelEvaluations": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "GetModelEvaluationSlice": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "ListModelEvaluationSlices": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}}}}}