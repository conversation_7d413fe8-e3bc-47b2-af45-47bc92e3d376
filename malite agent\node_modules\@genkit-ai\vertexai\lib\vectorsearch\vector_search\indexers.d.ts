import * as _genkit_ai_ai_retriever from '@genkit-ai/ai/retriever';
import { z, Genkit } from 'genkit';
import { IndexerAction } from 'genkit/retriever';
import { f as VertexVectorSearchOptions } from '../../types-gLlb90fM.js';
import '../../types-Bc0LKM8D.js';
import 'google-auth-library';
import '@google-cloud/vertexai';
import 'genkit/model';
import '@google-cloud/aiplatform';
import 'genkit/embedder';

/**
 * Creates a reference to a Vertex AI indexer.
 *
 * @param {Object} params - The parameters for the indexer reference.
 * @param {string} params.indexId - The ID of the Vertex AI index.
 * @param {string} [params.displayName] - An optional display name for the indexer.
 * @returns {Object} - The indexer reference object.
 */
declare const vertexAiIndexerRef: (params: {
    indexId: string;
    displayName?: string;
}) => _genkit_ai_ai_retriever.IndexerReference<z.ZodOptional<z.ZodAny>>;
/**
 * Creates Vertex AI indexers.
 *
 * This function returns a list of indexer actions for Vertex AI based on the provided
 * vector search options and embedder configurations.
 *
 * @param {VertexVectorSearchOptions<EmbedderCustomOptions>} params - The parameters for creating the indexers.
 * @returns {IndexerAction<z.ZodTypeAny>[]} - An array of indexer actions.
 */
declare function vertexAiIndexers<EmbedderCustomOptions extends z.ZodTypeAny>(ai: Genkit, params: VertexVectorSearchOptions<EmbedderCustomOptions>): IndexerAction<z.ZodTypeAny>[];

export { vertexAiIndexerRef, vertexAiIndexers };
