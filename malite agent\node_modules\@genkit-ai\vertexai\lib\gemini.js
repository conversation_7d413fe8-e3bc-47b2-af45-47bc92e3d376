"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
var gemini_exports = {};
__export(gemini_exports, {
  GENERIC_GEMINI_MODEL: () => GENERIC_GEMINI_MODEL,
  GeminiConfigSchema: () => GeminiConfigSchema,
  SUPPORTED_GEMINI_MODELS: () => SUPPORTED_GEMINI_MODELS,
  SUPPORTED_V15_MODELS: () => SUPPORTED_V15_MODELS,
  SafetySettingsSchema: () => SafetySettingsSchema,
  cleanSchema: () => cleanSchema,
  defineGeminiKnownModel: () => defineGeminiKnownModel,
  defineGeminiModel: () => defineGeminiModel,
  fromGeminiCandidate: () => fromGeminiCandidate,
  gemini: () => gemini,
  gemini10Pro: () => gemini10Pro,
  gemini15Flash: () => gemini15Flash,
  gemini15Pro: () => gemini15Pro,
  gemini20Flash: () => gemini20Flash,
  gemini20Flash001: () => gemini20Flash001,
  gemini20FlashLite: () => gemini20FlashLite,
  gemini20FlashLitePreview0205: () => gemini20FlashLitePreview0205,
  gemini20ProExp0205: () => gemini20ProExp0205,
  gemini25FlashPreview0417: () => gemini25FlashPreview0417,
  gemini25ProExp0325: () => gemini25ProExp0325,
  gemini25ProPreview0325: () => gemini25ProPreview0325,
  toGeminiMessage: () => toGeminiMessage,
  toGeminiSystemInstruction: () => toGeminiSystemInstruction,
  toGeminiTool: () => toGeminiTool
});
module.exports = __toCommonJS(gemini_exports);
var import_vertexai = require("@google-cloud/vertexai");
var import_resources = require("@google-cloud/vertexai/build/src/resources/index.js");
var import_genkit = require("genkit");
var import_model = require("genkit/model");
var import_middleware = require("genkit/model/middleware");
var import_tracing = require("genkit/tracing");
var import_google_auth_library = require("google-auth-library");
var import_context_caching = require("./context-caching/index.js");
var import_utils = require("./context-caching/utils.js");
const SafetySettingsSchema = import_genkit.z.object({
  category: import_genkit.z.enum([
    /** The harm category is unspecified. */
    "HARM_CATEGORY_UNSPECIFIED",
    /** The harm category is hate speech. */
    "HARM_CATEGORY_HATE_SPEECH",
    /** The harm category is dangerous content. */
    "HARM_CATEGORY_DANGEROUS_CONTENT",
    /** The harm category is harassment. */
    "HARM_CATEGORY_HARASSMENT",
    /** The harm category is sexually explicit content. */
    "HARM_CATEGORY_SEXUALLY_EXPLICIT"
  ]),
  threshold: import_genkit.z.enum([
    "BLOCK_LOW_AND_ABOVE",
    "BLOCK_MEDIUM_AND_ABOVE",
    "BLOCK_ONLY_HIGH",
    "BLOCK_NONE"
  ])
});
const VertexRetrievalSchema = import_genkit.z.object({
  datastore: import_genkit.z.object({
    projectId: import_genkit.z.string().describe("Google Cloud Project ID.").optional(),
    location: import_genkit.z.string().describe("Google Cloud region e.g. us-central1.").optional(),
    dataStoreId: import_genkit.z.string().describe(
      'The data store id, when project id and location are provided as separate options. Alternatively, the full path to the data store should be provided in the form: "projects/{project}/locations/{location}/collections/default_collection/dataStores/{data_store}".'
    )
  }).describe("Vertex AI Search data store details"),
  disableAttribution: import_genkit.z.boolean().describe(
    "Disable using the search data in detecting grounding attribution. This does not affect how the result is given to the model for generation."
  ).optional()
});
const GoogleSearchRetrievalSchema = import_genkit.z.object({
  disableAttribution: import_genkit.z.boolean().describe(
    "Disable using the search data in detecting grounding attribution. This does not affect how the result is given to the model for generation."
  ).optional()
});
const GeminiConfigSchema = import_model.GenerationCommonConfigSchema.extend({
  maxOutputTokens: import_genkit.z.number().min(1).max(8192).describe(import_model.GenerationCommonConfigDescriptions.maxOutputTokens).optional(),
  temperature: import_genkit.z.number().min(0).max(1).describe(
    import_model.GenerationCommonConfigDescriptions.temperature + " The default value is 0.2."
  ).optional(),
  topK: import_genkit.z.number().min(1).max(40).describe(
    import_model.GenerationCommonConfigDescriptions.topK + " The default value is 40."
  ).optional(),
  topP: import_genkit.z.number().min(0).max(1).describe(
    import_model.GenerationCommonConfigDescriptions.topP + " The default value is 0.8."
  ).optional(),
  location: import_genkit.z.string().describe("Google Cloud region e.g. us-central1.").optional(),
  /**
   * Safety filter settings. See: https://cloud.google.com/vertex-ai/generative-ai/docs/multimodal/configure-safety-filters#configurable-filters
   *
   * E.g.
   *
   * ```js
   * config: {
   *   safetySettings: [
   *     {
   *       category: 'HARM_CATEGORY_HATE_SPEECH',
   *       threshold: 'BLOCK_LOW_AND_ABOVE',
   *     },
   *     {
   *       category: 'HARM_CATEGORY_DANGEROUS_CONTENT',
   *       threshold: 'BLOCK_MEDIUM_AND_ABOVE',
   *     },
   *     {
   *       category: 'HARM_CATEGORY_HARASSMENT',
   *       threshold: 'BLOCK_ONLY_HIGH',
   *     },
   *     {
   *       category: 'HARM_CATEGORY_SEXUALLY_EXPLICIT',
   *       threshold: 'BLOCK_NONE',
   *     },
   *   ],
   * }
   * ```
   */
  safetySettings: import_genkit.z.array(SafetySettingsSchema).describe(
    "Adjust how likely you are to see responses that could be harmful. Content is blocked based on the probability that it is harmful."
  ).optional(),
  /**
   * Vertex retrieval options.
   *
   * E.g.
   *
   * ```js
   *   config: {
   *     vertexRetrieval: {
   *       datastore: {
   *         projectId: 'your-cloud-project',
   *         location: 'us-central1',
   *         collection: 'your-collection',
   *       },
   *       disableAttribution: true,
   *     }
   *   }
   * ```
   */
  vertexRetrieval: VertexRetrievalSchema.describe(
    "Retrieve from Vertex AI Search data store for grounding generative responses."
  ).optional(),
  /**
   * Google Search retrieval options.
   *
   * ```js
   *   config: {
   *     googleSearchRetrieval: {
   *       disableAttribution: true,
   *     }
   *   }
   * ```
   */
  googleSearchRetrieval: GoogleSearchRetrievalSchema.describe(
    "Retrieve public web data for grounding, powered by Google Search."
  ).optional(),
  /**
   * Function calling options.
   *
   * E.g. forced tool call:
   *
   * ```js
   *   config: {
   *     functionCallingConfig: {
   *       mode: 'ANY',
   *     }
   *   }
   * ```
   */
  functionCallingConfig: import_genkit.z.object({
    mode: import_genkit.z.enum(["MODE_UNSPECIFIED", "AUTO", "ANY", "NONE"]).optional(),
    allowedFunctionNames: import_genkit.z.array(import_genkit.z.string()).optional()
  }).describe(
    "Controls how the model uses the provided tools (function declarations). With AUTO (Default) mode, the model decides whether to generate a natural language response or suggest a function call based on the prompt and context. With ANY, the model is constrained to always predict a function call and guarantee function schema adherence. With NONE, the model is prohibited from making function calls."
  ).optional()
}).passthrough();
function gemini(version, options = {}) {
  const nearestModel = nearestGeminiModelRef(version);
  return (0, import_model.modelRef)({
    name: `vertexai/${version}`,
    config: options,
    configSchema: GeminiConfigSchema,
    info: {
      ...nearestModel.info,
      // If exact suffix match for a known model, use its label, otherwise create a new label
      label: nearestModel.name.endsWith(version) ? nearestModel.info?.label : `Vertex AI - ${version}`
    }
  });
}
function nearestGeminiModelRef(version, options = {}) {
  const matchingKey = longestMatchingPrefix(
    version,
    Object.keys(SUPPORTED_GEMINI_MODELS)
  );
  if (matchingKey) {
    return SUPPORTED_GEMINI_MODELS[matchingKey].withConfig({
      ...options,
      version
    });
  }
  return GENERIC_GEMINI_MODEL.withConfig({ ...options, version });
}
function longestMatchingPrefix(version, potentialMatches) {
  return potentialMatches.filter((p) => version.startsWith(p)).reduce(
    (longest, current) => current.length > longest.length ? current : longest,
    ""
  );
}
const gemini10Pro = (0, import_model.modelRef)({
  name: "vertexai/gemini-1.0-pro",
  info: {
    label: "Vertex AI - Gemini Pro",
    versions: ["gemini-1.0-pro-001", "gemini-1.0-pro-002"],
    supports: {
      multiturn: true,
      media: false,
      tools: true,
      systemRole: true,
      constrained: "no-tools",
      toolChoice: true
    }
  },
  configSchema: GeminiConfigSchema
});
const gemini15Pro = (0, import_model.modelRef)({
  name: "vertexai/gemini-1.5-pro",
  info: {
    label: "Vertex AI - Gemini 1.5 Pro",
    versions: ["gemini-1.5-pro-001", "gemini-1.5-pro-002"],
    supports: {
      multiturn: true,
      media: true,
      tools: true,
      toolChoice: true,
      systemRole: true,
      constrained: "no-tools"
    }
  },
  configSchema: GeminiConfigSchema
});
const gemini15Flash = (0, import_model.modelRef)({
  name: "vertexai/gemini-1.5-flash",
  info: {
    label: "Vertex AI - Gemini 1.5 Flash",
    versions: ["gemini-1.5-flash-001", "gemini-1.5-flash-002"],
    supports: {
      multiturn: true,
      media: true,
      tools: true,
      toolChoice: true,
      systemRole: true,
      constrained: "no-tools"
    }
  },
  configSchema: GeminiConfigSchema
});
const gemini20Flash001 = (0, import_model.modelRef)({
  name: "vertexai/gemini-2.0-flash-001",
  info: {
    label: "Vertex AI - Gemini 2.0 Flash 001",
    versions: [],
    supports: {
      multiturn: true,
      media: true,
      tools: true,
      toolChoice: true,
      systemRole: true,
      constrained: "no-tools"
    }
  },
  configSchema: GeminiConfigSchema
});
const gemini20Flash = (0, import_model.modelRef)({
  name: "vertexai/gemini-2.0-flash",
  info: {
    label: "Vertex AI - Gemini 2.0 Flash",
    versions: [],
    supports: {
      multiturn: true,
      media: true,
      tools: true,
      toolChoice: true,
      systemRole: true,
      constrained: "no-tools"
    }
  },
  configSchema: GeminiConfigSchema
});
const gemini20FlashLite = (0, import_model.modelRef)({
  name: "vertexai/gemini-2.0-flash-lite",
  info: {
    label: "Vertex AI - Gemini 2.0 Flash Lite",
    versions: [],
    supports: {
      multiturn: true,
      media: true,
      tools: true,
      toolChoice: true,
      systemRole: true,
      constrained: "no-tools"
    }
  },
  configSchema: GeminiConfigSchema
});
const gemini20FlashLitePreview0205 = (0, import_model.modelRef)({
  name: "vertexai/gemini-2.0-flash-lite-preview-02-05",
  info: {
    label: "Vertex AI - Gemini 2.0 Flash Lite Preview 02-05",
    versions: [],
    supports: {
      multiturn: true,
      media: true,
      tools: true,
      toolChoice: true,
      systemRole: true,
      constrained: "no-tools"
    }
  },
  configSchema: GeminiConfigSchema
});
const gemini20ProExp0205 = (0, import_model.modelRef)({
  name: "vertexai/gemini-2.0-pro-exp-02-05",
  info: {
    label: "Vertex AI - Gemini 2.0 Flash Pro Experimental 02-05",
    versions: [],
    supports: {
      multiturn: true,
      media: true,
      tools: true,
      toolChoice: true,
      systemRole: true,
      constrained: "no-tools"
    }
  },
  configSchema: GeminiConfigSchema
});
const gemini25FlashPreview0417 = (0, import_model.modelRef)({
  name: "vertexai/gemini-2.5-flash-preview-04-17",
  info: {
    label: "Vertex AI - Gemini 2.5 Flash Preview 04-17",
    versions: [],
    supports: {
      multiturn: true,
      media: true,
      tools: true,
      toolChoice: true,
      systemRole: true,
      constrained: "no-tools"
    }
  },
  configSchema: GeminiConfigSchema
});
const gemini25ProExp0325 = (0, import_model.modelRef)({
  name: "vertexai/gemini-2.5-pro-exp-03-25",
  info: {
    label: "Vertex AI - Gemini 2.5 Pro Experimental 03-25",
    versions: [],
    supports: {
      multiturn: true,
      media: true,
      tools: true,
      toolChoice: true,
      systemRole: true,
      constrained: "no-tools"
    }
  },
  configSchema: GeminiConfigSchema
});
const gemini25ProPreview0325 = (0, import_model.modelRef)({
  name: "vertexai/gemini-2.5-pro-preview-03-25",
  info: {
    label: "Vertex AI - Gemini 2.5 Pro Preview 03-25",
    versions: [],
    supports: {
      multiturn: true,
      media: true,
      tools: true,
      toolChoice: true,
      systemRole: true,
      constrained: "no-tools"
    }
  },
  configSchema: GeminiConfigSchema
});
const GENERIC_GEMINI_MODEL = (0, import_model.modelRef)({
  name: "vertexai/gemini",
  configSchema: GeminiConfigSchema,
  info: {
    label: "Google Gemini",
    supports: {
      multiturn: true,
      media: true,
      tools: true,
      toolChoice: true,
      systemRole: true
    }
  }
});
const SUPPORTED_V1_MODELS = {
  "gemini-1.0-pro": gemini10Pro
};
const SUPPORTED_V15_MODELS = {
  "gemini-1.5-pro": gemini15Pro,
  "gemini-1.5-flash": gemini15Flash,
  "gemini-2.0-flash": gemini20Flash,
  "gemini-2.0-flash-001": gemini20Flash001,
  "gemini-2.0-flash-lite": gemini20FlashLite,
  "gemini-2.0-flash-lite-preview-02-05": gemini20FlashLitePreview0205,
  "gemini-2.0-pro-exp-02-05": gemini20ProExp0205,
  "gemini-2.5-pro-exp-03-25": gemini25ProExp0325,
  "gemini-2.5-pro-preview-03-25": gemini25ProPreview0325,
  "gemini-2.5-flash-preview-04-17": gemini25FlashPreview0417
};
const SUPPORTED_GEMINI_MODELS = {
  ...SUPPORTED_V15_MODELS
};
function toGeminiRole(role, modelInfo) {
  switch (role) {
    case "user":
      return "user";
    case "model":
      return "model";
    case "system":
      if (modelInfo && modelInfo.supports?.systemRole) {
        throw new Error(
          "system role is only supported for a single message in the first position"
        );
      } else {
        throw new Error("system role is not supported");
      }
    case "tool":
      return "function";
    default:
      return "user";
  }
}
const toGeminiTool = (tool) => {
  const declaration = {
    name: tool.name.replace(/\//g, "__"),
    // Gemini throws on '/' in tool name
    description: tool.description,
    parameters: convertSchemaProperty(tool.inputSchema)
  };
  return declaration;
};
const toGeminiFileDataPart = (part) => {
  const media = part.media;
  if (media.url.startsWith("gs://") || media.url.startsWith("http")) {
    if (!media.contentType)
      throw new Error(
        "Must supply contentType when using media from http(s):// or gs:// URLs."
      );
    return {
      fileData: {
        mimeType: media.contentType,
        fileUri: media.url
      }
    };
  } else if (media.url.startsWith("data:")) {
    const dataUrl = media.url;
    const b64Data = dataUrl.substring(dataUrl.indexOf(",") + 1);
    const contentType = media.contentType || dataUrl.substring(dataUrl.indexOf(":") + 1, dataUrl.indexOf(";"));
    return { inlineData: { mimeType: contentType, data: b64Data } };
  }
  throw Error(
    "Could not convert genkit part to gemini tool response part: missing file data"
  );
};
const toGeminiToolRequestPart = (part) => {
  if (!part?.toolRequest?.input) {
    throw Error(
      "Could not convert genkit part to gemini tool response part: missing tool request data"
    );
  }
  return {
    functionCall: {
      name: part.toolRequest.name,
      args: part.toolRequest.input
    }
  };
};
const toGeminiToolResponsePart = (part) => {
  if (!part?.toolResponse?.output) {
    throw Error(
      "Could not convert genkit part to gemini tool response part: missing tool response data"
    );
  }
  return {
    functionResponse: {
      name: part.toolResponse.name,
      response: {
        name: part.toolResponse.name,
        content: part.toolResponse.output
      }
    }
  };
};
function toGeminiSystemInstruction(message) {
  return {
    role: "user",
    parts: message.content.map(toGeminiPart)
  };
}
function toGeminiMessage(message, modelInfo) {
  let sortedParts = message.content;
  if (message.role === "tool") {
    sortedParts = [...message.content].sort((a, b) => {
      const aRef = a.toolResponse?.ref;
      const bRef = b.toolResponse?.ref;
      if (!aRef && !bRef) return 0;
      if (!aRef) return 1;
      if (!bRef) return -1;
      return parseInt(aRef, 10) - parseInt(bRef, 10);
    });
  }
  return {
    role: toGeminiRole(message.role, modelInfo),
    parts: sortedParts.map(toGeminiPart)
  };
}
function fromGeminiFinishReason(reason) {
  if (!reason) return "unknown";
  switch (reason) {
    case "STOP":
      return "stop";
    case "MAX_TOKENS":
      return "length";
    case "SAFETY":
    // blocked for safety
    case "RECITATION":
      return "blocked";
    default:
      return "unknown";
  }
}
function toGeminiPart(part) {
  if (part.text) {
    return { text: part.text };
  } else if (part.media) {
    return toGeminiFileDataPart(part);
  } else if (part.toolRequest) {
    return toGeminiToolRequestPart(part);
  } else if (part.toolResponse) {
    return toGeminiToolResponsePart(part);
  } else {
    throw new Error("unsupported type");
  }
}
function fromGeminiInlineDataPart(part) {
  if (!part.inlineData || !part.inlineData.hasOwnProperty("mimeType") || !part.inlineData.hasOwnProperty("data")) {
    throw new Error("Invalid GeminiPart: missing required properties");
  }
  const { mimeType, data } = part.inlineData;
  const dataUrl = `data:${mimeType};base64,${data}`;
  return {
    media: {
      url: dataUrl,
      contentType: mimeType
    }
  };
}
function fromGeminiFileDataPart(part) {
  if (!part.fileData || !part.fileData.hasOwnProperty("mimeType") || !part.fileData.hasOwnProperty("url")) {
    throw new Error(
      "Invalid Gemini File Data Part: missing required properties"
    );
  }
  return {
    media: {
      url: part.fileData?.fileUri,
      contentType: part.fileData?.mimeType
    }
  };
}
function fromGeminiFunctionCallPart(part, ref) {
  if (!part.functionCall) {
    throw new Error(
      "Invalid Gemini Function Call Part: missing function call data"
    );
  }
  return {
    toolRequest: {
      name: part.functionCall.name,
      input: part.functionCall.args,
      ref
    }
  };
}
function fromGeminiFunctionResponsePart(part, ref) {
  if (!part.functionResponse) {
    throw new Error(
      "Invalid Gemini Function Call Part: missing function call data"
    );
  }
  return {
    toolResponse: {
      name: part.functionResponse.name.replace(/__/g, "/"),
      // restore slashes
      output: part.functionResponse.response,
      ref
    }
  };
}
function fromGeminiPart(part, jsonMode, ref) {
  if (part.text !== void 0) {
    if (part.thought === true) {
      return { reasoning: part.text };
    }
    return { text: part.text };
  }
  if (part.inlineData) return fromGeminiInlineDataPart(part);
  if (part.fileData) return fromGeminiFileDataPart(part);
  if (part.functionCall) return fromGeminiFunctionCallPart(part, ref);
  if (part.functionResponse) return fromGeminiFunctionResponsePart(part, ref);
  throw new Error(
    "Part type is unsupported/corrupted. Either data is missing or type cannot be inferred from type."
  );
}
function fromGeminiCandidate(candidate, jsonMode) {
  const parts = candidate.content.parts || [];
  const genkitCandidate = {
    index: candidate.index || 0,
    message: {
      role: "model",
      content: parts.map((part, index) => {
        return fromGeminiPart(part, jsonMode, index.toString());
      })
    },
    finishReason: fromGeminiFinishReason(candidate.finishReason),
    finishMessage: candidate.finishMessage,
    custom: {
      safetyRatings: candidate.safetyRatings,
      citationMetadata: candidate.citationMetadata
    }
  };
  return genkitCandidate;
}
function convertSchemaProperty(property) {
  if (!property || !property.type) {
    return void 0;
  }
  const baseSchema = {};
  if (property.description) {
    baseSchema.description = property.description;
  }
  if (property.enum) {
    baseSchema.enum = property.enum;
  }
  if (property.nullable) {
    baseSchema.nullable = property.nullable;
  }
  let propertyType;
  if (Array.isArray(property.type)) {
    const types = property.type;
    if (types.includes("null")) {
      baseSchema.nullable = true;
    }
    propertyType = types.find((t) => t !== "null");
  } else {
    propertyType = property.type;
  }
  if (propertyType === "object") {
    const nestedProperties = {};
    Object.keys(property.properties).forEach((key) => {
      nestedProperties[key] = convertSchemaProperty(property.properties[key]);
    });
    return {
      ...baseSchema,
      type: import_vertexai.FunctionDeclarationSchemaType.OBJECT,
      properties: nestedProperties,
      required: property.required
    };
  } else if (propertyType === "array") {
    return {
      ...baseSchema,
      type: import_vertexai.FunctionDeclarationSchemaType.ARRAY,
      items: convertSchemaProperty(property.items)
    };
  } else {
    const schemaType = import_vertexai.FunctionDeclarationSchemaType[propertyType.toUpperCase()];
    if (!schemaType) {
      throw new import_genkit.GenkitError({
        status: "INVALID_ARGUMENT",
        message: `Unsupported property type ${propertyType.toUpperCase()}`
      });
    }
    return {
      ...baseSchema,
      type: schemaType
    };
  }
}
function cleanSchema(schema) {
  const out = structuredClone(schema);
  for (const key in out) {
    if (key === "$schema" || key === "additionalProperties") {
      delete out[key];
      continue;
    }
    if (typeof out[key] === "object") {
      out[key] = cleanSchema(out[key]);
    }
    if (key === "type" && Array.isArray(out[key])) {
      out[key] = out[key].find((t) => t !== "null");
    }
  }
  return out;
}
function defineGeminiKnownModel(ai, name, vertexClientFactory, options, debugTraces) {
  const modelName = `vertexai/${name}`;
  const model = SUPPORTED_GEMINI_MODELS[name];
  if (!model) throw new Error(`Unsupported model: ${name}`);
  return defineGeminiModel({
    ai,
    modelName,
    version: name,
    modelInfo: model?.info,
    vertexClientFactory,
    options,
    debugTraces
  });
}
function defineGeminiModel({
  ai,
  modelName,
  version,
  modelInfo,
  vertexClientFactory,
  options,
  debugTraces
}) {
  const middlewares = [];
  if (SUPPORTED_V1_MODELS[version]) {
    middlewares.push((0, import_middleware.simulateSystemPrompt)());
  }
  if (modelInfo?.supports?.media) {
    middlewares.push(
      (0, import_middleware.downloadRequestMedia)({
        maxBytes: 1024 * 1024 * 20,
        filter: (part) => {
          try {
            const url = new URL(part.media.url);
            if (
              // Gemini can handle these URLs
              ["www.youtube.com", "youtube.com", "youtu.be"].includes(
                url.hostname
              )
            )
              return false;
          } catch {
          }
          return true;
        }
      })
    );
  }
  return ai.defineModel(
    {
      name: modelName,
      ...modelInfo,
      configSchema: GeminiConfigSchema,
      use: middlewares
    },
    async (request, sendChunk) => {
      const vertex = vertexClientFactory(request);
      const messages = [...request.messages];
      if (messages.length === 0) throw new Error("No messages provided.");
      let systemInstruction = void 0;
      if (!SUPPORTED_V1_MODELS[version]) {
        const systemMessage = messages.find((m) => m.role === "system");
        if (systemMessage) {
          messages.splice(messages.indexOf(systemMessage), 1);
          systemInstruction = toGeminiSystemInstruction(systemMessage);
        }
      }
      const requestConfig = request.config;
      const {
        functionCallingConfig,
        version: versionFromConfig,
        googleSearchRetrieval,
        vertexRetrieval,
        location,
        // location can be overridden via config, take it out.
        safetySettings,
        ...restOfConfig
      } = requestConfig;
      const tools = request.tools?.length ? [{ functionDeclarations: request.tools.map(toGeminiTool) }] : [];
      let toolConfig;
      if (functionCallingConfig) {
        toolConfig = {
          functionCallingConfig: {
            allowedFunctionNames: functionCallingConfig.allowedFunctionNames,
            mode: toFunctionModeEnum(functionCallingConfig.mode)
          }
        };
      } else if (request.toolChoice) {
        toolConfig = {
          functionCallingConfig: {
            mode: toGeminiFunctionModeEnum(request.toolChoice)
          }
        };
      }
      const jsonMode = (request.output?.format === "json" || !!request.output?.schema) && tools.length === 0;
      let chatRequest = {
        systemInstruction,
        tools,
        toolConfig,
        history: messages.slice(0, -1).map((message) => toGeminiMessage(message, modelInfo)),
        generationConfig: {
          ...restOfConfig,
          candidateCount: request.candidates || void 0,
          responseMimeType: jsonMode ? "application/json" : void 0
        },
        safetySettings: toGeminiSafetySettings(safetySettings)
      };
      const modelVersion = versionFromConfig || version;
      const cacheConfigDetails = (0, import_utils.extractCacheConfig)(request);
      const apiClient = new import_resources.ApiClient(
        options.projectId,
        options.location,
        "v1beta1",
        new import_google_auth_library.GoogleAuth(options.googleAuth)
      );
      const { chatRequest: updatedChatRequest, cache } = await (0, import_context_caching.handleCacheIfNeeded)(
        apiClient,
        request,
        chatRequest,
        modelVersion,
        cacheConfigDetails
      );
      let genModel;
      if (jsonMode && request.output?.constrained) {
        updatedChatRequest.generationConfig.responseSchema = cleanSchema(
          request.output.schema
        );
      }
      if (googleSearchRetrieval) {
        updatedChatRequest.tools?.push({
          googleSearchRetrieval
        });
      }
      if (vertexRetrieval) {
        const _projectId = vertexRetrieval.datastore.projectId || options.projectId;
        const _location = vertexRetrieval.datastore.location || options.location;
        const _dataStoreId = vertexRetrieval.datastore.dataStoreId;
        const datastore = `projects/${_projectId}/locations/${_location}/collections/default_collection/dataStores/${_dataStoreId}`;
        updatedChatRequest.tools?.push({
          retrieval: {
            vertexAiSearch: {
              datastore
            },
            disableAttribution: vertexRetrieval.disableAttribution
          }
        });
      }
      const msg = toGeminiMessage(messages[messages.length - 1], modelInfo);
      if (cache) {
        genModel = vertex.preview.getGenerativeModelFromCachedContent(
          cache,
          {
            model: modelVersion
          },
          {
            apiClient: import_genkit.GENKIT_CLIENT_HEADER
          }
        );
      } else {
        genModel = vertex.preview.getGenerativeModel(
          {
            model: modelVersion
          },
          {
            apiClient: import_genkit.GENKIT_CLIENT_HEADER
          }
        );
      }
      const callGemini = async () => {
        let response;
        if (sendChunk) {
          const result = await genModel.startChat(updatedChatRequest).sendMessageStream(msg.parts);
          for await (const item of result.stream) {
            item.candidates?.forEach(
              (candidate) => {
                const c = fromGeminiCandidate(candidate, jsonMode);
                sendChunk({
                  index: c.index,
                  content: c.message.content
                });
              }
            );
          }
          response = await result.response;
        } else {
          const result = await genModel.startChat(updatedChatRequest).sendMessage(msg.parts);
          response = result.response;
        }
        if (!response.candidates?.length) {
          throw new import_genkit.GenkitError({
            status: "FAILED_PRECONDITION",
            message: "No valid candidates returned."
          });
        }
        const candidateData = response.candidates.map(
          (c) => fromGeminiCandidate(c, jsonMode)
        );
        return {
          candidates: candidateData,
          custom: response,
          usage: {
            ...(0, import_model.getBasicUsageStats)(request.messages, candidateData),
            inputTokens: response.usageMetadata?.promptTokenCount,
            outputTokens: response.usageMetadata?.candidatesTokenCount,
            totalTokens: response.usageMetadata?.totalTokenCount,
            cachedContentTokens: response.usageMetadata?.cachedContentTokenCount
          }
        };
      };
      return debugTraces ? await (0, import_tracing.runInNewSpan)(
        ai.registry,
        {
          metadata: {
            name: sendChunk ? "sendMessageStream" : "sendMessage"
          }
        },
        async (metadata) => {
          metadata.input = {
            sdk: "@google-cloud/vertexai",
            cache,
            model: genModel.getModelName(),
            chatOptions: updatedChatRequest,
            parts: msg.parts,
            options
          };
          const response = await callGemini();
          metadata.output = response.custom;
          return response;
        }
      ) : await callGemini();
    }
  );
}
function toFunctionModeEnum(enumMode) {
  if (enumMode === void 0) {
    return void 0;
  }
  switch (enumMode) {
    case "MODE_UNSPECIFIED": {
      return import_vertexai.FunctionCallingMode.MODE_UNSPECIFIED;
    }
    case "ANY": {
      return import_vertexai.FunctionCallingMode.ANY;
    }
    case "AUTO": {
      return import_vertexai.FunctionCallingMode.AUTO;
    }
    case "NONE": {
      return import_vertexai.FunctionCallingMode.NONE;
    }
    default:
      throw new Error(`unsupported function calling mode: ${enumMode}`);
  }
}
function toGeminiSafetySettings(genkitSettings) {
  if (!genkitSettings) return void 0;
  return genkitSettings.map((s) => {
    return {
      category: s.category,
      threshold: s.threshold
    };
  });
}
function toGeminiFunctionModeEnum(genkitMode) {
  if (genkitMode === void 0) {
    return void 0;
  }
  switch (genkitMode) {
    case "required": {
      return import_vertexai.FunctionCallingMode.ANY;
    }
    case "auto": {
      return import_vertexai.FunctionCallingMode.AUTO;
    }
    case "none": {
      return import_vertexai.FunctionCallingMode.NONE;
    }
    default:
      throw new Error(`unsupported function calling mode: ${genkitMode}`);
  }
}
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  GENERIC_GEMINI_MODEL,
  GeminiConfigSchema,
  SUPPORTED_GEMINI_MODELS,
  SUPPORTED_V15_MODELS,
  SafetySettingsSchema,
  cleanSchema,
  defineGeminiKnownModel,
  defineGeminiModel,
  fromGeminiCandidate,
  gemini,
  gemini10Pro,
  gemini15Flash,
  gemini15Pro,
  gemini20Flash,
  gemini20Flash001,
  gemini20FlashLite,
  gemini20FlashLitePreview0205,
  gemini20ProExp0205,
  gemini25FlashPreview0417,
  gemini25ProExp0325,
  gemini25ProPreview0325,
  toGeminiMessage,
  toGeminiSystemInstruction,
  toGeminiTool
});
//# sourceMappingURL=gemini.js.map