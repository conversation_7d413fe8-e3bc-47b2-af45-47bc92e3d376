"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
var types_exports = {};
__export(types_exports, {
  VertexAIEvaluationMetricType: () => VertexAIEvaluationMetricType
});
module.exports = __toCommonJS(types_exports);
var VertexAIEvaluationMetricType = /* @__PURE__ */ ((VertexAIEvaluationMetricType2) => {
  VertexAIEvaluationMetricType2["BLEU"] = "BLEU";
  VertexAIEvaluationMetricType2["ROUGE"] = "ROUGE";
  VertexAIEvaluationMetricType2["FLUENCY"] = "FLEUNCY";
  VertexAIEvaluationMetricType2["SAFETY"] = "SAFETY";
  VertexAIEvaluationMetricType2["GROUNDEDNESS"] = "GROUNDEDNESS";
  VertexAIEvaluationMetricType2["SUMMARIZATION_QUALITY"] = "SUMMARIZATION_QUALITY";
  VertexAIEvaluationMetricType2["SUMMARIZATION_HELPFULNESS"] = "SUMMARIZATION_HELPFULNESS";
  VertexAIEvaluationMetricType2["SUMMARIZATION_VERBOSITY"] = "SUMMARIZATION_VERBOSITY";
  return VertexAIEvaluationMetricType2;
})(VertexAIEvaluationMetricType || {});
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  VertexAIEvaluationMetricType
});
//# sourceMappingURL=types.js.map