import * as grpc from '@grpc/grpc-js';
/**
 * Creates a unary service client constructor that, when instantiated, does not serialize/deserialize anything.
 * Allows for passing in {@link Buffer} directly, serialization can be handled via protobufjs or custom implementations.
 *
 * @param path service path
 * @param name service name
 */
export declare function createServiceClientConstructor(path: string, name: string): grpc.ServiceClientConstructor;
//# sourceMappingURL=create-service-client-constructor.d.ts.map