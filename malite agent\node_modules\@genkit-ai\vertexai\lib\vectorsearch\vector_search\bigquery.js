"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
var bigquery_exports = {};
__export(bigquery_exports, {
  getBigQueryDocumentIndexer: () => getBigQueryDocumentIndexer,
  getBigQueryDocumentRetriever: () => getBigQueryDocumentRetriever
});
module.exports = __toCommonJS(bigquery_exports);
var import_genkit = require("genkit");
var import_logging = require("genkit/logging");
var import_retriever = require("genkit/retriever");
const getBigQueryDocumentRetriever = (bq, tableId, datasetId) => {
  const bigQueryRetriever = async (neighbors) => {
    const ids = neighbors.map((neighbor) => neighbor.datapoint?.datapointId).filter(Boolean);
    const query = `
      SELECT * FROM \`${datasetId}.${tableId}\`
      WHERE id IN UNNEST(@ids)
    `;
    const options = {
      query,
      params: { ids }
    };
    let rows;
    try {
      [rows] = await bq.query(options);
    } catch (queryError) {
      import_logging.logger.error("Failed to execute BigQuery query:", queryError);
      return [];
    }
    const documents = [];
    for (const row of rows) {
      try {
        const docData = {
          content: JSON.parse(row.content)
        };
        if (row.metadata) {
          docData.metadata = JSON.parse(row.metadata);
        }
        const parsedDocData = import_retriever.DocumentDataSchema.parse(docData);
        documents.push(new import_retriever.Document(parsedDocData));
      } catch (error) {
        const id = row.id;
        const errorPrefix = `Failed to parse document data for document with ID ${id}:`;
        if (error instanceof import_genkit.z.ZodError || error instanceof Error) {
          import_logging.logger.warn(`${errorPrefix} ${error.message}`);
        } else {
          import_logging.logger.warn(errorPrefix);
        }
      }
    }
    return documents;
  };
  return bigQueryRetriever;
};
const getBigQueryDocumentIndexer = (bq, tableId, datasetId) => {
  const bigQueryIndexer = async (docs) => {
    const ids = [];
    const rows = docs.map((doc) => {
      const id = Math.random().toString(36).substring(7);
      ids.push(id);
      return {
        id,
        content: JSON.stringify(doc.content),
        metadata: JSON.stringify(doc.metadata)
      };
    });
    await bq.dataset(datasetId).table(tableId).insert(rows);
    return ids;
  };
  return bigQueryIndexer;
};
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  getBigQueryDocumentIndexer,
  getBigQueryDocumentRetriever
});
//# sourceMappingURL=bigquery.js.map