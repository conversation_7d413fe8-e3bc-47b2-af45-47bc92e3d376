{"version": 3, "file": "config.js", "sourceRoot": "", "sources": ["../../src/config.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,4CAA0C;AAC1C,8CAA+E;AAE/E,iEAA8D;AAC9D,+DAA4D;AAC5D,qEAAkE;AAClE,iFAA8E;AAE9E,MAAM,GAAG,GAAG,IAAA,aAAM,GAAE,CAAC;AACrB,MAAM,4BAA4B,GAAG,0BAAmB,CAAC,QAAQ,CAAC;AAClE,MAAM,aAAa,GAAG,CAAC,CAAC;AAExB;;;;;GAKG;AAEH,+EAA+E;AAC/E,+EAA+E;AAC/E,SAAgB,iBAAiB;IAC/B,OAAO;QACL,OAAO,EAAE,mBAAmB,CAAC,GAAG,CAAC;QACjC,uBAAuB,EAAE,KAAK;QAC9B,aAAa,EAAE;YACb,yBAAyB,EAAE,IAAA,aAAM,GAAE,CAAC,iCAAiC;YACrE,mBAAmB,EAAE,IAAA,aAAM,GAAE,CAAC,0BAA0B;SACzD;QACD,UAAU,EAAE;YACV,yBAAyB,EACvB,IAAA,aAAM,GAAE,CAAC,sCAAsC;YACjD,mBAAmB,EAAE,IAAA,aAAM,GAAE,CAAC,+BAA+B;YAC7D,cAAc,EAAE,IAAA,aAAM,GAAE,CAAC,0BAA0B;YACnD,eAAe,EAAE,IAAA,aAAM,GAAE,CAAC,2BAA2B;YACrD,2BAA2B,EACzB,IAAA,aAAM,GAAE,CAAC,yCAAyC;YACpD,0BAA0B,EACxB,IAAA,aAAM,GAAE,CAAC,wCAAwC;SACpD;KACF,CAAC;AACJ,CAAC;AApBD,8CAoBC;AAED;;;GAGG;AACH,SAAgB,mBAAmB,CACjC,cAAqC,IAAA,aAAM,GAAE;IAE7C,QAAQ,WAAW,CAAC,mBAAmB,EAAE;QACvC,KAAK,0BAAmB,CAAC,QAAQ;YAC/B,OAAO,IAAI,iCAAe,EAAE,CAAC;QAC/B,KAAK,0BAAmB,CAAC,SAAS;YAChC,OAAO,IAAI,mCAAgB,EAAE,CAAC;QAChC,KAAK,0BAAmB,CAAC,mBAAmB;YAC1C,OAAO,IAAI,uCAAkB,CAAC;gBAC5B,IAAI,EAAE,IAAI,iCAAe,EAAE;aAC5B,CAAC,CAAC;QACL,KAAK,0BAAmB,CAAC,oBAAoB;YAC3C,OAAO,IAAI,uCAAkB,CAAC;gBAC5B,IAAI,EAAE,IAAI,mCAAgB,EAAE;aAC7B,CAAC,CAAC;QACL,KAAK,0BAAmB,CAAC,YAAY;YACnC,OAAO,IAAI,mDAAwB,CACjC,4BAA4B,CAAC,WAAW,CAAC,CAC1C,CAAC;QACJ,KAAK,0BAAmB,CAAC,uBAAuB;YAC9C,OAAO,IAAI,uCAAkB,CAAC;gBAC5B,IAAI,EAAE,IAAI,mDAAwB,CAChC,4BAA4B,CAAC,WAAW,CAAC,CAC1C;aACF,CAAC,CAAC;QACL;YACE,UAAI,CAAC,KAAK,CACR,8BAA8B,WAAW,CAAC,mBAAmB,2BAA2B,4BAA4B,IAAI,CACzH,CAAC;YACF,OAAO,IAAI,iCAAe,EAAE,CAAC;KAChC;AACH,CAAC;AAhCD,kDAgCC;AAED,SAAS,4BAA4B,CACnC,WAAkC;IAElC,IACE,WAAW,CAAC,uBAAuB,KAAK,SAAS;QACjD,WAAW,CAAC,uBAAuB,KAAK,EAAE,EAC1C;QACA,UAAI,CAAC,KAAK,CACR,mDAAmD,aAAa,GAAG,CACpE,CAAC;QACF,OAAO,aAAa,CAAC;KACtB;IAED,MAAM,WAAW,GAAG,MAAM,CAAC,WAAW,CAAC,uBAAuB,CAAC,CAAC;IAEhE,IAAI,KAAK,CAAC,WAAW,CAAC,EAAE;QACtB,UAAI,CAAC,KAAK,CACR,2BAA2B,WAAW,CAAC,uBAAuB,gDAAgD,aAAa,GAAG,CAC/H,CAAC;QACF,OAAO,aAAa,CAAC;KACtB;IAED,IAAI,WAAW,GAAG,CAAC,IAAI,WAAW,GAAG,CAAC,EAAE;QACtC,UAAI,CAAC,KAAK,CACR,2BAA2B,WAAW,CAAC,uBAAuB,8DAA8D,aAAa,GAAG,CAC7I,CAAC;QACF,OAAO,aAAa,CAAC;KACtB;IAED,OAAO,WAAW,CAAC;AACrB,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { diag } from '@opentelemetry/api';\nimport { getEnv, TracesSamplerValues, ENVIRONMENT } from '@opentelemetry/core';\nimport { Sampler } from './Sampler';\nimport { AlwaysOffSampler } from './sampler/AlwaysOffSampler';\nimport { AlwaysOnSampler } from './sampler/AlwaysOnSampler';\nimport { ParentBasedSampler } from './sampler/ParentBasedSampler';\nimport { TraceIdRatioBasedSampler } from './sampler/TraceIdRatioBasedSampler';\n\nconst env = getEnv();\nconst FALLBACK_OTEL_TRACES_SAMPLER = TracesSamplerValues.AlwaysOn;\nconst DEFAULT_RATIO = 1;\n\n/**\n * Load default configuration. For fields with primitive values, any user-provided\n * value will override the corresponding default value. For fields with\n * non-primitive values (like `spanLimits`), the user-provided value will be\n * used to extend the default value.\n */\n\n// object needs to be wrapped in this function and called when needed otherwise\n// envs are parsed before tests are ran - causes tests using these envs to fail\nexport function loadDefaultConfig() {\n  return {\n    sampler: buildSamplerFromEnv(env),\n    forceFlushTimeoutMillis: 30000,\n    generalLimits: {\n      attributeValueLengthLimit: getEnv().OTEL_ATTRIBUTE_VALUE_LENGTH_LIMIT,\n      attributeCountLimit: getEnv().OTEL_ATTRIBUTE_COUNT_LIMIT,\n    },\n    spanLimits: {\n      attributeValueLengthLimit:\n        getEnv().OTEL_SPAN_ATTRIBUTE_VALUE_LENGTH_LIMIT,\n      attributeCountLimit: getEnv().OTEL_SPAN_ATTRIBUTE_COUNT_LIMIT,\n      linkCountLimit: getEnv().OTEL_SPAN_LINK_COUNT_LIMIT,\n      eventCountLimit: getEnv().OTEL_SPAN_EVENT_COUNT_LIMIT,\n      attributePerEventCountLimit:\n        getEnv().OTEL_SPAN_ATTRIBUTE_PER_EVENT_COUNT_LIMIT,\n      attributePerLinkCountLimit:\n        getEnv().OTEL_SPAN_ATTRIBUTE_PER_LINK_COUNT_LIMIT,\n    },\n  };\n}\n\n/**\n * Based on environment, builds a sampler, complies with specification.\n * @param environment optional, by default uses getEnv(), but allows passing a value to reuse parsed environment\n */\nexport function buildSamplerFromEnv(\n  environment: Required<ENVIRONMENT> = getEnv()\n): Sampler {\n  switch (environment.OTEL_TRACES_SAMPLER) {\n    case TracesSamplerValues.AlwaysOn:\n      return new AlwaysOnSampler();\n    case TracesSamplerValues.AlwaysOff:\n      return new AlwaysOffSampler();\n    case TracesSamplerValues.ParentBasedAlwaysOn:\n      return new ParentBasedSampler({\n        root: new AlwaysOnSampler(),\n      });\n    case TracesSamplerValues.ParentBasedAlwaysOff:\n      return new ParentBasedSampler({\n        root: new AlwaysOffSampler(),\n      });\n    case TracesSamplerValues.TraceIdRatio:\n      return new TraceIdRatioBasedSampler(\n        getSamplerProbabilityFromEnv(environment)\n      );\n    case TracesSamplerValues.ParentBasedTraceIdRatio:\n      return new ParentBasedSampler({\n        root: new TraceIdRatioBasedSampler(\n          getSamplerProbabilityFromEnv(environment)\n        ),\n      });\n    default:\n      diag.error(\n        `OTEL_TRACES_SAMPLER value \"${environment.OTEL_TRACES_SAMPLER} invalid, defaulting to ${FALLBACK_OTEL_TRACES_SAMPLER}\".`\n      );\n      return new AlwaysOnSampler();\n  }\n}\n\nfunction getSamplerProbabilityFromEnv(\n  environment: Required<ENVIRONMENT>\n): number | undefined {\n  if (\n    environment.OTEL_TRACES_SAMPLER_ARG === undefined ||\n    environment.OTEL_TRACES_SAMPLER_ARG === ''\n  ) {\n    diag.error(\n      `OTEL_TRACES_SAMPLER_ARG is blank, defaulting to ${DEFAULT_RATIO}.`\n    );\n    return DEFAULT_RATIO;\n  }\n\n  const probability = Number(environment.OTEL_TRACES_SAMPLER_ARG);\n\n  if (isNaN(probability)) {\n    diag.error(\n      `OTEL_TRACES_SAMPLER_ARG=${environment.OTEL_TRACES_SAMPLER_ARG} was given, but it is invalid, defaulting to ${DEFAULT_RATIO}.`\n    );\n    return DEFAULT_RATIO;\n  }\n\n  if (probability < 0 || probability > 1) {\n    diag.error(\n      `OTEL_TRACES_SAMPLER_ARG=${environment.OTEL_TRACES_SAMPLER_ARG} was given, but it is out of range ([0..1]), defaulting to ${DEFAULT_RATIO}.`\n    );\n    return DEFAULT_RATIO;\n  }\n\n  return probability;\n}\n"]}