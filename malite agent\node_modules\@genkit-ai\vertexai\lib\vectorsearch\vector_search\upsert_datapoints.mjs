async function upsertDatapoints(params) {
  const { datapoints, authClient, projectId, location, indexId } = params;
  const accessToken = await authClient.getAccessToken();
  const url = `https://${location}-aiplatform.googleapis.com/v1/projects/${projectId}/locations/${location}/indexes/${indexId}:upsertDatapoints`;
  const requestBody = {
    datapoints: datapoints.map((dp) => {
      const newDp = {
        datapoint_id: dp.datapointId,
        feature_vector: dp.featureVector
      };
      if (dp.restricts) {
        newDp.restricts = dp.restricts?.map((r) => ({
          namespace: r.namespace,
          allow_list: r.allowList,
          deny_list: r.denyList
        })) || [];
      }
      if (dp.numericRestricts) {
        newDp.numeric_restricts = dp.numericRestricts?.map((nr) => {
          const newNR = {
            namespace: nr.namespace
          };
          if (nr.valueInt) {
            newNR.value_int = nr.valueInt;
          }
          if (nr.valueFloat) {
            newNR.value_float = nr.valueFloat;
          }
          if (nr.valueDouble) {
            newNR.value_double = nr.valueDouble;
          }
          return newNR;
        }) || [];
      }
      if (dp.crowdingTag) {
        newDp.crowding_tag = dp.crowdingTag;
      }
      return newDp;
    })
  };
  const response = await fetch(url, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${accessToken}`
    },
    body: JSON.stringify(requestBody)
  });
  if (!response.ok) {
    const errMsg = (await response.json()).error?.message || "";
    throw new Error(
      `Error upserting datapoints into index ${indexId}: ${response.statusText}. ${errMsg}`
    );
  }
}
export {
  upsertDatapoints
};
//# sourceMappingURL=upsert_datapoints.mjs.map