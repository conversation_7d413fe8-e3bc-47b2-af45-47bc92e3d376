{"version": 3, "file": "TracerProviderWithEnvExporter.js", "sourceRoot": "", "sources": ["../../src/TracerProviderWithEnvExporter.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;;AAEH,4CAA0C;AAC1C,8CAAoE;AACpE,kEAOuC;AACvC,kEAGuC;AACvC,wFAAuG;AACvG,sFAAqG;AACrG,sFAAqG;AACrG,oEAAgE;AAEhE,MAAa,8BAA+B,SAAQ,mCAAkB;IA2DpE,YAAmB,SAA2B,EAAE;QAC9C,KAAK,CAAC,MAAM,CAAC,CAAC;QA3DR,yBAAoB,GAAmB,EAAE,CAAC;QAE1C,uBAAkB,GAAY,KAAK,CAAC;QA0D1C,IAAI,kBAAkB,GAAG,IAAI,CAAC,oBAAoB,CAChD,KAAK,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,IAAA,aAAM,GAAE,CAAC,oBAAoB,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAC9D,CAAC;QAEF,IAAI,kBAAkB,CAAC,CAAC,CAAC,KAAK,MAAM,EAAE;YACpC,UAAI,CAAC,IAAI,CACP,oEAAoE,CACrE,CAAC;SACH;aAAM,IAAI,kBAAkB,CAAC,MAAM,KAAK,CAAC,EAAE;YAC1C,UAAI,CAAC,IAAI,CAAC,6DAA6D,CAAC,CAAC;YAEzE,kBAAkB,GAAG,CAAC,MAAM,CAAC,CAAC;YAC9B,IAAI,CAAC,uBAAuB,CAAC,kBAAkB,CAAC,CAAC;YAEjD,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,uBAAuB,CACjD,IAAI,CAAC,oBAAoB,CAC1B,CAAC;YACF,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE;gBACvC,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC;YACnC,CAAC,CAAC,CAAC;SACJ;aAAM;YACL,IACE,kBAAkB,CAAC,MAAM,GAAG,CAAC;gBAC7B,kBAAkB,CAAC,QAAQ,CAAC,MAAM,CAAC,EACnC;gBACA,UAAI,CAAC,IAAI,CACP,+FAA+F,CAChG,CAAC;gBACF,kBAAkB,GAAG,CAAC,MAAM,CAAC,CAAC;aAC/B;YAED,IAAI,CAAC,uBAAuB,CAAC,kBAAkB,CAAC,CAAC;YAEjD,IAAI,IAAI,CAAC,oBAAoB,CAAC,MAAM,GAAG,CAAC,EAAE;gBACxC,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,uBAAuB,CACjD,IAAI,CAAC,oBAAoB,CAC1B,CAAC;gBACF,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE;oBACvC,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC;gBACnC,CAAC,CAAC,CAAC;aACJ;iBAAM;gBACL,UAAI,CAAC,IAAI,CACP,oFAAoF,CACrF,CAAC;aACH;SACF;IACH,CAAC;IAtGD,MAAM,CAAC,aAAa;QAClB,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;QAExC,QAAQ,QAAQ,EAAE;YAChB,KAAK,MAAM;gBACT,OAAO,IAAI,4CAAqB,EAAE,CAAC;YACrC,KAAK,WAAW;gBACd,OAAO,IAAI,4CAAqB,EAAE,CAAC;YACrC,KAAK,eAAe;gBAClB,OAAO,IAAI,6CAAsB,EAAE,CAAC;YACtC;gBACE,UAAI,CAAC,IAAI,CACP,qCAAqC,QAAQ,wBAAwB,CACtE,CAAC;gBACF,OAAO,IAAI,6CAAsB,EAAE,CAAC;SACvC;IACH,CAAC;IAED,MAAM,CAAC,eAAe;;QACpB,MAAM,eAAe,GAAG,IAAA,4BAAqB,GAAE,CAAC;QAEhD,OAAO,CACL,MAAA,MAAA,MAAA,eAAe,CAAC,kCAAkC,mCAClD,eAAe,CAAC,2BAA2B,mCAC3C,IAAA,aAAM,GAAE,CAAC,kCAAkC,mCAC3C,IAAA,aAAM,GAAE,CAAC,2BAA2B,CACrC,CAAC;IACJ,CAAC;IAEO,MAAM,CAAC,eAAe;QAC5B,gEAAgE;QAChE,8EAA8E;QAC9E,wDAAwD;QACxD,IAAI;YACF,8DAA8D;YAC9D,MAAM,EAAE,cAAc,EAAE,GAAG,OAAO,CAAC,gCAAgC,CAAC,CAAC;YACrE,OAAO,IAAI,cAAc,EAAE,CAAC;SAC7B;QAAC,OAAO,CAAC,EAAE;YACV,MAAM,IAAI,KAAK,CACb,oMAAoM,CAAC,EAAE,CACxM,CAAC;SACH;IACH,CAAC;IA8DQ,gBAAgB,CAAC,aAA4B;QACpD,KAAK,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC;QACtC,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;IACjC,CAAC;IAEQ,QAAQ,CAAC,MAA8B;QAC9C,IAAI,IAAI,CAAC,kBAAkB,EAAE;YAC3B,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;SACxB;IACH,CAAC;IAEO,uBAAuB,CAAC,YAAsB;QACpD,YAAY,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE;YAClC,MAAM,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC,CAAC;YACrD,IAAI,QAAQ,EAAE;gBACZ,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;aAC1C;iBAAM;gBACL,UAAI,CAAC,IAAI,CAAC,4CAA4C,YAAY,GAAG,CAAC,CAAC;aACxE;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,uBAAuB,CAC7B,SAAyB;QAEzB,OAAO,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE;YAC9B,IAAI,QAAQ,YAAY,oCAAmB,EAAE;gBAC3C,OAAO,IAAI,oCAAmB,CAAC,QAAQ,CAAC,CAAC;aAC1C;iBAAM;gBACL,OAAO,IAAI,mCAAkB,CAAC,QAAQ,CAAC,CAAC;aACzC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,oBAAoB,CAAC,IAAc;QACzC,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,MAAM,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC;IAC7E,CAAC;;AAjJH,wEAkJC;;AAjG2B,mDAAoB,GAAG,IAAI,GAAG,CAGtD;IACA,CAAC,MAAM,EAAE,GAAG,EAAE,CAAC,EAAI,CAAC,aAAa,EAAE,CAAC;IACpC,CAAC,QAAQ,EAAE,GAAG,EAAE,CAAC,IAAI,gCAAc,EAAE,CAAC;IACtC,CAAC,QAAQ,EAAE,GAAG,EAAE,CAAC,EAAI,CAAC,eAAe,EAAE,CAAC;IACxC,CAAC,SAAS,EAAE,GAAG,EAAE,CAAC,IAAI,oCAAmB,EAAE,CAAC;CAC7C,CAAE,CAAA", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { diag } from '@opentelemetry/api';\nimport { getEnv, getEnvWithoutDefaults } from '@opentelemetry/core';\nimport {\n  ConsoleSpanExporter,\n  SpanExporter,\n  BatchSpanProcessor,\n  SimpleSpanProcessor,\n  SDKRegistrationConfig,\n  SpanProcessor,\n} from '@opentelemetry/sdk-trace-base';\nimport {\n  NodeTracerConfig,\n  NodeTracerProvider,\n} from '@opentelemetry/sdk-trace-node';\nimport { OTLPTraceExporter as OTLPProtoTraceExporter } from '@opentelemetry/exporter-trace-otlp-proto';\nimport { OTLPTraceExporter as OTLPHttpTraceExporter } from '@opentelemetry/exporter-trace-otlp-http';\nimport { OTLPTraceExporter as OTLPGrpcTraceExporter } from '@opentelemetry/exporter-trace-otlp-grpc';\nimport { ZipkinExporter } from '@opentelemetry/exporter-zipkin';\n\nexport class TracerProviderWithEnvExporters extends NodeTracerProvider {\n  private _configuredExporters: SpanExporter[] = [];\n  private _spanProcessors: SpanProcessor[] | undefined;\n  private _hasSpanProcessors: boolean = false;\n\n  static configureOtlp(): SpanExporter {\n    const protocol = this.getOtlpProtocol();\n\n    switch (protocol) {\n      case 'grpc':\n        return new OTLPGrpcTraceExporter();\n      case 'http/json':\n        return new OTLPHttpTraceExporter();\n      case 'http/protobuf':\n        return new OTLPProtoTraceExporter();\n      default:\n        diag.warn(\n          `Unsupported OTLP traces protocol: ${protocol}. Using http/protobuf.`\n        );\n        return new OTLPProtoTraceExporter();\n    }\n  }\n\n  static getOtlpProtocol(): string {\n    const parsedEnvValues = getEnvWithoutDefaults();\n\n    return (\n      parsedEnvValues.OTEL_EXPORTER_OTLP_TRACES_PROTOCOL ??\n      parsedEnvValues.OTEL_EXPORTER_OTLP_PROTOCOL ??\n      getEnv().OTEL_EXPORTER_OTLP_TRACES_PROTOCOL ??\n      getEnv().OTEL_EXPORTER_OTLP_PROTOCOL\n    );\n  }\n\n  private static configureJaeger() {\n    // The JaegerExporter does not support being required in bundled\n    // environments. By delaying the require statement to here, we only crash when\n    // the exporter is actually used in such an environment.\n    try {\n      // eslint-disable-next-line @typescript-eslint/no-var-requires\n      const { JaegerExporter } = require('@opentelemetry/exporter-jaeger');\n      return new JaegerExporter();\n    } catch (e) {\n      throw new Error(\n        `Could not instantiate JaegerExporter. This could be due to the JaegerExporter's lack of support for bundling. If possible, use @opentelemetry/exporter-trace-otlp-proto instead. Original Error: ${e}`\n      );\n    }\n  }\n\n  protected static override _registeredExporters = new Map<\n    string,\n    () => SpanExporter\n  >([\n    ['otlp', () => this.configureOtlp()],\n    ['zipkin', () => new ZipkinExporter()],\n    ['jaeger', () => this.configureJaeger()],\n    ['console', () => new ConsoleSpanExporter()],\n  ]);\n\n  public constructor(config: NodeTracerConfig = {}) {\n    super(config);\n    let traceExportersList = this.filterBlanksAndNulls(\n      Array.from(new Set(getEnv().OTEL_TRACES_EXPORTER.split(',')))\n    );\n\n    if (traceExportersList[0] === 'none') {\n      diag.warn(\n        'OTEL_TRACES_EXPORTER contains \"none\". SDK will not be initialized.'\n      );\n    } else if (traceExportersList.length === 0) {\n      diag.warn('OTEL_TRACES_EXPORTER is empty. Using default otlp exporter.');\n\n      traceExportersList = ['otlp'];\n      this.createExportersFromList(traceExportersList);\n\n      this._spanProcessors = this.configureSpanProcessors(\n        this._configuredExporters\n      );\n      this._spanProcessors.forEach(processor => {\n        this.addSpanProcessor(processor);\n      });\n    } else {\n      if (\n        traceExportersList.length > 1 &&\n        traceExportersList.includes('none')\n      ) {\n        diag.warn(\n          'OTEL_TRACES_EXPORTER contains \"none\" along with other exporters. Using default otlp exporter.'\n        );\n        traceExportersList = ['otlp'];\n      }\n\n      this.createExportersFromList(traceExportersList);\n\n      if (this._configuredExporters.length > 0) {\n        this._spanProcessors = this.configureSpanProcessors(\n          this._configuredExporters\n        );\n        this._spanProcessors.forEach(processor => {\n          this.addSpanProcessor(processor);\n        });\n      } else {\n        diag.warn(\n          'Unable to set up trace exporter(s) due to invalid exporter and/or protocol values.'\n        );\n      }\n    }\n  }\n\n  override addSpanProcessor(spanProcessor: SpanProcessor) {\n    super.addSpanProcessor(spanProcessor);\n    this._hasSpanProcessors = true;\n  }\n\n  override register(config?: SDKRegistrationConfig) {\n    if (this._hasSpanProcessors) {\n      super.register(config);\n    }\n  }\n\n  private createExportersFromList(exporterList: string[]) {\n    exporterList.forEach(exporterName => {\n      const exporter = this._getSpanExporter(exporterName);\n      if (exporter) {\n        this._configuredExporters.push(exporter);\n      } else {\n        diag.warn(`Unrecognized OTEL_TRACES_EXPORTER value: ${exporterName}.`);\n      }\n    });\n  }\n\n  private configureSpanProcessors(\n    exporters: SpanExporter[]\n  ): (BatchSpanProcessor | SimpleSpanProcessor)[] {\n    return exporters.map(exporter => {\n      if (exporter instanceof ConsoleSpanExporter) {\n        return new SimpleSpanProcessor(exporter);\n      } else {\n        return new BatchSpanProcessor(exporter);\n      }\n    });\n  }\n\n  private filterBlanksAndNulls(list: string[]): string[] {\n    return list.map(item => item.trim()).filter(s => s !== 'null' && s !== '');\n  }\n}\n"]}