/*!
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
import { Service, GoogleAuthOptions } from '@google-cloud/common';
import * as common from '@google-cloud/common';
import { ResourceStream } from '@google-cloud/paginator';
import { PreciseDate } from '@google-cloud/precise-date';
import { Dataset, DatasetOptions } from './dataset';
import { Job, JobOptions, QueryResultsOptions } from './job';
import { Table, TableField, TableSchema, TableRow, JobCallback, JobResponse, RowMetadata } from './table';
import { RetryOptions } from '@google-cloud/common/build/src/util';
import bigquery from './types';
import { setLogFunction } from './logger';
export { common };
export interface RequestCallback<T> {
    (err: Error | null, response?: T | null): void;
}
export interface ResourceCallback<T, R> {
    (err: Error | null, resource?: T | null, response?: R | null): void;
}
export type PagedResponse<T, Q, R> = [T[]] | [T[], Q | null, R];
export interface PagedCallback<T, Q, R> {
    (err: Error | null, resource?: T[] | null, nextQuery?: Q | null, response?: R | null): void;
}
export type JobRequest<J> = J & {
    jobId?: string;
    jobPrefix?: string;
    location?: string;
    projectId?: string;
};
export type PagedRequest<P> = P & {
    autoPaginate?: boolean;
    maxApiCalls?: number;
};
export type QueryResultsResponse = bigquery.IGetQueryResultsResponse | bigquery.IQueryResponse;
export type QueryRowsResponse = PagedResponse<RowMetadata, Query, QueryResultsResponse>;
export type QueryRowsCallback = PagedCallback<RowMetadata, Query, QueryResultsResponse>;
export type SimpleQueryRowsResponse = [RowMetadata[], bigquery.IJob];
export type SimpleQueryRowsCallback = ResourceCallback<RowMetadata[], bigquery.IJob>;
export type Query = JobRequest<bigquery.IJobConfigurationQuery> & {
    destination?: Table;
    params?: any[] | {
        [param: string]: any;
    };
    dryRun?: boolean;
    labels?: {
        [label: string]: string;
    };
    types?: QueryParamTypes;
    job?: Job;
    maxResults?: number;
    jobTimeoutMs?: number;
    pageToken?: string;
    wrapIntegers?: boolean | IntegerTypeCastOptions;
    parseJSON?: boolean;
};
export type QueryParamTypeStruct = {
    [type: string]: string | string[] | QueryParamTypeStruct | QueryParamTypeStruct[];
};
export type QueryParamTypes = string[] | string[][] | QueryParamTypeStruct | QueryParamTypeStruct[];
export type QueryOptions = QueryResultsOptions;
export type QueryStreamOptions = {
    wrapIntegers?: boolean | IntegerTypeCastOptions;
    parseJSON?: boolean;
};
export type DatasetResource = bigquery.IDataset & {
    projectId?: string;
};
export type ValueType = bigquery.IQueryParameterType;
export type GetDatasetsOptions = PagedRequest<bigquery.datasets.IListParams> & {
    projectId?: string;
};
export type DatasetsResponse = PagedResponse<Dataset, GetDatasetsOptions, bigquery.IDatasetList>;
export type DatasetsCallback = PagedCallback<Dataset, GetDatasetsOptions, bigquery.IDatasetList>;
export type DatasetResponse = [Dataset, bigquery.IDataset];
export type DatasetCallback = ResourceCallback<Dataset, bigquery.IDataset>;
export type GetJobsOptions = PagedRequest<bigquery.jobs.IListParams>;
export type GetJobsResponse = PagedResponse<Job, GetJobsOptions, bigquery.IJobList>;
export type GetJobsCallback = PagedCallback<Job, GetJobsOptions, bigquery.IJobList>;
export type JobsQueryResponse = [Job, bigquery.IQueryResponse];
export type JobsQueryCallback = ResourceCallback<Job, bigquery.IQueryResponse>;
export interface BigQueryTimeOptions {
    hours?: number | string;
    minutes?: number | string;
    seconds?: number | string;
    fractional?: number | string;
}
export interface BigQueryDateOptions {
    year?: number | string;
    month?: number | string;
    day?: number | string;
}
export interface BigQueryDatetimeOptions {
    year?: string | number;
    month?: string | number;
    day?: string | number;
    hours?: string | number;
    minutes?: string | number;
    seconds?: string | number;
    fractional?: string | number;
}
export interface BigQueryRangeOptions {
    start?: BigQueryDate | BigQueryDatetime | BigQueryTimestamp | string;
    end?: BigQueryDate | BigQueryDatetime | BigQueryTimestamp | string;
}
export type ProvidedTypeArray = Array<ProvidedTypeStruct | string | []>;
export interface ProvidedTypeStruct {
    [key: string]: string | ProvidedTypeArray | ProvidedTypeStruct;
}
export type QueryParameter = bigquery.IQueryParameter;
export type ParameterMode = bigquery.IJobConfigurationQuery['parameterMode'];
export interface BigQueryOptions extends GoogleAuthOptions {
    /**
     * Automatically retry requests if the
     * response is related to rate limits or certain intermittent server errors.
     * We will exponentially backoff subsequent requests by default.
     *
     * Defaults to `true`.
     *
     * @deprecated Use retryOptions.
     */
    autoRetry?: boolean;
    /**
     * Maximum number of automatic retries
     * attempted before returning the error.
     *
     * Defaults to 3.
     *
     * @deprecated Use retryOptions.
     */
    maxRetries?: number;
    /**
     * Customize retry configuration for all requests in the SDK.
     * By default, a request is retried if the response is related to rate limits
     * or certain intermittent server errors.
     * We will exponentially backoff subsequent requests by default.
     *
     * More on the default retry predicate on the `shouldRetryRequest` method:
     * https://github.com/googleapis/nodejs-common/blob/main/src/util.ts
     *
     * Defaults:
     * - retryOptions.autoRetry: true
     * - retryOptions.maxRetries: 3
     */
    retryOptions?: RetryOptions;
    /**
     * The geographic location of all datasets and
     * jobs referenced and created through the client.
     */
    location?: string;
    /**
     * The value to be prepended to the User-Agent
     * header in API requests.
     */
    userAgent?: string;
    /**
     * The API endpoint of the service used to make requests.
     * Defaults to `bigquery.googleapis.com`.
     */
    apiEndpoint?: string;
    /**
     * The Trusted Cloud Domain (TPC) DNS of the service used to make requests.
     * Defaults to `googleapis.com`.
     */
    universeDomain?: string;
}
export interface IntegerTypeCastOptions {
    integerTypeCastFunction: Function;
    fields?: string | string[];
}
export type IntegerTypeCastValue = {
    integerValue: string | number;
    schemaFieldName?: string;
};
export declare const PROTOCOL_REGEX: RegExp;
/**
 * @typedef {object} BigQueryOptions
 * @property {string} [projectId] The project ID from the Google Developer's
 *     Console, e.g. 'grape-spaceship-123'. We will also check the environment
 *     variable `GCLOUD_PROJECT` for your project ID. If your app is running in
 *     an environment which supports {@link
 * https://cloud.google.com/docs/authentication/production#providing_credentials_to_your_application
 * Application Default Credentials}, your project ID will be detected
 * automatically.
 * @property {string} [keyFilename] Full path to the a .json, .pem, or .p12 key
 *     downloaded from the Google Developers Console. If you provide a path to a
 *     JSON file, the `projectId` option above is not necessary. NOTE: .pem and
 *     .p12 require you to specify the `email` option as well.
 * @property {string} [token] An OAUTH access token. If provided, we will not
 *     manage fetching, re-using, and re-minting access tokens.
 * @property {string} [email] Account email address. Required when using a .pem
 *     or .p12 keyFilename.
 * @property {object} [credentials] Credentials object.
 * @property {string} [credentials.client_email]
 * @property {string} [credentials.private_key]
 * @property {Constructor} [promise] Custom promise module to use instead of
 *     native Promises.
 * @property {string[]} [scopes] Additional OAuth scopes to use in requests. For
 *     example, to access an external data source, you may need the
 *     `https://www.googleapis.com/auth/drive.readonly` scope.
 */
/**
 * In the following examples from this page and the other modules (`Dataset`,
 * `Table`, etc.), we are going to be using a dataset from
 * {@link http://goo.gl/f2SXcb| data.gov} of higher education institutions.
 *
 * We will create a table with the correct schema, import the public CSV file
 * into that table, and query it for data.
 *
 * This client supports enabling query-related preview features via environmental
 * variables.  By setting the environment variable QUERY_PREVIEW_ENABLED to the string
 * "TRUE", the client will enable preview features, though behavior may still be
 * controlled via the bigquery service as well.  Currently, the feature(s) in scope
 * include: stateless queries (query execution without corresponding job metadata).
 *
 * @class
 *
 * See {@link https://cloud.google.com/bigquery/what-is-bigquery| What is BigQuery?}
 *
 * @param {BigQueryOptions} options Constructor options.
 *
 * @example Install the client library with <a href="https://www.npmjs.com/">npm</a>:
 * ```
 * npm install @google-cloud/bigquery
 *
 * ```
 * @example Import the client library
 * ```
 * const {BigQuery} = require('@google-cloud/bigquery');
 *
 * ```
 * @example Create a client that uses <a href="https://cloud.google.com/docs/authentication/production#providing_credentials_to_your_application">Application Default Credentials (ADC)</a>:
 * ```
 * const bigquery = new BigQuery();
 *
 * ```
 * @example Create a client with <a href="https://cloud.google.com/docs/authentication/production#obtaining_and_providing_service_account_credentials_manually">explicit credentials</a>:
 * ```
 * const bigquery = new BigQuery({
 *   projectId: 'your-project-id',
 *   keyFilename: '/path/to/keyfile.json'
 * });
 *
 * ```
 * @example <caption>include:samples/quickstart.js</caption>
 * region_tag:bigquery_quickstart
 * Full quickstart example:
 */
export declare class BigQuery extends Service {
    location?: string;
    private _universeDomain;
    private _enableQueryPreview;
    createQueryStream(options?: Query | string): ResourceStream<RowMetadata>;
    getDatasetsStream(options?: GetDatasetsOptions): ResourceStream<Dataset>;
    getJobsStream(options?: GetJobsOptions): ResourceStream<Job>;
    constructor(options?: BigQueryOptions);
    private trace_;
    get universeDomain(): string;
    private static sanitizeEndpoint;
    private static sanitizeDomain;
    /**
     * Merge a rowset returned from the API with a table schema.
     *
     * @private
     *
     * @param {object} schema
     * @param {array} rows
     * @param {object} options
     * @param {boolean|IntegerTypeCastOptions} options.wrapIntegers Wrap values of
     *     'INT64' type in {@link BigQueryInt} objects.
     *     If a `boolean`, this will wrap values in {@link BigQueryInt} objects.
     *     If an `object`, this will return a value returned by
     *     `wrapIntegers.integerTypeCastFunction`.
     *     Please see {@link IntegerTypeCastOptions} for options descriptions.
     * @param {array} options.selectedFields List of fields to return.
     * If unspecified, all fields are returned.
     * @param {array} options.parseJSON parse a 'JSON' field into a JSON object.
     * @returns Fields using their matching names from the table's schema.
     */
    static mergeSchemaWithRows_(schema: TableSchema | TableField | undefined, rows: TableRow[], options: {
        wrapIntegers: boolean | IntegerTypeCastOptions;
        selectedFields?: string[];
        parseJSON?: boolean;
    }): any[];
    /**
     * The `DATE` type represents a logical calendar date, independent of time
     * zone. It does not represent a specific 24-hour time period. Rather, a given
     * DATE value represents a different 24-hour period when interpreted in
     * different time zones, and may represent a shorter or longer day during
     * Daylight Savings Time transitions.
     *
     * @param {object|string} value The date. If a string, this should be in the
     *     format the API describes: `YYYY-[M]M-[D]D`.
     *     Otherwise, provide an object.
     * @param {string|number} value.year Four digits.
     * @param {string|number} value.month One or two digits.
     * @param {string|number} value.day One or two digits.
     *
     * @example
     * ```
     * const {BigQuery} = require('@google-cloud/bigquery');
     * const bigquery = new BigQuery();
     * const date = bigquery.date('2017-01-01');
     *
     * //-
     * // Alternatively, provide an object.
     * //-
     * const date2 = bigquery.date({
     *   year: 2017,
     *   month: 1,
     *   day: 1
     * });
     * ```
     */
    static date(value: BigQueryDateOptions | string): BigQueryDate;
    /**
     * @param {object|string} value The date. If a string, this should be in the
     *     format the API describes: `YYYY-[M]M-[D]D`.
     *     Otherwise, provide an object.
     * @param {string|number} value.year Four digits.
     * @param {string|number} value.month One or two digits.
     * @param {string|number} value.day One or two digits.
     *
     * @example
     * ```
     * const {BigQuery} = require('@google-cloud/bigquery');
     * const date = BigQuery.date('2017-01-01');
     *
     * //-
     * // Alternatively, provide an object.
     * //-
     * const date2 = BigQuery.date({
     *   year: 2017,
     *   month: 1,
     *   day: 1
     * });
     * ```
     */
    date(value: BigQueryDateOptions | string): BigQueryDate;
    /**
     * A `DATETIME` data type represents a point in time. Unlike a `TIMESTAMP`,
     * this does not refer to an absolute instance in time. Instead, it is the
     * civil time, or the time that a user would see on a watch or calendar.
     *
     * @method BigQuery.datetime
     * @param {object|string} value The time. If a string, this should be in the
     *     format the API describes: `YYYY-[M]M-[D]D[ [H]H:[M]M:[S]S[.DDDDDD]]`.
     *     Otherwise, provide an object.
     * @param {string|number} value.year Four digits.
     * @param {string|number} value.month One or two digits.
     * @param {string|number} value.day One or two digits.
     * @param {string|number} [value.hours] One or two digits (`00` - `23`).
     * @param {string|number} [value.minutes] One or two digits (`00` - `59`).
     * @param {string|number} [value.seconds] One or two digits (`00` - `59`).
     * @param {string|number} [value.fractional] Up to six digits for microsecond
     *     precision.
     *
     * @example
     * ```
     * const {BigQuery} = require('@google-cloud/bigquery');
     * const datetime = BigQuery.datetime('2017-01-01 13:00:00');
     *
     * //-
     * // Alternatively, provide an object.
     * //-
     * const datetime = BigQuery.datetime({
     *   year: 2017,
     *   month: 1,
     *   day: 1,
     *   hours: 14,
     *   minutes: 0,
     *   seconds: 0
     * });
     * ```
     */
    /**
     * A `DATETIME` data type represents a point in time. Unlike a `TIMESTAMP`,
     * this does not refer to an absolute instance in time. Instead, it is the
     * civil time, or the time that a user would see on a watch or calendar.
     *
     * @param {object|string} value The time. If a string, this should be in the
     *     format the API describes: `YYYY-[M]M-[D]D[ [H]H:[M]M:[S]S[.DDDDDD]]`.
     *     Otherwise, provide an object.
     * @param {string|number} value.year Four digits.
     * @param {string|number} value.month One or two digits.
     * @param {string|number} value.day One or two digits.
     * @param {string|number} [value.hours] One or two digits (`00` - `23`).
     * @param {string|number} [value.minutes] One or two digits (`00` - `59`).
     * @param {string|number} [value.seconds] One or two digits (`00` - `59`).
     * @param {string|number} [value.fractional] Up to six digits for microsecond
     *     precision.
     *
     * @example
     * ```
     * const {BigQuery} = require('@google-cloud/bigquery');
     * const bigquery = new BigQuery();
     * const datetime = bigquery.datetime('2017-01-01 13:00:00');
     *
     * //-
     * // Alternatively, provide an object.
     * //-
     * const datetime = bigquery.datetime({
     *   year: 2017,
     *   month: 1,
     *   day: 1,
     *   hours: 14,
     *   minutes: 0,
     *   seconds: 0
     * });
     * ```
     */
    static datetime(value: BigQueryDatetimeOptions | string): BigQueryDatetime;
    datetime(value: BigQueryDatetimeOptions | string): BigQueryDatetime;
    /**
     * A `TIME` data type represents a time, independent of a specific date.
     *
     * @method BigQuery.time
     * @param {object|string} value The time. If a string, this should be in the
     *     format the API describes: `[H]H:[M]M:[S]S[.DDDDDD]`. Otherwise, provide
     *     an object.
     * @param {string|number} [value.hours] One or two digits (`00` - `23`).
     * @param {string|number} [value.minutes] One or two digits (`00` - `59`).
     * @param {string|number} [value.seconds] One or two digits (`00` - `59`).
     * @param {string|number} [value.fractional] Up to six digits for microsecond
     *     precision.
     *
     * @example
     * ```
     * const {BigQuery} = require('@google-cloud/bigquery');
     * const time = BigQuery.time('14:00:00'); // 2:00 PM
     *
     * //-
     * // Alternatively, provide an object.
     * //-
     * const time = BigQuery.time({
     *   hours: 14,
     *   minutes: 0,
     *   seconds: 0
     * });
     * ```
     */
    /**
     * A `TIME` data type represents a time, independent of a specific date.
     *
     * @param {object|string} value The time. If a string, this should be in the
     *     format the API describes: `[H]H:[M]M:[S]S[.DDDDDD]`. Otherwise, provide
     *     an object.
     * @param {string|number} [value.hours] One or two digits (`00` - `23`).
     * @param {string|number} [value.minutes] One or two digits (`00` - `59`).
     * @param {string|number} [value.seconds] One or two digits (`00` - `59`).
     * @param {string|number} [value.fractional] Up to six digits for microsecond
     *     precision.
     *
     * @example
     * ```
     * const {BigQuery} = require('@google-cloud/bigquery');
     * const bigquery = new BigQuery();
     * const time = bigquery.time('14:00:00'); // 2:00 PM
     *
     * //-
     * // Alternatively, provide an object.
     * //-
     * const time = bigquery.time({
     *   hours: 14,
     *   minutes: 0,
     *   seconds: 0
     * });
     * ```
     */
    static time(value: BigQueryTimeOptions | string): BigQueryTime;
    time(value: BigQueryTimeOptions | string): BigQueryTime;
    /**
     * A timestamp represents an absolute point in time, independent of any time
     * zone or convention such as Daylight Savings Time.
     *
     * The recommended input here is a `Date` or `PreciseDate` class.
     * If passing as a `string`, it should be Timestamp literals: https://cloud.google.com/bigquery/docs/reference/standard-sql/lexical#timestamp_literals.
     * When passing a `number` input, it should be epoch seconds in float representation.
     *
     * @method BigQuery.timestamp
     * @param {Date|string} value The time.
     *
     * @example
     * ```
     * const {BigQuery} = require('@google-cloud/bigquery');
     * const timestamp = BigQuery.timestamp(new Date());
     * ```
     */
    static timestamp(value: Date | PreciseDate | string | number): BigQueryTimestamp;
    /**
     * A timestamp represents an absolute point in time, independent of any time
     * zone or convention such as Daylight Savings Time.
     *
     * The recommended input here is a `Date` or `PreciseDate` class.
     * If passing as a `string`, it should be Timestamp literals: https://cloud.google.com/bigquery/docs/reference/standard-sql/lexical#timestamp_literals.
     * When passing a `number` input, it should be epoch seconds in float representation.
     *
     * @param {Date|string|string|number} value The time.
     *
     * @example
     * ```
     * const {BigQuery} = require('@google-cloud/bigquery');
     * const bigquery = new BigQuery();
     * const timestamp = bigquery.timestamp(new Date());
     * ```
     */
    timestamp(value: Date | PreciseDate | string | number): BigQueryTimestamp;
    /**
     * A range represents contiguous range between two dates, datetimes, or timestamps.
     * The lower and upper bound for the range are optional.
     * The lower bound is inclusive and the upper bound is exclusive.
     *
     * @method BigQuery.range
     * @param {string|BigQueryRangeOptions} value The range API string or start/end with dates/datetimes/timestamp ranges.
     * @param {string} elementType The range element type - DATE|DATETIME|TIMESTAMP
     *
     * @example
     * ```
     * const {BigQuery} = require('@google-cloud/bigquery');
     * const timestampRange = BigQuery.range('[2020-10-01 12:00:00+08, 2020-12-31 12:00:00+08)', 'TIMESTAMP');
     * ```
     */
    static range(value: string | BigQueryRangeOptions, elementType?: string): BigQueryRange;
    /**
     * A range represents contiguous range between two dates, datetimes, or timestamps.
     * The lower and upper bound for the range are optional.
     * The lower bound is inclusive and the upper bound is exclusive.
     *
     * @param {string|BigQueryRangeOptions} value The range API string or start/end with dates/datetimes/timestamp ranges.
     * @param {string} elementType The range element type - DATE|DATETIME|TIMESTAMP
     *
     * @example
     * ```
     * const {BigQuery} = require('@google-cloud/bigquery');
     * const bigquery = new BigQuery();
     * const timestampRange = bigquery.range('[2020-10-01 12:00:00+08, 2020-12-31 12:00:00+08)', 'TIMESTAMP');
     * ```
     */
    range(value: string, elementType?: string): BigQueryRange;
    /**
     * A BigQueryInt wraps 'INT64' values. Can be used to maintain precision.
     *
     * @param {string|number|IntegerTypeCastValue} value The INT64 value to convert.
     * @param {IntegerTypeCastOptions} typeCastOptions Configuration to convert
     *     value. Must provide an `integerTypeCastFunction` to handle conversion.
     * @returns {BigQueryInt}
     *
     * @example
     * ```
     * const {BigQuery} = require('@google-cloud/bigquery');
     * const bigquery = new BigQuery();
     *
     * const largeIntegerValue = Number.MAX_SAFE_INTEGER + 1;
     *
     * const options = {
     *   integerTypeCastFunction: value => value.split(),
     * };
     *
     * const bqInteger = bigquery.int(largeIntegerValue, options);
     *
     * const customValue = bqInteger.valueOf();
     * // customValue is the value returned from your `integerTypeCastFunction`.
     * ```
     */
    static int(value: string | number | IntegerTypeCastValue, typeCastOptions?: IntegerTypeCastOptions): BigQueryInt;
    int(value: string | number | IntegerTypeCastValue, typeCastOptions?: IntegerTypeCastOptions): BigQueryInt;
    /**
     * A geography value represents a surface area on the Earth
     * in Well-known Text (WKT) format.
     *
     * @param {string} value The geospatial data.
     *
     * @example
     * ```
     * const {BigQuery} = require('@google-cloud/bigquery');
     * const bigquery = new BigQuery();
     * const geography = bigquery.geography('POINT(1, 2)');
     * ```
     */
    static geography(value: string): Geography;
    geography(value: string): Geography;
    /**
     * Convert an INT64 value to Number.
     *
     * @private
     * @param {object} value The INT64 value to convert.
     */
    static decodeIntegerValue_(value: IntegerTypeCastValue): number;
    /**
     * Return a value's provided type.
     *
     * @private
     *
     * @throws {error} If the type provided is invalid.
     *
     * See {@link https://cloud.google.com/bigquery/data-types| Data Type}
     *
     * @param {*} providedType The type.
     * @returns {string} The valid type provided.
     */
    static getTypeDescriptorFromProvidedType_(providedType: string | ProvidedTypeStruct | ProvidedTypeArray): ValueType;
    /**
     * Detect a value's type.
     *
     * @private
     *
     * @throws {error} If the type could not be detected.
     *
     * See {@link https://cloud.google.com/bigquery/data-types| Data Type}
     *
     * @param {*} value The value.
     * @returns {string} The type detected from the value.
     */
    static getTypeDescriptorFromValue_(value: unknown): ValueType;
    /**
     * Convert a value into a `queryParameter` object.
     *
     * @private
     *
     * See {@link https://cloud.google.com/bigquery/docs/reference/rest/v2/jobs/query#request-body| Jobs.query API Reference Docs (see `queryParameters`)}
     *
     * @param {*} value The value.
     * @param {string|ProvidedTypeStruct|ProvidedTypeArray} providedType Provided
     *     query parameter type.
     * @returns {object} A properly-formed `queryParameter` object.
     */
    static valueToQueryParameter_(value: any, providedType?: string | ProvidedTypeStruct | ProvidedTypeArray): bigquery.IQueryParameter;
    private static _getValue;
    private static _isCustomType;
    /**
     * @callback DatasetCallback
     * @param {?Error} err Request error, if any.
     * @param {object} dataset The [dataset resource]{@link https://cloud.google.com/bigquery/docs/reference/rest/v2/datasets#resource}.
     * @param {object} apiResponse The full API response.
     */
    /**
     * Create a dataset.
     *
     * See {@link https://cloud.google.com/bigquery/docs/reference/v2/datasets/insert| Datasets: insert API Documentation}
     *
     * @param {string} id ID of the dataset to create.
     * @param {object} [options] See a
     *     {@link https://cloud.google.com/bigquery/docs/reference/v2/datasets#resource| Dataset resource}.
     * @param {DatasetCallback} [callback] The callback function.
     * @param {?error} callback.err An error returned while making this request
     * @param {Dataset} callback.dataset The newly created dataset
     * @param {object} callback.apiResponse The full API response.
     *
     * @example
     * ```
     * const {BigQuery} = require('@google-cloud/bigquery');
     * const bigquery = new BigQuery();
     *
     * bigquery.createDataset('my-dataset', function(err, dataset, apiResponse)
     * {});
     *
     * //-
     * // If the callback is omitted, we'll return a Promise.
     * //-
     * bigquery.createDataset('my-dataset').then(function(data) {
     *   const dataset = data[0];
     *   const apiResponse = data[1];
     * });
     * ```
     */
    createDataset(id: string, options?: DatasetResource): Promise<DatasetResponse>;
    createDataset(id: string, options: DatasetResource, callback: DatasetCallback): void;
    createDataset(id: string, callback: DatasetCallback): void;
    /**
     * @callback JobCallback
     * @param {?Error} err Request error, if any.
     * @param {object} job The newly created job for your query.
     * @param {object} apiResponse The full API response.
     */
    /**
     * Run a query as a job. No results are immediately returned. Instead, your
     * callback will be executed with a {@link Job} object that you must
     * ping for the results. See the Job documentation for explanations of how to
     * check on the status of the job.
     *
     * See {@link https://cloud.google.com/bigquery/docs/reference/v2/jobs/insert| Jobs: insert API Documentation}
     *
     * @param {object|string} options The configuration object. This must be in
     * the format of the {@link https://cloud.google.com/bigquery/docs/reference/rest/v2/Job#JobConfigurationQuery| `configuration.query`}
     * property of a Jobs resource. If a string is provided, this is used as the
     * query string, and all other options are defaulted.
     * @param {Table} [options.destination] The table to save the
     *     query's results to. If omitted, a new table will be created.
     * @param {boolean} [options.dryRun] If set, don't actually run this job. A
     *     valid query will update the job with processing statistics. These can
     * be accessed via `job.metadata`.
     * @param {object} [options.labels] String key/value pairs to be attached as
     *     labels to the newly created Job.
     * @param {string} [options.location] The geographic location of the job.
     *     Required except for US and EU.
     * @param {number} [options.jobTimeoutMs] Job timeout in milliseconds.
     *     If this time limit is exceeded, BigQuery might attempt to stop the job.
     * @param {string} [options.jobId] Custom job id.
     * @param {string} [options.jobPrefix] Prefix to apply to the job id.
     * @param {string} options.query A query string, following the BigQuery query
     *     syntax, of the query to execute.
     * @param {boolean} [options.useLegacySql=false] Option to use legacy sql syntax.
     * @param {object} [options.defaultDataset] The dataset. This must be in
     *     the format of the {@link https://cloud.google.com/bigquery/docs/reference/rest/v2/datasets#DatasetReference| `DatasetReference`}
     * @param {boolean} [options.wrapIntegers] Optionally wrap INT64 in BigQueryInt
     *     or custom INT64 value type.
     * @param {boolean} [options.parseJSON] Optionally parse JSON as a JSON Object.
     * @param {object|array} [options.params] Option to provide query prarameters.
     * @param {JobCallback} [callback] The callback function.
     * @param {?error} callback.err An error returned while making this request.
     * @param {Job} callback.job The newly created job for your query.
     * @param {object} callback.apiResponse The full API response.
     *
     * @throws {Error} If a query is not specified.
     * @throws {Error} If a Table is not provided as a destination.
     *
     * @example
     * ```
     * const {BigQuery} = require('@google-cloud/bigquery');
     * const bigquery = new BigQuery();
     *
     * const query = 'SELECT url FROM `publicdata.samples.github_nested` LIMIT
     * 100';
     *
     * //-
     * // You may pass only a query string, having a new table created to store
     * the
     * // results of the query.
     * //-
     * bigquery.createQueryJob(query, function(err, job) {});
     *
     * //-
     * // You can also control the destination table by providing a
     * // {@link Table} object.
     * //-
     * bigquery.createQueryJob({
     *   destination: bigquery.dataset('higher_education').table('institutions'),
     *   query: query
     * }, function(err, job) {});
     *
     * //-
     * // After you have run `createQueryJob`, your query will execute in a job.
     * Your
     * // callback is executed with a {@link Job} object so that you may
     * // check for the results.
     * //-
     * bigquery.createQueryJob(query, function(err, job) {
     *   if (!err) {
     *     job.getQueryResults(function(err, rows, apiResponse) {});
     *   }
     * });
     *
     * //-
     * // If the callback is omitted, we'll return a Promise.
     * //-
     * bigquery.createQueryJob(query).then(function(data) {
     *   const job = data[0];
     *   const apiResponse = data[1];
     *
     *   return job.getQueryResults();
     * });
     * ```
     */
    createQueryJob(options: Query | string): Promise<JobResponse>;
    createQueryJob(options: Query | string, callback: JobCallback): void;
    private buildQueryParams_;
    /**
     * Creates a job. Typically when creating a job you'll have a very specific
     * task in mind. For this we recommend one of the following methods:
     *
     * - {@link BigQuery.createQueryJob}
     * - {@link Table#createCopyJob}
     * - {@link Table#createCopyFromJob}
     * - {@link Table#createExtractJob}
     * - {@link Table#createLoadJob}
     *
     * However in the event you need a finer level of control over the job
     * creation, you can use this method to pass in a raw {@link https://cloud.google.com/bigquery/docs/reference/rest/v2/Job| Job resource}
     * object.
     *
     * See {@link https://cloud.google.com/bigquery/docs/reference/rest/v2/jobs| Jobs Overview}
     * See {@link https://cloud.google.com/bigquery/docs/reference/v2/jobs/insert| Jobs: insert API Documentation}
     *
     * @param {object} options Object in the form of a {@link https://cloud.google.com/bigquery/docs/reference/rest/v2/Job| Job resource};
     * @param {string} [options.jobId] Custom job id.
     * @param {string} [options.jobPrefix] Prefix to apply to the job id.
     * @param {string} [options.location] The geographic location of the job.
     *     Required except for US and EU.
     * @param {JobCallback} [callback] The callback function.
     * @param {?error} callback.err An error returned while making this request.
     * @param {Job} callback.job The newly created job.
     * @param {object} callback.apiResponse The full API response.
     *
     * @example
     * ```
     * const {BigQuery} = require('@google-cloud/bigquery');
     * const bigquery = new BigQuery();
     *
     * const options = {
     *   configuration: {
     *     query: {
     *       query: 'SELECT url FROM `publicdata.samples.github_nested` LIMIT 100'
     *     }
     *   }
     * };
     *
     * bigquery.createJob(options, function(err, job) {
     *   if (err) {
     *     // Error handling omitted.
     *   }
     *
     *   job.getQueryResults(function(err, rows) {});
     * });
     *
     * //-
     * // If the callback is omitted, we'll return a Promise.
     * //-
     * bigquery.createJob(options).then(function(data) {
     *   const job = data[0];
     *
     *   return job.getQueryResults();
     * });
     * ```
     */
    createJob(options: JobOptions): Promise<JobResponse>;
    createJob(options: JobOptions, callback: JobCallback): void;
    /**
     * Create a reference to a dataset.
     *
     * @param {string} id ID of the dataset.
     * @param {object} [options] Dataset options.
     * @param {string} [options.projectId] The GCP project ID.
     * @param {string} [options.location] The geographic location of the dataset.
     *      Required except for US and EU.
     *
     * @example
     * ```
     * const {BigQuery} = require('@google-cloud/bigquery');
     * const bigquery = new BigQuery();
     * const dataset = bigquery.dataset('higher_education');
     * ```
     */
    dataset(id: string, options?: DatasetOptions): Dataset;
    /**
     * List all or some of the datasets in a project.
     *
     * See {@link https://cloud.google.com/bigquery/docs/reference/v2/datasets/list| Datasets: list API Documentation}
     *
     * @param {object} [options] Configuration object.
     * @param {boolean} [options.all] List all datasets, including hidden ones.
     * @param {string} [options.projectId] The GCP project ID.
     * @param {boolean} [options.autoPaginate] Have pagination handled automatically.
     *     Default: true.
     * @param {number} [options.maxApiCalls] Maximum number of API calls to make.
     * @param {number} [options.maxResults] Maximum number of results to return.
     * @param {string} [options.pageToken] Token returned from a previous call, to
     *     request the next page of results.
     * @param {DatasetsCallback} [callback] The callback function.
     * @param {?error} callback.err An error returned while making this request
     * @param {Dataset[]} callback.datasets The list of datasets in your project.
     *
     * @example
     * ```
     * const {BigQuery} = require('@google-cloud/bigquery');
     * const bigquery = new BigQuery();
     *
     * bigquery.getDatasets(function(err, datasets) {
     *   if (!err) {
     *     // datasets is an array of Dataset objects.
     *   }
     * });
     *
     * //-
     * // To control how many API requests are made and page through the results
     * // manually, set `autoPaginate` to `false`.
     * //-
     * function manualPaginationCallback(err, datasets, nextQuery, apiResponse) {
     *   if (nextQuery) {
     *     // More results exist.
     *     bigquery.getDatasets(nextQuery, manualPaginationCallback);
     *   }
     * }
     *
     * bigquery.getDatasets({
     *   autoPaginate: false
     * }, manualPaginationCallback);
     *
     * //-
     * // If the callback is omitted, we'll return a Promise.
     * //-
     * bigquery.getDatasets().then(function(datasets) {});
     * ```
     */
    getDatasets(options?: GetDatasetsOptions): Promise<DatasetsResponse>;
    getDatasets(options: GetDatasetsOptions, callback: DatasetsCallback): void;
    getDatasets(callback: DatasetsCallback): void;
    /**
     * @callback GetJobsCallback
     * @param {?Error} err Request error, if any.
     * @param {object} jobs An array of [Job]{@link https://cloud.google.com/bigquery/docs/reference/v2/Job} objects.
     */
    /**
     * @typedef {array} GetJobsResponse
     * @property {object} 0 An array of Job objects.
     */
    /**
     * Get all of the jobs from your project.
     *
     * See {@link https://cloud.google.com/bigquery/docs/reference/v2/jobs/list| Jobs: list API Documentation}
     *
     * @param {object} [options] Configuration object.
     * @param {boolean} [options.allUsers] Display jobs owned by all users in the
     *     project.
     * @param {boolean} [options.autoPaginate] Have pagination handled
     *     automatically. Default: true.
     * @param {number} [options.maxApiCalls] Maximum number of API calls to make.
     * @param {number} [options.maxResults] Maximum number of results to return.
     * @param {string} [options.pageToken] Token returned from a previous call, to
     *     request the next page of results.
     * @param {string} [options.projection] Restrict information returned to a set
     *     of selected fields. Acceptable values are "full", for all job data, and
     *     "minimal", to not include the job configuration.
     * @param {string} [options.stateFilter] Filter for job state. Acceptable
     *     values are "done", "pending", and "running". Sending an array to this
     *     option performs a disjunction.
     * @param {GetJobsCallback} [callback] The callback function.
     * @param {?error} callback.err An error returned while making this request
     * @param {Job[]} callback.jobs The list of jobs in your
     *     project.
     *
     * @example
     * ```
     * const {BigQuery} = require('@google-cloud/bigquery');
     * const bigquery = new BigQuery();
     *
     * bigquery.getJobs(function(err, jobs) {
     *   if (!err) {
     *     // jobs is an array of Job objects.
     *   }
     * });
     *
     * //-
     * // To control how many API requests are made and page through the results
     * // manually, set `autoPaginate` to `false`.
     * //-
     * function manualPaginationCallback(err, jobs, nextQuery, apiRespose) {
     *   if (nextQuery) {
     *     // More results exist.
     *     bigquery.getJobs(nextQuery, manualPaginationCallback);
     *   }
     * }
     *
     * bigquery.getJobs({
     *   autoPaginate: false
     * }, manualPaginationCallback);
     *
     * //-
     * // If the callback is omitted, we'll return a Promise.
     * //-
     * bigquery.getJobs().then(function(data) {
     *   const jobs = data[0];
     * });
     * ```
     */
    getJobs(options?: GetJobsOptions): Promise<GetJobsResponse>;
    getJobs(options: GetJobsOptions, callback: GetJobsCallback): void;
    getJobs(callback: GetJobsCallback): void;
    /**
     * Create a reference to an existing job.
     *
     * @param {string} id ID of the job.
     * @param {object} [options] Configuration object.
     * @param {string} [options.location] The geographic location of the job.
     *      Required except for US and EU.
     *
     * @example
     * ```
     * const {BigQuery} = require('@google-cloud/bigquery');
     * const bigquery = new BigQuery();
     *
     * const myExistingJob = bigquery.job('job-id');
     * ```
     */
    job(id: string, options?: JobOptions): Job;
    /**
     * Run a query scoped to your project. For manual pagination please refer to
     * {@link BigQuery.createQueryJob}.
     *
     * See {@link https://cloud.google.com/bigquery/docs/reference/v2/jobs/query| Jobs: query API Documentation}
     *
     * @param {string|object} query A string SQL query or configuration object.
     *     For all available options, see
     *     {@link https://cloud.google.com/bigquery/docs/reference/v2/jobs/query#request-body| Jobs: query request body}.
     * @param {string} [query.location] The geographic location of the job.
     *     Required except for US and EU.
     * @param {string} [query.jobId] Custom id for the underlying job.
     * @param {string} [query.jobPrefix] Prefix to apply to the underlying job id.
     * @param {object|Array<*>} query.params For positional SQL parameters, provide
     *     an array of values. For named SQL parameters, provide an object which
     *     maps each named parameter to its value. The supported types are
     * integers, floats, {@link BigQuery.date} objects, {@link BigQuery.datetime}
     *     objects, {@link BigQuery.time} objects, {@link BigQuery.timestamp}
     *     objects, Strings, Booleans, and Objects.
     * @param {string} query.query A query string, following the BigQuery query
     *     syntax, of the query to execute.
     * @param {object|Array<*>} query.types Provided types for query parameters.
     *     For positional SQL parameters, provide an array of types. For named
     *     SQL parameters, provide an object which maps each named parameter to
     *     its type.
     * @param {boolean} [query.useLegacySql=false] Option to use legacy sql syntax.
     * @param {object} [options] Configuration object for query results.
     * @param {number} [options.maxResults] Maximum number of results to read.
     * @param {number} [options.timeoutMs] How long to wait for the query to
     *     complete, in milliseconds, before returning. Default is 10 seconds.
     *     If the timeout passes before the job completes, an error will be returned
     *     and the 'jobComplete' field in the response will be false.
     * @param {boolean|IntegerTypeCastOptions} [options.wrapIntegers=false] Wrap values
     *     of 'INT64' type in {@link BigQueryInt} objects.
     *     If a `boolean`, this will wrap values in {@link BigQueryInt} objects.
     *     If an `object`, this will return a value returned by
     *     `wrapIntegers.integerTypeCastFunction`.
     *     Please see {@link IntegerTypeCastOptions} for options descriptions.
     * @param {function} [callback] The callback function.
     * @param {?error} callback.err An error returned while making this request
     * @param {array} callback.rows The list of results from your query.
     *
     * @example
     * ```
     * const {BigQuery} = require('@google-cloud/bigquery');
     * const bigquery = new BigQuery();
     *
     * const query = 'SELECT url FROM `publicdata.samples.github_nested` LIMIT
     * 100';
     *
     * bigquery.query(query, function(err, rows) {
     *   if (!err) {
     *     // rows is an array of results.
     *   }
     * });
     *
     * //-
     * // Positional SQL parameters are supported.
     * //-
     * bigquery.query({
     *   query: [
     *     'SELECT url',
     *     'FROM `publicdata.samples.github_nested`',
     *     'WHERE repository.owner = ?'
     *   ].join(' '),
     *
     *   params: [
     *     'google'
     *   ]
     * }, function(err, rows) {});
     *
     * //-
     * // Or if you prefer to name them, that's also supported.
     * //-
     * bigquery.query({
     *   query: [
     *     'SELECT url',
     *     'FROM `publicdata.samples.github_nested`',
     *     'WHERE repository.owner = @owner'
     *   ].join(' '),
     *   params: {
     *     owner: 'google'
     *   }
     * }, function(err, rows) {});
     *
     * //-
     * // Providing types for SQL parameters is supported.
     * //-
     * bigquery.query({
     *   query: [
     *     'SELECT url',
     *     'FROM `publicdata.samples.github_nested`',
     *     'WHERE repository.owner = ?'
     *   ].join(' '),
     *
     *   params: [
     *     null
     *   ],
     *
     *   types: ['string']
     * }, function(err, rows) {});
     *
     * //-
     * // If you need to use a `DATE`, `DATETIME`, `TIME`, or `TIMESTAMP` type in
     * // your query, see {@link BigQuery.date}, {@link BigQuery.datetime},
     * // {@link BigQuery.time}, and {@link BigQuery.timestamp}.
     * //-
     *
     * //-
     * // If the callback is omitted, we'll return a Promise.
     * //-
     * bigquery.query(query).then(function(data) {
     *   const rows = data[0];
     * });
     * ```
     */
    query(query: string, options?: QueryOptions): Promise<QueryRowsResponse>;
    query(query: Query, options?: QueryOptions): Promise<SimpleQueryRowsResponse>;
    query(query: string, options: QueryOptions, callback?: QueryRowsCallback): void;
    query(query: Query, options: QueryOptions, callback?: SimpleQueryRowsCallback): void;
    query(query: string, callback?: QueryRowsCallback): void;
    query(query: Query, callback?: SimpleQueryRowsCallback): void;
    /**
     * Check if the given Query can run using the `jobs.query` endpoint.
     * Returns a bigquery.IQueryRequest that can be used to call `jobs.query`.
     * Return undefined if is not possible to convert to a bigquery.IQueryRequest.
     *
     * @param query string | Query
     * @param options QueryOptions
     * @returns bigquery.IQueryRequest | undefined
     */
    private buildQueryRequest_;
    private runJobsQuery;
    /**
     * This method will be called by `createQueryStream()`. It is required to
     * properly set the `autoPaginate` option value.
     *
     * @private
     */
    queryAsStream_(query: Query, callback?: SimpleQueryRowsCallback): void;
    static setLogFunction: typeof setLogFunction;
}
/**
 * Range class for BigQuery.
 * A range represents contiguous range between two dates, datetimes, or timestamps.
 * The lower and upper bound for the range are optional.
 * The lower bound is inclusive and the upper bound is exclusive.
 * See https://cloud.google.com/bigquery/docs/reference/standard-sql/lexical#range_literals
 */
export declare class BigQueryRange {
    elementType?: string;
    start?: BigQueryTimestamp | BigQueryDate | BigQueryDatetime;
    end?: BigQueryTimestamp | BigQueryDate | BigQueryDatetime;
    constructor(value: string | BigQueryRangeOptions, elementType?: string);
    get apiValue(): string;
    get literalValue(): string;
    get value(): {
        start: string;
        end: string;
    };
    private static fromStringValue_;
    static fromSchemaValue_(value: string, elementType: string): BigQueryRange;
    private convertElement_;
}
/**
 * Date class for BigQuery.
 */
export declare class BigQueryDate {
    value: string;
    constructor(value: BigQueryDateOptions | string);
}
/**
 * Geography class for BigQuery.
 */
export declare class Geography {
    value: string;
    constructor(value: string);
}
/**
 * Timestamp class for BigQuery.
 *
 * The recommended input here is a `Date` or `PreciseDate` class.
 * If passing as a `string`, it should be Timestamp literals: https://cloud.google.com/bigquery/docs/reference/standard-sql/lexical#timestamp_literals.
 * When passing a `number` input, it should be epoch seconds in float representation.
 *
 */
export declare class BigQueryTimestamp {
    value: string;
    constructor(value: Date | PreciseDate | string | number);
    fromFloatValue_(value: number): PreciseDate;
}
/**
 * Datetime class for BigQuery.
 */
export declare class BigQueryDatetime {
    value: string;
    constructor(value: BigQueryDatetimeOptions | string);
}
/**
 * Time class for BigQuery.
 */
export declare class BigQueryTime {
    value: string;
    constructor(value: BigQueryTimeOptions | string);
}
/**
 * Build a BigQueryInt object. For long integers, a string can be provided.
 *
 * @class
 * @param {string|number|IntegerTypeCastValue} value The 'INT64' value.
 * @param {object} [typeCastOptions] Configuration to convert
 *     values of 'INT64' type to a custom value. Must provide an
 *     `integerTypeCastFunction` to handle conversion.
 * @param {function} typeCastOptions.integerTypeCastFunction A custom user
 *     provided function to convert value.
 * @param {string|string[]} [typeCastOptions.fields] Schema field
 *     names to be converted using `integerTypeCastFunction`.
 *
 * @example
 * ```
 * const {BigQuery} = require('@google-cloud/bigquery');
 * const bigquery = new BigQuery();
 * const anInt = bigquery.int(7);
 * ```
 */
export declare class BigQueryInt extends Number {
    type: string;
    value: string;
    typeCastFunction?: Function;
    private _schemaFieldName;
    constructor(value: string | number | IntegerTypeCastValue, typeCastOptions?: IntegerTypeCastOptions);
    valueOf(): any;
    toJSON(): Json;
}
export interface Json {
    [field: string]: string;
}
