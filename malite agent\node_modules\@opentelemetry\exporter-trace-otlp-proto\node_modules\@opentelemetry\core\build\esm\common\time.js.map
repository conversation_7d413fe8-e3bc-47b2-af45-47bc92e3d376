{"version": 3, "file": "time.js", "sourceRoot": "", "sources": ["../../../src/common/time.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAGH,OAAO,EAAE,aAAa,IAAI,WAAW,EAAE,MAAM,aAAa,CAAC;AAG3D,IAAM,iBAAiB,GAAG,CAAC,CAAC;AAC5B,IAAM,2BAA2B,GAAG,CAAC,CAAC;AACtC,IAAM,2BAA2B,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,2BAA2B,CAAC,CAAC;AAC9E,IAAM,qBAAqB,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,iBAAiB,CAAC,CAAC;AAE9D;;;GAGG;AACH,MAAM,UAAU,cAAc,CAAC,WAAmB;IAChD,IAAM,YAAY,GAAG,WAAW,GAAG,IAAI,CAAC;IACxC,iBAAiB;IACjB,IAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;IACzC,+CAA+C;IAC/C,IAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,WAAW,GAAG,IAAI,CAAC,GAAG,2BAA2B,CAAC,CAAC;IAC7E,OAAO,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;AAC1B,CAAC;AAED,MAAM,UAAU,aAAa;IAC3B,IAAI,UAAU,GAAG,WAAW,CAAC,UAAU,CAAC;IACxC,IAAI,OAAO,UAAU,KAAK,QAAQ,EAAE;QAClC,IAAM,IAAI,GAAqB,WAA0C,CAAC;QAC1E,UAAU,GAAG,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC;KACpD;IACD,OAAO,UAAU,CAAC;AACpB,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,MAAM,CAAC,cAAuB;IAC5C,IAAM,UAAU,GAAG,cAAc,CAAC,aAAa,EAAE,CAAC,CAAC;IACnD,IAAM,GAAG,GAAG,cAAc,CACxB,OAAO,cAAc,KAAK,QAAQ,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,WAAW,CAAC,GAAG,EAAE,CACxE,CAAC;IAEF,OAAO,UAAU,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC;AACrC,CAAC;AAED;;;;GAIG;AACH,MAAM,UAAU,iBAAiB,CAAC,IAAmB;IACnD,iBAAiB;IACjB,IAAI,iBAAiB,CAAC,IAAI,CAAC,EAAE;QAC3B,OAAO,IAAkB,CAAC;KAC3B;SAAM,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;QACnC,uEAAuE;QACvE,IAAI,IAAI,GAAG,aAAa,EAAE,EAAE;YAC1B,OAAO,MAAM,CAAC,IAAI,CAAC,CAAC;SACrB;aAAM;YACL,+CAA+C;YAC/C,OAAO,cAAc,CAAC,IAAI,CAAC,CAAC;SAC7B;KACF;SAAM,IAAI,IAAI,YAAY,IAAI,EAAE;QAC/B,OAAO,cAAc,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC;KACvC;SAAM;QACL,MAAM,SAAS,CAAC,oBAAoB,CAAC,CAAC;KACvC;AACH,CAAC;AAED;;;;GAIG;AACH,MAAM,UAAU,cAAc,CAC5B,SAAqB,EACrB,OAAmB;IAEnB,IAAI,OAAO,GAAG,OAAO,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;IACxC,IAAI,KAAK,GAAG,OAAO,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;IAEtC,WAAW;IACX,IAAI,KAAK,GAAG,CAAC,EAAE;QACb,OAAO,IAAI,CAAC,CAAC;QACb,SAAS;QACT,KAAK,IAAI,qBAAqB,CAAC;KAChC;IAED,OAAO,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;AAC1B,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,iBAAiB,CAAC,IAAgB;IAChD,IAAM,SAAS,GAAG,iBAAiB,CAAC;IACpC,IAAM,GAAG,GAAG,KAAG,GAAG,CAAC,MAAM,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,MAAG,CAAC;IAClD,IAAM,UAAU,GAAG,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,GAAG,SAAS,GAAG,CAAC,CAAC,CAAC;IAC1D,IAAM,IAAI,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,WAAW,EAAE,CAAC;IACpD,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;AAC1C,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,mBAAmB,CAAC,IAAgB;IAClD,OAAO,IAAI,CAAC,CAAC,CAAC,GAAG,qBAAqB,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;AACnD,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,oBAAoB,CAAC,IAAgB;IACnD,OAAO,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;AACvC,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,oBAAoB,CAAC,IAAgB;IACnD,OAAO,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;AACvC,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,iBAAiB,CAAC,KAAc;IAC9C,OAAO,CACL,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC;QACpB,KAAK,CAAC,MAAM,KAAK,CAAC;QAClB,OAAO,KAAK,CAAC,CAAC,CAAC,KAAK,QAAQ;QAC5B,OAAO,KAAK,CAAC,CAAC,CAAC,KAAK,QAAQ,CAC7B,CAAC;AACJ,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,WAAW,CACzB,KAAc;IAEd,OAAO,CACL,iBAAiB,CAAC,KAAK,CAAC;QACxB,OAAO,KAAK,KAAK,QAAQ;QACzB,KAAK,YAAY,IAAI,CACtB,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,UAAU,CAAC,KAAiB,EAAE,KAAiB;IAC7D,IAAM,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAe,CAAC;IAErE,cAAc;IACd,IAAI,GAAG,CAAC,CAAC,CAAC,IAAI,qBAAqB,EAAE;QACnC,GAAG,CAAC,CAAC,CAAC,IAAI,qBAAqB,CAAC;QAChC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;KACb;IAED,OAAO,GAAG,CAAC;AACb,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport * as api from '@opentelemetry/api';\nimport { otperformance as performance } from '../platform';\nimport { TimeOriginLegacy } from './types';\n\nconst NANOSECOND_DIGITS = 9;\nconst NANOSECOND_DIGITS_IN_MILLIS = 6;\nconst MILLISECONDS_TO_NANOSECONDS = Math.pow(10, NANOSECOND_DIGITS_IN_MILLIS);\nconst SECOND_TO_NANOSECONDS = Math.pow(10, NANOSECOND_DIGITS);\n\n/**\n * Converts a number of milliseconds from epoch to HrTime([seconds, remainder in nanoseconds]).\n * @param epochMillis\n */\nexport function millisToHrTime(epochMillis: number): api.HrTime {\n  const epochSeconds = epochMillis / 1000;\n  // Decimals only.\n  const seconds = Math.trunc(epochSeconds);\n  // Round sub-nanosecond accuracy to nanosecond.\n  const nanos = Math.round((epochMillis % 1000) * MILLISECONDS_TO_NANOSECONDS);\n  return [seconds, nanos];\n}\n\nexport function getTimeOrigin(): number {\n  let timeOrigin = performance.timeOrigin;\n  if (typeof timeOrigin !== 'number') {\n    const perf: TimeOriginLegacy = performance as unknown as TimeOriginLegacy;\n    timeOrigin = perf.timing && perf.timing.fetchStart;\n  }\n  return timeOrigin;\n}\n\n/**\n * Returns an hrtime calculated via performance component.\n * @param performanceNow\n */\nexport function hrTime(performanceNow?: number): api.HrTime {\n  const timeOrigin = millisToHrTime(getTimeOrigin());\n  const now = millisToHrTime(\n    typeof performanceNow === 'number' ? performanceNow : performance.now()\n  );\n\n  return addHrTimes(timeOrigin, now);\n}\n\n/**\n *\n * Converts a TimeInput to an HrTime, defaults to _hrtime().\n * @param time\n */\nexport function timeInputToHrTime(time: api.TimeInput): api.HrTime {\n  // process.hrtime\n  if (isTimeInputHrTime(time)) {\n    return time as api.HrTime;\n  } else if (typeof time === 'number') {\n    // Must be a performance.now() if it's smaller than process start time.\n    if (time < getTimeOrigin()) {\n      return hrTime(time);\n    } else {\n      // epoch milliseconds or performance.timeOrigin\n      return millisToHrTime(time);\n    }\n  } else if (time instanceof Date) {\n    return millisToHrTime(time.getTime());\n  } else {\n    throw TypeError('Invalid input type');\n  }\n}\n\n/**\n * Returns a duration of two hrTime.\n * @param startTime\n * @param endTime\n */\nexport function hrTimeDuration(\n  startTime: api.HrTime,\n  endTime: api.HrTime\n): api.HrTime {\n  let seconds = endTime[0] - startTime[0];\n  let nanos = endTime[1] - startTime[1];\n\n  // overflow\n  if (nanos < 0) {\n    seconds -= 1;\n    // negate\n    nanos += SECOND_TO_NANOSECONDS;\n  }\n\n  return [seconds, nanos];\n}\n\n/**\n * Convert hrTime to timestamp, for example \"2019-05-14T17:00:00.000123456Z\"\n * @param time\n */\nexport function hrTimeToTimeStamp(time: api.HrTime): string {\n  const precision = NANOSECOND_DIGITS;\n  const tmp = `${'0'.repeat(precision)}${time[1]}Z`;\n  const nanoString = tmp.substr(tmp.length - precision - 1);\n  const date = new Date(time[0] * 1000).toISOString();\n  return date.replace('000Z', nanoString);\n}\n\n/**\n * Convert hrTime to nanoseconds.\n * @param time\n */\nexport function hrTimeToNanoseconds(time: api.HrTime): number {\n  return time[0] * SECOND_TO_NANOSECONDS + time[1];\n}\n\n/**\n * Convert hrTime to milliseconds.\n * @param time\n */\nexport function hrTimeToMilliseconds(time: api.HrTime): number {\n  return time[0] * 1e3 + time[1] / 1e6;\n}\n\n/**\n * Convert hrTime to microseconds.\n * @param time\n */\nexport function hrTimeToMicroseconds(time: api.HrTime): number {\n  return time[0] * 1e6 + time[1] / 1e3;\n}\n\n/**\n * check if time is HrTime\n * @param value\n */\nexport function isTimeInputHrTime(value: unknown): value is api.HrTime {\n  return (\n    Array.isArray(value) &&\n    value.length === 2 &&\n    typeof value[0] === 'number' &&\n    typeof value[1] === 'number'\n  );\n}\n\n/**\n * check if input value is a correct types.TimeInput\n * @param value\n */\nexport function isTimeInput(\n  value: unknown\n): value is api.HrTime | number | Date {\n  return (\n    isTimeInputHrTime(value) ||\n    typeof value === 'number' ||\n    value instanceof Date\n  );\n}\n\n/**\n * Given 2 HrTime formatted times, return their sum as an HrTime.\n */\nexport function addHrTimes(time1: api.HrTime, time2: api.HrTime): api.HrTime {\n  const out = [time1[0] + time2[0], time1[1] + time2[1]] as api.HrTime;\n\n  // Nanoseconds\n  if (out[1] >= SECOND_TO_NANOSECONDS) {\n    out[1] -= SECOND_TO_NANOSECONDS;\n    out[0] += 1;\n  }\n\n  return out;\n}\n"]}