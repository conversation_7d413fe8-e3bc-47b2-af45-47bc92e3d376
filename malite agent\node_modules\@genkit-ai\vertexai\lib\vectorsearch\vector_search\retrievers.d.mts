import * as _genkit_ai_ai_retriever from '@genkit-ai/ai/retriever';
import { z, Genkit, RetrieverAction } from 'genkit';
import { f as VertexVectorSearchOptions } from '../../types-C_tGep_V.mjs';
import '../../types-Bc0LKM8D.mjs';
import 'google-auth-library';
import '@google-cloud/vertexai';
import 'genkit/model';
import '@google-cloud/aiplatform';
import 'genkit/embedder';
import 'genkit/retriever';

/**
 * Creates Vertex AI retrievers.
 *
 * This function returns a list of retriever actions for Vertex AI based on the provided
 * vector search options and embedder configurations.
 *
 * @param {VertexVectorSearchOptions<EmbedderCustomOptions>} params - The parameters for creating the retrievers.
 * @returns {RetrieverAction<z.ZodTypeAny>[]} - An array of retriever actions.
 */
declare function vertexAiRetrievers<EmbedderCustomOptions extends z.ZodTypeAny>(ai: Genkit, params: VertexVectorSearchOptions<EmbedderCustomOptions>): RetrieverAction<z.ZodTypeAny>[];
/**
 * Creates a reference to a Vertex AI retriever.
 *
 * @param {Object} params - The parameters for the retriever reference.
 * @param {string} params.indexId - The ID of the Vertex AI index.
 * @param {string} [params.displayName] - An optional display name for the retriever.
 * @returns {Object} - The retriever reference object.
 */
declare const vertexAiRetrieverRef: (params: {
    indexId: string;
    displayName?: string;
}) => _genkit_ai_ai_retriever.RetrieverReference<z.ZodOptional<z.ZodOptional<z.ZodObject<z.objectUtil.extendShape<{
    k: z.ZodOptional<z.ZodNumber>;
}, {}>, "strip", z.ZodTypeAny, {
    k?: number | undefined;
}, {
    k?: number | undefined;
}>>>>;

export { vertexAiRetrieverRef, vertexAiRetrievers };
