{"version": 3, "sources": ["../../../src/vectorsearch/vector_search/index.ts"], "sourcesContent": ["/**\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport {\n  getBigQueryDocumentIndexer,\n  getBigQueryDocumentRetriever,\n} from './bigquery.js';\nexport {\n  getFirestoreDocumentIndexer,\n  getFirestoreDocumentRetriever,\n} from './firestore.js';\nexport { vertexAiIndexerRef, vertexAiIndexers } from './indexers.js';\nexport { vertexAiRetrieverRef, vertexAiRetrievers } from './retrievers.js';\nexport {\n  VertexAIVectorIndexerOptionsSchema,\n  VertexAIVectorRetrieverOptionsSchema,\n  type DocumentIndexer,\n  type DocumentRetriever,\n  type Neighbor,\n  type VectorSearchOptions,\n  type VertexAIVectorIndexerOptions,\n  type VertexAIVectorRetrieverOptions,\n} from './types.js';\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAgBA,sBAGO;AACP,uBAGO;AACP,sBAAqD;AACrD,wBAAyD;AACzD,mBASO;", "names": []}