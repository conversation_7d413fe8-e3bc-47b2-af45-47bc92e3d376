"use strict";
/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.serviceInstanceIdDetectorSync = exports.processDetectorSync = exports.processDetector = exports.osDetectorSync = exports.osDetector = exports.hostDetectorSync = exports.hostDetector = void 0;
var HostDetector_1 = require("./HostDetector");
Object.defineProperty(exports, "hostDetector", { enumerable: true, get: function () { return HostDetector_1.hostDetector; } });
var HostDetectorSync_1 = require("./HostDetectorSync");
Object.defineProperty(exports, "hostDetectorSync", { enumerable: true, get: function () { return HostDetectorSync_1.hostDetectorSync; } });
var OSDetector_1 = require("./OSDetector");
Object.defineProperty(exports, "osDetector", { enumerable: true, get: function () { return OSDetector_1.osDetector; } });
var OSDetectorSync_1 = require("./OSDetectorSync");
Object.defineProperty(exports, "osDetectorSync", { enumerable: true, get: function () { return OSDetectorSync_1.osDetectorSync; } });
var ProcessDetector_1 = require("./ProcessDetector");
Object.defineProperty(exports, "processDetector", { enumerable: true, get: function () { return ProcessDetector_1.processDetector; } });
var ProcessDetectorSync_1 = require("./ProcessDetectorSync");
Object.defineProperty(exports, "processDetectorSync", { enumerable: true, get: function () { return ProcessDetectorSync_1.processDetectorSync; } });
var ServiceInstanceIdDetectorSync_1 = require("./ServiceInstanceIdDetectorSync");
Object.defineProperty(exports, "serviceInstanceIdDetectorSync", { enumerable: true, get: function () { return ServiceInstanceIdDetectorSync_1.serviceInstanceIdDetectorSync; } });
//# sourceMappingURL=index.js.map