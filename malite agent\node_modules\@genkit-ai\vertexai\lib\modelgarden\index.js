"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
var modelgarden_exports = {};
__export(modelgarden_exports, {
  claude35Sonnet: () => import_anthropic2.claude35Sonnet,
  claude35SonnetV2: () => import_anthropic2.claude35SonnetV2,
  claude3Haiku: () => import_anthropic2.claude3Haiku,
  claude3Opus: () => import_anthropic2.claude3Opus,
  claude3Sonnet: () => import_anthropic2.claude3Sonnet,
  codestral: () => import_mistral2.codestral,
  llama3: () => import_model_garden2.llama3,
  llama31: () => import_model_garden2.llama31,
  llama32: () => import_model_garden2.llama32,
  mistralLarge: () => import_mistral2.mistralLarge,
  mistralNemo: () => import_mistral2.mistralNemo,
  vertexAIModelGarden: () => vertexAIModelGarden
});
module.exports = __toCommonJS(modelgarden_exports);
var import_plugin = require("genkit/plugin");
var import_common = require("../common/index.js");
var import_anthropic = require("./anthropic.js");
var import_mistral = require("./mistral.js");
var import_model_garden = require("./model_garden.js");
var import_anthropic2 = require("./anthropic.js");
var import_mistral2 = require("./mistral.js");
var import_model_garden2 = require("./model_garden.js");
function vertexAIModelGarden(options) {
  return (0, import_plugin.genkitPlugin)("vertexAIModelGarden", async (ai) => {
    const { projectId, location, authClient } = await (0, import_common.getDerivedParams)(options);
    options.models.forEach((m) => {
      const anthropicEntry = Object.entries(import_anthropic.SUPPORTED_ANTHROPIC_MODELS).find(
        ([_, value]) => value.name === m.name
      );
      if (anthropicEntry) {
        (0, import_anthropic.anthropicModel)(ai, anthropicEntry[0], projectId, location);
        return;
      }
      const mistralEntry = Object.entries(import_mistral.SUPPORTED_MISTRAL_MODELS).find(
        ([_, value]) => value.name === m.name
      );
      if (mistralEntry) {
        (0, import_mistral.mistralModel)(ai, mistralEntry[0], projectId, location);
        return;
      }
      const openaiModel = Object.entries(import_model_garden.SUPPORTED_OPENAI_FORMAT_MODELS).find(
        ([_, value]) => value.name === m.name
      );
      if (openaiModel) {
        (0, import_model_garden.modelGardenOpenaiCompatibleModel)(
          ai,
          openaiModel[0],
          projectId,
          location,
          authClient,
          options.openAiBaseUrlTemplate
        );
        return;
      }
      throw new Error(`Unsupported model garden model: ${m.name}`);
    });
  });
}
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  claude35Sonnet,
  claude35SonnetV2,
  claude3Haiku,
  claude3Opus,
  claude3Sonnet,
  codestral,
  llama3,
  llama31,
  llama32,
  mistralLarge,
  mistralNemo,
  vertexAIModelGarden
});
//# sourceMappingURL=index.js.map