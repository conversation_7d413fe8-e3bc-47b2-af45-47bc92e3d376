"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
var rerankers_exports = {};
__export(rerankers_exports, {
  vertexAIRerankers: () => vertexAIRerankers
});
module.exports = __toCommonJS(rerankers_exports);
var import_plugin = require("genkit/plugin");
var import_common = require("../common/index.js");
var import_reranker = require("./reranker.js");
function vertexAIRerankers(options) {
  return (0, import_plugin.genkitPlugin)("vertexAIRerankers", async (ai) => {
    const { projectId, location, authClient } = await (0, import_common.getDerivedParams)(options);
    await (0, import_reranker.vertexAiRerankers)(ai, {
      projectId,
      location,
      authClient,
      rerankOptions: options.rerankers.map(
        (o) => typeof o === "string" ? { model: o } : o
      )
    });
  });
}
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  vertexAIRerankers
});
//# sourceMappingURL=index.js.map