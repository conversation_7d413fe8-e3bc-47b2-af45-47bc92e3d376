(e=>{"function"==typeof define&&define.amd?define(["protobufjs/minimal"],e):"function"==typeof require&&"object"==typeof module&&module&&module.exports&&(module.exports=e(require("protobufjs/minimal")))})(function(r){var e,t,o,n,x,i=r.util,a=r.roots.firestore_v1beta1||(r.roots.firestore_v1beta1={});function V(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function L(e){if(this.fields={},e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function s(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function U(e){if(this.values=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function J(e){if(this.file=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function l(e){if(this.dependency=[],this.publicDependency=[],this.weakDependency=[],this.messageType=[],this.enumType=[],this.service=[],this.extension=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function u(e){if(this.field=[],this.extension=[],this.nestedType=[],this.enumType=[],this.extensionRange=[],this.oneofDecl=[],this.reservedRange=[],this.reservedName=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function G(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function B(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function p(e){if(this.uninterpretedOption=[],this.declaration=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function M(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function c(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function Y(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function g(e){if(this.value=[],this.reservedRange=[],this.reservedName=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function Q(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function q(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function W(e){if(this.method=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function f(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function d(e){if(this.uninterpretedOption=[],this[".google.api.resourceDefinition"]=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function y(e){if(this.uninterpretedOption=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function m(e){if(this.targets=[],this.editionDefaults=[],this.uninterpretedOption=[],this[".google.api.fieldBehavior"]=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function H(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function z(e){if(this.uninterpretedOption=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function K(e){if(this.uninterpretedOption=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function X(e){if(this.uninterpretedOption=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function b(e){if(this.uninterpretedOption=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function O(e){if(this.uninterpretedOption=[],this[".google.api.methodSignature"]=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function h(e){if(this.name=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function Z(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function v(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function $(e){if(this.defaults=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function ee(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function te(e){if(this.location=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function oe(e){if(this.path=[],this.span=[],this.leadingDetachedComments=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function re(e){if(this.annotation=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function T(e){if(this.path=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function ne(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function ie(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function ae(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function se(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function le(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function ue(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function pe(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function ce(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function ge(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function fe(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function de(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function ye(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function me(e){if(this.paths=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function be(e){if(this.fieldPaths=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function Oe(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function he(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function ve(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function Te(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function Se(e){if(this.fields={},e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function S(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function je(e){if(this.values=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function Ee(e){if(this.fields={},e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function j(e,t,o){r.rpc.Service.call(this,e,t,o)}function Ne(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function E(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function we(e){if(this.documents=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function Pe(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function De(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function Re(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function N(e){if(this.documents=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function Ie(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function Ae(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function ke(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function _e(e){if(this.writes=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function Ce(e){if(this.writeResults=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function Fe(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function w(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function xe(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function Ve(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function Le(e){if(this.partitions=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function Ue(e){if(this.writes=[],this.labels={},e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function Je(e){if(this.writeResults=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function Ge(e){if(this.labels={},e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function Be(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function P(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function Me(e){if(this.documents=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function Ye(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function Qe(e){if(this.targetIds=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function qe(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function We(e){if(this.collectionIds=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function He(e){if(this.writes=[],this.labels={},e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function ze(e){if(this.writeResults=[],this.status=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function D(e){if(this.from=[],this.orderBy=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function Ke(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function Xe(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function Ze(e){if(this.filters=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function $e(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function et(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function tt(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function ot(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function rt(e){if(this.fields=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function nt(e){if(this.values=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function R(e){if(this.updateTransforms=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function it(e){if(this.fieldTransforms=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function I(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function at(e){if(this.transformResults=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function st(e){if(this.targetIds=[],this.removedTargetIds=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function lt(e){if(this.removedTargetIds=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function ut(e){if(this.removedTargetIds=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function pt(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function A(e){if(this.functionName=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function ct(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function gt(e){if(this.rules=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function k(e){if(this.additionalBindings=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function ft(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function dt(e){if(this.destinations=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function _(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function C(e){if(this.methodSettings=[],this.codeownerGithubTeams=[],this.librarySettings=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function yt(e){if(this.serviceClassNames={},e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function mt(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function bt(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function Ot(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function ht(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function vt(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function Tt(e){if(this.renamedServices={},this.renamedResources={},this.ignoredResources=[],this.forcedNamespaceAliases=[],this.handwrittenSignatures=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function St(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function jt(e){if(this.renamedServices={},e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function Et(e){if(this.autoPopulatedFields=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function Nt(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function wt(e){if(this.methods=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function F(e){if(this.pattern=[],this.style=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function Pt(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function Dt(e){if(this.details=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function Rt(e,t,o){r.rpc.Service.call(this,e,t,o)}function It(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function At(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function kt(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function _t(e){if(this.operations=[],e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function Ct(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function Ft(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function xt(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}function Vt(e){if(e)for(var t=Object.keys(e),o=0;o<t.length;++o)null!=e[t[o]]&&(this[t[o]]=e[t[o]])}return a.google=((x={}).protobuf=((n={}).Timestamp=(V.prototype.seconds=i.Long?i.Long.fromBits(0,0,!1):0,V.prototype.nanos=0,V.fromObject=function(e){var t;return e instanceof a.google.protobuf.Timestamp?e:(t=new a.google.protobuf.Timestamp,null!=e.seconds&&(i.Long?(t.seconds=i.Long.fromValue(e.seconds)).unsigned=!1:"string"==typeof e.seconds?t.seconds=parseInt(e.seconds,10):"number"==typeof e.seconds?t.seconds=e.seconds:"object"==typeof e.seconds&&(t.seconds=new i.LongBits(e.seconds.low>>>0,e.seconds.high>>>0).toNumber())),null!=e.nanos&&(t.nanos=0|e.nanos),t)},V.toObject=function(e,t){var o,r={};return(t=t||{}).defaults&&(i.Long?(o=new i.Long(0,0,!1),r.seconds=t.longs===String?o.toString():t.longs===Number?o.toNumber():o):r.seconds=t.longs===String?"0":0,r.nanos=0),null!=e.seconds&&e.hasOwnProperty("seconds")&&("number"==typeof e.seconds?r.seconds=t.longs===String?String(e.seconds):e.seconds:r.seconds=t.longs===String?i.Long.prototype.toString.call(e.seconds):t.longs===Number?new i.LongBits(e.seconds.low>>>0,e.seconds.high>>>0).toNumber():e.seconds),null!=e.nanos&&e.hasOwnProperty("nanos")&&(r.nanos=e.nanos),r},V.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},V.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.Timestamp"},V),n.Struct=(L.prototype.fields=i.emptyObject,L.fromObject=function(e){if(e instanceof a.google.protobuf.Struct)return e;var t=new a.google.protobuf.Struct;if(e.fields){if("object"!=typeof e.fields)throw TypeError(".google.protobuf.Struct.fields: object expected");t.fields={};for(var o=Object.keys(e.fields),r=0;r<o.length;++r){if("object"!=typeof e.fields[o[r]])throw TypeError(".google.protobuf.Struct.fields: object expected");t.fields[o[r]]=a.google.protobuf.Value.fromObject(e.fields[o[r]])}}return t},L.toObject=function(e,t){var o,r={};if(((t=t||{}).objects||t.defaults)&&(r.fields={}),e.fields&&(o=Object.keys(e.fields)).length){r.fields={};for(var n=0;n<o.length;++n)r.fields[o[n]]=a.google.protobuf.Value.toObject(e.fields[o[n]],t)}return r},L.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},L.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.Struct"},L),n.Value=(s.prototype.nullValue=null,s.prototype.numberValue=null,s.prototype.stringValue=null,s.prototype.boolValue=null,s.prototype.structValue=null,s.prototype.listValue=null,Object.defineProperty(s.prototype,"kind",{get:i.oneOfGetter(e=["nullValue","numberValue","stringValue","boolValue","structValue","listValue"]),set:i.oneOfSetter(e)}),s.fromObject=function(e){if(e instanceof a.google.protobuf.Value)return e;var t=new a.google.protobuf.Value;switch(e.nullValue){default:"number"==typeof e.nullValue&&(t.nullValue=e.nullValue);break;case"NULL_VALUE":case 0:t.nullValue=0}if(null!=e.numberValue&&(t.numberValue=Number(e.numberValue)),null!=e.stringValue&&(t.stringValue=String(e.stringValue)),null!=e.boolValue&&(t.boolValue=Boolean(e.boolValue)),null!=e.structValue){if("object"!=typeof e.structValue)throw TypeError(".google.protobuf.Value.structValue: object expected");t.structValue=a.google.protobuf.Struct.fromObject(e.structValue)}if(null!=e.listValue){if("object"!=typeof e.listValue)throw TypeError(".google.protobuf.Value.listValue: object expected");t.listValue=a.google.protobuf.ListValue.fromObject(e.listValue)}return t},s.toObject=function(e,t){t=t||{};var o={};return null!=e.nullValue&&e.hasOwnProperty("nullValue")&&(o.nullValue=t.enums!==String||void 0===a.google.protobuf.NullValue[e.nullValue]?e.nullValue:a.google.protobuf.NullValue[e.nullValue],t.oneofs)&&(o.kind="nullValue"),null!=e.numberValue&&e.hasOwnProperty("numberValue")&&(o.numberValue=t.json&&!isFinite(e.numberValue)?String(e.numberValue):e.numberValue,t.oneofs)&&(o.kind="numberValue"),null!=e.stringValue&&e.hasOwnProperty("stringValue")&&(o.stringValue=e.stringValue,t.oneofs)&&(o.kind="stringValue"),null!=e.boolValue&&e.hasOwnProperty("boolValue")&&(o.boolValue=e.boolValue,t.oneofs)&&(o.kind="boolValue"),null!=e.structValue&&e.hasOwnProperty("structValue")&&(o.structValue=a.google.protobuf.Struct.toObject(e.structValue,t),t.oneofs)&&(o.kind="structValue"),null!=e.listValue&&e.hasOwnProperty("listValue")&&(o.listValue=a.google.protobuf.ListValue.toObject(e.listValue,t),t.oneofs)&&(o.kind="listValue"),o},s.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},s.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.Value"},s),n.NullValue=(e={},(o=Object.create(e))[e[0]="NULL_VALUE"]="NULL_VALUE",o),n.ListValue=(U.prototype.values=i.emptyArray,U.fromObject=function(e){if(e instanceof a.google.protobuf.ListValue)return e;var t=new a.google.protobuf.ListValue;if(e.values){if(!Array.isArray(e.values))throw TypeError(".google.protobuf.ListValue.values: array expected");t.values=[];for(var o=0;o<e.values.length;++o){if("object"!=typeof e.values[o])throw TypeError(".google.protobuf.ListValue.values: object expected");t.values[o]=a.google.protobuf.Value.fromObject(e.values[o])}}return t},U.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.values=[]),e.values&&e.values.length){o.values=[];for(var r=0;r<e.values.length;++r)o.values[r]=a.google.protobuf.Value.toObject(e.values[r],t)}return o},U.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},U.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.ListValue"},U),n.FileDescriptorSet=(J.prototype.file=i.emptyArray,J.fromObject=function(e){if(e instanceof a.google.protobuf.FileDescriptorSet)return e;var t=new a.google.protobuf.FileDescriptorSet;if(e.file){if(!Array.isArray(e.file))throw TypeError(".google.protobuf.FileDescriptorSet.file: array expected");t.file=[];for(var o=0;o<e.file.length;++o){if("object"!=typeof e.file[o])throw TypeError(".google.protobuf.FileDescriptorSet.file: object expected");t.file[o]=a.google.protobuf.FileDescriptorProto.fromObject(e.file[o])}}return t},J.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.file=[]),e.file&&e.file.length){o.file=[];for(var r=0;r<e.file.length;++r)o.file[r]=a.google.protobuf.FileDescriptorProto.toObject(e.file[r],t)}return o},J.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},J.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.FileDescriptorSet"},J),n.Edition=(e={},(o=Object.create(e))[e[0]="EDITION_UNKNOWN"]="EDITION_UNKNOWN",o[e[998]="EDITION_PROTO2"]="EDITION_PROTO2",o[e[999]="EDITION_PROTO3"]="EDITION_PROTO3",o[e[1e3]="EDITION_2023"]="EDITION_2023",o[e[1001]="EDITION_2024"]="EDITION_2024",o[e[1]="EDITION_1_TEST_ONLY"]="EDITION_1_TEST_ONLY",o[e[2]="EDITION_2_TEST_ONLY"]="EDITION_2_TEST_ONLY",o[e[99997]="EDITION_99997_TEST_ONLY"]="EDITION_99997_TEST_ONLY",o[e[99998]="EDITION_99998_TEST_ONLY"]="EDITION_99998_TEST_ONLY",o[e[99999]="EDITION_99999_TEST_ONLY"]="EDITION_99999_TEST_ONLY",o[e[2147483647]="EDITION_MAX"]="EDITION_MAX",o),n.FileDescriptorProto=(l.prototype.name="",l.prototype.package="",l.prototype.dependency=i.emptyArray,l.prototype.publicDependency=i.emptyArray,l.prototype.weakDependency=i.emptyArray,l.prototype.messageType=i.emptyArray,l.prototype.enumType=i.emptyArray,l.prototype.service=i.emptyArray,l.prototype.extension=i.emptyArray,l.prototype.options=null,l.prototype.sourceCodeInfo=null,l.prototype.syntax="",l.prototype.edition=0,l.fromObject=function(e){if(e instanceof a.google.protobuf.FileDescriptorProto)return e;var t=new a.google.protobuf.FileDescriptorProto;if(null!=e.name&&(t.name=String(e.name)),null!=e.package&&(t.package=String(e.package)),e.dependency){if(!Array.isArray(e.dependency))throw TypeError(".google.protobuf.FileDescriptorProto.dependency: array expected");t.dependency=[];for(var o=0;o<e.dependency.length;++o)t.dependency[o]=String(e.dependency[o])}if(e.publicDependency){if(!Array.isArray(e.publicDependency))throw TypeError(".google.protobuf.FileDescriptorProto.publicDependency: array expected");t.publicDependency=[];for(o=0;o<e.publicDependency.length;++o)t.publicDependency[o]=0|e.publicDependency[o]}if(e.weakDependency){if(!Array.isArray(e.weakDependency))throw TypeError(".google.protobuf.FileDescriptorProto.weakDependency: array expected");t.weakDependency=[];for(o=0;o<e.weakDependency.length;++o)t.weakDependency[o]=0|e.weakDependency[o]}if(e.messageType){if(!Array.isArray(e.messageType))throw TypeError(".google.protobuf.FileDescriptorProto.messageType: array expected");t.messageType=[];for(o=0;o<e.messageType.length;++o){if("object"!=typeof e.messageType[o])throw TypeError(".google.protobuf.FileDescriptorProto.messageType: object expected");t.messageType[o]=a.google.protobuf.DescriptorProto.fromObject(e.messageType[o])}}if(e.enumType){if(!Array.isArray(e.enumType))throw TypeError(".google.protobuf.FileDescriptorProto.enumType: array expected");t.enumType=[];for(o=0;o<e.enumType.length;++o){if("object"!=typeof e.enumType[o])throw TypeError(".google.protobuf.FileDescriptorProto.enumType: object expected");t.enumType[o]=a.google.protobuf.EnumDescriptorProto.fromObject(e.enumType[o])}}if(e.service){if(!Array.isArray(e.service))throw TypeError(".google.protobuf.FileDescriptorProto.service: array expected");t.service=[];for(o=0;o<e.service.length;++o){if("object"!=typeof e.service[o])throw TypeError(".google.protobuf.FileDescriptorProto.service: object expected");t.service[o]=a.google.protobuf.ServiceDescriptorProto.fromObject(e.service[o])}}if(e.extension){if(!Array.isArray(e.extension))throw TypeError(".google.protobuf.FileDescriptorProto.extension: array expected");t.extension=[];for(o=0;o<e.extension.length;++o){if("object"!=typeof e.extension[o])throw TypeError(".google.protobuf.FileDescriptorProto.extension: object expected");t.extension[o]=a.google.protobuf.FieldDescriptorProto.fromObject(e.extension[o])}}if(null!=e.options){if("object"!=typeof e.options)throw TypeError(".google.protobuf.FileDescriptorProto.options: object expected");t.options=a.google.protobuf.FileOptions.fromObject(e.options)}if(null!=e.sourceCodeInfo){if("object"!=typeof e.sourceCodeInfo)throw TypeError(".google.protobuf.FileDescriptorProto.sourceCodeInfo: object expected");t.sourceCodeInfo=a.google.protobuf.SourceCodeInfo.fromObject(e.sourceCodeInfo)}switch(null!=e.syntax&&(t.syntax=String(e.syntax)),e.edition){default:"number"==typeof e.edition&&(t.edition=e.edition);break;case"EDITION_UNKNOWN":case 0:t.edition=0;break;case"EDITION_PROTO2":case 998:t.edition=998;break;case"EDITION_PROTO3":case 999:t.edition=999;break;case"EDITION_2023":case 1e3:t.edition=1e3;break;case"EDITION_2024":case 1001:t.edition=1001;break;case"EDITION_1_TEST_ONLY":case 1:t.edition=1;break;case"EDITION_2_TEST_ONLY":case 2:t.edition=2;break;case"EDITION_99997_TEST_ONLY":case 99997:t.edition=99997;break;case"EDITION_99998_TEST_ONLY":case 99998:t.edition=99998;break;case"EDITION_99999_TEST_ONLY":case 99999:t.edition=99999;break;case"EDITION_MAX":case 2147483647:t.edition=2147483647}return t},l.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.dependency=[],o.messageType=[],o.enumType=[],o.service=[],o.extension=[],o.publicDependency=[],o.weakDependency=[]),t.defaults&&(o.name="",o.package="",o.options=null,o.sourceCodeInfo=null,o.syntax="",o.edition=t.enums===String?"EDITION_UNKNOWN":0),null!=e.name&&e.hasOwnProperty("name")&&(o.name=e.name),null!=e.package&&e.hasOwnProperty("package")&&(o.package=e.package),e.dependency&&e.dependency.length){o.dependency=[];for(var r=0;r<e.dependency.length;++r)o.dependency[r]=e.dependency[r]}if(e.messageType&&e.messageType.length){o.messageType=[];for(r=0;r<e.messageType.length;++r)o.messageType[r]=a.google.protobuf.DescriptorProto.toObject(e.messageType[r],t)}if(e.enumType&&e.enumType.length){o.enumType=[];for(r=0;r<e.enumType.length;++r)o.enumType[r]=a.google.protobuf.EnumDescriptorProto.toObject(e.enumType[r],t)}if(e.service&&e.service.length){o.service=[];for(r=0;r<e.service.length;++r)o.service[r]=a.google.protobuf.ServiceDescriptorProto.toObject(e.service[r],t)}if(e.extension&&e.extension.length){o.extension=[];for(r=0;r<e.extension.length;++r)o.extension[r]=a.google.protobuf.FieldDescriptorProto.toObject(e.extension[r],t)}if(null!=e.options&&e.hasOwnProperty("options")&&(o.options=a.google.protobuf.FileOptions.toObject(e.options,t)),null!=e.sourceCodeInfo&&e.hasOwnProperty("sourceCodeInfo")&&(o.sourceCodeInfo=a.google.protobuf.SourceCodeInfo.toObject(e.sourceCodeInfo,t)),e.publicDependency&&e.publicDependency.length){o.publicDependency=[];for(r=0;r<e.publicDependency.length;++r)o.publicDependency[r]=e.publicDependency[r]}if(e.weakDependency&&e.weakDependency.length){o.weakDependency=[];for(r=0;r<e.weakDependency.length;++r)o.weakDependency[r]=e.weakDependency[r]}return null!=e.syntax&&e.hasOwnProperty("syntax")&&(o.syntax=e.syntax),null!=e.edition&&e.hasOwnProperty("edition")&&(o.edition=t.enums!==String||void 0===a.google.protobuf.Edition[e.edition]?e.edition:a.google.protobuf.Edition[e.edition]),o},l.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},l.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.FileDescriptorProto"},l),n.DescriptorProto=(u.prototype.name="",u.prototype.field=i.emptyArray,u.prototype.extension=i.emptyArray,u.prototype.nestedType=i.emptyArray,u.prototype.enumType=i.emptyArray,u.prototype.extensionRange=i.emptyArray,u.prototype.oneofDecl=i.emptyArray,u.prototype.options=null,u.prototype.reservedRange=i.emptyArray,u.prototype.reservedName=i.emptyArray,u.fromObject=function(e){if(e instanceof a.google.protobuf.DescriptorProto)return e;var t=new a.google.protobuf.DescriptorProto;if(null!=e.name&&(t.name=String(e.name)),e.field){if(!Array.isArray(e.field))throw TypeError(".google.protobuf.DescriptorProto.field: array expected");t.field=[];for(var o=0;o<e.field.length;++o){if("object"!=typeof e.field[o])throw TypeError(".google.protobuf.DescriptorProto.field: object expected");t.field[o]=a.google.protobuf.FieldDescriptorProto.fromObject(e.field[o])}}if(e.extension){if(!Array.isArray(e.extension))throw TypeError(".google.protobuf.DescriptorProto.extension: array expected");t.extension=[];for(o=0;o<e.extension.length;++o){if("object"!=typeof e.extension[o])throw TypeError(".google.protobuf.DescriptorProto.extension: object expected");t.extension[o]=a.google.protobuf.FieldDescriptorProto.fromObject(e.extension[o])}}if(e.nestedType){if(!Array.isArray(e.nestedType))throw TypeError(".google.protobuf.DescriptorProto.nestedType: array expected");t.nestedType=[];for(o=0;o<e.nestedType.length;++o){if("object"!=typeof e.nestedType[o])throw TypeError(".google.protobuf.DescriptorProto.nestedType: object expected");t.nestedType[o]=a.google.protobuf.DescriptorProto.fromObject(e.nestedType[o])}}if(e.enumType){if(!Array.isArray(e.enumType))throw TypeError(".google.protobuf.DescriptorProto.enumType: array expected");t.enumType=[];for(o=0;o<e.enumType.length;++o){if("object"!=typeof e.enumType[o])throw TypeError(".google.protobuf.DescriptorProto.enumType: object expected");t.enumType[o]=a.google.protobuf.EnumDescriptorProto.fromObject(e.enumType[o])}}if(e.extensionRange){if(!Array.isArray(e.extensionRange))throw TypeError(".google.protobuf.DescriptorProto.extensionRange: array expected");t.extensionRange=[];for(o=0;o<e.extensionRange.length;++o){if("object"!=typeof e.extensionRange[o])throw TypeError(".google.protobuf.DescriptorProto.extensionRange: object expected");t.extensionRange[o]=a.google.protobuf.DescriptorProto.ExtensionRange.fromObject(e.extensionRange[o])}}if(e.oneofDecl){if(!Array.isArray(e.oneofDecl))throw TypeError(".google.protobuf.DescriptorProto.oneofDecl: array expected");t.oneofDecl=[];for(o=0;o<e.oneofDecl.length;++o){if("object"!=typeof e.oneofDecl[o])throw TypeError(".google.protobuf.DescriptorProto.oneofDecl: object expected");t.oneofDecl[o]=a.google.protobuf.OneofDescriptorProto.fromObject(e.oneofDecl[o])}}if(null!=e.options){if("object"!=typeof e.options)throw TypeError(".google.protobuf.DescriptorProto.options: object expected");t.options=a.google.protobuf.MessageOptions.fromObject(e.options)}if(e.reservedRange){if(!Array.isArray(e.reservedRange))throw TypeError(".google.protobuf.DescriptorProto.reservedRange: array expected");t.reservedRange=[];for(o=0;o<e.reservedRange.length;++o){if("object"!=typeof e.reservedRange[o])throw TypeError(".google.protobuf.DescriptorProto.reservedRange: object expected");t.reservedRange[o]=a.google.protobuf.DescriptorProto.ReservedRange.fromObject(e.reservedRange[o])}}if(e.reservedName){if(!Array.isArray(e.reservedName))throw TypeError(".google.protobuf.DescriptorProto.reservedName: array expected");t.reservedName=[];for(o=0;o<e.reservedName.length;++o)t.reservedName[o]=String(e.reservedName[o])}return t},u.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.field=[],o.nestedType=[],o.enumType=[],o.extensionRange=[],o.extension=[],o.oneofDecl=[],o.reservedRange=[],o.reservedName=[]),t.defaults&&(o.name="",o.options=null),null!=e.name&&e.hasOwnProperty("name")&&(o.name=e.name),e.field&&e.field.length){o.field=[];for(var r=0;r<e.field.length;++r)o.field[r]=a.google.protobuf.FieldDescriptorProto.toObject(e.field[r],t)}if(e.nestedType&&e.nestedType.length){o.nestedType=[];for(r=0;r<e.nestedType.length;++r)o.nestedType[r]=a.google.protobuf.DescriptorProto.toObject(e.nestedType[r],t)}if(e.enumType&&e.enumType.length){o.enumType=[];for(r=0;r<e.enumType.length;++r)o.enumType[r]=a.google.protobuf.EnumDescriptorProto.toObject(e.enumType[r],t)}if(e.extensionRange&&e.extensionRange.length){o.extensionRange=[];for(r=0;r<e.extensionRange.length;++r)o.extensionRange[r]=a.google.protobuf.DescriptorProto.ExtensionRange.toObject(e.extensionRange[r],t)}if(e.extension&&e.extension.length){o.extension=[];for(r=0;r<e.extension.length;++r)o.extension[r]=a.google.protobuf.FieldDescriptorProto.toObject(e.extension[r],t)}if(null!=e.options&&e.hasOwnProperty("options")&&(o.options=a.google.protobuf.MessageOptions.toObject(e.options,t)),e.oneofDecl&&e.oneofDecl.length){o.oneofDecl=[];for(r=0;r<e.oneofDecl.length;++r)o.oneofDecl[r]=a.google.protobuf.OneofDescriptorProto.toObject(e.oneofDecl[r],t)}if(e.reservedRange&&e.reservedRange.length){o.reservedRange=[];for(r=0;r<e.reservedRange.length;++r)o.reservedRange[r]=a.google.protobuf.DescriptorProto.ReservedRange.toObject(e.reservedRange[r],t)}if(e.reservedName&&e.reservedName.length){o.reservedName=[];for(r=0;r<e.reservedName.length;++r)o.reservedName[r]=e.reservedName[r]}return o},u.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},u.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.DescriptorProto"},u.ExtensionRange=(G.prototype.start=0,G.prototype.end=0,G.prototype.options=null,G.fromObject=function(e){if(e instanceof a.google.protobuf.DescriptorProto.ExtensionRange)return e;var t=new a.google.protobuf.DescriptorProto.ExtensionRange;if(null!=e.start&&(t.start=0|e.start),null!=e.end&&(t.end=0|e.end),null!=e.options){if("object"!=typeof e.options)throw TypeError(".google.protobuf.DescriptorProto.ExtensionRange.options: object expected");t.options=a.google.protobuf.ExtensionRangeOptions.fromObject(e.options)}return t},G.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.start=0,o.end=0,o.options=null),null!=e.start&&e.hasOwnProperty("start")&&(o.start=e.start),null!=e.end&&e.hasOwnProperty("end")&&(o.end=e.end),null!=e.options&&e.hasOwnProperty("options")&&(o.options=a.google.protobuf.ExtensionRangeOptions.toObject(e.options,t)),o},G.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},G.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.DescriptorProto.ExtensionRange"},G),u.ReservedRange=(B.prototype.start=0,B.prototype.end=0,B.fromObject=function(e){var t;return e instanceof a.google.protobuf.DescriptorProto.ReservedRange?e:(t=new a.google.protobuf.DescriptorProto.ReservedRange,null!=e.start&&(t.start=0|e.start),null!=e.end&&(t.end=0|e.end),t)},B.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.start=0,o.end=0),null!=e.start&&e.hasOwnProperty("start")&&(o.start=e.start),null!=e.end&&e.hasOwnProperty("end")&&(o.end=e.end),o},B.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},B.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.DescriptorProto.ReservedRange"},B),u),n.ExtensionRangeOptions=(p.prototype.uninterpretedOption=i.emptyArray,p.prototype.declaration=i.emptyArray,p.prototype.features=null,p.prototype.verification=1,p.fromObject=function(e){if(e instanceof a.google.protobuf.ExtensionRangeOptions)return e;var t=new a.google.protobuf.ExtensionRangeOptions;if(e.uninterpretedOption){if(!Array.isArray(e.uninterpretedOption))throw TypeError(".google.protobuf.ExtensionRangeOptions.uninterpretedOption: array expected");t.uninterpretedOption=[];for(var o=0;o<e.uninterpretedOption.length;++o){if("object"!=typeof e.uninterpretedOption[o])throw TypeError(".google.protobuf.ExtensionRangeOptions.uninterpretedOption: object expected");t.uninterpretedOption[o]=a.google.protobuf.UninterpretedOption.fromObject(e.uninterpretedOption[o])}}if(e.declaration){if(!Array.isArray(e.declaration))throw TypeError(".google.protobuf.ExtensionRangeOptions.declaration: array expected");t.declaration=[];for(o=0;o<e.declaration.length;++o){if("object"!=typeof e.declaration[o])throw TypeError(".google.protobuf.ExtensionRangeOptions.declaration: object expected");t.declaration[o]=a.google.protobuf.ExtensionRangeOptions.Declaration.fromObject(e.declaration[o])}}if(null!=e.features){if("object"!=typeof e.features)throw TypeError(".google.protobuf.ExtensionRangeOptions.features: object expected");t.features=a.google.protobuf.FeatureSet.fromObject(e.features)}switch(e.verification){case"DECLARATION":case 0:t.verification=0;break;default:"number"==typeof e.verification&&(t.verification=e.verification);break;case"UNVERIFIED":case 1:t.verification=1}return t},p.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.declaration=[],o.uninterpretedOption=[]),t.defaults&&(o.verification=t.enums===String?"UNVERIFIED":1,o.features=null),e.declaration&&e.declaration.length){o.declaration=[];for(var r=0;r<e.declaration.length;++r)o.declaration[r]=a.google.protobuf.ExtensionRangeOptions.Declaration.toObject(e.declaration[r],t)}if(null!=e.verification&&e.hasOwnProperty("verification")&&(o.verification=t.enums!==String||void 0===a.google.protobuf.ExtensionRangeOptions.VerificationState[e.verification]?e.verification:a.google.protobuf.ExtensionRangeOptions.VerificationState[e.verification]),null!=e.features&&e.hasOwnProperty("features")&&(o.features=a.google.protobuf.FeatureSet.toObject(e.features,t)),e.uninterpretedOption&&e.uninterpretedOption.length){o.uninterpretedOption=[];for(r=0;r<e.uninterpretedOption.length;++r)o.uninterpretedOption[r]=a.google.protobuf.UninterpretedOption.toObject(e.uninterpretedOption[r],t)}return o},p.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},p.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.ExtensionRangeOptions"},p.Declaration=(M.prototype.number=0,M.prototype.fullName="",M.prototype.type="",M.prototype.reserved=!1,M.prototype.repeated=!1,M.fromObject=function(e){var t;return e instanceof a.google.protobuf.ExtensionRangeOptions.Declaration?e:(t=new a.google.protobuf.ExtensionRangeOptions.Declaration,null!=e.number&&(t.number=0|e.number),null!=e.fullName&&(t.fullName=String(e.fullName)),null!=e.type&&(t.type=String(e.type)),null!=e.reserved&&(t.reserved=Boolean(e.reserved)),null!=e.repeated&&(t.repeated=Boolean(e.repeated)),t)},M.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.number=0,o.fullName="",o.type="",o.reserved=!1,o.repeated=!1),null!=e.number&&e.hasOwnProperty("number")&&(o.number=e.number),null!=e.fullName&&e.hasOwnProperty("fullName")&&(o.fullName=e.fullName),null!=e.type&&e.hasOwnProperty("type")&&(o.type=e.type),null!=e.reserved&&e.hasOwnProperty("reserved")&&(o.reserved=e.reserved),null!=e.repeated&&e.hasOwnProperty("repeated")&&(o.repeated=e.repeated),o},M.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},M.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.ExtensionRangeOptions.Declaration"},M),p.VerificationState=(e={},(o=Object.create(e))[e[0]="DECLARATION"]="DECLARATION",o[e[1]="UNVERIFIED"]="UNVERIFIED",o),p),n.FieldDescriptorProto=(c.prototype.name="",c.prototype.number=0,c.prototype.label=1,c.prototype.type=1,c.prototype.typeName="",c.prototype.extendee="",c.prototype.defaultValue="",c.prototype.oneofIndex=0,c.prototype.jsonName="",c.prototype.options=null,c.prototype.proto3Optional=!1,c.fromObject=function(e){if(e instanceof a.google.protobuf.FieldDescriptorProto)return e;var t=new a.google.protobuf.FieldDescriptorProto;switch(null!=e.name&&(t.name=String(e.name)),null!=e.number&&(t.number=0|e.number),e.label){default:"number"==typeof e.label&&(t.label=e.label);break;case"LABEL_OPTIONAL":case 1:t.label=1;break;case"LABEL_REPEATED":case 3:t.label=3;break;case"LABEL_REQUIRED":case 2:t.label=2}switch(e.type){default:"number"==typeof e.type&&(t.type=e.type);break;case"TYPE_DOUBLE":case 1:t.type=1;break;case"TYPE_FLOAT":case 2:t.type=2;break;case"TYPE_INT64":case 3:t.type=3;break;case"TYPE_UINT64":case 4:t.type=4;break;case"TYPE_INT32":case 5:t.type=5;break;case"TYPE_FIXED64":case 6:t.type=6;break;case"TYPE_FIXED32":case 7:t.type=7;break;case"TYPE_BOOL":case 8:t.type=8;break;case"TYPE_STRING":case 9:t.type=9;break;case"TYPE_GROUP":case 10:t.type=10;break;case"TYPE_MESSAGE":case 11:t.type=11;break;case"TYPE_BYTES":case 12:t.type=12;break;case"TYPE_UINT32":case 13:t.type=13;break;case"TYPE_ENUM":case 14:t.type=14;break;case"TYPE_SFIXED32":case 15:t.type=15;break;case"TYPE_SFIXED64":case 16:t.type=16;break;case"TYPE_SINT32":case 17:t.type=17;break;case"TYPE_SINT64":case 18:t.type=18}if(null!=e.typeName&&(t.typeName=String(e.typeName)),null!=e.extendee&&(t.extendee=String(e.extendee)),null!=e.defaultValue&&(t.defaultValue=String(e.defaultValue)),null!=e.oneofIndex&&(t.oneofIndex=0|e.oneofIndex),null!=e.jsonName&&(t.jsonName=String(e.jsonName)),null!=e.options){if("object"!=typeof e.options)throw TypeError(".google.protobuf.FieldDescriptorProto.options: object expected");t.options=a.google.protobuf.FieldOptions.fromObject(e.options)}return null!=e.proto3Optional&&(t.proto3Optional=Boolean(e.proto3Optional)),t},c.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.name="",o.extendee="",o.number=0,o.label=t.enums===String?"LABEL_OPTIONAL":1,o.type=t.enums===String?"TYPE_DOUBLE":1,o.typeName="",o.defaultValue="",o.options=null,o.oneofIndex=0,o.jsonName="",o.proto3Optional=!1),null!=e.name&&e.hasOwnProperty("name")&&(o.name=e.name),null!=e.extendee&&e.hasOwnProperty("extendee")&&(o.extendee=e.extendee),null!=e.number&&e.hasOwnProperty("number")&&(o.number=e.number),null!=e.label&&e.hasOwnProperty("label")&&(o.label=t.enums!==String||void 0===a.google.protobuf.FieldDescriptorProto.Label[e.label]?e.label:a.google.protobuf.FieldDescriptorProto.Label[e.label]),null!=e.type&&e.hasOwnProperty("type")&&(o.type=t.enums!==String||void 0===a.google.protobuf.FieldDescriptorProto.Type[e.type]?e.type:a.google.protobuf.FieldDescriptorProto.Type[e.type]),null!=e.typeName&&e.hasOwnProperty("typeName")&&(o.typeName=e.typeName),null!=e.defaultValue&&e.hasOwnProperty("defaultValue")&&(o.defaultValue=e.defaultValue),null!=e.options&&e.hasOwnProperty("options")&&(o.options=a.google.protobuf.FieldOptions.toObject(e.options,t)),null!=e.oneofIndex&&e.hasOwnProperty("oneofIndex")&&(o.oneofIndex=e.oneofIndex),null!=e.jsonName&&e.hasOwnProperty("jsonName")&&(o.jsonName=e.jsonName),null!=e.proto3Optional&&e.hasOwnProperty("proto3Optional")&&(o.proto3Optional=e.proto3Optional),o},c.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},c.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.FieldDescriptorProto"},c.Type=(e={},(o=Object.create(e))[e[1]="TYPE_DOUBLE"]="TYPE_DOUBLE",o[e[2]="TYPE_FLOAT"]="TYPE_FLOAT",o[e[3]="TYPE_INT64"]="TYPE_INT64",o[e[4]="TYPE_UINT64"]="TYPE_UINT64",o[e[5]="TYPE_INT32"]="TYPE_INT32",o[e[6]="TYPE_FIXED64"]="TYPE_FIXED64",o[e[7]="TYPE_FIXED32"]="TYPE_FIXED32",o[e[8]="TYPE_BOOL"]="TYPE_BOOL",o[e[9]="TYPE_STRING"]="TYPE_STRING",o[e[10]="TYPE_GROUP"]="TYPE_GROUP",o[e[11]="TYPE_MESSAGE"]="TYPE_MESSAGE",o[e[12]="TYPE_BYTES"]="TYPE_BYTES",o[e[13]="TYPE_UINT32"]="TYPE_UINT32",o[e[14]="TYPE_ENUM"]="TYPE_ENUM",o[e[15]="TYPE_SFIXED32"]="TYPE_SFIXED32",o[e[16]="TYPE_SFIXED64"]="TYPE_SFIXED64",o[e[17]="TYPE_SINT32"]="TYPE_SINT32",o[e[18]="TYPE_SINT64"]="TYPE_SINT64",o),c.Label=(e={},(o=Object.create(e))[e[1]="LABEL_OPTIONAL"]="LABEL_OPTIONAL",o[e[3]="LABEL_REPEATED"]="LABEL_REPEATED",o[e[2]="LABEL_REQUIRED"]="LABEL_REQUIRED",o),c),n.OneofDescriptorProto=(Y.prototype.name="",Y.prototype.options=null,Y.fromObject=function(e){if(e instanceof a.google.protobuf.OneofDescriptorProto)return e;var t=new a.google.protobuf.OneofDescriptorProto;if(null!=e.name&&(t.name=String(e.name)),null!=e.options){if("object"!=typeof e.options)throw TypeError(".google.protobuf.OneofDescriptorProto.options: object expected");t.options=a.google.protobuf.OneofOptions.fromObject(e.options)}return t},Y.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.name="",o.options=null),null!=e.name&&e.hasOwnProperty("name")&&(o.name=e.name),null!=e.options&&e.hasOwnProperty("options")&&(o.options=a.google.protobuf.OneofOptions.toObject(e.options,t)),o},Y.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},Y.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.OneofDescriptorProto"},Y),n.EnumDescriptorProto=(g.prototype.name="",g.prototype.value=i.emptyArray,g.prototype.options=null,g.prototype.reservedRange=i.emptyArray,g.prototype.reservedName=i.emptyArray,g.fromObject=function(e){if(e instanceof a.google.protobuf.EnumDescriptorProto)return e;var t=new a.google.protobuf.EnumDescriptorProto;if(null!=e.name&&(t.name=String(e.name)),e.value){if(!Array.isArray(e.value))throw TypeError(".google.protobuf.EnumDescriptorProto.value: array expected");t.value=[];for(var o=0;o<e.value.length;++o){if("object"!=typeof e.value[o])throw TypeError(".google.protobuf.EnumDescriptorProto.value: object expected");t.value[o]=a.google.protobuf.EnumValueDescriptorProto.fromObject(e.value[o])}}if(null!=e.options){if("object"!=typeof e.options)throw TypeError(".google.protobuf.EnumDescriptorProto.options: object expected");t.options=a.google.protobuf.EnumOptions.fromObject(e.options)}if(e.reservedRange){if(!Array.isArray(e.reservedRange))throw TypeError(".google.protobuf.EnumDescriptorProto.reservedRange: array expected");t.reservedRange=[];for(o=0;o<e.reservedRange.length;++o){if("object"!=typeof e.reservedRange[o])throw TypeError(".google.protobuf.EnumDescriptorProto.reservedRange: object expected");t.reservedRange[o]=a.google.protobuf.EnumDescriptorProto.EnumReservedRange.fromObject(e.reservedRange[o])}}if(e.reservedName){if(!Array.isArray(e.reservedName))throw TypeError(".google.protobuf.EnumDescriptorProto.reservedName: array expected");t.reservedName=[];for(o=0;o<e.reservedName.length;++o)t.reservedName[o]=String(e.reservedName[o])}return t},g.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.value=[],o.reservedRange=[],o.reservedName=[]),t.defaults&&(o.name="",o.options=null),null!=e.name&&e.hasOwnProperty("name")&&(o.name=e.name),e.value&&e.value.length){o.value=[];for(var r=0;r<e.value.length;++r)o.value[r]=a.google.protobuf.EnumValueDescriptorProto.toObject(e.value[r],t)}if(null!=e.options&&e.hasOwnProperty("options")&&(o.options=a.google.protobuf.EnumOptions.toObject(e.options,t)),e.reservedRange&&e.reservedRange.length){o.reservedRange=[];for(r=0;r<e.reservedRange.length;++r)o.reservedRange[r]=a.google.protobuf.EnumDescriptorProto.EnumReservedRange.toObject(e.reservedRange[r],t)}if(e.reservedName&&e.reservedName.length){o.reservedName=[];for(r=0;r<e.reservedName.length;++r)o.reservedName[r]=e.reservedName[r]}return o},g.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},g.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.EnumDescriptorProto"},g.EnumReservedRange=(Q.prototype.start=0,Q.prototype.end=0,Q.fromObject=function(e){var t;return e instanceof a.google.protobuf.EnumDescriptorProto.EnumReservedRange?e:(t=new a.google.protobuf.EnumDescriptorProto.EnumReservedRange,null!=e.start&&(t.start=0|e.start),null!=e.end&&(t.end=0|e.end),t)},Q.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.start=0,o.end=0),null!=e.start&&e.hasOwnProperty("start")&&(o.start=e.start),null!=e.end&&e.hasOwnProperty("end")&&(o.end=e.end),o},Q.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},Q.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.EnumDescriptorProto.EnumReservedRange"},Q),g),n.EnumValueDescriptorProto=(q.prototype.name="",q.prototype.number=0,q.prototype.options=null,q.fromObject=function(e){if(e instanceof a.google.protobuf.EnumValueDescriptorProto)return e;var t=new a.google.protobuf.EnumValueDescriptorProto;if(null!=e.name&&(t.name=String(e.name)),null!=e.number&&(t.number=0|e.number),null!=e.options){if("object"!=typeof e.options)throw TypeError(".google.protobuf.EnumValueDescriptorProto.options: object expected");t.options=a.google.protobuf.EnumValueOptions.fromObject(e.options)}return t},q.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.name="",o.number=0,o.options=null),null!=e.name&&e.hasOwnProperty("name")&&(o.name=e.name),null!=e.number&&e.hasOwnProperty("number")&&(o.number=e.number),null!=e.options&&e.hasOwnProperty("options")&&(o.options=a.google.protobuf.EnumValueOptions.toObject(e.options,t)),o},q.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},q.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.EnumValueDescriptorProto"},q),n.ServiceDescriptorProto=(W.prototype.name="",W.prototype.method=i.emptyArray,W.prototype.options=null,W.fromObject=function(e){if(e instanceof a.google.protobuf.ServiceDescriptorProto)return e;var t=new a.google.protobuf.ServiceDescriptorProto;if(null!=e.name&&(t.name=String(e.name)),e.method){if(!Array.isArray(e.method))throw TypeError(".google.protobuf.ServiceDescriptorProto.method: array expected");t.method=[];for(var o=0;o<e.method.length;++o){if("object"!=typeof e.method[o])throw TypeError(".google.protobuf.ServiceDescriptorProto.method: object expected");t.method[o]=a.google.protobuf.MethodDescriptorProto.fromObject(e.method[o])}}if(null!=e.options){if("object"!=typeof e.options)throw TypeError(".google.protobuf.ServiceDescriptorProto.options: object expected");t.options=a.google.protobuf.ServiceOptions.fromObject(e.options)}return t},W.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.method=[]),t.defaults&&(o.name="",o.options=null),null!=e.name&&e.hasOwnProperty("name")&&(o.name=e.name),e.method&&e.method.length){o.method=[];for(var r=0;r<e.method.length;++r)o.method[r]=a.google.protobuf.MethodDescriptorProto.toObject(e.method[r],t)}return null!=e.options&&e.hasOwnProperty("options")&&(o.options=a.google.protobuf.ServiceOptions.toObject(e.options,t)),o},W.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},W.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.ServiceDescriptorProto"},W),n.MethodDescriptorProto=(f.prototype.name="",f.prototype.inputType="",f.prototype.outputType="",f.prototype.options=null,f.prototype.clientStreaming=!1,f.prototype.serverStreaming=!1,f.fromObject=function(e){if(e instanceof a.google.protobuf.MethodDescriptorProto)return e;var t=new a.google.protobuf.MethodDescriptorProto;if(null!=e.name&&(t.name=String(e.name)),null!=e.inputType&&(t.inputType=String(e.inputType)),null!=e.outputType&&(t.outputType=String(e.outputType)),null!=e.options){if("object"!=typeof e.options)throw TypeError(".google.protobuf.MethodDescriptorProto.options: object expected");t.options=a.google.protobuf.MethodOptions.fromObject(e.options)}return null!=e.clientStreaming&&(t.clientStreaming=Boolean(e.clientStreaming)),null!=e.serverStreaming&&(t.serverStreaming=Boolean(e.serverStreaming)),t},f.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.name="",o.inputType="",o.outputType="",o.options=null,o.clientStreaming=!1,o.serverStreaming=!1),null!=e.name&&e.hasOwnProperty("name")&&(o.name=e.name),null!=e.inputType&&e.hasOwnProperty("inputType")&&(o.inputType=e.inputType),null!=e.outputType&&e.hasOwnProperty("outputType")&&(o.outputType=e.outputType),null!=e.options&&e.hasOwnProperty("options")&&(o.options=a.google.protobuf.MethodOptions.toObject(e.options,t)),null!=e.clientStreaming&&e.hasOwnProperty("clientStreaming")&&(o.clientStreaming=e.clientStreaming),null!=e.serverStreaming&&e.hasOwnProperty("serverStreaming")&&(o.serverStreaming=e.serverStreaming),o},f.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},f.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.MethodDescriptorProto"},f),n.FileOptions=(d.prototype.javaPackage="",d.prototype.javaOuterClassname="",d.prototype.javaMultipleFiles=!1,d.prototype.javaGenerateEqualsAndHash=!1,d.prototype.javaStringCheckUtf8=!1,d.prototype.optimizeFor=1,d.prototype.goPackage="",d.prototype.ccGenericServices=!1,d.prototype.javaGenericServices=!1,d.prototype.pyGenericServices=!1,d.prototype.deprecated=!1,d.prototype.ccEnableArenas=!0,d.prototype.objcClassPrefix="",d.prototype.csharpNamespace="",d.prototype.swiftPrefix="",d.prototype.phpClassPrefix="",d.prototype.phpNamespace="",d.prototype.phpMetadataNamespace="",d.prototype.rubyPackage="",d.prototype.features=null,d.prototype.uninterpretedOption=i.emptyArray,d.prototype[".google.api.resourceDefinition"]=i.emptyArray,d.fromObject=function(e){if(e instanceof a.google.protobuf.FileOptions)return e;var t=new a.google.protobuf.FileOptions;switch(null!=e.javaPackage&&(t.javaPackage=String(e.javaPackage)),null!=e.javaOuterClassname&&(t.javaOuterClassname=String(e.javaOuterClassname)),null!=e.javaMultipleFiles&&(t.javaMultipleFiles=Boolean(e.javaMultipleFiles)),null!=e.javaGenerateEqualsAndHash&&(t.javaGenerateEqualsAndHash=Boolean(e.javaGenerateEqualsAndHash)),null!=e.javaStringCheckUtf8&&(t.javaStringCheckUtf8=Boolean(e.javaStringCheckUtf8)),e.optimizeFor){default:"number"==typeof e.optimizeFor&&(t.optimizeFor=e.optimizeFor);break;case"SPEED":case 1:t.optimizeFor=1;break;case"CODE_SIZE":case 2:t.optimizeFor=2;break;case"LITE_RUNTIME":case 3:t.optimizeFor=3}if(null!=e.goPackage&&(t.goPackage=String(e.goPackage)),null!=e.ccGenericServices&&(t.ccGenericServices=Boolean(e.ccGenericServices)),null!=e.javaGenericServices&&(t.javaGenericServices=Boolean(e.javaGenericServices)),null!=e.pyGenericServices&&(t.pyGenericServices=Boolean(e.pyGenericServices)),null!=e.deprecated&&(t.deprecated=Boolean(e.deprecated)),null!=e.ccEnableArenas&&(t.ccEnableArenas=Boolean(e.ccEnableArenas)),null!=e.objcClassPrefix&&(t.objcClassPrefix=String(e.objcClassPrefix)),null!=e.csharpNamespace&&(t.csharpNamespace=String(e.csharpNamespace)),null!=e.swiftPrefix&&(t.swiftPrefix=String(e.swiftPrefix)),null!=e.phpClassPrefix&&(t.phpClassPrefix=String(e.phpClassPrefix)),null!=e.phpNamespace&&(t.phpNamespace=String(e.phpNamespace)),null!=e.phpMetadataNamespace&&(t.phpMetadataNamespace=String(e.phpMetadataNamespace)),null!=e.rubyPackage&&(t.rubyPackage=String(e.rubyPackage)),null!=e.features){if("object"!=typeof e.features)throw TypeError(".google.protobuf.FileOptions.features: object expected");t.features=a.google.protobuf.FeatureSet.fromObject(e.features)}if(e.uninterpretedOption){if(!Array.isArray(e.uninterpretedOption))throw TypeError(".google.protobuf.FileOptions.uninterpretedOption: array expected");t.uninterpretedOption=[];for(var o=0;o<e.uninterpretedOption.length;++o){if("object"!=typeof e.uninterpretedOption[o])throw TypeError(".google.protobuf.FileOptions.uninterpretedOption: object expected");t.uninterpretedOption[o]=a.google.protobuf.UninterpretedOption.fromObject(e.uninterpretedOption[o])}}if(e[".google.api.resourceDefinition"]){if(!Array.isArray(e[".google.api.resourceDefinition"]))throw TypeError(".google.protobuf.FileOptions..google.api.resourceDefinition: array expected");t[".google.api.resourceDefinition"]=[];for(o=0;o<e[".google.api.resourceDefinition"].length;++o){if("object"!=typeof e[".google.api.resourceDefinition"][o])throw TypeError(".google.protobuf.FileOptions..google.api.resourceDefinition: object expected");t[".google.api.resourceDefinition"][o]=a.google.api.ResourceDescriptor.fromObject(e[".google.api.resourceDefinition"][o])}}return t},d.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.uninterpretedOption=[],o[".google.api.resourceDefinition"]=[]),t.defaults&&(o.javaPackage="",o.javaOuterClassname="",o.optimizeFor=t.enums===String?"SPEED":1,o.javaMultipleFiles=!1,o.goPackage="",o.ccGenericServices=!1,o.javaGenericServices=!1,o.pyGenericServices=!1,o.javaGenerateEqualsAndHash=!1,o.deprecated=!1,o.javaStringCheckUtf8=!1,o.ccEnableArenas=!0,o.objcClassPrefix="",o.csharpNamespace="",o.swiftPrefix="",o.phpClassPrefix="",o.phpNamespace="",o.phpMetadataNamespace="",o.rubyPackage="",o.features=null),null!=e.javaPackage&&e.hasOwnProperty("javaPackage")&&(o.javaPackage=e.javaPackage),null!=e.javaOuterClassname&&e.hasOwnProperty("javaOuterClassname")&&(o.javaOuterClassname=e.javaOuterClassname),null!=e.optimizeFor&&e.hasOwnProperty("optimizeFor")&&(o.optimizeFor=t.enums!==String||void 0===a.google.protobuf.FileOptions.OptimizeMode[e.optimizeFor]?e.optimizeFor:a.google.protobuf.FileOptions.OptimizeMode[e.optimizeFor]),null!=e.javaMultipleFiles&&e.hasOwnProperty("javaMultipleFiles")&&(o.javaMultipleFiles=e.javaMultipleFiles),null!=e.goPackage&&e.hasOwnProperty("goPackage")&&(o.goPackage=e.goPackage),null!=e.ccGenericServices&&e.hasOwnProperty("ccGenericServices")&&(o.ccGenericServices=e.ccGenericServices),null!=e.javaGenericServices&&e.hasOwnProperty("javaGenericServices")&&(o.javaGenericServices=e.javaGenericServices),null!=e.pyGenericServices&&e.hasOwnProperty("pyGenericServices")&&(o.pyGenericServices=e.pyGenericServices),null!=e.javaGenerateEqualsAndHash&&e.hasOwnProperty("javaGenerateEqualsAndHash")&&(o.javaGenerateEqualsAndHash=e.javaGenerateEqualsAndHash),null!=e.deprecated&&e.hasOwnProperty("deprecated")&&(o.deprecated=e.deprecated),null!=e.javaStringCheckUtf8&&e.hasOwnProperty("javaStringCheckUtf8")&&(o.javaStringCheckUtf8=e.javaStringCheckUtf8),null!=e.ccEnableArenas&&e.hasOwnProperty("ccEnableArenas")&&(o.ccEnableArenas=e.ccEnableArenas),null!=e.objcClassPrefix&&e.hasOwnProperty("objcClassPrefix")&&(o.objcClassPrefix=e.objcClassPrefix),null!=e.csharpNamespace&&e.hasOwnProperty("csharpNamespace")&&(o.csharpNamespace=e.csharpNamespace),null!=e.swiftPrefix&&e.hasOwnProperty("swiftPrefix")&&(o.swiftPrefix=e.swiftPrefix),null!=e.phpClassPrefix&&e.hasOwnProperty("phpClassPrefix")&&(o.phpClassPrefix=e.phpClassPrefix),null!=e.phpNamespace&&e.hasOwnProperty("phpNamespace")&&(o.phpNamespace=e.phpNamespace),null!=e.phpMetadataNamespace&&e.hasOwnProperty("phpMetadataNamespace")&&(o.phpMetadataNamespace=e.phpMetadataNamespace),null!=e.rubyPackage&&e.hasOwnProperty("rubyPackage")&&(o.rubyPackage=e.rubyPackage),null!=e.features&&e.hasOwnProperty("features")&&(o.features=a.google.protobuf.FeatureSet.toObject(e.features,t)),e.uninterpretedOption&&e.uninterpretedOption.length){o.uninterpretedOption=[];for(var r=0;r<e.uninterpretedOption.length;++r)o.uninterpretedOption[r]=a.google.protobuf.UninterpretedOption.toObject(e.uninterpretedOption[r],t)}if(e[".google.api.resourceDefinition"]&&e[".google.api.resourceDefinition"].length){o[".google.api.resourceDefinition"]=[];for(r=0;r<e[".google.api.resourceDefinition"].length;++r)o[".google.api.resourceDefinition"][r]=a.google.api.ResourceDescriptor.toObject(e[".google.api.resourceDefinition"][r],t)}return o},d.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},d.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.FileOptions"},d.OptimizeMode=(e={},(o=Object.create(e))[e[1]="SPEED"]="SPEED",o[e[2]="CODE_SIZE"]="CODE_SIZE",o[e[3]="LITE_RUNTIME"]="LITE_RUNTIME",o),d),n.MessageOptions=(y.prototype.messageSetWireFormat=!1,y.prototype.noStandardDescriptorAccessor=!1,y.prototype.deprecated=!1,y.prototype.mapEntry=!1,y.prototype.deprecatedLegacyJsonFieldConflicts=!1,y.prototype.features=null,y.prototype.uninterpretedOption=i.emptyArray,y.prototype[".google.api.resource"]=null,y.fromObject=function(e){if(e instanceof a.google.protobuf.MessageOptions)return e;var t=new a.google.protobuf.MessageOptions;if(null!=e.messageSetWireFormat&&(t.messageSetWireFormat=Boolean(e.messageSetWireFormat)),null!=e.noStandardDescriptorAccessor&&(t.noStandardDescriptorAccessor=Boolean(e.noStandardDescriptorAccessor)),null!=e.deprecated&&(t.deprecated=Boolean(e.deprecated)),null!=e.mapEntry&&(t.mapEntry=Boolean(e.mapEntry)),null!=e.deprecatedLegacyJsonFieldConflicts&&(t.deprecatedLegacyJsonFieldConflicts=Boolean(e.deprecatedLegacyJsonFieldConflicts)),null!=e.features){if("object"!=typeof e.features)throw TypeError(".google.protobuf.MessageOptions.features: object expected");t.features=a.google.protobuf.FeatureSet.fromObject(e.features)}if(e.uninterpretedOption){if(!Array.isArray(e.uninterpretedOption))throw TypeError(".google.protobuf.MessageOptions.uninterpretedOption: array expected");t.uninterpretedOption=[];for(var o=0;o<e.uninterpretedOption.length;++o){if("object"!=typeof e.uninterpretedOption[o])throw TypeError(".google.protobuf.MessageOptions.uninterpretedOption: object expected");t.uninterpretedOption[o]=a.google.protobuf.UninterpretedOption.fromObject(e.uninterpretedOption[o])}}if(null!=e[".google.api.resource"]){if("object"!=typeof e[".google.api.resource"])throw TypeError(".google.protobuf.MessageOptions..google.api.resource: object expected");t[".google.api.resource"]=a.google.api.ResourceDescriptor.fromObject(e[".google.api.resource"])}return t},y.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.uninterpretedOption=[]),t.defaults&&(o.messageSetWireFormat=!1,o.noStandardDescriptorAccessor=!1,o.deprecated=!1,o.mapEntry=!1,o.deprecatedLegacyJsonFieldConflicts=!1,o.features=null,o[".google.api.resource"]=null),null!=e.messageSetWireFormat&&e.hasOwnProperty("messageSetWireFormat")&&(o.messageSetWireFormat=e.messageSetWireFormat),null!=e.noStandardDescriptorAccessor&&e.hasOwnProperty("noStandardDescriptorAccessor")&&(o.noStandardDescriptorAccessor=e.noStandardDescriptorAccessor),null!=e.deprecated&&e.hasOwnProperty("deprecated")&&(o.deprecated=e.deprecated),null!=e.mapEntry&&e.hasOwnProperty("mapEntry")&&(o.mapEntry=e.mapEntry),null!=e.deprecatedLegacyJsonFieldConflicts&&e.hasOwnProperty("deprecatedLegacyJsonFieldConflicts")&&(o.deprecatedLegacyJsonFieldConflicts=e.deprecatedLegacyJsonFieldConflicts),null!=e.features&&e.hasOwnProperty("features")&&(o.features=a.google.protobuf.FeatureSet.toObject(e.features,t)),e.uninterpretedOption&&e.uninterpretedOption.length){o.uninterpretedOption=[];for(var r=0;r<e.uninterpretedOption.length;++r)o.uninterpretedOption[r]=a.google.protobuf.UninterpretedOption.toObject(e.uninterpretedOption[r],t)}return null!=e[".google.api.resource"]&&e.hasOwnProperty(".google.api.resource")&&(o[".google.api.resource"]=a.google.api.ResourceDescriptor.toObject(e[".google.api.resource"],t)),o},y.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},y.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.MessageOptions"},y),n.FieldOptions=(m.prototype.ctype=0,m.prototype.packed=!1,m.prototype.jstype=0,m.prototype.lazy=!1,m.prototype.unverifiedLazy=!1,m.prototype.deprecated=!1,m.prototype.weak=!1,m.prototype.debugRedact=!1,m.prototype.retention=0,m.prototype.targets=i.emptyArray,m.prototype.editionDefaults=i.emptyArray,m.prototype.features=null,m.prototype.uninterpretedOption=i.emptyArray,m.prototype[".google.api.fieldBehavior"]=i.emptyArray,m.prototype[".google.api.resourceReference"]=null,m.fromObject=function(e){if(e instanceof a.google.protobuf.FieldOptions)return e;var t=new a.google.protobuf.FieldOptions;switch(e.ctype){default:"number"==typeof e.ctype&&(t.ctype=e.ctype);break;case"STRING":case 0:t.ctype=0;break;case"CORD":case 1:t.ctype=1;break;case"STRING_PIECE":case 2:t.ctype=2}switch(null!=e.packed&&(t.packed=Boolean(e.packed)),e.jstype){default:"number"==typeof e.jstype&&(t.jstype=e.jstype);break;case"JS_NORMAL":case 0:t.jstype=0;break;case"JS_STRING":case 1:t.jstype=1;break;case"JS_NUMBER":case 2:t.jstype=2}switch(null!=e.lazy&&(t.lazy=Boolean(e.lazy)),null!=e.unverifiedLazy&&(t.unverifiedLazy=Boolean(e.unverifiedLazy)),null!=e.deprecated&&(t.deprecated=Boolean(e.deprecated)),null!=e.weak&&(t.weak=Boolean(e.weak)),null!=e.debugRedact&&(t.debugRedact=Boolean(e.debugRedact)),e.retention){default:"number"==typeof e.retention&&(t.retention=e.retention);break;case"RETENTION_UNKNOWN":case 0:t.retention=0;break;case"RETENTION_RUNTIME":case 1:t.retention=1;break;case"RETENTION_SOURCE":case 2:t.retention=2}if(e.targets){if(!Array.isArray(e.targets))throw TypeError(".google.protobuf.FieldOptions.targets: array expected");t.targets=[];for(var o=0;o<e.targets.length;++o)switch(e.targets[o]){default:if("number"==typeof e.targets[o]){t.targets[o]=e.targets[o];break}case"TARGET_TYPE_UNKNOWN":case 0:t.targets[o]=0;break;case"TARGET_TYPE_FILE":case 1:t.targets[o]=1;break;case"TARGET_TYPE_EXTENSION_RANGE":case 2:t.targets[o]=2;break;case"TARGET_TYPE_MESSAGE":case 3:t.targets[o]=3;break;case"TARGET_TYPE_FIELD":case 4:t.targets[o]=4;break;case"TARGET_TYPE_ONEOF":case 5:t.targets[o]=5;break;case"TARGET_TYPE_ENUM":case 6:t.targets[o]=6;break;case"TARGET_TYPE_ENUM_ENTRY":case 7:t.targets[o]=7;break;case"TARGET_TYPE_SERVICE":case 8:t.targets[o]=8;break;case"TARGET_TYPE_METHOD":case 9:t.targets[o]=9}}if(e.editionDefaults){if(!Array.isArray(e.editionDefaults))throw TypeError(".google.protobuf.FieldOptions.editionDefaults: array expected");t.editionDefaults=[];for(o=0;o<e.editionDefaults.length;++o){if("object"!=typeof e.editionDefaults[o])throw TypeError(".google.protobuf.FieldOptions.editionDefaults: object expected");t.editionDefaults[o]=a.google.protobuf.FieldOptions.EditionDefault.fromObject(e.editionDefaults[o])}}if(null!=e.features){if("object"!=typeof e.features)throw TypeError(".google.protobuf.FieldOptions.features: object expected");t.features=a.google.protobuf.FeatureSet.fromObject(e.features)}if(e.uninterpretedOption){if(!Array.isArray(e.uninterpretedOption))throw TypeError(".google.protobuf.FieldOptions.uninterpretedOption: array expected");t.uninterpretedOption=[];for(o=0;o<e.uninterpretedOption.length;++o){if("object"!=typeof e.uninterpretedOption[o])throw TypeError(".google.protobuf.FieldOptions.uninterpretedOption: object expected");t.uninterpretedOption[o]=a.google.protobuf.UninterpretedOption.fromObject(e.uninterpretedOption[o])}}if(e[".google.api.fieldBehavior"]){if(!Array.isArray(e[".google.api.fieldBehavior"]))throw TypeError(".google.protobuf.FieldOptions..google.api.fieldBehavior: array expected");t[".google.api.fieldBehavior"]=[];for(o=0;o<e[".google.api.fieldBehavior"].length;++o)switch(e[".google.api.fieldBehavior"][o]){default:if("number"==typeof e[".google.api.fieldBehavior"][o]){t[".google.api.fieldBehavior"][o]=e[".google.api.fieldBehavior"][o];break}case"FIELD_BEHAVIOR_UNSPECIFIED":case 0:t[".google.api.fieldBehavior"][o]=0;break;case"OPTIONAL":case 1:t[".google.api.fieldBehavior"][o]=1;break;case"REQUIRED":case 2:t[".google.api.fieldBehavior"][o]=2;break;case"OUTPUT_ONLY":case 3:t[".google.api.fieldBehavior"][o]=3;break;case"INPUT_ONLY":case 4:t[".google.api.fieldBehavior"][o]=4;break;case"IMMUTABLE":case 5:t[".google.api.fieldBehavior"][o]=5;break;case"UNORDERED_LIST":case 6:t[".google.api.fieldBehavior"][o]=6;break;case"NON_EMPTY_DEFAULT":case 7:t[".google.api.fieldBehavior"][o]=7;break;case"IDENTIFIER":case 8:t[".google.api.fieldBehavior"][o]=8}}if(null!=e[".google.api.resourceReference"]){if("object"!=typeof e[".google.api.resourceReference"])throw TypeError(".google.protobuf.FieldOptions..google.api.resourceReference: object expected");t[".google.api.resourceReference"]=a.google.api.ResourceReference.fromObject(e[".google.api.resourceReference"])}return t},m.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.targets=[],o.editionDefaults=[],o.uninterpretedOption=[],o[".google.api.fieldBehavior"]=[]),t.defaults&&(o.ctype=t.enums===String?"STRING":0,o.packed=!1,o.deprecated=!1,o.lazy=!1,o.jstype=t.enums===String?"JS_NORMAL":0,o.weak=!1,o.unverifiedLazy=!1,o.debugRedact=!1,o.retention=t.enums===String?"RETENTION_UNKNOWN":0,o.features=null,o[".google.api.resourceReference"]=null),null!=e.ctype&&e.hasOwnProperty("ctype")&&(o.ctype=t.enums!==String||void 0===a.google.protobuf.FieldOptions.CType[e.ctype]?e.ctype:a.google.protobuf.FieldOptions.CType[e.ctype]),null!=e.packed&&e.hasOwnProperty("packed")&&(o.packed=e.packed),null!=e.deprecated&&e.hasOwnProperty("deprecated")&&(o.deprecated=e.deprecated),null!=e.lazy&&e.hasOwnProperty("lazy")&&(o.lazy=e.lazy),null!=e.jstype&&e.hasOwnProperty("jstype")&&(o.jstype=t.enums!==String||void 0===a.google.protobuf.FieldOptions.JSType[e.jstype]?e.jstype:a.google.protobuf.FieldOptions.JSType[e.jstype]),null!=e.weak&&e.hasOwnProperty("weak")&&(o.weak=e.weak),null!=e.unverifiedLazy&&e.hasOwnProperty("unverifiedLazy")&&(o.unverifiedLazy=e.unverifiedLazy),null!=e.debugRedact&&e.hasOwnProperty("debugRedact")&&(o.debugRedact=e.debugRedact),null!=e.retention&&e.hasOwnProperty("retention")&&(o.retention=t.enums!==String||void 0===a.google.protobuf.FieldOptions.OptionRetention[e.retention]?e.retention:a.google.protobuf.FieldOptions.OptionRetention[e.retention]),e.targets&&e.targets.length){o.targets=[];for(var r=0;r<e.targets.length;++r)o.targets[r]=t.enums!==String||void 0===a.google.protobuf.FieldOptions.OptionTargetType[e.targets[r]]?e.targets[r]:a.google.protobuf.FieldOptions.OptionTargetType[e.targets[r]]}if(e.editionDefaults&&e.editionDefaults.length){o.editionDefaults=[];for(r=0;r<e.editionDefaults.length;++r)o.editionDefaults[r]=a.google.protobuf.FieldOptions.EditionDefault.toObject(e.editionDefaults[r],t)}if(null!=e.features&&e.hasOwnProperty("features")&&(o.features=a.google.protobuf.FeatureSet.toObject(e.features,t)),e.uninterpretedOption&&e.uninterpretedOption.length){o.uninterpretedOption=[];for(r=0;r<e.uninterpretedOption.length;++r)o.uninterpretedOption[r]=a.google.protobuf.UninterpretedOption.toObject(e.uninterpretedOption[r],t)}if(e[".google.api.fieldBehavior"]&&e[".google.api.fieldBehavior"].length){o[".google.api.fieldBehavior"]=[];for(r=0;r<e[".google.api.fieldBehavior"].length;++r)o[".google.api.fieldBehavior"][r]=t.enums!==String||void 0===a.google.api.FieldBehavior[e[".google.api.fieldBehavior"][r]]?e[".google.api.fieldBehavior"][r]:a.google.api.FieldBehavior[e[".google.api.fieldBehavior"][r]]}return null!=e[".google.api.resourceReference"]&&e.hasOwnProperty(".google.api.resourceReference")&&(o[".google.api.resourceReference"]=a.google.api.ResourceReference.toObject(e[".google.api.resourceReference"],t)),o},m.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},m.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.FieldOptions"},m.CType=(e={},(o=Object.create(e))[e[0]="STRING"]="STRING",o[e[1]="CORD"]="CORD",o[e[2]="STRING_PIECE"]="STRING_PIECE",o),m.JSType=(e={},(o=Object.create(e))[e[0]="JS_NORMAL"]="JS_NORMAL",o[e[1]="JS_STRING"]="JS_STRING",o[e[2]="JS_NUMBER"]="JS_NUMBER",o),m.OptionRetention=(e={},(o=Object.create(e))[e[0]="RETENTION_UNKNOWN"]="RETENTION_UNKNOWN",o[e[1]="RETENTION_RUNTIME"]="RETENTION_RUNTIME",o[e[2]="RETENTION_SOURCE"]="RETENTION_SOURCE",o),m.OptionTargetType=(e={},(o=Object.create(e))[e[0]="TARGET_TYPE_UNKNOWN"]="TARGET_TYPE_UNKNOWN",o[e[1]="TARGET_TYPE_FILE"]="TARGET_TYPE_FILE",o[e[2]="TARGET_TYPE_EXTENSION_RANGE"]="TARGET_TYPE_EXTENSION_RANGE",o[e[3]="TARGET_TYPE_MESSAGE"]="TARGET_TYPE_MESSAGE",o[e[4]="TARGET_TYPE_FIELD"]="TARGET_TYPE_FIELD",o[e[5]="TARGET_TYPE_ONEOF"]="TARGET_TYPE_ONEOF",o[e[6]="TARGET_TYPE_ENUM"]="TARGET_TYPE_ENUM",o[e[7]="TARGET_TYPE_ENUM_ENTRY"]="TARGET_TYPE_ENUM_ENTRY",o[e[8]="TARGET_TYPE_SERVICE"]="TARGET_TYPE_SERVICE",o[e[9]="TARGET_TYPE_METHOD"]="TARGET_TYPE_METHOD",o),m.EditionDefault=(H.prototype.edition=0,H.prototype.value="",H.fromObject=function(e){if(e instanceof a.google.protobuf.FieldOptions.EditionDefault)return e;var t=new a.google.protobuf.FieldOptions.EditionDefault;switch(e.edition){default:"number"==typeof e.edition&&(t.edition=e.edition);break;case"EDITION_UNKNOWN":case 0:t.edition=0;break;case"EDITION_PROTO2":case 998:t.edition=998;break;case"EDITION_PROTO3":case 999:t.edition=999;break;case"EDITION_2023":case 1e3:t.edition=1e3;break;case"EDITION_2024":case 1001:t.edition=1001;break;case"EDITION_1_TEST_ONLY":case 1:t.edition=1;break;case"EDITION_2_TEST_ONLY":case 2:t.edition=2;break;case"EDITION_99997_TEST_ONLY":case 99997:t.edition=99997;break;case"EDITION_99998_TEST_ONLY":case 99998:t.edition=99998;break;case"EDITION_99999_TEST_ONLY":case 99999:t.edition=99999;break;case"EDITION_MAX":case 2147483647:t.edition=2147483647}return null!=e.value&&(t.value=String(e.value)),t},H.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.value="",o.edition=t.enums===String?"EDITION_UNKNOWN":0),null!=e.value&&e.hasOwnProperty("value")&&(o.value=e.value),null!=e.edition&&e.hasOwnProperty("edition")&&(o.edition=t.enums!==String||void 0===a.google.protobuf.Edition[e.edition]?e.edition:a.google.protobuf.Edition[e.edition]),o},H.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},H.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.FieldOptions.EditionDefault"},H),m),n.OneofOptions=(z.prototype.features=null,z.prototype.uninterpretedOption=i.emptyArray,z.fromObject=function(e){if(e instanceof a.google.protobuf.OneofOptions)return e;var t=new a.google.protobuf.OneofOptions;if(null!=e.features){if("object"!=typeof e.features)throw TypeError(".google.protobuf.OneofOptions.features: object expected");t.features=a.google.protobuf.FeatureSet.fromObject(e.features)}if(e.uninterpretedOption){if(!Array.isArray(e.uninterpretedOption))throw TypeError(".google.protobuf.OneofOptions.uninterpretedOption: array expected");t.uninterpretedOption=[];for(var o=0;o<e.uninterpretedOption.length;++o){if("object"!=typeof e.uninterpretedOption[o])throw TypeError(".google.protobuf.OneofOptions.uninterpretedOption: object expected");t.uninterpretedOption[o]=a.google.protobuf.UninterpretedOption.fromObject(e.uninterpretedOption[o])}}return t},z.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.uninterpretedOption=[]),t.defaults&&(o.features=null),null!=e.features&&e.hasOwnProperty("features")&&(o.features=a.google.protobuf.FeatureSet.toObject(e.features,t)),e.uninterpretedOption&&e.uninterpretedOption.length){o.uninterpretedOption=[];for(var r=0;r<e.uninterpretedOption.length;++r)o.uninterpretedOption[r]=a.google.protobuf.UninterpretedOption.toObject(e.uninterpretedOption[r],t)}return o},z.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},z.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.OneofOptions"},z),n.EnumOptions=(K.prototype.allowAlias=!1,K.prototype.deprecated=!1,K.prototype.deprecatedLegacyJsonFieldConflicts=!1,K.prototype.features=null,K.prototype.uninterpretedOption=i.emptyArray,K.fromObject=function(e){if(e instanceof a.google.protobuf.EnumOptions)return e;var t=new a.google.protobuf.EnumOptions;if(null!=e.allowAlias&&(t.allowAlias=Boolean(e.allowAlias)),null!=e.deprecated&&(t.deprecated=Boolean(e.deprecated)),null!=e.deprecatedLegacyJsonFieldConflicts&&(t.deprecatedLegacyJsonFieldConflicts=Boolean(e.deprecatedLegacyJsonFieldConflicts)),null!=e.features){if("object"!=typeof e.features)throw TypeError(".google.protobuf.EnumOptions.features: object expected");t.features=a.google.protobuf.FeatureSet.fromObject(e.features)}if(e.uninterpretedOption){if(!Array.isArray(e.uninterpretedOption))throw TypeError(".google.protobuf.EnumOptions.uninterpretedOption: array expected");t.uninterpretedOption=[];for(var o=0;o<e.uninterpretedOption.length;++o){if("object"!=typeof e.uninterpretedOption[o])throw TypeError(".google.protobuf.EnumOptions.uninterpretedOption: object expected");t.uninterpretedOption[o]=a.google.protobuf.UninterpretedOption.fromObject(e.uninterpretedOption[o])}}return t},K.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.uninterpretedOption=[]),t.defaults&&(o.allowAlias=!1,o.deprecated=!1,o.deprecatedLegacyJsonFieldConflicts=!1,o.features=null),null!=e.allowAlias&&e.hasOwnProperty("allowAlias")&&(o.allowAlias=e.allowAlias),null!=e.deprecated&&e.hasOwnProperty("deprecated")&&(o.deprecated=e.deprecated),null!=e.deprecatedLegacyJsonFieldConflicts&&e.hasOwnProperty("deprecatedLegacyJsonFieldConflicts")&&(o.deprecatedLegacyJsonFieldConflicts=e.deprecatedLegacyJsonFieldConflicts),null!=e.features&&e.hasOwnProperty("features")&&(o.features=a.google.protobuf.FeatureSet.toObject(e.features,t)),e.uninterpretedOption&&e.uninterpretedOption.length){o.uninterpretedOption=[];for(var r=0;r<e.uninterpretedOption.length;++r)o.uninterpretedOption[r]=a.google.protobuf.UninterpretedOption.toObject(e.uninterpretedOption[r],t)}return o},K.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},K.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.EnumOptions"},K),n.EnumValueOptions=(X.prototype.deprecated=!1,X.prototype.features=null,X.prototype.debugRedact=!1,X.prototype.uninterpretedOption=i.emptyArray,X.fromObject=function(e){if(e instanceof a.google.protobuf.EnumValueOptions)return e;var t=new a.google.protobuf.EnumValueOptions;if(null!=e.deprecated&&(t.deprecated=Boolean(e.deprecated)),null!=e.features){if("object"!=typeof e.features)throw TypeError(".google.protobuf.EnumValueOptions.features: object expected");t.features=a.google.protobuf.FeatureSet.fromObject(e.features)}if(null!=e.debugRedact&&(t.debugRedact=Boolean(e.debugRedact)),e.uninterpretedOption){if(!Array.isArray(e.uninterpretedOption))throw TypeError(".google.protobuf.EnumValueOptions.uninterpretedOption: array expected");t.uninterpretedOption=[];for(var o=0;o<e.uninterpretedOption.length;++o){if("object"!=typeof e.uninterpretedOption[o])throw TypeError(".google.protobuf.EnumValueOptions.uninterpretedOption: object expected");t.uninterpretedOption[o]=a.google.protobuf.UninterpretedOption.fromObject(e.uninterpretedOption[o])}}return t},X.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.uninterpretedOption=[]),t.defaults&&(o.deprecated=!1,o.features=null,o.debugRedact=!1),null!=e.deprecated&&e.hasOwnProperty("deprecated")&&(o.deprecated=e.deprecated),null!=e.features&&e.hasOwnProperty("features")&&(o.features=a.google.protobuf.FeatureSet.toObject(e.features,t)),null!=e.debugRedact&&e.hasOwnProperty("debugRedact")&&(o.debugRedact=e.debugRedact),e.uninterpretedOption&&e.uninterpretedOption.length){o.uninterpretedOption=[];for(var r=0;r<e.uninterpretedOption.length;++r)o.uninterpretedOption[r]=a.google.protobuf.UninterpretedOption.toObject(e.uninterpretedOption[r],t)}return o},X.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},X.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.EnumValueOptions"},X),n.ServiceOptions=(b.prototype.features=null,b.prototype.deprecated=!1,b.prototype.uninterpretedOption=i.emptyArray,b.prototype[".google.api.defaultHost"]="",b.prototype[".google.api.oauthScopes"]="",b.prototype[".google.api.apiVersion"]="",b.fromObject=function(e){if(e instanceof a.google.protobuf.ServiceOptions)return e;var t=new a.google.protobuf.ServiceOptions;if(null!=e.features){if("object"!=typeof e.features)throw TypeError(".google.protobuf.ServiceOptions.features: object expected");t.features=a.google.protobuf.FeatureSet.fromObject(e.features)}if(null!=e.deprecated&&(t.deprecated=Boolean(e.deprecated)),e.uninterpretedOption){if(!Array.isArray(e.uninterpretedOption))throw TypeError(".google.protobuf.ServiceOptions.uninterpretedOption: array expected");t.uninterpretedOption=[];for(var o=0;o<e.uninterpretedOption.length;++o){if("object"!=typeof e.uninterpretedOption[o])throw TypeError(".google.protobuf.ServiceOptions.uninterpretedOption: object expected");t.uninterpretedOption[o]=a.google.protobuf.UninterpretedOption.fromObject(e.uninterpretedOption[o])}}return null!=e[".google.api.defaultHost"]&&(t[".google.api.defaultHost"]=String(e[".google.api.defaultHost"])),null!=e[".google.api.oauthScopes"]&&(t[".google.api.oauthScopes"]=String(e[".google.api.oauthScopes"])),null!=e[".google.api.apiVersion"]&&(t[".google.api.apiVersion"]=String(e[".google.api.apiVersion"])),t},b.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.uninterpretedOption=[]),t.defaults&&(o.deprecated=!1,o.features=null,o[".google.api.defaultHost"]="",o[".google.api.oauthScopes"]="",o[".google.api.apiVersion"]=""),null!=e.deprecated&&e.hasOwnProperty("deprecated")&&(o.deprecated=e.deprecated),null!=e.features&&e.hasOwnProperty("features")&&(o.features=a.google.protobuf.FeatureSet.toObject(e.features,t)),e.uninterpretedOption&&e.uninterpretedOption.length){o.uninterpretedOption=[];for(var r=0;r<e.uninterpretedOption.length;++r)o.uninterpretedOption[r]=a.google.protobuf.UninterpretedOption.toObject(e.uninterpretedOption[r],t)}return null!=e[".google.api.defaultHost"]&&e.hasOwnProperty(".google.api.defaultHost")&&(o[".google.api.defaultHost"]=e[".google.api.defaultHost"]),null!=e[".google.api.oauthScopes"]&&e.hasOwnProperty(".google.api.oauthScopes")&&(o[".google.api.oauthScopes"]=e[".google.api.oauthScopes"]),null!=e[".google.api.apiVersion"]&&e.hasOwnProperty(".google.api.apiVersion")&&(o[".google.api.apiVersion"]=e[".google.api.apiVersion"]),o},b.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},b.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.ServiceOptions"},b),n.MethodOptions=(O.prototype.deprecated=!1,O.prototype.idempotencyLevel=0,O.prototype.features=null,O.prototype.uninterpretedOption=i.emptyArray,O.prototype[".google.api.http"]=null,O.prototype[".google.api.methodSignature"]=i.emptyArray,O.prototype[".google.longrunning.operationInfo"]=null,O.fromObject=function(e){if(e instanceof a.google.protobuf.MethodOptions)return e;var t=new a.google.protobuf.MethodOptions;switch(null!=e.deprecated&&(t.deprecated=Boolean(e.deprecated)),e.idempotencyLevel){default:"number"==typeof e.idempotencyLevel&&(t.idempotencyLevel=e.idempotencyLevel);break;case"IDEMPOTENCY_UNKNOWN":case 0:t.idempotencyLevel=0;break;case"NO_SIDE_EFFECTS":case 1:t.idempotencyLevel=1;break;case"IDEMPOTENT":case 2:t.idempotencyLevel=2}if(null!=e.features){if("object"!=typeof e.features)throw TypeError(".google.protobuf.MethodOptions.features: object expected");t.features=a.google.protobuf.FeatureSet.fromObject(e.features)}if(e.uninterpretedOption){if(!Array.isArray(e.uninterpretedOption))throw TypeError(".google.protobuf.MethodOptions.uninterpretedOption: array expected");t.uninterpretedOption=[];for(var o=0;o<e.uninterpretedOption.length;++o){if("object"!=typeof e.uninterpretedOption[o])throw TypeError(".google.protobuf.MethodOptions.uninterpretedOption: object expected");t.uninterpretedOption[o]=a.google.protobuf.UninterpretedOption.fromObject(e.uninterpretedOption[o])}}if(null!=e[".google.api.http"]){if("object"!=typeof e[".google.api.http"])throw TypeError(".google.protobuf.MethodOptions..google.api.http: object expected");t[".google.api.http"]=a.google.api.HttpRule.fromObject(e[".google.api.http"])}if(e[".google.api.methodSignature"]){if(!Array.isArray(e[".google.api.methodSignature"]))throw TypeError(".google.protobuf.MethodOptions..google.api.methodSignature: array expected");t[".google.api.methodSignature"]=[];for(o=0;o<e[".google.api.methodSignature"].length;++o)t[".google.api.methodSignature"][o]=String(e[".google.api.methodSignature"][o])}if(null!=e[".google.longrunning.operationInfo"]){if("object"!=typeof e[".google.longrunning.operationInfo"])throw TypeError(".google.protobuf.MethodOptions..google.longrunning.operationInfo: object expected");t[".google.longrunning.operationInfo"]=a.google.longrunning.OperationInfo.fromObject(e[".google.longrunning.operationInfo"])}return t},O.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.uninterpretedOption=[],o[".google.api.methodSignature"]=[]),t.defaults&&(o.deprecated=!1,o.idempotencyLevel=t.enums===String?"IDEMPOTENCY_UNKNOWN":0,o.features=null,o[".google.longrunning.operationInfo"]=null,o[".google.api.http"]=null),null!=e.deprecated&&e.hasOwnProperty("deprecated")&&(o.deprecated=e.deprecated),null!=e.idempotencyLevel&&e.hasOwnProperty("idempotencyLevel")&&(o.idempotencyLevel=t.enums!==String||void 0===a.google.protobuf.MethodOptions.IdempotencyLevel[e.idempotencyLevel]?e.idempotencyLevel:a.google.protobuf.MethodOptions.IdempotencyLevel[e.idempotencyLevel]),null!=e.features&&e.hasOwnProperty("features")&&(o.features=a.google.protobuf.FeatureSet.toObject(e.features,t)),e.uninterpretedOption&&e.uninterpretedOption.length){o.uninterpretedOption=[];for(var r=0;r<e.uninterpretedOption.length;++r)o.uninterpretedOption[r]=a.google.protobuf.UninterpretedOption.toObject(e.uninterpretedOption[r],t)}if(null!=e[".google.longrunning.operationInfo"]&&e.hasOwnProperty(".google.longrunning.operationInfo")&&(o[".google.longrunning.operationInfo"]=a.google.longrunning.OperationInfo.toObject(e[".google.longrunning.operationInfo"],t)),e[".google.api.methodSignature"]&&e[".google.api.methodSignature"].length){o[".google.api.methodSignature"]=[];for(r=0;r<e[".google.api.methodSignature"].length;++r)o[".google.api.methodSignature"][r]=e[".google.api.methodSignature"][r]}return null!=e[".google.api.http"]&&e.hasOwnProperty(".google.api.http")&&(o[".google.api.http"]=a.google.api.HttpRule.toObject(e[".google.api.http"],t)),o},O.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},O.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.MethodOptions"},O.IdempotencyLevel=(e={},(o=Object.create(e))[e[0]="IDEMPOTENCY_UNKNOWN"]="IDEMPOTENCY_UNKNOWN",o[e[1]="NO_SIDE_EFFECTS"]="NO_SIDE_EFFECTS",o[e[2]="IDEMPOTENT"]="IDEMPOTENT",o),O),n.UninterpretedOption=(h.prototype.name=i.emptyArray,h.prototype.identifierValue="",h.prototype.positiveIntValue=i.Long?i.Long.fromBits(0,0,!0):0,h.prototype.negativeIntValue=i.Long?i.Long.fromBits(0,0,!1):0,h.prototype.doubleValue=0,h.prototype.stringValue=i.newBuffer([]),h.prototype.aggregateValue="",h.fromObject=function(e){if(e instanceof a.google.protobuf.UninterpretedOption)return e;var t=new a.google.protobuf.UninterpretedOption;if(e.name){if(!Array.isArray(e.name))throw TypeError(".google.protobuf.UninterpretedOption.name: array expected");t.name=[];for(var o=0;o<e.name.length;++o){if("object"!=typeof e.name[o])throw TypeError(".google.protobuf.UninterpretedOption.name: object expected");t.name[o]=a.google.protobuf.UninterpretedOption.NamePart.fromObject(e.name[o])}}return null!=e.identifierValue&&(t.identifierValue=String(e.identifierValue)),null!=e.positiveIntValue&&(i.Long?(t.positiveIntValue=i.Long.fromValue(e.positiveIntValue)).unsigned=!0:"string"==typeof e.positiveIntValue?t.positiveIntValue=parseInt(e.positiveIntValue,10):"number"==typeof e.positiveIntValue?t.positiveIntValue=e.positiveIntValue:"object"==typeof e.positiveIntValue&&(t.positiveIntValue=new i.LongBits(e.positiveIntValue.low>>>0,e.positiveIntValue.high>>>0).toNumber(!0))),null!=e.negativeIntValue&&(i.Long?(t.negativeIntValue=i.Long.fromValue(e.negativeIntValue)).unsigned=!1:"string"==typeof e.negativeIntValue?t.negativeIntValue=parseInt(e.negativeIntValue,10):"number"==typeof e.negativeIntValue?t.negativeIntValue=e.negativeIntValue:"object"==typeof e.negativeIntValue&&(t.negativeIntValue=new i.LongBits(e.negativeIntValue.low>>>0,e.negativeIntValue.high>>>0).toNumber())),null!=e.doubleValue&&(t.doubleValue=Number(e.doubleValue)),null!=e.stringValue&&("string"==typeof e.stringValue?i.base64.decode(e.stringValue,t.stringValue=i.newBuffer(i.base64.length(e.stringValue)),0):0<=e.stringValue.length&&(t.stringValue=e.stringValue)),null!=e.aggregateValue&&(t.aggregateValue=String(e.aggregateValue)),t},h.toObject=function(e,t){var o,r={};if(((t=t||{}).arrays||t.defaults)&&(r.name=[]),t.defaults&&(r.identifierValue="",i.Long?(o=new i.Long(0,0,!0),r.positiveIntValue=t.longs===String?o.toString():t.longs===Number?o.toNumber():o):r.positiveIntValue=t.longs===String?"0":0,i.Long?(o=new i.Long(0,0,!1),r.negativeIntValue=t.longs===String?o.toString():t.longs===Number?o.toNumber():o):r.negativeIntValue=t.longs===String?"0":0,r.doubleValue=0,t.bytes===String?r.stringValue="":(r.stringValue=[],t.bytes!==Array&&(r.stringValue=i.newBuffer(r.stringValue))),r.aggregateValue=""),e.name&&e.name.length){r.name=[];for(var n=0;n<e.name.length;++n)r.name[n]=a.google.protobuf.UninterpretedOption.NamePart.toObject(e.name[n],t)}return null!=e.identifierValue&&e.hasOwnProperty("identifierValue")&&(r.identifierValue=e.identifierValue),null!=e.positiveIntValue&&e.hasOwnProperty("positiveIntValue")&&("number"==typeof e.positiveIntValue?r.positiveIntValue=t.longs===String?String(e.positiveIntValue):e.positiveIntValue:r.positiveIntValue=t.longs===String?i.Long.prototype.toString.call(e.positiveIntValue):t.longs===Number?new i.LongBits(e.positiveIntValue.low>>>0,e.positiveIntValue.high>>>0).toNumber(!0):e.positiveIntValue),null!=e.negativeIntValue&&e.hasOwnProperty("negativeIntValue")&&("number"==typeof e.negativeIntValue?r.negativeIntValue=t.longs===String?String(e.negativeIntValue):e.negativeIntValue:r.negativeIntValue=t.longs===String?i.Long.prototype.toString.call(e.negativeIntValue):t.longs===Number?new i.LongBits(e.negativeIntValue.low>>>0,e.negativeIntValue.high>>>0).toNumber():e.negativeIntValue),null!=e.doubleValue&&e.hasOwnProperty("doubleValue")&&(r.doubleValue=t.json&&!isFinite(e.doubleValue)?String(e.doubleValue):e.doubleValue),null!=e.stringValue&&e.hasOwnProperty("stringValue")&&(r.stringValue=t.bytes===String?i.base64.encode(e.stringValue,0,e.stringValue.length):t.bytes===Array?Array.prototype.slice.call(e.stringValue):e.stringValue),null!=e.aggregateValue&&e.hasOwnProperty("aggregateValue")&&(r.aggregateValue=e.aggregateValue),r},h.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},h.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.UninterpretedOption"},h.NamePart=(Z.prototype.namePart="",Z.prototype.isExtension=!1,Z.fromObject=function(e){var t;return e instanceof a.google.protobuf.UninterpretedOption.NamePart?e:(t=new a.google.protobuf.UninterpretedOption.NamePart,null!=e.namePart&&(t.namePart=String(e.namePart)),null!=e.isExtension&&(t.isExtension=Boolean(e.isExtension)),t)},Z.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.namePart="",o.isExtension=!1),null!=e.namePart&&e.hasOwnProperty("namePart")&&(o.namePart=e.namePart),null!=e.isExtension&&e.hasOwnProperty("isExtension")&&(o.isExtension=e.isExtension),o},Z.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},Z.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.UninterpretedOption.NamePart"},Z),h),n.FeatureSet=(v.prototype.fieldPresence=0,v.prototype.enumType=0,v.prototype.repeatedFieldEncoding=0,v.prototype.utf8Validation=0,v.prototype.messageEncoding=0,v.prototype.jsonFormat=0,v.fromObject=function(e){if(e instanceof a.google.protobuf.FeatureSet)return e;var t=new a.google.protobuf.FeatureSet;switch(e.fieldPresence){default:"number"==typeof e.fieldPresence&&(t.fieldPresence=e.fieldPresence);break;case"FIELD_PRESENCE_UNKNOWN":case 0:t.fieldPresence=0;break;case"EXPLICIT":case 1:t.fieldPresence=1;break;case"IMPLICIT":case 2:t.fieldPresence=2;break;case"LEGACY_REQUIRED":case 3:t.fieldPresence=3}switch(e.enumType){default:"number"==typeof e.enumType&&(t.enumType=e.enumType);break;case"ENUM_TYPE_UNKNOWN":case 0:t.enumType=0;break;case"OPEN":case 1:t.enumType=1;break;case"CLOSED":case 2:t.enumType=2}switch(e.repeatedFieldEncoding){default:"number"==typeof e.repeatedFieldEncoding&&(t.repeatedFieldEncoding=e.repeatedFieldEncoding);break;case"REPEATED_FIELD_ENCODING_UNKNOWN":case 0:t.repeatedFieldEncoding=0;break;case"PACKED":case 1:t.repeatedFieldEncoding=1;break;case"EXPANDED":case 2:t.repeatedFieldEncoding=2}switch(e.utf8Validation){default:"number"==typeof e.utf8Validation&&(t.utf8Validation=e.utf8Validation);break;case"UTF8_VALIDATION_UNKNOWN":case 0:t.utf8Validation=0;break;case"VERIFY":case 2:t.utf8Validation=2;break;case"NONE":case 3:t.utf8Validation=3}switch(e.messageEncoding){default:"number"==typeof e.messageEncoding&&(t.messageEncoding=e.messageEncoding);break;case"MESSAGE_ENCODING_UNKNOWN":case 0:t.messageEncoding=0;break;case"LENGTH_PREFIXED":case 1:t.messageEncoding=1;break;case"DELIMITED":case 2:t.messageEncoding=2}switch(e.jsonFormat){default:"number"==typeof e.jsonFormat&&(t.jsonFormat=e.jsonFormat);break;case"JSON_FORMAT_UNKNOWN":case 0:t.jsonFormat=0;break;case"ALLOW":case 1:t.jsonFormat=1;break;case"LEGACY_BEST_EFFORT":case 2:t.jsonFormat=2}return t},v.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.fieldPresence=t.enums===String?"FIELD_PRESENCE_UNKNOWN":0,o.enumType=t.enums===String?"ENUM_TYPE_UNKNOWN":0,o.repeatedFieldEncoding=t.enums===String?"REPEATED_FIELD_ENCODING_UNKNOWN":0,o.utf8Validation=t.enums===String?"UTF8_VALIDATION_UNKNOWN":0,o.messageEncoding=t.enums===String?"MESSAGE_ENCODING_UNKNOWN":0,o.jsonFormat=t.enums===String?"JSON_FORMAT_UNKNOWN":0),null!=e.fieldPresence&&e.hasOwnProperty("fieldPresence")&&(o.fieldPresence=t.enums!==String||void 0===a.google.protobuf.FeatureSet.FieldPresence[e.fieldPresence]?e.fieldPresence:a.google.protobuf.FeatureSet.FieldPresence[e.fieldPresence]),null!=e.enumType&&e.hasOwnProperty("enumType")&&(o.enumType=t.enums!==String||void 0===a.google.protobuf.FeatureSet.EnumType[e.enumType]?e.enumType:a.google.protobuf.FeatureSet.EnumType[e.enumType]),null!=e.repeatedFieldEncoding&&e.hasOwnProperty("repeatedFieldEncoding")&&(o.repeatedFieldEncoding=t.enums!==String||void 0===a.google.protobuf.FeatureSet.RepeatedFieldEncoding[e.repeatedFieldEncoding]?e.repeatedFieldEncoding:a.google.protobuf.FeatureSet.RepeatedFieldEncoding[e.repeatedFieldEncoding]),null!=e.utf8Validation&&e.hasOwnProperty("utf8Validation")&&(o.utf8Validation=t.enums!==String||void 0===a.google.protobuf.FeatureSet.Utf8Validation[e.utf8Validation]?e.utf8Validation:a.google.protobuf.FeatureSet.Utf8Validation[e.utf8Validation]),null!=e.messageEncoding&&e.hasOwnProperty("messageEncoding")&&(o.messageEncoding=t.enums!==String||void 0===a.google.protobuf.FeatureSet.MessageEncoding[e.messageEncoding]?e.messageEncoding:a.google.protobuf.FeatureSet.MessageEncoding[e.messageEncoding]),null!=e.jsonFormat&&e.hasOwnProperty("jsonFormat")&&(o.jsonFormat=t.enums!==String||void 0===a.google.protobuf.FeatureSet.JsonFormat[e.jsonFormat]?e.jsonFormat:a.google.protobuf.FeatureSet.JsonFormat[e.jsonFormat]),o},v.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},v.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.FeatureSet"},v.FieldPresence=(e={},(o=Object.create(e))[e[0]="FIELD_PRESENCE_UNKNOWN"]="FIELD_PRESENCE_UNKNOWN",o[e[1]="EXPLICIT"]="EXPLICIT",o[e[2]="IMPLICIT"]="IMPLICIT",o[e[3]="LEGACY_REQUIRED"]="LEGACY_REQUIRED",o),v.EnumType=(e={},(o=Object.create(e))[e[0]="ENUM_TYPE_UNKNOWN"]="ENUM_TYPE_UNKNOWN",o[e[1]="OPEN"]="OPEN",o[e[2]="CLOSED"]="CLOSED",o),v.RepeatedFieldEncoding=(e={},(o=Object.create(e))[e[0]="REPEATED_FIELD_ENCODING_UNKNOWN"]="REPEATED_FIELD_ENCODING_UNKNOWN",o[e[1]="PACKED"]="PACKED",o[e[2]="EXPANDED"]="EXPANDED",o),v.Utf8Validation=(e={},(o=Object.create(e))[e[0]="UTF8_VALIDATION_UNKNOWN"]="UTF8_VALIDATION_UNKNOWN",o[e[2]="VERIFY"]="VERIFY",o[e[3]="NONE"]="NONE",o),v.MessageEncoding=(e={},(o=Object.create(e))[e[0]="MESSAGE_ENCODING_UNKNOWN"]="MESSAGE_ENCODING_UNKNOWN",o[e[1]="LENGTH_PREFIXED"]="LENGTH_PREFIXED",o[e[2]="DELIMITED"]="DELIMITED",o),v.JsonFormat=(e={},(o=Object.create(e))[e[0]="JSON_FORMAT_UNKNOWN"]="JSON_FORMAT_UNKNOWN",o[e[1]="ALLOW"]="ALLOW",o[e[2]="LEGACY_BEST_EFFORT"]="LEGACY_BEST_EFFORT",o),v),n.FeatureSetDefaults=($.prototype.defaults=i.emptyArray,$.prototype.minimumEdition=0,$.prototype.maximumEdition=0,$.fromObject=function(e){if(e instanceof a.google.protobuf.FeatureSetDefaults)return e;var t=new a.google.protobuf.FeatureSetDefaults;if(e.defaults){if(!Array.isArray(e.defaults))throw TypeError(".google.protobuf.FeatureSetDefaults.defaults: array expected");t.defaults=[];for(var o=0;o<e.defaults.length;++o){if("object"!=typeof e.defaults[o])throw TypeError(".google.protobuf.FeatureSetDefaults.defaults: object expected");t.defaults[o]=a.google.protobuf.FeatureSetDefaults.FeatureSetEditionDefault.fromObject(e.defaults[o])}}switch(e.minimumEdition){default:"number"==typeof e.minimumEdition&&(t.minimumEdition=e.minimumEdition);break;case"EDITION_UNKNOWN":case 0:t.minimumEdition=0;break;case"EDITION_PROTO2":case 998:t.minimumEdition=998;break;case"EDITION_PROTO3":case 999:t.minimumEdition=999;break;case"EDITION_2023":case 1e3:t.minimumEdition=1e3;break;case"EDITION_2024":case 1001:t.minimumEdition=1001;break;case"EDITION_1_TEST_ONLY":case 1:t.minimumEdition=1;break;case"EDITION_2_TEST_ONLY":case 2:t.minimumEdition=2;break;case"EDITION_99997_TEST_ONLY":case 99997:t.minimumEdition=99997;break;case"EDITION_99998_TEST_ONLY":case 99998:t.minimumEdition=99998;break;case"EDITION_99999_TEST_ONLY":case 99999:t.minimumEdition=99999;break;case"EDITION_MAX":case 2147483647:t.minimumEdition=2147483647}switch(e.maximumEdition){default:"number"==typeof e.maximumEdition&&(t.maximumEdition=e.maximumEdition);break;case"EDITION_UNKNOWN":case 0:t.maximumEdition=0;break;case"EDITION_PROTO2":case 998:t.maximumEdition=998;break;case"EDITION_PROTO3":case 999:t.maximumEdition=999;break;case"EDITION_2023":case 1e3:t.maximumEdition=1e3;break;case"EDITION_2024":case 1001:t.maximumEdition=1001;break;case"EDITION_1_TEST_ONLY":case 1:t.maximumEdition=1;break;case"EDITION_2_TEST_ONLY":case 2:t.maximumEdition=2;break;case"EDITION_99997_TEST_ONLY":case 99997:t.maximumEdition=99997;break;case"EDITION_99998_TEST_ONLY":case 99998:t.maximumEdition=99998;break;case"EDITION_99999_TEST_ONLY":case 99999:t.maximumEdition=99999;break;case"EDITION_MAX":case 2147483647:t.maximumEdition=2147483647}return t},$.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.defaults=[]),t.defaults&&(o.minimumEdition=t.enums===String?"EDITION_UNKNOWN":0,o.maximumEdition=t.enums===String?"EDITION_UNKNOWN":0),e.defaults&&e.defaults.length){o.defaults=[];for(var r=0;r<e.defaults.length;++r)o.defaults[r]=a.google.protobuf.FeatureSetDefaults.FeatureSetEditionDefault.toObject(e.defaults[r],t)}return null!=e.minimumEdition&&e.hasOwnProperty("minimumEdition")&&(o.minimumEdition=t.enums!==String||void 0===a.google.protobuf.Edition[e.minimumEdition]?e.minimumEdition:a.google.protobuf.Edition[e.minimumEdition]),null!=e.maximumEdition&&e.hasOwnProperty("maximumEdition")&&(o.maximumEdition=t.enums!==String||void 0===a.google.protobuf.Edition[e.maximumEdition]?e.maximumEdition:a.google.protobuf.Edition[e.maximumEdition]),o},$.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},$.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.FeatureSetDefaults"},$.FeatureSetEditionDefault=(ee.prototype.edition=0,ee.prototype.features=null,ee.fromObject=function(e){if(e instanceof a.google.protobuf.FeatureSetDefaults.FeatureSetEditionDefault)return e;var t=new a.google.protobuf.FeatureSetDefaults.FeatureSetEditionDefault;switch(e.edition){default:"number"==typeof e.edition&&(t.edition=e.edition);break;case"EDITION_UNKNOWN":case 0:t.edition=0;break;case"EDITION_PROTO2":case 998:t.edition=998;break;case"EDITION_PROTO3":case 999:t.edition=999;break;case"EDITION_2023":case 1e3:t.edition=1e3;break;case"EDITION_2024":case 1001:t.edition=1001;break;case"EDITION_1_TEST_ONLY":case 1:t.edition=1;break;case"EDITION_2_TEST_ONLY":case 2:t.edition=2;break;case"EDITION_99997_TEST_ONLY":case 99997:t.edition=99997;break;case"EDITION_99998_TEST_ONLY":case 99998:t.edition=99998;break;case"EDITION_99999_TEST_ONLY":case 99999:t.edition=99999;break;case"EDITION_MAX":case 2147483647:t.edition=2147483647}if(null!=e.features){if("object"!=typeof e.features)throw TypeError(".google.protobuf.FeatureSetDefaults.FeatureSetEditionDefault.features: object expected");t.features=a.google.protobuf.FeatureSet.fromObject(e.features)}return t},ee.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.features=null,o.edition=t.enums===String?"EDITION_UNKNOWN":0),null!=e.features&&e.hasOwnProperty("features")&&(o.features=a.google.protobuf.FeatureSet.toObject(e.features,t)),null!=e.edition&&e.hasOwnProperty("edition")&&(o.edition=t.enums!==String||void 0===a.google.protobuf.Edition[e.edition]?e.edition:a.google.protobuf.Edition[e.edition]),o},ee.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},ee.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.FeatureSetDefaults.FeatureSetEditionDefault"},ee),$),n.SourceCodeInfo=(te.prototype.location=i.emptyArray,te.fromObject=function(e){if(e instanceof a.google.protobuf.SourceCodeInfo)return e;var t=new a.google.protobuf.SourceCodeInfo;if(e.location){if(!Array.isArray(e.location))throw TypeError(".google.protobuf.SourceCodeInfo.location: array expected");t.location=[];for(var o=0;o<e.location.length;++o){if("object"!=typeof e.location[o])throw TypeError(".google.protobuf.SourceCodeInfo.location: object expected");t.location[o]=a.google.protobuf.SourceCodeInfo.Location.fromObject(e.location[o])}}return t},te.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.location=[]),e.location&&e.location.length){o.location=[];for(var r=0;r<e.location.length;++r)o.location[r]=a.google.protobuf.SourceCodeInfo.Location.toObject(e.location[r],t)}return o},te.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},te.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.SourceCodeInfo"},te.Location=(oe.prototype.path=i.emptyArray,oe.prototype.span=i.emptyArray,oe.prototype.leadingComments="",oe.prototype.trailingComments="",oe.prototype.leadingDetachedComments=i.emptyArray,oe.fromObject=function(e){if(e instanceof a.google.protobuf.SourceCodeInfo.Location)return e;var t=new a.google.protobuf.SourceCodeInfo.Location;if(e.path){if(!Array.isArray(e.path))throw TypeError(".google.protobuf.SourceCodeInfo.Location.path: array expected");t.path=[];for(var o=0;o<e.path.length;++o)t.path[o]=0|e.path[o]}if(e.span){if(!Array.isArray(e.span))throw TypeError(".google.protobuf.SourceCodeInfo.Location.span: array expected");t.span=[];for(o=0;o<e.span.length;++o)t.span[o]=0|e.span[o]}if(null!=e.leadingComments&&(t.leadingComments=String(e.leadingComments)),null!=e.trailingComments&&(t.trailingComments=String(e.trailingComments)),e.leadingDetachedComments){if(!Array.isArray(e.leadingDetachedComments))throw TypeError(".google.protobuf.SourceCodeInfo.Location.leadingDetachedComments: array expected");t.leadingDetachedComments=[];for(o=0;o<e.leadingDetachedComments.length;++o)t.leadingDetachedComments[o]=String(e.leadingDetachedComments[o])}return t},oe.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.path=[],o.span=[],o.leadingDetachedComments=[]),t.defaults&&(o.leadingComments="",o.trailingComments=""),e.path&&e.path.length){o.path=[];for(var r=0;r<e.path.length;++r)o.path[r]=e.path[r]}if(e.span&&e.span.length){o.span=[];for(r=0;r<e.span.length;++r)o.span[r]=e.span[r]}if(null!=e.leadingComments&&e.hasOwnProperty("leadingComments")&&(o.leadingComments=e.leadingComments),null!=e.trailingComments&&e.hasOwnProperty("trailingComments")&&(o.trailingComments=e.trailingComments),e.leadingDetachedComments&&e.leadingDetachedComments.length){o.leadingDetachedComments=[];for(r=0;r<e.leadingDetachedComments.length;++r)o.leadingDetachedComments[r]=e.leadingDetachedComments[r]}return o},oe.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},oe.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.SourceCodeInfo.Location"},oe),te),n.GeneratedCodeInfo=(re.prototype.annotation=i.emptyArray,re.fromObject=function(e){if(e instanceof a.google.protobuf.GeneratedCodeInfo)return e;var t=new a.google.protobuf.GeneratedCodeInfo;if(e.annotation){if(!Array.isArray(e.annotation))throw TypeError(".google.protobuf.GeneratedCodeInfo.annotation: array expected");t.annotation=[];for(var o=0;o<e.annotation.length;++o){if("object"!=typeof e.annotation[o])throw TypeError(".google.protobuf.GeneratedCodeInfo.annotation: object expected");t.annotation[o]=a.google.protobuf.GeneratedCodeInfo.Annotation.fromObject(e.annotation[o])}}return t},re.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.annotation=[]),e.annotation&&e.annotation.length){o.annotation=[];for(var r=0;r<e.annotation.length;++r)o.annotation[r]=a.google.protobuf.GeneratedCodeInfo.Annotation.toObject(e.annotation[r],t)}return o},re.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},re.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.GeneratedCodeInfo"},re.Annotation=(T.prototype.path=i.emptyArray,T.prototype.sourceFile="",T.prototype.begin=0,T.prototype.end=0,T.prototype.semantic=0,T.fromObject=function(e){if(e instanceof a.google.protobuf.GeneratedCodeInfo.Annotation)return e;var t=new a.google.protobuf.GeneratedCodeInfo.Annotation;if(e.path){if(!Array.isArray(e.path))throw TypeError(".google.protobuf.GeneratedCodeInfo.Annotation.path: array expected");t.path=[];for(var o=0;o<e.path.length;++o)t.path[o]=0|e.path[o]}switch(null!=e.sourceFile&&(t.sourceFile=String(e.sourceFile)),null!=e.begin&&(t.begin=0|e.begin),null!=e.end&&(t.end=0|e.end),e.semantic){default:"number"==typeof e.semantic&&(t.semantic=e.semantic);break;case"NONE":case 0:t.semantic=0;break;case"SET":case 1:t.semantic=1;break;case"ALIAS":case 2:t.semantic=2}return t},T.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.path=[]),t.defaults&&(o.sourceFile="",o.begin=0,o.end=0,o.semantic=t.enums===String?"NONE":0),e.path&&e.path.length){o.path=[];for(var r=0;r<e.path.length;++r)o.path[r]=e.path[r]}return null!=e.sourceFile&&e.hasOwnProperty("sourceFile")&&(o.sourceFile=e.sourceFile),null!=e.begin&&e.hasOwnProperty("begin")&&(o.begin=e.begin),null!=e.end&&e.hasOwnProperty("end")&&(o.end=e.end),null!=e.semantic&&e.hasOwnProperty("semantic")&&(o.semantic=t.enums!==String||void 0===a.google.protobuf.GeneratedCodeInfo.Annotation.Semantic[e.semantic]?e.semantic:a.google.protobuf.GeneratedCodeInfo.Annotation.Semantic[e.semantic]),o},T.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},T.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.GeneratedCodeInfo.Annotation"},T.Semantic=(e={},(o=Object.create(e))[e[0]="NONE"]="NONE",o[e[1]="SET"]="SET",o[e[2]="ALIAS"]="ALIAS",o),T),re),n.Duration=(ne.prototype.seconds=i.Long?i.Long.fromBits(0,0,!1):0,ne.prototype.nanos=0,ne.fromObject=function(e){var t;return e instanceof a.google.protobuf.Duration?e:(t=new a.google.protobuf.Duration,null!=e.seconds&&(i.Long?(t.seconds=i.Long.fromValue(e.seconds)).unsigned=!1:"string"==typeof e.seconds?t.seconds=parseInt(e.seconds,10):"number"==typeof e.seconds?t.seconds=e.seconds:"object"==typeof e.seconds&&(t.seconds=new i.LongBits(e.seconds.low>>>0,e.seconds.high>>>0).toNumber())),null!=e.nanos&&(t.nanos=0|e.nanos),t)},ne.toObject=function(e,t){var o,r={};return(t=t||{}).defaults&&(i.Long?(o=new i.Long(0,0,!1),r.seconds=t.longs===String?o.toString():t.longs===Number?o.toNumber():o):r.seconds=t.longs===String?"0":0,r.nanos=0),null!=e.seconds&&e.hasOwnProperty("seconds")&&("number"==typeof e.seconds?r.seconds=t.longs===String?String(e.seconds):e.seconds:r.seconds=t.longs===String?i.Long.prototype.toString.call(e.seconds):t.longs===Number?new i.LongBits(e.seconds.low>>>0,e.seconds.high>>>0).toNumber():e.seconds),null!=e.nanos&&e.hasOwnProperty("nanos")&&(r.nanos=e.nanos),r},ne.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},ne.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.Duration"},ne),n.DoubleValue=(ie.prototype.value=0,ie.fromObject=function(e){var t;return e instanceof a.google.protobuf.DoubleValue?e:(t=new a.google.protobuf.DoubleValue,null!=e.value&&(t.value=Number(e.value)),t)},ie.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.value=0),null!=e.value&&e.hasOwnProperty("value")&&(o.value=t.json&&!isFinite(e.value)?String(e.value):e.value),o},ie.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},ie.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.DoubleValue"},ie),n.FloatValue=(ae.prototype.value=0,ae.fromObject=function(e){var t;return e instanceof a.google.protobuf.FloatValue?e:(t=new a.google.protobuf.FloatValue,null!=e.value&&(t.value=Number(e.value)),t)},ae.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.value=0),null!=e.value&&e.hasOwnProperty("value")&&(o.value=t.json&&!isFinite(e.value)?String(e.value):e.value),o},ae.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},ae.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.FloatValue"},ae),n.Int64Value=(se.prototype.value=i.Long?i.Long.fromBits(0,0,!1):0,se.fromObject=function(e){var t;return e instanceof a.google.protobuf.Int64Value?e:(t=new a.google.protobuf.Int64Value,null!=e.value&&(i.Long?(t.value=i.Long.fromValue(e.value)).unsigned=!1:"string"==typeof e.value?t.value=parseInt(e.value,10):"number"==typeof e.value?t.value=e.value:"object"==typeof e.value&&(t.value=new i.LongBits(e.value.low>>>0,e.value.high>>>0).toNumber())),t)},se.toObject=function(e,t){var o,r={};return(t=t||{}).defaults&&(i.Long?(o=new i.Long(0,0,!1),r.value=t.longs===String?o.toString():t.longs===Number?o.toNumber():o):r.value=t.longs===String?"0":0),null!=e.value&&e.hasOwnProperty("value")&&("number"==typeof e.value?r.value=t.longs===String?String(e.value):e.value:r.value=t.longs===String?i.Long.prototype.toString.call(e.value):t.longs===Number?new i.LongBits(e.value.low>>>0,e.value.high>>>0).toNumber():e.value),r},se.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},se.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.Int64Value"},se),n.UInt64Value=(le.prototype.value=i.Long?i.Long.fromBits(0,0,!0):0,le.fromObject=function(e){var t;return e instanceof a.google.protobuf.UInt64Value?e:(t=new a.google.protobuf.UInt64Value,null!=e.value&&(i.Long?(t.value=i.Long.fromValue(e.value)).unsigned=!0:"string"==typeof e.value?t.value=parseInt(e.value,10):"number"==typeof e.value?t.value=e.value:"object"==typeof e.value&&(t.value=new i.LongBits(e.value.low>>>0,e.value.high>>>0).toNumber(!0))),t)},le.toObject=function(e,t){var o,r={};return(t=t||{}).defaults&&(i.Long?(o=new i.Long(0,0,!0),r.value=t.longs===String?o.toString():t.longs===Number?o.toNumber():o):r.value=t.longs===String?"0":0),null!=e.value&&e.hasOwnProperty("value")&&("number"==typeof e.value?r.value=t.longs===String?String(e.value):e.value:r.value=t.longs===String?i.Long.prototype.toString.call(e.value):t.longs===Number?new i.LongBits(e.value.low>>>0,e.value.high>>>0).toNumber(!0):e.value),r},le.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},le.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.UInt64Value"},le),n.Int32Value=(ue.prototype.value=0,ue.fromObject=function(e){var t;return e instanceof a.google.protobuf.Int32Value?e:(t=new a.google.protobuf.Int32Value,null!=e.value&&(t.value=0|e.value),t)},ue.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.value=0),null!=e.value&&e.hasOwnProperty("value")&&(o.value=e.value),o},ue.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},ue.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.Int32Value"},ue),n.UInt32Value=(pe.prototype.value=0,pe.fromObject=function(e){var t;return e instanceof a.google.protobuf.UInt32Value?e:(t=new a.google.protobuf.UInt32Value,null!=e.value&&(t.value=e.value>>>0),t)},pe.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.value=0),null!=e.value&&e.hasOwnProperty("value")&&(o.value=e.value),o},pe.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},pe.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.UInt32Value"},pe),n.BoolValue=(ce.prototype.value=!1,ce.fromObject=function(e){var t;return e instanceof a.google.protobuf.BoolValue?e:(t=new a.google.protobuf.BoolValue,null!=e.value&&(t.value=Boolean(e.value)),t)},ce.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.value=!1),null!=e.value&&e.hasOwnProperty("value")&&(o.value=e.value),o},ce.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},ce.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.BoolValue"},ce),n.StringValue=(ge.prototype.value="",ge.fromObject=function(e){var t;return e instanceof a.google.protobuf.StringValue?e:(t=new a.google.protobuf.StringValue,null!=e.value&&(t.value=String(e.value)),t)},ge.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.value=""),null!=e.value&&e.hasOwnProperty("value")&&(o.value=e.value),o},ge.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},ge.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.StringValue"},ge),n.BytesValue=(fe.prototype.value=i.newBuffer([]),fe.fromObject=function(e){var t;return e instanceof a.google.protobuf.BytesValue?e:(t=new a.google.protobuf.BytesValue,null!=e.value&&("string"==typeof e.value?i.base64.decode(e.value,t.value=i.newBuffer(i.base64.length(e.value)),0):0<=e.value.length&&(t.value=e.value)),t)},fe.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(t.bytes===String?o.value="":(o.value=[],t.bytes!==Array&&(o.value=i.newBuffer(o.value)))),null!=e.value&&e.hasOwnProperty("value")&&(o.value=t.bytes===String?i.base64.encode(e.value,0,e.value.length):t.bytes===Array?Array.prototype.slice.call(e.value):e.value),o},fe.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},fe.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.BytesValue"},fe),n.Empty=(de.fromObject=function(e){return e instanceof a.google.protobuf.Empty?e:new a.google.protobuf.Empty},de.toObject=function(){return{}},de.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},de.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.Empty"},de),n.Any=(ye.prototype.type_url="",ye.prototype.value=i.newBuffer([]),ye.fromObject=function(e){var t;return e instanceof a.google.protobuf.Any?e:(t=new a.google.protobuf.Any,null!=e.type_url&&(t.type_url=String(e.type_url)),null!=e.value&&("string"==typeof e.value?i.base64.decode(e.value,t.value=i.newBuffer(i.base64.length(e.value)),0):0<=e.value.length&&(t.value=e.value)),t)},ye.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.type_url="",t.bytes===String?o.value="":(o.value=[],t.bytes!==Array&&(o.value=i.newBuffer(o.value)))),null!=e.type_url&&e.hasOwnProperty("type_url")&&(o.type_url=e.type_url),null!=e.value&&e.hasOwnProperty("value")&&(o.value=t.bytes===String?i.base64.encode(e.value,0,e.value.length):t.bytes===Array?Array.prototype.slice.call(e.value):e.value),o},ye.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},ye.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.Any"},ye),n.FieldMask=(me.prototype.paths=i.emptyArray,me.fromObject=function(e){if(e instanceof a.google.protobuf.FieldMask)return e;var t=new a.google.protobuf.FieldMask;if(e.paths){if(!Array.isArray(e.paths))throw TypeError(".google.protobuf.FieldMask.paths: array expected");t.paths=[];for(var o=0;o<e.paths.length;++o)t.paths[o]=String(e.paths[o])}return t},me.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.paths=[]),e.paths&&e.paths.length){o.paths=[];for(var r=0;r<e.paths.length;++r)o.paths[r]=e.paths[r]}return o},me.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},me.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.protobuf.FieldMask"},me),n),x.firestore=((e={}).v1beta1=((o={}).DocumentMask=(be.prototype.fieldPaths=i.emptyArray,be.fromObject=function(e){if(e instanceof a.google.firestore.v1beta1.DocumentMask)return e;var t=new a.google.firestore.v1beta1.DocumentMask;if(e.fieldPaths){if(!Array.isArray(e.fieldPaths))throw TypeError(".google.firestore.v1beta1.DocumentMask.fieldPaths: array expected");t.fieldPaths=[];for(var o=0;o<e.fieldPaths.length;++o)t.fieldPaths[o]=String(e.fieldPaths[o])}return t},be.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.fieldPaths=[]),e.fieldPaths&&e.fieldPaths.length){o.fieldPaths=[];for(var r=0;r<e.fieldPaths.length;++r)o.fieldPaths[r]=e.fieldPaths[r]}return o},be.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},be.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.v1beta1.DocumentMask"},be),o.Precondition=(Oe.prototype.exists=null,Oe.prototype.updateTime=null,Object.defineProperty(Oe.prototype,"conditionType",{get:i.oneOfGetter(n=["exists","updateTime"]),set:i.oneOfSetter(n)}),Oe.fromObject=function(e){if(e instanceof a.google.firestore.v1beta1.Precondition)return e;var t=new a.google.firestore.v1beta1.Precondition;if(null!=e.exists&&(t.exists=Boolean(e.exists)),null!=e.updateTime){if("object"!=typeof e.updateTime)throw TypeError(".google.firestore.v1beta1.Precondition.updateTime: object expected");t.updateTime=a.google.protobuf.Timestamp.fromObject(e.updateTime)}return t},Oe.toObject=function(e,t){t=t||{};var o={};return null!=e.exists&&e.hasOwnProperty("exists")&&(o.exists=e.exists,t.oneofs)&&(o.conditionType="exists"),null!=e.updateTime&&e.hasOwnProperty("updateTime")&&(o.updateTime=a.google.protobuf.Timestamp.toObject(e.updateTime,t),t.oneofs)&&(o.conditionType="updateTime"),o},Oe.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},Oe.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.v1beta1.Precondition"},Oe),o.TransactionOptions=(he.prototype.readOnly=null,he.prototype.readWrite=null,Object.defineProperty(he.prototype,"mode",{get:i.oneOfGetter(n=["readOnly","readWrite"]),set:i.oneOfSetter(n)}),he.fromObject=function(e){if(e instanceof a.google.firestore.v1beta1.TransactionOptions)return e;var t=new a.google.firestore.v1beta1.TransactionOptions;if(null!=e.readOnly){if("object"!=typeof e.readOnly)throw TypeError(".google.firestore.v1beta1.TransactionOptions.readOnly: object expected");t.readOnly=a.google.firestore.v1beta1.TransactionOptions.ReadOnly.fromObject(e.readOnly)}if(null!=e.readWrite){if("object"!=typeof e.readWrite)throw TypeError(".google.firestore.v1beta1.TransactionOptions.readWrite: object expected");t.readWrite=a.google.firestore.v1beta1.TransactionOptions.ReadWrite.fromObject(e.readWrite)}return t},he.toObject=function(e,t){t=t||{};var o={};return null!=e.readOnly&&e.hasOwnProperty("readOnly")&&(o.readOnly=a.google.firestore.v1beta1.TransactionOptions.ReadOnly.toObject(e.readOnly,t),t.oneofs)&&(o.mode="readOnly"),null!=e.readWrite&&e.hasOwnProperty("readWrite")&&(o.readWrite=a.google.firestore.v1beta1.TransactionOptions.ReadWrite.toObject(e.readWrite,t),t.oneofs)&&(o.mode="readWrite"),o},he.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},he.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.v1beta1.TransactionOptions"},he.ReadWrite=(ve.prototype.retryTransaction=i.newBuffer([]),ve.fromObject=function(e){var t;return e instanceof a.google.firestore.v1beta1.TransactionOptions.ReadWrite?e:(t=new a.google.firestore.v1beta1.TransactionOptions.ReadWrite,null!=e.retryTransaction&&("string"==typeof e.retryTransaction?i.base64.decode(e.retryTransaction,t.retryTransaction=i.newBuffer(i.base64.length(e.retryTransaction)),0):0<=e.retryTransaction.length&&(t.retryTransaction=e.retryTransaction)),t)},ve.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(t.bytes===String?o.retryTransaction="":(o.retryTransaction=[],t.bytes!==Array&&(o.retryTransaction=i.newBuffer(o.retryTransaction)))),null!=e.retryTransaction&&e.hasOwnProperty("retryTransaction")&&(o.retryTransaction=t.bytes===String?i.base64.encode(e.retryTransaction,0,e.retryTransaction.length):t.bytes===Array?Array.prototype.slice.call(e.retryTransaction):e.retryTransaction),o},ve.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},ve.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.v1beta1.TransactionOptions.ReadWrite"},ve),he.ReadOnly=(Te.prototype.readTime=null,Object.defineProperty(Te.prototype,"consistencySelector",{get:i.oneOfGetter(n=["readTime"]),set:i.oneOfSetter(n)}),Te.fromObject=function(e){if(e instanceof a.google.firestore.v1beta1.TransactionOptions.ReadOnly)return e;var t=new a.google.firestore.v1beta1.TransactionOptions.ReadOnly;if(null!=e.readTime){if("object"!=typeof e.readTime)throw TypeError(".google.firestore.v1beta1.TransactionOptions.ReadOnly.readTime: object expected");t.readTime=a.google.protobuf.Timestamp.fromObject(e.readTime)}return t},Te.toObject=function(e,t){t=t||{};var o={};return null!=e.readTime&&e.hasOwnProperty("readTime")&&(o.readTime=a.google.protobuf.Timestamp.toObject(e.readTime,t),t.oneofs)&&(o.consistencySelector="readTime"),o},Te.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},Te.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.v1beta1.TransactionOptions.ReadOnly"},Te),he),o.Document=(Se.prototype.name="",Se.prototype.fields=i.emptyObject,Se.prototype.createTime=null,Se.prototype.updateTime=null,Se.fromObject=function(e){if(e instanceof a.google.firestore.v1beta1.Document)return e;var t=new a.google.firestore.v1beta1.Document;if(null!=e.name&&(t.name=String(e.name)),e.fields){if("object"!=typeof e.fields)throw TypeError(".google.firestore.v1beta1.Document.fields: object expected");t.fields={};for(var o=Object.keys(e.fields),r=0;r<o.length;++r){if("object"!=typeof e.fields[o[r]])throw TypeError(".google.firestore.v1beta1.Document.fields: object expected");t.fields[o[r]]=a.google.firestore.v1beta1.Value.fromObject(e.fields[o[r]])}}if(null!=e.createTime){if("object"!=typeof e.createTime)throw TypeError(".google.firestore.v1beta1.Document.createTime: object expected");t.createTime=a.google.protobuf.Timestamp.fromObject(e.createTime)}if(null!=e.updateTime){if("object"!=typeof e.updateTime)throw TypeError(".google.firestore.v1beta1.Document.updateTime: object expected");t.updateTime=a.google.protobuf.Timestamp.fromObject(e.updateTime)}return t},Se.toObject=function(e,t){var o,r={};if(((t=t||{}).objects||t.defaults)&&(r.fields={}),t.defaults&&(r.name="",r.createTime=null,r.updateTime=null),null!=e.name&&e.hasOwnProperty("name")&&(r.name=e.name),e.fields&&(o=Object.keys(e.fields)).length){r.fields={};for(var n=0;n<o.length;++n)r.fields[o[n]]=a.google.firestore.v1beta1.Value.toObject(e.fields[o[n]],t)}return null!=e.createTime&&e.hasOwnProperty("createTime")&&(r.createTime=a.google.protobuf.Timestamp.toObject(e.createTime,t)),null!=e.updateTime&&e.hasOwnProperty("updateTime")&&(r.updateTime=a.google.protobuf.Timestamp.toObject(e.updateTime,t)),r},Se.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},Se.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.v1beta1.Document"},Se),o.Value=(S.prototype.nullValue=null,S.prototype.booleanValue=null,S.prototype.integerValue=null,S.prototype.doubleValue=null,S.prototype.timestampValue=null,S.prototype.stringValue=null,S.prototype.bytesValue=null,S.prototype.referenceValue=null,S.prototype.geoPointValue=null,S.prototype.arrayValue=null,S.prototype.mapValue=null,Object.defineProperty(S.prototype,"valueType",{get:i.oneOfGetter(n=["nullValue","booleanValue","integerValue","doubleValue","timestampValue","stringValue","bytesValue","referenceValue","geoPointValue","arrayValue","mapValue"]),set:i.oneOfSetter(n)}),S.fromObject=function(e){if(e instanceof a.google.firestore.v1beta1.Value)return e;var t=new a.google.firestore.v1beta1.Value;switch(e.nullValue){default:"number"==typeof e.nullValue&&(t.nullValue=e.nullValue);break;case"NULL_VALUE":case 0:t.nullValue=0}if(null!=e.booleanValue&&(t.booleanValue=Boolean(e.booleanValue)),null!=e.integerValue&&(i.Long?(t.integerValue=i.Long.fromValue(e.integerValue)).unsigned=!1:"string"==typeof e.integerValue?t.integerValue=parseInt(e.integerValue,10):"number"==typeof e.integerValue?t.integerValue=e.integerValue:"object"==typeof e.integerValue&&(t.integerValue=new i.LongBits(e.integerValue.low>>>0,e.integerValue.high>>>0).toNumber())),null!=e.doubleValue&&(t.doubleValue=Number(e.doubleValue)),null!=e.timestampValue){if("object"!=typeof e.timestampValue)throw TypeError(".google.firestore.v1beta1.Value.timestampValue: object expected");t.timestampValue=a.google.protobuf.Timestamp.fromObject(e.timestampValue)}if(null!=e.stringValue&&(t.stringValue=String(e.stringValue)),null!=e.bytesValue&&("string"==typeof e.bytesValue?i.base64.decode(e.bytesValue,t.bytesValue=i.newBuffer(i.base64.length(e.bytesValue)),0):0<=e.bytesValue.length&&(t.bytesValue=e.bytesValue)),null!=e.referenceValue&&(t.referenceValue=String(e.referenceValue)),null!=e.geoPointValue){if("object"!=typeof e.geoPointValue)throw TypeError(".google.firestore.v1beta1.Value.geoPointValue: object expected");t.geoPointValue=a.google.type.LatLng.fromObject(e.geoPointValue)}if(null!=e.arrayValue){if("object"!=typeof e.arrayValue)throw TypeError(".google.firestore.v1beta1.Value.arrayValue: object expected");t.arrayValue=a.google.firestore.v1beta1.ArrayValue.fromObject(e.arrayValue)}if(null!=e.mapValue){if("object"!=typeof e.mapValue)throw TypeError(".google.firestore.v1beta1.Value.mapValue: object expected");t.mapValue=a.google.firestore.v1beta1.MapValue.fromObject(e.mapValue)}return t},S.toObject=function(e,t){t=t||{};var o={};return null!=e.booleanValue&&e.hasOwnProperty("booleanValue")&&(o.booleanValue=e.booleanValue,t.oneofs)&&(o.valueType="booleanValue"),null!=e.integerValue&&e.hasOwnProperty("integerValue")&&("number"==typeof e.integerValue?o.integerValue=t.longs===String?String(e.integerValue):e.integerValue:o.integerValue=t.longs===String?i.Long.prototype.toString.call(e.integerValue):t.longs===Number?new i.LongBits(e.integerValue.low>>>0,e.integerValue.high>>>0).toNumber():e.integerValue,t.oneofs)&&(o.valueType="integerValue"),null!=e.doubleValue&&e.hasOwnProperty("doubleValue")&&(o.doubleValue=t.json&&!isFinite(e.doubleValue)?String(e.doubleValue):e.doubleValue,t.oneofs)&&(o.valueType="doubleValue"),null!=e.referenceValue&&e.hasOwnProperty("referenceValue")&&(o.referenceValue=e.referenceValue,t.oneofs)&&(o.valueType="referenceValue"),null!=e.mapValue&&e.hasOwnProperty("mapValue")&&(o.mapValue=a.google.firestore.v1beta1.MapValue.toObject(e.mapValue,t),t.oneofs)&&(o.valueType="mapValue"),null!=e.geoPointValue&&e.hasOwnProperty("geoPointValue")&&(o.geoPointValue=a.google.type.LatLng.toObject(e.geoPointValue,t),t.oneofs)&&(o.valueType="geoPointValue"),null!=e.arrayValue&&e.hasOwnProperty("arrayValue")&&(o.arrayValue=a.google.firestore.v1beta1.ArrayValue.toObject(e.arrayValue,t),t.oneofs)&&(o.valueType="arrayValue"),null!=e.timestampValue&&e.hasOwnProperty("timestampValue")&&(o.timestampValue=a.google.protobuf.Timestamp.toObject(e.timestampValue,t),t.oneofs)&&(o.valueType="timestampValue"),null!=e.nullValue&&e.hasOwnProperty("nullValue")&&(o.nullValue=t.enums!==String||void 0===a.google.protobuf.NullValue[e.nullValue]?e.nullValue:a.google.protobuf.NullValue[e.nullValue],t.oneofs)&&(o.valueType="nullValue"),null!=e.stringValue&&e.hasOwnProperty("stringValue")&&(o.stringValue=e.stringValue,t.oneofs)&&(o.valueType="stringValue"),null!=e.bytesValue&&e.hasOwnProperty("bytesValue")&&(o.bytesValue=t.bytes===String?i.base64.encode(e.bytesValue,0,e.bytesValue.length):t.bytes===Array?Array.prototype.slice.call(e.bytesValue):e.bytesValue,t.oneofs)&&(o.valueType="bytesValue"),o},S.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},S.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.v1beta1.Value"},S),o.ArrayValue=(je.prototype.values=i.emptyArray,je.fromObject=function(e){if(e instanceof a.google.firestore.v1beta1.ArrayValue)return e;var t=new a.google.firestore.v1beta1.ArrayValue;if(e.values){if(!Array.isArray(e.values))throw TypeError(".google.firestore.v1beta1.ArrayValue.values: array expected");t.values=[];for(var o=0;o<e.values.length;++o){if("object"!=typeof e.values[o])throw TypeError(".google.firestore.v1beta1.ArrayValue.values: object expected");t.values[o]=a.google.firestore.v1beta1.Value.fromObject(e.values[o])}}return t},je.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.values=[]),e.values&&e.values.length){o.values=[];for(var r=0;r<e.values.length;++r)o.values[r]=a.google.firestore.v1beta1.Value.toObject(e.values[r],t)}return o},je.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},je.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.v1beta1.ArrayValue"},je),o.MapValue=(Ee.prototype.fields=i.emptyObject,Ee.fromObject=function(e){if(e instanceof a.google.firestore.v1beta1.MapValue)return e;var t=new a.google.firestore.v1beta1.MapValue;if(e.fields){if("object"!=typeof e.fields)throw TypeError(".google.firestore.v1beta1.MapValue.fields: object expected");t.fields={};for(var o=Object.keys(e.fields),r=0;r<o.length;++r){if("object"!=typeof e.fields[o[r]])throw TypeError(".google.firestore.v1beta1.MapValue.fields: object expected");t.fields[o[r]]=a.google.firestore.v1beta1.Value.fromObject(e.fields[o[r]])}}return t},Ee.toObject=function(e,t){var o,r={};if(((t=t||{}).objects||t.defaults)&&(r.fields={}),e.fields&&(o=Object.keys(e.fields)).length){r.fields={};for(var n=0;n<o.length;++n)r.fields[o[n]]=a.google.firestore.v1beta1.Value.toObject(e.fields[o[n]],t)}return r},Ee.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},Ee.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.v1beta1.MapValue"},Ee),o.Firestore=((j.prototype=Object.create(r.rpc.Service.prototype)).constructor=j,Object.defineProperty(j.prototype.getDocument=function e(t,o){return this.rpcCall(e,a.google.firestore.v1beta1.GetDocumentRequest,a.google.firestore.v1beta1.Document,t,o)},"name",{value:"GetDocument"}),Object.defineProperty(j.prototype.listDocuments=function e(t,o){return this.rpcCall(e,a.google.firestore.v1beta1.ListDocumentsRequest,a.google.firestore.v1beta1.ListDocumentsResponse,t,o)},"name",{value:"ListDocuments"}),Object.defineProperty(j.prototype.updateDocument=function e(t,o){return this.rpcCall(e,a.google.firestore.v1beta1.UpdateDocumentRequest,a.google.firestore.v1beta1.Document,t,o)},"name",{value:"UpdateDocument"}),Object.defineProperty(j.prototype.deleteDocument=function e(t,o){return this.rpcCall(e,a.google.firestore.v1beta1.DeleteDocumentRequest,a.google.protobuf.Empty,t,o)},"name",{value:"DeleteDocument"}),Object.defineProperty(j.prototype.batchGetDocuments=function e(t,o){return this.rpcCall(e,a.google.firestore.v1beta1.BatchGetDocumentsRequest,a.google.firestore.v1beta1.BatchGetDocumentsResponse,t,o)},"name",{value:"BatchGetDocuments"}),Object.defineProperty(j.prototype.beginTransaction=function e(t,o){return this.rpcCall(e,a.google.firestore.v1beta1.BeginTransactionRequest,a.google.firestore.v1beta1.BeginTransactionResponse,t,o)},"name",{value:"BeginTransaction"}),Object.defineProperty(j.prototype.commit=function e(t,o){return this.rpcCall(e,a.google.firestore.v1beta1.CommitRequest,a.google.firestore.v1beta1.CommitResponse,t,o)},"name",{value:"Commit"}),Object.defineProperty(j.prototype.rollback=function e(t,o){return this.rpcCall(e,a.google.firestore.v1beta1.RollbackRequest,a.google.protobuf.Empty,t,o)},"name",{value:"Rollback"}),Object.defineProperty(j.prototype.runQuery=function e(t,o){return this.rpcCall(e,a.google.firestore.v1beta1.RunQueryRequest,a.google.firestore.v1beta1.RunQueryResponse,t,o)},"name",{value:"RunQuery"}),Object.defineProperty(j.prototype.partitionQuery=function e(t,o){return this.rpcCall(e,a.google.firestore.v1beta1.PartitionQueryRequest,a.google.firestore.v1beta1.PartitionQueryResponse,t,o)},"name",{value:"PartitionQuery"}),Object.defineProperty(j.prototype.write=function e(t,o){return this.rpcCall(e,a.google.firestore.v1beta1.WriteRequest,a.google.firestore.v1beta1.WriteResponse,t,o)},"name",{value:"Write"}),Object.defineProperty(j.prototype.listen=function e(t,o){return this.rpcCall(e,a.google.firestore.v1beta1.ListenRequest,a.google.firestore.v1beta1.ListenResponse,t,o)},"name",{value:"Listen"}),Object.defineProperty(j.prototype.listCollectionIds=function e(t,o){return this.rpcCall(e,a.google.firestore.v1beta1.ListCollectionIdsRequest,a.google.firestore.v1beta1.ListCollectionIdsResponse,t,o)},"name",{value:"ListCollectionIds"}),Object.defineProperty(j.prototype.batchWrite=function e(t,o){return this.rpcCall(e,a.google.firestore.v1beta1.BatchWriteRequest,a.google.firestore.v1beta1.BatchWriteResponse,t,o)},"name",{value:"BatchWrite"}),Object.defineProperty(j.prototype.createDocument=function e(t,o){return this.rpcCall(e,a.google.firestore.v1beta1.CreateDocumentRequest,a.google.firestore.v1beta1.Document,t,o)},"name",{value:"CreateDocument"}),j),o.GetDocumentRequest=(Ne.prototype.name="",Ne.prototype.mask=null,Ne.prototype.transaction=null,Ne.prototype.readTime=null,Object.defineProperty(Ne.prototype,"consistencySelector",{get:i.oneOfGetter(n=["transaction","readTime"]),set:i.oneOfSetter(n)}),Ne.fromObject=function(e){if(e instanceof a.google.firestore.v1beta1.GetDocumentRequest)return e;var t=new a.google.firestore.v1beta1.GetDocumentRequest;if(null!=e.name&&(t.name=String(e.name)),null!=e.mask){if("object"!=typeof e.mask)throw TypeError(".google.firestore.v1beta1.GetDocumentRequest.mask: object expected");t.mask=a.google.firestore.v1beta1.DocumentMask.fromObject(e.mask)}if(null!=e.transaction&&("string"==typeof e.transaction?i.base64.decode(e.transaction,t.transaction=i.newBuffer(i.base64.length(e.transaction)),0):0<=e.transaction.length&&(t.transaction=e.transaction)),null!=e.readTime){if("object"!=typeof e.readTime)throw TypeError(".google.firestore.v1beta1.GetDocumentRequest.readTime: object expected");t.readTime=a.google.protobuf.Timestamp.fromObject(e.readTime)}return t},Ne.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.name="",o.mask=null),null!=e.name&&e.hasOwnProperty("name")&&(o.name=e.name),null!=e.mask&&e.hasOwnProperty("mask")&&(o.mask=a.google.firestore.v1beta1.DocumentMask.toObject(e.mask,t)),null!=e.transaction&&e.hasOwnProperty("transaction")&&(o.transaction=t.bytes===String?i.base64.encode(e.transaction,0,e.transaction.length):t.bytes===Array?Array.prototype.slice.call(e.transaction):e.transaction,t.oneofs)&&(o.consistencySelector="transaction"),null!=e.readTime&&e.hasOwnProperty("readTime")&&(o.readTime=a.google.protobuf.Timestamp.toObject(e.readTime,t),t.oneofs)&&(o.consistencySelector="readTime"),o},Ne.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},Ne.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.v1beta1.GetDocumentRequest"},Ne),o.ListDocumentsRequest=(E.prototype.parent="",E.prototype.collectionId="",E.prototype.pageSize=0,E.prototype.pageToken="",E.prototype.orderBy="",E.prototype.mask=null,E.prototype.transaction=null,E.prototype.readTime=null,E.prototype.showMissing=!1,Object.defineProperty(E.prototype,"consistencySelector",{get:i.oneOfGetter(n=["transaction","readTime"]),set:i.oneOfSetter(n)}),E.fromObject=function(e){if(e instanceof a.google.firestore.v1beta1.ListDocumentsRequest)return e;var t=new a.google.firestore.v1beta1.ListDocumentsRequest;if(null!=e.parent&&(t.parent=String(e.parent)),null!=e.collectionId&&(t.collectionId=String(e.collectionId)),null!=e.pageSize&&(t.pageSize=0|e.pageSize),null!=e.pageToken&&(t.pageToken=String(e.pageToken)),null!=e.orderBy&&(t.orderBy=String(e.orderBy)),null!=e.mask){if("object"!=typeof e.mask)throw TypeError(".google.firestore.v1beta1.ListDocumentsRequest.mask: object expected");t.mask=a.google.firestore.v1beta1.DocumentMask.fromObject(e.mask)}if(null!=e.transaction&&("string"==typeof e.transaction?i.base64.decode(e.transaction,t.transaction=i.newBuffer(i.base64.length(e.transaction)),0):0<=e.transaction.length&&(t.transaction=e.transaction)),null!=e.readTime){if("object"!=typeof e.readTime)throw TypeError(".google.firestore.v1beta1.ListDocumentsRequest.readTime: object expected");t.readTime=a.google.protobuf.Timestamp.fromObject(e.readTime)}return null!=e.showMissing&&(t.showMissing=Boolean(e.showMissing)),t},E.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.parent="",o.collectionId="",o.pageSize=0,o.pageToken="",o.orderBy="",o.mask=null,o.showMissing=!1),null!=e.parent&&e.hasOwnProperty("parent")&&(o.parent=e.parent),null!=e.collectionId&&e.hasOwnProperty("collectionId")&&(o.collectionId=e.collectionId),null!=e.pageSize&&e.hasOwnProperty("pageSize")&&(o.pageSize=e.pageSize),null!=e.pageToken&&e.hasOwnProperty("pageToken")&&(o.pageToken=e.pageToken),null!=e.orderBy&&e.hasOwnProperty("orderBy")&&(o.orderBy=e.orderBy),null!=e.mask&&e.hasOwnProperty("mask")&&(o.mask=a.google.firestore.v1beta1.DocumentMask.toObject(e.mask,t)),null!=e.transaction&&e.hasOwnProperty("transaction")&&(o.transaction=t.bytes===String?i.base64.encode(e.transaction,0,e.transaction.length):t.bytes===Array?Array.prototype.slice.call(e.transaction):e.transaction,t.oneofs)&&(o.consistencySelector="transaction"),null!=e.readTime&&e.hasOwnProperty("readTime")&&(o.readTime=a.google.protobuf.Timestamp.toObject(e.readTime,t),t.oneofs)&&(o.consistencySelector="readTime"),null!=e.showMissing&&e.hasOwnProperty("showMissing")&&(o.showMissing=e.showMissing),o},E.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},E.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.v1beta1.ListDocumentsRequest"},E),o.ListDocumentsResponse=(we.prototype.documents=i.emptyArray,we.prototype.nextPageToken="",we.fromObject=function(e){if(e instanceof a.google.firestore.v1beta1.ListDocumentsResponse)return e;var t=new a.google.firestore.v1beta1.ListDocumentsResponse;if(e.documents){if(!Array.isArray(e.documents))throw TypeError(".google.firestore.v1beta1.ListDocumentsResponse.documents: array expected");t.documents=[];for(var o=0;o<e.documents.length;++o){if("object"!=typeof e.documents[o])throw TypeError(".google.firestore.v1beta1.ListDocumentsResponse.documents: object expected");t.documents[o]=a.google.firestore.v1beta1.Document.fromObject(e.documents[o])}}return null!=e.nextPageToken&&(t.nextPageToken=String(e.nextPageToken)),t},we.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.documents=[]),t.defaults&&(o.nextPageToken=""),e.documents&&e.documents.length){o.documents=[];for(var r=0;r<e.documents.length;++r)o.documents[r]=a.google.firestore.v1beta1.Document.toObject(e.documents[r],t)}return null!=e.nextPageToken&&e.hasOwnProperty("nextPageToken")&&(o.nextPageToken=e.nextPageToken),o},we.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},we.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.v1beta1.ListDocumentsResponse"},we),o.CreateDocumentRequest=(Pe.prototype.parent="",Pe.prototype.collectionId="",Pe.prototype.documentId="",Pe.prototype.document=null,Pe.prototype.mask=null,Pe.fromObject=function(e){if(e instanceof a.google.firestore.v1beta1.CreateDocumentRequest)return e;var t=new a.google.firestore.v1beta1.CreateDocumentRequest;if(null!=e.parent&&(t.parent=String(e.parent)),null!=e.collectionId&&(t.collectionId=String(e.collectionId)),null!=e.documentId&&(t.documentId=String(e.documentId)),null!=e.document){if("object"!=typeof e.document)throw TypeError(".google.firestore.v1beta1.CreateDocumentRequest.document: object expected");t.document=a.google.firestore.v1beta1.Document.fromObject(e.document)}if(null!=e.mask){if("object"!=typeof e.mask)throw TypeError(".google.firestore.v1beta1.CreateDocumentRequest.mask: object expected");t.mask=a.google.firestore.v1beta1.DocumentMask.fromObject(e.mask)}return t},Pe.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.parent="",o.collectionId="",o.documentId="",o.document=null,o.mask=null),null!=e.parent&&e.hasOwnProperty("parent")&&(o.parent=e.parent),null!=e.collectionId&&e.hasOwnProperty("collectionId")&&(o.collectionId=e.collectionId),null!=e.documentId&&e.hasOwnProperty("documentId")&&(o.documentId=e.documentId),null!=e.document&&e.hasOwnProperty("document")&&(o.document=a.google.firestore.v1beta1.Document.toObject(e.document,t)),null!=e.mask&&e.hasOwnProperty("mask")&&(o.mask=a.google.firestore.v1beta1.DocumentMask.toObject(e.mask,t)),o},Pe.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},Pe.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.v1beta1.CreateDocumentRequest"},Pe),o.UpdateDocumentRequest=(De.prototype.document=null,De.prototype.updateMask=null,De.prototype.mask=null,De.prototype.currentDocument=null,De.fromObject=function(e){if(e instanceof a.google.firestore.v1beta1.UpdateDocumentRequest)return e;var t=new a.google.firestore.v1beta1.UpdateDocumentRequest;if(null!=e.document){if("object"!=typeof e.document)throw TypeError(".google.firestore.v1beta1.UpdateDocumentRequest.document: object expected");t.document=a.google.firestore.v1beta1.Document.fromObject(e.document)}if(null!=e.updateMask){if("object"!=typeof e.updateMask)throw TypeError(".google.firestore.v1beta1.UpdateDocumentRequest.updateMask: object expected");t.updateMask=a.google.firestore.v1beta1.DocumentMask.fromObject(e.updateMask)}if(null!=e.mask){if("object"!=typeof e.mask)throw TypeError(".google.firestore.v1beta1.UpdateDocumentRequest.mask: object expected");t.mask=a.google.firestore.v1beta1.DocumentMask.fromObject(e.mask)}if(null!=e.currentDocument){if("object"!=typeof e.currentDocument)throw TypeError(".google.firestore.v1beta1.UpdateDocumentRequest.currentDocument: object expected");t.currentDocument=a.google.firestore.v1beta1.Precondition.fromObject(e.currentDocument)}return t},De.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.document=null,o.updateMask=null,o.mask=null,o.currentDocument=null),null!=e.document&&e.hasOwnProperty("document")&&(o.document=a.google.firestore.v1beta1.Document.toObject(e.document,t)),null!=e.updateMask&&e.hasOwnProperty("updateMask")&&(o.updateMask=a.google.firestore.v1beta1.DocumentMask.toObject(e.updateMask,t)),null!=e.mask&&e.hasOwnProperty("mask")&&(o.mask=a.google.firestore.v1beta1.DocumentMask.toObject(e.mask,t)),null!=e.currentDocument&&e.hasOwnProperty("currentDocument")&&(o.currentDocument=a.google.firestore.v1beta1.Precondition.toObject(e.currentDocument,t)),o},De.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},De.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.v1beta1.UpdateDocumentRequest"},De),o.DeleteDocumentRequest=(Re.prototype.name="",Re.prototype.currentDocument=null,Re.fromObject=function(e){if(e instanceof a.google.firestore.v1beta1.DeleteDocumentRequest)return e;var t=new a.google.firestore.v1beta1.DeleteDocumentRequest;if(null!=e.name&&(t.name=String(e.name)),null!=e.currentDocument){if("object"!=typeof e.currentDocument)throw TypeError(".google.firestore.v1beta1.DeleteDocumentRequest.currentDocument: object expected");t.currentDocument=a.google.firestore.v1beta1.Precondition.fromObject(e.currentDocument)}return t},Re.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.name="",o.currentDocument=null),null!=e.name&&e.hasOwnProperty("name")&&(o.name=e.name),null!=e.currentDocument&&e.hasOwnProperty("currentDocument")&&(o.currentDocument=a.google.firestore.v1beta1.Precondition.toObject(e.currentDocument,t)),o},Re.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},Re.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.v1beta1.DeleteDocumentRequest"},Re),o.BatchGetDocumentsRequest=(N.prototype.database="",N.prototype.documents=i.emptyArray,N.prototype.mask=null,N.prototype.transaction=null,N.prototype.newTransaction=null,N.prototype.readTime=null,Object.defineProperty(N.prototype,"consistencySelector",{get:i.oneOfGetter(n=["transaction","newTransaction","readTime"]),set:i.oneOfSetter(n)}),N.fromObject=function(e){if(e instanceof a.google.firestore.v1beta1.BatchGetDocumentsRequest)return e;var t=new a.google.firestore.v1beta1.BatchGetDocumentsRequest;if(null!=e.database&&(t.database=String(e.database)),e.documents){if(!Array.isArray(e.documents))throw TypeError(".google.firestore.v1beta1.BatchGetDocumentsRequest.documents: array expected");t.documents=[];for(var o=0;o<e.documents.length;++o)t.documents[o]=String(e.documents[o])}if(null!=e.mask){if("object"!=typeof e.mask)throw TypeError(".google.firestore.v1beta1.BatchGetDocumentsRequest.mask: object expected");t.mask=a.google.firestore.v1beta1.DocumentMask.fromObject(e.mask)}if(null!=e.transaction&&("string"==typeof e.transaction?i.base64.decode(e.transaction,t.transaction=i.newBuffer(i.base64.length(e.transaction)),0):0<=e.transaction.length&&(t.transaction=e.transaction)),null!=e.newTransaction){if("object"!=typeof e.newTransaction)throw TypeError(".google.firestore.v1beta1.BatchGetDocumentsRequest.newTransaction: object expected");t.newTransaction=a.google.firestore.v1beta1.TransactionOptions.fromObject(e.newTransaction)}if(null!=e.readTime){if("object"!=typeof e.readTime)throw TypeError(".google.firestore.v1beta1.BatchGetDocumentsRequest.readTime: object expected");t.readTime=a.google.protobuf.Timestamp.fromObject(e.readTime)}return t},N.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.documents=[]),t.defaults&&(o.database="",o.mask=null),null!=e.database&&e.hasOwnProperty("database")&&(o.database=e.database),e.documents&&e.documents.length){o.documents=[];for(var r=0;r<e.documents.length;++r)o.documents[r]=e.documents[r]}return null!=e.mask&&e.hasOwnProperty("mask")&&(o.mask=a.google.firestore.v1beta1.DocumentMask.toObject(e.mask,t)),null!=e.transaction&&e.hasOwnProperty("transaction")&&(o.transaction=t.bytes===String?i.base64.encode(e.transaction,0,e.transaction.length):t.bytes===Array?Array.prototype.slice.call(e.transaction):e.transaction,t.oneofs)&&(o.consistencySelector="transaction"),null!=e.newTransaction&&e.hasOwnProperty("newTransaction")&&(o.newTransaction=a.google.firestore.v1beta1.TransactionOptions.toObject(e.newTransaction,t),t.oneofs)&&(o.consistencySelector="newTransaction"),null!=e.readTime&&e.hasOwnProperty("readTime")&&(o.readTime=a.google.protobuf.Timestamp.toObject(e.readTime,t),t.oneofs)&&(o.consistencySelector="readTime"),o},N.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},N.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.v1beta1.BatchGetDocumentsRequest"},N),o.BatchGetDocumentsResponse=(Ie.prototype.found=null,Ie.prototype.missing=null,Ie.prototype.transaction=i.newBuffer([]),Ie.prototype.readTime=null,Object.defineProperty(Ie.prototype,"result",{get:i.oneOfGetter(n=["found","missing"]),set:i.oneOfSetter(n)}),Ie.fromObject=function(e){if(e instanceof a.google.firestore.v1beta1.BatchGetDocumentsResponse)return e;var t=new a.google.firestore.v1beta1.BatchGetDocumentsResponse;if(null!=e.found){if("object"!=typeof e.found)throw TypeError(".google.firestore.v1beta1.BatchGetDocumentsResponse.found: object expected");t.found=a.google.firestore.v1beta1.Document.fromObject(e.found)}if(null!=e.missing&&(t.missing=String(e.missing)),null!=e.transaction&&("string"==typeof e.transaction?i.base64.decode(e.transaction,t.transaction=i.newBuffer(i.base64.length(e.transaction)),0):0<=e.transaction.length&&(t.transaction=e.transaction)),null!=e.readTime){if("object"!=typeof e.readTime)throw TypeError(".google.firestore.v1beta1.BatchGetDocumentsResponse.readTime: object expected");t.readTime=a.google.protobuf.Timestamp.fromObject(e.readTime)}return t},Ie.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(t.bytes===String?o.transaction="":(o.transaction=[],t.bytes!==Array&&(o.transaction=i.newBuffer(o.transaction))),o.readTime=null),null!=e.found&&e.hasOwnProperty("found")&&(o.found=a.google.firestore.v1beta1.Document.toObject(e.found,t),t.oneofs)&&(o.result="found"),null!=e.missing&&e.hasOwnProperty("missing")&&(o.missing=e.missing,t.oneofs)&&(o.result="missing"),null!=e.transaction&&e.hasOwnProperty("transaction")&&(o.transaction=t.bytes===String?i.base64.encode(e.transaction,0,e.transaction.length):t.bytes===Array?Array.prototype.slice.call(e.transaction):e.transaction),null!=e.readTime&&e.hasOwnProperty("readTime")&&(o.readTime=a.google.protobuf.Timestamp.toObject(e.readTime,t)),o},Ie.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},Ie.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.v1beta1.BatchGetDocumentsResponse"},Ie),o.BeginTransactionRequest=(Ae.prototype.database="",Ae.prototype.options=null,Ae.fromObject=function(e){if(e instanceof a.google.firestore.v1beta1.BeginTransactionRequest)return e;var t=new a.google.firestore.v1beta1.BeginTransactionRequest;if(null!=e.database&&(t.database=String(e.database)),null!=e.options){if("object"!=typeof e.options)throw TypeError(".google.firestore.v1beta1.BeginTransactionRequest.options: object expected");t.options=a.google.firestore.v1beta1.TransactionOptions.fromObject(e.options)}return t},Ae.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.database="",o.options=null),null!=e.database&&e.hasOwnProperty("database")&&(o.database=e.database),null!=e.options&&e.hasOwnProperty("options")&&(o.options=a.google.firestore.v1beta1.TransactionOptions.toObject(e.options,t)),o},Ae.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},Ae.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.v1beta1.BeginTransactionRequest"},Ae),o.BeginTransactionResponse=(ke.prototype.transaction=i.newBuffer([]),ke.fromObject=function(e){var t;return e instanceof a.google.firestore.v1beta1.BeginTransactionResponse?e:(t=new a.google.firestore.v1beta1.BeginTransactionResponse,null!=e.transaction&&("string"==typeof e.transaction?i.base64.decode(e.transaction,t.transaction=i.newBuffer(i.base64.length(e.transaction)),0):0<=e.transaction.length&&(t.transaction=e.transaction)),t)},ke.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(t.bytes===String?o.transaction="":(o.transaction=[],t.bytes!==Array&&(o.transaction=i.newBuffer(o.transaction)))),null!=e.transaction&&e.hasOwnProperty("transaction")&&(o.transaction=t.bytes===String?i.base64.encode(e.transaction,0,e.transaction.length):t.bytes===Array?Array.prototype.slice.call(e.transaction):e.transaction),o},ke.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},ke.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.v1beta1.BeginTransactionResponse"},ke),o.CommitRequest=(_e.prototype.database="",_e.prototype.writes=i.emptyArray,_e.prototype.transaction=i.newBuffer([]),_e.fromObject=function(e){if(e instanceof a.google.firestore.v1beta1.CommitRequest)return e;var t=new a.google.firestore.v1beta1.CommitRequest;if(null!=e.database&&(t.database=String(e.database)),e.writes){if(!Array.isArray(e.writes))throw TypeError(".google.firestore.v1beta1.CommitRequest.writes: array expected");t.writes=[];for(var o=0;o<e.writes.length;++o){if("object"!=typeof e.writes[o])throw TypeError(".google.firestore.v1beta1.CommitRequest.writes: object expected");t.writes[o]=a.google.firestore.v1beta1.Write.fromObject(e.writes[o])}}return null!=e.transaction&&("string"==typeof e.transaction?i.base64.decode(e.transaction,t.transaction=i.newBuffer(i.base64.length(e.transaction)),0):0<=e.transaction.length&&(t.transaction=e.transaction)),t},_e.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.writes=[]),t.defaults&&(o.database="",t.bytes===String?o.transaction="":(o.transaction=[],t.bytes!==Array&&(o.transaction=i.newBuffer(o.transaction)))),null!=e.database&&e.hasOwnProperty("database")&&(o.database=e.database),e.writes&&e.writes.length){o.writes=[];for(var r=0;r<e.writes.length;++r)o.writes[r]=a.google.firestore.v1beta1.Write.toObject(e.writes[r],t)}return null!=e.transaction&&e.hasOwnProperty("transaction")&&(o.transaction=t.bytes===String?i.base64.encode(e.transaction,0,e.transaction.length):t.bytes===Array?Array.prototype.slice.call(e.transaction):e.transaction),o},_e.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},_e.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.v1beta1.CommitRequest"},_e),o.CommitResponse=(Ce.prototype.writeResults=i.emptyArray,Ce.prototype.commitTime=null,Ce.fromObject=function(e){if(e instanceof a.google.firestore.v1beta1.CommitResponse)return e;var t=new a.google.firestore.v1beta1.CommitResponse;if(e.writeResults){if(!Array.isArray(e.writeResults))throw TypeError(".google.firestore.v1beta1.CommitResponse.writeResults: array expected");t.writeResults=[];for(var o=0;o<e.writeResults.length;++o){if("object"!=typeof e.writeResults[o])throw TypeError(".google.firestore.v1beta1.CommitResponse.writeResults: object expected");t.writeResults[o]=a.google.firestore.v1beta1.WriteResult.fromObject(e.writeResults[o])}}if(null!=e.commitTime){if("object"!=typeof e.commitTime)throw TypeError(".google.firestore.v1beta1.CommitResponse.commitTime: object expected");t.commitTime=a.google.protobuf.Timestamp.fromObject(e.commitTime)}return t},Ce.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.writeResults=[]),t.defaults&&(o.commitTime=null),e.writeResults&&e.writeResults.length){o.writeResults=[];for(var r=0;r<e.writeResults.length;++r)o.writeResults[r]=a.google.firestore.v1beta1.WriteResult.toObject(e.writeResults[r],t)}return null!=e.commitTime&&e.hasOwnProperty("commitTime")&&(o.commitTime=a.google.protobuf.Timestamp.toObject(e.commitTime,t)),o},Ce.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},Ce.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.v1beta1.CommitResponse"},Ce),o.RollbackRequest=(Fe.prototype.database="",Fe.prototype.transaction=i.newBuffer([]),Fe.fromObject=function(e){var t;return e instanceof a.google.firestore.v1beta1.RollbackRequest?e:(t=new a.google.firestore.v1beta1.RollbackRequest,null!=e.database&&(t.database=String(e.database)),null!=e.transaction&&("string"==typeof e.transaction?i.base64.decode(e.transaction,t.transaction=i.newBuffer(i.base64.length(e.transaction)),0):0<=e.transaction.length&&(t.transaction=e.transaction)),t)},Fe.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.database="",t.bytes===String?o.transaction="":(o.transaction=[],t.bytes!==Array&&(o.transaction=i.newBuffer(o.transaction)))),null!=e.database&&e.hasOwnProperty("database")&&(o.database=e.database),null!=e.transaction&&e.hasOwnProperty("transaction")&&(o.transaction=t.bytes===String?i.base64.encode(e.transaction,0,e.transaction.length):t.bytes===Array?Array.prototype.slice.call(e.transaction):e.transaction),o},Fe.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},Fe.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.v1beta1.RollbackRequest"},Fe),o.RunQueryRequest=(w.prototype.parent="",w.prototype.structuredQuery=null,w.prototype.transaction=null,w.prototype.newTransaction=null,w.prototype.readTime=null,Object.defineProperty(w.prototype,"queryType",{get:i.oneOfGetter(n=["structuredQuery"]),set:i.oneOfSetter(n)}),Object.defineProperty(w.prototype,"consistencySelector",{get:i.oneOfGetter(n=["transaction","newTransaction","readTime"]),set:i.oneOfSetter(n)}),w.fromObject=function(e){if(e instanceof a.google.firestore.v1beta1.RunQueryRequest)return e;var t=new a.google.firestore.v1beta1.RunQueryRequest;if(null!=e.parent&&(t.parent=String(e.parent)),null!=e.structuredQuery){if("object"!=typeof e.structuredQuery)throw TypeError(".google.firestore.v1beta1.RunQueryRequest.structuredQuery: object expected");t.structuredQuery=a.google.firestore.v1beta1.StructuredQuery.fromObject(e.structuredQuery)}if(null!=e.transaction&&("string"==typeof e.transaction?i.base64.decode(e.transaction,t.transaction=i.newBuffer(i.base64.length(e.transaction)),0):0<=e.transaction.length&&(t.transaction=e.transaction)),null!=e.newTransaction){if("object"!=typeof e.newTransaction)throw TypeError(".google.firestore.v1beta1.RunQueryRequest.newTransaction: object expected");t.newTransaction=a.google.firestore.v1beta1.TransactionOptions.fromObject(e.newTransaction)}if(null!=e.readTime){if("object"!=typeof e.readTime)throw TypeError(".google.firestore.v1beta1.RunQueryRequest.readTime: object expected");t.readTime=a.google.protobuf.Timestamp.fromObject(e.readTime)}return t},w.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.parent=""),null!=e.parent&&e.hasOwnProperty("parent")&&(o.parent=e.parent),null!=e.structuredQuery&&e.hasOwnProperty("structuredQuery")&&(o.structuredQuery=a.google.firestore.v1beta1.StructuredQuery.toObject(e.structuredQuery,t),t.oneofs)&&(o.queryType="structuredQuery"),null!=e.transaction&&e.hasOwnProperty("transaction")&&(o.transaction=t.bytes===String?i.base64.encode(e.transaction,0,e.transaction.length):t.bytes===Array?Array.prototype.slice.call(e.transaction):e.transaction,t.oneofs)&&(o.consistencySelector="transaction"),null!=e.newTransaction&&e.hasOwnProperty("newTransaction")&&(o.newTransaction=a.google.firestore.v1beta1.TransactionOptions.toObject(e.newTransaction,t),t.oneofs)&&(o.consistencySelector="newTransaction"),null!=e.readTime&&e.hasOwnProperty("readTime")&&(o.readTime=a.google.protobuf.Timestamp.toObject(e.readTime,t),t.oneofs)&&(o.consistencySelector="readTime"),o},w.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},w.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.v1beta1.RunQueryRequest"},w),o.RunQueryResponse=(xe.prototype.transaction=i.newBuffer([]),xe.prototype.document=null,xe.prototype.readTime=null,xe.prototype.skippedResults=0,xe.fromObject=function(e){if(e instanceof a.google.firestore.v1beta1.RunQueryResponse)return e;var t=new a.google.firestore.v1beta1.RunQueryResponse;if(null!=e.transaction&&("string"==typeof e.transaction?i.base64.decode(e.transaction,t.transaction=i.newBuffer(i.base64.length(e.transaction)),0):0<=e.transaction.length&&(t.transaction=e.transaction)),null!=e.document){if("object"!=typeof e.document)throw TypeError(".google.firestore.v1beta1.RunQueryResponse.document: object expected");t.document=a.google.firestore.v1beta1.Document.fromObject(e.document)}if(null!=e.readTime){if("object"!=typeof e.readTime)throw TypeError(".google.firestore.v1beta1.RunQueryResponse.readTime: object expected");t.readTime=a.google.protobuf.Timestamp.fromObject(e.readTime)}return null!=e.skippedResults&&(t.skippedResults=0|e.skippedResults),t},xe.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.document=null,t.bytes===String?o.transaction="":(o.transaction=[],t.bytes!==Array&&(o.transaction=i.newBuffer(o.transaction))),o.readTime=null,o.skippedResults=0),null!=e.document&&e.hasOwnProperty("document")&&(o.document=a.google.firestore.v1beta1.Document.toObject(e.document,t)),null!=e.transaction&&e.hasOwnProperty("transaction")&&(o.transaction=t.bytes===String?i.base64.encode(e.transaction,0,e.transaction.length):t.bytes===Array?Array.prototype.slice.call(e.transaction):e.transaction),null!=e.readTime&&e.hasOwnProperty("readTime")&&(o.readTime=a.google.protobuf.Timestamp.toObject(e.readTime,t)),null!=e.skippedResults&&e.hasOwnProperty("skippedResults")&&(o.skippedResults=e.skippedResults),o},xe.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},xe.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.v1beta1.RunQueryResponse"},xe),o.PartitionQueryRequest=(Ve.prototype.parent="",Ve.prototype.structuredQuery=null,Ve.prototype.partitionCount=i.Long?i.Long.fromBits(0,0,!1):0,Ve.prototype.pageToken="",Ve.prototype.pageSize=0,Object.defineProperty(Ve.prototype,"queryType",{get:i.oneOfGetter(n=["structuredQuery"]),set:i.oneOfSetter(n)}),Ve.fromObject=function(e){if(e instanceof a.google.firestore.v1beta1.PartitionQueryRequest)return e;var t=new a.google.firestore.v1beta1.PartitionQueryRequest;if(null!=e.parent&&(t.parent=String(e.parent)),null!=e.structuredQuery){if("object"!=typeof e.structuredQuery)throw TypeError(".google.firestore.v1beta1.PartitionQueryRequest.structuredQuery: object expected");t.structuredQuery=a.google.firestore.v1beta1.StructuredQuery.fromObject(e.structuredQuery)}return null!=e.partitionCount&&(i.Long?(t.partitionCount=i.Long.fromValue(e.partitionCount)).unsigned=!1:"string"==typeof e.partitionCount?t.partitionCount=parseInt(e.partitionCount,10):"number"==typeof e.partitionCount?t.partitionCount=e.partitionCount:"object"==typeof e.partitionCount&&(t.partitionCount=new i.LongBits(e.partitionCount.low>>>0,e.partitionCount.high>>>0).toNumber())),null!=e.pageToken&&(t.pageToken=String(e.pageToken)),null!=e.pageSize&&(t.pageSize=0|e.pageSize),t},Ve.toObject=function(e,t){var o,r={};return(t=t||{}).defaults&&(r.parent="",i.Long?(o=new i.Long(0,0,!1),r.partitionCount=t.longs===String?o.toString():t.longs===Number?o.toNumber():o):r.partitionCount=t.longs===String?"0":0,r.pageToken="",r.pageSize=0),null!=e.parent&&e.hasOwnProperty("parent")&&(r.parent=e.parent),null!=e.structuredQuery&&e.hasOwnProperty("structuredQuery")&&(r.structuredQuery=a.google.firestore.v1beta1.StructuredQuery.toObject(e.structuredQuery,t),t.oneofs)&&(r.queryType="structuredQuery"),null!=e.partitionCount&&e.hasOwnProperty("partitionCount")&&("number"==typeof e.partitionCount?r.partitionCount=t.longs===String?String(e.partitionCount):e.partitionCount:r.partitionCount=t.longs===String?i.Long.prototype.toString.call(e.partitionCount):t.longs===Number?new i.LongBits(e.partitionCount.low>>>0,e.partitionCount.high>>>0).toNumber():e.partitionCount),null!=e.pageToken&&e.hasOwnProperty("pageToken")&&(r.pageToken=e.pageToken),null!=e.pageSize&&e.hasOwnProperty("pageSize")&&(r.pageSize=e.pageSize),r},Ve.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},Ve.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.v1beta1.PartitionQueryRequest"},Ve),o.PartitionQueryResponse=(Le.prototype.partitions=i.emptyArray,Le.prototype.nextPageToken="",Le.fromObject=function(e){if(e instanceof a.google.firestore.v1beta1.PartitionQueryResponse)return e;var t=new a.google.firestore.v1beta1.PartitionQueryResponse;if(e.partitions){if(!Array.isArray(e.partitions))throw TypeError(".google.firestore.v1beta1.PartitionQueryResponse.partitions: array expected");t.partitions=[];for(var o=0;o<e.partitions.length;++o){if("object"!=typeof e.partitions[o])throw TypeError(".google.firestore.v1beta1.PartitionQueryResponse.partitions: object expected");t.partitions[o]=a.google.firestore.v1beta1.Cursor.fromObject(e.partitions[o])}}return null!=e.nextPageToken&&(t.nextPageToken=String(e.nextPageToken)),t},Le.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.partitions=[]),t.defaults&&(o.nextPageToken=""),e.partitions&&e.partitions.length){o.partitions=[];for(var r=0;r<e.partitions.length;++r)o.partitions[r]=a.google.firestore.v1beta1.Cursor.toObject(e.partitions[r],t)}return null!=e.nextPageToken&&e.hasOwnProperty("nextPageToken")&&(o.nextPageToken=e.nextPageToken),o},Le.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},Le.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.v1beta1.PartitionQueryResponse"},Le),o.WriteRequest=(Ue.prototype.database="",Ue.prototype.streamId="",Ue.prototype.writes=i.emptyArray,Ue.prototype.streamToken=i.newBuffer([]),Ue.prototype.labels=i.emptyObject,Ue.fromObject=function(e){if(e instanceof a.google.firestore.v1beta1.WriteRequest)return e;var t=new a.google.firestore.v1beta1.WriteRequest;if(null!=e.database&&(t.database=String(e.database)),null!=e.streamId&&(t.streamId=String(e.streamId)),e.writes){if(!Array.isArray(e.writes))throw TypeError(".google.firestore.v1beta1.WriteRequest.writes: array expected");t.writes=[];for(var o=0;o<e.writes.length;++o){if("object"!=typeof e.writes[o])throw TypeError(".google.firestore.v1beta1.WriteRequest.writes: object expected");t.writes[o]=a.google.firestore.v1beta1.Write.fromObject(e.writes[o])}}if(null!=e.streamToken&&("string"==typeof e.streamToken?i.base64.decode(e.streamToken,t.streamToken=i.newBuffer(i.base64.length(e.streamToken)),0):0<=e.streamToken.length&&(t.streamToken=e.streamToken)),e.labels){if("object"!=typeof e.labels)throw TypeError(".google.firestore.v1beta1.WriteRequest.labels: object expected");t.labels={};for(var r=Object.keys(e.labels),o=0;o<r.length;++o)t.labels[r[o]]=String(e.labels[r[o]])}return t},Ue.toObject=function(e,t){var o,r={};if(((t=t||{}).arrays||t.defaults)&&(r.writes=[]),(t.objects||t.defaults)&&(r.labels={}),t.defaults&&(r.database="",r.streamId="",t.bytes===String?r.streamToken="":(r.streamToken=[],t.bytes!==Array&&(r.streamToken=i.newBuffer(r.streamToken)))),null!=e.database&&e.hasOwnProperty("database")&&(r.database=e.database),null!=e.streamId&&e.hasOwnProperty("streamId")&&(r.streamId=e.streamId),e.writes&&e.writes.length){r.writes=[];for(var n=0;n<e.writes.length;++n)r.writes[n]=a.google.firestore.v1beta1.Write.toObject(e.writes[n],t)}if(null!=e.streamToken&&e.hasOwnProperty("streamToken")&&(r.streamToken=t.bytes===String?i.base64.encode(e.streamToken,0,e.streamToken.length):t.bytes===Array?Array.prototype.slice.call(e.streamToken):e.streamToken),e.labels&&(o=Object.keys(e.labels)).length){r.labels={};for(n=0;n<o.length;++n)r.labels[o[n]]=e.labels[o[n]]}return r},Ue.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},Ue.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.v1beta1.WriteRequest"},Ue),o.WriteResponse=(Je.prototype.streamId="",Je.prototype.streamToken=i.newBuffer([]),Je.prototype.writeResults=i.emptyArray,Je.prototype.commitTime=null,Je.fromObject=function(e){if(e instanceof a.google.firestore.v1beta1.WriteResponse)return e;var t=new a.google.firestore.v1beta1.WriteResponse;if(null!=e.streamId&&(t.streamId=String(e.streamId)),null!=e.streamToken&&("string"==typeof e.streamToken?i.base64.decode(e.streamToken,t.streamToken=i.newBuffer(i.base64.length(e.streamToken)),0):0<=e.streamToken.length&&(t.streamToken=e.streamToken)),e.writeResults){if(!Array.isArray(e.writeResults))throw TypeError(".google.firestore.v1beta1.WriteResponse.writeResults: array expected");t.writeResults=[];for(var o=0;o<e.writeResults.length;++o){if("object"!=typeof e.writeResults[o])throw TypeError(".google.firestore.v1beta1.WriteResponse.writeResults: object expected");t.writeResults[o]=a.google.firestore.v1beta1.WriteResult.fromObject(e.writeResults[o])}}if(null!=e.commitTime){if("object"!=typeof e.commitTime)throw TypeError(".google.firestore.v1beta1.WriteResponse.commitTime: object expected");t.commitTime=a.google.protobuf.Timestamp.fromObject(e.commitTime)}return t},Je.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.writeResults=[]),t.defaults&&(o.streamId="",t.bytes===String?o.streamToken="":(o.streamToken=[],t.bytes!==Array&&(o.streamToken=i.newBuffer(o.streamToken))),o.commitTime=null),null!=e.streamId&&e.hasOwnProperty("streamId")&&(o.streamId=e.streamId),null!=e.streamToken&&e.hasOwnProperty("streamToken")&&(o.streamToken=t.bytes===String?i.base64.encode(e.streamToken,0,e.streamToken.length):t.bytes===Array?Array.prototype.slice.call(e.streamToken):e.streamToken),e.writeResults&&e.writeResults.length){o.writeResults=[];for(var r=0;r<e.writeResults.length;++r)o.writeResults[r]=a.google.firestore.v1beta1.WriteResult.toObject(e.writeResults[r],t)}return null!=e.commitTime&&e.hasOwnProperty("commitTime")&&(o.commitTime=a.google.protobuf.Timestamp.toObject(e.commitTime,t)),o},Je.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},Je.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.v1beta1.WriteResponse"},Je),o.ListenRequest=(Ge.prototype.database="",Ge.prototype.addTarget=null,Ge.prototype.removeTarget=null,Ge.prototype.labels=i.emptyObject,Object.defineProperty(Ge.prototype,"targetChange",{get:i.oneOfGetter(n=["addTarget","removeTarget"]),set:i.oneOfSetter(n)}),Ge.fromObject=function(e){if(e instanceof a.google.firestore.v1beta1.ListenRequest)return e;var t=new a.google.firestore.v1beta1.ListenRequest;if(null!=e.database&&(t.database=String(e.database)),null!=e.addTarget){if("object"!=typeof e.addTarget)throw TypeError(".google.firestore.v1beta1.ListenRequest.addTarget: object expected");t.addTarget=a.google.firestore.v1beta1.Target.fromObject(e.addTarget)}if(null!=e.removeTarget&&(t.removeTarget=0|e.removeTarget),e.labels){if("object"!=typeof e.labels)throw TypeError(".google.firestore.v1beta1.ListenRequest.labels: object expected");t.labels={};for(var o=Object.keys(e.labels),r=0;r<o.length;++r)t.labels[o[r]]=String(e.labels[o[r]])}return t},Ge.toObject=function(e,t){var o,r={};if(((t=t||{}).objects||t.defaults)&&(r.labels={}),t.defaults&&(r.database=""),null!=e.database&&e.hasOwnProperty("database")&&(r.database=e.database),null!=e.addTarget&&e.hasOwnProperty("addTarget")&&(r.addTarget=a.google.firestore.v1beta1.Target.toObject(e.addTarget,t),t.oneofs)&&(r.targetChange="addTarget"),null!=e.removeTarget&&e.hasOwnProperty("removeTarget")&&(r.removeTarget=e.removeTarget,t.oneofs)&&(r.targetChange="removeTarget"),e.labels&&(o=Object.keys(e.labels)).length){r.labels={};for(var n=0;n<o.length;++n)r.labels[o[n]]=e.labels[o[n]]}return r},Ge.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},Ge.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.v1beta1.ListenRequest"},Ge),o.ListenResponse=(Be.prototype.targetChange=null,Be.prototype.documentChange=null,Be.prototype.documentDelete=null,Be.prototype.documentRemove=null,Be.prototype.filter=null,Object.defineProperty(Be.prototype,"responseType",{get:i.oneOfGetter(n=["targetChange","documentChange","documentDelete","documentRemove","filter"]),set:i.oneOfSetter(n)}),Be.fromObject=function(e){if(e instanceof a.google.firestore.v1beta1.ListenResponse)return e;var t=new a.google.firestore.v1beta1.ListenResponse;if(null!=e.targetChange){if("object"!=typeof e.targetChange)throw TypeError(".google.firestore.v1beta1.ListenResponse.targetChange: object expected");t.targetChange=a.google.firestore.v1beta1.TargetChange.fromObject(e.targetChange)}if(null!=e.documentChange){if("object"!=typeof e.documentChange)throw TypeError(".google.firestore.v1beta1.ListenResponse.documentChange: object expected");t.documentChange=a.google.firestore.v1beta1.DocumentChange.fromObject(e.documentChange)}if(null!=e.documentDelete){if("object"!=typeof e.documentDelete)throw TypeError(".google.firestore.v1beta1.ListenResponse.documentDelete: object expected");t.documentDelete=a.google.firestore.v1beta1.DocumentDelete.fromObject(e.documentDelete)}if(null!=e.documentRemove){if("object"!=typeof e.documentRemove)throw TypeError(".google.firestore.v1beta1.ListenResponse.documentRemove: object expected");t.documentRemove=a.google.firestore.v1beta1.DocumentRemove.fromObject(e.documentRemove)}if(null!=e.filter){if("object"!=typeof e.filter)throw TypeError(".google.firestore.v1beta1.ListenResponse.filter: object expected");t.filter=a.google.firestore.v1beta1.ExistenceFilter.fromObject(e.filter)}return t},Be.toObject=function(e,t){t=t||{};var o={};return null!=e.targetChange&&e.hasOwnProperty("targetChange")&&(o.targetChange=a.google.firestore.v1beta1.TargetChange.toObject(e.targetChange,t),t.oneofs)&&(o.responseType="targetChange"),null!=e.documentChange&&e.hasOwnProperty("documentChange")&&(o.documentChange=a.google.firestore.v1beta1.DocumentChange.toObject(e.documentChange,t),t.oneofs)&&(o.responseType="documentChange"),null!=e.documentDelete&&e.hasOwnProperty("documentDelete")&&(o.documentDelete=a.google.firestore.v1beta1.DocumentDelete.toObject(e.documentDelete,t),t.oneofs)&&(o.responseType="documentDelete"),null!=e.filter&&e.hasOwnProperty("filter")&&(o.filter=a.google.firestore.v1beta1.ExistenceFilter.toObject(e.filter,t),t.oneofs)&&(o.responseType="filter"),null!=e.documentRemove&&e.hasOwnProperty("documentRemove")&&(o.documentRemove=a.google.firestore.v1beta1.DocumentRemove.toObject(e.documentRemove,t),t.oneofs)&&(o.responseType="documentRemove"),o},Be.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},Be.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.v1beta1.ListenResponse"},Be),o.Target=(P.prototype.query=null,P.prototype.documents=null,P.prototype.resumeToken=null,P.prototype.readTime=null,P.prototype.targetId=0,P.prototype.once=!1,Object.defineProperty(P.prototype,"targetType",{get:i.oneOfGetter(n=["query","documents"]),set:i.oneOfSetter(n)}),Object.defineProperty(P.prototype,"resumeType",{get:i.oneOfGetter(n=["resumeToken","readTime"]),set:i.oneOfSetter(n)}),P.fromObject=function(e){if(e instanceof a.google.firestore.v1beta1.Target)return e;var t=new a.google.firestore.v1beta1.Target;if(null!=e.query){if("object"!=typeof e.query)throw TypeError(".google.firestore.v1beta1.Target.query: object expected");t.query=a.google.firestore.v1beta1.Target.QueryTarget.fromObject(e.query)}if(null!=e.documents){if("object"!=typeof e.documents)throw TypeError(".google.firestore.v1beta1.Target.documents: object expected");t.documents=a.google.firestore.v1beta1.Target.DocumentsTarget.fromObject(e.documents)}if(null!=e.resumeToken&&("string"==typeof e.resumeToken?i.base64.decode(e.resumeToken,t.resumeToken=i.newBuffer(i.base64.length(e.resumeToken)),0):0<=e.resumeToken.length&&(t.resumeToken=e.resumeToken)),null!=e.readTime){if("object"!=typeof e.readTime)throw TypeError(".google.firestore.v1beta1.Target.readTime: object expected");t.readTime=a.google.protobuf.Timestamp.fromObject(e.readTime)}return null!=e.targetId&&(t.targetId=0|e.targetId),null!=e.once&&(t.once=Boolean(e.once)),t},P.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.targetId=0,o.once=!1),null!=e.query&&e.hasOwnProperty("query")&&(o.query=a.google.firestore.v1beta1.Target.QueryTarget.toObject(e.query,t),t.oneofs)&&(o.targetType="query"),null!=e.documents&&e.hasOwnProperty("documents")&&(o.documents=a.google.firestore.v1beta1.Target.DocumentsTarget.toObject(e.documents,t),t.oneofs)&&(o.targetType="documents"),null!=e.resumeToken&&e.hasOwnProperty("resumeToken")&&(o.resumeToken=t.bytes===String?i.base64.encode(e.resumeToken,0,e.resumeToken.length):t.bytes===Array?Array.prototype.slice.call(e.resumeToken):e.resumeToken,t.oneofs)&&(o.resumeType="resumeToken"),null!=e.targetId&&e.hasOwnProperty("targetId")&&(o.targetId=e.targetId),null!=e.once&&e.hasOwnProperty("once")&&(o.once=e.once),null!=e.readTime&&e.hasOwnProperty("readTime")&&(o.readTime=a.google.protobuf.Timestamp.toObject(e.readTime,t),t.oneofs)&&(o.resumeType="readTime"),o},P.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},P.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.v1beta1.Target"},P.DocumentsTarget=(Me.prototype.documents=i.emptyArray,Me.fromObject=function(e){if(e instanceof a.google.firestore.v1beta1.Target.DocumentsTarget)return e;var t=new a.google.firestore.v1beta1.Target.DocumentsTarget;if(e.documents){if(!Array.isArray(e.documents))throw TypeError(".google.firestore.v1beta1.Target.DocumentsTarget.documents: array expected");t.documents=[];for(var o=0;o<e.documents.length;++o)t.documents[o]=String(e.documents[o])}return t},Me.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.documents=[]),e.documents&&e.documents.length){o.documents=[];for(var r=0;r<e.documents.length;++r)o.documents[r]=e.documents[r]}return o},Me.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},Me.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.v1beta1.Target.DocumentsTarget"},Me),P.QueryTarget=(Ye.prototype.parent="",Ye.prototype.structuredQuery=null,Object.defineProperty(Ye.prototype,"queryType",{get:i.oneOfGetter(n=["structuredQuery"]),set:i.oneOfSetter(n)}),Ye.fromObject=function(e){if(e instanceof a.google.firestore.v1beta1.Target.QueryTarget)return e;var t=new a.google.firestore.v1beta1.Target.QueryTarget;if(null!=e.parent&&(t.parent=String(e.parent)),null!=e.structuredQuery){if("object"!=typeof e.structuredQuery)throw TypeError(".google.firestore.v1beta1.Target.QueryTarget.structuredQuery: object expected");t.structuredQuery=a.google.firestore.v1beta1.StructuredQuery.fromObject(e.structuredQuery)}return t},Ye.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.parent=""),null!=e.parent&&e.hasOwnProperty("parent")&&(o.parent=e.parent),null!=e.structuredQuery&&e.hasOwnProperty("structuredQuery")&&(o.structuredQuery=a.google.firestore.v1beta1.StructuredQuery.toObject(e.structuredQuery,t),t.oneofs)&&(o.queryType="structuredQuery"),o},Ye.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},Ye.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.v1beta1.Target.QueryTarget"},Ye),P),o.TargetChange=(Qe.prototype.targetChangeType=0,Qe.prototype.targetIds=i.emptyArray,Qe.prototype.cause=null,Qe.prototype.resumeToken=i.newBuffer([]),Qe.prototype.readTime=null,Qe.fromObject=function(e){if(e instanceof a.google.firestore.v1beta1.TargetChange)return e;var t=new a.google.firestore.v1beta1.TargetChange;switch(e.targetChangeType){default:"number"==typeof e.targetChangeType&&(t.targetChangeType=e.targetChangeType);break;case"NO_CHANGE":case 0:t.targetChangeType=0;break;case"ADD":case 1:t.targetChangeType=1;break;case"REMOVE":case 2:t.targetChangeType=2;break;case"CURRENT":case 3:t.targetChangeType=3;break;case"RESET":case 4:t.targetChangeType=4}if(e.targetIds){if(!Array.isArray(e.targetIds))throw TypeError(".google.firestore.v1beta1.TargetChange.targetIds: array expected");t.targetIds=[];for(var o=0;o<e.targetIds.length;++o)t.targetIds[o]=0|e.targetIds[o]}if(null!=e.cause){if("object"!=typeof e.cause)throw TypeError(".google.firestore.v1beta1.TargetChange.cause: object expected");t.cause=a.google.rpc.Status.fromObject(e.cause)}if(null!=e.resumeToken&&("string"==typeof e.resumeToken?i.base64.decode(e.resumeToken,t.resumeToken=i.newBuffer(i.base64.length(e.resumeToken)),0):0<=e.resumeToken.length&&(t.resumeToken=e.resumeToken)),null!=e.readTime){if("object"!=typeof e.readTime)throw TypeError(".google.firestore.v1beta1.TargetChange.readTime: object expected");t.readTime=a.google.protobuf.Timestamp.fromObject(e.readTime)}return t},Qe.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.targetIds=[]),t.defaults&&(o.targetChangeType=t.enums===String?"NO_CHANGE":0,o.cause=null,t.bytes===String?o.resumeToken="":(o.resumeToken=[],t.bytes!==Array&&(o.resumeToken=i.newBuffer(o.resumeToken))),o.readTime=null),null!=e.targetChangeType&&e.hasOwnProperty("targetChangeType")&&(o.targetChangeType=t.enums!==String||void 0===a.google.firestore.v1beta1.TargetChange.TargetChangeType[e.targetChangeType]?e.targetChangeType:a.google.firestore.v1beta1.TargetChange.TargetChangeType[e.targetChangeType]),e.targetIds&&e.targetIds.length){o.targetIds=[];for(var r=0;r<e.targetIds.length;++r)o.targetIds[r]=e.targetIds[r]}return null!=e.cause&&e.hasOwnProperty("cause")&&(o.cause=a.google.rpc.Status.toObject(e.cause,t)),null!=e.resumeToken&&e.hasOwnProperty("resumeToken")&&(o.resumeToken=t.bytes===String?i.base64.encode(e.resumeToken,0,e.resumeToken.length):t.bytes===Array?Array.prototype.slice.call(e.resumeToken):e.resumeToken),null!=e.readTime&&e.hasOwnProperty("readTime")&&(o.readTime=a.google.protobuf.Timestamp.toObject(e.readTime,t)),o},Qe.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},Qe.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.v1beta1.TargetChange"},Qe.TargetChangeType=(n={},(t=Object.create(n))[n[0]="NO_CHANGE"]="NO_CHANGE",t[n[1]="ADD"]="ADD",t[n[2]="REMOVE"]="REMOVE",t[n[3]="CURRENT"]="CURRENT",t[n[4]="RESET"]="RESET",t),Qe),o.ListCollectionIdsRequest=(qe.prototype.parent="",qe.prototype.pageSize=0,qe.prototype.pageToken="",qe.fromObject=function(e){var t;return e instanceof a.google.firestore.v1beta1.ListCollectionIdsRequest?e:(t=new a.google.firestore.v1beta1.ListCollectionIdsRequest,null!=e.parent&&(t.parent=String(e.parent)),null!=e.pageSize&&(t.pageSize=0|e.pageSize),null!=e.pageToken&&(t.pageToken=String(e.pageToken)),t)},qe.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.parent="",o.pageSize=0,o.pageToken=""),null!=e.parent&&e.hasOwnProperty("parent")&&(o.parent=e.parent),null!=e.pageSize&&e.hasOwnProperty("pageSize")&&(o.pageSize=e.pageSize),null!=e.pageToken&&e.hasOwnProperty("pageToken")&&(o.pageToken=e.pageToken),o},qe.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},qe.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.v1beta1.ListCollectionIdsRequest"},qe),o.ListCollectionIdsResponse=(We.prototype.collectionIds=i.emptyArray,We.prototype.nextPageToken="",We.fromObject=function(e){if(e instanceof a.google.firestore.v1beta1.ListCollectionIdsResponse)return e;var t=new a.google.firestore.v1beta1.ListCollectionIdsResponse;if(e.collectionIds){if(!Array.isArray(e.collectionIds))throw TypeError(".google.firestore.v1beta1.ListCollectionIdsResponse.collectionIds: array expected");t.collectionIds=[];for(var o=0;o<e.collectionIds.length;++o)t.collectionIds[o]=String(e.collectionIds[o])}return null!=e.nextPageToken&&(t.nextPageToken=String(e.nextPageToken)),t},We.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.collectionIds=[]),t.defaults&&(o.nextPageToken=""),e.collectionIds&&e.collectionIds.length){o.collectionIds=[];for(var r=0;r<e.collectionIds.length;++r)o.collectionIds[r]=e.collectionIds[r]}return null!=e.nextPageToken&&e.hasOwnProperty("nextPageToken")&&(o.nextPageToken=e.nextPageToken),o},We.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},We.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.v1beta1.ListCollectionIdsResponse"},We),o.BatchWriteRequest=(He.prototype.database="",He.prototype.writes=i.emptyArray,He.prototype.labels=i.emptyObject,He.fromObject=function(e){if(e instanceof a.google.firestore.v1beta1.BatchWriteRequest)return e;var t=new a.google.firestore.v1beta1.BatchWriteRequest;if(null!=e.database&&(t.database=String(e.database)),e.writes){if(!Array.isArray(e.writes))throw TypeError(".google.firestore.v1beta1.BatchWriteRequest.writes: array expected");t.writes=[];for(var o=0;o<e.writes.length;++o){if("object"!=typeof e.writes[o])throw TypeError(".google.firestore.v1beta1.BatchWriteRequest.writes: object expected");t.writes[o]=a.google.firestore.v1beta1.Write.fromObject(e.writes[o])}}if(e.labels){if("object"!=typeof e.labels)throw TypeError(".google.firestore.v1beta1.BatchWriteRequest.labels: object expected");t.labels={};for(var r=Object.keys(e.labels),o=0;o<r.length;++o)t.labels[r[o]]=String(e.labels[r[o]])}return t},He.toObject=function(e,t){var o,r={};if(((t=t||{}).arrays||t.defaults)&&(r.writes=[]),(t.objects||t.defaults)&&(r.labels={}),t.defaults&&(r.database=""),null!=e.database&&e.hasOwnProperty("database")&&(r.database=e.database),e.writes&&e.writes.length){r.writes=[];for(var n=0;n<e.writes.length;++n)r.writes[n]=a.google.firestore.v1beta1.Write.toObject(e.writes[n],t)}if(e.labels&&(o=Object.keys(e.labels)).length){r.labels={};for(n=0;n<o.length;++n)r.labels[o[n]]=e.labels[o[n]]}return r},He.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},He.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.v1beta1.BatchWriteRequest"},He),o.BatchWriteResponse=(ze.prototype.writeResults=i.emptyArray,ze.prototype.status=i.emptyArray,ze.fromObject=function(e){if(e instanceof a.google.firestore.v1beta1.BatchWriteResponse)return e;var t=new a.google.firestore.v1beta1.BatchWriteResponse;if(e.writeResults){if(!Array.isArray(e.writeResults))throw TypeError(".google.firestore.v1beta1.BatchWriteResponse.writeResults: array expected");t.writeResults=[];for(var o=0;o<e.writeResults.length;++o){if("object"!=typeof e.writeResults[o])throw TypeError(".google.firestore.v1beta1.BatchWriteResponse.writeResults: object expected");t.writeResults[o]=a.google.firestore.v1beta1.WriteResult.fromObject(e.writeResults[o])}}if(e.status){if(!Array.isArray(e.status))throw TypeError(".google.firestore.v1beta1.BatchWriteResponse.status: array expected");t.status=[];for(o=0;o<e.status.length;++o){if("object"!=typeof e.status[o])throw TypeError(".google.firestore.v1beta1.BatchWriteResponse.status: object expected");t.status[o]=a.google.rpc.Status.fromObject(e.status[o])}}return t},ze.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.writeResults=[],o.status=[]),e.writeResults&&e.writeResults.length){o.writeResults=[];for(var r=0;r<e.writeResults.length;++r)o.writeResults[r]=a.google.firestore.v1beta1.WriteResult.toObject(e.writeResults[r],t)}if(e.status&&e.status.length){o.status=[];for(r=0;r<e.status.length;++r)o.status[r]=a.google.rpc.Status.toObject(e.status[r],t)}return o},ze.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},ze.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.v1beta1.BatchWriteResponse"},ze),o.StructuredQuery=(D.prototype.select=null,D.prototype.from=i.emptyArray,D.prototype.where=null,D.prototype.orderBy=i.emptyArray,D.prototype.startAt=null,D.prototype.endAt=null,D.prototype.offset=0,D.prototype.limit=null,D.fromObject=function(e){if(e instanceof a.google.firestore.v1beta1.StructuredQuery)return e;var t=new a.google.firestore.v1beta1.StructuredQuery;if(null!=e.select){if("object"!=typeof e.select)throw TypeError(".google.firestore.v1beta1.StructuredQuery.select: object expected");t.select=a.google.firestore.v1beta1.StructuredQuery.Projection.fromObject(e.select)}if(e.from){if(!Array.isArray(e.from))throw TypeError(".google.firestore.v1beta1.StructuredQuery.from: array expected");t.from=[];for(var o=0;o<e.from.length;++o){if("object"!=typeof e.from[o])throw TypeError(".google.firestore.v1beta1.StructuredQuery.from: object expected");t.from[o]=a.google.firestore.v1beta1.StructuredQuery.CollectionSelector.fromObject(e.from[o])}}if(null!=e.where){if("object"!=typeof e.where)throw TypeError(".google.firestore.v1beta1.StructuredQuery.where: object expected");t.where=a.google.firestore.v1beta1.StructuredQuery.Filter.fromObject(e.where)}if(e.orderBy){if(!Array.isArray(e.orderBy))throw TypeError(".google.firestore.v1beta1.StructuredQuery.orderBy: array expected");t.orderBy=[];for(o=0;o<e.orderBy.length;++o){if("object"!=typeof e.orderBy[o])throw TypeError(".google.firestore.v1beta1.StructuredQuery.orderBy: object expected");t.orderBy[o]=a.google.firestore.v1beta1.StructuredQuery.Order.fromObject(e.orderBy[o])}}if(null!=e.startAt){if("object"!=typeof e.startAt)throw TypeError(".google.firestore.v1beta1.StructuredQuery.startAt: object expected");t.startAt=a.google.firestore.v1beta1.Cursor.fromObject(e.startAt)}if(null!=e.endAt){if("object"!=typeof e.endAt)throw TypeError(".google.firestore.v1beta1.StructuredQuery.endAt: object expected");t.endAt=a.google.firestore.v1beta1.Cursor.fromObject(e.endAt)}if(null!=e.offset&&(t.offset=0|e.offset),null!=e.limit){if("object"!=typeof e.limit)throw TypeError(".google.firestore.v1beta1.StructuredQuery.limit: object expected");t.limit=a.google.protobuf.Int32Value.fromObject(e.limit)}return t},D.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.from=[],o.orderBy=[]),t.defaults&&(o.select=null,o.where=null,o.limit=null,o.offset=0,o.startAt=null,o.endAt=null),null!=e.select&&e.hasOwnProperty("select")&&(o.select=a.google.firestore.v1beta1.StructuredQuery.Projection.toObject(e.select,t)),e.from&&e.from.length){o.from=[];for(var r=0;r<e.from.length;++r)o.from[r]=a.google.firestore.v1beta1.StructuredQuery.CollectionSelector.toObject(e.from[r],t)}if(null!=e.where&&e.hasOwnProperty("where")&&(o.where=a.google.firestore.v1beta1.StructuredQuery.Filter.toObject(e.where,t)),e.orderBy&&e.orderBy.length){o.orderBy=[];for(r=0;r<e.orderBy.length;++r)o.orderBy[r]=a.google.firestore.v1beta1.StructuredQuery.Order.toObject(e.orderBy[r],t)}return null!=e.limit&&e.hasOwnProperty("limit")&&(o.limit=a.google.protobuf.Int32Value.toObject(e.limit,t)),null!=e.offset&&e.hasOwnProperty("offset")&&(o.offset=e.offset),null!=e.startAt&&e.hasOwnProperty("startAt")&&(o.startAt=a.google.firestore.v1beta1.Cursor.toObject(e.startAt,t)),null!=e.endAt&&e.hasOwnProperty("endAt")&&(o.endAt=a.google.firestore.v1beta1.Cursor.toObject(e.endAt,t)),o},D.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},D.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.v1beta1.StructuredQuery"},D.CollectionSelector=(Ke.prototype.collectionId="",Ke.prototype.allDescendants=!1,Ke.fromObject=function(e){var t;return e instanceof a.google.firestore.v1beta1.StructuredQuery.CollectionSelector?e:(t=new a.google.firestore.v1beta1.StructuredQuery.CollectionSelector,null!=e.collectionId&&(t.collectionId=String(e.collectionId)),null!=e.allDescendants&&(t.allDescendants=Boolean(e.allDescendants)),t)},Ke.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.collectionId="",o.allDescendants=!1),null!=e.collectionId&&e.hasOwnProperty("collectionId")&&(o.collectionId=e.collectionId),null!=e.allDescendants&&e.hasOwnProperty("allDescendants")&&(o.allDescendants=e.allDescendants),o},Ke.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},Ke.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.v1beta1.StructuredQuery.CollectionSelector"},Ke),D.Filter=(Xe.prototype.compositeFilter=null,Xe.prototype.fieldFilter=null,Xe.prototype.unaryFilter=null,Object.defineProperty(Xe.prototype,"filterType",{get:i.oneOfGetter(n=["compositeFilter","fieldFilter","unaryFilter"]),set:i.oneOfSetter(n)}),Xe.fromObject=function(e){if(e instanceof a.google.firestore.v1beta1.StructuredQuery.Filter)return e;var t=new a.google.firestore.v1beta1.StructuredQuery.Filter;if(null!=e.compositeFilter){if("object"!=typeof e.compositeFilter)throw TypeError(".google.firestore.v1beta1.StructuredQuery.Filter.compositeFilter: object expected");t.compositeFilter=a.google.firestore.v1beta1.StructuredQuery.CompositeFilter.fromObject(e.compositeFilter)}if(null!=e.fieldFilter){if("object"!=typeof e.fieldFilter)throw TypeError(".google.firestore.v1beta1.StructuredQuery.Filter.fieldFilter: object expected");t.fieldFilter=a.google.firestore.v1beta1.StructuredQuery.FieldFilter.fromObject(e.fieldFilter)}if(null!=e.unaryFilter){if("object"!=typeof e.unaryFilter)throw TypeError(".google.firestore.v1beta1.StructuredQuery.Filter.unaryFilter: object expected");t.unaryFilter=a.google.firestore.v1beta1.StructuredQuery.UnaryFilter.fromObject(e.unaryFilter)}return t},Xe.toObject=function(e,t){t=t||{};var o={};return null!=e.compositeFilter&&e.hasOwnProperty("compositeFilter")&&(o.compositeFilter=a.google.firestore.v1beta1.StructuredQuery.CompositeFilter.toObject(e.compositeFilter,t),t.oneofs)&&(o.filterType="compositeFilter"),null!=e.fieldFilter&&e.hasOwnProperty("fieldFilter")&&(o.fieldFilter=a.google.firestore.v1beta1.StructuredQuery.FieldFilter.toObject(e.fieldFilter,t),t.oneofs)&&(o.filterType="fieldFilter"),null!=e.unaryFilter&&e.hasOwnProperty("unaryFilter")&&(o.unaryFilter=a.google.firestore.v1beta1.StructuredQuery.UnaryFilter.toObject(e.unaryFilter,t),t.oneofs)&&(o.filterType="unaryFilter"),o},Xe.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},Xe.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.v1beta1.StructuredQuery.Filter"},Xe),D.CompositeFilter=(Ze.prototype.op=0,Ze.prototype.filters=i.emptyArray,Ze.fromObject=function(e){if(e instanceof a.google.firestore.v1beta1.StructuredQuery.CompositeFilter)return e;var t=new a.google.firestore.v1beta1.StructuredQuery.CompositeFilter;switch(e.op){default:"number"==typeof e.op&&(t.op=e.op);break;case"OPERATOR_UNSPECIFIED":case 0:t.op=0;break;case"AND":case 1:t.op=1}if(e.filters){if(!Array.isArray(e.filters))throw TypeError(".google.firestore.v1beta1.StructuredQuery.CompositeFilter.filters: array expected");t.filters=[];for(var o=0;o<e.filters.length;++o){if("object"!=typeof e.filters[o])throw TypeError(".google.firestore.v1beta1.StructuredQuery.CompositeFilter.filters: object expected");t.filters[o]=a.google.firestore.v1beta1.StructuredQuery.Filter.fromObject(e.filters[o])}}return t},Ze.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.filters=[]),t.defaults&&(o.op=t.enums===String?"OPERATOR_UNSPECIFIED":0),null!=e.op&&e.hasOwnProperty("op")&&(o.op=t.enums!==String||void 0===a.google.firestore.v1beta1.StructuredQuery.CompositeFilter.Operator[e.op]?e.op:a.google.firestore.v1beta1.StructuredQuery.CompositeFilter.Operator[e.op]),e.filters&&e.filters.length){o.filters=[];for(var r=0;r<e.filters.length;++r)o.filters[r]=a.google.firestore.v1beta1.StructuredQuery.Filter.toObject(e.filters[r],t)}return o},Ze.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},Ze.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.v1beta1.StructuredQuery.CompositeFilter"},Ze.Operator=(n={},(t=Object.create(n))[n[0]="OPERATOR_UNSPECIFIED"]="OPERATOR_UNSPECIFIED",t[n[1]="AND"]="AND",t),Ze),D.FieldFilter=($e.prototype.field=null,$e.prototype.op=0,$e.prototype.value=null,$e.fromObject=function(e){if(e instanceof a.google.firestore.v1beta1.StructuredQuery.FieldFilter)return e;var t=new a.google.firestore.v1beta1.StructuredQuery.FieldFilter;if(null!=e.field){if("object"!=typeof e.field)throw TypeError(".google.firestore.v1beta1.StructuredQuery.FieldFilter.field: object expected");t.field=a.google.firestore.v1beta1.StructuredQuery.FieldReference.fromObject(e.field)}switch(e.op){default:"number"==typeof e.op&&(t.op=e.op);break;case"OPERATOR_UNSPECIFIED":case 0:t.op=0;break;case"LESS_THAN":case 1:t.op=1;break;case"LESS_THAN_OR_EQUAL":case 2:t.op=2;break;case"GREATER_THAN":case 3:t.op=3;break;case"GREATER_THAN_OR_EQUAL":case 4:t.op=4;break;case"EQUAL":case 5:t.op=5;break;case"NOT_EQUAL":case 6:t.op=6;break;case"ARRAY_CONTAINS":case 7:t.op=7;break;case"IN":case 8:t.op=8;break;case"ARRAY_CONTAINS_ANY":case 9:t.op=9;break;case"NOT_IN":case 10:t.op=10}if(null!=e.value){if("object"!=typeof e.value)throw TypeError(".google.firestore.v1beta1.StructuredQuery.FieldFilter.value: object expected");t.value=a.google.firestore.v1beta1.Value.fromObject(e.value)}return t},$e.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.field=null,o.op=t.enums===String?"OPERATOR_UNSPECIFIED":0,o.value=null),null!=e.field&&e.hasOwnProperty("field")&&(o.field=a.google.firestore.v1beta1.StructuredQuery.FieldReference.toObject(e.field,t)),null!=e.op&&e.hasOwnProperty("op")&&(o.op=t.enums!==String||void 0===a.google.firestore.v1beta1.StructuredQuery.FieldFilter.Operator[e.op]?e.op:a.google.firestore.v1beta1.StructuredQuery.FieldFilter.Operator[e.op]),null!=e.value&&e.hasOwnProperty("value")&&(o.value=a.google.firestore.v1beta1.Value.toObject(e.value,t)),o},$e.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},$e.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.v1beta1.StructuredQuery.FieldFilter"},$e.Operator=(n={},(t=Object.create(n))[n[0]="OPERATOR_UNSPECIFIED"]="OPERATOR_UNSPECIFIED",t[n[1]="LESS_THAN"]="LESS_THAN",t[n[2]="LESS_THAN_OR_EQUAL"]="LESS_THAN_OR_EQUAL",t[n[3]="GREATER_THAN"]="GREATER_THAN",t[n[4]="GREATER_THAN_OR_EQUAL"]="GREATER_THAN_OR_EQUAL",t[n[5]="EQUAL"]="EQUAL",t[n[6]="NOT_EQUAL"]="NOT_EQUAL",t[n[7]="ARRAY_CONTAINS"]="ARRAY_CONTAINS",t[n[8]="IN"]="IN",t[n[9]="ARRAY_CONTAINS_ANY"]="ARRAY_CONTAINS_ANY",t[n[10]="NOT_IN"]="NOT_IN",t),$e),D.UnaryFilter=(et.prototype.op=0,et.prototype.field=null,Object.defineProperty(et.prototype,"operandType",{get:i.oneOfGetter(n=["field"]),set:i.oneOfSetter(n)}),et.fromObject=function(e){if(e instanceof a.google.firestore.v1beta1.StructuredQuery.UnaryFilter)return e;var t=new a.google.firestore.v1beta1.StructuredQuery.UnaryFilter;switch(e.op){default:"number"==typeof e.op&&(t.op=e.op);break;case"OPERATOR_UNSPECIFIED":case 0:t.op=0;break;case"IS_NAN":case 2:t.op=2;break;case"IS_NULL":case 3:t.op=3;break;case"IS_NOT_NAN":case 4:t.op=4;break;case"IS_NOT_NULL":case 5:t.op=5}if(null!=e.field){if("object"!=typeof e.field)throw TypeError(".google.firestore.v1beta1.StructuredQuery.UnaryFilter.field: object expected");t.field=a.google.firestore.v1beta1.StructuredQuery.FieldReference.fromObject(e.field)}return t},et.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.op=t.enums===String?"OPERATOR_UNSPECIFIED":0),null!=e.op&&e.hasOwnProperty("op")&&(o.op=t.enums!==String||void 0===a.google.firestore.v1beta1.StructuredQuery.UnaryFilter.Operator[e.op]?e.op:a.google.firestore.v1beta1.StructuredQuery.UnaryFilter.Operator[e.op]),null!=e.field&&e.hasOwnProperty("field")&&(o.field=a.google.firestore.v1beta1.StructuredQuery.FieldReference.toObject(e.field,t),t.oneofs)&&(o.operandType="field"),o},et.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},et.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.v1beta1.StructuredQuery.UnaryFilter"},et.Operator=(n={},(t=Object.create(n))[n[0]="OPERATOR_UNSPECIFIED"]="OPERATOR_UNSPECIFIED",t[n[2]="IS_NAN"]="IS_NAN",t[n[3]="IS_NULL"]="IS_NULL",t[n[4]="IS_NOT_NAN"]="IS_NOT_NAN",t[n[5]="IS_NOT_NULL"]="IS_NOT_NULL",t),et),D.FieldReference=(tt.prototype.fieldPath="",tt.fromObject=function(e){var t;return e instanceof a.google.firestore.v1beta1.StructuredQuery.FieldReference?e:(t=new a.google.firestore.v1beta1.StructuredQuery.FieldReference,null!=e.fieldPath&&(t.fieldPath=String(e.fieldPath)),t)},tt.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.fieldPath=""),null!=e.fieldPath&&e.hasOwnProperty("fieldPath")&&(o.fieldPath=e.fieldPath),o},tt.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},tt.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.v1beta1.StructuredQuery.FieldReference"},tt),D.Order=(ot.prototype.field=null,ot.prototype.direction=0,ot.fromObject=function(e){if(e instanceof a.google.firestore.v1beta1.StructuredQuery.Order)return e;var t=new a.google.firestore.v1beta1.StructuredQuery.Order;if(null!=e.field){if("object"!=typeof e.field)throw TypeError(".google.firestore.v1beta1.StructuredQuery.Order.field: object expected");t.field=a.google.firestore.v1beta1.StructuredQuery.FieldReference.fromObject(e.field)}switch(e.direction){default:"number"==typeof e.direction&&(t.direction=e.direction);break;case"DIRECTION_UNSPECIFIED":case 0:t.direction=0;break;case"ASCENDING":case 1:t.direction=1;break;case"DESCENDING":case 2:t.direction=2}return t},ot.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.field=null,o.direction=t.enums===String?"DIRECTION_UNSPECIFIED":0),null!=e.field&&e.hasOwnProperty("field")&&(o.field=a.google.firestore.v1beta1.StructuredQuery.FieldReference.toObject(e.field,t)),null!=e.direction&&e.hasOwnProperty("direction")&&(o.direction=t.enums!==String||void 0===a.google.firestore.v1beta1.StructuredQuery.Direction[e.direction]?e.direction:a.google.firestore.v1beta1.StructuredQuery.Direction[e.direction]),o},ot.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},ot.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.v1beta1.StructuredQuery.Order"},ot),D.Projection=(rt.prototype.fields=i.emptyArray,rt.fromObject=function(e){if(e instanceof a.google.firestore.v1beta1.StructuredQuery.Projection)return e;var t=new a.google.firestore.v1beta1.StructuredQuery.Projection;if(e.fields){if(!Array.isArray(e.fields))throw TypeError(".google.firestore.v1beta1.StructuredQuery.Projection.fields: array expected");t.fields=[];for(var o=0;o<e.fields.length;++o){if("object"!=typeof e.fields[o])throw TypeError(".google.firestore.v1beta1.StructuredQuery.Projection.fields: object expected");t.fields[o]=a.google.firestore.v1beta1.StructuredQuery.FieldReference.fromObject(e.fields[o])}}return t},rt.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.fields=[]),e.fields&&e.fields.length){o.fields=[];for(var r=0;r<e.fields.length;++r)o.fields[r]=a.google.firestore.v1beta1.StructuredQuery.FieldReference.toObject(e.fields[r],t)}return o},rt.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},rt.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.v1beta1.StructuredQuery.Projection"},rt),D.Direction=(n={},(t=Object.create(n))[n[0]="DIRECTION_UNSPECIFIED"]="DIRECTION_UNSPECIFIED",t[n[1]="ASCENDING"]="ASCENDING",t[n[2]="DESCENDING"]="DESCENDING",t),D),o.Cursor=(nt.prototype.values=i.emptyArray,nt.prototype.before=!1,nt.fromObject=function(e){if(e instanceof a.google.firestore.v1beta1.Cursor)return e;var t=new a.google.firestore.v1beta1.Cursor;if(e.values){if(!Array.isArray(e.values))throw TypeError(".google.firestore.v1beta1.Cursor.values: array expected");t.values=[];for(var o=0;o<e.values.length;++o){if("object"!=typeof e.values[o])throw TypeError(".google.firestore.v1beta1.Cursor.values: object expected");t.values[o]=a.google.firestore.v1beta1.Value.fromObject(e.values[o])}}return null!=e.before&&(t.before=Boolean(e.before)),t},nt.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.values=[]),t.defaults&&(o.before=!1),e.values&&e.values.length){o.values=[];for(var r=0;r<e.values.length;++r)o.values[r]=a.google.firestore.v1beta1.Value.toObject(e.values[r],t)}return null!=e.before&&e.hasOwnProperty("before")&&(o.before=e.before),o},nt.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},nt.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.v1beta1.Cursor"},nt),o.Write=(R.prototype.update=null,R.prototype.delete=null,R.prototype.transform=null,R.prototype.updateMask=null,R.prototype.updateTransforms=i.emptyArray,R.prototype.currentDocument=null,Object.defineProperty(R.prototype,"operation",{get:i.oneOfGetter(n=["update","delete","transform"]),set:i.oneOfSetter(n)}),R.fromObject=function(e){if(e instanceof a.google.firestore.v1beta1.Write)return e;var t=new a.google.firestore.v1beta1.Write;if(null!=e.update){if("object"!=typeof e.update)throw TypeError(".google.firestore.v1beta1.Write.update: object expected");t.update=a.google.firestore.v1beta1.Document.fromObject(e.update)}if(null!=e.delete&&(t.delete=String(e.delete)),null!=e.transform){if("object"!=typeof e.transform)throw TypeError(".google.firestore.v1beta1.Write.transform: object expected");t.transform=a.google.firestore.v1beta1.DocumentTransform.fromObject(e.transform)}if(null!=e.updateMask){if("object"!=typeof e.updateMask)throw TypeError(".google.firestore.v1beta1.Write.updateMask: object expected");t.updateMask=a.google.firestore.v1beta1.DocumentMask.fromObject(e.updateMask)}if(e.updateTransforms){if(!Array.isArray(e.updateTransforms))throw TypeError(".google.firestore.v1beta1.Write.updateTransforms: array expected");t.updateTransforms=[];for(var o=0;o<e.updateTransforms.length;++o){if("object"!=typeof e.updateTransforms[o])throw TypeError(".google.firestore.v1beta1.Write.updateTransforms: object expected");t.updateTransforms[o]=a.google.firestore.v1beta1.DocumentTransform.FieldTransform.fromObject(e.updateTransforms[o])}}if(null!=e.currentDocument){if("object"!=typeof e.currentDocument)throw TypeError(".google.firestore.v1beta1.Write.currentDocument: object expected");t.currentDocument=a.google.firestore.v1beta1.Precondition.fromObject(e.currentDocument)}return t},R.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.updateTransforms=[]),t.defaults&&(o.updateMask=null,o.currentDocument=null),null!=e.update&&e.hasOwnProperty("update")&&(o.update=a.google.firestore.v1beta1.Document.toObject(e.update,t),t.oneofs)&&(o.operation="update"),null!=e.delete&&e.hasOwnProperty("delete")&&(o.delete=e.delete,t.oneofs)&&(o.operation="delete"),null!=e.updateMask&&e.hasOwnProperty("updateMask")&&(o.updateMask=a.google.firestore.v1beta1.DocumentMask.toObject(e.updateMask,t)),null!=e.currentDocument&&e.hasOwnProperty("currentDocument")&&(o.currentDocument=a.google.firestore.v1beta1.Precondition.toObject(e.currentDocument,t)),null!=e.transform&&e.hasOwnProperty("transform")&&(o.transform=a.google.firestore.v1beta1.DocumentTransform.toObject(e.transform,t),t.oneofs)&&(o.operation="transform"),e.updateTransforms&&e.updateTransforms.length){o.updateTransforms=[];for(var r=0;r<e.updateTransforms.length;++r)o.updateTransforms[r]=a.google.firestore.v1beta1.DocumentTransform.FieldTransform.toObject(e.updateTransforms[r],t)}return o},R.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},R.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.v1beta1.Write"},R),o.DocumentTransform=(it.prototype.document="",it.prototype.fieldTransforms=i.emptyArray,it.fromObject=function(e){if(e instanceof a.google.firestore.v1beta1.DocumentTransform)return e;var t=new a.google.firestore.v1beta1.DocumentTransform;if(null!=e.document&&(t.document=String(e.document)),e.fieldTransforms){if(!Array.isArray(e.fieldTransforms))throw TypeError(".google.firestore.v1beta1.DocumentTransform.fieldTransforms: array expected");t.fieldTransforms=[];for(var o=0;o<e.fieldTransforms.length;++o){if("object"!=typeof e.fieldTransforms[o])throw TypeError(".google.firestore.v1beta1.DocumentTransform.fieldTransforms: object expected");t.fieldTransforms[o]=a.google.firestore.v1beta1.DocumentTransform.FieldTransform.fromObject(e.fieldTransforms[o])}}return t},it.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.fieldTransforms=[]),t.defaults&&(o.document=""),null!=e.document&&e.hasOwnProperty("document")&&(o.document=e.document),e.fieldTransforms&&e.fieldTransforms.length){o.fieldTransforms=[];for(var r=0;r<e.fieldTransforms.length;++r)o.fieldTransforms[r]=a.google.firestore.v1beta1.DocumentTransform.FieldTransform.toObject(e.fieldTransforms[r],t)}return o},it.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},it.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.v1beta1.DocumentTransform"},it.FieldTransform=(I.prototype.fieldPath="",I.prototype.setToServerValue=null,I.prototype.increment=null,I.prototype.maximum=null,I.prototype.minimum=null,I.prototype.appendMissingElements=null,I.prototype.removeAllFromArray=null,Object.defineProperty(I.prototype,"transformType",{get:i.oneOfGetter(t=["setToServerValue","increment","maximum","minimum","appendMissingElements","removeAllFromArray"]),set:i.oneOfSetter(t)}),I.fromObject=function(e){if(e instanceof a.google.firestore.v1beta1.DocumentTransform.FieldTransform)return e;var t=new a.google.firestore.v1beta1.DocumentTransform.FieldTransform;switch(null!=e.fieldPath&&(t.fieldPath=String(e.fieldPath)),e.setToServerValue){default:"number"==typeof e.setToServerValue&&(t.setToServerValue=e.setToServerValue);break;case"SERVER_VALUE_UNSPECIFIED":case 0:t.setToServerValue=0;break;case"REQUEST_TIME":case 1:t.setToServerValue=1}if(null!=e.increment){if("object"!=typeof e.increment)throw TypeError(".google.firestore.v1beta1.DocumentTransform.FieldTransform.increment: object expected");t.increment=a.google.firestore.v1beta1.Value.fromObject(e.increment)}if(null!=e.maximum){if("object"!=typeof e.maximum)throw TypeError(".google.firestore.v1beta1.DocumentTransform.FieldTransform.maximum: object expected");t.maximum=a.google.firestore.v1beta1.Value.fromObject(e.maximum)}if(null!=e.minimum){if("object"!=typeof e.minimum)throw TypeError(".google.firestore.v1beta1.DocumentTransform.FieldTransform.minimum: object expected");t.minimum=a.google.firestore.v1beta1.Value.fromObject(e.minimum)}if(null!=e.appendMissingElements){if("object"!=typeof e.appendMissingElements)throw TypeError(".google.firestore.v1beta1.DocumentTransform.FieldTransform.appendMissingElements: object expected");t.appendMissingElements=a.google.firestore.v1beta1.ArrayValue.fromObject(e.appendMissingElements)}if(null!=e.removeAllFromArray){if("object"!=typeof e.removeAllFromArray)throw TypeError(".google.firestore.v1beta1.DocumentTransform.FieldTransform.removeAllFromArray: object expected");t.removeAllFromArray=a.google.firestore.v1beta1.ArrayValue.fromObject(e.removeAllFromArray)}return t},I.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.fieldPath=""),null!=e.fieldPath&&e.hasOwnProperty("fieldPath")&&(o.fieldPath=e.fieldPath),null!=e.setToServerValue&&e.hasOwnProperty("setToServerValue")&&(o.setToServerValue=t.enums!==String||void 0===a.google.firestore.v1beta1.DocumentTransform.FieldTransform.ServerValue[e.setToServerValue]?e.setToServerValue:a.google.firestore.v1beta1.DocumentTransform.FieldTransform.ServerValue[e.setToServerValue],t.oneofs)&&(o.transformType="setToServerValue"),null!=e.increment&&e.hasOwnProperty("increment")&&(o.increment=a.google.firestore.v1beta1.Value.toObject(e.increment,t),t.oneofs)&&(o.transformType="increment"),null!=e.maximum&&e.hasOwnProperty("maximum")&&(o.maximum=a.google.firestore.v1beta1.Value.toObject(e.maximum,t),t.oneofs)&&(o.transformType="maximum"),null!=e.minimum&&e.hasOwnProperty("minimum")&&(o.minimum=a.google.firestore.v1beta1.Value.toObject(e.minimum,t),t.oneofs)&&(o.transformType="minimum"),null!=e.appendMissingElements&&e.hasOwnProperty("appendMissingElements")&&(o.appendMissingElements=a.google.firestore.v1beta1.ArrayValue.toObject(e.appendMissingElements,t),t.oneofs)&&(o.transformType="appendMissingElements"),null!=e.removeAllFromArray&&e.hasOwnProperty("removeAllFromArray")&&(o.removeAllFromArray=a.google.firestore.v1beta1.ArrayValue.toObject(e.removeAllFromArray,t),t.oneofs)&&(o.transformType="removeAllFromArray"),o},I.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},I.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.v1beta1.DocumentTransform.FieldTransform"},I.ServerValue=(t={},(n=Object.create(t))[t[0]="SERVER_VALUE_UNSPECIFIED"]="SERVER_VALUE_UNSPECIFIED",n[t[1]="REQUEST_TIME"]="REQUEST_TIME",n),I),it),o.WriteResult=(at.prototype.updateTime=null,at.prototype.transformResults=i.emptyArray,at.fromObject=function(e){if(e instanceof a.google.firestore.v1beta1.WriteResult)return e;var t=new a.google.firestore.v1beta1.WriteResult;if(null!=e.updateTime){if("object"!=typeof e.updateTime)throw TypeError(".google.firestore.v1beta1.WriteResult.updateTime: object expected");t.updateTime=a.google.protobuf.Timestamp.fromObject(e.updateTime)}if(e.transformResults){if(!Array.isArray(e.transformResults))throw TypeError(".google.firestore.v1beta1.WriteResult.transformResults: array expected");t.transformResults=[];for(var o=0;o<e.transformResults.length;++o){if("object"!=typeof e.transformResults[o])throw TypeError(".google.firestore.v1beta1.WriteResult.transformResults: object expected");t.transformResults[o]=a.google.firestore.v1beta1.Value.fromObject(e.transformResults[o])}}return t},at.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.transformResults=[]),t.defaults&&(o.updateTime=null),null!=e.updateTime&&e.hasOwnProperty("updateTime")&&(o.updateTime=a.google.protobuf.Timestamp.toObject(e.updateTime,t)),e.transformResults&&e.transformResults.length){o.transformResults=[];for(var r=0;r<e.transformResults.length;++r)o.transformResults[r]=a.google.firestore.v1beta1.Value.toObject(e.transformResults[r],t)}return o},at.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},at.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.v1beta1.WriteResult"},at),o.DocumentChange=(st.prototype.document=null,st.prototype.targetIds=i.emptyArray,st.prototype.removedTargetIds=i.emptyArray,st.fromObject=function(e){if(e instanceof a.google.firestore.v1beta1.DocumentChange)return e;var t=new a.google.firestore.v1beta1.DocumentChange;if(null!=e.document){if("object"!=typeof e.document)throw TypeError(".google.firestore.v1beta1.DocumentChange.document: object expected");t.document=a.google.firestore.v1beta1.Document.fromObject(e.document)}if(e.targetIds){if(!Array.isArray(e.targetIds))throw TypeError(".google.firestore.v1beta1.DocumentChange.targetIds: array expected");t.targetIds=[];for(var o=0;o<e.targetIds.length;++o)t.targetIds[o]=0|e.targetIds[o]}if(e.removedTargetIds){if(!Array.isArray(e.removedTargetIds))throw TypeError(".google.firestore.v1beta1.DocumentChange.removedTargetIds: array expected");t.removedTargetIds=[];for(o=0;o<e.removedTargetIds.length;++o)t.removedTargetIds[o]=0|e.removedTargetIds[o]}return t},st.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.targetIds=[],o.removedTargetIds=[]),t.defaults&&(o.document=null),null!=e.document&&e.hasOwnProperty("document")&&(o.document=a.google.firestore.v1beta1.Document.toObject(e.document,t)),e.targetIds&&e.targetIds.length){o.targetIds=[];for(var r=0;r<e.targetIds.length;++r)o.targetIds[r]=e.targetIds[r]}if(e.removedTargetIds&&e.removedTargetIds.length){o.removedTargetIds=[];for(r=0;r<e.removedTargetIds.length;++r)o.removedTargetIds[r]=e.removedTargetIds[r]}return o},st.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},st.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.v1beta1.DocumentChange"},st),o.DocumentDelete=(lt.prototype.document="",lt.prototype.removedTargetIds=i.emptyArray,lt.prototype.readTime=null,lt.fromObject=function(e){if(e instanceof a.google.firestore.v1beta1.DocumentDelete)return e;var t=new a.google.firestore.v1beta1.DocumentDelete;if(null!=e.document&&(t.document=String(e.document)),e.removedTargetIds){if(!Array.isArray(e.removedTargetIds))throw TypeError(".google.firestore.v1beta1.DocumentDelete.removedTargetIds: array expected");t.removedTargetIds=[];for(var o=0;o<e.removedTargetIds.length;++o)t.removedTargetIds[o]=0|e.removedTargetIds[o]}if(null!=e.readTime){if("object"!=typeof e.readTime)throw TypeError(".google.firestore.v1beta1.DocumentDelete.readTime: object expected");t.readTime=a.google.protobuf.Timestamp.fromObject(e.readTime)}return t},lt.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.removedTargetIds=[]),t.defaults&&(o.document="",o.readTime=null),null!=e.document&&e.hasOwnProperty("document")&&(o.document=e.document),null!=e.readTime&&e.hasOwnProperty("readTime")&&(o.readTime=a.google.protobuf.Timestamp.toObject(e.readTime,t)),e.removedTargetIds&&e.removedTargetIds.length){o.removedTargetIds=[];for(var r=0;r<e.removedTargetIds.length;++r)o.removedTargetIds[r]=e.removedTargetIds[r]}return o},lt.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},lt.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.v1beta1.DocumentDelete"},lt),o.DocumentRemove=(ut.prototype.document="",ut.prototype.removedTargetIds=i.emptyArray,ut.prototype.readTime=null,ut.fromObject=function(e){if(e instanceof a.google.firestore.v1beta1.DocumentRemove)return e;var t=new a.google.firestore.v1beta1.DocumentRemove;if(null!=e.document&&(t.document=String(e.document)),e.removedTargetIds){if(!Array.isArray(e.removedTargetIds))throw TypeError(".google.firestore.v1beta1.DocumentRemove.removedTargetIds: array expected");t.removedTargetIds=[];for(var o=0;o<e.removedTargetIds.length;++o)t.removedTargetIds[o]=0|e.removedTargetIds[o]}if(null!=e.readTime){if("object"!=typeof e.readTime)throw TypeError(".google.firestore.v1beta1.DocumentRemove.readTime: object expected");t.readTime=a.google.protobuf.Timestamp.fromObject(e.readTime)}return t},ut.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.removedTargetIds=[]),t.defaults&&(o.document="",o.readTime=null),null!=e.document&&e.hasOwnProperty("document")&&(o.document=e.document),e.removedTargetIds&&e.removedTargetIds.length){o.removedTargetIds=[];for(var r=0;r<e.removedTargetIds.length;++r)o.removedTargetIds[r]=e.removedTargetIds[r]}return null!=e.readTime&&e.hasOwnProperty("readTime")&&(o.readTime=a.google.protobuf.Timestamp.toObject(e.readTime,t)),o},ut.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},ut.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.v1beta1.DocumentRemove"},ut),o.ExistenceFilter=(pt.prototype.targetId=0,pt.prototype.count=0,pt.fromObject=function(e){var t;return e instanceof a.google.firestore.v1beta1.ExistenceFilter?e:(t=new a.google.firestore.v1beta1.ExistenceFilter,null!=e.targetId&&(t.targetId=0|e.targetId),null!=e.count&&(t.count=0|e.count),t)},pt.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.targetId=0,o.count=0),null!=e.targetId&&e.hasOwnProperty("targetId")&&(o.targetId=e.targetId),null!=e.count&&e.hasOwnProperty("count")&&(o.count=e.count),o},pt.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},pt.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.v1beta1.ExistenceFilter"},pt),o.UndeliverableFirstGenEvent=(A.prototype.message="",A.prototype.reason=0,A.prototype.documentName="",A.prototype.documentChangeType=0,A.prototype.functionName=i.emptyArray,A.prototype.triggeredTime=null,A.fromObject=function(e){if(e instanceof a.google.firestore.v1beta1.UndeliverableFirstGenEvent)return e;var t=new a.google.firestore.v1beta1.UndeliverableFirstGenEvent;switch(null!=e.message&&(t.message=String(e.message)),e.reason){default:"number"==typeof e.reason&&(t.reason=e.reason);break;case"REASON_UNSPECIFIED":case 0:t.reason=0;break;case"EXCEEDING_SIZE_LIMIT":case 1:t.reason=1}switch(null!=e.documentName&&(t.documentName=String(e.documentName)),e.documentChangeType){default:"number"==typeof e.documentChangeType&&(t.documentChangeType=e.documentChangeType);break;case"DOCUMENT_CHANGE_TYPE_UNSPECIFIED":case 0:t.documentChangeType=0;break;case"CREATE":case 1:t.documentChangeType=1;break;case"DELETE":case 2:t.documentChangeType=2;break;case"UPDATE":case 3:t.documentChangeType=3}if(e.functionName){if(!Array.isArray(e.functionName))throw TypeError(".google.firestore.v1beta1.UndeliverableFirstGenEvent.functionName: array expected");t.functionName=[];for(var o=0;o<e.functionName.length;++o)t.functionName[o]=String(e.functionName[o])}if(null!=e.triggeredTime){if("object"!=typeof e.triggeredTime)throw TypeError(".google.firestore.v1beta1.UndeliverableFirstGenEvent.triggeredTime: object expected");t.triggeredTime=a.google.protobuf.Timestamp.fromObject(e.triggeredTime)}return t},A.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.functionName=[]),t.defaults&&(o.message="",o.reason=t.enums===String?"REASON_UNSPECIFIED":0,o.documentName="",o.documentChangeType=t.enums===String?"DOCUMENT_CHANGE_TYPE_UNSPECIFIED":0,o.triggeredTime=null),null!=e.message&&e.hasOwnProperty("message")&&(o.message=e.message),null!=e.reason&&e.hasOwnProperty("reason")&&(o.reason=t.enums!==String||void 0===a.google.firestore.v1beta1.UndeliverableFirstGenEvent.Reason[e.reason]?e.reason:a.google.firestore.v1beta1.UndeliverableFirstGenEvent.Reason[e.reason]),null!=e.documentName&&e.hasOwnProperty("documentName")&&(o.documentName=e.documentName),null!=e.documentChangeType&&e.hasOwnProperty("documentChangeType")&&(o.documentChangeType=t.enums!==String||void 0===a.google.firestore.v1beta1.UndeliverableFirstGenEvent.DocumentChangeType[e.documentChangeType]?e.documentChangeType:a.google.firestore.v1beta1.UndeliverableFirstGenEvent.DocumentChangeType[e.documentChangeType]),e.functionName&&e.functionName.length){o.functionName=[];for(var r=0;r<e.functionName.length;++r)o.functionName[r]=e.functionName[r]}return null!=e.triggeredTime&&e.hasOwnProperty("triggeredTime")&&(o.triggeredTime=a.google.protobuf.Timestamp.toObject(e.triggeredTime,t)),o},A.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},A.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.firestore.v1beta1.UndeliverableFirstGenEvent"},A.Reason=(t={},(n=Object.create(t))[t[0]="REASON_UNSPECIFIED"]="REASON_UNSPECIFIED",n[t[1]="EXCEEDING_SIZE_LIMIT"]="EXCEEDING_SIZE_LIMIT",n),A.DocumentChangeType=(t={},(n=Object.create(t))[t[0]="DOCUMENT_CHANGE_TYPE_UNSPECIFIED"]="DOCUMENT_CHANGE_TYPE_UNSPECIFIED",n[t[1]="CREATE"]="CREATE",n[t[2]="DELETE"]="DELETE",n[t[3]="UPDATE"]="UPDATE",n),A),o),e),x.type=((t={}).LatLng=(ct.prototype.latitude=0,ct.prototype.longitude=0,ct.fromObject=function(e){var t;return e instanceof a.google.type.LatLng?e:(t=new a.google.type.LatLng,null!=e.latitude&&(t.latitude=Number(e.latitude)),null!=e.longitude&&(t.longitude=Number(e.longitude)),t)},ct.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.latitude=0,o.longitude=0),null!=e.latitude&&e.hasOwnProperty("latitude")&&(o.latitude=t.json&&!isFinite(e.latitude)?String(e.latitude):e.latitude),null!=e.longitude&&e.hasOwnProperty("longitude")&&(o.longitude=t.json&&!isFinite(e.longitude)?String(e.longitude):e.longitude),o},ct.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},ct.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.type.LatLng"},ct),t.DayOfWeek=(n={},(o=Object.create(n))[n[0]="DAY_OF_WEEK_UNSPECIFIED"]="DAY_OF_WEEK_UNSPECIFIED",o[n[1]="MONDAY"]="MONDAY",o[n[2]="TUESDAY"]="TUESDAY",o[n[3]="WEDNESDAY"]="WEDNESDAY",o[n[4]="THURSDAY"]="THURSDAY",o[n[5]="FRIDAY"]="FRIDAY",o[n[6]="SATURDAY"]="SATURDAY",o[n[7]="SUNDAY"]="SUNDAY",o),t),x.api=((e={}).Http=(gt.prototype.rules=i.emptyArray,gt.prototype.fullyDecodeReservedExpansion=!1,gt.fromObject=function(e){if(e instanceof a.google.api.Http)return e;var t=new a.google.api.Http;if(e.rules){if(!Array.isArray(e.rules))throw TypeError(".google.api.Http.rules: array expected");t.rules=[];for(var o=0;o<e.rules.length;++o){if("object"!=typeof e.rules[o])throw TypeError(".google.api.Http.rules: object expected");t.rules[o]=a.google.api.HttpRule.fromObject(e.rules[o])}}return null!=e.fullyDecodeReservedExpansion&&(t.fullyDecodeReservedExpansion=Boolean(e.fullyDecodeReservedExpansion)),t},gt.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.rules=[]),t.defaults&&(o.fullyDecodeReservedExpansion=!1),e.rules&&e.rules.length){o.rules=[];for(var r=0;r<e.rules.length;++r)o.rules[r]=a.google.api.HttpRule.toObject(e.rules[r],t)}return null!=e.fullyDecodeReservedExpansion&&e.hasOwnProperty("fullyDecodeReservedExpansion")&&(o.fullyDecodeReservedExpansion=e.fullyDecodeReservedExpansion),o},gt.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},gt.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.api.Http"},gt),e.HttpRule=(k.prototype.selector="",k.prototype.get=null,k.prototype.put=null,k.prototype.post=null,k.prototype.delete=null,k.prototype.patch=null,k.prototype.custom=null,k.prototype.body="",k.prototype.responseBody="",k.prototype.additionalBindings=i.emptyArray,Object.defineProperty(k.prototype,"pattern",{get:i.oneOfGetter(n=["get","put","post","delete","patch","custom"]),set:i.oneOfSetter(n)}),k.fromObject=function(e){if(e instanceof a.google.api.HttpRule)return e;var t=new a.google.api.HttpRule;if(null!=e.selector&&(t.selector=String(e.selector)),null!=e.get&&(t.get=String(e.get)),null!=e.put&&(t.put=String(e.put)),null!=e.post&&(t.post=String(e.post)),null!=e.delete&&(t.delete=String(e.delete)),null!=e.patch&&(t.patch=String(e.patch)),null!=e.custom){if("object"!=typeof e.custom)throw TypeError(".google.api.HttpRule.custom: object expected");t.custom=a.google.api.CustomHttpPattern.fromObject(e.custom)}if(null!=e.body&&(t.body=String(e.body)),null!=e.responseBody&&(t.responseBody=String(e.responseBody)),e.additionalBindings){if(!Array.isArray(e.additionalBindings))throw TypeError(".google.api.HttpRule.additionalBindings: array expected");t.additionalBindings=[];for(var o=0;o<e.additionalBindings.length;++o){if("object"!=typeof e.additionalBindings[o])throw TypeError(".google.api.HttpRule.additionalBindings: object expected");t.additionalBindings[o]=a.google.api.HttpRule.fromObject(e.additionalBindings[o])}}return t},k.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.additionalBindings=[]),t.defaults&&(o.selector="",o.body="",o.responseBody=""),null!=e.selector&&e.hasOwnProperty("selector")&&(o.selector=e.selector),null!=e.get&&e.hasOwnProperty("get")&&(o.get=e.get,t.oneofs)&&(o.pattern="get"),null!=e.put&&e.hasOwnProperty("put")&&(o.put=e.put,t.oneofs)&&(o.pattern="put"),null!=e.post&&e.hasOwnProperty("post")&&(o.post=e.post,t.oneofs)&&(o.pattern="post"),null!=e.delete&&e.hasOwnProperty("delete")&&(o.delete=e.delete,t.oneofs)&&(o.pattern="delete"),null!=e.patch&&e.hasOwnProperty("patch")&&(o.patch=e.patch,t.oneofs)&&(o.pattern="patch"),null!=e.body&&e.hasOwnProperty("body")&&(o.body=e.body),null!=e.custom&&e.hasOwnProperty("custom")&&(o.custom=a.google.api.CustomHttpPattern.toObject(e.custom,t),t.oneofs)&&(o.pattern="custom"),e.additionalBindings&&e.additionalBindings.length){o.additionalBindings=[];for(var r=0;r<e.additionalBindings.length;++r)o.additionalBindings[r]=a.google.api.HttpRule.toObject(e.additionalBindings[r],t)}return null!=e.responseBody&&e.hasOwnProperty("responseBody")&&(o.responseBody=e.responseBody),o},k.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},k.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.api.HttpRule"},k),e.CustomHttpPattern=(ft.prototype.kind="",ft.prototype.path="",ft.fromObject=function(e){var t;return e instanceof a.google.api.CustomHttpPattern?e:(t=new a.google.api.CustomHttpPattern,null!=e.kind&&(t.kind=String(e.kind)),null!=e.path&&(t.path=String(e.path)),t)},ft.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.kind="",o.path=""),null!=e.kind&&e.hasOwnProperty("kind")&&(o.kind=e.kind),null!=e.path&&e.hasOwnProperty("path")&&(o.path=e.path),o},ft.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},ft.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.api.CustomHttpPattern"},ft),e.CommonLanguageSettings=(dt.prototype.referenceDocsUri="",dt.prototype.destinations=i.emptyArray,dt.prototype.selectiveGapicGeneration=null,dt.fromObject=function(e){if(e instanceof a.google.api.CommonLanguageSettings)return e;var t=new a.google.api.CommonLanguageSettings;if(null!=e.referenceDocsUri&&(t.referenceDocsUri=String(e.referenceDocsUri)),e.destinations){if(!Array.isArray(e.destinations))throw TypeError(".google.api.CommonLanguageSettings.destinations: array expected");t.destinations=[];for(var o=0;o<e.destinations.length;++o)switch(e.destinations[o]){default:if("number"==typeof e.destinations[o]){t.destinations[o]=e.destinations[o];break}case"CLIENT_LIBRARY_DESTINATION_UNSPECIFIED":case 0:t.destinations[o]=0;break;case"GITHUB":case 10:t.destinations[o]=10;break;case"PACKAGE_MANAGER":case 20:t.destinations[o]=20}}if(null!=e.selectiveGapicGeneration){if("object"!=typeof e.selectiveGapicGeneration)throw TypeError(".google.api.CommonLanguageSettings.selectiveGapicGeneration: object expected");t.selectiveGapicGeneration=a.google.api.SelectiveGapicGeneration.fromObject(e.selectiveGapicGeneration)}return t},dt.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.destinations=[]),t.defaults&&(o.referenceDocsUri="",o.selectiveGapicGeneration=null),null!=e.referenceDocsUri&&e.hasOwnProperty("referenceDocsUri")&&(o.referenceDocsUri=e.referenceDocsUri),e.destinations&&e.destinations.length){o.destinations=[];for(var r=0;r<e.destinations.length;++r)o.destinations[r]=t.enums!==String||void 0===a.google.api.ClientLibraryDestination[e.destinations[r]]?e.destinations[r]:a.google.api.ClientLibraryDestination[e.destinations[r]]}return null!=e.selectiveGapicGeneration&&e.hasOwnProperty("selectiveGapicGeneration")&&(o.selectiveGapicGeneration=a.google.api.SelectiveGapicGeneration.toObject(e.selectiveGapicGeneration,t)),o},dt.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},dt.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.api.CommonLanguageSettings"},dt),e.ClientLibrarySettings=(_.prototype.version="",_.prototype.launchStage=0,_.prototype.restNumericEnums=!1,_.prototype.javaSettings=null,_.prototype.cppSettings=null,_.prototype.phpSettings=null,_.prototype.pythonSettings=null,_.prototype.nodeSettings=null,_.prototype.dotnetSettings=null,_.prototype.rubySettings=null,_.prototype.goSettings=null,_.fromObject=function(e){if(e instanceof a.google.api.ClientLibrarySettings)return e;var t=new a.google.api.ClientLibrarySettings;switch(null!=e.version&&(t.version=String(e.version)),e.launchStage){default:"number"==typeof e.launchStage&&(t.launchStage=e.launchStage);break;case"LAUNCH_STAGE_UNSPECIFIED":case 0:t.launchStage=0;break;case"UNIMPLEMENTED":case 6:t.launchStage=6;break;case"PRELAUNCH":case 7:t.launchStage=7;break;case"EARLY_ACCESS":case 1:t.launchStage=1;break;case"ALPHA":case 2:t.launchStage=2;break;case"BETA":case 3:t.launchStage=3;break;case"GA":case 4:t.launchStage=4;break;case"DEPRECATED":case 5:t.launchStage=5}if(null!=e.restNumericEnums&&(t.restNumericEnums=Boolean(e.restNumericEnums)),null!=e.javaSettings){if("object"!=typeof e.javaSettings)throw TypeError(".google.api.ClientLibrarySettings.javaSettings: object expected");t.javaSettings=a.google.api.JavaSettings.fromObject(e.javaSettings)}if(null!=e.cppSettings){if("object"!=typeof e.cppSettings)throw TypeError(".google.api.ClientLibrarySettings.cppSettings: object expected");t.cppSettings=a.google.api.CppSettings.fromObject(e.cppSettings)}if(null!=e.phpSettings){if("object"!=typeof e.phpSettings)throw TypeError(".google.api.ClientLibrarySettings.phpSettings: object expected");t.phpSettings=a.google.api.PhpSettings.fromObject(e.phpSettings)}if(null!=e.pythonSettings){if("object"!=typeof e.pythonSettings)throw TypeError(".google.api.ClientLibrarySettings.pythonSettings: object expected");t.pythonSettings=a.google.api.PythonSettings.fromObject(e.pythonSettings)}if(null!=e.nodeSettings){if("object"!=typeof e.nodeSettings)throw TypeError(".google.api.ClientLibrarySettings.nodeSettings: object expected");t.nodeSettings=a.google.api.NodeSettings.fromObject(e.nodeSettings)}if(null!=e.dotnetSettings){if("object"!=typeof e.dotnetSettings)throw TypeError(".google.api.ClientLibrarySettings.dotnetSettings: object expected");t.dotnetSettings=a.google.api.DotnetSettings.fromObject(e.dotnetSettings)}if(null!=e.rubySettings){if("object"!=typeof e.rubySettings)throw TypeError(".google.api.ClientLibrarySettings.rubySettings: object expected");t.rubySettings=a.google.api.RubySettings.fromObject(e.rubySettings)}if(null!=e.goSettings){if("object"!=typeof e.goSettings)throw TypeError(".google.api.ClientLibrarySettings.goSettings: object expected");t.goSettings=a.google.api.GoSettings.fromObject(e.goSettings)}return t},_.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.version="",o.launchStage=t.enums===String?"LAUNCH_STAGE_UNSPECIFIED":0,o.restNumericEnums=!1,o.javaSettings=null,o.cppSettings=null,o.phpSettings=null,o.pythonSettings=null,o.nodeSettings=null,o.dotnetSettings=null,o.rubySettings=null,o.goSettings=null),null!=e.version&&e.hasOwnProperty("version")&&(o.version=e.version),null!=e.launchStage&&e.hasOwnProperty("launchStage")&&(o.launchStage=t.enums!==String||void 0===a.google.api.LaunchStage[e.launchStage]?e.launchStage:a.google.api.LaunchStage[e.launchStage]),null!=e.restNumericEnums&&e.hasOwnProperty("restNumericEnums")&&(o.restNumericEnums=e.restNumericEnums),null!=e.javaSettings&&e.hasOwnProperty("javaSettings")&&(o.javaSettings=a.google.api.JavaSettings.toObject(e.javaSettings,t)),null!=e.cppSettings&&e.hasOwnProperty("cppSettings")&&(o.cppSettings=a.google.api.CppSettings.toObject(e.cppSettings,t)),null!=e.phpSettings&&e.hasOwnProperty("phpSettings")&&(o.phpSettings=a.google.api.PhpSettings.toObject(e.phpSettings,t)),null!=e.pythonSettings&&e.hasOwnProperty("pythonSettings")&&(o.pythonSettings=a.google.api.PythonSettings.toObject(e.pythonSettings,t)),null!=e.nodeSettings&&e.hasOwnProperty("nodeSettings")&&(o.nodeSettings=a.google.api.NodeSettings.toObject(e.nodeSettings,t)),null!=e.dotnetSettings&&e.hasOwnProperty("dotnetSettings")&&(o.dotnetSettings=a.google.api.DotnetSettings.toObject(e.dotnetSettings,t)),null!=e.rubySettings&&e.hasOwnProperty("rubySettings")&&(o.rubySettings=a.google.api.RubySettings.toObject(e.rubySettings,t)),null!=e.goSettings&&e.hasOwnProperty("goSettings")&&(o.goSettings=a.google.api.GoSettings.toObject(e.goSettings,t)),o},_.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},_.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.api.ClientLibrarySettings"},_),e.Publishing=(C.prototype.methodSettings=i.emptyArray,C.prototype.newIssueUri="",C.prototype.documentationUri="",C.prototype.apiShortName="",C.prototype.githubLabel="",C.prototype.codeownerGithubTeams=i.emptyArray,C.prototype.docTagPrefix="",C.prototype.organization=0,C.prototype.librarySettings=i.emptyArray,C.prototype.protoReferenceDocumentationUri="",C.prototype.restReferenceDocumentationUri="",C.fromObject=function(e){if(e instanceof a.google.api.Publishing)return e;var t=new a.google.api.Publishing;if(e.methodSettings){if(!Array.isArray(e.methodSettings))throw TypeError(".google.api.Publishing.methodSettings: array expected");t.methodSettings=[];for(var o=0;o<e.methodSettings.length;++o){if("object"!=typeof e.methodSettings[o])throw TypeError(".google.api.Publishing.methodSettings: object expected");t.methodSettings[o]=a.google.api.MethodSettings.fromObject(e.methodSettings[o])}}if(null!=e.newIssueUri&&(t.newIssueUri=String(e.newIssueUri)),null!=e.documentationUri&&(t.documentationUri=String(e.documentationUri)),null!=e.apiShortName&&(t.apiShortName=String(e.apiShortName)),null!=e.githubLabel&&(t.githubLabel=String(e.githubLabel)),e.codeownerGithubTeams){if(!Array.isArray(e.codeownerGithubTeams))throw TypeError(".google.api.Publishing.codeownerGithubTeams: array expected");t.codeownerGithubTeams=[];for(o=0;o<e.codeownerGithubTeams.length;++o)t.codeownerGithubTeams[o]=String(e.codeownerGithubTeams[o])}switch(null!=e.docTagPrefix&&(t.docTagPrefix=String(e.docTagPrefix)),e.organization){default:"number"==typeof e.organization&&(t.organization=e.organization);break;case"CLIENT_LIBRARY_ORGANIZATION_UNSPECIFIED":case 0:t.organization=0;break;case"CLOUD":case 1:t.organization=1;break;case"ADS":case 2:t.organization=2;break;case"PHOTOS":case 3:t.organization=3;break;case"STREET_VIEW":case 4:t.organization=4;break;case"SHOPPING":case 5:t.organization=5;break;case"GEO":case 6:t.organization=6;break;case"GENERATIVE_AI":case 7:t.organization=7}if(e.librarySettings){if(!Array.isArray(e.librarySettings))throw TypeError(".google.api.Publishing.librarySettings: array expected");t.librarySettings=[];for(o=0;o<e.librarySettings.length;++o){if("object"!=typeof e.librarySettings[o])throw TypeError(".google.api.Publishing.librarySettings: object expected");t.librarySettings[o]=a.google.api.ClientLibrarySettings.fromObject(e.librarySettings[o])}}return null!=e.protoReferenceDocumentationUri&&(t.protoReferenceDocumentationUri=String(e.protoReferenceDocumentationUri)),null!=e.restReferenceDocumentationUri&&(t.restReferenceDocumentationUri=String(e.restReferenceDocumentationUri)),t},C.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.methodSettings=[],o.codeownerGithubTeams=[],o.librarySettings=[]),t.defaults&&(o.newIssueUri="",o.documentationUri="",o.apiShortName="",o.githubLabel="",o.docTagPrefix="",o.organization=t.enums===String?"CLIENT_LIBRARY_ORGANIZATION_UNSPECIFIED":0,o.protoReferenceDocumentationUri="",o.restReferenceDocumentationUri=""),e.methodSettings&&e.methodSettings.length){o.methodSettings=[];for(var r=0;r<e.methodSettings.length;++r)o.methodSettings[r]=a.google.api.MethodSettings.toObject(e.methodSettings[r],t)}if(null!=e.newIssueUri&&e.hasOwnProperty("newIssueUri")&&(o.newIssueUri=e.newIssueUri),null!=e.documentationUri&&e.hasOwnProperty("documentationUri")&&(o.documentationUri=e.documentationUri),null!=e.apiShortName&&e.hasOwnProperty("apiShortName")&&(o.apiShortName=e.apiShortName),null!=e.githubLabel&&e.hasOwnProperty("githubLabel")&&(o.githubLabel=e.githubLabel),e.codeownerGithubTeams&&e.codeownerGithubTeams.length){o.codeownerGithubTeams=[];for(r=0;r<e.codeownerGithubTeams.length;++r)o.codeownerGithubTeams[r]=e.codeownerGithubTeams[r]}if(null!=e.docTagPrefix&&e.hasOwnProperty("docTagPrefix")&&(o.docTagPrefix=e.docTagPrefix),null!=e.organization&&e.hasOwnProperty("organization")&&(o.organization=t.enums!==String||void 0===a.google.api.ClientLibraryOrganization[e.organization]?e.organization:a.google.api.ClientLibraryOrganization[e.organization]),e.librarySettings&&e.librarySettings.length){o.librarySettings=[];for(r=0;r<e.librarySettings.length;++r)o.librarySettings[r]=a.google.api.ClientLibrarySettings.toObject(e.librarySettings[r],t)}return null!=e.protoReferenceDocumentationUri&&e.hasOwnProperty("protoReferenceDocumentationUri")&&(o.protoReferenceDocumentationUri=e.protoReferenceDocumentationUri),null!=e.restReferenceDocumentationUri&&e.hasOwnProperty("restReferenceDocumentationUri")&&(o.restReferenceDocumentationUri=e.restReferenceDocumentationUri),o},C.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},C.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.api.Publishing"},C),e.JavaSettings=(yt.prototype.libraryPackage="",yt.prototype.serviceClassNames=i.emptyObject,yt.prototype.common=null,yt.fromObject=function(e){if(e instanceof a.google.api.JavaSettings)return e;var t=new a.google.api.JavaSettings;if(null!=e.libraryPackage&&(t.libraryPackage=String(e.libraryPackage)),e.serviceClassNames){if("object"!=typeof e.serviceClassNames)throw TypeError(".google.api.JavaSettings.serviceClassNames: object expected");t.serviceClassNames={};for(var o=Object.keys(e.serviceClassNames),r=0;r<o.length;++r)t.serviceClassNames[o[r]]=String(e.serviceClassNames[o[r]])}if(null!=e.common){if("object"!=typeof e.common)throw TypeError(".google.api.JavaSettings.common: object expected");t.common=a.google.api.CommonLanguageSettings.fromObject(e.common)}return t},yt.toObject=function(e,t){var o,r={};if(((t=t||{}).objects||t.defaults)&&(r.serviceClassNames={}),t.defaults&&(r.libraryPackage="",r.common=null),null!=e.libraryPackage&&e.hasOwnProperty("libraryPackage")&&(r.libraryPackage=e.libraryPackage),e.serviceClassNames&&(o=Object.keys(e.serviceClassNames)).length){r.serviceClassNames={};for(var n=0;n<o.length;++n)r.serviceClassNames[o[n]]=e.serviceClassNames[o[n]]}return null!=e.common&&e.hasOwnProperty("common")&&(r.common=a.google.api.CommonLanguageSettings.toObject(e.common,t)),r},yt.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},yt.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.api.JavaSettings"},yt),e.CppSettings=(mt.prototype.common=null,mt.fromObject=function(e){if(e instanceof a.google.api.CppSettings)return e;var t=new a.google.api.CppSettings;if(null!=e.common){if("object"!=typeof e.common)throw TypeError(".google.api.CppSettings.common: object expected");t.common=a.google.api.CommonLanguageSettings.fromObject(e.common)}return t},mt.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.common=null),null!=e.common&&e.hasOwnProperty("common")&&(o.common=a.google.api.CommonLanguageSettings.toObject(e.common,t)),o},mt.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},mt.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.api.CppSettings"},mt),e.PhpSettings=(bt.prototype.common=null,bt.fromObject=function(e){if(e instanceof a.google.api.PhpSettings)return e;var t=new a.google.api.PhpSettings;if(null!=e.common){if("object"!=typeof e.common)throw TypeError(".google.api.PhpSettings.common: object expected");t.common=a.google.api.CommonLanguageSettings.fromObject(e.common)}return t},bt.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.common=null),null!=e.common&&e.hasOwnProperty("common")&&(o.common=a.google.api.CommonLanguageSettings.toObject(e.common,t)),o},bt.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},bt.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.api.PhpSettings"},bt),e.PythonSettings=(Ot.prototype.common=null,Ot.prototype.experimentalFeatures=null,Ot.fromObject=function(e){if(e instanceof a.google.api.PythonSettings)return e;var t=new a.google.api.PythonSettings;if(null!=e.common){if("object"!=typeof e.common)throw TypeError(".google.api.PythonSettings.common: object expected");t.common=a.google.api.CommonLanguageSettings.fromObject(e.common)}if(null!=e.experimentalFeatures){if("object"!=typeof e.experimentalFeatures)throw TypeError(".google.api.PythonSettings.experimentalFeatures: object expected");t.experimentalFeatures=a.google.api.PythonSettings.ExperimentalFeatures.fromObject(e.experimentalFeatures)}return t},Ot.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.common=null,o.experimentalFeatures=null),null!=e.common&&e.hasOwnProperty("common")&&(o.common=a.google.api.CommonLanguageSettings.toObject(e.common,t)),null!=e.experimentalFeatures&&e.hasOwnProperty("experimentalFeatures")&&(o.experimentalFeatures=a.google.api.PythonSettings.ExperimentalFeatures.toObject(e.experimentalFeatures,t)),o},Ot.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},Ot.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.api.PythonSettings"},Ot.ExperimentalFeatures=(ht.prototype.restAsyncIoEnabled=!1,ht.prototype.protobufPythonicTypesEnabled=!1,ht.fromObject=function(e){var t;return e instanceof a.google.api.PythonSettings.ExperimentalFeatures?e:(t=new a.google.api.PythonSettings.ExperimentalFeatures,null!=e.restAsyncIoEnabled&&(t.restAsyncIoEnabled=Boolean(e.restAsyncIoEnabled)),null!=e.protobufPythonicTypesEnabled&&(t.protobufPythonicTypesEnabled=Boolean(e.protobufPythonicTypesEnabled)),t)},ht.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.restAsyncIoEnabled=!1,o.protobufPythonicTypesEnabled=!1),null!=e.restAsyncIoEnabled&&e.hasOwnProperty("restAsyncIoEnabled")&&(o.restAsyncIoEnabled=e.restAsyncIoEnabled),null!=e.protobufPythonicTypesEnabled&&e.hasOwnProperty("protobufPythonicTypesEnabled")&&(o.protobufPythonicTypesEnabled=e.protobufPythonicTypesEnabled),o},ht.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},ht.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.api.PythonSettings.ExperimentalFeatures"},ht),Ot),e.NodeSettings=(vt.prototype.common=null,vt.fromObject=function(e){if(e instanceof a.google.api.NodeSettings)return e;var t=new a.google.api.NodeSettings;if(null!=e.common){if("object"!=typeof e.common)throw TypeError(".google.api.NodeSettings.common: object expected");t.common=a.google.api.CommonLanguageSettings.fromObject(e.common)}return t},vt.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.common=null),null!=e.common&&e.hasOwnProperty("common")&&(o.common=a.google.api.CommonLanguageSettings.toObject(e.common,t)),o},vt.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},vt.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.api.NodeSettings"},vt),e.DotnetSettings=(Tt.prototype.common=null,Tt.prototype.renamedServices=i.emptyObject,Tt.prototype.renamedResources=i.emptyObject,Tt.prototype.ignoredResources=i.emptyArray,Tt.prototype.forcedNamespaceAliases=i.emptyArray,Tt.prototype.handwrittenSignatures=i.emptyArray,Tt.fromObject=function(e){if(e instanceof a.google.api.DotnetSettings)return e;var t=new a.google.api.DotnetSettings;if(null!=e.common){if("object"!=typeof e.common)throw TypeError(".google.api.DotnetSettings.common: object expected");t.common=a.google.api.CommonLanguageSettings.fromObject(e.common)}if(e.renamedServices){if("object"!=typeof e.renamedServices)throw TypeError(".google.api.DotnetSettings.renamedServices: object expected");t.renamedServices={};for(var o=Object.keys(e.renamedServices),r=0;r<o.length;++r)t.renamedServices[o[r]]=String(e.renamedServices[o[r]])}if(e.renamedResources){if("object"!=typeof e.renamedResources)throw TypeError(".google.api.DotnetSettings.renamedResources: object expected");t.renamedResources={};for(o=Object.keys(e.renamedResources),r=0;r<o.length;++r)t.renamedResources[o[r]]=String(e.renamedResources[o[r]])}if(e.ignoredResources){if(!Array.isArray(e.ignoredResources))throw TypeError(".google.api.DotnetSettings.ignoredResources: array expected");t.ignoredResources=[];for(r=0;r<e.ignoredResources.length;++r)t.ignoredResources[r]=String(e.ignoredResources[r])}if(e.forcedNamespaceAliases){if(!Array.isArray(e.forcedNamespaceAliases))throw TypeError(".google.api.DotnetSettings.forcedNamespaceAliases: array expected");t.forcedNamespaceAliases=[];for(r=0;r<e.forcedNamespaceAliases.length;++r)t.forcedNamespaceAliases[r]=String(e.forcedNamespaceAliases[r])}if(e.handwrittenSignatures){if(!Array.isArray(e.handwrittenSignatures))throw TypeError(".google.api.DotnetSettings.handwrittenSignatures: array expected");t.handwrittenSignatures=[];for(r=0;r<e.handwrittenSignatures.length;++r)t.handwrittenSignatures[r]=String(e.handwrittenSignatures[r])}return t},Tt.toObject=function(e,t){var o,r={};if(((t=t||{}).arrays||t.defaults)&&(r.ignoredResources=[],r.forcedNamespaceAliases=[],r.handwrittenSignatures=[]),(t.objects||t.defaults)&&(r.renamedServices={},r.renamedResources={}),t.defaults&&(r.common=null),null!=e.common&&e.hasOwnProperty("common")&&(r.common=a.google.api.CommonLanguageSettings.toObject(e.common,t)),e.renamedServices&&(o=Object.keys(e.renamedServices)).length){r.renamedServices={};for(var n=0;n<o.length;++n)r.renamedServices[o[n]]=e.renamedServices[o[n]]}if(e.renamedResources&&(o=Object.keys(e.renamedResources)).length){r.renamedResources={};for(n=0;n<o.length;++n)r.renamedResources[o[n]]=e.renamedResources[o[n]]}if(e.ignoredResources&&e.ignoredResources.length){r.ignoredResources=[];for(n=0;n<e.ignoredResources.length;++n)r.ignoredResources[n]=e.ignoredResources[n]}if(e.forcedNamespaceAliases&&e.forcedNamespaceAliases.length){r.forcedNamespaceAliases=[];for(n=0;n<e.forcedNamespaceAliases.length;++n)r.forcedNamespaceAliases[n]=e.forcedNamespaceAliases[n]}if(e.handwrittenSignatures&&e.handwrittenSignatures.length){r.handwrittenSignatures=[];for(n=0;n<e.handwrittenSignatures.length;++n)r.handwrittenSignatures[n]=e.handwrittenSignatures[n]}return r},Tt.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},Tt.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.api.DotnetSettings"},Tt),e.RubySettings=(St.prototype.common=null,St.fromObject=function(e){if(e instanceof a.google.api.RubySettings)return e;var t=new a.google.api.RubySettings;if(null!=e.common){if("object"!=typeof e.common)throw TypeError(".google.api.RubySettings.common: object expected");t.common=a.google.api.CommonLanguageSettings.fromObject(e.common)}return t},St.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.common=null),null!=e.common&&e.hasOwnProperty("common")&&(o.common=a.google.api.CommonLanguageSettings.toObject(e.common,t)),o},St.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},St.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.api.RubySettings"},St),e.GoSettings=(jt.prototype.common=null,jt.prototype.renamedServices=i.emptyObject,jt.fromObject=function(e){if(e instanceof a.google.api.GoSettings)return e;var t=new a.google.api.GoSettings;if(null!=e.common){if("object"!=typeof e.common)throw TypeError(".google.api.GoSettings.common: object expected");t.common=a.google.api.CommonLanguageSettings.fromObject(e.common)}if(e.renamedServices){if("object"!=typeof e.renamedServices)throw TypeError(".google.api.GoSettings.renamedServices: object expected");t.renamedServices={};for(var o=Object.keys(e.renamedServices),r=0;r<o.length;++r)t.renamedServices[o[r]]=String(e.renamedServices[o[r]])}return t},jt.toObject=function(e,t){var o,r={};if(((t=t||{}).objects||t.defaults)&&(r.renamedServices={}),t.defaults&&(r.common=null),null!=e.common&&e.hasOwnProperty("common")&&(r.common=a.google.api.CommonLanguageSettings.toObject(e.common,t)),e.renamedServices&&(o=Object.keys(e.renamedServices)).length){r.renamedServices={};for(var n=0;n<o.length;++n)r.renamedServices[o[n]]=e.renamedServices[o[n]]}return r},jt.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},jt.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.api.GoSettings"},jt),e.MethodSettings=(Et.prototype.selector="",Et.prototype.longRunning=null,Et.prototype.autoPopulatedFields=i.emptyArray,Et.fromObject=function(e){if(e instanceof a.google.api.MethodSettings)return e;var t=new a.google.api.MethodSettings;if(null!=e.selector&&(t.selector=String(e.selector)),null!=e.longRunning){if("object"!=typeof e.longRunning)throw TypeError(".google.api.MethodSettings.longRunning: object expected");t.longRunning=a.google.api.MethodSettings.LongRunning.fromObject(e.longRunning)}if(e.autoPopulatedFields){if(!Array.isArray(e.autoPopulatedFields))throw TypeError(".google.api.MethodSettings.autoPopulatedFields: array expected");t.autoPopulatedFields=[];for(var o=0;o<e.autoPopulatedFields.length;++o)t.autoPopulatedFields[o]=String(e.autoPopulatedFields[o])}return t},Et.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.autoPopulatedFields=[]),t.defaults&&(o.selector="",o.longRunning=null),null!=e.selector&&e.hasOwnProperty("selector")&&(o.selector=e.selector),null!=e.longRunning&&e.hasOwnProperty("longRunning")&&(o.longRunning=a.google.api.MethodSettings.LongRunning.toObject(e.longRunning,t)),e.autoPopulatedFields&&e.autoPopulatedFields.length){o.autoPopulatedFields=[];for(var r=0;r<e.autoPopulatedFields.length;++r)o.autoPopulatedFields[r]=e.autoPopulatedFields[r]}return o},Et.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},Et.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.api.MethodSettings"},Et.LongRunning=(Nt.prototype.initialPollDelay=null,Nt.prototype.pollDelayMultiplier=0,Nt.prototype.maxPollDelay=null,Nt.prototype.totalPollTimeout=null,Nt.fromObject=function(e){if(e instanceof a.google.api.MethodSettings.LongRunning)return e;var t=new a.google.api.MethodSettings.LongRunning;if(null!=e.initialPollDelay){if("object"!=typeof e.initialPollDelay)throw TypeError(".google.api.MethodSettings.LongRunning.initialPollDelay: object expected");t.initialPollDelay=a.google.protobuf.Duration.fromObject(e.initialPollDelay)}if(null!=e.pollDelayMultiplier&&(t.pollDelayMultiplier=Number(e.pollDelayMultiplier)),null!=e.maxPollDelay){if("object"!=typeof e.maxPollDelay)throw TypeError(".google.api.MethodSettings.LongRunning.maxPollDelay: object expected");t.maxPollDelay=a.google.protobuf.Duration.fromObject(e.maxPollDelay)}if(null!=e.totalPollTimeout){if("object"!=typeof e.totalPollTimeout)throw TypeError(".google.api.MethodSettings.LongRunning.totalPollTimeout: object expected");t.totalPollTimeout=a.google.protobuf.Duration.fromObject(e.totalPollTimeout)}return t},Nt.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.initialPollDelay=null,o.pollDelayMultiplier=0,o.maxPollDelay=null,o.totalPollTimeout=null),null!=e.initialPollDelay&&e.hasOwnProperty("initialPollDelay")&&(o.initialPollDelay=a.google.protobuf.Duration.toObject(e.initialPollDelay,t)),null!=e.pollDelayMultiplier&&e.hasOwnProperty("pollDelayMultiplier")&&(o.pollDelayMultiplier=t.json&&!isFinite(e.pollDelayMultiplier)?String(e.pollDelayMultiplier):e.pollDelayMultiplier),null!=e.maxPollDelay&&e.hasOwnProperty("maxPollDelay")&&(o.maxPollDelay=a.google.protobuf.Duration.toObject(e.maxPollDelay,t)),null!=e.totalPollTimeout&&e.hasOwnProperty("totalPollTimeout")&&(o.totalPollTimeout=a.google.protobuf.Duration.toObject(e.totalPollTimeout,t)),o},Nt.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},Nt.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.api.MethodSettings.LongRunning"},Nt),Et),e.ClientLibraryOrganization=(n={},(o=Object.create(n))[n[0]="CLIENT_LIBRARY_ORGANIZATION_UNSPECIFIED"]="CLIENT_LIBRARY_ORGANIZATION_UNSPECIFIED",o[n[1]="CLOUD"]="CLOUD",o[n[2]="ADS"]="ADS",o[n[3]="PHOTOS"]="PHOTOS",o[n[4]="STREET_VIEW"]="STREET_VIEW",o[n[5]="SHOPPING"]="SHOPPING",o[n[6]="GEO"]="GEO",o[n[7]="GENERATIVE_AI"]="GENERATIVE_AI",o),e.ClientLibraryDestination=(n={},(o=Object.create(n))[n[0]="CLIENT_LIBRARY_DESTINATION_UNSPECIFIED"]="CLIENT_LIBRARY_DESTINATION_UNSPECIFIED",o[n[10]="GITHUB"]="GITHUB",o[n[20]="PACKAGE_MANAGER"]="PACKAGE_MANAGER",o),e.SelectiveGapicGeneration=(wt.prototype.methods=i.emptyArray,wt.prototype.generateOmittedAsInternal=!1,wt.fromObject=function(e){if(e instanceof a.google.api.SelectiveGapicGeneration)return e;var t=new a.google.api.SelectiveGapicGeneration;if(e.methods){if(!Array.isArray(e.methods))throw TypeError(".google.api.SelectiveGapicGeneration.methods: array expected");t.methods=[];for(var o=0;o<e.methods.length;++o)t.methods[o]=String(e.methods[o])}return null!=e.generateOmittedAsInternal&&(t.generateOmittedAsInternal=Boolean(e.generateOmittedAsInternal)),t},wt.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.methods=[]),t.defaults&&(o.generateOmittedAsInternal=!1),e.methods&&e.methods.length){o.methods=[];for(var r=0;r<e.methods.length;++r)o.methods[r]=e.methods[r]}return null!=e.generateOmittedAsInternal&&e.hasOwnProperty("generateOmittedAsInternal")&&(o.generateOmittedAsInternal=e.generateOmittedAsInternal),o},wt.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},wt.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.api.SelectiveGapicGeneration"},wt),e.LaunchStage=(n={},(o=Object.create(n))[n[0]="LAUNCH_STAGE_UNSPECIFIED"]="LAUNCH_STAGE_UNSPECIFIED",o[n[6]="UNIMPLEMENTED"]="UNIMPLEMENTED",o[n[7]="PRELAUNCH"]="PRELAUNCH",o[n[1]="EARLY_ACCESS"]="EARLY_ACCESS",o[n[2]="ALPHA"]="ALPHA",o[n[3]="BETA"]="BETA",o[n[4]="GA"]="GA",o[n[5]="DEPRECATED"]="DEPRECATED",o),e.FieldBehavior=(n={},(o=Object.create(n))[n[0]="FIELD_BEHAVIOR_UNSPECIFIED"]="FIELD_BEHAVIOR_UNSPECIFIED",o[n[1]="OPTIONAL"]="OPTIONAL",o[n[2]="REQUIRED"]="REQUIRED",o[n[3]="OUTPUT_ONLY"]="OUTPUT_ONLY",o[n[4]="INPUT_ONLY"]="INPUT_ONLY",o[n[5]="IMMUTABLE"]="IMMUTABLE",o[n[6]="UNORDERED_LIST"]="UNORDERED_LIST",o[n[7]="NON_EMPTY_DEFAULT"]="NON_EMPTY_DEFAULT",o[n[8]="IDENTIFIER"]="IDENTIFIER",o),e.ResourceDescriptor=(F.prototype.type="",F.prototype.pattern=i.emptyArray,F.prototype.nameField="",F.prototype.history=0,F.prototype.plural="",F.prototype.singular="",F.prototype.style=i.emptyArray,F.fromObject=function(e){if(e instanceof a.google.api.ResourceDescriptor)return e;var t=new a.google.api.ResourceDescriptor;if(null!=e.type&&(t.type=String(e.type)),e.pattern){if(!Array.isArray(e.pattern))throw TypeError(".google.api.ResourceDescriptor.pattern: array expected");t.pattern=[];for(var o=0;o<e.pattern.length;++o)t.pattern[o]=String(e.pattern[o])}switch(null!=e.nameField&&(t.nameField=String(e.nameField)),e.history){default:"number"==typeof e.history&&(t.history=e.history);break;case"HISTORY_UNSPECIFIED":case 0:t.history=0;break;case"ORIGINALLY_SINGLE_PATTERN":case 1:t.history=1;break;case"FUTURE_MULTI_PATTERN":case 2:t.history=2}if(null!=e.plural&&(t.plural=String(e.plural)),null!=e.singular&&(t.singular=String(e.singular)),e.style){if(!Array.isArray(e.style))throw TypeError(".google.api.ResourceDescriptor.style: array expected");t.style=[];for(o=0;o<e.style.length;++o)switch(e.style[o]){default:if("number"==typeof e.style[o]){t.style[o]=e.style[o];break}case"STYLE_UNSPECIFIED":case 0:t.style[o]=0;break;case"DECLARATIVE_FRIENDLY":case 1:t.style[o]=1}}return t},F.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.pattern=[],o.style=[]),t.defaults&&(o.type="",o.nameField="",o.history=t.enums===String?"HISTORY_UNSPECIFIED":0,o.plural="",o.singular=""),null!=e.type&&e.hasOwnProperty("type")&&(o.type=e.type),e.pattern&&e.pattern.length){o.pattern=[];for(var r=0;r<e.pattern.length;++r)o.pattern[r]=e.pattern[r]}if(null!=e.nameField&&e.hasOwnProperty("nameField")&&(o.nameField=e.nameField),null!=e.history&&e.hasOwnProperty("history")&&(o.history=t.enums!==String||void 0===a.google.api.ResourceDescriptor.History[e.history]?e.history:a.google.api.ResourceDescriptor.History[e.history]),null!=e.plural&&e.hasOwnProperty("plural")&&(o.plural=e.plural),null!=e.singular&&e.hasOwnProperty("singular")&&(o.singular=e.singular),e.style&&e.style.length){o.style=[];for(r=0;r<e.style.length;++r)o.style[r]=t.enums!==String||void 0===a.google.api.ResourceDescriptor.Style[e.style[r]]?e.style[r]:a.google.api.ResourceDescriptor.Style[e.style[r]]}return o},F.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},F.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.api.ResourceDescriptor"},F.History=(n={},(o=Object.create(n))[n[0]="HISTORY_UNSPECIFIED"]="HISTORY_UNSPECIFIED",o[n[1]="ORIGINALLY_SINGLE_PATTERN"]="ORIGINALLY_SINGLE_PATTERN",o[n[2]="FUTURE_MULTI_PATTERN"]="FUTURE_MULTI_PATTERN",o),F.Style=(n={},(o=Object.create(n))[n[0]="STYLE_UNSPECIFIED"]="STYLE_UNSPECIFIED",o[n[1]="DECLARATIVE_FRIENDLY"]="DECLARATIVE_FRIENDLY",o),F),e.ResourceReference=(Pt.prototype.type="",Pt.prototype.childType="",Pt.fromObject=function(e){var t;return e instanceof a.google.api.ResourceReference?e:(t=new a.google.api.ResourceReference,null!=e.type&&(t.type=String(e.type)),null!=e.childType&&(t.childType=String(e.childType)),t)},Pt.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.type="",o.childType=""),null!=e.type&&e.hasOwnProperty("type")&&(o.type=e.type),null!=e.childType&&e.hasOwnProperty("childType")&&(o.childType=e.childType),o},Pt.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},Pt.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.api.ResourceReference"},Pt),e),x.rpc=((t={}).Status=(Dt.prototype.code=0,Dt.prototype.message="",Dt.prototype.details=i.emptyArray,Dt.fromObject=function(e){if(e instanceof a.google.rpc.Status)return e;var t=new a.google.rpc.Status;if(null!=e.code&&(t.code=0|e.code),null!=e.message&&(t.message=String(e.message)),e.details){if(!Array.isArray(e.details))throw TypeError(".google.rpc.Status.details: array expected");t.details=[];for(var o=0;o<e.details.length;++o){if("object"!=typeof e.details[o])throw TypeError(".google.rpc.Status.details: object expected");t.details[o]=a.google.protobuf.Any.fromObject(e.details[o])}}return t},Dt.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.details=[]),t.defaults&&(o.code=0,o.message=""),null!=e.code&&e.hasOwnProperty("code")&&(o.code=e.code),null!=e.message&&e.hasOwnProperty("message")&&(o.message=e.message),e.details&&e.details.length){o.details=[];for(var r=0;r<e.details.length;++r)o.details[r]=a.google.protobuf.Any.toObject(e.details[r],t)}return o},Dt.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},Dt.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.rpc.Status"},Dt),t),x.longrunning=((n={}).Operations=((Rt.prototype=Object.create(r.rpc.Service.prototype)).constructor=Rt,Object.defineProperty(Rt.prototype.listOperations=function e(t,o){return this.rpcCall(e,a.google.longrunning.ListOperationsRequest,a.google.longrunning.ListOperationsResponse,t,o)},"name",{value:"ListOperations"}),Object.defineProperty(Rt.prototype.getOperation=function e(t,o){return this.rpcCall(e,a.google.longrunning.GetOperationRequest,a.google.longrunning.Operation,t,o)},"name",{value:"GetOperation"}),Object.defineProperty(Rt.prototype.deleteOperation=function e(t,o){return this.rpcCall(e,a.google.longrunning.DeleteOperationRequest,a.google.protobuf.Empty,t,o)},"name",{value:"DeleteOperation"}),Object.defineProperty(Rt.prototype.cancelOperation=function e(t,o){return this.rpcCall(e,a.google.longrunning.CancelOperationRequest,a.google.protobuf.Empty,t,o)},"name",{value:"CancelOperation"}),Object.defineProperty(Rt.prototype.waitOperation=function e(t,o){return this.rpcCall(e,a.google.longrunning.WaitOperationRequest,a.google.longrunning.Operation,t,o)},"name",{value:"WaitOperation"}),Rt),n.Operation=(It.prototype.name="",It.prototype.metadata=null,It.prototype.done=!1,It.prototype.error=null,It.prototype.response=null,Object.defineProperty(It.prototype,"result",{get:i.oneOfGetter(o=["error","response"]),set:i.oneOfSetter(o)}),It.fromObject=function(e){if(e instanceof a.google.longrunning.Operation)return e;var t=new a.google.longrunning.Operation;if(null!=e.name&&(t.name=String(e.name)),null!=e.metadata){if("object"!=typeof e.metadata)throw TypeError(".google.longrunning.Operation.metadata: object expected");t.metadata=a.google.protobuf.Any.fromObject(e.metadata)}if(null!=e.done&&(t.done=Boolean(e.done)),null!=e.error){if("object"!=typeof e.error)throw TypeError(".google.longrunning.Operation.error: object expected");t.error=a.google.rpc.Status.fromObject(e.error)}if(null!=e.response){if("object"!=typeof e.response)throw TypeError(".google.longrunning.Operation.response: object expected");t.response=a.google.protobuf.Any.fromObject(e.response)}return t},It.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.name="",o.metadata=null,o.done=!1),null!=e.name&&e.hasOwnProperty("name")&&(o.name=e.name),null!=e.metadata&&e.hasOwnProperty("metadata")&&(o.metadata=a.google.protobuf.Any.toObject(e.metadata,t)),null!=e.done&&e.hasOwnProperty("done")&&(o.done=e.done),null!=e.error&&e.hasOwnProperty("error")&&(o.error=a.google.rpc.Status.toObject(e.error,t),t.oneofs)&&(o.result="error"),null!=e.response&&e.hasOwnProperty("response")&&(o.response=a.google.protobuf.Any.toObject(e.response,t),t.oneofs)&&(o.result="response"),o},It.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},It.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.longrunning.Operation"},It),n.GetOperationRequest=(At.prototype.name="",At.fromObject=function(e){var t;return e instanceof a.google.longrunning.GetOperationRequest?e:(t=new a.google.longrunning.GetOperationRequest,null!=e.name&&(t.name=String(e.name)),t)},At.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.name=""),null!=e.name&&e.hasOwnProperty("name")&&(o.name=e.name),o},At.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},At.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.longrunning.GetOperationRequest"},At),n.ListOperationsRequest=(kt.prototype.name="",kt.prototype.filter="",kt.prototype.pageSize=0,kt.prototype.pageToken="",kt.fromObject=function(e){var t;return e instanceof a.google.longrunning.ListOperationsRequest?e:(t=new a.google.longrunning.ListOperationsRequest,null!=e.name&&(t.name=String(e.name)),null!=e.filter&&(t.filter=String(e.filter)),null!=e.pageSize&&(t.pageSize=0|e.pageSize),null!=e.pageToken&&(t.pageToken=String(e.pageToken)),t)},kt.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.filter="",o.pageSize=0,o.pageToken="",o.name=""),null!=e.filter&&e.hasOwnProperty("filter")&&(o.filter=e.filter),null!=e.pageSize&&e.hasOwnProperty("pageSize")&&(o.pageSize=e.pageSize),null!=e.pageToken&&e.hasOwnProperty("pageToken")&&(o.pageToken=e.pageToken),null!=e.name&&e.hasOwnProperty("name")&&(o.name=e.name),o},kt.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},kt.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.longrunning.ListOperationsRequest"},kt),n.ListOperationsResponse=(_t.prototype.operations=i.emptyArray,_t.prototype.nextPageToken="",_t.fromObject=function(e){if(e instanceof a.google.longrunning.ListOperationsResponse)return e;var t=new a.google.longrunning.ListOperationsResponse;if(e.operations){if(!Array.isArray(e.operations))throw TypeError(".google.longrunning.ListOperationsResponse.operations: array expected");t.operations=[];for(var o=0;o<e.operations.length;++o){if("object"!=typeof e.operations[o])throw TypeError(".google.longrunning.ListOperationsResponse.operations: object expected");t.operations[o]=a.google.longrunning.Operation.fromObject(e.operations[o])}}return null!=e.nextPageToken&&(t.nextPageToken=String(e.nextPageToken)),t},_t.toObject=function(e,t){var o={};if(((t=t||{}).arrays||t.defaults)&&(o.operations=[]),t.defaults&&(o.nextPageToken=""),e.operations&&e.operations.length){o.operations=[];for(var r=0;r<e.operations.length;++r)o.operations[r]=a.google.longrunning.Operation.toObject(e.operations[r],t)}return null!=e.nextPageToken&&e.hasOwnProperty("nextPageToken")&&(o.nextPageToken=e.nextPageToken),o},_t.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},_t.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.longrunning.ListOperationsResponse"},_t),n.CancelOperationRequest=(Ct.prototype.name="",Ct.fromObject=function(e){var t;return e instanceof a.google.longrunning.CancelOperationRequest?e:(t=new a.google.longrunning.CancelOperationRequest,null!=e.name&&(t.name=String(e.name)),t)},Ct.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.name=""),null!=e.name&&e.hasOwnProperty("name")&&(o.name=e.name),o},Ct.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},Ct.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.longrunning.CancelOperationRequest"},Ct),n.DeleteOperationRequest=(Ft.prototype.name="",Ft.fromObject=function(e){var t;return e instanceof a.google.longrunning.DeleteOperationRequest?e:(t=new a.google.longrunning.DeleteOperationRequest,null!=e.name&&(t.name=String(e.name)),t)},Ft.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.name=""),null!=e.name&&e.hasOwnProperty("name")&&(o.name=e.name),o},Ft.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},Ft.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.longrunning.DeleteOperationRequest"},Ft),n.WaitOperationRequest=(xt.prototype.name="",xt.prototype.timeout=null,xt.fromObject=function(e){if(e instanceof a.google.longrunning.WaitOperationRequest)return e;var t=new a.google.longrunning.WaitOperationRequest;if(null!=e.name&&(t.name=String(e.name)),null!=e.timeout){if("object"!=typeof e.timeout)throw TypeError(".google.longrunning.WaitOperationRequest.timeout: object expected");t.timeout=a.google.protobuf.Duration.fromObject(e.timeout)}return t},xt.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.name="",o.timeout=null),null!=e.name&&e.hasOwnProperty("name")&&(o.name=e.name),null!=e.timeout&&e.hasOwnProperty("timeout")&&(o.timeout=a.google.protobuf.Duration.toObject(e.timeout,t)),o},xt.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},xt.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.longrunning.WaitOperationRequest"},xt),n.OperationInfo=(Vt.prototype.responseType="",Vt.prototype.metadataType="",Vt.fromObject=function(e){var t;return e instanceof a.google.longrunning.OperationInfo?e:(t=new a.google.longrunning.OperationInfo,null!=e.responseType&&(t.responseType=String(e.responseType)),null!=e.metadataType&&(t.metadataType=String(e.metadataType)),t)},Vt.toObject=function(e,t){var o={};return(t=t||{}).defaults&&(o.responseType="",o.metadataType=""),null!=e.responseType&&e.hasOwnProperty("responseType")&&(o.responseType=e.responseType),null!=e.metadataType&&e.hasOwnProperty("metadataType")&&(o.metadataType=e.metadataType),o},Vt.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},Vt.getTypeUrl=function(e){return(e=void 0===e?"type.googleapis.com":e)+"/google.longrunning.OperationInfo"},Vt),n),x),a});