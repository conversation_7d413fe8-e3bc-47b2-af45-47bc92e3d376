# ملف إعدادات البيئة لمشروع Malite Agent
# انسخ هذا الملف إلى .env وأضف قيمك الفعلية

# ==============================================
# إعدادات Google Cloud و Vertex AI
# ==============================================

# معرف مشروع Google Cloud
GOOGLE_CLOUD_PROJECT=your-project-id

# منطقة Vertex AI (مثل us-central1, europe-west1)
VERTEX_AI_LOCATION=us-central1

# مسار ملف مفاتيح الخدمة (Service Account Key)
GOOGLE_APPLICATION_CREDENTIALS=path/to/your/service-account-key.json

# ==============================================
# إعدادات النماذج المفضلة
# ==============================================

# النموذج الافتراضي للنصوص
DEFAULT_TEXT_MODEL=gemini.pro15

# النموذج الافتراضي للرؤية
DEFAULT_VISION_MODEL=geminiVision15

# النموذج الافتراضي للتضمين
DEFAULT_EMBEDDING_MODEL=textEmbeddingMultilingual

# النموذج الافتراضي للبرمجة
DEFAULT_CODE_MODEL=codestral

# ==============================================
# إعدادات الأداء
# ==============================================

# درجة الحرارة الافتراضية (0.0 - 1.0)
DEFAULT_TEMPERATURE=0.7

# الحد الأقصى لعدد الرموز في الاستجابة
DEFAULT_MAX_OUTPUT_TOKENS=1024

# قيمة Top-K الافتراضية
DEFAULT_TOP_K=40

# قيمة Top-P الافتراضية
DEFAULT_TOP_P=0.95

# ==============================================
# إعدادات النماذج المتقدمة
# ==============================================

# تفعيل النماذج التجريبية (true/false)
ENABLE_EXPERIMENTAL_MODELS=false

# تفعيل نماذج الصور (true/false)
ENABLE_IMAGE_GENERATION=true

# تفعيل نماذج الصوت (true/false)
ENABLE_AUDIO_MODELS=false

# ==============================================
# إعدادات الأمان والحدود
# ==============================================

# الحد الأقصى لعدد الطلبات في الدقيقة
MAX_REQUESTS_PER_MINUTE=60

# الحد الأقصى لحجم الملف المرفوع (بالميجابايت)
MAX_FILE_SIZE_MB=10

# تفعيل فلترة المحتوى (true/false)
ENABLE_CONTENT_FILTERING=true

# مستوى الأمان (LOW, MEDIUM, HIGH)
SAFETY_LEVEL=MEDIUM

# ==============================================
# إعدادات التطوير والتشخيص
# ==============================================

# مستوى السجلات (DEBUG, INFO, WARN, ERROR)
LOG_LEVEL=INFO

# تفعيل وضع التطوير (true/false)
DEVELOPMENT_MODE=true

# تفعيل التتبع المفصل (true/false)
ENABLE_DETAILED_LOGGING=false

# حفظ الطلبات والاستجابات للتشخيص (true/false)
SAVE_REQUEST_RESPONSE_LOGS=false

# ==============================================
# إعدادات النماذج المخصصة
# ==============================================

# إعدادات Gemini المخصصة
GEMINI_TEMPERATURE=0.7
GEMINI_MAX_TOKENS=2048
GEMINI_TOP_P=0.8

# إعدادات Claude المخصصة
CLAUDE_TEMPERATURE=0.3
CLAUDE_MAX_TOKENS=4000
CLAUDE_TOP_P=0.9

# إعدادات Llama المخصصة
LLAMA_TEMPERATURE=0.8
LLAMA_MAX_TOKENS=2048
LLAMA_TOP_P=0.95

# إعدادات Mistral المخصصة
MISTRAL_TEMPERATURE=0.5
MISTRAL_MAX_TOKENS=1500
MISTRAL_TOP_P=0.85

# ==============================================
# إعدادات التخزين المؤقت
# ==============================================

# تفعيل التخزين المؤقت (true/false)
ENABLE_CACHING=true

# مدة التخزين المؤقت بالثواني
CACHE_DURATION_SECONDS=3600

# حجم التخزين المؤقت الأقصى (بالميجابايت)
MAX_CACHE_SIZE_MB=100

# ==============================================
# إعدادات المراقبة والتحليلات
# ==============================================

# تفعيل مراقبة الأداء (true/false)
ENABLE_PERFORMANCE_MONITORING=true

# تفعيل تحليلات الاستخدام (true/false)
ENABLE_USAGE_ANALYTICS=false

# معرف مشروع التحليلات (اختياري)
ANALYTICS_PROJECT_ID=

# ==============================================
# إعدادات التكامل مع خدمات أخرى
# ==============================================

# تفعيل التكامل مع Firebase (true/false)
ENABLE_FIREBASE_INTEGRATION=false

# معرف مشروع Firebase
FIREBASE_PROJECT_ID=

# تفعيل التكامل مع BigQuery (true/false)
ENABLE_BIGQUERY_INTEGRATION=false

# معرف مجموعة بيانات BigQuery
BIGQUERY_DATASET_ID=

# ==============================================
# إعدادات اللغة والتوطين
# ==============================================

# اللغة الافتراضية (ar, en, fr, etc.)
DEFAULT_LANGUAGE=ar

# تفعيل الترجمة التلقائية (true/false)
ENABLE_AUTO_TRANSLATION=true

# تفعيل دعم اللغات المتعددة (true/false)
ENABLE_MULTILINGUAL_SUPPORT=true

# ==============================================
# إعدادات الشبكة والاتصال
# ==============================================

# مهلة الاتصال بالثواني
CONNECTION_TIMEOUT_SECONDS=30

# عدد محاولات إعادة الاتصال
MAX_RETRY_ATTEMPTS=3

# تأخير إعادة المحاولة بالثواني
RETRY_DELAY_SECONDS=2

# تفعيل ضغط البيانات (true/false)
ENABLE_COMPRESSION=true

# ==============================================
# ملاحظات مهمة
# ==============================================

# 1. تأكد من إعداد GOOGLE_APPLICATION_CREDENTIALS بشكل صحيح
# 2. استخدم مشروع Google Cloud مع تفعيل Vertex AI API
# 3. تحقق من الحصص والحدود في مشروعك
# 4. احتفظ بنسخة احتياطية من ملف .env
# 5. لا تشارك ملف .env أو تضعه في نظام التحكم بالإصدارات

# ==============================================
# روابط مفيدة
# ==============================================

# وثائق Vertex AI: https://cloud.google.com/vertex-ai/docs
# وثائق Genkit: https://firebase.google.com/docs/genkit
# إدارة مشاريع Google Cloud: https://console.cloud.google.com/
