"use strict";
/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.SumAggregator = exports.SumAccumulation = exports.LastValueAggregator = exports.LastValueAccumulation = exports.ExponentialHistogramAggregator = exports.ExponentialHistogramAccumulation = exports.HistogramAggregator = exports.HistogramAccumulation = exports.DropAggregator = void 0;
var Drop_1 = require("./Drop");
Object.defineProperty(exports, "DropAggregator", { enumerable: true, get: function () { return Drop_1.DropAggregator; } });
var Histogram_1 = require("./Histogram");
Object.defineProperty(exports, "HistogramAccumulation", { enumerable: true, get: function () { return Histogram_1.HistogramAccumulation; } });
Object.defineProperty(exports, "HistogramAggregator", { enumerable: true, get: function () { return Histogram_1.HistogramAggregator; } });
var ExponentialHistogram_1 = require("./ExponentialHistogram");
Object.defineProperty(exports, "ExponentialHistogramAccumulation", { enumerable: true, get: function () { return ExponentialHistogram_1.ExponentialHistogramAccumulation; } });
Object.defineProperty(exports, "ExponentialHistogramAggregator", { enumerable: true, get: function () { return ExponentialHistogram_1.ExponentialHistogramAggregator; } });
var LastValue_1 = require("./LastValue");
Object.defineProperty(exports, "LastValueAccumulation", { enumerable: true, get: function () { return LastValue_1.LastValueAccumulation; } });
Object.defineProperty(exports, "LastValueAggregator", { enumerable: true, get: function () { return LastValue_1.LastValueAggregator; } });
var Sum_1 = require("./Sum");
Object.defineProperty(exports, "SumAccumulation", { enumerable: true, get: function () { return Sum_1.SumAccumulation; } });
Object.defineProperty(exports, "SumAggregator", { enumerable: true, get: function () { return Sum_1.SumAggregator; } });
//# sourceMappingURL=index.js.map