{"version": 3, "file": "LoggerProvider.js", "sourceRoot": "", "sources": ["../../src/LoggerProvider.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AACH,OAAO,EAAE,IAAI,EAAE,MAAM,oBAAoB,CAAC;AAE1C,OAAO,EAAE,WAAW,EAAE,MAAM,yBAAyB,CAAC;AACtD,OAAO,EAAE,QAAQ,EAAE,MAAM,0BAA0B,CAAC;AACpD,OAAO,EAAE,cAAc,EAAE,KAAK,EAAE,MAAM,qBAAqB,CAAC;AAI5D,OAAO,EAAE,MAAM,EAAE,MAAM,UAAU,CAAC;AAClC,OAAO,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,MAAM,UAAU,CAAC;AAChE,OAAO,EAAE,uBAAuB,EAAE,MAAM,2BAA2B,CAAC;AACpE,OAAO,EAAE,yBAAyB,EAAE,MAAM,sCAAsC,CAAC;AAEjF,MAAM,CAAC,MAAM,mBAAmB,GAAG,SAAS,CAAC;AAE7C,MAAM,OAAO,cAAc;IAIzB,YAAY,SAA+B,EAAE;;QAC3C,MAAM,YAAY,GAAG,KAAK,CAAC,EAAE,EAAE,iBAAiB,EAAE,EAAE,MAAM,CAAC,CAAC;QAC5D,MAAM,QAAQ,GAAG,QAAQ,CAAC,OAAO,EAAE,CAAC,KAAK,CACvC,MAAA,YAAY,CAAC,QAAQ,mCAAI,QAAQ,CAAC,KAAK,EAAE,CAC1C,CAAC;QACF,IAAI,CAAC,YAAY,GAAG,IAAI,yBAAyB,CAC/C,QAAQ,EACR,YAAY,CAAC,uBAAuB,EACpC,iBAAiB,CAAC,YAAY,CAAC,eAAe,CAAC,CAChD,CAAC;QACF,IAAI,CAAC,aAAa,GAAG,IAAI,cAAc,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;IAChE,CAAC;IAED;;OAEG;IACI,SAAS,CACd,IAAY,EACZ,OAAgB,EAChB,OAA+B;QAE/B,IAAI,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE;YAC/B,IAAI,CAAC,IAAI,CAAC,mDAAmD,CAAC,CAAC;YAC/D,OAAO,WAAW,CAAC;SACpB;QAED,IAAI,CAAC,IAAI,EAAE;YACT,IAAI,CAAC,IAAI,CAAC,sDAAsD,CAAC,CAAC;SACnE;QACD,MAAM,UAAU,GAAG,IAAI,IAAI,mBAAmB,CAAC;QAC/C,MAAM,GAAG,GAAG,GAAG,UAAU,IAAI,OAAO,IAAI,EAAE,IAAI,CAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,SAAS,KAAI,EAAE,EAAE,CAAC;QACzE,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;YACvC,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,GAAG,CAC3B,GAAG,EACH,IAAI,MAAM,CACR,EAAE,IAAI,EAAE,UAAU,EAAE,OAAO,EAAE,SAAS,EAAE,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,SAAS,EAAE,EAC5D,IAAI,CAAC,YAAY,CAClB,CACF,CAAC;SACH;QACD,oEAAoE;QACpE,OAAO,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAE,CAAC;IAC7C,CAAC;IAED;;;OAGG;IACI,qBAAqB,CAAC,SAA6B;QACxD,IAAI,IAAI,CAAC,YAAY,CAAC,6BAA6B,CAAC,MAAM,KAAK,CAAC,EAAE;YAChE,yEAAyE;YACzE,4BAA4B;YAC5B,IAAI,CAAC,YAAY,CAAC,eAAe;iBAC9B,QAAQ,EAAE;iBACV,KAAK,CAAC,GAAG,CAAC,EAAE,CACX,IAAI,CAAC,KAAK,CACR,6DAA6D,EAC7D,GAAG,CACJ,CACF,CAAC;SACL;QACD,IAAI,CAAC,YAAY,CAAC,6BAA6B,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAChE,IAAI,CAAC,YAAY,CAAC,eAAe,GAAG,IAAI,uBAAuB,CAC7D,IAAI,CAAC,YAAY,CAAC,6BAA6B,EAC/C,IAAI,CAAC,YAAY,CAAC,uBAAuB,CAC1C,CAAC;IACJ,CAAC;IAED;;;;OAIG;IACI,UAAU;QACf,8BAA8B;QAC9B,IAAI,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE;YAC/B,IAAI,CAAC,IAAI,CAAC,8DAA8D,CAAC,CAAC;YAC1E,OAAO,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC;SACnC;QACD,OAAO,IAAI,CAAC,YAAY,CAAC,eAAe,CAAC,UAAU,EAAE,CAAC;IACxD,CAAC;IAED;;;;;OAKG;IACI,QAAQ;QACb,IAAI,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE;YAC/B,IAAI,CAAC,IAAI,CAAC,qDAAqD,CAAC,CAAC;YACjE,OAAO,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC;SACnC;QACD,OAAO,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC;IACnC,CAAC;IAEO,SAAS;QACf,OAAO,IAAI,CAAC,YAAY,CAAC,eAAe,CAAC,QAAQ,EAAE,CAAC;IACtD,CAAC;CACF", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { diag } from '@opentelemetry/api';\nimport type * as logsAPI from '@opentelemetry/api-logs';\nimport { NOOP_LOGGER } from '@opentelemetry/api-logs';\nimport { Resource } from '@opentelemetry/resources';\nimport { BindOnceFuture, merge } from '@opentelemetry/core';\n\nimport type { LoggerProviderConfig } from './types';\nimport type { LogRecordProcessor } from './LogRecordProcessor';\nimport { Logger } from './Logger';\nimport { loadDefaultConfig, reconfigureLimits } from './config';\nimport { MultiLogRecordProcessor } from './MultiLogRecordProcessor';\nimport { LoggerProviderSharedState } from './internal/LoggerProviderSharedState';\n\nexport const DEFAULT_LOGGER_NAME = 'unknown';\n\nexport class LoggerProvider implements logsAPI.LoggerProvider {\n  private _shutdownOnce: BindOnceFuture<void>;\n  private readonly _sharedState: LoggerProviderSharedState;\n\n  constructor(config: LoggerProviderConfig = {}) {\n    const mergedConfig = merge({}, loadDefaultConfig(), config);\n    const resource = Resource.default().merge(\n      mergedConfig.resource ?? Resource.empty()\n    );\n    this._sharedState = new LoggerProviderSharedState(\n      resource,\n      mergedConfig.forceFlushTimeoutMillis,\n      reconfigureLimits(mergedConfig.logRecordLimits)\n    );\n    this._shutdownOnce = new BindOnceFuture(this._shutdown, this);\n  }\n\n  /**\n   * Get a logger with the configuration of the LoggerProvider.\n   */\n  public getLogger(\n    name: string,\n    version?: string,\n    options?: logsAPI.LoggerOptions\n  ): logsAPI.Logger {\n    if (this._shutdownOnce.isCalled) {\n      diag.warn('A shutdown LoggerProvider cannot provide a Logger');\n      return NOOP_LOGGER;\n    }\n\n    if (!name) {\n      diag.warn('Logger requested without instrumentation scope name.');\n    }\n    const loggerName = name || DEFAULT_LOGGER_NAME;\n    const key = `${loggerName}@${version || ''}:${options?.schemaUrl || ''}`;\n    if (!this._sharedState.loggers.has(key)) {\n      this._sharedState.loggers.set(\n        key,\n        new Logger(\n          { name: loggerName, version, schemaUrl: options?.schemaUrl },\n          this._sharedState\n        )\n      );\n    }\n    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n    return this._sharedState.loggers.get(key)!;\n  }\n\n  /**\n   * Adds a new {@link LogRecordProcessor} to this logger.\n   * @param processor the new LogRecordProcessor to be added.\n   */\n  public addLogRecordProcessor(processor: LogRecordProcessor) {\n    if (this._sharedState.registeredLogRecordProcessors.length === 0) {\n      // since we might have enabled by default a batchProcessor, we disable it\n      // before adding the new one\n      this._sharedState.activeProcessor\n        .shutdown()\n        .catch(err =>\n          diag.error(\n            'Error while trying to shutdown current log record processor',\n            err\n          )\n        );\n    }\n    this._sharedState.registeredLogRecordProcessors.push(processor);\n    this._sharedState.activeProcessor = new MultiLogRecordProcessor(\n      this._sharedState.registeredLogRecordProcessors,\n      this._sharedState.forceFlushTimeoutMillis\n    );\n  }\n\n  /**\n   * Notifies all registered LogRecordProcessor to flush any buffered data.\n   *\n   * Returns a promise which is resolved when all flushes are complete.\n   */\n  public forceFlush(): Promise<void> {\n    // do not flush after shutdown\n    if (this._shutdownOnce.isCalled) {\n      diag.warn('invalid attempt to force flush after LoggerProvider shutdown');\n      return this._shutdownOnce.promise;\n    }\n    return this._sharedState.activeProcessor.forceFlush();\n  }\n\n  /**\n   * Flush all buffered data and shut down the LoggerProvider and all registered\n   * LogRecordProcessor.\n   *\n   * Returns a promise which is resolved when all flushes are complete.\n   */\n  public shutdown(): Promise<void> {\n    if (this._shutdownOnce.isCalled) {\n      diag.warn('shutdown may only be called once per LoggerProvider');\n      return this._shutdownOnce.promise;\n    }\n    return this._shutdownOnce.call();\n  }\n\n  private _shutdown(): Promise<void> {\n    return this._sharedState.activeProcessor.shutdown();\n  }\n}\n"]}