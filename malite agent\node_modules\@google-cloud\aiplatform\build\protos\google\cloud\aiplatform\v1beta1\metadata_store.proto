// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.aiplatform.v1beta1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/aiplatform/v1beta1/encryption_spec.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.AIPlatform.V1Beta1";
option go_package = "cloud.google.com/go/aiplatform/apiv1beta1/aiplatformpb;aiplatformpb";
option java_multiple_files = true;
option java_outer_classname = "MetadataProto";
option java_package = "com.google.cloud.aiplatform.v1beta1";
option php_namespace = "Google\\Cloud\\AIPlatform\\V1beta1";
option ruby_package = "Google::Cloud::AIPlatform::V1beta1";

// Instance of a metadata store. Contains a set of metadata that can be
// queried.
message MetadataStore {
  option (google.api.resource) = {
    type: "aiplatform.googleapis.com/MetadataStore"
    pattern: "projects/{project}/locations/{location}/metadataStores/{metadata_store}"
  };

  // Represents state information for a MetadataStore.
  message MetadataStoreState {
    // The disk utilization of the MetadataStore in bytes.
    int64 disk_utilization_bytes = 1;
  }

  // Represents Dataplex integration settings.
  message DataplexConfig {
    // Optional. Whether or not Data Lineage synchronization is enabled for
    // Vertex Pipelines.
    bool enabled_pipelines_lineage = 1 [(google.api.field_behavior) = OPTIONAL];
  }

  // Output only. The resource name of the MetadataStore instance.
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Timestamp when this MetadataStore was created.
  google.protobuf.Timestamp create_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Timestamp when this MetadataStore was last updated.
  google.protobuf.Timestamp update_time = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Customer-managed encryption key spec for a Metadata Store. If set, this
  // Metadata Store and all sub-resources of this Metadata Store are secured
  // using this key.
  EncryptionSpec encryption_spec = 5;

  // Description of the MetadataStore.
  string description = 6;

  // Output only. State information of the MetadataStore.
  MetadataStoreState state = 7 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Dataplex integration settings.
  DataplexConfig dataplex_config = 8 [(google.api.field_behavior) = OPTIONAL];
}
