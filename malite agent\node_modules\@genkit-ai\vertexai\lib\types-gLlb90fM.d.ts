import { z, EmbedderReference as EmbedderReference$1 } from 'genkit';
import { C as CommonPluginOptions } from './types-Bc0LKM8D.js';
import * as aiplatform from '@google-cloud/aiplatform';
import { EmbedderReference } from 'genkit/embedder';
import { Document } from 'genkit/retriever';
import { GoogleAuth } from 'google-auth-library';

/**
 * Copyright 2024 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

interface VertexVectorSearchOptions<EmbedderCustomOptions extends z.ZodTypeAny> {
    pluginOptions: PluginOptions;
    authClient: GoogleAuth;
    defaultEmbedder?: EmbedderReference<EmbedderCustomOptions>;
}
type IIndexDatapoint = aiplatform.protos.google.cloud.aiplatform.v1.IIndexDatapoint;
declare class Datapoint extends aiplatform.protos.google.cloud.aiplatform.v1
    .IndexDatapoint {
    constructor(properties: IIndexDatapoint);
}
type IFindNeighborsRequest = aiplatform.protos.google.cloud.aiplatform.v1.IFindNeighborsRequest;
type IFindNeighborsResponse = aiplatform.protos.google.cloud.aiplatform.v1.IFindNeighborsResponse;
type ISparseEmbedding = aiplatform.protos.google.cloud.aiplatform.v1.IndexDatapoint.ISparseEmbedding;
type IRestriction = aiplatform.protos.google.cloud.aiplatform.v1.IndexDatapoint.IRestriction;
type INumericRestriction = aiplatform.protos.google.cloud.aiplatform.v1.IndexDatapoint.INumericRestriction;
declare const SparseEmbeddingSchema: z.ZodObject<{
    values: z.ZodOptional<z.ZodArray<z.ZodNumber, "many">>;
    dimensions: z.ZodOptional<z.ZodArray<z.ZodUnion<[z.ZodNumber, z.ZodString]>, "many">>;
}, "strip", z.ZodTypeAny, {
    values?: number[] | undefined;
    dimensions?: (string | number)[] | undefined;
}, {
    values?: number[] | undefined;
    dimensions?: (string | number)[] | undefined;
}>;
type SparseEmbedding = z.infer<typeof SparseEmbeddingSchema>;
declare const RestrictionSchema: z.ZodObject<{
    namespace: z.ZodString;
    allowList: z.ZodArray<z.ZodString, "many">;
    denyList: z.ZodArray<z.ZodString, "many">;
}, "strip", z.ZodTypeAny, {
    namespace: string;
    allowList: string[];
    denyList: string[];
}, {
    namespace: string;
    allowList: string[];
    denyList: string[];
}>;
type Restriction = z.infer<typeof RestrictionSchema>;
declare const NumericRestrictionOperatorSchema: z.ZodEnum<["OPERATOR_UNSPECIFIED", "LESS", "LESS_EQUAL", "EQUAL", "GREATER_EQUAL", "GREATER", "NOT_EQUAL"]>;
type NumericRestrictionOperator = z.infer<typeof NumericRestrictionOperatorSchema>;
declare const NumericRestrictionSchema: z.ZodObject<{
    valueInt: z.ZodOptional<z.ZodUnion<[z.ZodNumber, z.ZodString]>>;
    valueFloat: z.ZodOptional<z.ZodNumber>;
    valueDouble: z.ZodOptional<z.ZodNumber>;
    namespace: z.ZodString;
    op: z.ZodOptional<z.ZodUnion<[z.ZodEnum<["OPERATOR_UNSPECIFIED", "LESS", "LESS_EQUAL", "EQUAL", "GREATER_EQUAL", "GREATER", "NOT_EQUAL"]>, z.ZodNull]>>;
}, "strip", z.ZodTypeAny, {
    namespace: string;
    valueInt?: string | number | undefined;
    valueFloat?: number | undefined;
    valueDouble?: number | undefined;
    op?: "OPERATOR_UNSPECIFIED" | "LESS" | "LESS_EQUAL" | "EQUAL" | "GREATER_EQUAL" | "GREATER" | "NOT_EQUAL" | null | undefined;
}, {
    namespace: string;
    valueInt?: string | number | undefined;
    valueFloat?: number | undefined;
    valueDouble?: number | undefined;
    op?: "OPERATOR_UNSPECIFIED" | "LESS" | "LESS_EQUAL" | "EQUAL" | "GREATER_EQUAL" | "GREATER" | "NOT_EQUAL" | null | undefined;
}>;
type NumericRestriction = z.infer<typeof NumericRestrictionSchema>;
declare const CrowdingTagSchema: z.ZodObject<{
    crowdingAttribute: z.ZodOptional<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    crowdingAttribute?: string | undefined;
}, {
    crowdingAttribute?: string | undefined;
}>;
type CrowdingTag = z.infer<typeof CrowdingTagSchema>;
declare const NeighborSchema: z.ZodObject<{
    datapoint: z.ZodOptional<z.ZodObject<{
        datapointId: z.ZodOptional<z.ZodString>;
        featureVector: z.ZodOptional<z.ZodArray<z.ZodNumber, "many">>;
        sparseEmbedding: z.ZodOptional<z.ZodObject<{
            values: z.ZodOptional<z.ZodArray<z.ZodNumber, "many">>;
            dimensions: z.ZodOptional<z.ZodArray<z.ZodUnion<[z.ZodNumber, z.ZodString]>, "many">>;
        }, "strip", z.ZodTypeAny, {
            values?: number[] | undefined;
            dimensions?: (string | number)[] | undefined;
        }, {
            values?: number[] | undefined;
            dimensions?: (string | number)[] | undefined;
        }>>;
        restricts: z.ZodOptional<z.ZodArray<z.ZodObject<{
            namespace: z.ZodString;
            allowList: z.ZodArray<z.ZodString, "many">;
            denyList: z.ZodArray<z.ZodString, "many">;
        }, "strip", z.ZodTypeAny, {
            namespace: string;
            allowList: string[];
            denyList: string[];
        }, {
            namespace: string;
            allowList: string[];
            denyList: string[];
        }>, "many">>;
        numericRestricts: z.ZodOptional<z.ZodArray<z.ZodObject<{
            valueInt: z.ZodOptional<z.ZodUnion<[z.ZodNumber, z.ZodString]>>;
            valueFloat: z.ZodOptional<z.ZodNumber>;
            valueDouble: z.ZodOptional<z.ZodNumber>;
            namespace: z.ZodString;
            op: z.ZodOptional<z.ZodUnion<[z.ZodEnum<["OPERATOR_UNSPECIFIED", "LESS", "LESS_EQUAL", "EQUAL", "GREATER_EQUAL", "GREATER", "NOT_EQUAL"]>, z.ZodNull]>>;
        }, "strip", z.ZodTypeAny, {
            namespace: string;
            valueInt?: string | number | undefined;
            valueFloat?: number | undefined;
            valueDouble?: number | undefined;
            op?: "OPERATOR_UNSPECIFIED" | "LESS" | "LESS_EQUAL" | "EQUAL" | "GREATER_EQUAL" | "GREATER" | "NOT_EQUAL" | null | undefined;
        }, {
            namespace: string;
            valueInt?: string | number | undefined;
            valueFloat?: number | undefined;
            valueDouble?: number | undefined;
            op?: "OPERATOR_UNSPECIFIED" | "LESS" | "LESS_EQUAL" | "EQUAL" | "GREATER_EQUAL" | "GREATER" | "NOT_EQUAL" | null | undefined;
        }>, "many">>;
        crowdingTag: z.ZodOptional<z.ZodObject<{
            crowdingAttribute: z.ZodOptional<z.ZodString>;
        }, "strip", z.ZodTypeAny, {
            crowdingAttribute?: string | undefined;
        }, {
            crowdingAttribute?: string | undefined;
        }>>;
    }, "strip", z.ZodTypeAny, {
        datapointId?: string | undefined;
        featureVector?: number[] | undefined;
        sparseEmbedding?: {
            values?: number[] | undefined;
            dimensions?: (string | number)[] | undefined;
        } | undefined;
        restricts?: {
            namespace: string;
            allowList: string[];
            denyList: string[];
        }[] | undefined;
        numericRestricts?: {
            namespace: string;
            valueInt?: string | number | undefined;
            valueFloat?: number | undefined;
            valueDouble?: number | undefined;
            op?: "OPERATOR_UNSPECIFIED" | "LESS" | "LESS_EQUAL" | "EQUAL" | "GREATER_EQUAL" | "GREATER" | "NOT_EQUAL" | null | undefined;
        }[] | undefined;
        crowdingTag?: {
            crowdingAttribute?: string | undefined;
        } | undefined;
    }, {
        datapointId?: string | undefined;
        featureVector?: number[] | undefined;
        sparseEmbedding?: {
            values?: number[] | undefined;
            dimensions?: (string | number)[] | undefined;
        } | undefined;
        restricts?: {
            namespace: string;
            allowList: string[];
            denyList: string[];
        }[] | undefined;
        numericRestricts?: {
            namespace: string;
            valueInt?: string | number | undefined;
            valueFloat?: number | undefined;
            valueDouble?: number | undefined;
            op?: "OPERATOR_UNSPECIFIED" | "LESS" | "LESS_EQUAL" | "EQUAL" | "GREATER_EQUAL" | "GREATER" | "NOT_EQUAL" | null | undefined;
        }[] | undefined;
        crowdingTag?: {
            crowdingAttribute?: string | undefined;
        } | undefined;
    }>>;
    distance: z.ZodOptional<z.ZodNumber>;
    sparseDistance: z.ZodOptional<z.ZodNumber>;
}, "strip", z.ZodTypeAny, {
    datapoint?: {
        datapointId?: string | undefined;
        featureVector?: number[] | undefined;
        sparseEmbedding?: {
            values?: number[] | undefined;
            dimensions?: (string | number)[] | undefined;
        } | undefined;
        restricts?: {
            namespace: string;
            allowList: string[];
            denyList: string[];
        }[] | undefined;
        numericRestricts?: {
            namespace: string;
            valueInt?: string | number | undefined;
            valueFloat?: number | undefined;
            valueDouble?: number | undefined;
            op?: "OPERATOR_UNSPECIFIED" | "LESS" | "LESS_EQUAL" | "EQUAL" | "GREATER_EQUAL" | "GREATER" | "NOT_EQUAL" | null | undefined;
        }[] | undefined;
        crowdingTag?: {
            crowdingAttribute?: string | undefined;
        } | undefined;
    } | undefined;
    distance?: number | undefined;
    sparseDistance?: number | undefined;
}, {
    datapoint?: {
        datapointId?: string | undefined;
        featureVector?: number[] | undefined;
        sparseEmbedding?: {
            values?: number[] | undefined;
            dimensions?: (string | number)[] | undefined;
        } | undefined;
        restricts?: {
            namespace: string;
            allowList: string[];
            denyList: string[];
        }[] | undefined;
        numericRestricts?: {
            namespace: string;
            valueInt?: string | number | undefined;
            valueFloat?: number | undefined;
            valueDouble?: number | undefined;
            op?: "OPERATOR_UNSPECIFIED" | "LESS" | "LESS_EQUAL" | "EQUAL" | "GREATER_EQUAL" | "GREATER" | "NOT_EQUAL" | null | undefined;
        }[] | undefined;
        crowdingTag?: {
            crowdingAttribute?: string | undefined;
        } | undefined;
    } | undefined;
    distance?: number | undefined;
    sparseDistance?: number | undefined;
}>;
type Neighbor = z.infer<typeof NeighborSchema>;
declare const FindNeighborsResponseSchema: z.ZodObject<{
    nearestNeighbors: z.ZodOptional<z.ZodArray<z.ZodObject<{
        id: z.ZodOptional<z.ZodString>;
        neighbors: z.ZodOptional<z.ZodArray<z.ZodObject<{
            datapoint: z.ZodOptional<z.ZodObject<{
                datapointId: z.ZodOptional<z.ZodString>;
                featureVector: z.ZodOptional<z.ZodArray<z.ZodNumber, "many">>;
                sparseEmbedding: z.ZodOptional<z.ZodObject<{
                    values: z.ZodOptional<z.ZodArray<z.ZodNumber, "many">>;
                    dimensions: z.ZodOptional<z.ZodArray<z.ZodUnion<[z.ZodNumber, z.ZodString]>, "many">>;
                }, "strip", z.ZodTypeAny, {
                    values?: number[] | undefined;
                    dimensions?: (string | number)[] | undefined;
                }, {
                    values?: number[] | undefined;
                    dimensions?: (string | number)[] | undefined;
                }>>;
                restricts: z.ZodOptional<z.ZodArray<z.ZodObject<{
                    namespace: z.ZodString;
                    allowList: z.ZodArray<z.ZodString, "many">;
                    denyList: z.ZodArray<z.ZodString, "many">;
                }, "strip", z.ZodTypeAny, {
                    namespace: string;
                    allowList: string[];
                    denyList: string[];
                }, {
                    namespace: string;
                    allowList: string[];
                    denyList: string[];
                }>, "many">>;
                numericRestricts: z.ZodOptional<z.ZodArray<z.ZodObject<{
                    valueInt: z.ZodOptional<z.ZodUnion<[z.ZodNumber, z.ZodString]>>;
                    valueFloat: z.ZodOptional<z.ZodNumber>;
                    valueDouble: z.ZodOptional<z.ZodNumber>;
                    namespace: z.ZodString;
                    op: z.ZodOptional<z.ZodUnion<[z.ZodEnum<["OPERATOR_UNSPECIFIED", "LESS", "LESS_EQUAL", "EQUAL", "GREATER_EQUAL", "GREATER", "NOT_EQUAL"]>, z.ZodNull]>>;
                }, "strip", z.ZodTypeAny, {
                    namespace: string;
                    valueInt?: string | number | undefined;
                    valueFloat?: number | undefined;
                    valueDouble?: number | undefined;
                    op?: "OPERATOR_UNSPECIFIED" | "LESS" | "LESS_EQUAL" | "EQUAL" | "GREATER_EQUAL" | "GREATER" | "NOT_EQUAL" | null | undefined;
                }, {
                    namespace: string;
                    valueInt?: string | number | undefined;
                    valueFloat?: number | undefined;
                    valueDouble?: number | undefined;
                    op?: "OPERATOR_UNSPECIFIED" | "LESS" | "LESS_EQUAL" | "EQUAL" | "GREATER_EQUAL" | "GREATER" | "NOT_EQUAL" | null | undefined;
                }>, "many">>;
                crowdingTag: z.ZodOptional<z.ZodObject<{
                    crowdingAttribute: z.ZodOptional<z.ZodString>;
                }, "strip", z.ZodTypeAny, {
                    crowdingAttribute?: string | undefined;
                }, {
                    crowdingAttribute?: string | undefined;
                }>>;
            }, "strip", z.ZodTypeAny, {
                datapointId?: string | undefined;
                featureVector?: number[] | undefined;
                sparseEmbedding?: {
                    values?: number[] | undefined;
                    dimensions?: (string | number)[] | undefined;
                } | undefined;
                restricts?: {
                    namespace: string;
                    allowList: string[];
                    denyList: string[];
                }[] | undefined;
                numericRestricts?: {
                    namespace: string;
                    valueInt?: string | number | undefined;
                    valueFloat?: number | undefined;
                    valueDouble?: number | undefined;
                    op?: "OPERATOR_UNSPECIFIED" | "LESS" | "LESS_EQUAL" | "EQUAL" | "GREATER_EQUAL" | "GREATER" | "NOT_EQUAL" | null | undefined;
                }[] | undefined;
                crowdingTag?: {
                    crowdingAttribute?: string | undefined;
                } | undefined;
            }, {
                datapointId?: string | undefined;
                featureVector?: number[] | undefined;
                sparseEmbedding?: {
                    values?: number[] | undefined;
                    dimensions?: (string | number)[] | undefined;
                } | undefined;
                restricts?: {
                    namespace: string;
                    allowList: string[];
                    denyList: string[];
                }[] | undefined;
                numericRestricts?: {
                    namespace: string;
                    valueInt?: string | number | undefined;
                    valueFloat?: number | undefined;
                    valueDouble?: number | undefined;
                    op?: "OPERATOR_UNSPECIFIED" | "LESS" | "LESS_EQUAL" | "EQUAL" | "GREATER_EQUAL" | "GREATER" | "NOT_EQUAL" | null | undefined;
                }[] | undefined;
                crowdingTag?: {
                    crowdingAttribute?: string | undefined;
                } | undefined;
            }>>;
            distance: z.ZodOptional<z.ZodNumber>;
            sparseDistance: z.ZodOptional<z.ZodNumber>;
        }, "strip", z.ZodTypeAny, {
            datapoint?: {
                datapointId?: string | undefined;
                featureVector?: number[] | undefined;
                sparseEmbedding?: {
                    values?: number[] | undefined;
                    dimensions?: (string | number)[] | undefined;
                } | undefined;
                restricts?: {
                    namespace: string;
                    allowList: string[];
                    denyList: string[];
                }[] | undefined;
                numericRestricts?: {
                    namespace: string;
                    valueInt?: string | number | undefined;
                    valueFloat?: number | undefined;
                    valueDouble?: number | undefined;
                    op?: "OPERATOR_UNSPECIFIED" | "LESS" | "LESS_EQUAL" | "EQUAL" | "GREATER_EQUAL" | "GREATER" | "NOT_EQUAL" | null | undefined;
                }[] | undefined;
                crowdingTag?: {
                    crowdingAttribute?: string | undefined;
                } | undefined;
            } | undefined;
            distance?: number | undefined;
            sparseDistance?: number | undefined;
        }, {
            datapoint?: {
                datapointId?: string | undefined;
                featureVector?: number[] | undefined;
                sparseEmbedding?: {
                    values?: number[] | undefined;
                    dimensions?: (string | number)[] | undefined;
                } | undefined;
                restricts?: {
                    namespace: string;
                    allowList: string[];
                    denyList: string[];
                }[] | undefined;
                numericRestricts?: {
                    namespace: string;
                    valueInt?: string | number | undefined;
                    valueFloat?: number | undefined;
                    valueDouble?: number | undefined;
                    op?: "OPERATOR_UNSPECIFIED" | "LESS" | "LESS_EQUAL" | "EQUAL" | "GREATER_EQUAL" | "GREATER" | "NOT_EQUAL" | null | undefined;
                }[] | undefined;
                crowdingTag?: {
                    crowdingAttribute?: string | undefined;
                } | undefined;
            } | undefined;
            distance?: number | undefined;
            sparseDistance?: number | undefined;
        }>, "many">>;
    }, "strip", z.ZodTypeAny, {
        id?: string | undefined;
        neighbors?: {
            datapoint?: {
                datapointId?: string | undefined;
                featureVector?: number[] | undefined;
                sparseEmbedding?: {
                    values?: number[] | undefined;
                    dimensions?: (string | number)[] | undefined;
                } | undefined;
                restricts?: {
                    namespace: string;
                    allowList: string[];
                    denyList: string[];
                }[] | undefined;
                numericRestricts?: {
                    namespace: string;
                    valueInt?: string | number | undefined;
                    valueFloat?: number | undefined;
                    valueDouble?: number | undefined;
                    op?: "OPERATOR_UNSPECIFIED" | "LESS" | "LESS_EQUAL" | "EQUAL" | "GREATER_EQUAL" | "GREATER" | "NOT_EQUAL" | null | undefined;
                }[] | undefined;
                crowdingTag?: {
                    crowdingAttribute?: string | undefined;
                } | undefined;
            } | undefined;
            distance?: number | undefined;
            sparseDistance?: number | undefined;
        }[] | undefined;
    }, {
        id?: string | undefined;
        neighbors?: {
            datapoint?: {
                datapointId?: string | undefined;
                featureVector?: number[] | undefined;
                sparseEmbedding?: {
                    values?: number[] | undefined;
                    dimensions?: (string | number)[] | undefined;
                } | undefined;
                restricts?: {
                    namespace: string;
                    allowList: string[];
                    denyList: string[];
                }[] | undefined;
                numericRestricts?: {
                    namespace: string;
                    valueInt?: string | number | undefined;
                    valueFloat?: number | undefined;
                    valueDouble?: number | undefined;
                    op?: "OPERATOR_UNSPECIFIED" | "LESS" | "LESS_EQUAL" | "EQUAL" | "GREATER_EQUAL" | "GREATER" | "NOT_EQUAL" | null | undefined;
                }[] | undefined;
                crowdingTag?: {
                    crowdingAttribute?: string | undefined;
                } | undefined;
            } | undefined;
            distance?: number | undefined;
            sparseDistance?: number | undefined;
        }[] | undefined;
    }>, "many">>;
}, "strip", z.ZodTypeAny, {
    nearestNeighbors?: {
        id?: string | undefined;
        neighbors?: {
            datapoint?: {
                datapointId?: string | undefined;
                featureVector?: number[] | undefined;
                sparseEmbedding?: {
                    values?: number[] | undefined;
                    dimensions?: (string | number)[] | undefined;
                } | undefined;
                restricts?: {
                    namespace: string;
                    allowList: string[];
                    denyList: string[];
                }[] | undefined;
                numericRestricts?: {
                    namespace: string;
                    valueInt?: string | number | undefined;
                    valueFloat?: number | undefined;
                    valueDouble?: number | undefined;
                    op?: "OPERATOR_UNSPECIFIED" | "LESS" | "LESS_EQUAL" | "EQUAL" | "GREATER_EQUAL" | "GREATER" | "NOT_EQUAL" | null | undefined;
                }[] | undefined;
                crowdingTag?: {
                    crowdingAttribute?: string | undefined;
                } | undefined;
            } | undefined;
            distance?: number | undefined;
            sparseDistance?: number | undefined;
        }[] | undefined;
    }[] | undefined;
}, {
    nearestNeighbors?: {
        id?: string | undefined;
        neighbors?: {
            datapoint?: {
                datapointId?: string | undefined;
                featureVector?: number[] | undefined;
                sparseEmbedding?: {
                    values?: number[] | undefined;
                    dimensions?: (string | number)[] | undefined;
                } | undefined;
                restricts?: {
                    namespace: string;
                    allowList: string[];
                    denyList: string[];
                }[] | undefined;
                numericRestricts?: {
                    namespace: string;
                    valueInt?: string | number | undefined;
                    valueFloat?: number | undefined;
                    valueDouble?: number | undefined;
                    op?: "OPERATOR_UNSPECIFIED" | "LESS" | "LESS_EQUAL" | "EQUAL" | "GREATER_EQUAL" | "GREATER" | "NOT_EQUAL" | null | undefined;
                }[] | undefined;
                crowdingTag?: {
                    crowdingAttribute?: string | undefined;
                } | undefined;
            } | undefined;
            distance?: number | undefined;
            sparseDistance?: number | undefined;
        }[] | undefined;
    }[] | undefined;
}>;
type FindNeighborsResponse = z.infer<typeof FindNeighborsResponseSchema>;
declare const VertexAIVectorRetrieverOptionsSchema: z.ZodOptional<z.ZodObject<z.objectUtil.extendShape<{
    k: z.ZodOptional<z.ZodNumber>;
}, {}>, "strip", z.ZodTypeAny, {
    k?: number | undefined;
}, {
    k?: number | undefined;
}>>;
type VertexAIVectorRetrieverOptions = z.infer<typeof VertexAIVectorRetrieverOptionsSchema>;
declare const VertexAIVectorIndexerOptionsSchema: z.ZodAny;
type VertexAIVectorIndexerOptions = z.infer<typeof VertexAIVectorIndexerOptionsSchema>;
/**
 * A document retriever function that takes an array of Neighbors from Vertex AI Vector Search query result, and resolves to a list of documents.
 * Also takes an options object that can be used to configure the retriever.
 */
type DocumentRetriever<Options extends {
    k?: number;
} = {
    k?: number;
}> = (docIds: Neighbor[], options?: Options) => Promise<Document[]>;
/**
 * Indexer function that takes an array of documents, stores them in a database of the user's choice, and resolves to a list of document ids.
 * Also takes an options object that can be used to configure the indexer. Only Streaming Update Indexers are supported.
 */
type DocumentIndexer<Options extends {} = {}> = (docs: Document[], options?: Options) => Promise<string[]>;
interface VectorSearchOptions<EmbedderCustomOptions extends z.ZodTypeAny, IndexerOptions extends {}, RetrieverOptions extends {
    k?: number;
}> {
    deployedIndexId: string;
    indexEndpointId: string;
    publicDomainName: string;
    indexId: string;
    documentRetriever: DocumentRetriever<RetrieverOptions>;
    documentIndexer: DocumentIndexer<IndexerOptions>;
    embedder?: EmbedderReference<EmbedderCustomOptions>;
    embedderOptions?: z.infer<EmbedderCustomOptions>;
}

/**
 * Copyright 2024 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

/** Options specific to vector search configuration */
interface VectorSearchOptionsConfig {
    /** Configure Vertex AI vector search index options */
    vectorSearchOptions?: VectorSearchOptions<z.ZodTypeAny, any, any>[];
    embedder?: EmbedderReference$1;
}
interface PluginOptions extends CommonPluginOptions, VectorSearchOptionsConfig {
}

export { CrowdingTagSchema as C, type DocumentIndexer as D, type FindNeighborsResponse as F, type IRestriction as I, type Neighbor as N, type PluginOptions as P, RestrictionSchema as R, SparseEmbeddingSchema as S, VertexAIVectorIndexerOptionsSchema as V, VertexAIVectorRetrieverOptionsSchema as a, type DocumentRetriever as b, type VectorSearchOptions as c, type VertexAIVectorIndexerOptions as d, type VertexAIVectorRetrieverOptions as e, type VertexVectorSearchOptions as f, type INumericRestriction as g, type IIndexDatapoint as h, Datapoint as i, type IFindNeighborsRequest as j, type IFindNeighborsResponse as k, type ISparseEmbedding as l, type SparseEmbedding as m, type Restriction as n, NumericRestrictionOperatorSchema as o, type NumericRestrictionOperator as p, NumericRestrictionSchema as q, type NumericRestriction as r, type CrowdingTag as s, NeighborSchema as t, FindNeighborsResponseSchema as u, type VectorSearchOptionsConfig as v };
