/**
 * @param obj The object to walk
 * @param key The key path to walk the object with
 * @param def A default value to return if the result is undefined
 *
 * @example
 * dlv(obj, "a.b.c.d")
 * @example
 * dlv(object, ["a", "b", "c", "d"])
 * @example
 * dlv(object, "foo.bar.baz", "Hello, default value!")
 */
export declare function dlv<T = any>(obj: any, key: string | string[], def?: T, p?: number, undef?: never): T | undefined;
//# sourceMappingURL=dlv.d.ts.map