// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.aiplatform.v1beta1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/aiplatform/v1beta1/encryption_spec.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.AIPlatform.V1Beta1";
option go_package = "cloud.google.com/go/aiplatform/apiv1beta1/aiplatformpb;aiplatformpb";
option java_multiple_files = true;
option java_outer_classname = "TensorboardProto";
option java_package = "com.google.cloud.aiplatform.v1beta1";
option php_namespace = "Google\\Cloud\\AIPlatform\\V1beta1";
option ruby_package = "Google::Cloud::AIPlatform::V1beta1";

// Tensorboard is a physical database that stores users' training metrics.
// A default Tensorboard is provided in each region of a Google Cloud project.
// If needed users can also create extra Tensorboards in their projects.
message Tensorboard {
  option (google.api.resource) = {
    type: "aiplatform.googleapis.com/Tensorboard"
    pattern: "projects/{project}/locations/{location}/tensorboards/{tensorboard}"
  };

  // Output only. Name of the Tensorboard.
  // Format:
  // `projects/{project}/locations/{location}/tensorboards/{tensorboard}`
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. User provided name of this Tensorboard.
  string display_name = 2 [(google.api.field_behavior) = REQUIRED];

  // Description of this Tensorboard.
  string description = 3;

  // Customer-managed encryption key spec for a Tensorboard. If set, this
  // Tensorboard and all sub-resources of this Tensorboard will be secured by
  // this key.
  EncryptionSpec encryption_spec = 11;

  // Output only. Consumer project Cloud Storage path prefix used to store blob
  // data, which can either be a bucket or directory. Does not end with a '/'.
  string blob_storage_path_prefix = 10
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The number of Runs stored in this Tensorboard.
  int32 run_count = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Timestamp when this Tensorboard was created.
  google.protobuf.Timestamp create_time = 6
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Timestamp when this Tensorboard was last updated.
  google.protobuf.Timestamp update_time = 7
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // The labels with user-defined metadata to organize your Tensorboards.
  //
  // Label keys and values can be no longer than 64 characters
  // (Unicode codepoints), can only contain lowercase letters, numeric
  // characters, underscores and dashes. International characters are allowed.
  // No more than 64 user labels can be associated with one Tensorboard
  // (System labels are excluded).
  //
  // See https://goo.gl/xmQnxf for more information and examples of labels.
  // System reserved label keys are prefixed with "aiplatform.googleapis.com/"
  // and are immutable.
  map<string, string> labels = 8;

  // Used to perform a consistent read-modify-write updates. If not set, a blind
  // "overwrite" update happens.
  string etag = 9;

  // Used to indicate if the TensorBoard instance is the default one.
  // Each project & region can have at most one default TensorBoard instance.
  // Creation of a default TensorBoard instance and updating an existing
  // TensorBoard instance to be default will mark all other TensorBoard
  // instances (if any) as non default.
  bool is_default = 12;

  // Output only. Reserved for future use.
  bool satisfies_pzs = 13 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Reserved for future use.
  bool satisfies_pzi = 14 [(google.api.field_behavior) = OUTPUT_ONLY];
}
