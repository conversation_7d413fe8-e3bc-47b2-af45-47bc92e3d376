{"version": 3, "file": "types.js", "sourceRoot": "", "sources": ["../../src/types.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { TracerProvider, MeterProvider, Span } from '@opentelemetry/api';\nimport { LoggerProvider } from '@opentelemetry/api-logs';\n\n/** Interface Instrumentation to apply patch. */\nexport interface Instrumentation<\n  ConfigType extends InstrumentationConfig = InstrumentationConfig,\n> {\n  /** Instrumentation Name  */\n  instrumentationName: string;\n\n  /** Instrumentation Version  */\n  instrumentationVersion: string;\n\n  /**\n   * Instrumentation Description - please describe all useful information\n   * as Instrumentation might patch different version of different modules,\n   * or support different browsers etc.\n   */\n  instrumentationDescription?: string;\n\n  /** Method to disable the instrumentation  */\n  disable(): void;\n\n  /** Method to enable the instrumentation  */\n  enable(): void;\n\n  /** Method to set tracer provider  */\n  setTracerProvider(tracerProvider: TracerProvider): void;\n\n  /** Method to set meter provider  */\n  setMeterProvider(meterProvider: MeterProvider): void;\n\n  /** Method to set logger provider  */\n  setLoggerProvider?(loggerProvider: LoggerProvider): void;\n\n  /** Method to set instrumentation config  */\n  setConfig(config: ConfigType): void;\n\n  /** Method to get instrumentation config  */\n  getConfig(): ConfigType;\n}\n\n/**\n * Base interface for configuration options common to all instrumentations.\n * This interface can be extended by individual instrumentations to include\n * additional configuration options specific to that instrumentation.\n * All configuration options must be optional.\n */\nexport interface InstrumentationConfig {\n  /**\n   * Whether to enable the plugin.\n   * @default true\n   */\n  enabled?: boolean;\n}\n\n/**\n * This interface defines the params that are be added to the wrapped function\n * using the \"shimmer.wrap\"\n */\nexport interface ShimWrapped extends Function {\n  __wrapped: boolean;\n  // eslint-disable-next-line @typescript-eslint/ban-types\n  __unwrap: Function;\n  // eslint-disable-next-line @typescript-eslint/ban-types\n  __original: Function;\n}\n\nexport interface InstrumentationModuleFile {\n  /** Name of file to be patched with relative path */\n  name: string;\n\n  moduleExports?: unknown;\n\n  /** Supported versions for the file.\n   *\n   * A module version is supported if one of the supportedVersions in the array satisfies the module version.\n   * The syntax of the version is checked with the `satisfies` function of \"The semantic versioner for npm\", see\n   * [`semver` package](https://www.npmjs.com/package/semver)\n   * If the version is not supported, we won't apply instrumentation patch.\n   * If omitted, all versions of the module will be patched.\n   *\n   * It is recommended to always specify a range that is bound to a major version, to avoid breaking changes.\n   * New major versions should be reviewed and tested before being added to the supportedVersions array.\n   *\n   * Example: ['>=1.2.3 <3']\n   */\n  supportedVersions: string[];\n\n  /** Method to patch the instrumentation  */\n  patch(moduleExports: unknown, moduleVersion?: string): unknown;\n\n  /** Method to unpatch the instrumentation  */\n  unpatch(moduleExports?: unknown, moduleVersion?: string): void;\n}\n\nexport interface InstrumentationModuleDefinition {\n  /** Module name or path  */\n  name: string;\n\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  moduleExports?: any;\n\n  /** Instrumented module version */\n  moduleVersion?: string;\n\n  /** Supported version of module.\n   *\n   * A module version is supported if one of the supportedVersions in the array satisfies the module version.\n   * The syntax of the version is checked with the `satisfies` function of \"The semantic versioner for npm\", see\n   * [`semver` package](https://www.npmjs.com/package/semver)\n   * If the version is not supported, we won't apply instrumentation patch (see `enable` method).\n   * If omitted, all versions of the module will be patched.\n   *\n   * It is recommended to always specify a range that is bound to a major version, to avoid breaking changes.\n   * New major versions should be reviewed and tested before being added to the supportedVersions array.\n   *\n   * Example: ['>=1.2.3 <3']\n   */\n  supportedVersions: string[];\n\n  /** Module internal files to be patched  */\n  files: InstrumentationModuleFile[];\n\n  /** If set to true, the includePrerelease check will be included when calling semver.satisfies */\n  includePrerelease?: boolean;\n\n  /** Method to patch the instrumentation  */\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  patch?: (moduleExports: any, moduleVersion?: string) => any;\n\n  /** Method to unpatch the instrumentation  */\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  unpatch?: (moduleExports: any, moduleVersion?: string) => void;\n}\n\n/**\n * SpanCustomizationHook is a common way for instrumentations to expose extension points\n * where users can add custom behavior to a span based on info object passed to the hook at different times of the span lifecycle.\n * This is an advanced feature, commonly used to add additional or non-spec-compliant attributes to the span,\n * capture payloads, modify the span in some way, or carry some other side effect.\n *\n * The hook is registered with the instrumentation specific config by implementing an handler function with this signature,\n * and if the hook is present, it will be called with the span and the event information\n * when the event is emitted.\n *\n * When and under what conditions the hook is called and what data is passed\n * in the info argument, is specific to each instrumentation and life-cycle event\n * and should be documented where it is used.\n *\n * Instrumentation may define multiple hooks, for different spans, or different span life-cycle events.\n */\nexport type SpanCustomizationHook<SpanCustomizationInfoType> = (\n  span: Span,\n  info: SpanCustomizationInfoType\n) => void;\n"]}