{"name": "@google/generative-ai", "version": "0.24.1", "description": "Google AI JavaScript SDK", "main": "dist/index.js", "module": "dist/index.mjs", "typings": "dist/generative-ai.d.ts", "exports": {".": {"types": "./dist/generative-ai.d.ts", "require": "./dist/index.js", "import": "./dist/index.mjs", "default": "./dist/index.js"}, "./server": {"types": "./dist/server/server.d.ts", "require": "./dist/server/index.js", "import": "./dist/server/index.mjs", "default": "./dist/server/index.js"}, "./package.json": "./package.json"}, "engines": {"node": ">=18.0.0"}, "files": ["server", "dist"], "server": ["dist", "server/package.json"], "scripts": {"build": "rollup -c && npm run api-report", "test": "npm run lint && npm run test:node:unit", "test:web:integration": "npm run build && npx web-test-runner", "test:node:unit": "TS_NODE_COMPILER_OPTIONS='{\"module\":\"commonjs\"}' mocha \"src/**/*.test.ts\"", "test:node:integration": "npm run build && TS_NODE_COMPILER_OPTIONS='{\"module\":\"commonjs\"}' mocha \"test-integration/node/**/*.test.ts\"", "lint": "eslint -c .eslintrc.js '**/*.ts' --ignore-path './.gitignore'", "api-report": "api-extractor run -c api-extractor.json --local --verbose && api-extractor run -c api-extractor.server.json --local --verbose", "docs": "npm run build && npx api-documenter markdown -i ./temp/main -o ./docs/reference/main && npx api-documenter markdown -i ./temp/server -o ./docs/reference/server", "format": "TS_NODE_COMPILER_OPTIONS='{\"module\":\"nodenext\"}' npx ts-node scripts/run-format.ts", "format:check": "TS_NODE_COMPILER_OPTIONS='{\"module\":\"nodenext\"}' npx ts-node scripts/check-format.ts"}, "devDependencies": {"@changesets/cli": "^2.27.1", "@esm-bundle/chai": "^4.3.4-fix.0", "@microsoft/api-documenter": "^7.23.12", "@microsoft/api-extractor": "^7.50.0", "@rollup/plugin-json": "^6.0.1", "@types/chai": "^4.3.9", "@types/chai-as-promised": "^7.1.8", "@types/mocha": "^10.0.3", "@types/node": "^20.8.10", "@types/sinon": "^10.0.20", "@types/sinon-chai": "^3.2.11", "@typescript-eslint/eslint-plugin": "^6.9.1", "@typescript-eslint/eslint-plugin-tslint": "^6.9.1", "@typescript-eslint/parser": "^6.9.1", "@web/dev-server-esbuild": "^1.0.1", "@web/test-runner": "^0.18.0", "chai": "^4.3.10", "chai-as-promised": "^7.1.1", "chai-deep-equal-ignore-undefined": "^1.1.1", "eslint": "^8.52.0", "eslint-plugin-import": "^2.29.0", "eslint-plugin-unused-imports": "^3.0.0", "glob": "^10.3.10", "mocha": "^10.2.0", "prettier": "^3.0.3", "rollup": "^4.2.0", "rollup-plugin-dts": "^6.1.0", "rollup-plugin-replace": "^2.2.0", "rollup-plugin-typescript2": "^0.36.0", "sinon": "^17.0.1", "sinon-chai": "^3.7.0", "ts-node": "^10.9.1", "tslint": "^6.1.3", "typescript": "5.2.2"}, "repository": {"type": "git", "url": "git+https://github.com/google/generative-ai-js.git"}, "author": "", "license": "Apache-2.0", "bugs": {"url": "https://github.com/google/generative-ai-js/issues"}, "homepage": "https://github.com/google/generative-ai-js#readme"}