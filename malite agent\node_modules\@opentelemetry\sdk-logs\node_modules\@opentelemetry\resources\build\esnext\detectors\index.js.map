{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../src/detectors/index.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAEH,OAAO,EACL,YAAY,EACZ,gBAAgB,EAChB,UAAU,EACV,cAAc,EACd,eAAe,EACf,mBAAmB,EACnB,6BAA6B,GAC9B,MAAM,YAAY,CAAC;AACpB,OAAO,EAAE,eAAe,EAAE,MAAM,mBAAmB,CAAC;AACpD,OAAO,EAAE,WAAW,EAAE,MAAM,eAAe,CAAC;AAC5C,OAAO,EAAE,mBAAmB,EAAE,MAAM,uBAAuB,CAAC;AAC5D,OAAO,EAAE,eAAe,EAAE,MAAM,mBAAmB,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport {\n  hostDetector,\n  hostDetectorSync,\n  osDetector,\n  osDetectorSync,\n  processDetector,\n  processDetectorSync,\n  serviceInstanceIdDetectorSync,\n} from './platform';\nexport { browserDetector } from './BrowserDetector';\nexport { envDetector } from './EnvDetector';\nexport { browserDetectorSync } from './BrowserDetectorSync';\nexport { envDetectorSync } from './EnvDetectorSync';\n"]}