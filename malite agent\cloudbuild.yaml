# ملف تكوين Google Cloud Build لبناء ونشر تطبيق Malite Agent
# يحدد خطوات البناء والنشر التلقائي

steps:
  # الخطوة 1: تثبيت التبعيات
  - name: 'node:20'
    entrypoint: 'npm'
    args: ['install']
    id: 'install-dependencies'
    
  # الخطوة 2: تشغيل الاختبارات
  - name: 'node:20'
    entrypoint: 'npm'
    args: ['run', 'test:models']
    id: 'run-tests'
    waitFor: ['install-dependencies']
    
  # الخطوة 3: بناء التطبيق (إذا كان هناك خطوة بناء)
  - name: 'node:20'
    entrypoint: 'npm'
    args: ['run', 'build']
    id: 'build-app'
    waitFor: ['run-tests']
    # تجاهل الأخطاء إذا لم يكن هناك script للبناء
    allowFailure: true
    
  # الخطوة 4: إنشاء ملف .env للإنتاج
  - name: 'gcr.io/cloud-builders/gcloud'
    entrypoint: 'bash'
    args:
      - '-c'
      - |
        echo "NODE_ENV=production" > .env
        echo "GENKIT_ENV=production" >> .env
        echo "VERTEX_AI_LOCATION=us-central1" >> .env
        echo "GOOGLE_CLOUD_PROJECT=$PROJECT_ID" >> .env
        echo "DEFAULT_TEMPERATURE=0.7" >> .env
        echo "DEFAULT_MAX_OUTPUT_TOKENS=1024" >> .env
        echo "ENABLE_CONTENT_FILTERING=true" >> .env
        echo "LOG_LEVEL=INFO" >> .env
    id: 'create-env-file'
    waitFor: ['build-app']
    
  # الخطوة 5: نشر على App Engine
  - name: 'gcr.io/cloud-builders/gcloud'
    args: ['app', 'deploy', '--quiet']
    id: 'deploy-app-engine'
    waitFor: ['create-env-file']
    
  # الخطوة 6: نشر على Cloud Run (بديل)
  - name: 'gcr.io/cloud-builders/docker'
    args: [
      'build',
      '-t', 'gcr.io/$PROJECT_ID/malite-agent:$BUILD_ID',
      '-t', 'gcr.io/$PROJECT_ID/malite-agent:latest',
      '.'
    ]
    id: 'build-docker-image'
    waitFor: ['create-env-file']
    
  - name: 'gcr.io/cloud-builders/docker'
    args: ['push', 'gcr.io/$PROJECT_ID/malite-agent:$BUILD_ID']
    id: 'push-docker-image'
    waitFor: ['build-docker-image']
    
  - name: 'gcr.io/cloud-builders/gcloud'
    args: [
      'run', 'deploy', 'malite-agent',
      '--image', 'gcr.io/$PROJECT_ID/malite-agent:$BUILD_ID',
      '--region', 'us-central1',
      '--platform', 'managed',
      '--allow-unauthenticated',
      '--memory', '2Gi',
      '--cpu', '1',
      '--max-instances', '10',
      '--set-env-vars', 'NODE_ENV=production,GENKIT_ENV=production,VERTEX_AI_LOCATION=us-central1'
    ]
    id: 'deploy-cloud-run'
    waitFor: ['push-docker-image']

# إعدادات البناء
options:
  # استخدام آلة أقوى للبناء
  machineType: 'E2_HIGHCPU_8'
  
  # تفعيل التخزين المؤقت
  substitution_option: 'ALLOW_LOOSE'
  
  # إعدادات السجلات
  logging: CLOUD_LOGGING_ONLY
  
  # إعدادات الشبكة
  dynamic_substitutions: true

# متغيرات البناء
substitutions:
  _SERVICE_NAME: 'malite-agent'
  _REGION: 'us-central1'
  _MEMORY: '2Gi'
  _CPU: '1'
  _MAX_INSTANCES: '10'

# المهلة الزمنية للبناء (20 دقيقة)
timeout: '1200s'

# الصور المبنية
images:
  - 'gcr.io/$PROJECT_ID/malite-agent:$BUILD_ID'
  - 'gcr.io/$PROJECT_ID/malite-agent:latest'

# إعدادات IAM المطلوبة
serviceAccount: 'projects/$PROJECT_ID/serviceAccounts/cloudbuild@$PROJECT_ID.iam.gserviceaccount.com'

# تفعيل APIs المطلوبة
availableSecrets:
  secretManager:
    - versionName: projects/$PROJECT_ID/secrets/vertex-ai-key/versions/latest
      env: 'GOOGLE_APPLICATION_CREDENTIALS_JSON'
