import * as z from "zod";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import { AssistantMessage, AssistantMessage$Outbound } from "./assistantmessage.js";
import { Prediction, Prediction$Outbound } from "./prediction.js";
import { ResponseFormat, ResponseFormat$Outbound } from "./responseformat.js";
import { SystemMessage, SystemMessage$Outbound } from "./systemmessage.js";
import { Tool, Tool$Outbound } from "./tool.js";
import { ToolChoice, ToolChoice$Outbound } from "./toolchoice.js";
import { ToolChoiceEnum } from "./toolchoiceenum.js";
import { ToolMessage, ToolMessage$Outbound } from "./toolmessage.js";
import { UserMessage, UserMessage$Outbound } from "./usermessage.js";
/**
 * Stop generation if this token is detected. Or if one of these tokens is detected when providing an array
 */
export type ChatCompletionRequestStop = string | Array<string>;
export type ChatCompletionRequestMessages = (SystemMessage & {
    role: "system";
}) | (UserMessage & {
    role: "user";
}) | (AssistantMessage & {
    role: "assistant";
}) | (ToolMessage & {
    role: "tool";
});
export type ChatCompletionRequestToolChoice = ToolChoice | ToolChoiceEnum;
export type ChatCompletionRequest = {
    /**
     * ID of the model to use. You can use the [List Available Models](/api/#tag/models/operation/list_models_v1_models_get) API to see all of your available models, or see our [Model overview](/models) for model descriptions.
     */
    model: string;
    /**
     * What sampling temperature to use, we recommend between 0.0 and 0.7. Higher values like 0.7 will make the output more random, while lower values like 0.2 will make it more focused and deterministic. We generally recommend altering this or `top_p` but not both. The default value varies depending on the model you are targeting. Call the `/models` endpoint to retrieve the appropriate value.
     */
    temperature?: number | null | undefined;
    /**
     * Nucleus sampling, where the model considers the results of the tokens with `top_p` probability mass. So 0.1 means only the tokens comprising the top 10% probability mass are considered. We generally recommend altering this or `temperature` but not both.
     */
    topP?: number | undefined;
    /**
     * The maximum number of tokens to generate in the completion. The token count of your prompt plus `max_tokens` cannot exceed the model's context length.
     */
    maxTokens?: number | null | undefined;
    /**
     * Whether to stream back partial progress. If set, tokens will be sent as data-only server-side events as they become available, with the stream terminated by a data: [DONE] message. Otherwise, the server will hold the request open until the timeout or until completion, with the response containing the full result as JSON.
     */
    stream?: boolean | undefined;
    /**
     * Stop generation if this token is detected. Or if one of these tokens is detected when providing an array
     */
    stop?: string | Array<string> | undefined;
    /**
     * The seed to use for random sampling. If set, different calls will generate deterministic results.
     */
    randomSeed?: number | null | undefined;
    /**
     * The prompt(s) to generate completions for, encoded as a list of dict with role and content.
     */
    messages: Array<(SystemMessage & {
        role: "system";
    }) | (UserMessage & {
        role: "user";
    }) | (AssistantMessage & {
        role: "assistant";
    }) | (ToolMessage & {
        role: "tool";
    })>;
    responseFormat?: ResponseFormat | undefined;
    tools?: Array<Tool> | null | undefined;
    toolChoice?: ToolChoice | ToolChoiceEnum | undefined;
    /**
     * presence_penalty determines how much the model penalizes the repetition of words or phrases. A higher presence penalty encourages the model to use a wider variety of words and phrases, making the output more diverse and creative.
     */
    presencePenalty?: number | undefined;
    /**
     * frequency_penalty penalizes the repetition of words based on their frequency in the generated text. A higher frequency penalty discourages the model from repeating words that have already appeared frequently in the output, promoting diversity and reducing repetition.
     */
    frequencyPenalty?: number | undefined;
    /**
     * Number of completions to return for each request, input tokens are only billed once.
     */
    n?: number | null | undefined;
    prediction?: Prediction | undefined;
    parallelToolCalls?: boolean | undefined;
};
/** @internal */
export declare const ChatCompletionRequestStop$inboundSchema: z.ZodType<ChatCompletionRequestStop, z.ZodTypeDef, unknown>;
/** @internal */
export type ChatCompletionRequestStop$Outbound = string | Array<string>;
/** @internal */
export declare const ChatCompletionRequestStop$outboundSchema: z.ZodType<ChatCompletionRequestStop$Outbound, z.ZodTypeDef, ChatCompletionRequestStop>;
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export declare namespace ChatCompletionRequestStop$ {
    /** @deprecated use `ChatCompletionRequestStop$inboundSchema` instead. */
    const inboundSchema: z.ZodType<ChatCompletionRequestStop, z.ZodTypeDef, unknown>;
    /** @deprecated use `ChatCompletionRequestStop$outboundSchema` instead. */
    const outboundSchema: z.ZodType<ChatCompletionRequestStop$Outbound, z.ZodTypeDef, ChatCompletionRequestStop>;
    /** @deprecated use `ChatCompletionRequestStop$Outbound` instead. */
    type Outbound = ChatCompletionRequestStop$Outbound;
}
export declare function chatCompletionRequestStopToJSON(chatCompletionRequestStop: ChatCompletionRequestStop): string;
export declare function chatCompletionRequestStopFromJSON(jsonString: string): SafeParseResult<ChatCompletionRequestStop, SDKValidationError>;
/** @internal */
export declare const ChatCompletionRequestMessages$inboundSchema: z.ZodType<ChatCompletionRequestMessages, z.ZodTypeDef, unknown>;
/** @internal */
export type ChatCompletionRequestMessages$Outbound = (SystemMessage$Outbound & {
    role: "system";
}) | (UserMessage$Outbound & {
    role: "user";
}) | (AssistantMessage$Outbound & {
    role: "assistant";
}) | (ToolMessage$Outbound & {
    role: "tool";
});
/** @internal */
export declare const ChatCompletionRequestMessages$outboundSchema: z.ZodType<ChatCompletionRequestMessages$Outbound, z.ZodTypeDef, ChatCompletionRequestMessages>;
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export declare namespace ChatCompletionRequestMessages$ {
    /** @deprecated use `ChatCompletionRequestMessages$inboundSchema` instead. */
    const inboundSchema: z.ZodType<ChatCompletionRequestMessages, z.ZodTypeDef, unknown>;
    /** @deprecated use `ChatCompletionRequestMessages$outboundSchema` instead. */
    const outboundSchema: z.ZodType<ChatCompletionRequestMessages$Outbound, z.ZodTypeDef, ChatCompletionRequestMessages>;
    /** @deprecated use `ChatCompletionRequestMessages$Outbound` instead. */
    type Outbound = ChatCompletionRequestMessages$Outbound;
}
export declare function chatCompletionRequestMessagesToJSON(chatCompletionRequestMessages: ChatCompletionRequestMessages): string;
export declare function chatCompletionRequestMessagesFromJSON(jsonString: string): SafeParseResult<ChatCompletionRequestMessages, SDKValidationError>;
/** @internal */
export declare const ChatCompletionRequestToolChoice$inboundSchema: z.ZodType<ChatCompletionRequestToolChoice, z.ZodTypeDef, unknown>;
/** @internal */
export type ChatCompletionRequestToolChoice$Outbound = ToolChoice$Outbound | string;
/** @internal */
export declare const ChatCompletionRequestToolChoice$outboundSchema: z.ZodType<ChatCompletionRequestToolChoice$Outbound, z.ZodTypeDef, ChatCompletionRequestToolChoice>;
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export declare namespace ChatCompletionRequestToolChoice$ {
    /** @deprecated use `ChatCompletionRequestToolChoice$inboundSchema` instead. */
    const inboundSchema: z.ZodType<ChatCompletionRequestToolChoice, z.ZodTypeDef, unknown>;
    /** @deprecated use `ChatCompletionRequestToolChoice$outboundSchema` instead. */
    const outboundSchema: z.ZodType<ChatCompletionRequestToolChoice$Outbound, z.ZodTypeDef, ChatCompletionRequestToolChoice>;
    /** @deprecated use `ChatCompletionRequestToolChoice$Outbound` instead. */
    type Outbound = ChatCompletionRequestToolChoice$Outbound;
}
export declare function chatCompletionRequestToolChoiceToJSON(chatCompletionRequestToolChoice: ChatCompletionRequestToolChoice): string;
export declare function chatCompletionRequestToolChoiceFromJSON(jsonString: string): SafeParseResult<ChatCompletionRequestToolChoice, SDKValidationError>;
/** @internal */
export declare const ChatCompletionRequest$inboundSchema: z.ZodType<ChatCompletionRequest, z.ZodTypeDef, unknown>;
/** @internal */
export type ChatCompletionRequest$Outbound = {
    model: string;
    temperature?: number | null | undefined;
    top_p?: number | undefined;
    max_tokens?: number | null | undefined;
    stream: boolean;
    stop?: string | Array<string> | undefined;
    random_seed?: number | null | undefined;
    messages: Array<(SystemMessage$Outbound & {
        role: "system";
    }) | (UserMessage$Outbound & {
        role: "user";
    }) | (AssistantMessage$Outbound & {
        role: "assistant";
    }) | (ToolMessage$Outbound & {
        role: "tool";
    })>;
    response_format?: ResponseFormat$Outbound | undefined;
    tools?: Array<Tool$Outbound> | null | undefined;
    tool_choice?: ToolChoice$Outbound | string | undefined;
    presence_penalty?: number | undefined;
    frequency_penalty?: number | undefined;
    n?: number | null | undefined;
    prediction?: Prediction$Outbound | undefined;
    parallel_tool_calls?: boolean | undefined;
};
/** @internal */
export declare const ChatCompletionRequest$outboundSchema: z.ZodType<ChatCompletionRequest$Outbound, z.ZodTypeDef, ChatCompletionRequest>;
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export declare namespace ChatCompletionRequest$ {
    /** @deprecated use `ChatCompletionRequest$inboundSchema` instead. */
    const inboundSchema: z.ZodType<ChatCompletionRequest, z.ZodTypeDef, unknown>;
    /** @deprecated use `ChatCompletionRequest$outboundSchema` instead. */
    const outboundSchema: z.ZodType<ChatCompletionRequest$Outbound, z.ZodTypeDef, ChatCompletionRequest>;
    /** @deprecated use `ChatCompletionRequest$Outbound` instead. */
    type Outbound = ChatCompletionRequest$Outbound;
}
export declare function chatCompletionRequestToJSON(chatCompletionRequest: ChatCompletionRequest): string;
export declare function chatCompletionRequestFromJSON(jsonString: string): SafeParseResult<ChatCompletionRequest, SDKValidationError>;
//# sourceMappingURL=chatcompletionrequest.d.ts.map