# Dockerfile لتطبيق Malite Agent
# يستخدم لبناء صورة Docker للنشر على Cloud Run أو Kubernetes

# استخدام Node.js 20 كصورة أساسية
FROM node:20-slim

# تعيين مجلد العمل
WORKDIR /app

# نسخ ملفات package.json و package-lock.json
COPY package*.json ./

# تثبيت التبعيات الإنتاجية فقط
RUN npm ci --only=production && npm cache clean --force

# إنشاء مستخدم غير جذر لتشغيل التطبيق
RUN groupadd -r appuser && useradd -r -g appuser appuser

# نسخ ملفات التطبيق
COPY --chown=appuser:appuser . .

# إنشاء مجلدات مطلوبة
RUN mkdir -p /app/logs /app/temp && \
    chown -R appuser:appuser /app/logs /app/temp

# تعيين متغيرات البيئة
ENV NODE_ENV=production
ENV GENKIT_ENV=production
ENV PORT=8080

# فتح المنفذ
EXPOSE 8080

# التبديل إلى المستخدم غير الجذر
USER appuser

# فحص صحة التطبيق
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
  CMD node healthcheck.js || exit 1

# تشغيل التطبيق
CMD ["npm", "start"]
