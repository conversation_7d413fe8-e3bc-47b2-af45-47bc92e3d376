# Dockerfile لتطبيق Malite Agent
# يستخدم لبناء صورة Docker للنشر على Cloud Run أو Kubernetes

# استخدام Node.js 20 كصورة أساسية مع Alpine للحصول على حجم أصغر
FROM node:20-alpine

# تثبيت الأدوات المطلوبة
RUN apk add --no-cache \
    curl \
    jq \
    && rm -rf /var/cache/apk/*

# تعيين مجلد العمل
WORKDIR /app

# إنشاء مستخدم غير جذر مبكراً
RUN addgroup -g 1001 -S appuser && \
    adduser -S appuser -u 1001 -G appuser

# نسخ ملفات package.json و package-lock.json
COPY --chown=appuser:appuser package*.json ./

# نسخ ملفات التطبيق
COPY --chown=appuser:appuser . .

# تثبيت التبعيات وإنشاء المجلدات المطلوبة
RUN npm ci --only=production --silent && \
    npm cache clean --force && \
    mkdir -p /app/logs /app/temp && \
    chown -R appuser:appuser /app/logs /app/temp

# تعيين متغيرات البيئة
ENV NODE_ENV=production
ENV GENKIT_ENV=production
ENV PORT=8080

# فتح المنفذ
EXPOSE 8080

# التبديل إلى المستخدم غير الجذر
USER appuser

# جعل script فحص الصحة قابل للتنفيذ
RUN chmod +x docker-healthcheck.sh

# فحص صحة التطبيق
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
  CMD ["./docker-healthcheck.sh"]

# تشغيل التطبيق
CMD ["npm", "start"]
