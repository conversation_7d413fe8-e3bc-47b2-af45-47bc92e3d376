{"version": 3, "file": "MetricData.js", "sourceRoot": "", "sources": ["../../../src/export/MetricData.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAoGH;;GAEG;AACH,MAAM,CAAN,IAAY,aAuBX;AAvBD,WAAY,aAAa;IACvB;;;;OAIG;IACH,2DAAS,CAAA;IACT;;;;;OAKG;IACH,mFAAqB,CAAA;IACrB;;OAEG;IACH,mDAAK,CAAA;IACL;;;OAGG;IACH,+CAAG,CAAA;AACL,CAAC,EAvBW,aAAa,KAAb,aAAa,QAuBxB", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { HrTime, Attributes, ValueType } from '@opentelemetry/api';\nimport { InstrumentationScope } from '@opentelemetry/core';\nimport { IResource } from '@opentelemetry/resources';\nimport { InstrumentType } from '../InstrumentDescriptor';\nimport { AggregationTemporality } from './AggregationTemporality';\nimport { Histogram, ExponentialHistogram } from '../aggregator/types';\n\nexport interface MetricDescriptor {\n  readonly name: string;\n  readonly description: string;\n  readonly unit: string;\n  /**\n   * @deprecated exporter should avoid depending on the type of the instrument\n   * as their resulting aggregator can be re-mapped with views.\n   */\n  readonly type: InstrumentType;\n  readonly valueType: ValueType;\n}\n\n/**\n * Basic metric data fields.\n */\ninterface BaseMetricData {\n  readonly descriptor: MetricDescriptor;\n  readonly aggregationTemporality: AggregationTemporality;\n  /**\n   * DataPointType of the metric instrument.\n   */\n  readonly dataPointType: DataPointType;\n}\n\n/**\n * Represents a metric data aggregated by either a LastValueAggregation or\n * SumAggregation.\n */\nexport interface SumMetricData extends BaseMetricData {\n  readonly dataPointType: DataPointType.SUM;\n  readonly dataPoints: DataPoint<number>[];\n  readonly isMonotonic: boolean;\n}\n\nexport interface GaugeMetricData extends BaseMetricData {\n  readonly dataPointType: DataPointType.GAUGE;\n  readonly dataPoints: DataPoint<number>[];\n}\n\n/**\n * Represents a metric data aggregated by a HistogramAggregation.\n */\nexport interface HistogramMetricData extends BaseMetricData {\n  readonly dataPointType: DataPointType.HISTOGRAM;\n  readonly dataPoints: DataPoint<Histogram>[];\n}\n\n/**\n * Represents a metric data aggregated by a ExponentialHistogramAggregation.\n */\nexport interface ExponentialHistogramMetricData extends BaseMetricData {\n  readonly dataPointType: DataPointType.EXPONENTIAL_HISTOGRAM;\n  readonly dataPoints: DataPoint<ExponentialHistogram>[];\n}\n\n/**\n * Represents an aggregated metric data.\n */\nexport type MetricData =\n  | SumMetricData\n  | GaugeMetricData\n  | HistogramMetricData\n  | ExponentialHistogramMetricData;\n\nexport interface ScopeMetrics {\n  scope: InstrumentationScope;\n  metrics: MetricData[];\n}\n\nexport interface ResourceMetrics {\n  resource: IResource;\n  scopeMetrics: ScopeMetrics[];\n}\n\n/**\n * Represents the collection result of the metrics. If there are any\n * non-critical errors in the collection, like throwing in a single observable\n * callback, these errors are aggregated in the {@link CollectionResult.errors}\n * array and other successfully collected metrics are returned.\n */\nexport interface CollectionResult {\n  /**\n   * Collected metrics.\n   */\n  resourceMetrics: ResourceMetrics;\n  /**\n   * Arbitrary JavaScript exception values.\n   */\n  errors: unknown[];\n}\n\n/**\n * The aggregated point data type.\n */\nexport enum DataPointType {\n  /**\n   * A histogram data point contains a histogram statistics of collected\n   * values with a list of explicit bucket boundaries and statistics such\n   * as min, max, count, and sum of all collected values.\n   */\n  HISTOGRAM,\n  /**\n   * An exponential histogram data point contains a histogram statistics of\n   * collected values where bucket boundaries are automatically calculated\n   * using an exponential function, and statistics such as min, max, count,\n   * and sum of all collected values.\n   */\n  EXPONENTIAL_HISTOGRAM,\n  /**\n   * A gauge metric data point has only a single numeric value.\n   */\n  GAUGE,\n  /**\n   * A sum metric data point has a single numeric value and a\n   * monotonicity-indicator.\n   */\n  SUM,\n}\n\n/**\n * Represents an aggregated point data with start time, end time and their\n * associated attributes and points.\n */\nexport interface DataPoint<T> {\n  /**\n   * The start epoch timestamp of the DataPoint, usually the time when\n   * the metric was created when the preferred AggregationTemporality is\n   * CUMULATIVE, or last collection time otherwise.\n   */\n  readonly startTime: HrTime;\n  /**\n   * The end epoch timestamp when data were collected, usually it represents\n   * the moment when `MetricReader.collect` was called.\n   */\n  readonly endTime: HrTime;\n  /**\n   * The attributes associated with this DataPoint.\n   */\n  readonly attributes: Attributes;\n  /**\n   * The value for this DataPoint. The type of the value is indicated by the\n   * {@link DataPointType}.\n   */\n  readonly value: T;\n}\n"]}