{"interfaces": {"google.cloud.aiplatform.v1.MetadataService": {"retry_codes": {"non_idempotent": [], "idempotent": ["DEADLINE_EXCEEDED", "UNAVAILABLE"]}, "retry_params": {"default": {"initial_retry_delay_millis": 100, "retry_delay_multiplier": 1.3, "max_retry_delay_millis": 60000, "initial_rpc_timeout_millis": 60000, "rpc_timeout_multiplier": 1, "max_rpc_timeout_millis": 60000, "total_timeout_millis": 600000}}, "methods": {"CreateMetadataStore": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "GetMetadataStore": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "ListMetadataStores": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "DeleteMetadataStore": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "CreateArtifact": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "GetArtifact": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "ListArtifacts": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "UpdateArtifact": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "DeleteArtifact": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "PurgeArtifacts": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "CreateContext": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "GetContext": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "ListContexts": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "UpdateContext": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "DeleteContext": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "PurgeContexts": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "AddContextArtifactsAndExecutions": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "AddContextChildren": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "RemoveContextChildren": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "QueryContextLineageSubgraph": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "CreateExecution": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "GetExecution": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "ListExecutions": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "UpdateExecution": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "DeleteExecution": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "PurgeExecutions": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "AddExecutionEvents": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "QueryExecutionInputsAndOutputs": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "CreateMetadataSchema": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "GetMetadataSchema": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "ListMetadataSchemas": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "QueryArtifactLineageSubgraph": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}}}}}