"use strict";
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FIMCompletionRequest$ = exports.FIMCompletionRequest$outboundSchema = exports.FIMCompletionRequest$inboundSchema = exports.FIMCompletionRequestStop$ = exports.FIMCompletionRequestStop$outboundSchema = exports.FIMCompletionRequestStop$inboundSchema = void 0;
exports.fimCompletionRequestStopToJSON = fimCompletionRequestStopToJSON;
exports.fimCompletionRequestStopFromJSON = fimCompletionRequestStopFromJSON;
exports.fimCompletionRequestToJSON = fimCompletionRequestToJSON;
exports.fimCompletionRequestFromJSON = fimCompletionRequestFromJSON;
const z = __importStar(require("zod"));
const primitives_js_1 = require("../../lib/primitives.js");
const schemas_js_1 = require("../../lib/schemas.js");
/** @internal */
exports.FIMCompletionRequestStop$inboundSchema = z.union([z.string(), z.array(z.string())]);
/** @internal */
exports.FIMCompletionRequestStop$outboundSchema = z.union([z.string(), z.array(z.string())]);
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var FIMCompletionRequestStop$;
(function (FIMCompletionRequestStop$) {
    /** @deprecated use `FIMCompletionRequestStop$inboundSchema` instead. */
    FIMCompletionRequestStop$.inboundSchema = exports.FIMCompletionRequestStop$inboundSchema;
    /** @deprecated use `FIMCompletionRequestStop$outboundSchema` instead. */
    FIMCompletionRequestStop$.outboundSchema = exports.FIMCompletionRequestStop$outboundSchema;
})(FIMCompletionRequestStop$ || (exports.FIMCompletionRequestStop$ = FIMCompletionRequestStop$ = {}));
function fimCompletionRequestStopToJSON(fimCompletionRequestStop) {
    return JSON.stringify(exports.FIMCompletionRequestStop$outboundSchema.parse(fimCompletionRequestStop));
}
function fimCompletionRequestStopFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.FIMCompletionRequestStop$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'FIMCompletionRequestStop' from JSON`);
}
/** @internal */
exports.FIMCompletionRequest$inboundSchema = z.object({
    model: z.string(),
    temperature: z.nullable(z.number()).optional(),
    top_p: z.number().default(1),
    max_tokens: z.nullable(z.number().int()).optional(),
    stream: z.boolean().default(false),
    stop: z.union([z.string(), z.array(z.string())]).optional(),
    random_seed: z.nullable(z.number().int()).optional(),
    prompt: z.string(),
    suffix: z.nullable(z.string()).optional(),
    min_tokens: z.nullable(z.number().int()).optional(),
}).transform((v) => {
    return (0, primitives_js_1.remap)(v, {
        "top_p": "topP",
        "max_tokens": "maxTokens",
        "random_seed": "randomSeed",
        "min_tokens": "minTokens",
    });
});
/** @internal */
exports.FIMCompletionRequest$outboundSchema = z.object({
    model: z.string(),
    temperature: z.nullable(z.number()).optional(),
    topP: z.number().default(1),
    maxTokens: z.nullable(z.number().int()).optional(),
    stream: z.boolean().default(false),
    stop: z.union([z.string(), z.array(z.string())]).optional(),
    randomSeed: z.nullable(z.number().int()).optional(),
    prompt: z.string(),
    suffix: z.nullable(z.string()).optional(),
    minTokens: z.nullable(z.number().int()).optional(),
}).transform((v) => {
    return (0, primitives_js_1.remap)(v, {
        topP: "top_p",
        maxTokens: "max_tokens",
        randomSeed: "random_seed",
        minTokens: "min_tokens",
    });
});
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var FIMCompletionRequest$;
(function (FIMCompletionRequest$) {
    /** @deprecated use `FIMCompletionRequest$inboundSchema` instead. */
    FIMCompletionRequest$.inboundSchema = exports.FIMCompletionRequest$inboundSchema;
    /** @deprecated use `FIMCompletionRequest$outboundSchema` instead. */
    FIMCompletionRequest$.outboundSchema = exports.FIMCompletionRequest$outboundSchema;
})(FIMCompletionRequest$ || (exports.FIMCompletionRequest$ = FIMCompletionRequest$ = {}));
function fimCompletionRequestToJSON(fimCompletionRequest) {
    return JSON.stringify(exports.FIMCompletionRequest$outboundSchema.parse(fimCompletionRequest));
}
function fimCompletionRequestFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.FIMCompletionRequest$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'FIMCompletionRequest' from JSON`);
}
//# sourceMappingURL=fimcompletionrequest.js.map