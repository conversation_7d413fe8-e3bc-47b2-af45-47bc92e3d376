{"version": 3, "file": "detect-resources.js", "sourceRoot": "", "sources": ["../../src/detect-resources.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,yCAAsC;AAEtC,4CAA0C;AAC1C,mCAAwC;AAIxC;;;;;;;GAOG;AACI,MAAM,eAAe,GAAG,KAAK,EAClC,SAAkC,EAAE,EAChB,EAAE;IACtB,MAAM,SAAS,GAAgB,MAAM,OAAO,CAAC,GAAG,CAC9C,CAAC,MAAM,CAAC,SAAS,IAAI,EAAE,CAAC,CAAC,GAAG,CAAC,KAAK,EAAC,CAAC,EAAC,EAAE;QACrC,IAAI;YACF,MAAM,QAAQ,GAAG,MAAM,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;YACxC,UAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,WAAW,CAAC,IAAI,kBAAkB,EAAE,QAAQ,CAAC,CAAC;YAC9D,OAAO,QAAQ,CAAC;SACjB;QAAC,OAAO,CAAC,EAAE;YACV,UAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,WAAW,CAAC,IAAI,YAAY,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC;YACzD,OAAO,mBAAQ,CAAC,KAAK,EAAE,CAAC;SACzB;IACH,CAAC,CAAC,CACH,CAAC;IAEF,yDAAyD;IACzD,YAAY,CAAC,SAAS,CAAC,CAAC;IAExB,OAAO,SAAS,CAAC,MAAM,CACrB,CAAC,GAAG,EAAE,QAAQ,EAAE,EAAE,CAAC,GAAG,CAAC,KAAK,CAAC,QAAQ,CAAC,EACtC,mBAAQ,CAAC,KAAK,EAAE,CACjB,CAAC;AACJ,CAAC,CAAC;AAvBW,QAAA,eAAe,mBAuB1B;AAEF;;;;GAIG;AACI,MAAM,mBAAmB,GAAG,CACjC,SAAkC,EAAE,EACzB,EAAE;;IACb,MAAM,SAAS,GAAgB,CAAC,MAAA,MAAM,CAAC,SAAS,mCAAI,EAAE,CAAC,CAAC,GAAG,CACzD,CAAC,CAA0B,EAAE,EAAE;QAC7B,IAAI;YACF,MAAM,iBAAiB,GAAG,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;YAC3C,IAAI,QAAmB,CAAC;YACxB,IAAI,IAAA,qBAAa,EAAW,iBAAiB,CAAC,EAAE;gBAC9C,MAAM,aAAa,GAAG,KAAK,IAAI,EAAE;oBAC/B,MAAM,gBAAgB,GAAG,MAAM,iBAAiB,CAAC;oBACjD,OAAO,gBAAgB,CAAC,UAAU,CAAC;gBACrC,CAAC,CAAC;gBACF,QAAQ,GAAG,IAAI,mBAAQ,CAAC,EAAE,EAAE,aAAa,EAAE,CAAC,CAAC;aAC9C;iBAAM;gBACL,QAAQ,GAAG,iBAA8B,CAAC;aAC3C;YAED,IAAI,QAAQ,CAAC,sBAAsB,EAAE;gBACnC,KAAK,QAAQ;qBACV,sBAAsB,EAAE;qBACxB,IAAI,CAAC,GAAG,EAAE,CACT,UAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,WAAW,CAAC,IAAI,kBAAkB,EAAE,QAAQ,CAAC,CAC9D,CAAC;aACL;iBAAM;gBACL,UAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,WAAW,CAAC,IAAI,kBAAkB,EAAE,QAAQ,CAAC,CAAC;aAC/D;YAED,OAAO,QAAQ,CAAC;SACjB;QAAC,OAAO,CAAC,EAAE;YACV,UAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,WAAW,CAAC,IAAI,YAAY,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC;YACzD,OAAO,mBAAQ,CAAC,KAAK,EAAE,CAAC;SACzB;IACH,CAAC,CACF,CAAC;IAEF,MAAM,eAAe,GAAG,SAAS,CAAC,MAAM,CACtC,CAAC,GAAG,EAAE,QAAQ,EAAE,EAAE,CAAC,GAAG,CAAC,KAAK,CAAC,QAAQ,CAAC,EACtC,mBAAQ,CAAC,KAAK,EAAE,CACjB,CAAC;IAEF,IAAI,eAAe,CAAC,sBAAsB,EAAE;QAC1C,KAAK,eAAe,CAAC,sBAAsB,EAAE,CAAC,IAAI,CAAC,GAAG,EAAE;YACtD,yDAAyD;YACzD,YAAY,CAAC,SAAS,CAAC,CAAC;QAC1B,CAAC,CAAC,CAAC;KACJ;IAED,OAAO,eAAe,CAAC;AACzB,CAAC,CAAC;AAjDW,QAAA,mBAAmB,uBAiD9B;AAEF;;;;GAIG;AACH,MAAM,YAAY,GAAG,CAAC,SAA2B,EAAE,EAAE;IACnD,SAAS,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;QAC3B,iCAAiC;QACjC,IAAI,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE;YAC/C,MAAM,mBAAmB,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,UAAU,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;YACzE,UAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,CAAC;SACnC;IACH,CAAC,CAAC,CAAC;AACL,CAAC,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Resource } from './Resource';\nimport { ResourceDetectionConfig } from './config';\nimport { diag } from '@opentelemetry/api';\nimport { isPromiseLike } from './utils';\nimport { Detector, DetectorSync } from './types';\nimport { IResource } from './IResource';\n\n/**\n * Runs all resource detectors and returns the results merged into a single Resource. Promise\n * does not resolve until all the underlying detectors have resolved, unlike\n * detectResourcesSync.\n *\n * @deprecated use detectResourcesSync() instead.\n * @param config Configuration for resource detection\n */\nexport const detectResources = async (\n  config: ResourceDetectionConfig = {}\n): Promise<IResource> => {\n  const resources: IResource[] = await Promise.all(\n    (config.detectors || []).map(async d => {\n      try {\n        const resource = await d.detect(config);\n        diag.debug(`${d.constructor.name} found resource.`, resource);\n        return resource;\n      } catch (e) {\n        diag.debug(`${d.constructor.name} failed: ${e.message}`);\n        return Resource.empty();\n      }\n    })\n  );\n\n  // Future check if verbose logging is enabled issue #1903\n  logResources(resources);\n\n  return resources.reduce(\n    (acc, resource) => acc.merge(resource),\n    Resource.empty()\n  );\n};\n\n/**\n * Runs all resource detectors synchronously, merging their results. In case of attribute collision later resources will take precedence.\n *\n * @param config Configuration for resource detection\n */\nexport const detectResourcesSync = (\n  config: ResourceDetectionConfig = {}\n): IResource => {\n  const resources: IResource[] = (config.detectors ?? []).map(\n    (d: Detector | DetectorSync) => {\n      try {\n        const resourceOrPromise = d.detect(config);\n        let resource: IResource;\n        if (isPromiseLike<Resource>(resourceOrPromise)) {\n          const createPromise = async () => {\n            const resolvedResource = await resourceOrPromise;\n            return resolvedResource.attributes;\n          };\n          resource = new Resource({}, createPromise());\n        } else {\n          resource = resourceOrPromise as IResource;\n        }\n\n        if (resource.waitForAsyncAttributes) {\n          void resource\n            .waitForAsyncAttributes()\n            .then(() =>\n              diag.debug(`${d.constructor.name} found resource.`, resource)\n            );\n        } else {\n          diag.debug(`${d.constructor.name} found resource.`, resource);\n        }\n\n        return resource;\n      } catch (e) {\n        diag.error(`${d.constructor.name} failed: ${e.message}`);\n        return Resource.empty();\n      }\n    }\n  );\n\n  const mergedResources = resources.reduce(\n    (acc, resource) => acc.merge(resource),\n    Resource.empty()\n  );\n\n  if (mergedResources.waitForAsyncAttributes) {\n    void mergedResources.waitForAsyncAttributes().then(() => {\n      // Future check if verbose logging is enabled issue #1903\n      logResources(resources);\n    });\n  }\n\n  return mergedResources;\n};\n\n/**\n * Writes debug information about the detected resources to the logger defined in the resource detection config, if one is provided.\n *\n * @param resources The array of {@link Resource} that should be logged. Empty entries will be ignored.\n */\nconst logResources = (resources: Array<IResource>) => {\n  resources.forEach(resource => {\n    // Print only populated resources\n    if (Object.keys(resource.attributes).length > 0) {\n      const resourceDebugString = JSON.stringify(resource.attributes, null, 4);\n      diag.verbose(resourceDebugString);\n    }\n  });\n};\n"]}