/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
export { toLongBits, getOtlpEncoder, encodeAsLongBits, encodeAsString, hrTimeToNanos, } from './common';
export { ESpanKind, } from './trace/types';
export { createExportTraceServiceRequest } from './trace';
export { createExportMetricsServiceRequest } from './metrics';
export { createExportLogsServiceRequest } from './logs';
export { ProtobufLogsSerializer, ProtobufMetricsSerializer, ProtobufTraceSerializer, } from './protobuf/serializers';
export { JsonTraceSerializer, JsonLogsSerializer, JsonMetricsSerializer, } from './json/serializers';
//# sourceMappingURL=index.js.map