{"version": 3, "file": "Metric.js", "sourceRoot": "", "sources": ["../../../src/metrics/Metric.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AA+CH,gEAAgE;AAChE,MAAM,CAAN,IAAY,SAGX;AAHD,WAAY,SAAS;IACnB,uCAAG,CAAA;IACH,6CAAM,CAAA;AACR,CAAC,EAHW,SAAS,KAAT,SAAS,QAGpB", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Attributes, AttributeValue } from '../common/Attributes';\nimport { Context } from '../context/types';\nimport { BatchObservableResult, ObservableResult } from './ObservableResult';\n\n/**\n * Advisory options influencing aggregation configuration parameters.\n * @experimental\n */\nexport interface MetricAdvice {\n  /**\n   * Hint the explicit bucket boundaries for SDK if the metric is been\n   * aggregated with a HistogramAggregator.\n   */\n  explicitBucketBoundaries?: number[];\n}\n\n/**\n * Options needed for metric creation\n */\nexport interface MetricOptions {\n  /**\n   * The description of the Metric.\n   * @default ''\n   */\n  description?: string;\n\n  /**\n   * The unit of the Metric values.\n   * @default ''\n   */\n  unit?: string;\n\n  /**\n   * Indicates the type of the recorded value.\n   * @default {@link ValueType.DOUBLE}\n   */\n  valueType?: ValueType;\n\n  /**\n   * The advice influencing aggregation configuration parameters.\n   * @experimental\n   */\n  advice?: MetricAdvice;\n}\n\n/** The Type of value. It describes how the data is reported. */\nexport enum ValueType {\n  INT,\n  DOUBLE,\n}\n\n/**\n * Counter is the most common synchronous instrument. This instrument supports\n * an `Add(increment)` function for reporting a sum, and is restricted to\n * non-negative increments. The default aggregation is Sum, as for any additive\n * instrument.\n *\n * Example uses for Counter:\n * <ol>\n *   <li> count the number of bytes received. </li>\n *   <li> count the number of requests completed. </li>\n *   <li> count the number of accounts created. </li>\n *   <li> count the number of checkpoints run. </li>\n *   <li> count the number of 5xx errors. </li>\n * <ol>\n */\nexport interface Counter<\n  AttributesTypes extends MetricAttributes = MetricAttributes,\n> {\n  /**\n   * Increment value of counter by the input. Inputs must not be negative.\n   */\n  add(value: number, attributes?: AttributesTypes, context?: Context): void;\n}\n\nexport interface UpDownCounter<\n  AttributesTypes extends MetricAttributes = MetricAttributes,\n> {\n  /**\n   * Increment value of counter by the input. Inputs may be negative.\n   */\n  add(value: number, attributes?: AttributesTypes, context?: Context): void;\n}\n\nexport interface Gauge<\n  AttributesTypes extends MetricAttributes = MetricAttributes,\n> {\n  /**\n   * Records a measurement.\n   */\n  record(value: number, attributes?: AttributesTypes, context?: Context): void;\n}\n\nexport interface Histogram<\n  AttributesTypes extends MetricAttributes = MetricAttributes,\n> {\n  /**\n   * Records a measurement. Value of the measurement must not be negative.\n   */\n  record(value: number, attributes?: AttributesTypes, context?: Context): void;\n}\n\n/**\n * @deprecated please use {@link Attributes}\n */\nexport type MetricAttributes = Attributes;\n\n/**\n * @deprecated please use {@link AttributeValue}\n */\nexport type MetricAttributeValue = AttributeValue;\n\n/**\n * The observable callback for Observable instruments.\n */\nexport type ObservableCallback<\n  AttributesTypes extends MetricAttributes = MetricAttributes,\n> = (\n  observableResult: ObservableResult<AttributesTypes>\n) => void | Promise<void>;\n\n/**\n * The observable callback for a batch of Observable instruments.\n */\nexport type BatchObservableCallback<\n  AttributesTypes extends MetricAttributes = MetricAttributes,\n> = (\n  observableResult: BatchObservableResult<AttributesTypes>\n) => void | Promise<void>;\n\nexport interface Observable<\n  AttributesTypes extends MetricAttributes = MetricAttributes,\n> {\n  /**\n   * Sets up a function that will be called whenever a metric collection is initiated.\n   *\n   * If the function is already in the list of callbacks for this Observable, the function is not added a second time.\n   */\n  addCallback(callback: ObservableCallback<AttributesTypes>): void;\n\n  /**\n   * Removes a callback previously registered with {@link Observable.addCallback}.\n   */\n  removeCallback(callback: ObservableCallback<AttributesTypes>): void;\n}\n\nexport type ObservableCounter<\n  AttributesTypes extends MetricAttributes = MetricAttributes,\n> = Observable<AttributesTypes>;\nexport type ObservableUpDownCounter<\n  AttributesTypes extends MetricAttributes = MetricAttributes,\n> = Observable<AttributesTypes>;\nexport type ObservableGauge<\n  AttributesTypes extends MetricAttributes = MetricAttributes,\n> = Observable<AttributesTypes>;\n"]}