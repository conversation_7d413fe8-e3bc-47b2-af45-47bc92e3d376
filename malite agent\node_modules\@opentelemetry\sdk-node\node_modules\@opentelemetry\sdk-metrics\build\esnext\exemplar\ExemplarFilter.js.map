{"version": 3, "file": "ExemplarFilter.js", "sourceRoot": "", "sources": ["../../../src/exemplar/ExemplarFilter.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Context, HrTime, MetricAttributes } from '@opentelemetry/api';\n\n/**\n * This interface represents a ExemplarFilter. Exemplar filters are\n * used to filter measurements before attempting to store them in a\n * reservoir.\n */\nexport interface ExemplarFilter {\n  /**\n   * Returns whether or not a reservoir should attempt to filter a measurement.\n   *\n   * @param value The value of the measurement\n   * @param timestamp A timestamp that best represents when the measurement was taken\n   * @param attributes The complete set of MetricAttributes of the measurement\n   * @param ctx The Context of the measurement\n   */\n  shouldSample(\n    value: number,\n    timestamp: HrTime,\n    attributes: MetricAttributes,\n    ctx: Context\n  ): boolean;\n}\n"]}