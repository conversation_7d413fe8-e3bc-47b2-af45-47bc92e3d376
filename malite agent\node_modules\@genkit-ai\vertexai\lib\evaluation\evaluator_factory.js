"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
var evaluator_factory_exports = {};
__export(evaluator_factory_exports, {
  EvaluatorFactory: () => EvaluatorFactory
});
module.exports = __toCommonJS(evaluator_factory_exports);
var import_genkit = require("genkit");
var import_tracing = require("genkit/tracing");
class EvaluatorFactory {
  constructor(auth, location, projectId) {
    this.auth = auth;
    this.location = location;
    this.projectId = projectId;
  }
  create(ai, config, toRequest, responseHandler) {
    return ai.defineEvaluator(
      {
        name: `vertexai/${config.metric.toLocaleLowerCase()}`,
        displayName: config.displayName,
        definition: config.definition
      },
      async (datapoint) => {
        const responseSchema = config.responseSchema;
        const response = await this.evaluateInstances(
          ai,
          toRequest(datapoint),
          responseSchema
        );
        return {
          evaluation: responseHandler(response),
          testCaseId: datapoint.testCaseId
        };
      }
    );
  }
  async evaluateInstances(ai, partialRequest, responseSchema) {
    const locationName = `projects/${this.projectId}/locations/${this.location}`;
    return await (0, import_tracing.runInNewSpan)(
      ai,
      {
        metadata: {
          name: "EvaluationService#evaluateInstances"
        }
      },
      async (metadata, _otSpan) => {
        const request = {
          location: locationName,
          ...partialRequest
        };
        metadata.input = request;
        const client = await this.auth.getClient();
        const url = `https://${this.location}-aiplatform.googleapis.com/v1beta1/${locationName}:evaluateInstances`;
        const response = await client.request({
          url,
          method: "POST",
          body: JSON.stringify(request),
          headers: {
            "X-Goog-Api-Client": import_genkit.GENKIT_CLIENT_HEADER
          }
        });
        metadata.output = response.data;
        try {
          return responseSchema.parse(response.data);
        } catch (e) {
          throw new Error(`Error parsing ${url} API response: ${e}`);
        }
      }
    );
  }
}
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  EvaluatorFactory
});
//# sourceMappingURL=evaluator_factory.js.map