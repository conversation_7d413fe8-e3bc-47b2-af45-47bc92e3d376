{"interfaces": {"google.cloud.aiplatform.v1.EndpointService": {"retry_codes": {"non_idempotent": [], "idempotent": ["DEADLINE_EXCEEDED", "UNAVAILABLE"]}, "retry_params": {"default": {"initial_retry_delay_millis": 100, "retry_delay_multiplier": 1.3, "max_retry_delay_millis": 60000, "initial_rpc_timeout_millis": 60000, "rpc_timeout_multiplier": 1, "max_rpc_timeout_millis": 60000, "total_timeout_millis": 600000}}, "methods": {"CreateEndpoint": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "GetEndpoint": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "ListEndpoints": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "UpdateEndpoint": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "UpdateEndpointLongRunning": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "DeleteEndpoint": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "DeployModel": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "UndeployModel": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "MutateDeployedModel": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}}}}}