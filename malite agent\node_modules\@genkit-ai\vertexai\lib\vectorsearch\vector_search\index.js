"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
var vector_search_exports = {};
__export(vector_search_exports, {
  VertexAIVectorIndexerOptionsSchema: () => import_types.VertexAIVectorIndexerOptionsSchema,
  VertexAIVectorRetrieverOptionsSchema: () => import_types.VertexAIVectorRetrieverOptionsSchema,
  getBigQueryDocumentIndexer: () => import_bigquery.getBigQueryDocumentIndexer,
  getBigQueryDocumentRetriever: () => import_bigquery.getBigQueryDocumentRetriever,
  getFirestoreDocumentIndexer: () => import_firestore.getFirestoreDocumentIndexer,
  getFirestoreDocumentRetriever: () => import_firestore.getFirestoreDocumentRetriever,
  vertexAiIndexerRef: () => import_indexers.vertexAiIndexerRef,
  vertexAiIndexers: () => import_indexers.vertexAiIndexers,
  vertexAiRetrieverRef: () => import_retrievers.vertexAiRetrieverRef,
  vertexAiRetrievers: () => import_retrievers.vertexAiRetrievers
});
module.exports = __toCommonJS(vector_search_exports);
var import_bigquery = require("./bigquery.js");
var import_firestore = require("./firestore.js");
var import_indexers = require("./indexers.js");
var import_retrievers = require("./retrievers.js");
var import_types = require("./types.js");
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  VertexAIVectorIndexerOptionsSchema,
  VertexAIVectorRetrieverOptionsSchema,
  getBigQueryDocumentIndexer,
  getBigQueryDocumentRetriever,
  getFirestoreDocumentIndexer,
  getFirestoreDocumentRetriever,
  vertexAiIndexerRef,
  vertexAiIndexers,
  vertexAiRetrieverRef,
  vertexAiRetrievers
});
//# sourceMappingURL=index.js.map