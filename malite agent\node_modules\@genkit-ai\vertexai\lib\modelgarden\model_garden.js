"use strict";
var __create = Object.create;
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __getProtoOf = Object.getPrototypeOf;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(
  // If the importer is in node compatibility mode or this is not an ESM
  // file that has been converted to a CommonJS file using a Babel-
  // compatible transform (i.e. "__esModule" has not been set), then set
  // "default" to the CommonJS "module.exports" for node compatibility.
  isNodeMode || !mod || !mod.__esModule ? __defProp(target, "default", { value: mod, enumerable: true }) : target,
  mod
));
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
var model_garden_exports = {};
__export(model_garden_exports, {
  ModelGardenModelConfigSchema: () => ModelGardenModelConfigSchema,
  SUPPORTED_OPENAI_FORMAT_MODELS: () => SUPPORTED_OPENAI_FORMAT_MODELS,
  llama3: () => llama3,
  llama31: () => llama31,
  llama32: () => llama32,
  modelGardenOpenaiCompatibleModel: () => modelGardenOpenaiCompatibleModel
});
module.exports = __toCommonJS(model_garden_exports);
var import_genkit = require("genkit");
var import_model = require("genkit/model");
var import_openai = __toESM(require("openai"));
var import_openai_compatibility = require("./openai_compatibility.js");
const ModelGardenModelConfigSchema = import_openai_compatibility.OpenAIConfigSchema.extend({
  location: import_genkit.z.string().optional()
});
const llama31 = (0, import_model.modelRef)({
  name: "vertexai/llama-3.1",
  info: {
    label: "Llama 3.1",
    supports: {
      multiturn: true,
      tools: true,
      media: false,
      systemRole: true,
      output: ["text", "json"]
    },
    versions: [
      "meta/llama3-405b-instruct-maas"
      // 8b and 70b versions are coming soon
    ]
  },
  configSchema: ModelGardenModelConfigSchema,
  version: "meta/llama3-405b-instruct-maas"
});
const llama32 = (0, import_model.modelRef)({
  name: "vertexai/llama-3.2",
  info: {
    label: "Llama 3.2",
    supports: {
      multiturn: true,
      tools: true,
      media: true,
      systemRole: true,
      output: ["text", "json"]
    },
    versions: ["meta/llama-3.2-90b-vision-instruct-maas"]
  },
  configSchema: ModelGardenModelConfigSchema,
  version: "meta/llama-3.2-90b-vision-instruct-maas"
});
const llama3 = (0, import_model.modelRef)({
  name: "vertexai/llama3-405b",
  info: {
    label: "Llama 3.1 405b",
    supports: {
      multiturn: true,
      tools: true,
      media: false,
      systemRole: true,
      output: ["text"]
    },
    versions: ["meta/llama3-405b-instruct-maas"]
  },
  configSchema: ModelGardenModelConfigSchema,
  version: "meta/llama3-405b-instruct-maas"
});
const SUPPORTED_OPENAI_FORMAT_MODELS = {
  "llama3-405b": llama3,
  "llama-3.1": llama31,
  "llama-3.2": llama32
};
function modelGardenOpenaiCompatibleModel(ai, name, projectId, location, googleAuth, baseUrlTemplate) {
  const model = SUPPORTED_OPENAI_FORMAT_MODELS[name];
  if (!model) throw new Error(`Unsupported model: ${name}`);
  if (!baseUrlTemplate) {
    baseUrlTemplate = "https://{location}-aiplatform.googleapis.com/v1beta1/projects/{projectId}/locations/{location}/endpoints/openapi";
  }
  const clientFactory = async (request) => {
    const requestLocation = request.config?.location || location;
    return new import_openai.default({
      baseURL: baseUrlTemplate.replace(/{location}/g, requestLocation).replace(/{projectId}/g, projectId),
      apiKey: await googleAuth.getAccessToken(),
      defaultHeaders: {
        "X-Goog-Api-Client": import_genkit.GENKIT_CLIENT_HEADER
      }
    });
  };
  return (0, import_openai_compatibility.openaiCompatibleModel)(ai, model, clientFactory);
}
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  ModelGardenModelConfigSchema,
  SUPPORTED_OPENAI_FORMAT_MODELS,
  llama3,
  llama31,
  llama32,
  modelGardenOpenaiCompatibleModel
});
//# sourceMappingURL=model_garden.js.map