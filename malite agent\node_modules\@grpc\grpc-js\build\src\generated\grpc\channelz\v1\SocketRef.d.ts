import type { Long } from '@grpc/proto-loader';
/**
 * SocketRef is a reference to a Socket.
 */
export interface SocketRef {
    /**
     * The globally unique id for this socket.  Must be a positive number.
     */
    'socket_id'?: (number | string | Long);
    /**
     * An optional name associated with the socket.
     */
    'name'?: (string);
}
/**
 * SocketRef is a reference to a Socket.
 */
export interface SocketRef__Output {
    /**
     * The globally unique id for this socket.  Must be a positive number.
     */
    'socket_id': (string);
    /**
     * An optional name associated with the socket.
     */
    'name': (string);
}
