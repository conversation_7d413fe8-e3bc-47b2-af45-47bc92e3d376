"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
var indexers_exports = {};
__export(indexers_exports, {
  vertexAiIndexerRef: () => vertexAiIndexerRef,
  vertexAiIndexers: () => vertexAiIndexers
});
module.exports = __toCommonJS(indexers_exports);
var import_retriever = require("genkit/retriever");
var import_types = require("./types");
var import_upsert_datapoints = require("./upsert_datapoints");
const vertexAiIndexerRef = (params) => {
  return (0, import_retriever.indexerRef)({
    name: `vertexai/${params.indexId}`,
    info: {
      label: params.displayName ?? `Vertex AI - ${params.indexId}`
    },
    configSchema: import_types.VertexAIVectorIndexerOptionsSchema.optional()
  });
};
function vertexAiIndexers(ai, params) {
  const vectorSearchOptions = params.pluginOptions.vectorSearchOptions;
  const indexers = [];
  if (!vectorSearchOptions || vectorSearchOptions.length === 0) {
    return indexers;
  }
  for (const vectorSearchOption of vectorSearchOptions) {
    const { documentIndexer, indexId } = vectorSearchOption;
    const embedderReference = vectorSearchOption.embedder ?? params.defaultEmbedder;
    if (!embedderReference) {
      throw new Error(
        "Embedder reference is required to define Vertex AI retriever"
      );
    }
    const embedderOptions = vectorSearchOption.embedderOptions;
    const indexer = ai.defineIndexer(
      {
        name: `vertexai/${indexId}`,
        configSchema: import_types.VertexAIVectorIndexerOptionsSchema.optional()
      },
      async (docs, options) => {
        let docIds = [];
        try {
          docIds = await documentIndexer(docs, options);
        } catch (error) {
          throw new Error(
            `Error storing your document content/metadata: ${error}`
          );
        }
        const embeddings = await ai.embedMany({
          embedder: embedderReference,
          content: docs,
          options: embedderOptions
        });
        const datapoints = embeddings.map(({ embedding }, i) => {
          const dp = new import_types.Datapoint({
            datapointId: docIds[i],
            featureVector: embedding
          });
          if (docs[i].metadata?.restricts) {
            dp.restricts = docs[i].metadata?.restricts;
          }
          if (docs[i].metadata?.numericRestricts) {
            dp.numericRestricts = docs[i].metadata?.numericRestricts;
          }
          if (docs[i].metadata?.crowdingTag) {
            dp.crowdingTag = docs[i].metadata?.crowdingTag;
          }
          return dp;
        });
        try {
          await (0, import_upsert_datapoints.upsertDatapoints)({
            datapoints,
            authClient: params.authClient,
            projectId: params.pluginOptions.projectId,
            location: params.pluginOptions.location,
            indexId
          });
        } catch (error) {
          throw error;
        }
      }
    );
    indexers.push(indexer);
  }
  return indexers;
}
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  vertexAiIndexerRef,
  vertexAiIndexers
});
//# sourceMappingURL=indexers.js.map