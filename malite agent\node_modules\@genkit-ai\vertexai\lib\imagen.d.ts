import { z, Genkit } from 'genkit';
import { ModelReference, ModelAction } from 'genkit/model';
import { GoogleAuth } from 'google-auth-library';
import { P as PluginOptions } from './types-Bc0LKM8D.js';
import '@google-cloud/vertexai';

/**
 * Copyright 2024 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

/**
 * See https://cloud.google.com/vertex-ai/generative-ai/docs/model-reference/imagen-api.
 */
declare const ImagenConfigSchema: z.ZodObject<z.objectUtil.extendShape<{
    version: z.ZodOptional<z.ZodString>;
    temperature: z.ZodOptional<z.ZodNumber>;
    maxOutputTokens: z.ZodOptional<z.ZodNumber>;
    topK: z.ZodOptional<z.ZodNumber>;
    topP: z.ZodOptional<z.ZodNumber>;
    stopSequences: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
}, {
    language: z.ZodOptional<z.ZodEnum<["auto", "en", "es", "hi", "ja", "ko", "pt", "zh-TW", "zh", "zh-CN"]>>;
    aspectRatio: z.ZodOptional<z.ZodEnum<["1:1", "9:16", "16:9", "3:4", "4:3"]>>;
    negativePrompt: z.ZodOptional<z.ZodString>;
    seed: z.ZodOptional<z.ZodNumber>;
    location: z.ZodOptional<z.ZodString>;
    personGeneration: z.ZodOptional<z.ZodEnum<["dont_allow", "allow_adult", "allow_all"]>>;
    safetySetting: z.ZodOptional<z.ZodEnum<["block_most", "block_some", "block_few", "block_fewest"]>>;
    addWatermark: z.ZodOptional<z.ZodBoolean>;
    storageUri: z.ZodOptional<z.ZodString>;
    mode: z.ZodOptional<z.ZodEnum<["upscale"]>>;
    /**
     * Describes the editing intention for the request.
     *
     * See https://cloud.google.com/vertex-ai/generative-ai/docs/model-reference/imagen-api#edit_images_2 for details.
     */
    editConfig: z.ZodOptional<z.ZodObject<{
        editMode: z.ZodOptional<z.ZodEnum<["inpainting-insert", "inpainting-remove", "outpainting", "product-image"]>>;
        maskMode: z.ZodOptional<z.ZodObject<{
            maskType: z.ZodEnum<["background", "foreground", "semantic"]>;
            classes: z.ZodOptional<z.ZodArray<z.ZodNumber, "many">>;
        }, "strip", z.ZodTypeAny, {
            maskType: "background" | "foreground" | "semantic";
            classes?: number[] | undefined;
        }, {
            maskType: "background" | "foreground" | "semantic";
            classes?: number[] | undefined;
        }>>;
        maskDilation: z.ZodOptional<z.ZodNumber>;
        guidanceScale: z.ZodOptional<z.ZodNumber>;
        productPosition: z.ZodOptional<z.ZodEnum<["reposition", "fixed"]>>;
    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
        editMode: z.ZodOptional<z.ZodEnum<["inpainting-insert", "inpainting-remove", "outpainting", "product-image"]>>;
        maskMode: z.ZodOptional<z.ZodObject<{
            maskType: z.ZodEnum<["background", "foreground", "semantic"]>;
            classes: z.ZodOptional<z.ZodArray<z.ZodNumber, "many">>;
        }, "strip", z.ZodTypeAny, {
            maskType: "background" | "foreground" | "semantic";
            classes?: number[] | undefined;
        }, {
            maskType: "background" | "foreground" | "semantic";
            classes?: number[] | undefined;
        }>>;
        maskDilation: z.ZodOptional<z.ZodNumber>;
        guidanceScale: z.ZodOptional<z.ZodNumber>;
        productPosition: z.ZodOptional<z.ZodEnum<["reposition", "fixed"]>>;
    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
        editMode: z.ZodOptional<z.ZodEnum<["inpainting-insert", "inpainting-remove", "outpainting", "product-image"]>>;
        maskMode: z.ZodOptional<z.ZodObject<{
            maskType: z.ZodEnum<["background", "foreground", "semantic"]>;
            classes: z.ZodOptional<z.ZodArray<z.ZodNumber, "many">>;
        }, "strip", z.ZodTypeAny, {
            maskType: "background" | "foreground" | "semantic";
            classes?: number[] | undefined;
        }, {
            maskType: "background" | "foreground" | "semantic";
            classes?: number[] | undefined;
        }>>;
        maskDilation: z.ZodOptional<z.ZodNumber>;
        guidanceScale: z.ZodOptional<z.ZodNumber>;
        productPosition: z.ZodOptional<z.ZodEnum<["reposition", "fixed"]>>;
    }, z.ZodTypeAny, "passthrough">>>;
    upscaleConfig: z.ZodOptional<z.ZodObject<{
        upscaleFactor: z.ZodEnum<["x2", "x4"]>;
    }, "strip", z.ZodTypeAny, {
        upscaleFactor: "x2" | "x4";
    }, {
        upscaleFactor: "x2" | "x4";
    }>>;
}>, "passthrough", z.ZodTypeAny, z.objectOutputType<z.objectUtil.extendShape<{
    version: z.ZodOptional<z.ZodString>;
    temperature: z.ZodOptional<z.ZodNumber>;
    maxOutputTokens: z.ZodOptional<z.ZodNumber>;
    topK: z.ZodOptional<z.ZodNumber>;
    topP: z.ZodOptional<z.ZodNumber>;
    stopSequences: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
}, {
    language: z.ZodOptional<z.ZodEnum<["auto", "en", "es", "hi", "ja", "ko", "pt", "zh-TW", "zh", "zh-CN"]>>;
    aspectRatio: z.ZodOptional<z.ZodEnum<["1:1", "9:16", "16:9", "3:4", "4:3"]>>;
    negativePrompt: z.ZodOptional<z.ZodString>;
    seed: z.ZodOptional<z.ZodNumber>;
    location: z.ZodOptional<z.ZodString>;
    personGeneration: z.ZodOptional<z.ZodEnum<["dont_allow", "allow_adult", "allow_all"]>>;
    safetySetting: z.ZodOptional<z.ZodEnum<["block_most", "block_some", "block_few", "block_fewest"]>>;
    addWatermark: z.ZodOptional<z.ZodBoolean>;
    storageUri: z.ZodOptional<z.ZodString>;
    mode: z.ZodOptional<z.ZodEnum<["upscale"]>>;
    /**
     * Describes the editing intention for the request.
     *
     * See https://cloud.google.com/vertex-ai/generative-ai/docs/model-reference/imagen-api#edit_images_2 for details.
     */
    editConfig: z.ZodOptional<z.ZodObject<{
        editMode: z.ZodOptional<z.ZodEnum<["inpainting-insert", "inpainting-remove", "outpainting", "product-image"]>>;
        maskMode: z.ZodOptional<z.ZodObject<{
            maskType: z.ZodEnum<["background", "foreground", "semantic"]>;
            classes: z.ZodOptional<z.ZodArray<z.ZodNumber, "many">>;
        }, "strip", z.ZodTypeAny, {
            maskType: "background" | "foreground" | "semantic";
            classes?: number[] | undefined;
        }, {
            maskType: "background" | "foreground" | "semantic";
            classes?: number[] | undefined;
        }>>;
        maskDilation: z.ZodOptional<z.ZodNumber>;
        guidanceScale: z.ZodOptional<z.ZodNumber>;
        productPosition: z.ZodOptional<z.ZodEnum<["reposition", "fixed"]>>;
    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
        editMode: z.ZodOptional<z.ZodEnum<["inpainting-insert", "inpainting-remove", "outpainting", "product-image"]>>;
        maskMode: z.ZodOptional<z.ZodObject<{
            maskType: z.ZodEnum<["background", "foreground", "semantic"]>;
            classes: z.ZodOptional<z.ZodArray<z.ZodNumber, "many">>;
        }, "strip", z.ZodTypeAny, {
            maskType: "background" | "foreground" | "semantic";
            classes?: number[] | undefined;
        }, {
            maskType: "background" | "foreground" | "semantic";
            classes?: number[] | undefined;
        }>>;
        maskDilation: z.ZodOptional<z.ZodNumber>;
        guidanceScale: z.ZodOptional<z.ZodNumber>;
        productPosition: z.ZodOptional<z.ZodEnum<["reposition", "fixed"]>>;
    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
        editMode: z.ZodOptional<z.ZodEnum<["inpainting-insert", "inpainting-remove", "outpainting", "product-image"]>>;
        maskMode: z.ZodOptional<z.ZodObject<{
            maskType: z.ZodEnum<["background", "foreground", "semantic"]>;
            classes: z.ZodOptional<z.ZodArray<z.ZodNumber, "many">>;
        }, "strip", z.ZodTypeAny, {
            maskType: "background" | "foreground" | "semantic";
            classes?: number[] | undefined;
        }, {
            maskType: "background" | "foreground" | "semantic";
            classes?: number[] | undefined;
        }>>;
        maskDilation: z.ZodOptional<z.ZodNumber>;
        guidanceScale: z.ZodOptional<z.ZodNumber>;
        productPosition: z.ZodOptional<z.ZodEnum<["reposition", "fixed"]>>;
    }, z.ZodTypeAny, "passthrough">>>;
    upscaleConfig: z.ZodOptional<z.ZodObject<{
        upscaleFactor: z.ZodEnum<["x2", "x4"]>;
    }, "strip", z.ZodTypeAny, {
        upscaleFactor: "x2" | "x4";
    }, {
        upscaleFactor: "x2" | "x4";
    }>>;
}>, z.ZodTypeAny, "passthrough">, z.objectInputType<z.objectUtil.extendShape<{
    version: z.ZodOptional<z.ZodString>;
    temperature: z.ZodOptional<z.ZodNumber>;
    maxOutputTokens: z.ZodOptional<z.ZodNumber>;
    topK: z.ZodOptional<z.ZodNumber>;
    topP: z.ZodOptional<z.ZodNumber>;
    stopSequences: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
}, {
    language: z.ZodOptional<z.ZodEnum<["auto", "en", "es", "hi", "ja", "ko", "pt", "zh-TW", "zh", "zh-CN"]>>;
    aspectRatio: z.ZodOptional<z.ZodEnum<["1:1", "9:16", "16:9", "3:4", "4:3"]>>;
    negativePrompt: z.ZodOptional<z.ZodString>;
    seed: z.ZodOptional<z.ZodNumber>;
    location: z.ZodOptional<z.ZodString>;
    personGeneration: z.ZodOptional<z.ZodEnum<["dont_allow", "allow_adult", "allow_all"]>>;
    safetySetting: z.ZodOptional<z.ZodEnum<["block_most", "block_some", "block_few", "block_fewest"]>>;
    addWatermark: z.ZodOptional<z.ZodBoolean>;
    storageUri: z.ZodOptional<z.ZodString>;
    mode: z.ZodOptional<z.ZodEnum<["upscale"]>>;
    /**
     * Describes the editing intention for the request.
     *
     * See https://cloud.google.com/vertex-ai/generative-ai/docs/model-reference/imagen-api#edit_images_2 for details.
     */
    editConfig: z.ZodOptional<z.ZodObject<{
        editMode: z.ZodOptional<z.ZodEnum<["inpainting-insert", "inpainting-remove", "outpainting", "product-image"]>>;
        maskMode: z.ZodOptional<z.ZodObject<{
            maskType: z.ZodEnum<["background", "foreground", "semantic"]>;
            classes: z.ZodOptional<z.ZodArray<z.ZodNumber, "many">>;
        }, "strip", z.ZodTypeAny, {
            maskType: "background" | "foreground" | "semantic";
            classes?: number[] | undefined;
        }, {
            maskType: "background" | "foreground" | "semantic";
            classes?: number[] | undefined;
        }>>;
        maskDilation: z.ZodOptional<z.ZodNumber>;
        guidanceScale: z.ZodOptional<z.ZodNumber>;
        productPosition: z.ZodOptional<z.ZodEnum<["reposition", "fixed"]>>;
    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
        editMode: z.ZodOptional<z.ZodEnum<["inpainting-insert", "inpainting-remove", "outpainting", "product-image"]>>;
        maskMode: z.ZodOptional<z.ZodObject<{
            maskType: z.ZodEnum<["background", "foreground", "semantic"]>;
            classes: z.ZodOptional<z.ZodArray<z.ZodNumber, "many">>;
        }, "strip", z.ZodTypeAny, {
            maskType: "background" | "foreground" | "semantic";
            classes?: number[] | undefined;
        }, {
            maskType: "background" | "foreground" | "semantic";
            classes?: number[] | undefined;
        }>>;
        maskDilation: z.ZodOptional<z.ZodNumber>;
        guidanceScale: z.ZodOptional<z.ZodNumber>;
        productPosition: z.ZodOptional<z.ZodEnum<["reposition", "fixed"]>>;
    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
        editMode: z.ZodOptional<z.ZodEnum<["inpainting-insert", "inpainting-remove", "outpainting", "product-image"]>>;
        maskMode: z.ZodOptional<z.ZodObject<{
            maskType: z.ZodEnum<["background", "foreground", "semantic"]>;
            classes: z.ZodOptional<z.ZodArray<z.ZodNumber, "many">>;
        }, "strip", z.ZodTypeAny, {
            maskType: "background" | "foreground" | "semantic";
            classes?: number[] | undefined;
        }, {
            maskType: "background" | "foreground" | "semantic";
            classes?: number[] | undefined;
        }>>;
        maskDilation: z.ZodOptional<z.ZodNumber>;
        guidanceScale: z.ZodOptional<z.ZodNumber>;
        productPosition: z.ZodOptional<z.ZodEnum<["reposition", "fixed"]>>;
    }, z.ZodTypeAny, "passthrough">>>;
    upscaleConfig: z.ZodOptional<z.ZodObject<{
        upscaleFactor: z.ZodEnum<["x2", "x4"]>;
    }, "strip", z.ZodTypeAny, {
        upscaleFactor: "x2" | "x4";
    }, {
        upscaleFactor: "x2" | "x4";
    }>>;
}>, z.ZodTypeAny, "passthrough">>;
declare const imagen2: ModelReference<z.ZodObject<z.objectUtil.extendShape<{
    version: z.ZodOptional<z.ZodString>;
    temperature: z.ZodOptional<z.ZodNumber>;
    maxOutputTokens: z.ZodOptional<z.ZodNumber>;
    topK: z.ZodOptional<z.ZodNumber>;
    topP: z.ZodOptional<z.ZodNumber>;
    stopSequences: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
}, {
    language: z.ZodOptional<z.ZodEnum<["auto", "en", "es", "hi", "ja", "ko", "pt", "zh-TW", "zh", "zh-CN"]>>;
    aspectRatio: z.ZodOptional<z.ZodEnum<["1:1", "9:16", "16:9", "3:4", "4:3"]>>;
    negativePrompt: z.ZodOptional<z.ZodString>;
    seed: z.ZodOptional<z.ZodNumber>;
    location: z.ZodOptional<z.ZodString>;
    personGeneration: z.ZodOptional<z.ZodEnum<["dont_allow", "allow_adult", "allow_all"]>>;
    safetySetting: z.ZodOptional<z.ZodEnum<["block_most", "block_some", "block_few", "block_fewest"]>>;
    addWatermark: z.ZodOptional<z.ZodBoolean>;
    storageUri: z.ZodOptional<z.ZodString>;
    mode: z.ZodOptional<z.ZodEnum<["upscale"]>>;
    /**
     * Describes the editing intention for the request.
     *
     * See https://cloud.google.com/vertex-ai/generative-ai/docs/model-reference/imagen-api#edit_images_2 for details.
     */
    editConfig: z.ZodOptional<z.ZodObject<{
        editMode: z.ZodOptional<z.ZodEnum<["inpainting-insert", "inpainting-remove", "outpainting", "product-image"]>>;
        maskMode: z.ZodOptional<z.ZodObject<{
            maskType: z.ZodEnum<["background", "foreground", "semantic"]>;
            classes: z.ZodOptional<z.ZodArray<z.ZodNumber, "many">>;
        }, "strip", z.ZodTypeAny, {
            maskType: "background" | "foreground" | "semantic";
            classes?: number[] | undefined;
        }, {
            maskType: "background" | "foreground" | "semantic";
            classes?: number[] | undefined;
        }>>;
        maskDilation: z.ZodOptional<z.ZodNumber>;
        guidanceScale: z.ZodOptional<z.ZodNumber>;
        productPosition: z.ZodOptional<z.ZodEnum<["reposition", "fixed"]>>;
    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
        editMode: z.ZodOptional<z.ZodEnum<["inpainting-insert", "inpainting-remove", "outpainting", "product-image"]>>;
        maskMode: z.ZodOptional<z.ZodObject<{
            maskType: z.ZodEnum<["background", "foreground", "semantic"]>;
            classes: z.ZodOptional<z.ZodArray<z.ZodNumber, "many">>;
        }, "strip", z.ZodTypeAny, {
            maskType: "background" | "foreground" | "semantic";
            classes?: number[] | undefined;
        }, {
            maskType: "background" | "foreground" | "semantic";
            classes?: number[] | undefined;
        }>>;
        maskDilation: z.ZodOptional<z.ZodNumber>;
        guidanceScale: z.ZodOptional<z.ZodNumber>;
        productPosition: z.ZodOptional<z.ZodEnum<["reposition", "fixed"]>>;
    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
        editMode: z.ZodOptional<z.ZodEnum<["inpainting-insert", "inpainting-remove", "outpainting", "product-image"]>>;
        maskMode: z.ZodOptional<z.ZodObject<{
            maskType: z.ZodEnum<["background", "foreground", "semantic"]>;
            classes: z.ZodOptional<z.ZodArray<z.ZodNumber, "many">>;
        }, "strip", z.ZodTypeAny, {
            maskType: "background" | "foreground" | "semantic";
            classes?: number[] | undefined;
        }, {
            maskType: "background" | "foreground" | "semantic";
            classes?: number[] | undefined;
        }>>;
        maskDilation: z.ZodOptional<z.ZodNumber>;
        guidanceScale: z.ZodOptional<z.ZodNumber>;
        productPosition: z.ZodOptional<z.ZodEnum<["reposition", "fixed"]>>;
    }, z.ZodTypeAny, "passthrough">>>;
    upscaleConfig: z.ZodOptional<z.ZodObject<{
        upscaleFactor: z.ZodEnum<["x2", "x4"]>;
    }, "strip", z.ZodTypeAny, {
        upscaleFactor: "x2" | "x4";
    }, {
        upscaleFactor: "x2" | "x4";
    }>>;
}>, "passthrough", z.ZodTypeAny, z.objectOutputType<z.objectUtil.extendShape<{
    version: z.ZodOptional<z.ZodString>;
    temperature: z.ZodOptional<z.ZodNumber>;
    maxOutputTokens: z.ZodOptional<z.ZodNumber>;
    topK: z.ZodOptional<z.ZodNumber>;
    topP: z.ZodOptional<z.ZodNumber>;
    stopSequences: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
}, {
    language: z.ZodOptional<z.ZodEnum<["auto", "en", "es", "hi", "ja", "ko", "pt", "zh-TW", "zh", "zh-CN"]>>;
    aspectRatio: z.ZodOptional<z.ZodEnum<["1:1", "9:16", "16:9", "3:4", "4:3"]>>;
    negativePrompt: z.ZodOptional<z.ZodString>;
    seed: z.ZodOptional<z.ZodNumber>;
    location: z.ZodOptional<z.ZodString>;
    personGeneration: z.ZodOptional<z.ZodEnum<["dont_allow", "allow_adult", "allow_all"]>>;
    safetySetting: z.ZodOptional<z.ZodEnum<["block_most", "block_some", "block_few", "block_fewest"]>>;
    addWatermark: z.ZodOptional<z.ZodBoolean>;
    storageUri: z.ZodOptional<z.ZodString>;
    mode: z.ZodOptional<z.ZodEnum<["upscale"]>>;
    /**
     * Describes the editing intention for the request.
     *
     * See https://cloud.google.com/vertex-ai/generative-ai/docs/model-reference/imagen-api#edit_images_2 for details.
     */
    editConfig: z.ZodOptional<z.ZodObject<{
        editMode: z.ZodOptional<z.ZodEnum<["inpainting-insert", "inpainting-remove", "outpainting", "product-image"]>>;
        maskMode: z.ZodOptional<z.ZodObject<{
            maskType: z.ZodEnum<["background", "foreground", "semantic"]>;
            classes: z.ZodOptional<z.ZodArray<z.ZodNumber, "many">>;
        }, "strip", z.ZodTypeAny, {
            maskType: "background" | "foreground" | "semantic";
            classes?: number[] | undefined;
        }, {
            maskType: "background" | "foreground" | "semantic";
            classes?: number[] | undefined;
        }>>;
        maskDilation: z.ZodOptional<z.ZodNumber>;
        guidanceScale: z.ZodOptional<z.ZodNumber>;
        productPosition: z.ZodOptional<z.ZodEnum<["reposition", "fixed"]>>;
    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
        editMode: z.ZodOptional<z.ZodEnum<["inpainting-insert", "inpainting-remove", "outpainting", "product-image"]>>;
        maskMode: z.ZodOptional<z.ZodObject<{
            maskType: z.ZodEnum<["background", "foreground", "semantic"]>;
            classes: z.ZodOptional<z.ZodArray<z.ZodNumber, "many">>;
        }, "strip", z.ZodTypeAny, {
            maskType: "background" | "foreground" | "semantic";
            classes?: number[] | undefined;
        }, {
            maskType: "background" | "foreground" | "semantic";
            classes?: number[] | undefined;
        }>>;
        maskDilation: z.ZodOptional<z.ZodNumber>;
        guidanceScale: z.ZodOptional<z.ZodNumber>;
        productPosition: z.ZodOptional<z.ZodEnum<["reposition", "fixed"]>>;
    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
        editMode: z.ZodOptional<z.ZodEnum<["inpainting-insert", "inpainting-remove", "outpainting", "product-image"]>>;
        maskMode: z.ZodOptional<z.ZodObject<{
            maskType: z.ZodEnum<["background", "foreground", "semantic"]>;
            classes: z.ZodOptional<z.ZodArray<z.ZodNumber, "many">>;
        }, "strip", z.ZodTypeAny, {
            maskType: "background" | "foreground" | "semantic";
            classes?: number[] | undefined;
        }, {
            maskType: "background" | "foreground" | "semantic";
            classes?: number[] | undefined;
        }>>;
        maskDilation: z.ZodOptional<z.ZodNumber>;
        guidanceScale: z.ZodOptional<z.ZodNumber>;
        productPosition: z.ZodOptional<z.ZodEnum<["reposition", "fixed"]>>;
    }, z.ZodTypeAny, "passthrough">>>;
    upscaleConfig: z.ZodOptional<z.ZodObject<{
        upscaleFactor: z.ZodEnum<["x2", "x4"]>;
    }, "strip", z.ZodTypeAny, {
        upscaleFactor: "x2" | "x4";
    }, {
        upscaleFactor: "x2" | "x4";
    }>>;
}>, z.ZodTypeAny, "passthrough">, z.objectInputType<z.objectUtil.extendShape<{
    version: z.ZodOptional<z.ZodString>;
    temperature: z.ZodOptional<z.ZodNumber>;
    maxOutputTokens: z.ZodOptional<z.ZodNumber>;
    topK: z.ZodOptional<z.ZodNumber>;
    topP: z.ZodOptional<z.ZodNumber>;
    stopSequences: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
}, {
    language: z.ZodOptional<z.ZodEnum<["auto", "en", "es", "hi", "ja", "ko", "pt", "zh-TW", "zh", "zh-CN"]>>;
    aspectRatio: z.ZodOptional<z.ZodEnum<["1:1", "9:16", "16:9", "3:4", "4:3"]>>;
    negativePrompt: z.ZodOptional<z.ZodString>;
    seed: z.ZodOptional<z.ZodNumber>;
    location: z.ZodOptional<z.ZodString>;
    personGeneration: z.ZodOptional<z.ZodEnum<["dont_allow", "allow_adult", "allow_all"]>>;
    safetySetting: z.ZodOptional<z.ZodEnum<["block_most", "block_some", "block_few", "block_fewest"]>>;
    addWatermark: z.ZodOptional<z.ZodBoolean>;
    storageUri: z.ZodOptional<z.ZodString>;
    mode: z.ZodOptional<z.ZodEnum<["upscale"]>>;
    /**
     * Describes the editing intention for the request.
     *
     * See https://cloud.google.com/vertex-ai/generative-ai/docs/model-reference/imagen-api#edit_images_2 for details.
     */
    editConfig: z.ZodOptional<z.ZodObject<{
        editMode: z.ZodOptional<z.ZodEnum<["inpainting-insert", "inpainting-remove", "outpainting", "product-image"]>>;
        maskMode: z.ZodOptional<z.ZodObject<{
            maskType: z.ZodEnum<["background", "foreground", "semantic"]>;
            classes: z.ZodOptional<z.ZodArray<z.ZodNumber, "many">>;
        }, "strip", z.ZodTypeAny, {
            maskType: "background" | "foreground" | "semantic";
            classes?: number[] | undefined;
        }, {
            maskType: "background" | "foreground" | "semantic";
            classes?: number[] | undefined;
        }>>;
        maskDilation: z.ZodOptional<z.ZodNumber>;
        guidanceScale: z.ZodOptional<z.ZodNumber>;
        productPosition: z.ZodOptional<z.ZodEnum<["reposition", "fixed"]>>;
    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
        editMode: z.ZodOptional<z.ZodEnum<["inpainting-insert", "inpainting-remove", "outpainting", "product-image"]>>;
        maskMode: z.ZodOptional<z.ZodObject<{
            maskType: z.ZodEnum<["background", "foreground", "semantic"]>;
            classes: z.ZodOptional<z.ZodArray<z.ZodNumber, "many">>;
        }, "strip", z.ZodTypeAny, {
            maskType: "background" | "foreground" | "semantic";
            classes?: number[] | undefined;
        }, {
            maskType: "background" | "foreground" | "semantic";
            classes?: number[] | undefined;
        }>>;
        maskDilation: z.ZodOptional<z.ZodNumber>;
        guidanceScale: z.ZodOptional<z.ZodNumber>;
        productPosition: z.ZodOptional<z.ZodEnum<["reposition", "fixed"]>>;
    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
        editMode: z.ZodOptional<z.ZodEnum<["inpainting-insert", "inpainting-remove", "outpainting", "product-image"]>>;
        maskMode: z.ZodOptional<z.ZodObject<{
            maskType: z.ZodEnum<["background", "foreground", "semantic"]>;
            classes: z.ZodOptional<z.ZodArray<z.ZodNumber, "many">>;
        }, "strip", z.ZodTypeAny, {
            maskType: "background" | "foreground" | "semantic";
            classes?: number[] | undefined;
        }, {
            maskType: "background" | "foreground" | "semantic";
            classes?: number[] | undefined;
        }>>;
        maskDilation: z.ZodOptional<z.ZodNumber>;
        guidanceScale: z.ZodOptional<z.ZodNumber>;
        productPosition: z.ZodOptional<z.ZodEnum<["reposition", "fixed"]>>;
    }, z.ZodTypeAny, "passthrough">>>;
    upscaleConfig: z.ZodOptional<z.ZodObject<{
        upscaleFactor: z.ZodEnum<["x2", "x4"]>;
    }, "strip", z.ZodTypeAny, {
        upscaleFactor: "x2" | "x4";
    }, {
        upscaleFactor: "x2" | "x4";
    }>>;
}>, z.ZodTypeAny, "passthrough">>>;
declare const imagen3: ModelReference<z.ZodObject<z.objectUtil.extendShape<{
    version: z.ZodOptional<z.ZodString>;
    temperature: z.ZodOptional<z.ZodNumber>;
    maxOutputTokens: z.ZodOptional<z.ZodNumber>;
    topK: z.ZodOptional<z.ZodNumber>;
    topP: z.ZodOptional<z.ZodNumber>;
    stopSequences: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
}, {
    language: z.ZodOptional<z.ZodEnum<["auto", "en", "es", "hi", "ja", "ko", "pt", "zh-TW", "zh", "zh-CN"]>>;
    aspectRatio: z.ZodOptional<z.ZodEnum<["1:1", "9:16", "16:9", "3:4", "4:3"]>>;
    negativePrompt: z.ZodOptional<z.ZodString>;
    seed: z.ZodOptional<z.ZodNumber>;
    location: z.ZodOptional<z.ZodString>;
    personGeneration: z.ZodOptional<z.ZodEnum<["dont_allow", "allow_adult", "allow_all"]>>;
    safetySetting: z.ZodOptional<z.ZodEnum<["block_most", "block_some", "block_few", "block_fewest"]>>;
    addWatermark: z.ZodOptional<z.ZodBoolean>;
    storageUri: z.ZodOptional<z.ZodString>;
    mode: z.ZodOptional<z.ZodEnum<["upscale"]>>;
    /**
     * Describes the editing intention for the request.
     *
     * See https://cloud.google.com/vertex-ai/generative-ai/docs/model-reference/imagen-api#edit_images_2 for details.
     */
    editConfig: z.ZodOptional<z.ZodObject<{
        editMode: z.ZodOptional<z.ZodEnum<["inpainting-insert", "inpainting-remove", "outpainting", "product-image"]>>;
        maskMode: z.ZodOptional<z.ZodObject<{
            maskType: z.ZodEnum<["background", "foreground", "semantic"]>;
            classes: z.ZodOptional<z.ZodArray<z.ZodNumber, "many">>;
        }, "strip", z.ZodTypeAny, {
            maskType: "background" | "foreground" | "semantic";
            classes?: number[] | undefined;
        }, {
            maskType: "background" | "foreground" | "semantic";
            classes?: number[] | undefined;
        }>>;
        maskDilation: z.ZodOptional<z.ZodNumber>;
        guidanceScale: z.ZodOptional<z.ZodNumber>;
        productPosition: z.ZodOptional<z.ZodEnum<["reposition", "fixed"]>>;
    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
        editMode: z.ZodOptional<z.ZodEnum<["inpainting-insert", "inpainting-remove", "outpainting", "product-image"]>>;
        maskMode: z.ZodOptional<z.ZodObject<{
            maskType: z.ZodEnum<["background", "foreground", "semantic"]>;
            classes: z.ZodOptional<z.ZodArray<z.ZodNumber, "many">>;
        }, "strip", z.ZodTypeAny, {
            maskType: "background" | "foreground" | "semantic";
            classes?: number[] | undefined;
        }, {
            maskType: "background" | "foreground" | "semantic";
            classes?: number[] | undefined;
        }>>;
        maskDilation: z.ZodOptional<z.ZodNumber>;
        guidanceScale: z.ZodOptional<z.ZodNumber>;
        productPosition: z.ZodOptional<z.ZodEnum<["reposition", "fixed"]>>;
    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
        editMode: z.ZodOptional<z.ZodEnum<["inpainting-insert", "inpainting-remove", "outpainting", "product-image"]>>;
        maskMode: z.ZodOptional<z.ZodObject<{
            maskType: z.ZodEnum<["background", "foreground", "semantic"]>;
            classes: z.ZodOptional<z.ZodArray<z.ZodNumber, "many">>;
        }, "strip", z.ZodTypeAny, {
            maskType: "background" | "foreground" | "semantic";
            classes?: number[] | undefined;
        }, {
            maskType: "background" | "foreground" | "semantic";
            classes?: number[] | undefined;
        }>>;
        maskDilation: z.ZodOptional<z.ZodNumber>;
        guidanceScale: z.ZodOptional<z.ZodNumber>;
        productPosition: z.ZodOptional<z.ZodEnum<["reposition", "fixed"]>>;
    }, z.ZodTypeAny, "passthrough">>>;
    upscaleConfig: z.ZodOptional<z.ZodObject<{
        upscaleFactor: z.ZodEnum<["x2", "x4"]>;
    }, "strip", z.ZodTypeAny, {
        upscaleFactor: "x2" | "x4";
    }, {
        upscaleFactor: "x2" | "x4";
    }>>;
}>, "passthrough", z.ZodTypeAny, z.objectOutputType<z.objectUtil.extendShape<{
    version: z.ZodOptional<z.ZodString>;
    temperature: z.ZodOptional<z.ZodNumber>;
    maxOutputTokens: z.ZodOptional<z.ZodNumber>;
    topK: z.ZodOptional<z.ZodNumber>;
    topP: z.ZodOptional<z.ZodNumber>;
    stopSequences: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
}, {
    language: z.ZodOptional<z.ZodEnum<["auto", "en", "es", "hi", "ja", "ko", "pt", "zh-TW", "zh", "zh-CN"]>>;
    aspectRatio: z.ZodOptional<z.ZodEnum<["1:1", "9:16", "16:9", "3:4", "4:3"]>>;
    negativePrompt: z.ZodOptional<z.ZodString>;
    seed: z.ZodOptional<z.ZodNumber>;
    location: z.ZodOptional<z.ZodString>;
    personGeneration: z.ZodOptional<z.ZodEnum<["dont_allow", "allow_adult", "allow_all"]>>;
    safetySetting: z.ZodOptional<z.ZodEnum<["block_most", "block_some", "block_few", "block_fewest"]>>;
    addWatermark: z.ZodOptional<z.ZodBoolean>;
    storageUri: z.ZodOptional<z.ZodString>;
    mode: z.ZodOptional<z.ZodEnum<["upscale"]>>;
    /**
     * Describes the editing intention for the request.
     *
     * See https://cloud.google.com/vertex-ai/generative-ai/docs/model-reference/imagen-api#edit_images_2 for details.
     */
    editConfig: z.ZodOptional<z.ZodObject<{
        editMode: z.ZodOptional<z.ZodEnum<["inpainting-insert", "inpainting-remove", "outpainting", "product-image"]>>;
        maskMode: z.ZodOptional<z.ZodObject<{
            maskType: z.ZodEnum<["background", "foreground", "semantic"]>;
            classes: z.ZodOptional<z.ZodArray<z.ZodNumber, "many">>;
        }, "strip", z.ZodTypeAny, {
            maskType: "background" | "foreground" | "semantic";
            classes?: number[] | undefined;
        }, {
            maskType: "background" | "foreground" | "semantic";
            classes?: number[] | undefined;
        }>>;
        maskDilation: z.ZodOptional<z.ZodNumber>;
        guidanceScale: z.ZodOptional<z.ZodNumber>;
        productPosition: z.ZodOptional<z.ZodEnum<["reposition", "fixed"]>>;
    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
        editMode: z.ZodOptional<z.ZodEnum<["inpainting-insert", "inpainting-remove", "outpainting", "product-image"]>>;
        maskMode: z.ZodOptional<z.ZodObject<{
            maskType: z.ZodEnum<["background", "foreground", "semantic"]>;
            classes: z.ZodOptional<z.ZodArray<z.ZodNumber, "many">>;
        }, "strip", z.ZodTypeAny, {
            maskType: "background" | "foreground" | "semantic";
            classes?: number[] | undefined;
        }, {
            maskType: "background" | "foreground" | "semantic";
            classes?: number[] | undefined;
        }>>;
        maskDilation: z.ZodOptional<z.ZodNumber>;
        guidanceScale: z.ZodOptional<z.ZodNumber>;
        productPosition: z.ZodOptional<z.ZodEnum<["reposition", "fixed"]>>;
    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
        editMode: z.ZodOptional<z.ZodEnum<["inpainting-insert", "inpainting-remove", "outpainting", "product-image"]>>;
        maskMode: z.ZodOptional<z.ZodObject<{
            maskType: z.ZodEnum<["background", "foreground", "semantic"]>;
            classes: z.ZodOptional<z.ZodArray<z.ZodNumber, "many">>;
        }, "strip", z.ZodTypeAny, {
            maskType: "background" | "foreground" | "semantic";
            classes?: number[] | undefined;
        }, {
            maskType: "background" | "foreground" | "semantic";
            classes?: number[] | undefined;
        }>>;
        maskDilation: z.ZodOptional<z.ZodNumber>;
        guidanceScale: z.ZodOptional<z.ZodNumber>;
        productPosition: z.ZodOptional<z.ZodEnum<["reposition", "fixed"]>>;
    }, z.ZodTypeAny, "passthrough">>>;
    upscaleConfig: z.ZodOptional<z.ZodObject<{
        upscaleFactor: z.ZodEnum<["x2", "x4"]>;
    }, "strip", z.ZodTypeAny, {
        upscaleFactor: "x2" | "x4";
    }, {
        upscaleFactor: "x2" | "x4";
    }>>;
}>, z.ZodTypeAny, "passthrough">, z.objectInputType<z.objectUtil.extendShape<{
    version: z.ZodOptional<z.ZodString>;
    temperature: z.ZodOptional<z.ZodNumber>;
    maxOutputTokens: z.ZodOptional<z.ZodNumber>;
    topK: z.ZodOptional<z.ZodNumber>;
    topP: z.ZodOptional<z.ZodNumber>;
    stopSequences: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
}, {
    language: z.ZodOptional<z.ZodEnum<["auto", "en", "es", "hi", "ja", "ko", "pt", "zh-TW", "zh", "zh-CN"]>>;
    aspectRatio: z.ZodOptional<z.ZodEnum<["1:1", "9:16", "16:9", "3:4", "4:3"]>>;
    negativePrompt: z.ZodOptional<z.ZodString>;
    seed: z.ZodOptional<z.ZodNumber>;
    location: z.ZodOptional<z.ZodString>;
    personGeneration: z.ZodOptional<z.ZodEnum<["dont_allow", "allow_adult", "allow_all"]>>;
    safetySetting: z.ZodOptional<z.ZodEnum<["block_most", "block_some", "block_few", "block_fewest"]>>;
    addWatermark: z.ZodOptional<z.ZodBoolean>;
    storageUri: z.ZodOptional<z.ZodString>;
    mode: z.ZodOptional<z.ZodEnum<["upscale"]>>;
    /**
     * Describes the editing intention for the request.
     *
     * See https://cloud.google.com/vertex-ai/generative-ai/docs/model-reference/imagen-api#edit_images_2 for details.
     */
    editConfig: z.ZodOptional<z.ZodObject<{
        editMode: z.ZodOptional<z.ZodEnum<["inpainting-insert", "inpainting-remove", "outpainting", "product-image"]>>;
        maskMode: z.ZodOptional<z.ZodObject<{
            maskType: z.ZodEnum<["background", "foreground", "semantic"]>;
            classes: z.ZodOptional<z.ZodArray<z.ZodNumber, "many">>;
        }, "strip", z.ZodTypeAny, {
            maskType: "background" | "foreground" | "semantic";
            classes?: number[] | undefined;
        }, {
            maskType: "background" | "foreground" | "semantic";
            classes?: number[] | undefined;
        }>>;
        maskDilation: z.ZodOptional<z.ZodNumber>;
        guidanceScale: z.ZodOptional<z.ZodNumber>;
        productPosition: z.ZodOptional<z.ZodEnum<["reposition", "fixed"]>>;
    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
        editMode: z.ZodOptional<z.ZodEnum<["inpainting-insert", "inpainting-remove", "outpainting", "product-image"]>>;
        maskMode: z.ZodOptional<z.ZodObject<{
            maskType: z.ZodEnum<["background", "foreground", "semantic"]>;
            classes: z.ZodOptional<z.ZodArray<z.ZodNumber, "many">>;
        }, "strip", z.ZodTypeAny, {
            maskType: "background" | "foreground" | "semantic";
            classes?: number[] | undefined;
        }, {
            maskType: "background" | "foreground" | "semantic";
            classes?: number[] | undefined;
        }>>;
        maskDilation: z.ZodOptional<z.ZodNumber>;
        guidanceScale: z.ZodOptional<z.ZodNumber>;
        productPosition: z.ZodOptional<z.ZodEnum<["reposition", "fixed"]>>;
    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
        editMode: z.ZodOptional<z.ZodEnum<["inpainting-insert", "inpainting-remove", "outpainting", "product-image"]>>;
        maskMode: z.ZodOptional<z.ZodObject<{
            maskType: z.ZodEnum<["background", "foreground", "semantic"]>;
            classes: z.ZodOptional<z.ZodArray<z.ZodNumber, "many">>;
        }, "strip", z.ZodTypeAny, {
            maskType: "background" | "foreground" | "semantic";
            classes?: number[] | undefined;
        }, {
            maskType: "background" | "foreground" | "semantic";
            classes?: number[] | undefined;
        }>>;
        maskDilation: z.ZodOptional<z.ZodNumber>;
        guidanceScale: z.ZodOptional<z.ZodNumber>;
        productPosition: z.ZodOptional<z.ZodEnum<["reposition", "fixed"]>>;
    }, z.ZodTypeAny, "passthrough">>>;
    upscaleConfig: z.ZodOptional<z.ZodObject<{
        upscaleFactor: z.ZodEnum<["x2", "x4"]>;
    }, "strip", z.ZodTypeAny, {
        upscaleFactor: "x2" | "x4";
    }, {
        upscaleFactor: "x2" | "x4";
    }>>;
}>, z.ZodTypeAny, "passthrough">>>;
declare const imagen3Fast: ModelReference<z.ZodObject<z.objectUtil.extendShape<{
    version: z.ZodOptional<z.ZodString>;
    temperature: z.ZodOptional<z.ZodNumber>;
    maxOutputTokens: z.ZodOptional<z.ZodNumber>;
    topK: z.ZodOptional<z.ZodNumber>;
    topP: z.ZodOptional<z.ZodNumber>;
    stopSequences: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
}, {
    language: z.ZodOptional<z.ZodEnum<["auto", "en", "es", "hi", "ja", "ko", "pt", "zh-TW", "zh", "zh-CN"]>>;
    aspectRatio: z.ZodOptional<z.ZodEnum<["1:1", "9:16", "16:9", "3:4", "4:3"]>>;
    negativePrompt: z.ZodOptional<z.ZodString>;
    seed: z.ZodOptional<z.ZodNumber>;
    location: z.ZodOptional<z.ZodString>;
    personGeneration: z.ZodOptional<z.ZodEnum<["dont_allow", "allow_adult", "allow_all"]>>;
    safetySetting: z.ZodOptional<z.ZodEnum<["block_most", "block_some", "block_few", "block_fewest"]>>;
    addWatermark: z.ZodOptional<z.ZodBoolean>;
    storageUri: z.ZodOptional<z.ZodString>;
    mode: z.ZodOptional<z.ZodEnum<["upscale"]>>;
    /**
     * Describes the editing intention for the request.
     *
     * See https://cloud.google.com/vertex-ai/generative-ai/docs/model-reference/imagen-api#edit_images_2 for details.
     */
    editConfig: z.ZodOptional<z.ZodObject<{
        editMode: z.ZodOptional<z.ZodEnum<["inpainting-insert", "inpainting-remove", "outpainting", "product-image"]>>;
        maskMode: z.ZodOptional<z.ZodObject<{
            maskType: z.ZodEnum<["background", "foreground", "semantic"]>;
            classes: z.ZodOptional<z.ZodArray<z.ZodNumber, "many">>;
        }, "strip", z.ZodTypeAny, {
            maskType: "background" | "foreground" | "semantic";
            classes?: number[] | undefined;
        }, {
            maskType: "background" | "foreground" | "semantic";
            classes?: number[] | undefined;
        }>>;
        maskDilation: z.ZodOptional<z.ZodNumber>;
        guidanceScale: z.ZodOptional<z.ZodNumber>;
        productPosition: z.ZodOptional<z.ZodEnum<["reposition", "fixed"]>>;
    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
        editMode: z.ZodOptional<z.ZodEnum<["inpainting-insert", "inpainting-remove", "outpainting", "product-image"]>>;
        maskMode: z.ZodOptional<z.ZodObject<{
            maskType: z.ZodEnum<["background", "foreground", "semantic"]>;
            classes: z.ZodOptional<z.ZodArray<z.ZodNumber, "many">>;
        }, "strip", z.ZodTypeAny, {
            maskType: "background" | "foreground" | "semantic";
            classes?: number[] | undefined;
        }, {
            maskType: "background" | "foreground" | "semantic";
            classes?: number[] | undefined;
        }>>;
        maskDilation: z.ZodOptional<z.ZodNumber>;
        guidanceScale: z.ZodOptional<z.ZodNumber>;
        productPosition: z.ZodOptional<z.ZodEnum<["reposition", "fixed"]>>;
    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
        editMode: z.ZodOptional<z.ZodEnum<["inpainting-insert", "inpainting-remove", "outpainting", "product-image"]>>;
        maskMode: z.ZodOptional<z.ZodObject<{
            maskType: z.ZodEnum<["background", "foreground", "semantic"]>;
            classes: z.ZodOptional<z.ZodArray<z.ZodNumber, "many">>;
        }, "strip", z.ZodTypeAny, {
            maskType: "background" | "foreground" | "semantic";
            classes?: number[] | undefined;
        }, {
            maskType: "background" | "foreground" | "semantic";
            classes?: number[] | undefined;
        }>>;
        maskDilation: z.ZodOptional<z.ZodNumber>;
        guidanceScale: z.ZodOptional<z.ZodNumber>;
        productPosition: z.ZodOptional<z.ZodEnum<["reposition", "fixed"]>>;
    }, z.ZodTypeAny, "passthrough">>>;
    upscaleConfig: z.ZodOptional<z.ZodObject<{
        upscaleFactor: z.ZodEnum<["x2", "x4"]>;
    }, "strip", z.ZodTypeAny, {
        upscaleFactor: "x2" | "x4";
    }, {
        upscaleFactor: "x2" | "x4";
    }>>;
}>, "passthrough", z.ZodTypeAny, z.objectOutputType<z.objectUtil.extendShape<{
    version: z.ZodOptional<z.ZodString>;
    temperature: z.ZodOptional<z.ZodNumber>;
    maxOutputTokens: z.ZodOptional<z.ZodNumber>;
    topK: z.ZodOptional<z.ZodNumber>;
    topP: z.ZodOptional<z.ZodNumber>;
    stopSequences: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
}, {
    language: z.ZodOptional<z.ZodEnum<["auto", "en", "es", "hi", "ja", "ko", "pt", "zh-TW", "zh", "zh-CN"]>>;
    aspectRatio: z.ZodOptional<z.ZodEnum<["1:1", "9:16", "16:9", "3:4", "4:3"]>>;
    negativePrompt: z.ZodOptional<z.ZodString>;
    seed: z.ZodOptional<z.ZodNumber>;
    location: z.ZodOptional<z.ZodString>;
    personGeneration: z.ZodOptional<z.ZodEnum<["dont_allow", "allow_adult", "allow_all"]>>;
    safetySetting: z.ZodOptional<z.ZodEnum<["block_most", "block_some", "block_few", "block_fewest"]>>;
    addWatermark: z.ZodOptional<z.ZodBoolean>;
    storageUri: z.ZodOptional<z.ZodString>;
    mode: z.ZodOptional<z.ZodEnum<["upscale"]>>;
    /**
     * Describes the editing intention for the request.
     *
     * See https://cloud.google.com/vertex-ai/generative-ai/docs/model-reference/imagen-api#edit_images_2 for details.
     */
    editConfig: z.ZodOptional<z.ZodObject<{
        editMode: z.ZodOptional<z.ZodEnum<["inpainting-insert", "inpainting-remove", "outpainting", "product-image"]>>;
        maskMode: z.ZodOptional<z.ZodObject<{
            maskType: z.ZodEnum<["background", "foreground", "semantic"]>;
            classes: z.ZodOptional<z.ZodArray<z.ZodNumber, "many">>;
        }, "strip", z.ZodTypeAny, {
            maskType: "background" | "foreground" | "semantic";
            classes?: number[] | undefined;
        }, {
            maskType: "background" | "foreground" | "semantic";
            classes?: number[] | undefined;
        }>>;
        maskDilation: z.ZodOptional<z.ZodNumber>;
        guidanceScale: z.ZodOptional<z.ZodNumber>;
        productPosition: z.ZodOptional<z.ZodEnum<["reposition", "fixed"]>>;
    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
        editMode: z.ZodOptional<z.ZodEnum<["inpainting-insert", "inpainting-remove", "outpainting", "product-image"]>>;
        maskMode: z.ZodOptional<z.ZodObject<{
            maskType: z.ZodEnum<["background", "foreground", "semantic"]>;
            classes: z.ZodOptional<z.ZodArray<z.ZodNumber, "many">>;
        }, "strip", z.ZodTypeAny, {
            maskType: "background" | "foreground" | "semantic";
            classes?: number[] | undefined;
        }, {
            maskType: "background" | "foreground" | "semantic";
            classes?: number[] | undefined;
        }>>;
        maskDilation: z.ZodOptional<z.ZodNumber>;
        guidanceScale: z.ZodOptional<z.ZodNumber>;
        productPosition: z.ZodOptional<z.ZodEnum<["reposition", "fixed"]>>;
    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
        editMode: z.ZodOptional<z.ZodEnum<["inpainting-insert", "inpainting-remove", "outpainting", "product-image"]>>;
        maskMode: z.ZodOptional<z.ZodObject<{
            maskType: z.ZodEnum<["background", "foreground", "semantic"]>;
            classes: z.ZodOptional<z.ZodArray<z.ZodNumber, "many">>;
        }, "strip", z.ZodTypeAny, {
            maskType: "background" | "foreground" | "semantic";
            classes?: number[] | undefined;
        }, {
            maskType: "background" | "foreground" | "semantic";
            classes?: number[] | undefined;
        }>>;
        maskDilation: z.ZodOptional<z.ZodNumber>;
        guidanceScale: z.ZodOptional<z.ZodNumber>;
        productPosition: z.ZodOptional<z.ZodEnum<["reposition", "fixed"]>>;
    }, z.ZodTypeAny, "passthrough">>>;
    upscaleConfig: z.ZodOptional<z.ZodObject<{
        upscaleFactor: z.ZodEnum<["x2", "x4"]>;
    }, "strip", z.ZodTypeAny, {
        upscaleFactor: "x2" | "x4";
    }, {
        upscaleFactor: "x2" | "x4";
    }>>;
}>, z.ZodTypeAny, "passthrough">, z.objectInputType<z.objectUtil.extendShape<{
    version: z.ZodOptional<z.ZodString>;
    temperature: z.ZodOptional<z.ZodNumber>;
    maxOutputTokens: z.ZodOptional<z.ZodNumber>;
    topK: z.ZodOptional<z.ZodNumber>;
    topP: z.ZodOptional<z.ZodNumber>;
    stopSequences: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
}, {
    language: z.ZodOptional<z.ZodEnum<["auto", "en", "es", "hi", "ja", "ko", "pt", "zh-TW", "zh", "zh-CN"]>>;
    aspectRatio: z.ZodOptional<z.ZodEnum<["1:1", "9:16", "16:9", "3:4", "4:3"]>>;
    negativePrompt: z.ZodOptional<z.ZodString>;
    seed: z.ZodOptional<z.ZodNumber>;
    location: z.ZodOptional<z.ZodString>;
    personGeneration: z.ZodOptional<z.ZodEnum<["dont_allow", "allow_adult", "allow_all"]>>;
    safetySetting: z.ZodOptional<z.ZodEnum<["block_most", "block_some", "block_few", "block_fewest"]>>;
    addWatermark: z.ZodOptional<z.ZodBoolean>;
    storageUri: z.ZodOptional<z.ZodString>;
    mode: z.ZodOptional<z.ZodEnum<["upscale"]>>;
    /**
     * Describes the editing intention for the request.
     *
     * See https://cloud.google.com/vertex-ai/generative-ai/docs/model-reference/imagen-api#edit_images_2 for details.
     */
    editConfig: z.ZodOptional<z.ZodObject<{
        editMode: z.ZodOptional<z.ZodEnum<["inpainting-insert", "inpainting-remove", "outpainting", "product-image"]>>;
        maskMode: z.ZodOptional<z.ZodObject<{
            maskType: z.ZodEnum<["background", "foreground", "semantic"]>;
            classes: z.ZodOptional<z.ZodArray<z.ZodNumber, "many">>;
        }, "strip", z.ZodTypeAny, {
            maskType: "background" | "foreground" | "semantic";
            classes?: number[] | undefined;
        }, {
            maskType: "background" | "foreground" | "semantic";
            classes?: number[] | undefined;
        }>>;
        maskDilation: z.ZodOptional<z.ZodNumber>;
        guidanceScale: z.ZodOptional<z.ZodNumber>;
        productPosition: z.ZodOptional<z.ZodEnum<["reposition", "fixed"]>>;
    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
        editMode: z.ZodOptional<z.ZodEnum<["inpainting-insert", "inpainting-remove", "outpainting", "product-image"]>>;
        maskMode: z.ZodOptional<z.ZodObject<{
            maskType: z.ZodEnum<["background", "foreground", "semantic"]>;
            classes: z.ZodOptional<z.ZodArray<z.ZodNumber, "many">>;
        }, "strip", z.ZodTypeAny, {
            maskType: "background" | "foreground" | "semantic";
            classes?: number[] | undefined;
        }, {
            maskType: "background" | "foreground" | "semantic";
            classes?: number[] | undefined;
        }>>;
        maskDilation: z.ZodOptional<z.ZodNumber>;
        guidanceScale: z.ZodOptional<z.ZodNumber>;
        productPosition: z.ZodOptional<z.ZodEnum<["reposition", "fixed"]>>;
    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
        editMode: z.ZodOptional<z.ZodEnum<["inpainting-insert", "inpainting-remove", "outpainting", "product-image"]>>;
        maskMode: z.ZodOptional<z.ZodObject<{
            maskType: z.ZodEnum<["background", "foreground", "semantic"]>;
            classes: z.ZodOptional<z.ZodArray<z.ZodNumber, "many">>;
        }, "strip", z.ZodTypeAny, {
            maskType: "background" | "foreground" | "semantic";
            classes?: number[] | undefined;
        }, {
            maskType: "background" | "foreground" | "semantic";
            classes?: number[] | undefined;
        }>>;
        maskDilation: z.ZodOptional<z.ZodNumber>;
        guidanceScale: z.ZodOptional<z.ZodNumber>;
        productPosition: z.ZodOptional<z.ZodEnum<["reposition", "fixed"]>>;
    }, z.ZodTypeAny, "passthrough">>>;
    upscaleConfig: z.ZodOptional<z.ZodObject<{
        upscaleFactor: z.ZodEnum<["x2", "x4"]>;
    }, "strip", z.ZodTypeAny, {
        upscaleFactor: "x2" | "x4";
    }, {
        upscaleFactor: "x2" | "x4";
    }>>;
}>, z.ZodTypeAny, "passthrough">>>;
declare const ACTUAL_IMAGEN_MODELS: {
    readonly 'imagen-3.0-generate-001': ModelReference<z.ZodObject<z.objectUtil.extendShape<{
        version: z.ZodOptional<z.ZodString>;
        temperature: z.ZodOptional<z.ZodNumber>;
        maxOutputTokens: z.ZodOptional<z.ZodNumber>;
        topK: z.ZodOptional<z.ZodNumber>;
        topP: z.ZodOptional<z.ZodNumber>;
        stopSequences: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
    }, {
        language: z.ZodOptional<z.ZodEnum<["auto", "en", "es", "hi", "ja", "ko", "pt", "zh-TW", "zh", "zh-CN"]>>;
        aspectRatio: z.ZodOptional<z.ZodEnum<["1:1", "9:16", "16:9", "3:4", "4:3"]>>;
        negativePrompt: z.ZodOptional<z.ZodString>;
        seed: z.ZodOptional<z.ZodNumber>;
        location: z.ZodOptional<z.ZodString>;
        personGeneration: z.ZodOptional<z.ZodEnum<["dont_allow", "allow_adult", "allow_all"]>>;
        safetySetting: z.ZodOptional<z.ZodEnum<["block_most", "block_some", "block_few", "block_fewest"]>>;
        addWatermark: z.ZodOptional<z.ZodBoolean>;
        storageUri: z.ZodOptional<z.ZodString>;
        mode: z.ZodOptional<z.ZodEnum<["upscale"]>>;
        /**
         * Describes the editing intention for the request.
         *
         * See https://cloud.google.com/vertex-ai/generative-ai/docs/model-reference/imagen-api#edit_images_2 for details.
         */
        editConfig: z.ZodOptional<z.ZodObject<{
            editMode: z.ZodOptional<z.ZodEnum<["inpainting-insert", "inpainting-remove", "outpainting", "product-image"]>>;
            maskMode: z.ZodOptional<z.ZodObject<{
                maskType: z.ZodEnum<["background", "foreground", "semantic"]>;
                classes: z.ZodOptional<z.ZodArray<z.ZodNumber, "many">>;
            }, "strip", z.ZodTypeAny, {
                maskType: "background" | "foreground" | "semantic";
                classes?: number[] | undefined;
            }, {
                maskType: "background" | "foreground" | "semantic";
                classes?: number[] | undefined;
            }>>;
            maskDilation: z.ZodOptional<z.ZodNumber>;
            guidanceScale: z.ZodOptional<z.ZodNumber>;
            productPosition: z.ZodOptional<z.ZodEnum<["reposition", "fixed"]>>;
        }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
            editMode: z.ZodOptional<z.ZodEnum<["inpainting-insert", "inpainting-remove", "outpainting", "product-image"]>>;
            maskMode: z.ZodOptional<z.ZodObject<{
                maskType: z.ZodEnum<["background", "foreground", "semantic"]>;
                classes: z.ZodOptional<z.ZodArray<z.ZodNumber, "many">>;
            }, "strip", z.ZodTypeAny, {
                maskType: "background" | "foreground" | "semantic";
                classes?: number[] | undefined;
            }, {
                maskType: "background" | "foreground" | "semantic";
                classes?: number[] | undefined;
            }>>;
            maskDilation: z.ZodOptional<z.ZodNumber>;
            guidanceScale: z.ZodOptional<z.ZodNumber>;
            productPosition: z.ZodOptional<z.ZodEnum<["reposition", "fixed"]>>;
        }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
            editMode: z.ZodOptional<z.ZodEnum<["inpainting-insert", "inpainting-remove", "outpainting", "product-image"]>>;
            maskMode: z.ZodOptional<z.ZodObject<{
                maskType: z.ZodEnum<["background", "foreground", "semantic"]>;
                classes: z.ZodOptional<z.ZodArray<z.ZodNumber, "many">>;
            }, "strip", z.ZodTypeAny, {
                maskType: "background" | "foreground" | "semantic";
                classes?: number[] | undefined;
            }, {
                maskType: "background" | "foreground" | "semantic";
                classes?: number[] | undefined;
            }>>;
            maskDilation: z.ZodOptional<z.ZodNumber>;
            guidanceScale: z.ZodOptional<z.ZodNumber>;
            productPosition: z.ZodOptional<z.ZodEnum<["reposition", "fixed"]>>;
        }, z.ZodTypeAny, "passthrough">>>;
        upscaleConfig: z.ZodOptional<z.ZodObject<{
            upscaleFactor: z.ZodEnum<["x2", "x4"]>;
        }, "strip", z.ZodTypeAny, {
            upscaleFactor: "x2" | "x4";
        }, {
            upscaleFactor: "x2" | "x4";
        }>>;
    }>, "passthrough", z.ZodTypeAny, z.objectOutputType<z.objectUtil.extendShape<{
        version: z.ZodOptional<z.ZodString>;
        temperature: z.ZodOptional<z.ZodNumber>;
        maxOutputTokens: z.ZodOptional<z.ZodNumber>;
        topK: z.ZodOptional<z.ZodNumber>;
        topP: z.ZodOptional<z.ZodNumber>;
        stopSequences: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
    }, {
        language: z.ZodOptional<z.ZodEnum<["auto", "en", "es", "hi", "ja", "ko", "pt", "zh-TW", "zh", "zh-CN"]>>;
        aspectRatio: z.ZodOptional<z.ZodEnum<["1:1", "9:16", "16:9", "3:4", "4:3"]>>;
        negativePrompt: z.ZodOptional<z.ZodString>;
        seed: z.ZodOptional<z.ZodNumber>;
        location: z.ZodOptional<z.ZodString>;
        personGeneration: z.ZodOptional<z.ZodEnum<["dont_allow", "allow_adult", "allow_all"]>>;
        safetySetting: z.ZodOptional<z.ZodEnum<["block_most", "block_some", "block_few", "block_fewest"]>>;
        addWatermark: z.ZodOptional<z.ZodBoolean>;
        storageUri: z.ZodOptional<z.ZodString>;
        mode: z.ZodOptional<z.ZodEnum<["upscale"]>>;
        /**
         * Describes the editing intention for the request.
         *
         * See https://cloud.google.com/vertex-ai/generative-ai/docs/model-reference/imagen-api#edit_images_2 for details.
         */
        editConfig: z.ZodOptional<z.ZodObject<{
            editMode: z.ZodOptional<z.ZodEnum<["inpainting-insert", "inpainting-remove", "outpainting", "product-image"]>>;
            maskMode: z.ZodOptional<z.ZodObject<{
                maskType: z.ZodEnum<["background", "foreground", "semantic"]>;
                classes: z.ZodOptional<z.ZodArray<z.ZodNumber, "many">>;
            }, "strip", z.ZodTypeAny, {
                maskType: "background" | "foreground" | "semantic";
                classes?: number[] | undefined;
            }, {
                maskType: "background" | "foreground" | "semantic";
                classes?: number[] | undefined;
            }>>;
            maskDilation: z.ZodOptional<z.ZodNumber>;
            guidanceScale: z.ZodOptional<z.ZodNumber>;
            productPosition: z.ZodOptional<z.ZodEnum<["reposition", "fixed"]>>;
        }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
            editMode: z.ZodOptional<z.ZodEnum<["inpainting-insert", "inpainting-remove", "outpainting", "product-image"]>>;
            maskMode: z.ZodOptional<z.ZodObject<{
                maskType: z.ZodEnum<["background", "foreground", "semantic"]>;
                classes: z.ZodOptional<z.ZodArray<z.ZodNumber, "many">>;
            }, "strip", z.ZodTypeAny, {
                maskType: "background" | "foreground" | "semantic";
                classes?: number[] | undefined;
            }, {
                maskType: "background" | "foreground" | "semantic";
                classes?: number[] | undefined;
            }>>;
            maskDilation: z.ZodOptional<z.ZodNumber>;
            guidanceScale: z.ZodOptional<z.ZodNumber>;
            productPosition: z.ZodOptional<z.ZodEnum<["reposition", "fixed"]>>;
        }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
            editMode: z.ZodOptional<z.ZodEnum<["inpainting-insert", "inpainting-remove", "outpainting", "product-image"]>>;
            maskMode: z.ZodOptional<z.ZodObject<{
                maskType: z.ZodEnum<["background", "foreground", "semantic"]>;
                classes: z.ZodOptional<z.ZodArray<z.ZodNumber, "many">>;
            }, "strip", z.ZodTypeAny, {
                maskType: "background" | "foreground" | "semantic";
                classes?: number[] | undefined;
            }, {
                maskType: "background" | "foreground" | "semantic";
                classes?: number[] | undefined;
            }>>;
            maskDilation: z.ZodOptional<z.ZodNumber>;
            guidanceScale: z.ZodOptional<z.ZodNumber>;
            productPosition: z.ZodOptional<z.ZodEnum<["reposition", "fixed"]>>;
        }, z.ZodTypeAny, "passthrough">>>;
        upscaleConfig: z.ZodOptional<z.ZodObject<{
            upscaleFactor: z.ZodEnum<["x2", "x4"]>;
        }, "strip", z.ZodTypeAny, {
            upscaleFactor: "x2" | "x4";
        }, {
            upscaleFactor: "x2" | "x4";
        }>>;
    }>, z.ZodTypeAny, "passthrough">, z.objectInputType<z.objectUtil.extendShape<{
        version: z.ZodOptional<z.ZodString>;
        temperature: z.ZodOptional<z.ZodNumber>;
        maxOutputTokens: z.ZodOptional<z.ZodNumber>;
        topK: z.ZodOptional<z.ZodNumber>;
        topP: z.ZodOptional<z.ZodNumber>;
        stopSequences: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
    }, {
        language: z.ZodOptional<z.ZodEnum<["auto", "en", "es", "hi", "ja", "ko", "pt", "zh-TW", "zh", "zh-CN"]>>;
        aspectRatio: z.ZodOptional<z.ZodEnum<["1:1", "9:16", "16:9", "3:4", "4:3"]>>;
        negativePrompt: z.ZodOptional<z.ZodString>;
        seed: z.ZodOptional<z.ZodNumber>;
        location: z.ZodOptional<z.ZodString>;
        personGeneration: z.ZodOptional<z.ZodEnum<["dont_allow", "allow_adult", "allow_all"]>>;
        safetySetting: z.ZodOptional<z.ZodEnum<["block_most", "block_some", "block_few", "block_fewest"]>>;
        addWatermark: z.ZodOptional<z.ZodBoolean>;
        storageUri: z.ZodOptional<z.ZodString>;
        mode: z.ZodOptional<z.ZodEnum<["upscale"]>>;
        /**
         * Describes the editing intention for the request.
         *
         * See https://cloud.google.com/vertex-ai/generative-ai/docs/model-reference/imagen-api#edit_images_2 for details.
         */
        editConfig: z.ZodOptional<z.ZodObject<{
            editMode: z.ZodOptional<z.ZodEnum<["inpainting-insert", "inpainting-remove", "outpainting", "product-image"]>>;
            maskMode: z.ZodOptional<z.ZodObject<{
                maskType: z.ZodEnum<["background", "foreground", "semantic"]>;
                classes: z.ZodOptional<z.ZodArray<z.ZodNumber, "many">>;
            }, "strip", z.ZodTypeAny, {
                maskType: "background" | "foreground" | "semantic";
                classes?: number[] | undefined;
            }, {
                maskType: "background" | "foreground" | "semantic";
                classes?: number[] | undefined;
            }>>;
            maskDilation: z.ZodOptional<z.ZodNumber>;
            guidanceScale: z.ZodOptional<z.ZodNumber>;
            productPosition: z.ZodOptional<z.ZodEnum<["reposition", "fixed"]>>;
        }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
            editMode: z.ZodOptional<z.ZodEnum<["inpainting-insert", "inpainting-remove", "outpainting", "product-image"]>>;
            maskMode: z.ZodOptional<z.ZodObject<{
                maskType: z.ZodEnum<["background", "foreground", "semantic"]>;
                classes: z.ZodOptional<z.ZodArray<z.ZodNumber, "many">>;
            }, "strip", z.ZodTypeAny, {
                maskType: "background" | "foreground" | "semantic";
                classes?: number[] | undefined;
            }, {
                maskType: "background" | "foreground" | "semantic";
                classes?: number[] | undefined;
            }>>;
            maskDilation: z.ZodOptional<z.ZodNumber>;
            guidanceScale: z.ZodOptional<z.ZodNumber>;
            productPosition: z.ZodOptional<z.ZodEnum<["reposition", "fixed"]>>;
        }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
            editMode: z.ZodOptional<z.ZodEnum<["inpainting-insert", "inpainting-remove", "outpainting", "product-image"]>>;
            maskMode: z.ZodOptional<z.ZodObject<{
                maskType: z.ZodEnum<["background", "foreground", "semantic"]>;
                classes: z.ZodOptional<z.ZodArray<z.ZodNumber, "many">>;
            }, "strip", z.ZodTypeAny, {
                maskType: "background" | "foreground" | "semantic";
                classes?: number[] | undefined;
            }, {
                maskType: "background" | "foreground" | "semantic";
                classes?: number[] | undefined;
            }>>;
            maskDilation: z.ZodOptional<z.ZodNumber>;
            guidanceScale: z.ZodOptional<z.ZodNumber>;
            productPosition: z.ZodOptional<z.ZodEnum<["reposition", "fixed"]>>;
        }, z.ZodTypeAny, "passthrough">>>;
        upscaleConfig: z.ZodOptional<z.ZodObject<{
            upscaleFactor: z.ZodEnum<["x2", "x4"]>;
        }, "strip", z.ZodTypeAny, {
            upscaleFactor: "x2" | "x4";
        }, {
            upscaleFactor: "x2" | "x4";
        }>>;
    }>, z.ZodTypeAny, "passthrough">>>;
    readonly 'imagen-3.0-fast-generate-001': ModelReference<z.ZodObject<z.objectUtil.extendShape<{
        version: z.ZodOptional<z.ZodString>;
        temperature: z.ZodOptional<z.ZodNumber>;
        maxOutputTokens: z.ZodOptional<z.ZodNumber>;
        topK: z.ZodOptional<z.ZodNumber>;
        topP: z.ZodOptional<z.ZodNumber>;
        stopSequences: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
    }, {
        language: z.ZodOptional<z.ZodEnum<["auto", "en", "es", "hi", "ja", "ko", "pt", "zh-TW", "zh", "zh-CN"]>>;
        aspectRatio: z.ZodOptional<z.ZodEnum<["1:1", "9:16", "16:9", "3:4", "4:3"]>>;
        negativePrompt: z.ZodOptional<z.ZodString>;
        seed: z.ZodOptional<z.ZodNumber>;
        location: z.ZodOptional<z.ZodString>;
        personGeneration: z.ZodOptional<z.ZodEnum<["dont_allow", "allow_adult", "allow_all"]>>;
        safetySetting: z.ZodOptional<z.ZodEnum<["block_most", "block_some", "block_few", "block_fewest"]>>;
        addWatermark: z.ZodOptional<z.ZodBoolean>;
        storageUri: z.ZodOptional<z.ZodString>;
        mode: z.ZodOptional<z.ZodEnum<["upscale"]>>;
        /**
         * Describes the editing intention for the request.
         *
         * See https://cloud.google.com/vertex-ai/generative-ai/docs/model-reference/imagen-api#edit_images_2 for details.
         */
        editConfig: z.ZodOptional<z.ZodObject<{
            editMode: z.ZodOptional<z.ZodEnum<["inpainting-insert", "inpainting-remove", "outpainting", "product-image"]>>;
            maskMode: z.ZodOptional<z.ZodObject<{
                maskType: z.ZodEnum<["background", "foreground", "semantic"]>;
                classes: z.ZodOptional<z.ZodArray<z.ZodNumber, "many">>;
            }, "strip", z.ZodTypeAny, {
                maskType: "background" | "foreground" | "semantic";
                classes?: number[] | undefined;
            }, {
                maskType: "background" | "foreground" | "semantic";
                classes?: number[] | undefined;
            }>>;
            maskDilation: z.ZodOptional<z.ZodNumber>;
            guidanceScale: z.ZodOptional<z.ZodNumber>;
            productPosition: z.ZodOptional<z.ZodEnum<["reposition", "fixed"]>>;
        }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
            editMode: z.ZodOptional<z.ZodEnum<["inpainting-insert", "inpainting-remove", "outpainting", "product-image"]>>;
            maskMode: z.ZodOptional<z.ZodObject<{
                maskType: z.ZodEnum<["background", "foreground", "semantic"]>;
                classes: z.ZodOptional<z.ZodArray<z.ZodNumber, "many">>;
            }, "strip", z.ZodTypeAny, {
                maskType: "background" | "foreground" | "semantic";
                classes?: number[] | undefined;
            }, {
                maskType: "background" | "foreground" | "semantic";
                classes?: number[] | undefined;
            }>>;
            maskDilation: z.ZodOptional<z.ZodNumber>;
            guidanceScale: z.ZodOptional<z.ZodNumber>;
            productPosition: z.ZodOptional<z.ZodEnum<["reposition", "fixed"]>>;
        }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
            editMode: z.ZodOptional<z.ZodEnum<["inpainting-insert", "inpainting-remove", "outpainting", "product-image"]>>;
            maskMode: z.ZodOptional<z.ZodObject<{
                maskType: z.ZodEnum<["background", "foreground", "semantic"]>;
                classes: z.ZodOptional<z.ZodArray<z.ZodNumber, "many">>;
            }, "strip", z.ZodTypeAny, {
                maskType: "background" | "foreground" | "semantic";
                classes?: number[] | undefined;
            }, {
                maskType: "background" | "foreground" | "semantic";
                classes?: number[] | undefined;
            }>>;
            maskDilation: z.ZodOptional<z.ZodNumber>;
            guidanceScale: z.ZodOptional<z.ZodNumber>;
            productPosition: z.ZodOptional<z.ZodEnum<["reposition", "fixed"]>>;
        }, z.ZodTypeAny, "passthrough">>>;
        upscaleConfig: z.ZodOptional<z.ZodObject<{
            upscaleFactor: z.ZodEnum<["x2", "x4"]>;
        }, "strip", z.ZodTypeAny, {
            upscaleFactor: "x2" | "x4";
        }, {
            upscaleFactor: "x2" | "x4";
        }>>;
    }>, "passthrough", z.ZodTypeAny, z.objectOutputType<z.objectUtil.extendShape<{
        version: z.ZodOptional<z.ZodString>;
        temperature: z.ZodOptional<z.ZodNumber>;
        maxOutputTokens: z.ZodOptional<z.ZodNumber>;
        topK: z.ZodOptional<z.ZodNumber>;
        topP: z.ZodOptional<z.ZodNumber>;
        stopSequences: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
    }, {
        language: z.ZodOptional<z.ZodEnum<["auto", "en", "es", "hi", "ja", "ko", "pt", "zh-TW", "zh", "zh-CN"]>>;
        aspectRatio: z.ZodOptional<z.ZodEnum<["1:1", "9:16", "16:9", "3:4", "4:3"]>>;
        negativePrompt: z.ZodOptional<z.ZodString>;
        seed: z.ZodOptional<z.ZodNumber>;
        location: z.ZodOptional<z.ZodString>;
        personGeneration: z.ZodOptional<z.ZodEnum<["dont_allow", "allow_adult", "allow_all"]>>;
        safetySetting: z.ZodOptional<z.ZodEnum<["block_most", "block_some", "block_few", "block_fewest"]>>;
        addWatermark: z.ZodOptional<z.ZodBoolean>;
        storageUri: z.ZodOptional<z.ZodString>;
        mode: z.ZodOptional<z.ZodEnum<["upscale"]>>;
        /**
         * Describes the editing intention for the request.
         *
         * See https://cloud.google.com/vertex-ai/generative-ai/docs/model-reference/imagen-api#edit_images_2 for details.
         */
        editConfig: z.ZodOptional<z.ZodObject<{
            editMode: z.ZodOptional<z.ZodEnum<["inpainting-insert", "inpainting-remove", "outpainting", "product-image"]>>;
            maskMode: z.ZodOptional<z.ZodObject<{
                maskType: z.ZodEnum<["background", "foreground", "semantic"]>;
                classes: z.ZodOptional<z.ZodArray<z.ZodNumber, "many">>;
            }, "strip", z.ZodTypeAny, {
                maskType: "background" | "foreground" | "semantic";
                classes?: number[] | undefined;
            }, {
                maskType: "background" | "foreground" | "semantic";
                classes?: number[] | undefined;
            }>>;
            maskDilation: z.ZodOptional<z.ZodNumber>;
            guidanceScale: z.ZodOptional<z.ZodNumber>;
            productPosition: z.ZodOptional<z.ZodEnum<["reposition", "fixed"]>>;
        }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
            editMode: z.ZodOptional<z.ZodEnum<["inpainting-insert", "inpainting-remove", "outpainting", "product-image"]>>;
            maskMode: z.ZodOptional<z.ZodObject<{
                maskType: z.ZodEnum<["background", "foreground", "semantic"]>;
                classes: z.ZodOptional<z.ZodArray<z.ZodNumber, "many">>;
            }, "strip", z.ZodTypeAny, {
                maskType: "background" | "foreground" | "semantic";
                classes?: number[] | undefined;
            }, {
                maskType: "background" | "foreground" | "semantic";
                classes?: number[] | undefined;
            }>>;
            maskDilation: z.ZodOptional<z.ZodNumber>;
            guidanceScale: z.ZodOptional<z.ZodNumber>;
            productPosition: z.ZodOptional<z.ZodEnum<["reposition", "fixed"]>>;
        }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
            editMode: z.ZodOptional<z.ZodEnum<["inpainting-insert", "inpainting-remove", "outpainting", "product-image"]>>;
            maskMode: z.ZodOptional<z.ZodObject<{
                maskType: z.ZodEnum<["background", "foreground", "semantic"]>;
                classes: z.ZodOptional<z.ZodArray<z.ZodNumber, "many">>;
            }, "strip", z.ZodTypeAny, {
                maskType: "background" | "foreground" | "semantic";
                classes?: number[] | undefined;
            }, {
                maskType: "background" | "foreground" | "semantic";
                classes?: number[] | undefined;
            }>>;
            maskDilation: z.ZodOptional<z.ZodNumber>;
            guidanceScale: z.ZodOptional<z.ZodNumber>;
            productPosition: z.ZodOptional<z.ZodEnum<["reposition", "fixed"]>>;
        }, z.ZodTypeAny, "passthrough">>>;
        upscaleConfig: z.ZodOptional<z.ZodObject<{
            upscaleFactor: z.ZodEnum<["x2", "x4"]>;
        }, "strip", z.ZodTypeAny, {
            upscaleFactor: "x2" | "x4";
        }, {
            upscaleFactor: "x2" | "x4";
        }>>;
    }>, z.ZodTypeAny, "passthrough">, z.objectInputType<z.objectUtil.extendShape<{
        version: z.ZodOptional<z.ZodString>;
        temperature: z.ZodOptional<z.ZodNumber>;
        maxOutputTokens: z.ZodOptional<z.ZodNumber>;
        topK: z.ZodOptional<z.ZodNumber>;
        topP: z.ZodOptional<z.ZodNumber>;
        stopSequences: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
    }, {
        language: z.ZodOptional<z.ZodEnum<["auto", "en", "es", "hi", "ja", "ko", "pt", "zh-TW", "zh", "zh-CN"]>>;
        aspectRatio: z.ZodOptional<z.ZodEnum<["1:1", "9:16", "16:9", "3:4", "4:3"]>>;
        negativePrompt: z.ZodOptional<z.ZodString>;
        seed: z.ZodOptional<z.ZodNumber>;
        location: z.ZodOptional<z.ZodString>;
        personGeneration: z.ZodOptional<z.ZodEnum<["dont_allow", "allow_adult", "allow_all"]>>;
        safetySetting: z.ZodOptional<z.ZodEnum<["block_most", "block_some", "block_few", "block_fewest"]>>;
        addWatermark: z.ZodOptional<z.ZodBoolean>;
        storageUri: z.ZodOptional<z.ZodString>;
        mode: z.ZodOptional<z.ZodEnum<["upscale"]>>;
        /**
         * Describes the editing intention for the request.
         *
         * See https://cloud.google.com/vertex-ai/generative-ai/docs/model-reference/imagen-api#edit_images_2 for details.
         */
        editConfig: z.ZodOptional<z.ZodObject<{
            editMode: z.ZodOptional<z.ZodEnum<["inpainting-insert", "inpainting-remove", "outpainting", "product-image"]>>;
            maskMode: z.ZodOptional<z.ZodObject<{
                maskType: z.ZodEnum<["background", "foreground", "semantic"]>;
                classes: z.ZodOptional<z.ZodArray<z.ZodNumber, "many">>;
            }, "strip", z.ZodTypeAny, {
                maskType: "background" | "foreground" | "semantic";
                classes?: number[] | undefined;
            }, {
                maskType: "background" | "foreground" | "semantic";
                classes?: number[] | undefined;
            }>>;
            maskDilation: z.ZodOptional<z.ZodNumber>;
            guidanceScale: z.ZodOptional<z.ZodNumber>;
            productPosition: z.ZodOptional<z.ZodEnum<["reposition", "fixed"]>>;
        }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
            editMode: z.ZodOptional<z.ZodEnum<["inpainting-insert", "inpainting-remove", "outpainting", "product-image"]>>;
            maskMode: z.ZodOptional<z.ZodObject<{
                maskType: z.ZodEnum<["background", "foreground", "semantic"]>;
                classes: z.ZodOptional<z.ZodArray<z.ZodNumber, "many">>;
            }, "strip", z.ZodTypeAny, {
                maskType: "background" | "foreground" | "semantic";
                classes?: number[] | undefined;
            }, {
                maskType: "background" | "foreground" | "semantic";
                classes?: number[] | undefined;
            }>>;
            maskDilation: z.ZodOptional<z.ZodNumber>;
            guidanceScale: z.ZodOptional<z.ZodNumber>;
            productPosition: z.ZodOptional<z.ZodEnum<["reposition", "fixed"]>>;
        }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
            editMode: z.ZodOptional<z.ZodEnum<["inpainting-insert", "inpainting-remove", "outpainting", "product-image"]>>;
            maskMode: z.ZodOptional<z.ZodObject<{
                maskType: z.ZodEnum<["background", "foreground", "semantic"]>;
                classes: z.ZodOptional<z.ZodArray<z.ZodNumber, "many">>;
            }, "strip", z.ZodTypeAny, {
                maskType: "background" | "foreground" | "semantic";
                classes?: number[] | undefined;
            }, {
                maskType: "background" | "foreground" | "semantic";
                classes?: number[] | undefined;
            }>>;
            maskDilation: z.ZodOptional<z.ZodNumber>;
            guidanceScale: z.ZodOptional<z.ZodNumber>;
            productPosition: z.ZodOptional<z.ZodEnum<["reposition", "fixed"]>>;
        }, z.ZodTypeAny, "passthrough">>>;
        upscaleConfig: z.ZodOptional<z.ZodObject<{
            upscaleFactor: z.ZodEnum<["x2", "x4"]>;
        }, "strip", z.ZodTypeAny, {
            upscaleFactor: "x2" | "x4";
        }, {
            upscaleFactor: "x2" | "x4";
        }>>;
    }>, z.ZodTypeAny, "passthrough">>>;
};
declare const SUPPORTED_IMAGEN_MODELS: {
    imagen2: ModelReference<z.ZodObject<z.objectUtil.extendShape<{
        version: z.ZodOptional<z.ZodString>;
        temperature: z.ZodOptional<z.ZodNumber>;
        maxOutputTokens: z.ZodOptional<z.ZodNumber>;
        topK: z.ZodOptional<z.ZodNumber>;
        topP: z.ZodOptional<z.ZodNumber>;
        stopSequences: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
    }, {
        language: z.ZodOptional<z.ZodEnum<["auto", "en", "es", "hi", "ja", "ko", "pt", "zh-TW", "zh", "zh-CN"]>>;
        aspectRatio: z.ZodOptional<z.ZodEnum<["1:1", "9:16", "16:9", "3:4", "4:3"]>>;
        negativePrompt: z.ZodOptional<z.ZodString>;
        seed: z.ZodOptional<z.ZodNumber>;
        location: z.ZodOptional<z.ZodString>;
        personGeneration: z.ZodOptional<z.ZodEnum<["dont_allow", "allow_adult", "allow_all"]>>;
        safetySetting: z.ZodOptional<z.ZodEnum<["block_most", "block_some", "block_few", "block_fewest"]>>;
        addWatermark: z.ZodOptional<z.ZodBoolean>;
        storageUri: z.ZodOptional<z.ZodString>;
        mode: z.ZodOptional<z.ZodEnum<["upscale"]>>;
        /**
         * Describes the editing intention for the request.
         *
         * See https://cloud.google.com/vertex-ai/generative-ai/docs/model-reference/imagen-api#edit_images_2 for details.
         */
        editConfig: z.ZodOptional<z.ZodObject<{
            editMode: z.ZodOptional<z.ZodEnum<["inpainting-insert", "inpainting-remove", "outpainting", "product-image"]>>;
            maskMode: z.ZodOptional<z.ZodObject<{
                maskType: z.ZodEnum<["background", "foreground", "semantic"]>;
                classes: z.ZodOptional<z.ZodArray<z.ZodNumber, "many">>;
            }, "strip", z.ZodTypeAny, {
                maskType: "background" | "foreground" | "semantic";
                classes?: number[] | undefined;
            }, {
                maskType: "background" | "foreground" | "semantic";
                classes?: number[] | undefined;
            }>>;
            maskDilation: z.ZodOptional<z.ZodNumber>;
            guidanceScale: z.ZodOptional<z.ZodNumber>;
            productPosition: z.ZodOptional<z.ZodEnum<["reposition", "fixed"]>>;
        }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
            editMode: z.ZodOptional<z.ZodEnum<["inpainting-insert", "inpainting-remove", "outpainting", "product-image"]>>;
            maskMode: z.ZodOptional<z.ZodObject<{
                maskType: z.ZodEnum<["background", "foreground", "semantic"]>;
                classes: z.ZodOptional<z.ZodArray<z.ZodNumber, "many">>;
            }, "strip", z.ZodTypeAny, {
                maskType: "background" | "foreground" | "semantic";
                classes?: number[] | undefined;
            }, {
                maskType: "background" | "foreground" | "semantic";
                classes?: number[] | undefined;
            }>>;
            maskDilation: z.ZodOptional<z.ZodNumber>;
            guidanceScale: z.ZodOptional<z.ZodNumber>;
            productPosition: z.ZodOptional<z.ZodEnum<["reposition", "fixed"]>>;
        }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
            editMode: z.ZodOptional<z.ZodEnum<["inpainting-insert", "inpainting-remove", "outpainting", "product-image"]>>;
            maskMode: z.ZodOptional<z.ZodObject<{
                maskType: z.ZodEnum<["background", "foreground", "semantic"]>;
                classes: z.ZodOptional<z.ZodArray<z.ZodNumber, "many">>;
            }, "strip", z.ZodTypeAny, {
                maskType: "background" | "foreground" | "semantic";
                classes?: number[] | undefined;
            }, {
                maskType: "background" | "foreground" | "semantic";
                classes?: number[] | undefined;
            }>>;
            maskDilation: z.ZodOptional<z.ZodNumber>;
            guidanceScale: z.ZodOptional<z.ZodNumber>;
            productPosition: z.ZodOptional<z.ZodEnum<["reposition", "fixed"]>>;
        }, z.ZodTypeAny, "passthrough">>>;
        upscaleConfig: z.ZodOptional<z.ZodObject<{
            upscaleFactor: z.ZodEnum<["x2", "x4"]>;
        }, "strip", z.ZodTypeAny, {
            upscaleFactor: "x2" | "x4";
        }, {
            upscaleFactor: "x2" | "x4";
        }>>;
    }>, "passthrough", z.ZodTypeAny, z.objectOutputType<z.objectUtil.extendShape<{
        version: z.ZodOptional<z.ZodString>;
        temperature: z.ZodOptional<z.ZodNumber>;
        maxOutputTokens: z.ZodOptional<z.ZodNumber>;
        topK: z.ZodOptional<z.ZodNumber>;
        topP: z.ZodOptional<z.ZodNumber>;
        stopSequences: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
    }, {
        language: z.ZodOptional<z.ZodEnum<["auto", "en", "es", "hi", "ja", "ko", "pt", "zh-TW", "zh", "zh-CN"]>>;
        aspectRatio: z.ZodOptional<z.ZodEnum<["1:1", "9:16", "16:9", "3:4", "4:3"]>>;
        negativePrompt: z.ZodOptional<z.ZodString>;
        seed: z.ZodOptional<z.ZodNumber>;
        location: z.ZodOptional<z.ZodString>;
        personGeneration: z.ZodOptional<z.ZodEnum<["dont_allow", "allow_adult", "allow_all"]>>;
        safetySetting: z.ZodOptional<z.ZodEnum<["block_most", "block_some", "block_few", "block_fewest"]>>;
        addWatermark: z.ZodOptional<z.ZodBoolean>;
        storageUri: z.ZodOptional<z.ZodString>;
        mode: z.ZodOptional<z.ZodEnum<["upscale"]>>;
        /**
         * Describes the editing intention for the request.
         *
         * See https://cloud.google.com/vertex-ai/generative-ai/docs/model-reference/imagen-api#edit_images_2 for details.
         */
        editConfig: z.ZodOptional<z.ZodObject<{
            editMode: z.ZodOptional<z.ZodEnum<["inpainting-insert", "inpainting-remove", "outpainting", "product-image"]>>;
            maskMode: z.ZodOptional<z.ZodObject<{
                maskType: z.ZodEnum<["background", "foreground", "semantic"]>;
                classes: z.ZodOptional<z.ZodArray<z.ZodNumber, "many">>;
            }, "strip", z.ZodTypeAny, {
                maskType: "background" | "foreground" | "semantic";
                classes?: number[] | undefined;
            }, {
                maskType: "background" | "foreground" | "semantic";
                classes?: number[] | undefined;
            }>>;
            maskDilation: z.ZodOptional<z.ZodNumber>;
            guidanceScale: z.ZodOptional<z.ZodNumber>;
            productPosition: z.ZodOptional<z.ZodEnum<["reposition", "fixed"]>>;
        }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
            editMode: z.ZodOptional<z.ZodEnum<["inpainting-insert", "inpainting-remove", "outpainting", "product-image"]>>;
            maskMode: z.ZodOptional<z.ZodObject<{
                maskType: z.ZodEnum<["background", "foreground", "semantic"]>;
                classes: z.ZodOptional<z.ZodArray<z.ZodNumber, "many">>;
            }, "strip", z.ZodTypeAny, {
                maskType: "background" | "foreground" | "semantic";
                classes?: number[] | undefined;
            }, {
                maskType: "background" | "foreground" | "semantic";
                classes?: number[] | undefined;
            }>>;
            maskDilation: z.ZodOptional<z.ZodNumber>;
            guidanceScale: z.ZodOptional<z.ZodNumber>;
            productPosition: z.ZodOptional<z.ZodEnum<["reposition", "fixed"]>>;
        }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
            editMode: z.ZodOptional<z.ZodEnum<["inpainting-insert", "inpainting-remove", "outpainting", "product-image"]>>;
            maskMode: z.ZodOptional<z.ZodObject<{
                maskType: z.ZodEnum<["background", "foreground", "semantic"]>;
                classes: z.ZodOptional<z.ZodArray<z.ZodNumber, "many">>;
            }, "strip", z.ZodTypeAny, {
                maskType: "background" | "foreground" | "semantic";
                classes?: number[] | undefined;
            }, {
                maskType: "background" | "foreground" | "semantic";
                classes?: number[] | undefined;
            }>>;
            maskDilation: z.ZodOptional<z.ZodNumber>;
            guidanceScale: z.ZodOptional<z.ZodNumber>;
            productPosition: z.ZodOptional<z.ZodEnum<["reposition", "fixed"]>>;
        }, z.ZodTypeAny, "passthrough">>>;
        upscaleConfig: z.ZodOptional<z.ZodObject<{
            upscaleFactor: z.ZodEnum<["x2", "x4"]>;
        }, "strip", z.ZodTypeAny, {
            upscaleFactor: "x2" | "x4";
        }, {
            upscaleFactor: "x2" | "x4";
        }>>;
    }>, z.ZodTypeAny, "passthrough">, z.objectInputType<z.objectUtil.extendShape<{
        version: z.ZodOptional<z.ZodString>;
        temperature: z.ZodOptional<z.ZodNumber>;
        maxOutputTokens: z.ZodOptional<z.ZodNumber>;
        topK: z.ZodOptional<z.ZodNumber>;
        topP: z.ZodOptional<z.ZodNumber>;
        stopSequences: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
    }, {
        language: z.ZodOptional<z.ZodEnum<["auto", "en", "es", "hi", "ja", "ko", "pt", "zh-TW", "zh", "zh-CN"]>>;
        aspectRatio: z.ZodOptional<z.ZodEnum<["1:1", "9:16", "16:9", "3:4", "4:3"]>>;
        negativePrompt: z.ZodOptional<z.ZodString>;
        seed: z.ZodOptional<z.ZodNumber>;
        location: z.ZodOptional<z.ZodString>;
        personGeneration: z.ZodOptional<z.ZodEnum<["dont_allow", "allow_adult", "allow_all"]>>;
        safetySetting: z.ZodOptional<z.ZodEnum<["block_most", "block_some", "block_few", "block_fewest"]>>;
        addWatermark: z.ZodOptional<z.ZodBoolean>;
        storageUri: z.ZodOptional<z.ZodString>;
        mode: z.ZodOptional<z.ZodEnum<["upscale"]>>;
        /**
         * Describes the editing intention for the request.
         *
         * See https://cloud.google.com/vertex-ai/generative-ai/docs/model-reference/imagen-api#edit_images_2 for details.
         */
        editConfig: z.ZodOptional<z.ZodObject<{
            editMode: z.ZodOptional<z.ZodEnum<["inpainting-insert", "inpainting-remove", "outpainting", "product-image"]>>;
            maskMode: z.ZodOptional<z.ZodObject<{
                maskType: z.ZodEnum<["background", "foreground", "semantic"]>;
                classes: z.ZodOptional<z.ZodArray<z.ZodNumber, "many">>;
            }, "strip", z.ZodTypeAny, {
                maskType: "background" | "foreground" | "semantic";
                classes?: number[] | undefined;
            }, {
                maskType: "background" | "foreground" | "semantic";
                classes?: number[] | undefined;
            }>>;
            maskDilation: z.ZodOptional<z.ZodNumber>;
            guidanceScale: z.ZodOptional<z.ZodNumber>;
            productPosition: z.ZodOptional<z.ZodEnum<["reposition", "fixed"]>>;
        }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
            editMode: z.ZodOptional<z.ZodEnum<["inpainting-insert", "inpainting-remove", "outpainting", "product-image"]>>;
            maskMode: z.ZodOptional<z.ZodObject<{
                maskType: z.ZodEnum<["background", "foreground", "semantic"]>;
                classes: z.ZodOptional<z.ZodArray<z.ZodNumber, "many">>;
            }, "strip", z.ZodTypeAny, {
                maskType: "background" | "foreground" | "semantic";
                classes?: number[] | undefined;
            }, {
                maskType: "background" | "foreground" | "semantic";
                classes?: number[] | undefined;
            }>>;
            maskDilation: z.ZodOptional<z.ZodNumber>;
            guidanceScale: z.ZodOptional<z.ZodNumber>;
            productPosition: z.ZodOptional<z.ZodEnum<["reposition", "fixed"]>>;
        }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
            editMode: z.ZodOptional<z.ZodEnum<["inpainting-insert", "inpainting-remove", "outpainting", "product-image"]>>;
            maskMode: z.ZodOptional<z.ZodObject<{
                maskType: z.ZodEnum<["background", "foreground", "semantic"]>;
                classes: z.ZodOptional<z.ZodArray<z.ZodNumber, "many">>;
            }, "strip", z.ZodTypeAny, {
                maskType: "background" | "foreground" | "semantic";
                classes?: number[] | undefined;
            }, {
                maskType: "background" | "foreground" | "semantic";
                classes?: number[] | undefined;
            }>>;
            maskDilation: z.ZodOptional<z.ZodNumber>;
            guidanceScale: z.ZodOptional<z.ZodNumber>;
            productPosition: z.ZodOptional<z.ZodEnum<["reposition", "fixed"]>>;
        }, z.ZodTypeAny, "passthrough">>>;
        upscaleConfig: z.ZodOptional<z.ZodObject<{
            upscaleFactor: z.ZodEnum<["x2", "x4"]>;
        }, "strip", z.ZodTypeAny, {
            upscaleFactor: "x2" | "x4";
        }, {
            upscaleFactor: "x2" | "x4";
        }>>;
    }>, z.ZodTypeAny, "passthrough">>>;
    imagen3: ModelReference<z.ZodObject<z.objectUtil.extendShape<{
        version: z.ZodOptional<z.ZodString>;
        temperature: z.ZodOptional<z.ZodNumber>;
        maxOutputTokens: z.ZodOptional<z.ZodNumber>;
        topK: z.ZodOptional<z.ZodNumber>;
        topP: z.ZodOptional<z.ZodNumber>;
        stopSequences: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
    }, {
        language: z.ZodOptional<z.ZodEnum<["auto", "en", "es", "hi", "ja", "ko", "pt", "zh-TW", "zh", "zh-CN"]>>;
        aspectRatio: z.ZodOptional<z.ZodEnum<["1:1", "9:16", "16:9", "3:4", "4:3"]>>;
        negativePrompt: z.ZodOptional<z.ZodString>;
        seed: z.ZodOptional<z.ZodNumber>;
        location: z.ZodOptional<z.ZodString>;
        personGeneration: z.ZodOptional<z.ZodEnum<["dont_allow", "allow_adult", "allow_all"]>>;
        safetySetting: z.ZodOptional<z.ZodEnum<["block_most", "block_some", "block_few", "block_fewest"]>>;
        addWatermark: z.ZodOptional<z.ZodBoolean>;
        storageUri: z.ZodOptional<z.ZodString>;
        mode: z.ZodOptional<z.ZodEnum<["upscale"]>>;
        /**
         * Describes the editing intention for the request.
         *
         * See https://cloud.google.com/vertex-ai/generative-ai/docs/model-reference/imagen-api#edit_images_2 for details.
         */
        editConfig: z.ZodOptional<z.ZodObject<{
            editMode: z.ZodOptional<z.ZodEnum<["inpainting-insert", "inpainting-remove", "outpainting", "product-image"]>>;
            maskMode: z.ZodOptional<z.ZodObject<{
                maskType: z.ZodEnum<["background", "foreground", "semantic"]>;
                classes: z.ZodOptional<z.ZodArray<z.ZodNumber, "many">>;
            }, "strip", z.ZodTypeAny, {
                maskType: "background" | "foreground" | "semantic";
                classes?: number[] | undefined;
            }, {
                maskType: "background" | "foreground" | "semantic";
                classes?: number[] | undefined;
            }>>;
            maskDilation: z.ZodOptional<z.ZodNumber>;
            guidanceScale: z.ZodOptional<z.ZodNumber>;
            productPosition: z.ZodOptional<z.ZodEnum<["reposition", "fixed"]>>;
        }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
            editMode: z.ZodOptional<z.ZodEnum<["inpainting-insert", "inpainting-remove", "outpainting", "product-image"]>>;
            maskMode: z.ZodOptional<z.ZodObject<{
                maskType: z.ZodEnum<["background", "foreground", "semantic"]>;
                classes: z.ZodOptional<z.ZodArray<z.ZodNumber, "many">>;
            }, "strip", z.ZodTypeAny, {
                maskType: "background" | "foreground" | "semantic";
                classes?: number[] | undefined;
            }, {
                maskType: "background" | "foreground" | "semantic";
                classes?: number[] | undefined;
            }>>;
            maskDilation: z.ZodOptional<z.ZodNumber>;
            guidanceScale: z.ZodOptional<z.ZodNumber>;
            productPosition: z.ZodOptional<z.ZodEnum<["reposition", "fixed"]>>;
        }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
            editMode: z.ZodOptional<z.ZodEnum<["inpainting-insert", "inpainting-remove", "outpainting", "product-image"]>>;
            maskMode: z.ZodOptional<z.ZodObject<{
                maskType: z.ZodEnum<["background", "foreground", "semantic"]>;
                classes: z.ZodOptional<z.ZodArray<z.ZodNumber, "many">>;
            }, "strip", z.ZodTypeAny, {
                maskType: "background" | "foreground" | "semantic";
                classes?: number[] | undefined;
            }, {
                maskType: "background" | "foreground" | "semantic";
                classes?: number[] | undefined;
            }>>;
            maskDilation: z.ZodOptional<z.ZodNumber>;
            guidanceScale: z.ZodOptional<z.ZodNumber>;
            productPosition: z.ZodOptional<z.ZodEnum<["reposition", "fixed"]>>;
        }, z.ZodTypeAny, "passthrough">>>;
        upscaleConfig: z.ZodOptional<z.ZodObject<{
            upscaleFactor: z.ZodEnum<["x2", "x4"]>;
        }, "strip", z.ZodTypeAny, {
            upscaleFactor: "x2" | "x4";
        }, {
            upscaleFactor: "x2" | "x4";
        }>>;
    }>, "passthrough", z.ZodTypeAny, z.objectOutputType<z.objectUtil.extendShape<{
        version: z.ZodOptional<z.ZodString>;
        temperature: z.ZodOptional<z.ZodNumber>;
        maxOutputTokens: z.ZodOptional<z.ZodNumber>;
        topK: z.ZodOptional<z.ZodNumber>;
        topP: z.ZodOptional<z.ZodNumber>;
        stopSequences: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
    }, {
        language: z.ZodOptional<z.ZodEnum<["auto", "en", "es", "hi", "ja", "ko", "pt", "zh-TW", "zh", "zh-CN"]>>;
        aspectRatio: z.ZodOptional<z.ZodEnum<["1:1", "9:16", "16:9", "3:4", "4:3"]>>;
        negativePrompt: z.ZodOptional<z.ZodString>;
        seed: z.ZodOptional<z.ZodNumber>;
        location: z.ZodOptional<z.ZodString>;
        personGeneration: z.ZodOptional<z.ZodEnum<["dont_allow", "allow_adult", "allow_all"]>>;
        safetySetting: z.ZodOptional<z.ZodEnum<["block_most", "block_some", "block_few", "block_fewest"]>>;
        addWatermark: z.ZodOptional<z.ZodBoolean>;
        storageUri: z.ZodOptional<z.ZodString>;
        mode: z.ZodOptional<z.ZodEnum<["upscale"]>>;
        /**
         * Describes the editing intention for the request.
         *
         * See https://cloud.google.com/vertex-ai/generative-ai/docs/model-reference/imagen-api#edit_images_2 for details.
         */
        editConfig: z.ZodOptional<z.ZodObject<{
            editMode: z.ZodOptional<z.ZodEnum<["inpainting-insert", "inpainting-remove", "outpainting", "product-image"]>>;
            maskMode: z.ZodOptional<z.ZodObject<{
                maskType: z.ZodEnum<["background", "foreground", "semantic"]>;
                classes: z.ZodOptional<z.ZodArray<z.ZodNumber, "many">>;
            }, "strip", z.ZodTypeAny, {
                maskType: "background" | "foreground" | "semantic";
                classes?: number[] | undefined;
            }, {
                maskType: "background" | "foreground" | "semantic";
                classes?: number[] | undefined;
            }>>;
            maskDilation: z.ZodOptional<z.ZodNumber>;
            guidanceScale: z.ZodOptional<z.ZodNumber>;
            productPosition: z.ZodOptional<z.ZodEnum<["reposition", "fixed"]>>;
        }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
            editMode: z.ZodOptional<z.ZodEnum<["inpainting-insert", "inpainting-remove", "outpainting", "product-image"]>>;
            maskMode: z.ZodOptional<z.ZodObject<{
                maskType: z.ZodEnum<["background", "foreground", "semantic"]>;
                classes: z.ZodOptional<z.ZodArray<z.ZodNumber, "many">>;
            }, "strip", z.ZodTypeAny, {
                maskType: "background" | "foreground" | "semantic";
                classes?: number[] | undefined;
            }, {
                maskType: "background" | "foreground" | "semantic";
                classes?: number[] | undefined;
            }>>;
            maskDilation: z.ZodOptional<z.ZodNumber>;
            guidanceScale: z.ZodOptional<z.ZodNumber>;
            productPosition: z.ZodOptional<z.ZodEnum<["reposition", "fixed"]>>;
        }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
            editMode: z.ZodOptional<z.ZodEnum<["inpainting-insert", "inpainting-remove", "outpainting", "product-image"]>>;
            maskMode: z.ZodOptional<z.ZodObject<{
                maskType: z.ZodEnum<["background", "foreground", "semantic"]>;
                classes: z.ZodOptional<z.ZodArray<z.ZodNumber, "many">>;
            }, "strip", z.ZodTypeAny, {
                maskType: "background" | "foreground" | "semantic";
                classes?: number[] | undefined;
            }, {
                maskType: "background" | "foreground" | "semantic";
                classes?: number[] | undefined;
            }>>;
            maskDilation: z.ZodOptional<z.ZodNumber>;
            guidanceScale: z.ZodOptional<z.ZodNumber>;
            productPosition: z.ZodOptional<z.ZodEnum<["reposition", "fixed"]>>;
        }, z.ZodTypeAny, "passthrough">>>;
        upscaleConfig: z.ZodOptional<z.ZodObject<{
            upscaleFactor: z.ZodEnum<["x2", "x4"]>;
        }, "strip", z.ZodTypeAny, {
            upscaleFactor: "x2" | "x4";
        }, {
            upscaleFactor: "x2" | "x4";
        }>>;
    }>, z.ZodTypeAny, "passthrough">, z.objectInputType<z.objectUtil.extendShape<{
        version: z.ZodOptional<z.ZodString>;
        temperature: z.ZodOptional<z.ZodNumber>;
        maxOutputTokens: z.ZodOptional<z.ZodNumber>;
        topK: z.ZodOptional<z.ZodNumber>;
        topP: z.ZodOptional<z.ZodNumber>;
        stopSequences: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
    }, {
        language: z.ZodOptional<z.ZodEnum<["auto", "en", "es", "hi", "ja", "ko", "pt", "zh-TW", "zh", "zh-CN"]>>;
        aspectRatio: z.ZodOptional<z.ZodEnum<["1:1", "9:16", "16:9", "3:4", "4:3"]>>;
        negativePrompt: z.ZodOptional<z.ZodString>;
        seed: z.ZodOptional<z.ZodNumber>;
        location: z.ZodOptional<z.ZodString>;
        personGeneration: z.ZodOptional<z.ZodEnum<["dont_allow", "allow_adult", "allow_all"]>>;
        safetySetting: z.ZodOptional<z.ZodEnum<["block_most", "block_some", "block_few", "block_fewest"]>>;
        addWatermark: z.ZodOptional<z.ZodBoolean>;
        storageUri: z.ZodOptional<z.ZodString>;
        mode: z.ZodOptional<z.ZodEnum<["upscale"]>>;
        /**
         * Describes the editing intention for the request.
         *
         * See https://cloud.google.com/vertex-ai/generative-ai/docs/model-reference/imagen-api#edit_images_2 for details.
         */
        editConfig: z.ZodOptional<z.ZodObject<{
            editMode: z.ZodOptional<z.ZodEnum<["inpainting-insert", "inpainting-remove", "outpainting", "product-image"]>>;
            maskMode: z.ZodOptional<z.ZodObject<{
                maskType: z.ZodEnum<["background", "foreground", "semantic"]>;
                classes: z.ZodOptional<z.ZodArray<z.ZodNumber, "many">>;
            }, "strip", z.ZodTypeAny, {
                maskType: "background" | "foreground" | "semantic";
                classes?: number[] | undefined;
            }, {
                maskType: "background" | "foreground" | "semantic";
                classes?: number[] | undefined;
            }>>;
            maskDilation: z.ZodOptional<z.ZodNumber>;
            guidanceScale: z.ZodOptional<z.ZodNumber>;
            productPosition: z.ZodOptional<z.ZodEnum<["reposition", "fixed"]>>;
        }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
            editMode: z.ZodOptional<z.ZodEnum<["inpainting-insert", "inpainting-remove", "outpainting", "product-image"]>>;
            maskMode: z.ZodOptional<z.ZodObject<{
                maskType: z.ZodEnum<["background", "foreground", "semantic"]>;
                classes: z.ZodOptional<z.ZodArray<z.ZodNumber, "many">>;
            }, "strip", z.ZodTypeAny, {
                maskType: "background" | "foreground" | "semantic";
                classes?: number[] | undefined;
            }, {
                maskType: "background" | "foreground" | "semantic";
                classes?: number[] | undefined;
            }>>;
            maskDilation: z.ZodOptional<z.ZodNumber>;
            guidanceScale: z.ZodOptional<z.ZodNumber>;
            productPosition: z.ZodOptional<z.ZodEnum<["reposition", "fixed"]>>;
        }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
            editMode: z.ZodOptional<z.ZodEnum<["inpainting-insert", "inpainting-remove", "outpainting", "product-image"]>>;
            maskMode: z.ZodOptional<z.ZodObject<{
                maskType: z.ZodEnum<["background", "foreground", "semantic"]>;
                classes: z.ZodOptional<z.ZodArray<z.ZodNumber, "many">>;
            }, "strip", z.ZodTypeAny, {
                maskType: "background" | "foreground" | "semantic";
                classes?: number[] | undefined;
            }, {
                maskType: "background" | "foreground" | "semantic";
                classes?: number[] | undefined;
            }>>;
            maskDilation: z.ZodOptional<z.ZodNumber>;
            guidanceScale: z.ZodOptional<z.ZodNumber>;
            productPosition: z.ZodOptional<z.ZodEnum<["reposition", "fixed"]>>;
        }, z.ZodTypeAny, "passthrough">>>;
        upscaleConfig: z.ZodOptional<z.ZodObject<{
            upscaleFactor: z.ZodEnum<["x2", "x4"]>;
        }, "strip", z.ZodTypeAny, {
            upscaleFactor: "x2" | "x4";
        }, {
            upscaleFactor: "x2" | "x4";
        }>>;
    }>, z.ZodTypeAny, "passthrough">>>;
    'imagen3-fast': ModelReference<z.ZodObject<z.objectUtil.extendShape<{
        version: z.ZodOptional<z.ZodString>;
        temperature: z.ZodOptional<z.ZodNumber>;
        maxOutputTokens: z.ZodOptional<z.ZodNumber>;
        topK: z.ZodOptional<z.ZodNumber>;
        topP: z.ZodOptional<z.ZodNumber>;
        stopSequences: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
    }, {
        language: z.ZodOptional<z.ZodEnum<["auto", "en", "es", "hi", "ja", "ko", "pt", "zh-TW", "zh", "zh-CN"]>>;
        aspectRatio: z.ZodOptional<z.ZodEnum<["1:1", "9:16", "16:9", "3:4", "4:3"]>>;
        negativePrompt: z.ZodOptional<z.ZodString>;
        seed: z.ZodOptional<z.ZodNumber>;
        location: z.ZodOptional<z.ZodString>;
        personGeneration: z.ZodOptional<z.ZodEnum<["dont_allow", "allow_adult", "allow_all"]>>;
        safetySetting: z.ZodOptional<z.ZodEnum<["block_most", "block_some", "block_few", "block_fewest"]>>;
        addWatermark: z.ZodOptional<z.ZodBoolean>;
        storageUri: z.ZodOptional<z.ZodString>;
        mode: z.ZodOptional<z.ZodEnum<["upscale"]>>;
        /**
         * Describes the editing intention for the request.
         *
         * See https://cloud.google.com/vertex-ai/generative-ai/docs/model-reference/imagen-api#edit_images_2 for details.
         */
        editConfig: z.ZodOptional<z.ZodObject<{
            editMode: z.ZodOptional<z.ZodEnum<["inpainting-insert", "inpainting-remove", "outpainting", "product-image"]>>;
            maskMode: z.ZodOptional<z.ZodObject<{
                maskType: z.ZodEnum<["background", "foreground", "semantic"]>;
                classes: z.ZodOptional<z.ZodArray<z.ZodNumber, "many">>;
            }, "strip", z.ZodTypeAny, {
                maskType: "background" | "foreground" | "semantic";
                classes?: number[] | undefined;
            }, {
                maskType: "background" | "foreground" | "semantic";
                classes?: number[] | undefined;
            }>>;
            maskDilation: z.ZodOptional<z.ZodNumber>;
            guidanceScale: z.ZodOptional<z.ZodNumber>;
            productPosition: z.ZodOptional<z.ZodEnum<["reposition", "fixed"]>>;
        }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
            editMode: z.ZodOptional<z.ZodEnum<["inpainting-insert", "inpainting-remove", "outpainting", "product-image"]>>;
            maskMode: z.ZodOptional<z.ZodObject<{
                maskType: z.ZodEnum<["background", "foreground", "semantic"]>;
                classes: z.ZodOptional<z.ZodArray<z.ZodNumber, "many">>;
            }, "strip", z.ZodTypeAny, {
                maskType: "background" | "foreground" | "semantic";
                classes?: number[] | undefined;
            }, {
                maskType: "background" | "foreground" | "semantic";
                classes?: number[] | undefined;
            }>>;
            maskDilation: z.ZodOptional<z.ZodNumber>;
            guidanceScale: z.ZodOptional<z.ZodNumber>;
            productPosition: z.ZodOptional<z.ZodEnum<["reposition", "fixed"]>>;
        }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
            editMode: z.ZodOptional<z.ZodEnum<["inpainting-insert", "inpainting-remove", "outpainting", "product-image"]>>;
            maskMode: z.ZodOptional<z.ZodObject<{
                maskType: z.ZodEnum<["background", "foreground", "semantic"]>;
                classes: z.ZodOptional<z.ZodArray<z.ZodNumber, "many">>;
            }, "strip", z.ZodTypeAny, {
                maskType: "background" | "foreground" | "semantic";
                classes?: number[] | undefined;
            }, {
                maskType: "background" | "foreground" | "semantic";
                classes?: number[] | undefined;
            }>>;
            maskDilation: z.ZodOptional<z.ZodNumber>;
            guidanceScale: z.ZodOptional<z.ZodNumber>;
            productPosition: z.ZodOptional<z.ZodEnum<["reposition", "fixed"]>>;
        }, z.ZodTypeAny, "passthrough">>>;
        upscaleConfig: z.ZodOptional<z.ZodObject<{
            upscaleFactor: z.ZodEnum<["x2", "x4"]>;
        }, "strip", z.ZodTypeAny, {
            upscaleFactor: "x2" | "x4";
        }, {
            upscaleFactor: "x2" | "x4";
        }>>;
    }>, "passthrough", z.ZodTypeAny, z.objectOutputType<z.objectUtil.extendShape<{
        version: z.ZodOptional<z.ZodString>;
        temperature: z.ZodOptional<z.ZodNumber>;
        maxOutputTokens: z.ZodOptional<z.ZodNumber>;
        topK: z.ZodOptional<z.ZodNumber>;
        topP: z.ZodOptional<z.ZodNumber>;
        stopSequences: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
    }, {
        language: z.ZodOptional<z.ZodEnum<["auto", "en", "es", "hi", "ja", "ko", "pt", "zh-TW", "zh", "zh-CN"]>>;
        aspectRatio: z.ZodOptional<z.ZodEnum<["1:1", "9:16", "16:9", "3:4", "4:3"]>>;
        negativePrompt: z.ZodOptional<z.ZodString>;
        seed: z.ZodOptional<z.ZodNumber>;
        location: z.ZodOptional<z.ZodString>;
        personGeneration: z.ZodOptional<z.ZodEnum<["dont_allow", "allow_adult", "allow_all"]>>;
        safetySetting: z.ZodOptional<z.ZodEnum<["block_most", "block_some", "block_few", "block_fewest"]>>;
        addWatermark: z.ZodOptional<z.ZodBoolean>;
        storageUri: z.ZodOptional<z.ZodString>;
        mode: z.ZodOptional<z.ZodEnum<["upscale"]>>;
        /**
         * Describes the editing intention for the request.
         *
         * See https://cloud.google.com/vertex-ai/generative-ai/docs/model-reference/imagen-api#edit_images_2 for details.
         */
        editConfig: z.ZodOptional<z.ZodObject<{
            editMode: z.ZodOptional<z.ZodEnum<["inpainting-insert", "inpainting-remove", "outpainting", "product-image"]>>;
            maskMode: z.ZodOptional<z.ZodObject<{
                maskType: z.ZodEnum<["background", "foreground", "semantic"]>;
                classes: z.ZodOptional<z.ZodArray<z.ZodNumber, "many">>;
            }, "strip", z.ZodTypeAny, {
                maskType: "background" | "foreground" | "semantic";
                classes?: number[] | undefined;
            }, {
                maskType: "background" | "foreground" | "semantic";
                classes?: number[] | undefined;
            }>>;
            maskDilation: z.ZodOptional<z.ZodNumber>;
            guidanceScale: z.ZodOptional<z.ZodNumber>;
            productPosition: z.ZodOptional<z.ZodEnum<["reposition", "fixed"]>>;
        }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
            editMode: z.ZodOptional<z.ZodEnum<["inpainting-insert", "inpainting-remove", "outpainting", "product-image"]>>;
            maskMode: z.ZodOptional<z.ZodObject<{
                maskType: z.ZodEnum<["background", "foreground", "semantic"]>;
                classes: z.ZodOptional<z.ZodArray<z.ZodNumber, "many">>;
            }, "strip", z.ZodTypeAny, {
                maskType: "background" | "foreground" | "semantic";
                classes?: number[] | undefined;
            }, {
                maskType: "background" | "foreground" | "semantic";
                classes?: number[] | undefined;
            }>>;
            maskDilation: z.ZodOptional<z.ZodNumber>;
            guidanceScale: z.ZodOptional<z.ZodNumber>;
            productPosition: z.ZodOptional<z.ZodEnum<["reposition", "fixed"]>>;
        }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
            editMode: z.ZodOptional<z.ZodEnum<["inpainting-insert", "inpainting-remove", "outpainting", "product-image"]>>;
            maskMode: z.ZodOptional<z.ZodObject<{
                maskType: z.ZodEnum<["background", "foreground", "semantic"]>;
                classes: z.ZodOptional<z.ZodArray<z.ZodNumber, "many">>;
            }, "strip", z.ZodTypeAny, {
                maskType: "background" | "foreground" | "semantic";
                classes?: number[] | undefined;
            }, {
                maskType: "background" | "foreground" | "semantic";
                classes?: number[] | undefined;
            }>>;
            maskDilation: z.ZodOptional<z.ZodNumber>;
            guidanceScale: z.ZodOptional<z.ZodNumber>;
            productPosition: z.ZodOptional<z.ZodEnum<["reposition", "fixed"]>>;
        }, z.ZodTypeAny, "passthrough">>>;
        upscaleConfig: z.ZodOptional<z.ZodObject<{
            upscaleFactor: z.ZodEnum<["x2", "x4"]>;
        }, "strip", z.ZodTypeAny, {
            upscaleFactor: "x2" | "x4";
        }, {
            upscaleFactor: "x2" | "x4";
        }>>;
    }>, z.ZodTypeAny, "passthrough">, z.objectInputType<z.objectUtil.extendShape<{
        version: z.ZodOptional<z.ZodString>;
        temperature: z.ZodOptional<z.ZodNumber>;
        maxOutputTokens: z.ZodOptional<z.ZodNumber>;
        topK: z.ZodOptional<z.ZodNumber>;
        topP: z.ZodOptional<z.ZodNumber>;
        stopSequences: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
    }, {
        language: z.ZodOptional<z.ZodEnum<["auto", "en", "es", "hi", "ja", "ko", "pt", "zh-TW", "zh", "zh-CN"]>>;
        aspectRatio: z.ZodOptional<z.ZodEnum<["1:1", "9:16", "16:9", "3:4", "4:3"]>>;
        negativePrompt: z.ZodOptional<z.ZodString>;
        seed: z.ZodOptional<z.ZodNumber>;
        location: z.ZodOptional<z.ZodString>;
        personGeneration: z.ZodOptional<z.ZodEnum<["dont_allow", "allow_adult", "allow_all"]>>;
        safetySetting: z.ZodOptional<z.ZodEnum<["block_most", "block_some", "block_few", "block_fewest"]>>;
        addWatermark: z.ZodOptional<z.ZodBoolean>;
        storageUri: z.ZodOptional<z.ZodString>;
        mode: z.ZodOptional<z.ZodEnum<["upscale"]>>;
        /**
         * Describes the editing intention for the request.
         *
         * See https://cloud.google.com/vertex-ai/generative-ai/docs/model-reference/imagen-api#edit_images_2 for details.
         */
        editConfig: z.ZodOptional<z.ZodObject<{
            editMode: z.ZodOptional<z.ZodEnum<["inpainting-insert", "inpainting-remove", "outpainting", "product-image"]>>;
            maskMode: z.ZodOptional<z.ZodObject<{
                maskType: z.ZodEnum<["background", "foreground", "semantic"]>;
                classes: z.ZodOptional<z.ZodArray<z.ZodNumber, "many">>;
            }, "strip", z.ZodTypeAny, {
                maskType: "background" | "foreground" | "semantic";
                classes?: number[] | undefined;
            }, {
                maskType: "background" | "foreground" | "semantic";
                classes?: number[] | undefined;
            }>>;
            maskDilation: z.ZodOptional<z.ZodNumber>;
            guidanceScale: z.ZodOptional<z.ZodNumber>;
            productPosition: z.ZodOptional<z.ZodEnum<["reposition", "fixed"]>>;
        }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
            editMode: z.ZodOptional<z.ZodEnum<["inpainting-insert", "inpainting-remove", "outpainting", "product-image"]>>;
            maskMode: z.ZodOptional<z.ZodObject<{
                maskType: z.ZodEnum<["background", "foreground", "semantic"]>;
                classes: z.ZodOptional<z.ZodArray<z.ZodNumber, "many">>;
            }, "strip", z.ZodTypeAny, {
                maskType: "background" | "foreground" | "semantic";
                classes?: number[] | undefined;
            }, {
                maskType: "background" | "foreground" | "semantic";
                classes?: number[] | undefined;
            }>>;
            maskDilation: z.ZodOptional<z.ZodNumber>;
            guidanceScale: z.ZodOptional<z.ZodNumber>;
            productPosition: z.ZodOptional<z.ZodEnum<["reposition", "fixed"]>>;
        }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
            editMode: z.ZodOptional<z.ZodEnum<["inpainting-insert", "inpainting-remove", "outpainting", "product-image"]>>;
            maskMode: z.ZodOptional<z.ZodObject<{
                maskType: z.ZodEnum<["background", "foreground", "semantic"]>;
                classes: z.ZodOptional<z.ZodArray<z.ZodNumber, "many">>;
            }, "strip", z.ZodTypeAny, {
                maskType: "background" | "foreground" | "semantic";
                classes?: number[] | undefined;
            }, {
                maskType: "background" | "foreground" | "semantic";
                classes?: number[] | undefined;
            }>>;
            maskDilation: z.ZodOptional<z.ZodNumber>;
            guidanceScale: z.ZodOptional<z.ZodNumber>;
            productPosition: z.ZodOptional<z.ZodEnum<["reposition", "fixed"]>>;
        }, z.ZodTypeAny, "passthrough">>>;
        upscaleConfig: z.ZodOptional<z.ZodObject<{
            upscaleFactor: z.ZodEnum<["x2", "x4"]>;
        }, "strip", z.ZodTypeAny, {
            upscaleFactor: "x2" | "x4";
        }, {
            upscaleFactor: "x2" | "x4";
        }>>;
    }>, z.ZodTypeAny, "passthrough">>>;
    'imagen-3.0-generate-001': ModelReference<z.ZodObject<z.objectUtil.extendShape<{
        version: z.ZodOptional<z.ZodString>;
        temperature: z.ZodOptional<z.ZodNumber>;
        maxOutputTokens: z.ZodOptional<z.ZodNumber>;
        topK: z.ZodOptional<z.ZodNumber>;
        topP: z.ZodOptional<z.ZodNumber>;
        stopSequences: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
    }, {
        language: z.ZodOptional<z.ZodEnum<["auto", "en", "es", "hi", "ja", "ko", "pt", "zh-TW", "zh", "zh-CN"]>>;
        aspectRatio: z.ZodOptional<z.ZodEnum<["1:1", "9:16", "16:9", "3:4", "4:3"]>>;
        negativePrompt: z.ZodOptional<z.ZodString>;
        seed: z.ZodOptional<z.ZodNumber>;
        location: z.ZodOptional<z.ZodString>;
        personGeneration: z.ZodOptional<z.ZodEnum<["dont_allow", "allow_adult", "allow_all"]>>;
        safetySetting: z.ZodOptional<z.ZodEnum<["block_most", "block_some", "block_few", "block_fewest"]>>;
        addWatermark: z.ZodOptional<z.ZodBoolean>;
        storageUri: z.ZodOptional<z.ZodString>;
        mode: z.ZodOptional<z.ZodEnum<["upscale"]>>;
        /**
         * Describes the editing intention for the request.
         *
         * See https://cloud.google.com/vertex-ai/generative-ai/docs/model-reference/imagen-api#edit_images_2 for details.
         */
        editConfig: z.ZodOptional<z.ZodObject<{
            editMode: z.ZodOptional<z.ZodEnum<["inpainting-insert", "inpainting-remove", "outpainting", "product-image"]>>;
            maskMode: z.ZodOptional<z.ZodObject<{
                maskType: z.ZodEnum<["background", "foreground", "semantic"]>;
                classes: z.ZodOptional<z.ZodArray<z.ZodNumber, "many">>;
            }, "strip", z.ZodTypeAny, {
                maskType: "background" | "foreground" | "semantic";
                classes?: number[] | undefined;
            }, {
                maskType: "background" | "foreground" | "semantic";
                classes?: number[] | undefined;
            }>>;
            maskDilation: z.ZodOptional<z.ZodNumber>;
            guidanceScale: z.ZodOptional<z.ZodNumber>;
            productPosition: z.ZodOptional<z.ZodEnum<["reposition", "fixed"]>>;
        }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
            editMode: z.ZodOptional<z.ZodEnum<["inpainting-insert", "inpainting-remove", "outpainting", "product-image"]>>;
            maskMode: z.ZodOptional<z.ZodObject<{
                maskType: z.ZodEnum<["background", "foreground", "semantic"]>;
                classes: z.ZodOptional<z.ZodArray<z.ZodNumber, "many">>;
            }, "strip", z.ZodTypeAny, {
                maskType: "background" | "foreground" | "semantic";
                classes?: number[] | undefined;
            }, {
                maskType: "background" | "foreground" | "semantic";
                classes?: number[] | undefined;
            }>>;
            maskDilation: z.ZodOptional<z.ZodNumber>;
            guidanceScale: z.ZodOptional<z.ZodNumber>;
            productPosition: z.ZodOptional<z.ZodEnum<["reposition", "fixed"]>>;
        }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
            editMode: z.ZodOptional<z.ZodEnum<["inpainting-insert", "inpainting-remove", "outpainting", "product-image"]>>;
            maskMode: z.ZodOptional<z.ZodObject<{
                maskType: z.ZodEnum<["background", "foreground", "semantic"]>;
                classes: z.ZodOptional<z.ZodArray<z.ZodNumber, "many">>;
            }, "strip", z.ZodTypeAny, {
                maskType: "background" | "foreground" | "semantic";
                classes?: number[] | undefined;
            }, {
                maskType: "background" | "foreground" | "semantic";
                classes?: number[] | undefined;
            }>>;
            maskDilation: z.ZodOptional<z.ZodNumber>;
            guidanceScale: z.ZodOptional<z.ZodNumber>;
            productPosition: z.ZodOptional<z.ZodEnum<["reposition", "fixed"]>>;
        }, z.ZodTypeAny, "passthrough">>>;
        upscaleConfig: z.ZodOptional<z.ZodObject<{
            upscaleFactor: z.ZodEnum<["x2", "x4"]>;
        }, "strip", z.ZodTypeAny, {
            upscaleFactor: "x2" | "x4";
        }, {
            upscaleFactor: "x2" | "x4";
        }>>;
    }>, "passthrough", z.ZodTypeAny, z.objectOutputType<z.objectUtil.extendShape<{
        version: z.ZodOptional<z.ZodString>;
        temperature: z.ZodOptional<z.ZodNumber>;
        maxOutputTokens: z.ZodOptional<z.ZodNumber>;
        topK: z.ZodOptional<z.ZodNumber>;
        topP: z.ZodOptional<z.ZodNumber>;
        stopSequences: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
    }, {
        language: z.ZodOptional<z.ZodEnum<["auto", "en", "es", "hi", "ja", "ko", "pt", "zh-TW", "zh", "zh-CN"]>>;
        aspectRatio: z.ZodOptional<z.ZodEnum<["1:1", "9:16", "16:9", "3:4", "4:3"]>>;
        negativePrompt: z.ZodOptional<z.ZodString>;
        seed: z.ZodOptional<z.ZodNumber>;
        location: z.ZodOptional<z.ZodString>;
        personGeneration: z.ZodOptional<z.ZodEnum<["dont_allow", "allow_adult", "allow_all"]>>;
        safetySetting: z.ZodOptional<z.ZodEnum<["block_most", "block_some", "block_few", "block_fewest"]>>;
        addWatermark: z.ZodOptional<z.ZodBoolean>;
        storageUri: z.ZodOptional<z.ZodString>;
        mode: z.ZodOptional<z.ZodEnum<["upscale"]>>;
        /**
         * Describes the editing intention for the request.
         *
         * See https://cloud.google.com/vertex-ai/generative-ai/docs/model-reference/imagen-api#edit_images_2 for details.
         */
        editConfig: z.ZodOptional<z.ZodObject<{
            editMode: z.ZodOptional<z.ZodEnum<["inpainting-insert", "inpainting-remove", "outpainting", "product-image"]>>;
            maskMode: z.ZodOptional<z.ZodObject<{
                maskType: z.ZodEnum<["background", "foreground", "semantic"]>;
                classes: z.ZodOptional<z.ZodArray<z.ZodNumber, "many">>;
            }, "strip", z.ZodTypeAny, {
                maskType: "background" | "foreground" | "semantic";
                classes?: number[] | undefined;
            }, {
                maskType: "background" | "foreground" | "semantic";
                classes?: number[] | undefined;
            }>>;
            maskDilation: z.ZodOptional<z.ZodNumber>;
            guidanceScale: z.ZodOptional<z.ZodNumber>;
            productPosition: z.ZodOptional<z.ZodEnum<["reposition", "fixed"]>>;
        }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
            editMode: z.ZodOptional<z.ZodEnum<["inpainting-insert", "inpainting-remove", "outpainting", "product-image"]>>;
            maskMode: z.ZodOptional<z.ZodObject<{
                maskType: z.ZodEnum<["background", "foreground", "semantic"]>;
                classes: z.ZodOptional<z.ZodArray<z.ZodNumber, "many">>;
            }, "strip", z.ZodTypeAny, {
                maskType: "background" | "foreground" | "semantic";
                classes?: number[] | undefined;
            }, {
                maskType: "background" | "foreground" | "semantic";
                classes?: number[] | undefined;
            }>>;
            maskDilation: z.ZodOptional<z.ZodNumber>;
            guidanceScale: z.ZodOptional<z.ZodNumber>;
            productPosition: z.ZodOptional<z.ZodEnum<["reposition", "fixed"]>>;
        }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
            editMode: z.ZodOptional<z.ZodEnum<["inpainting-insert", "inpainting-remove", "outpainting", "product-image"]>>;
            maskMode: z.ZodOptional<z.ZodObject<{
                maskType: z.ZodEnum<["background", "foreground", "semantic"]>;
                classes: z.ZodOptional<z.ZodArray<z.ZodNumber, "many">>;
            }, "strip", z.ZodTypeAny, {
                maskType: "background" | "foreground" | "semantic";
                classes?: number[] | undefined;
            }, {
                maskType: "background" | "foreground" | "semantic";
                classes?: number[] | undefined;
            }>>;
            maskDilation: z.ZodOptional<z.ZodNumber>;
            guidanceScale: z.ZodOptional<z.ZodNumber>;
            productPosition: z.ZodOptional<z.ZodEnum<["reposition", "fixed"]>>;
        }, z.ZodTypeAny, "passthrough">>>;
        upscaleConfig: z.ZodOptional<z.ZodObject<{
            upscaleFactor: z.ZodEnum<["x2", "x4"]>;
        }, "strip", z.ZodTypeAny, {
            upscaleFactor: "x2" | "x4";
        }, {
            upscaleFactor: "x2" | "x4";
        }>>;
    }>, z.ZodTypeAny, "passthrough">, z.objectInputType<z.objectUtil.extendShape<{
        version: z.ZodOptional<z.ZodString>;
        temperature: z.ZodOptional<z.ZodNumber>;
        maxOutputTokens: z.ZodOptional<z.ZodNumber>;
        topK: z.ZodOptional<z.ZodNumber>;
        topP: z.ZodOptional<z.ZodNumber>;
        stopSequences: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
    }, {
        language: z.ZodOptional<z.ZodEnum<["auto", "en", "es", "hi", "ja", "ko", "pt", "zh-TW", "zh", "zh-CN"]>>;
        aspectRatio: z.ZodOptional<z.ZodEnum<["1:1", "9:16", "16:9", "3:4", "4:3"]>>;
        negativePrompt: z.ZodOptional<z.ZodString>;
        seed: z.ZodOptional<z.ZodNumber>;
        location: z.ZodOptional<z.ZodString>;
        personGeneration: z.ZodOptional<z.ZodEnum<["dont_allow", "allow_adult", "allow_all"]>>;
        safetySetting: z.ZodOptional<z.ZodEnum<["block_most", "block_some", "block_few", "block_fewest"]>>;
        addWatermark: z.ZodOptional<z.ZodBoolean>;
        storageUri: z.ZodOptional<z.ZodString>;
        mode: z.ZodOptional<z.ZodEnum<["upscale"]>>;
        /**
         * Describes the editing intention for the request.
         *
         * See https://cloud.google.com/vertex-ai/generative-ai/docs/model-reference/imagen-api#edit_images_2 for details.
         */
        editConfig: z.ZodOptional<z.ZodObject<{
            editMode: z.ZodOptional<z.ZodEnum<["inpainting-insert", "inpainting-remove", "outpainting", "product-image"]>>;
            maskMode: z.ZodOptional<z.ZodObject<{
                maskType: z.ZodEnum<["background", "foreground", "semantic"]>;
                classes: z.ZodOptional<z.ZodArray<z.ZodNumber, "many">>;
            }, "strip", z.ZodTypeAny, {
                maskType: "background" | "foreground" | "semantic";
                classes?: number[] | undefined;
            }, {
                maskType: "background" | "foreground" | "semantic";
                classes?: number[] | undefined;
            }>>;
            maskDilation: z.ZodOptional<z.ZodNumber>;
            guidanceScale: z.ZodOptional<z.ZodNumber>;
            productPosition: z.ZodOptional<z.ZodEnum<["reposition", "fixed"]>>;
        }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
            editMode: z.ZodOptional<z.ZodEnum<["inpainting-insert", "inpainting-remove", "outpainting", "product-image"]>>;
            maskMode: z.ZodOptional<z.ZodObject<{
                maskType: z.ZodEnum<["background", "foreground", "semantic"]>;
                classes: z.ZodOptional<z.ZodArray<z.ZodNumber, "many">>;
            }, "strip", z.ZodTypeAny, {
                maskType: "background" | "foreground" | "semantic";
                classes?: number[] | undefined;
            }, {
                maskType: "background" | "foreground" | "semantic";
                classes?: number[] | undefined;
            }>>;
            maskDilation: z.ZodOptional<z.ZodNumber>;
            guidanceScale: z.ZodOptional<z.ZodNumber>;
            productPosition: z.ZodOptional<z.ZodEnum<["reposition", "fixed"]>>;
        }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
            editMode: z.ZodOptional<z.ZodEnum<["inpainting-insert", "inpainting-remove", "outpainting", "product-image"]>>;
            maskMode: z.ZodOptional<z.ZodObject<{
                maskType: z.ZodEnum<["background", "foreground", "semantic"]>;
                classes: z.ZodOptional<z.ZodArray<z.ZodNumber, "many">>;
            }, "strip", z.ZodTypeAny, {
                maskType: "background" | "foreground" | "semantic";
                classes?: number[] | undefined;
            }, {
                maskType: "background" | "foreground" | "semantic";
                classes?: number[] | undefined;
            }>>;
            maskDilation: z.ZodOptional<z.ZodNumber>;
            guidanceScale: z.ZodOptional<z.ZodNumber>;
            productPosition: z.ZodOptional<z.ZodEnum<["reposition", "fixed"]>>;
        }, z.ZodTypeAny, "passthrough">>>;
        upscaleConfig: z.ZodOptional<z.ZodObject<{
            upscaleFactor: z.ZodEnum<["x2", "x4"]>;
        }, "strip", z.ZodTypeAny, {
            upscaleFactor: "x2" | "x4";
        }, {
            upscaleFactor: "x2" | "x4";
        }>>;
    }>, z.ZodTypeAny, "passthrough">>>;
    'imagen-3.0-fast-generate-001': ModelReference<z.ZodObject<z.objectUtil.extendShape<{
        version: z.ZodOptional<z.ZodString>;
        temperature: z.ZodOptional<z.ZodNumber>;
        maxOutputTokens: z.ZodOptional<z.ZodNumber>;
        topK: z.ZodOptional<z.ZodNumber>;
        topP: z.ZodOptional<z.ZodNumber>;
        stopSequences: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
    }, {
        language: z.ZodOptional<z.ZodEnum<["auto", "en", "es", "hi", "ja", "ko", "pt", "zh-TW", "zh", "zh-CN"]>>;
        aspectRatio: z.ZodOptional<z.ZodEnum<["1:1", "9:16", "16:9", "3:4", "4:3"]>>;
        negativePrompt: z.ZodOptional<z.ZodString>;
        seed: z.ZodOptional<z.ZodNumber>;
        location: z.ZodOptional<z.ZodString>;
        personGeneration: z.ZodOptional<z.ZodEnum<["dont_allow", "allow_adult", "allow_all"]>>;
        safetySetting: z.ZodOptional<z.ZodEnum<["block_most", "block_some", "block_few", "block_fewest"]>>;
        addWatermark: z.ZodOptional<z.ZodBoolean>;
        storageUri: z.ZodOptional<z.ZodString>;
        mode: z.ZodOptional<z.ZodEnum<["upscale"]>>;
        /**
         * Describes the editing intention for the request.
         *
         * See https://cloud.google.com/vertex-ai/generative-ai/docs/model-reference/imagen-api#edit_images_2 for details.
         */
        editConfig: z.ZodOptional<z.ZodObject<{
            editMode: z.ZodOptional<z.ZodEnum<["inpainting-insert", "inpainting-remove", "outpainting", "product-image"]>>;
            maskMode: z.ZodOptional<z.ZodObject<{
                maskType: z.ZodEnum<["background", "foreground", "semantic"]>;
                classes: z.ZodOptional<z.ZodArray<z.ZodNumber, "many">>;
            }, "strip", z.ZodTypeAny, {
                maskType: "background" | "foreground" | "semantic";
                classes?: number[] | undefined;
            }, {
                maskType: "background" | "foreground" | "semantic";
                classes?: number[] | undefined;
            }>>;
            maskDilation: z.ZodOptional<z.ZodNumber>;
            guidanceScale: z.ZodOptional<z.ZodNumber>;
            productPosition: z.ZodOptional<z.ZodEnum<["reposition", "fixed"]>>;
        }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
            editMode: z.ZodOptional<z.ZodEnum<["inpainting-insert", "inpainting-remove", "outpainting", "product-image"]>>;
            maskMode: z.ZodOptional<z.ZodObject<{
                maskType: z.ZodEnum<["background", "foreground", "semantic"]>;
                classes: z.ZodOptional<z.ZodArray<z.ZodNumber, "many">>;
            }, "strip", z.ZodTypeAny, {
                maskType: "background" | "foreground" | "semantic";
                classes?: number[] | undefined;
            }, {
                maskType: "background" | "foreground" | "semantic";
                classes?: number[] | undefined;
            }>>;
            maskDilation: z.ZodOptional<z.ZodNumber>;
            guidanceScale: z.ZodOptional<z.ZodNumber>;
            productPosition: z.ZodOptional<z.ZodEnum<["reposition", "fixed"]>>;
        }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
            editMode: z.ZodOptional<z.ZodEnum<["inpainting-insert", "inpainting-remove", "outpainting", "product-image"]>>;
            maskMode: z.ZodOptional<z.ZodObject<{
                maskType: z.ZodEnum<["background", "foreground", "semantic"]>;
                classes: z.ZodOptional<z.ZodArray<z.ZodNumber, "many">>;
            }, "strip", z.ZodTypeAny, {
                maskType: "background" | "foreground" | "semantic";
                classes?: number[] | undefined;
            }, {
                maskType: "background" | "foreground" | "semantic";
                classes?: number[] | undefined;
            }>>;
            maskDilation: z.ZodOptional<z.ZodNumber>;
            guidanceScale: z.ZodOptional<z.ZodNumber>;
            productPosition: z.ZodOptional<z.ZodEnum<["reposition", "fixed"]>>;
        }, z.ZodTypeAny, "passthrough">>>;
        upscaleConfig: z.ZodOptional<z.ZodObject<{
            upscaleFactor: z.ZodEnum<["x2", "x4"]>;
        }, "strip", z.ZodTypeAny, {
            upscaleFactor: "x2" | "x4";
        }, {
            upscaleFactor: "x2" | "x4";
        }>>;
    }>, "passthrough", z.ZodTypeAny, z.objectOutputType<z.objectUtil.extendShape<{
        version: z.ZodOptional<z.ZodString>;
        temperature: z.ZodOptional<z.ZodNumber>;
        maxOutputTokens: z.ZodOptional<z.ZodNumber>;
        topK: z.ZodOptional<z.ZodNumber>;
        topP: z.ZodOptional<z.ZodNumber>;
        stopSequences: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
    }, {
        language: z.ZodOptional<z.ZodEnum<["auto", "en", "es", "hi", "ja", "ko", "pt", "zh-TW", "zh", "zh-CN"]>>;
        aspectRatio: z.ZodOptional<z.ZodEnum<["1:1", "9:16", "16:9", "3:4", "4:3"]>>;
        negativePrompt: z.ZodOptional<z.ZodString>;
        seed: z.ZodOptional<z.ZodNumber>;
        location: z.ZodOptional<z.ZodString>;
        personGeneration: z.ZodOptional<z.ZodEnum<["dont_allow", "allow_adult", "allow_all"]>>;
        safetySetting: z.ZodOptional<z.ZodEnum<["block_most", "block_some", "block_few", "block_fewest"]>>;
        addWatermark: z.ZodOptional<z.ZodBoolean>;
        storageUri: z.ZodOptional<z.ZodString>;
        mode: z.ZodOptional<z.ZodEnum<["upscale"]>>;
        /**
         * Describes the editing intention for the request.
         *
         * See https://cloud.google.com/vertex-ai/generative-ai/docs/model-reference/imagen-api#edit_images_2 for details.
         */
        editConfig: z.ZodOptional<z.ZodObject<{
            editMode: z.ZodOptional<z.ZodEnum<["inpainting-insert", "inpainting-remove", "outpainting", "product-image"]>>;
            maskMode: z.ZodOptional<z.ZodObject<{
                maskType: z.ZodEnum<["background", "foreground", "semantic"]>;
                classes: z.ZodOptional<z.ZodArray<z.ZodNumber, "many">>;
            }, "strip", z.ZodTypeAny, {
                maskType: "background" | "foreground" | "semantic";
                classes?: number[] | undefined;
            }, {
                maskType: "background" | "foreground" | "semantic";
                classes?: number[] | undefined;
            }>>;
            maskDilation: z.ZodOptional<z.ZodNumber>;
            guidanceScale: z.ZodOptional<z.ZodNumber>;
            productPosition: z.ZodOptional<z.ZodEnum<["reposition", "fixed"]>>;
        }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
            editMode: z.ZodOptional<z.ZodEnum<["inpainting-insert", "inpainting-remove", "outpainting", "product-image"]>>;
            maskMode: z.ZodOptional<z.ZodObject<{
                maskType: z.ZodEnum<["background", "foreground", "semantic"]>;
                classes: z.ZodOptional<z.ZodArray<z.ZodNumber, "many">>;
            }, "strip", z.ZodTypeAny, {
                maskType: "background" | "foreground" | "semantic";
                classes?: number[] | undefined;
            }, {
                maskType: "background" | "foreground" | "semantic";
                classes?: number[] | undefined;
            }>>;
            maskDilation: z.ZodOptional<z.ZodNumber>;
            guidanceScale: z.ZodOptional<z.ZodNumber>;
            productPosition: z.ZodOptional<z.ZodEnum<["reposition", "fixed"]>>;
        }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
            editMode: z.ZodOptional<z.ZodEnum<["inpainting-insert", "inpainting-remove", "outpainting", "product-image"]>>;
            maskMode: z.ZodOptional<z.ZodObject<{
                maskType: z.ZodEnum<["background", "foreground", "semantic"]>;
                classes: z.ZodOptional<z.ZodArray<z.ZodNumber, "many">>;
            }, "strip", z.ZodTypeAny, {
                maskType: "background" | "foreground" | "semantic";
                classes?: number[] | undefined;
            }, {
                maskType: "background" | "foreground" | "semantic";
                classes?: number[] | undefined;
            }>>;
            maskDilation: z.ZodOptional<z.ZodNumber>;
            guidanceScale: z.ZodOptional<z.ZodNumber>;
            productPosition: z.ZodOptional<z.ZodEnum<["reposition", "fixed"]>>;
        }, z.ZodTypeAny, "passthrough">>>;
        upscaleConfig: z.ZodOptional<z.ZodObject<{
            upscaleFactor: z.ZodEnum<["x2", "x4"]>;
        }, "strip", z.ZodTypeAny, {
            upscaleFactor: "x2" | "x4";
        }, {
            upscaleFactor: "x2" | "x4";
        }>>;
    }>, z.ZodTypeAny, "passthrough">, z.objectInputType<z.objectUtil.extendShape<{
        version: z.ZodOptional<z.ZodString>;
        temperature: z.ZodOptional<z.ZodNumber>;
        maxOutputTokens: z.ZodOptional<z.ZodNumber>;
        topK: z.ZodOptional<z.ZodNumber>;
        topP: z.ZodOptional<z.ZodNumber>;
        stopSequences: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
    }, {
        language: z.ZodOptional<z.ZodEnum<["auto", "en", "es", "hi", "ja", "ko", "pt", "zh-TW", "zh", "zh-CN"]>>;
        aspectRatio: z.ZodOptional<z.ZodEnum<["1:1", "9:16", "16:9", "3:4", "4:3"]>>;
        negativePrompt: z.ZodOptional<z.ZodString>;
        seed: z.ZodOptional<z.ZodNumber>;
        location: z.ZodOptional<z.ZodString>;
        personGeneration: z.ZodOptional<z.ZodEnum<["dont_allow", "allow_adult", "allow_all"]>>;
        safetySetting: z.ZodOptional<z.ZodEnum<["block_most", "block_some", "block_few", "block_fewest"]>>;
        addWatermark: z.ZodOptional<z.ZodBoolean>;
        storageUri: z.ZodOptional<z.ZodString>;
        mode: z.ZodOptional<z.ZodEnum<["upscale"]>>;
        /**
         * Describes the editing intention for the request.
         *
         * See https://cloud.google.com/vertex-ai/generative-ai/docs/model-reference/imagen-api#edit_images_2 for details.
         */
        editConfig: z.ZodOptional<z.ZodObject<{
            editMode: z.ZodOptional<z.ZodEnum<["inpainting-insert", "inpainting-remove", "outpainting", "product-image"]>>;
            maskMode: z.ZodOptional<z.ZodObject<{
                maskType: z.ZodEnum<["background", "foreground", "semantic"]>;
                classes: z.ZodOptional<z.ZodArray<z.ZodNumber, "many">>;
            }, "strip", z.ZodTypeAny, {
                maskType: "background" | "foreground" | "semantic";
                classes?: number[] | undefined;
            }, {
                maskType: "background" | "foreground" | "semantic";
                classes?: number[] | undefined;
            }>>;
            maskDilation: z.ZodOptional<z.ZodNumber>;
            guidanceScale: z.ZodOptional<z.ZodNumber>;
            productPosition: z.ZodOptional<z.ZodEnum<["reposition", "fixed"]>>;
        }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
            editMode: z.ZodOptional<z.ZodEnum<["inpainting-insert", "inpainting-remove", "outpainting", "product-image"]>>;
            maskMode: z.ZodOptional<z.ZodObject<{
                maskType: z.ZodEnum<["background", "foreground", "semantic"]>;
                classes: z.ZodOptional<z.ZodArray<z.ZodNumber, "many">>;
            }, "strip", z.ZodTypeAny, {
                maskType: "background" | "foreground" | "semantic";
                classes?: number[] | undefined;
            }, {
                maskType: "background" | "foreground" | "semantic";
                classes?: number[] | undefined;
            }>>;
            maskDilation: z.ZodOptional<z.ZodNumber>;
            guidanceScale: z.ZodOptional<z.ZodNumber>;
            productPosition: z.ZodOptional<z.ZodEnum<["reposition", "fixed"]>>;
        }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
            editMode: z.ZodOptional<z.ZodEnum<["inpainting-insert", "inpainting-remove", "outpainting", "product-image"]>>;
            maskMode: z.ZodOptional<z.ZodObject<{
                maskType: z.ZodEnum<["background", "foreground", "semantic"]>;
                classes: z.ZodOptional<z.ZodArray<z.ZodNumber, "many">>;
            }, "strip", z.ZodTypeAny, {
                maskType: "background" | "foreground" | "semantic";
                classes?: number[] | undefined;
            }, {
                maskType: "background" | "foreground" | "semantic";
                classes?: number[] | undefined;
            }>>;
            maskDilation: z.ZodOptional<z.ZodNumber>;
            guidanceScale: z.ZodOptional<z.ZodNumber>;
            productPosition: z.ZodOptional<z.ZodEnum<["reposition", "fixed"]>>;
        }, z.ZodTypeAny, "passthrough">>>;
        upscaleConfig: z.ZodOptional<z.ZodObject<{
            upscaleFactor: z.ZodEnum<["x2", "x4"]>;
        }, "strip", z.ZodTypeAny, {
            upscaleFactor: "x2" | "x4";
        }, {
            upscaleFactor: "x2" | "x4";
        }>>;
    }>, z.ZodTypeAny, "passthrough">>>;
};
declare const GENERIC_IMAGEN_INFO: {
    versions?: string[] | undefined;
    label?: string | undefined;
    configSchema?: Record<string, any> | undefined;
    supports?: {
        tools?: boolean | undefined;
        toolChoice?: boolean | undefined;
        output?: string[] | undefined;
        context?: boolean | undefined;
        media?: boolean | undefined;
        contentType?: string[] | undefined;
        constrained?: "none" | "all" | "no-tools" | undefined;
        multiturn?: boolean | undefined;
        systemRole?: boolean | undefined;
    } | undefined;
    stage?: "featured" | "stable" | "unstable" | "legacy" | "deprecated" | undefined;
};
declare function defineImagenModel(ai: Genkit, name: string, client: GoogleAuth, options: PluginOptions): ModelAction;

export { ACTUAL_IMAGEN_MODELS, GENERIC_IMAGEN_INFO, ImagenConfigSchema, SUPPORTED_IMAGEN_MODELS, defineImagenModel, imagen2, imagen3, imagen3Fast };
