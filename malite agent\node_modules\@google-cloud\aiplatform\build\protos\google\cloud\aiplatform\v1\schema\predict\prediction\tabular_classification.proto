// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.aiplatform.v1.schema.predict.prediction;


option csharp_namespace = "Google.Cloud.AIPlatform.V1.Schema.Predict.Prediction";
option go_package = "cloud.google.com/go/aiplatform/apiv1/schema/predict/prediction/predictionpb;predictionpb";
option java_multiple_files = true;
option java_outer_classname = "TabularClassificationPredictionResultProto";
option java_package = "com.google.cloud.aiplatform.v1.schema.predict.prediction";
option php_namespace = "Google\\Cloud\\AIPlatform\\V1\\Schema\\Predict\\Prediction";
option ruby_package = "Google::Cloud::AIPlatform::V1::Schema::Predict::Prediction";

// Prediction output format for Tabular Classification.
message TabularClassificationPredictionResult {
  // The name of the classes being classified, contains all possible values of
  // the target column.
  repeated string classes = 1;

  // The model's confidence in each class being correct, higher
  // value means higher confidence. The N-th score corresponds to
  // the N-th class in classes.
  repeated float scores = 2;
}
