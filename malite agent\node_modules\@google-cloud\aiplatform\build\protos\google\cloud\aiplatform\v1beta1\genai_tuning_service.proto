// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.aiplatform.v1beta1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/aiplatform/v1beta1/io.proto";
import "google/cloud/aiplatform/v1beta1/operation.proto";
import "google/cloud/aiplatform/v1beta1/tuning_job.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/empty.proto";

option csharp_namespace = "Google.Cloud.AIPlatform.V1Beta1";
option go_package = "cloud.google.com/go/aiplatform/apiv1beta1/aiplatformpb;aiplatformpb";
option java_multiple_files = true;
option java_outer_classname = "GenAiTuningServiceProto";
option java_package = "com.google.cloud.aiplatform.v1beta1";
option php_namespace = "Google\\Cloud\\AIPlatform\\V1beta1";
option ruby_package = "Google::Cloud::AIPlatform::V1beta1";

// A service for creating and managing GenAI Tuning Jobs.
service GenAiTuningService {
  option (google.api.default_host) = "aiplatform.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Creates a TuningJob. A created TuningJob right away will be attempted to
  // be run.
  rpc CreateTuningJob(CreateTuningJobRequest) returns (TuningJob) {
    option (google.api.http) = {
      post: "/v1beta1/{parent=projects/*/locations/*}/tuningJobs"
      body: "tuning_job"
    };
    option (google.api.method_signature) = "parent,tuning_job";
  }

  // Gets a TuningJob.
  rpc GetTuningJob(GetTuningJobRequest) returns (TuningJob) {
    option (google.api.http) = {
      get: "/v1beta1/{name=projects/*/locations/*/tuningJobs/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists TuningJobs in a Location.
  rpc ListTuningJobs(ListTuningJobsRequest) returns (ListTuningJobsResponse) {
    option (google.api.http) = {
      get: "/v1beta1/{parent=projects/*/locations/*}/tuningJobs"
    };
    option (google.api.method_signature) = "parent";
  }

  // Cancels a TuningJob.
  // Starts asynchronous cancellation on the TuningJob. The server makes a best
  // effort to cancel the job, but success is not guaranteed. Clients can use
  // [GenAiTuningService.GetTuningJob][google.cloud.aiplatform.v1beta1.GenAiTuningService.GetTuningJob]
  // or other methods to check whether the cancellation succeeded or whether the
  // job completed despite cancellation. On successful cancellation, the
  // TuningJob is not deleted; instead it becomes a job with a
  // [TuningJob.error][google.cloud.aiplatform.v1beta1.TuningJob.error] value
  // with a [google.rpc.Status.code][google.rpc.Status.code] of 1, corresponding
  // to `Code.CANCELLED`, and
  // [TuningJob.state][google.cloud.aiplatform.v1beta1.TuningJob.state] is set
  // to `CANCELLED`.
  rpc CancelTuningJob(CancelTuningJobRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/v1beta1/{name=projects/*/locations/*/tuningJobs/*}:cancel"
      body: "*"
    };
    option (google.api.method_signature) = "name";
  }

  // Rebase a TunedModel.
  // Creates a LongRunningOperation that takes a legacy Tuned GenAI model
  // Reference and creates a TuningJob based on newly available model.
  rpc RebaseTunedModel(RebaseTunedModelRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1beta1/{parent=projects/*/locations/*}/tuningJobs:rebaseTunedModel"
      body: "*"
    };
    option (google.api.method_signature) = "parent,tuned_model_ref";
    option (google.longrunning.operation_info) = {
      response_type: "TuningJob"
      metadata_type: "RebaseTunedModelOperationMetadata"
    };
  }
}

// Request message for
// [GenAiTuningService.CreateTuningJob][google.cloud.aiplatform.v1beta1.GenAiTuningService.CreateTuningJob].
message CreateTuningJobRequest {
  // Required. The resource name of the Location to create the TuningJob in.
  // Format: `projects/{project}/locations/{location}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Required. The TuningJob to create.
  TuningJob tuning_job = 2 [(google.api.field_behavior) = REQUIRED];
}

// Request message for
// [GenAiTuningService.GetTuningJob][google.cloud.aiplatform.v1beta1.GenAiTuningService.GetTuningJob].
message GetTuningJobRequest {
  // Required. The name of the TuningJob resource. Format:
  // `projects/{project}/locations/{location}/tuningJobs/{tuning_job}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/TuningJob"
    }
  ];
}

// Request message for
// [GenAiTuningService.ListTuningJobs][google.cloud.aiplatform.v1beta1.GenAiTuningService.ListTuningJobs].
message ListTuningJobsRequest {
  // Required. The resource name of the Location to list the TuningJobs from.
  // Format: `projects/{project}/locations/{location}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Optional. The standard list filter.
  string filter = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The standard list page size.
  int32 page_size = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The standard list page token.
  // Typically obtained via [ListTuningJob.next_page_token][] of the
  // previous GenAiTuningService.ListTuningJob][] call.
  string page_token = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for
// [GenAiTuningService.ListTuningJobs][google.cloud.aiplatform.v1beta1.GenAiTuningService.ListTuningJobs]
message ListTuningJobsResponse {
  // List of TuningJobs in the requested page.
  repeated TuningJob tuning_jobs = 1;

  // A token to retrieve the next page of results.
  // Pass to
  // [ListTuningJobsRequest.page_token][google.cloud.aiplatform.v1beta1.ListTuningJobsRequest.page_token]
  // to obtain that page.
  string next_page_token = 2;
}

// Request message for
// [GenAiTuningService.CancelTuningJob][google.cloud.aiplatform.v1beta1.GenAiTuningService.CancelTuningJob].
message CancelTuningJobRequest {
  // Required. The name of the TuningJob to cancel. Format:
  // `projects/{project}/locations/{location}/tuningJobs/{tuning_job}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/TuningJob"
    }
  ];
}

// Request message for
// [GenAiTuningService.RebaseTunedModel][google.cloud.aiplatform.v1beta1.GenAiTuningService.RebaseTunedModel].
message RebaseTunedModelRequest {
  // Required. The resource name of the Location into which to rebase the Model.
  // Format: `projects/{project}/locations/{location}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Required. TunedModel reference to retrieve the legacy model information.
  TunedModelRef tuned_model_ref = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. The TuningJob to be updated. Users can use this TuningJob field
  // to overwrite tuning configs.
  TuningJob tuning_job = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The Google Cloud Storage location to write the artifacts.
  GcsDestination artifact_destination = 4
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. By default, bison to gemini migration will always create new
  // model/endpoint, but for gemini-1.0 to gemini-1.5 migration, we default
  // deploy to the same endpoint. See details in this Section.
  bool deploy_to_same_endpoint = 5 [(google.api.field_behavior) = OPTIONAL];
}

// Runtime operation information for
// [GenAiTuningService.RebaseTunedModel][google.cloud.aiplatform.v1beta1.GenAiTuningService.RebaseTunedModel].
message RebaseTunedModelOperationMetadata {
  // The common part of the operation generic information.
  GenericOperationMetadata generic_metadata = 1;
}
