{"version": 3, "file": "rpc-metadata.js", "sourceRoot": "", "sources": ["../../../src/trace/rpc-metadata.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAEH,OAAO,EAAW,gBAAgB,EAAQ,MAAM,oBAAoB,CAAC;AAErE,IAAM,gBAAgB,GAAG,gBAAgB,CACvC,4CAA4C,CAC7C,CAAC;AAEF,MAAM,CAAN,IAAY,OAEX;AAFD,WAAY,OAAO;IACjB,wBAAa,CAAA;AACf,CAAC,EAFW,OAAO,KAAP,OAAO,QAElB;AAaD,MAAM,UAAU,cAAc,CAAC,OAAgB,EAAE,IAAiB;IAChE,OAAO,OAAO,CAAC,QAAQ,CAAC,gBAAgB,EAAE,IAAI,CAAC,CAAC;AAClD,CAAC;AAED,MAAM,UAAU,iBAAiB,CAAC,OAAgB;IAChD,OAAO,OAAO,CAAC,WAAW,CAAC,gBAAgB,CAAC,CAAC;AAC/C,CAAC;AAED,MAAM,UAAU,cAAc,CAAC,OAAgB;IAC7C,OAAO,OAAO,CAAC,QAAQ,CAAC,gBAAgB,CAA4B,CAAC;AACvE,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Context, createContextKey, Span } from '@opentelemetry/api';\n\nconst RPC_METADATA_KEY = createContextKey(\n  'OpenTelemetry SDK Context Key RPC_METADATA'\n);\n\nexport enum RPCType {\n  HTTP = 'http',\n}\n\ntype HTTPMetadata = {\n  type: RPCType.HTTP;\n  route?: string;\n  span: Span;\n};\n\n/**\n * Allows for future rpc metadata to be used with this mechanism\n */\nexport type RPCMetadata = HTTPMetadata;\n\nexport function setRPCMetadata(context: Context, meta: RPCMetadata): Context {\n  return context.setValue(RPC_METADATA_KEY, meta);\n}\n\nexport function deleteRPCMetadata(context: Context): Context {\n  return context.deleteValue(RPC_METADATA_KEY);\n}\n\nexport function getRPCMetadata(context: Context): RPCMetadata | undefined {\n  return context.getValue(RPC_METADATA_KEY) as RPCMetadata | undefined;\n}\n"]}