import { ISerializer } from '../common/i-serializer';
import { IExportMetricsServiceResponse } from '../metrics/types';
import { IExportTraceServiceResponse } from '../trace/types';
import { IExportLogsServiceResponse } from '../logs/types';
import { ReadableSpan } from '@opentelemetry/sdk-trace-base';
import { ResourceMetrics } from '@opentelemetry/sdk-metrics';
import { ReadableLogRecord } from '@opentelemetry/sdk-logs';
export declare const ProtobufLogsSerializer: ISerializer<ReadableLogRecord[], IExportLogsServiceResponse>;
export declare const ProtobufMetricsSerializer: ISerializer<ResourceMetrics[], IExportMetricsServiceResponse>;
export declare const ProtobufTraceSerializer: ISerializer<ReadableSpan[], IExportTraceServiceResponse>;
//# sourceMappingURL=serializers.d.ts.map