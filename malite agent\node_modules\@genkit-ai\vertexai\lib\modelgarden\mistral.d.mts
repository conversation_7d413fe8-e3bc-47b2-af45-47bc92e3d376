import { ChatCompletionRequest, ChatCompletionChoiceFinishReason, ChatCompletionResponse, CompletionChunk } from '@mistralai/mistralai-gcp/models/components';
import { z, ModelReference, GenerateRequest, ModelResponseData, Genkit, Part } from 'genkit';
import { ModelAction } from 'genkit/model';

/**
 * Copyright 2024 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

/**
 * See https://docs.mistral.ai/api/#tag/chat/operation/chat_completion_v1_chat_completions_post
 */
declare const MistralConfigSchema: z.ZodObject<z.objectUtil.extendShape<{
    version: z.ZodOptional<z.ZodString>;
    temperature: z.ZodOptional<z.ZodNumber>;
    maxOutputTokens: z.ZodOptional<z.ZodNumber>;
    topK: z.ZodOptional<z.ZodNumber>;
    topP: z.ZodOptional<z.ZodNumber>;
    stopSequences: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
}, {
    location: z.ZodOptional<z.ZodString>;
    topP: z.ZodOptional<z.ZodNumber>;
}>, "passthrough", z.ZodTypeAny, z.objectOutputType<z.objectUtil.extendShape<{
    version: z.ZodOptional<z.ZodString>;
    temperature: z.ZodOptional<z.ZodNumber>;
    maxOutputTokens: z.ZodOptional<z.ZodNumber>;
    topK: z.ZodOptional<z.ZodNumber>;
    topP: z.ZodOptional<z.ZodNumber>;
    stopSequences: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
}, {
    location: z.ZodOptional<z.ZodString>;
    topP: z.ZodOptional<z.ZodNumber>;
}>, z.ZodTypeAny, "passthrough">, z.objectInputType<z.objectUtil.extendShape<{
    version: z.ZodOptional<z.ZodString>;
    temperature: z.ZodOptional<z.ZodNumber>;
    maxOutputTokens: z.ZodOptional<z.ZodNumber>;
    topK: z.ZodOptional<z.ZodNumber>;
    topP: z.ZodOptional<z.ZodNumber>;
    stopSequences: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
}, {
    location: z.ZodOptional<z.ZodString>;
    topP: z.ZodOptional<z.ZodNumber>;
}>, z.ZodTypeAny, "passthrough">>;
declare const mistralLarge: ModelReference<z.ZodObject<z.objectUtil.extendShape<{
    version: z.ZodOptional<z.ZodString>;
    temperature: z.ZodOptional<z.ZodNumber>;
    maxOutputTokens: z.ZodOptional<z.ZodNumber>;
    topK: z.ZodOptional<z.ZodNumber>;
    topP: z.ZodOptional<z.ZodNumber>;
    stopSequences: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
}, {
    location: z.ZodOptional<z.ZodString>;
    topP: z.ZodOptional<z.ZodNumber>;
}>, "passthrough", z.ZodTypeAny, z.objectOutputType<z.objectUtil.extendShape<{
    version: z.ZodOptional<z.ZodString>;
    temperature: z.ZodOptional<z.ZodNumber>;
    maxOutputTokens: z.ZodOptional<z.ZodNumber>;
    topK: z.ZodOptional<z.ZodNumber>;
    topP: z.ZodOptional<z.ZodNumber>;
    stopSequences: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
}, {
    location: z.ZodOptional<z.ZodString>;
    topP: z.ZodOptional<z.ZodNumber>;
}>, z.ZodTypeAny, "passthrough">, z.objectInputType<z.objectUtil.extendShape<{
    version: z.ZodOptional<z.ZodString>;
    temperature: z.ZodOptional<z.ZodNumber>;
    maxOutputTokens: z.ZodOptional<z.ZodNumber>;
    topK: z.ZodOptional<z.ZodNumber>;
    topP: z.ZodOptional<z.ZodNumber>;
    stopSequences: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
}, {
    location: z.ZodOptional<z.ZodString>;
    topP: z.ZodOptional<z.ZodNumber>;
}>, z.ZodTypeAny, "passthrough">>>;
declare const mistralNemo: ModelReference<z.ZodObject<z.objectUtil.extendShape<{
    version: z.ZodOptional<z.ZodString>;
    temperature: z.ZodOptional<z.ZodNumber>;
    maxOutputTokens: z.ZodOptional<z.ZodNumber>;
    topK: z.ZodOptional<z.ZodNumber>;
    topP: z.ZodOptional<z.ZodNumber>;
    stopSequences: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
}, {
    location: z.ZodOptional<z.ZodString>;
    topP: z.ZodOptional<z.ZodNumber>;
}>, "passthrough", z.ZodTypeAny, z.objectOutputType<z.objectUtil.extendShape<{
    version: z.ZodOptional<z.ZodString>;
    temperature: z.ZodOptional<z.ZodNumber>;
    maxOutputTokens: z.ZodOptional<z.ZodNumber>;
    topK: z.ZodOptional<z.ZodNumber>;
    topP: z.ZodOptional<z.ZodNumber>;
    stopSequences: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
}, {
    location: z.ZodOptional<z.ZodString>;
    topP: z.ZodOptional<z.ZodNumber>;
}>, z.ZodTypeAny, "passthrough">, z.objectInputType<z.objectUtil.extendShape<{
    version: z.ZodOptional<z.ZodString>;
    temperature: z.ZodOptional<z.ZodNumber>;
    maxOutputTokens: z.ZodOptional<z.ZodNumber>;
    topK: z.ZodOptional<z.ZodNumber>;
    topP: z.ZodOptional<z.ZodNumber>;
    stopSequences: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
}, {
    location: z.ZodOptional<z.ZodString>;
    topP: z.ZodOptional<z.ZodNumber>;
}>, z.ZodTypeAny, "passthrough">>>;
declare const codestral: ModelReference<z.ZodObject<z.objectUtil.extendShape<{
    version: z.ZodOptional<z.ZodString>;
    temperature: z.ZodOptional<z.ZodNumber>;
    maxOutputTokens: z.ZodOptional<z.ZodNumber>;
    topK: z.ZodOptional<z.ZodNumber>;
    topP: z.ZodOptional<z.ZodNumber>;
    stopSequences: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
}, {
    location: z.ZodOptional<z.ZodString>;
    topP: z.ZodOptional<z.ZodNumber>;
}>, "passthrough", z.ZodTypeAny, z.objectOutputType<z.objectUtil.extendShape<{
    version: z.ZodOptional<z.ZodString>;
    temperature: z.ZodOptional<z.ZodNumber>;
    maxOutputTokens: z.ZodOptional<z.ZodNumber>;
    topK: z.ZodOptional<z.ZodNumber>;
    topP: z.ZodOptional<z.ZodNumber>;
    stopSequences: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
}, {
    location: z.ZodOptional<z.ZodString>;
    topP: z.ZodOptional<z.ZodNumber>;
}>, z.ZodTypeAny, "passthrough">, z.objectInputType<z.objectUtil.extendShape<{
    version: z.ZodOptional<z.ZodString>;
    temperature: z.ZodOptional<z.ZodNumber>;
    maxOutputTokens: z.ZodOptional<z.ZodNumber>;
    topK: z.ZodOptional<z.ZodNumber>;
    topP: z.ZodOptional<z.ZodNumber>;
    stopSequences: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
}, {
    location: z.ZodOptional<z.ZodString>;
    topP: z.ZodOptional<z.ZodNumber>;
}>, z.ZodTypeAny, "passthrough">>>;
declare const SUPPORTED_MISTRAL_MODELS: Record<string, ModelReference<typeof MistralConfigSchema>>;
declare function toMistralRequest(model: string, input: GenerateRequest<typeof MistralConfigSchema>): ChatCompletionRequest;
declare function fromMistralFinishReason(reason: ChatCompletionChoiceFinishReason | undefined): 'length' | 'unknown' | 'stop' | 'blocked' | 'other';
declare function fromMistralResponse(_input: GenerateRequest<typeof MistralConfigSchema>, response: ChatCompletionResponse): ModelResponseData;
declare function mistralModel(ai: Genkit, modelName: string, projectId: string, region: string): ModelAction;
declare function fromMistralCompletionChunk(chunk: CompletionChunk): Part[];

export { MistralConfigSchema, SUPPORTED_MISTRAL_MODELS, codestral, fromMistralCompletionChunk, fromMistralFinishReason, fromMistralResponse, mistralLarge, mistralModel, mistralNemo, toMistralRequest };
