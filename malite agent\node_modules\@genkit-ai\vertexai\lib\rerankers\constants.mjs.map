{"version": 3, "sources": ["../../src/rerankers/constants.ts"], "sourcesContent": ["/**\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport const DEFAULT_MODEL = 'semantic-ranker-512@latest';\n\nexport const getRerankEndpoint = (projectId: string, location: string) => {\n  return `https://discoveryengine.googleapis.com/v1/projects/${projectId}/locations/${location}/rankingConfigs/default_ranking_config:rank`;\n};\n"], "mappings": "AAgBO,MAAM,gBAAgB;AAEtB,MAAM,oBAAoB,CAAC,WAAmB,aAAqB;AACxE,SAAO,sDAAsD,SAAS,cAAc,QAAQ;AAC9F;", "names": []}