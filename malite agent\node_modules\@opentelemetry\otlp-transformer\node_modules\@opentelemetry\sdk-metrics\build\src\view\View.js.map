{"version": 3, "file": "View.js", "sourceRoot": "", "sources": ["../../../src/view/View.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,2CAA+C;AAC/C,+DAG+B;AAC/B,6DAA0D;AAC1D,mDAAgD;AAChD,+CAA4C;AAgG5C,SAAS,qBAAqB,CAAC,OAAoB;IACjD,OAAO,CACL,OAAO,CAAC,cAAc,IAAI,IAAI;QAC9B,OAAO,CAAC,cAAc,IAAI,IAAI;QAC9B,OAAO,CAAC,cAAc,IAAI,IAAI;QAC9B,OAAO,CAAC,SAAS,IAAI,IAAI;QACzB,OAAO,CAAC,YAAY,IAAI,IAAI;QAC5B,OAAO,CAAC,cAAc,IAAI,IAAI,CAC/B,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,MAAa,IAAI;IAQf;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAmDG;IACH,YAAY,WAAwB;;QAClC,mEAAmE;QACnE,4DAA4D;QAC5D,IAAI,qBAAqB,CAAC,WAAW,CAAC,EAAE;YACtC,MAAM,IAAI,KAAK,CAAC,wDAAwD,CAAC,CAAC;SAC3E;QAED,qGAAqG;QACrG,0FAA0F;QAC1F,IACE,WAAW,CAAC,IAAI,IAAI,IAAI;YACxB,CAAC,CAAA,WAAW,aAAX,WAAW,uBAAX,WAAW,CAAE,cAAc,KAAI,IAAI;gBAClC,4BAAgB,CAAC,WAAW,CAAC,WAAW,CAAC,cAAc,CAAC,CAAC,EAC3D;YACA,MAAM,IAAI,KAAK,CACb,yHAAyH,CAC1H,CAAC;SACH;QAED,+DAA+D;QAC/D,IAAI,WAAW,CAAC,aAAa,IAAI,IAAI,EAAE;YACrC,IAAI,CAAC,mBAAmB,GAAG,IAAI,kDAA4B,CACzD,WAAW,CAAC,aAAa,CAC1B,CAAC;SACH;aAAM;YACL,IAAI,CAAC,mBAAmB,GAAG,yCAAmB,CAAC,IAAI,EAAE,CAAC;SACvD;QAED,IAAI,CAAC,IAAI,GAAG,WAAW,CAAC,IAAI,CAAC;QAC7B,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC,WAAW,CAAC;QAC3C,IAAI,CAAC,WAAW,GAAG,MAAA,WAAW,CAAC,WAAW,mCAAI,yBAAW,CAAC,OAAO,EAAE,CAAC;QACpE,IAAI,CAAC,kBAAkB,GAAG,IAAI,uCAAkB,CAAC;YAC/C,IAAI,EAAE,WAAW,CAAC,cAAc;YAChC,IAAI,EAAE,WAAW,CAAC,cAAc;YAChC,IAAI,EAAE,WAAW,CAAC,cAAc;SACjC,CAAC,CAAC;QACH,IAAI,CAAC,aAAa,GAAG,IAAI,6BAAa,CAAC;YACrC,IAAI,EAAE,WAAW,CAAC,SAAS;YAC3B,OAAO,EAAE,WAAW,CAAC,YAAY;YACjC,SAAS,EAAE,WAAW,CAAC,cAAc;SACtC,CAAC,CAAC;IACL,CAAC;CACF;AAtGD,oBAsGC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { PatternPredicate } from './Predicate';\nimport {\n  AttributesProcessor,\n  FilteringAttributesProcessor,\n} from './AttributesProcessor';\nimport { InstrumentSelector } from './InstrumentSelector';\nimport { MeterSelector } from './MeterSelector';\nimport { Aggregation } from './Aggregation';\nimport { InstrumentType } from '../InstrumentDescriptor';\n\nexport type ViewOptions = {\n  /**\n   *  Alters the metric stream:\n   *  This will be used as the name of the metrics stream.\n   *  If not provided, the original Instrument name will be used.\n   */\n  name?: string;\n  /**\n   * Alters the metric stream:\n   * This will be used as the description of the metrics stream.\n   * If not provided, the original Instrument description will be used by default.\n   *\n   * @example <caption>changes the description of all selected instruments to 'sample description'</caption>\n   * description: 'sample description'\n   */\n  description?: string;\n  /**\n   * Alters the metric stream:\n   * If provided, the attributes that are not in the list will be ignored.\n   * If not provided, all attribute keys will be used by default.\n   *\n   * @example <caption>drops all attributes with top-level keys except for 'myAttr' and 'myOtherAttr'</caption>\n   * attributeKeys: ['myAttr', 'myOtherAttr']\n   * @example <caption>drops all attributes</caption>\n   * attributeKeys: []\n   */\n  attributeKeys?: string[];\n  /**\n   * Alters the metric stream:\n   * Alters the {@link Aggregation} of the metric stream.\n   *\n   * @example <caption>changes the aggregation of the selected instrument(s) to ExplicitBucketHistogramAggregation</caption>\n   * aggregation: new ExplicitBucketHistogramAggregation([1, 10, 100])\n   * @example <caption>changes the aggregation of the selected instrument(s) to LastValueAggregation</caption>\n   * aggregation: new LastValueAggregation()\n   */\n  aggregation?: Aggregation;\n  /**\n   * Instrument selection criteria:\n   * The original type of the Instrument(s).\n   *\n   * @example <caption>selects all counters</caption>\n   * instrumentType: InstrumentType.COUNTER\n   * @example <caption>selects all histograms</caption>\n   * instrumentType: InstrumentType.HISTOGRAM\n   */\n  instrumentType?: InstrumentType;\n  /**\n   * Instrument selection criteria:\n   * Original name of the Instrument(s) with wildcard support.\n   *\n   * @example <caption>select all instruments</caption>\n   * instrumentName: '*'\n   * @example <caption>select all instruments starting with 'my.instruments.'</caption>\n   * instrumentName: 'my.instruments.*'\n   * @example <caption>select all instruments named 'my.instrument.requests' exactly</caption>\n   * instrumentName: 'my.instruments.requests'\n   */\n  instrumentName?: string;\n  /**\n   * Instrument selection criteria:\n   * The unit of the Instrument(s).\n   *\n   * @example <caption>select all instruments with unit 'ms'</caption>\n   * instrumentUnit: 'ms'\n   */\n  instrumentUnit?: string;\n  /**\n   * Instrument selection criteria:\n   * The name of the Meter. No wildcard support, name must match the meter exactly.\n   *\n   * @example <caption>select all meters named 'example.component.app' exactly</caption>\n   * meterName: 'example.component.app'\n   */\n  meterName?: string;\n  /**\n   * Instrument selection criteria:\n   * The version of the Meter. No wildcard support, version must match exactly.\n   *\n   * @example\n   * meterVersion: '1.0.1'\n   */\n  meterVersion?: string;\n  /**\n   * Instrument selection criteria:\n   * The schema URL of the Meter. No wildcard support, schema URL must match exactly.\n   *\n   * @example <caption>Select all meters with schema URL 'https://example.com/schema' exactly.</caption>\n   * meterSchemaUrl: 'https://example.com/schema'\n   */\n  meterSchemaUrl?: string;\n};\n\nfunction isSelectorNotProvided(options: ViewOptions): boolean {\n  return (\n    options.instrumentName == null &&\n    options.instrumentType == null &&\n    options.instrumentUnit == null &&\n    options.meterName == null &&\n    options.meterVersion == null &&\n    options.meterSchemaUrl == null\n  );\n}\n\n/**\n * Can be passed to a {@link MeterProvider} to select instruments and alter their metric stream.\n */\nexport class View {\n  readonly name?: string;\n  readonly description?: string;\n  readonly aggregation: Aggregation;\n  readonly attributesProcessor: AttributesProcessor;\n  readonly instrumentSelector: InstrumentSelector;\n  readonly meterSelector: MeterSelector;\n\n  /**\n   * Create a new {@link View} instance.\n   *\n   * Parameters can be categorized as two types:\n   *  Instrument selection criteria: Used to describe the instrument(s) this view will be applied to.\n   *  Will be treated as additive (the Instrument has to meet all the provided criteria to be selected).\n   *\n   *  Metric stream altering: Alter the metric stream of instruments selected by instrument selection criteria.\n   *\n   * @param viewOptions {@link ViewOptions} for altering the metric stream and instrument selection.\n   * @param viewOptions.name\n   * Alters the metric stream:\n   *  This will be used as the name of the metrics stream.\n   *  If not provided, the original Instrument name will be used.\n   * @param viewOptions.description\n   * Alters the metric stream:\n   *  This will be used as the description of the metrics stream.\n   *  If not provided, the original Instrument description will be used by default.\n   * @param viewOptions.attributeKeys\n   * Alters the metric stream:\n   *  If provided, the attributes that are not in the list will be ignored.\n   *  If not provided, all attribute keys will be used by default.\n   * @param viewOptions.aggregation\n   * Alters the metric stream:\n   *  Alters the {@link Aggregation} of the metric stream.\n   * @param viewOptions.instrumentName\n   * Instrument selection criteria:\n   *  Original name of the Instrument(s) with wildcard support.\n   * @param viewOptions.instrumentType\n   * Instrument selection criteria:\n   *  The original type of the Instrument(s).\n   * @param viewOptions.instrumentUnit\n   * Instrument selection criteria:\n   *  The unit of the Instrument(s).\n   * @param viewOptions.meterName\n   * Instrument selection criteria:\n   *  The name of the Meter. No wildcard support, name must match the meter exactly.\n   * @param viewOptions.meterVersion\n   * Instrument selection criteria:\n   *  The version of the Meter. No wildcard support, version must match exactly.\n   * @param viewOptions.meterSchemaUrl\n   * Instrument selection criteria:\n   *  The schema URL of the Meter. No wildcard support, schema URL must match exactly.\n   *\n   * @example\n   * // Create a view that changes the Instrument 'my.instrument' to use to an\n   * // ExplicitBucketHistogramAggregation with the boundaries [20, 30, 40]\n   * new View({\n   *   aggregation: new ExplicitBucketHistogramAggregation([20, 30, 40]),\n   *   instrumentName: 'my.instrument'\n   * })\n   */\n  constructor(viewOptions: ViewOptions) {\n    // If no criteria is provided, the SDK SHOULD treat it as an error.\n    // It is recommended that the SDK implementations fail fast.\n    if (isSelectorNotProvided(viewOptions)) {\n      throw new Error('Cannot create view with no selector arguments supplied');\n    }\n\n    // the SDK SHOULD NOT allow Views with a specified name to be declared with instrument selectors that\n    // may select more than one instrument (e.g. wild card instrument name) in the same Meter.\n    if (\n      viewOptions.name != null &&\n      (viewOptions?.instrumentName == null ||\n        PatternPredicate.hasWildcard(viewOptions.instrumentName))\n    ) {\n      throw new Error(\n        'Views with a specified name must be declared with an instrument selector that selects at most one instrument per meter.'\n      );\n    }\n\n    // Create AttributesProcessor if attributeKeys are defined set.\n    if (viewOptions.attributeKeys != null) {\n      this.attributesProcessor = new FilteringAttributesProcessor(\n        viewOptions.attributeKeys\n      );\n    } else {\n      this.attributesProcessor = AttributesProcessor.Noop();\n    }\n\n    this.name = viewOptions.name;\n    this.description = viewOptions.description;\n    this.aggregation = viewOptions.aggregation ?? Aggregation.Default();\n    this.instrumentSelector = new InstrumentSelector({\n      name: viewOptions.instrumentName,\n      type: viewOptions.instrumentType,\n      unit: viewOptions.instrumentUnit,\n    });\n    this.meterSelector = new MeterSelector({\n      name: viewOptions.meterName,\n      version: viewOptions.meterVersion,\n      schemaUrl: viewOptions.meterSchemaUrl,\n    });\n  }\n}\n"]}