import { Vertex<PERSON><PERSON> } from "@google-cloud/vertexai";
import { GoogleAuth } from "google-auth-library";
import { CLOUD_PLATFORM_OAUTH_SCOPE } from "./constants.js";
function parseFirebaseProjectId() {
  if (!process.env.FIREBASE_CONFIG) return void 0;
  try {
    return JSON.parse(process.env.FIREBASE_CONFIG).projectId;
  } catch {
    return void 0;
  }
}
function __setFakeDerivedParams(params) {
  __fake_getDerivedParams = params;
}
let __fake_getDerivedParams;
async function getDerivedParams(options) {
  if (__fake_getDerivedParams) {
    return __fake_getDerivedParams;
  }
  let authOptions = options?.googleAuth;
  let authClient;
  const providedProjectId = options?.projectId || process.env.GCLOUD_PROJECT || parseFirebaseProjectId();
  if (process.env.GCLOUD_SERVICE_ACCOUNT_CREDS) {
    const serviceAccountCreds = JSON.parse(
      process.env.GCLOUD_SERVICE_ACCOUNT_CREDS
    );
    authOptions = {
      credentials: serviceAccountCreds,
      scopes: [CLOUD_PLATFORM_OAUTH_SCOPE],
      projectId: providedProjectId
    };
    authClient = new GoogleAuth(authOptions);
  } else {
    authClient = new GoogleAuth(
      authOptions ?? {
        scopes: [CLOUD_PLATFORM_OAUTH_SCOPE],
        projectId: providedProjectId
      }
    );
  }
  const projectId = options?.projectId || await authClient.getProjectId();
  const location = options?.location || "us-central1";
  if (!location) {
    throw new Error(
      `VertexAI Plugin is missing the 'location' configuration. Please set the 'GCLOUD_LOCATION' environment variable or explicitly pass 'location' into genkit config.`
    );
  }
  if (!projectId) {
    throw new Error(
      `VertexAI Plugin is missing the 'project' configuration. Please set the 'GCLOUD_PROJECT' environment variable or explicitly pass 'project' into genkit config.`
    );
  }
  const vertexClientFactoryCache = {};
  const vertexClientFactory = (request) => {
    const requestLocation = request.config?.location || location;
    if (!vertexClientFactoryCache[requestLocation]) {
      vertexClientFactoryCache[requestLocation] = new VertexAI({
        project: projectId,
        location: requestLocation,
        googleAuthOptions: { projectId: providedProjectId, ...authOptions }
      });
    }
    return vertexClientFactoryCache[requestLocation];
  };
  return {
    location,
    projectId,
    vertexClientFactory,
    authClient
  };
}
export {
  __setFakeDerivedParams,
  getDerivedParams
};
//# sourceMappingURL=index.mjs.map