import { DetectorSync } from '../types';
import { ResourceDetectionConfig } from '../config';
import { IResource } from '../IResource';
/**
 * BrowserDetectorSync will be used to detect the resources related to browser.
 */
declare class BrowserDetectorSync implements DetectorSync {
    detect(config?: ResourceDetectionConfig): IResource;
    /**
     * Validates process resource attribute map from process variables
     *
     * @param browserResource The un-sanitized resource attributes from process as key/value pairs.
     * @param config: Config
     * @returns The sanitized resource attributes.
     */
    private _getResourceAttributes;
}
export declare const browserDetectorSync: BrowserDetectorSync;
export {};
//# sourceMappingURL=BrowserDetectorSync.d.ts.map