"use strict";
var __create = Object.create;
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __getProtoOf = Object.getPrototypeOf;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(
  // If the importer is in node compatibility mode or this is not an ESM
  // file that has been converted to a CommonJS file using a Babel-
  // compatible transform (i.e. "__esModule" has not been set), then set
  // "default" to the CommonJS "module.exports" for node compatibility.
  isNodeMode || !mod || !mod.__esModule ? __defProp(target, "default", { value: mod, enumerable: true }) : target,
  mod
));
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
var types_exports = {};
__export(types_exports, {
  CrowdingTagSchema: () => CrowdingTagSchema,
  Datapoint: () => Datapoint,
  FindNeighborsResponseSchema: () => FindNeighborsResponseSchema,
  NeighborSchema: () => NeighborSchema,
  NumericRestrictionOperatorSchema: () => NumericRestrictionOperatorSchema,
  NumericRestrictionSchema: () => NumericRestrictionSchema,
  RestrictionSchema: () => RestrictionSchema,
  SparseEmbeddingSchema: () => SparseEmbeddingSchema,
  VertexAIVectorIndexerOptionsSchema: () => VertexAIVectorIndexerOptionsSchema,
  VertexAIVectorRetrieverOptionsSchema: () => VertexAIVectorRetrieverOptionsSchema
});
module.exports = __toCommonJS(types_exports);
var aiplatform = __toESM(require("@google-cloud/aiplatform"));
var import_genkit = require("genkit");
var import_retriever = require("genkit/retriever");
class Datapoint extends aiplatform.protos.google.cloud.aiplatform.v1.IndexDatapoint {
  constructor(properties) {
    super(properties);
  }
}
const SparseEmbeddingSchema = import_genkit.z.object({
  values: import_genkit.z.array(import_genkit.z.number()).optional(),
  dimensions: import_genkit.z.array(import_genkit.z.union([import_genkit.z.number(), import_genkit.z.string()])).optional()
});
const RestrictionSchema = import_genkit.z.object({
  namespace: import_genkit.z.string(),
  allowList: import_genkit.z.array(import_genkit.z.string()),
  denyList: import_genkit.z.array(import_genkit.z.string())
});
const NumericRestrictionOperatorSchema = import_genkit.z.enum([
  "OPERATOR_UNSPECIFIED",
  "LESS",
  "LESS_EQUAL",
  "EQUAL",
  "GREATER_EQUAL",
  "GREATER",
  "NOT_EQUAL"
]);
const NumericRestrictionSchema = import_genkit.z.object({
  valueInt: import_genkit.z.union([import_genkit.z.number(), import_genkit.z.string()]).optional(),
  valueFloat: import_genkit.z.number().optional(),
  valueDouble: import_genkit.z.number().optional(),
  namespace: import_genkit.z.string(),
  op: import_genkit.z.union([NumericRestrictionOperatorSchema, import_genkit.z.null()]).optional()
});
const CrowdingTagSchema = import_genkit.z.object({
  crowdingAttribute: import_genkit.z.string().optional()
});
const IndexDatapointSchema = import_genkit.z.object({
  datapointId: import_genkit.z.string().optional(),
  featureVector: import_genkit.z.array(import_genkit.z.number()).optional(),
  sparseEmbedding: SparseEmbeddingSchema.optional(),
  restricts: import_genkit.z.array(RestrictionSchema).optional(),
  numericRestricts: import_genkit.z.array(NumericRestrictionSchema).optional(),
  crowdingTag: CrowdingTagSchema.optional()
});
const NeighborSchema = import_genkit.z.object({
  datapoint: IndexDatapointSchema.optional(),
  distance: import_genkit.z.number().optional(),
  sparseDistance: import_genkit.z.number().optional()
});
const NearestNeighborsSchema = import_genkit.z.object({
  id: import_genkit.z.string().optional(),
  neighbors: import_genkit.z.array(NeighborSchema).optional()
});
const FindNeighborsResponseSchema = import_genkit.z.object({
  nearestNeighbors: import_genkit.z.array(NearestNeighborsSchema).optional()
});
function assertTypeEquality(value) {
}
assertTypeEquality({});
assertTypeEquality({});
const VertexAIVectorRetrieverOptionsSchema = import_retriever.CommonRetrieverOptionsSchema.extend({}).optional();
const VertexAIVectorIndexerOptionsSchema = import_genkit.z.any();
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  CrowdingTagSchema,
  Datapoint,
  FindNeighborsResponseSchema,
  NeighborSchema,
  NumericRestrictionOperatorSchema,
  NumericRestrictionSchema,
  RestrictionSchema,
  SparseEmbeddingSchema,
  VertexAIVectorIndexerOptionsSchema,
  VertexAIVectorRetrieverOptionsSchema
});
//# sourceMappingURL=types.js.map