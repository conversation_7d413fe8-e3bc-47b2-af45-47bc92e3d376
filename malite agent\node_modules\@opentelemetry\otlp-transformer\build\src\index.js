"use strict";
/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.JsonMetricsSerializer = exports.JsonLogsSerializer = exports.JsonTraceSerializer = exports.ProtobufTraceSerializer = exports.ProtobufMetricsSerializer = exports.ProtobufLogsSerializer = exports.createExportLogsServiceRequest = exports.createExportMetricsServiceRequest = exports.createExportTraceServiceRequest = exports.ESpanKind = exports.hrTimeToNanos = exports.encodeAsString = exports.encodeAsLongBits = exports.getOtlpEncoder = exports.toLongBits = void 0;
var common_1 = require("./common");
Object.defineProperty(exports, "toLongBits", { enumerable: true, get: function () { return common_1.toLongBits; } });
Object.defineProperty(exports, "getOtlpEncoder", { enumerable: true, get: function () { return common_1.getOtlpEncoder; } });
Object.defineProperty(exports, "encodeAsLongBits", { enumerable: true, get: function () { return common_1.encodeAsLongBits; } });
Object.defineProperty(exports, "encodeAsString", { enumerable: true, get: function () { return common_1.encodeAsString; } });
Object.defineProperty(exports, "hrTimeToNanos", { enumerable: true, get: function () { return common_1.hrTimeToNanos; } });
var types_1 = require("./trace/types");
Object.defineProperty(exports, "ESpanKind", { enumerable: true, get: function () { return types_1.ESpanKind; } });
var trace_1 = require("./trace");
Object.defineProperty(exports, "createExportTraceServiceRequest", { enumerable: true, get: function () { return trace_1.createExportTraceServiceRequest; } });
var metrics_1 = require("./metrics");
Object.defineProperty(exports, "createExportMetricsServiceRequest", { enumerable: true, get: function () { return metrics_1.createExportMetricsServiceRequest; } });
var logs_1 = require("./logs");
Object.defineProperty(exports, "createExportLogsServiceRequest", { enumerable: true, get: function () { return logs_1.createExportLogsServiceRequest; } });
var serializers_1 = require("./protobuf/serializers");
Object.defineProperty(exports, "ProtobufLogsSerializer", { enumerable: true, get: function () { return serializers_1.ProtobufLogsSerializer; } });
Object.defineProperty(exports, "ProtobufMetricsSerializer", { enumerable: true, get: function () { return serializers_1.ProtobufMetricsSerializer; } });
Object.defineProperty(exports, "ProtobufTraceSerializer", { enumerable: true, get: function () { return serializers_1.ProtobufTraceSerializer; } });
var serializers_2 = require("./json/serializers");
Object.defineProperty(exports, "JsonTraceSerializer", { enumerable: true, get: function () { return serializers_2.JsonTraceSerializer; } });
Object.defineProperty(exports, "JsonLogsSerializer", { enumerable: true, get: function () { return serializers_2.JsonLogsSerializer; } });
Object.defineProperty(exports, "JsonMetricsSerializer", { enumerable: true, get: function () { return serializers_2.JsonMetricsSerializer; } });
//# sourceMappingURL=index.js.map