"use strict";
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.parse = parse;
exports.safeParse = safeParse;
exports.collectExtraKeys = collectExtraKeys;
const zod_1 = require("zod");
const sdkvalidationerror_js_1 = require("../models/errors/sdkvalidationerror.js");
const fp_js_1 = require("../types/fp.js");
/**
 * Utility function that executes some code which may throw a ZodError. It
 * intercepts this error and converts it to an SDKValidationError so as to not
 * leak Zod implementation details to user code.
 */
function parse(rawValue, fn, errorMessage) {
    try {
        return fn(rawValue);
    }
    catch (err) {
        if (err instanceof zod_1.ZodError) {
            throw new sdkvalidationerror_js_1.SDKValidationError(errorMessage, err, rawValue);
        }
        throw err;
    }
}
/**
 * Utility function that executes some code which may result in a ZodError. It
 * intercepts this error and converts it to an SDKValidationError so as to not
 * leak Zod implementation details to user code.
 */
function safeParse(rawValue, fn, errorMessage) {
    try {
        return (0, fp_js_1.OK)(fn(rawValue));
    }
    catch (err) {
        return (0, fp_js_1.ERR)(new sdkvalidationerror_js_1.SDKValidationError(errorMessage, err, rawValue));
    }
}
function collectExtraKeys(obj, extrasKey, optional) {
    return obj.transform((val) => {
        const extras = {};
        const { shape } = obj;
        for (const [key] of Object.entries(val)) {
            if (key in shape) {
                continue;
            }
            const v = val[key];
            if (typeof v === "undefined") {
                continue;
            }
            extras[key] = v;
            delete val[key];
        }
        if (optional && Object.keys(extras).length === 0) {
            return val;
        }
        return { ...val, [extrasKey]: extras };
    });
}
//# sourceMappingURL=schemas.js.map