#!/bin/bash

# Script لبناء حاوية Docker لتطبيق Malite Agent
# يدعم بناء الحاوية للتطوير والإنتاج

set -e

# ألوان للنص
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# دوال مساعدة
print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_header() {
    echo -e "${BLUE}"
    echo "=================================================="
    echo "🐳 $1"
    echo "=================================================="
    echo -e "${NC}"
}

# التحقق من المتطلبات
check_requirements() {
    print_header "التحقق من المتطلبات"
    
    # التحقق من وجود Docker
    if ! command -v docker &> /dev/null; then
        print_error "Docker غير مثبت. يرجى تثبيته من: https://docs.docker.com/get-docker/"
        exit 1
    fi
    print_success "Docker مثبت"
    
    # التحقق من تشغيل Docker
    if ! docker info &> /dev/null; then
        print_error "Docker غير يعمل. يرجى تشغيل Docker Desktop"
        exit 1
    fi
    print_success "Docker يعمل بشكل صحيح"
    
    # التحقق من وجود Dockerfile
    if [ ! -f "Dockerfile" ]; then
        print_error "ملف Dockerfile غير موجود"
        exit 1
    fi
    print_success "ملف Dockerfile موجود"
}

# بناء الصورة
build_image() {
    local image_name=$1
    local tag=$2
    local build_args=$3
    
    print_header "بناء صورة Docker"
    
    print_info "اسم الصورة: $image_name:$tag"
    print_info "بدء عملية البناء..."
    
    # بناء الصورة
    if [ -n "$build_args" ]; then
        docker build $build_args -t "$image_name:$tag" -t "$image_name:latest" .
    else
        docker build -t "$image_name:$tag" -t "$image_name:latest" .
    fi
    
    print_success "تم بناء الصورة بنجاح"
    
    # عرض معلومات الصورة
    print_info "معلومات الصورة:"
    docker images "$image_name" --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}\t{{.CreatedAt}}"
}

# اختبار الصورة
test_image() {
    local image_name=$1
    local tag=$2
    
    print_header "اختبار الصورة"
    
    print_info "تشغيل اختبار سريع للصورة..."
    
    # تشغيل حاوية مؤقتة للاختبار
    container_id=$(docker run -d -p 8081:8080 --name malite-test "$image_name:$tag")
    
    # انتظار حتى تصبح الحاوية جاهزة
    print_info "انتظار تشغيل الحاوية..."
    sleep 10
    
    # اختبار فحص الصحة
    if curl -f -s http://localhost:8081/health > /dev/null; then
        print_success "اختبار فحص الصحة نجح"
        test_passed=true
    else
        print_warning "اختبار فحص الصحة فشل"
        test_passed=false
    fi
    
    # إيقاف وحذف الحاوية المؤقتة
    docker stop $container_id > /dev/null
    docker rm $container_id > /dev/null
    
    if [ "$test_passed" = true ]; then
        print_success "جميع الاختبارات نجحت"
    else
        print_warning "بعض الاختبارات فشلت"
    fi
}

# رفع الصورة إلى Registry
push_image() {
    local image_name=$1
    local tag=$2
    local registry=$3
    
    if [ -n "$registry" ]; then
        print_header "رفع الصورة إلى Registry"
        
        # إعادة تسمية الصورة للـ Registry
        registry_image="$registry/$image_name"
        docker tag "$image_name:$tag" "$registry_image:$tag"
        docker tag "$image_name:$tag" "$registry_image:latest"
        
        print_info "رفع الصورة إلى $registry..."
        docker push "$registry_image:$tag"
        docker push "$registry_image:latest"
        
        print_success "تم رفع الصورة بنجاح"
        print_info "الصورة متاحة على: $registry_image:$tag"
    fi
}

# تنظيف الصور القديمة
cleanup_old_images() {
    print_header "تنظيف الصور القديمة"
    
    print_info "حذف الصور غير المستخدمة..."
    docker image prune -f
    
    print_info "حذف الحاويات المتوقفة..."
    docker container prune -f
    
    print_success "تم التنظيف بنجاح"
}

# عرض معلومات الاستخدام
show_usage() {
    echo "الاستخدام: $0 [OPTIONS]"
    echo ""
    echo "الخيارات:"
    echo "  -n, --name NAME          اسم الصورة (افتراضي: malite-agent)"
    echo "  -t, --tag TAG           علامة الصورة (افتراضي: latest)"
    echo "  -r, --registry REGISTRY  رفع إلى Registry"
    echo "  -e, --env ENV           بيئة البناء (dev|prod)"
    echo "  --no-test               تخطي الاختبارات"
    echo "  --no-cache              بناء بدون استخدام Cache"
    echo "  --cleanup               تنظيف الصور القديمة"
    echo "  -h, --help              عرض هذه المساعدة"
    echo ""
    echo "أمثلة:"
    echo "  $0                                    # بناء أساسي"
    echo "  $0 -n myapp -t v1.0.0               # بناء مع اسم وعلامة مخصصة"
    echo "  $0 -r gcr.io/my-project             # بناء ورفع إلى GCR"
    echo "  $0 -e prod --no-test                # بناء للإنتاج بدون اختبارات"
}

# الدالة الرئيسية
main() {
    # القيم الافتراضية
    IMAGE_NAME="malite-agent"
    TAG="latest"
    REGISTRY=""
    ENVIRONMENT="dev"
    RUN_TESTS=true
    USE_CACHE=true
    CLEANUP=false
    
    # معالجة المعاملات
    while [[ $# -gt 0 ]]; do
        case $1 in
            -n|--name)
                IMAGE_NAME="$2"
                shift 2
                ;;
            -t|--tag)
                TAG="$2"
                shift 2
                ;;
            -r|--registry)
                REGISTRY="$2"
                shift 2
                ;;
            -e|--env)
                ENVIRONMENT="$2"
                shift 2
                ;;
            --no-test)
                RUN_TESTS=false
                shift
                ;;
            --no-cache)
                USE_CACHE=false
                shift
                ;;
            --cleanup)
                CLEANUP=true
                shift
                ;;
            -h|--help)
                show_usage
                exit 0
                ;;
            *)
                print_error "خيار غير معروف: $1"
                show_usage
                exit 1
                ;;
        esac
    done
    
    print_header "بناء حاوية Malite Agent"
    
    # إعداد معاملات البناء
    BUILD_ARGS=""
    if [ "$USE_CACHE" = false ]; then
        BUILD_ARGS="--no-cache"
    fi
    
    if [ "$ENVIRONMENT" = "prod" ]; then
        BUILD_ARGS="$BUILD_ARGS --build-arg NODE_ENV=production"
        TAG="${TAG}-prod"
    fi
    
    # تنفيذ خطوات البناء
    check_requirements
    build_image "$IMAGE_NAME" "$TAG" "$BUILD_ARGS"
    
    if [ "$RUN_TESTS" = true ]; then
        test_image "$IMAGE_NAME" "$TAG"
    fi
    
    if [ -n "$REGISTRY" ]; then
        push_image "$IMAGE_NAME" "$TAG" "$REGISTRY"
    fi
    
    if [ "$CLEANUP" = true ]; then
        cleanup_old_images
    fi
    
    print_header "اكتمل البناء بنجاح"
    print_success "الصورة جاهزة: $IMAGE_NAME:$TAG"
    
    echo ""
    echo "🚀 أوامر مفيدة:"
    echo "   تشغيل الحاوية: docker run -p 8080:8080 $IMAGE_NAME:$TAG"
    echo "   عرض السجلات: docker logs <container_id>"
    echo "   دخول الحاوية: docker exec -it <container_id> sh"
}

# تشغيل الدالة الرئيسية
main "$@"
