export { GoogleAuthOptions } from 'google-auth-library';
/**
 * @type {module:common/operation}
 * @private
 */
export { Operation } from './operation';
/**
 * @type {module:common/service}
 * @private
 */
export { Service, ServiceConfig, ServiceOptions, StreamRequestOptions, } from './service';
/**
 * @type {module:common/serviceObject}
 * @private
 */
export { DeleteCallback, ExistsCallback, GetConfig, InstanceResponseCallback, Interceptor, Metadata, MetadataCallback, MetadataResponse, Methods, ResponseCallback, ServiceObject, ServiceObjectConfig, ServiceObjectParent, SetMetadataResponse, } from './service-object';
/**
 * @type {module:common/util}
 * @private
 */
export { Abortable, AbortableDuplex, ApiError, BodyResponseCallback, DecorateRequestOptions, ResponseBody, util, } from './util';
