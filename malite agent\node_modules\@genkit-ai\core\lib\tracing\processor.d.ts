import { Context } from '@opentelemetry/api';
import { SpanProcessor, Span, ReadableSpan } from '@opentelemetry/sdk-trace-base';

/**
 * Copyright 2024 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

declare class GenkitSpanProcessorWrapper implements SpanProcessor {
    private processor;
    constructor(processor: SpanProcessor);
    forceFlush(): Promise<void>;
    onStart(span: Span, parentContext: Context): void;
    onEnd(span: ReadableSpan): void;
    shutdown(): Promise<void>;
}

export { GenkitSpanProcessorWrapper };
