"use strict";
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SDKValidationError = void 0;
exports.formatZodError = formatZodError;
const z = __importStar(require("zod"));
class SDKValidationError extends Error {
    constructor(message, cause, rawValue) {
        super(`${message}: ${cause}`);
        this.name = "SDKValidationError";
        this.cause = cause;
        this.rawValue = rawValue;
        this.rawMessage = message;
    }
    /**
     * Return a pretty-formatted error message if the underlying validation error
     * is a ZodError or some other recognized error type, otherwise return the
     * default error message.
     */
    pretty() {
        if (this.cause instanceof z.ZodError) {
            return `${this.rawMessage}\n${formatZodError(this.cause)}`;
        }
        else {
            return this.toString();
        }
    }
}
exports.SDKValidationError = SDKValidationError;
function formatZodError(err, level = 0) {
    let pre = "  ".repeat(level);
    pre = level > 0 ? `│${pre}` : pre;
    pre += " ".repeat(level);
    let message = "";
    const append = (str) => (message += `\n${pre}${str}`);
    const len = err.issues.length;
    const headline = len === 1 ? `${len} issue found` : `${len} issues found`;
    if (len) {
        append(`┌ ${headline}:`);
    }
    for (const issue of err.issues) {
        let path = issue.path.join(".");
        path = path ? `<root>.${path}` : "<root>";
        append(`│ • [${path}]: ${issue.message} (${issue.code})`);
        switch (issue.code) {
            case "invalid_literal":
            case "invalid_type": {
                append(`│     Want: ${issue.expected}`);
                append(`│      Got: ${issue.received}`);
                break;
            }
            case "unrecognized_keys": {
                append(`│     Keys: ${issue.keys.join(", ")}`);
                break;
            }
            case "invalid_enum_value": {
                append(`│     Allowed: ${issue.options.join(", ")}`);
                append(`│         Got: ${issue.received}`);
                break;
            }
            case "invalid_union_discriminator": {
                append(`│     Allowed: ${issue.options.join(", ")}`);
                break;
            }
            case "invalid_union": {
                const len = issue.unionErrors.length;
                append(`│   ✖︎ Attemped to deserialize into one of ${len} union members:`);
                issue.unionErrors.forEach((err, i) => {
                    append(`│   ✖︎ Member ${i + 1} of ${len}`);
                    append(`${formatZodError(err, level + 1)}`);
                });
            }
        }
    }
    if (err.issues.length) {
        append(`└─*`);
    }
    return message.slice(1);
}
//# sourceMappingURL=sdkvalidationerror.js.map