{"name": "@google-cloud/common", "description": "Common components for Cloud APIs Node.js Client Libraries", "version": "5.0.2", "license": "Apache-2.0", "author": "Google Inc.", "engines": {"node": ">=14.0.0"}, "repository": "googleapis/nodejs-common", "main": "./build/src/index.js", "types": "./build/src/index.d.ts", "files": ["build/src", "!build/src/**/*.map"], "scripts": {"docs": "compodoc src/", "test": "c8 mocha build/test", "prepare": "npm run compile", "pretest": "npm run compile", "compile": "tsc -p .", "fix": "gts fix", "lint": "gts check", "presystem-test": "npm run compile", "system-test": "mocha build/system-test", "samples-test": "cd samples/ && npm link ../ && npm test && cd ../", "docs-test": "linkinator docs", "predocs-test": "npm run docs", "prelint": "cd samples; npm link ../; npm install", "clean": "gts clean", "precompile": "gts clean"}, "dependencies": {"@google-cloud/projectify": "^4.0.0", "@google-cloud/promisify": "^4.0.0", "arrify": "^2.0.1", "duplexify": "^4.1.1", "extend": "^3.0.2", "google-auth-library": "^9.0.0", "html-entities": "^2.5.2", "retry-request": "^7.0.0", "teeny-request": "^9.0.0"}, "devDependencies": {"@compodoc/compodoc": "1.1.23", "@types/ent": "^2.2.1", "@types/extend": "^3.0.1", "@types/mocha": "^9.0.0", "@types/mv": "^2.1.0", "@types/ncp": "^2.0.3", "@types/node": "^20.4.9", "@types/proxyquire": "^1.3.28", "@types/request": "^2.48.4", "@types/sinon": "^17.0.0", "@types/tmp": "0.2.6", "c8": "^8.0.1", "codecov": "^3.6.5", "gts": "^5.0.0", "linkinator": "^4.0.0", "mocha": "^9.2.2", "mv": "^2.1.1", "ncp": "^2.0.0", "nock": "^13.0.0", "proxyquire": "^2.1.3", "sinon": "^17.0.0", "tmp": "0.2.3", "typescript": "^5.1.6"}}