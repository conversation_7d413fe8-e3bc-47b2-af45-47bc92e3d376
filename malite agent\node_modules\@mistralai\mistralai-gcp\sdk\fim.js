"use strict";
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.Fim = void 0;
const fimComplete_js_1 = require("../funcs/fimComplete.js");
const fimStream_js_1 = require("../funcs/fimStream.js");
const sdks_js_1 = require("../lib/sdks.js");
const fp_js_1 = require("../types/fp.js");
class Fim extends sdks_js_1.ClientSDK {
    /**
     * Stream fim completion
     *
     * @remarks
     * Mistral AI provides the ability to stream responses back to a client in order to allow partial results for certain requests. Tokens will be sent as data-only server-sent events as they become available, with the stream terminated by a data: [DONE] message. Otherwise, the server will hold the request open until the timeout or until completion, with the response containing the full result as JSON.
     */
    async stream(request, options) {
        return (0, fp_js_1.unwrapAsync)((0, fimStream_js_1.fimStream)(this, request, options));
    }
    /**
     * Fim Completion
     *
     * @remarks
     * FIM completion.
     */
    async complete(request, options) {
        return (0, fp_js_1.unwrapAsync)((0, fimComplete_js_1.fimComplete)(this, request, options));
    }
}
exports.Fim = Fim;
//# sourceMappingURL=fim.js.map