/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
export { AggregationTemporality } from './export/AggregationTemporality';
export { DataPointType, } from './export/MetricData';
export { MetricReader } from './export/MetricReader';
export { PeriodicExportingMetricReader, } from './export/PeriodicExportingMetricReader';
export { InMemoryMetricExporter } from './export/InMemoryMetricExporter';
export { ConsoleMetricExporter } from './export/ConsoleMetricExporter';
export { InstrumentType } from './InstrumentDescriptor';
export { MeterProvider } from './MeterProvider';
export { DefaultAggregation, ExplicitBucketHistogramAggregation, ExponentialHistogramAggregation, DropAggregation, HistogramAggregation, LastValueAggregation, SumAggregation, Aggregation, } from './view/Aggregation';
export { View } from './view/View';
export { TimeoutError } from './utils';
//# sourceMappingURL=index.js.map