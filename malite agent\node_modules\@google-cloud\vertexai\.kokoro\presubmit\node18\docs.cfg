# doc publications use a Python image.
env_vars: {
    key: "TRAMPOLINE_IMAGE"
    value: "gcr.io/cloud-devrel-kokoro-resources/node:18-user"
}

# Download trampoline resources.
gfile_resources: "/bigstore/cloud-devrel-kokoro-resources/trampoline"

# Use the trampoline script to run in docker.
build_file: "nodejs-vertexai/.kokoro/trampoline_v2.sh"

env_vars: {
    key: "TRAMPOLINE_BUILD_FILE"
    value: "github/nodejs-vertexai/.kokoro/presubmit/node18/docs.sh"
}