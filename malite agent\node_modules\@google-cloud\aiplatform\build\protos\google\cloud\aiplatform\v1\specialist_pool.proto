// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.aiplatform.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Cloud.AIPlatform.V1";
option go_package = "cloud.google.com/go/aiplatform/apiv1/aiplatformpb;aiplatformpb";
option java_multiple_files = true;
option java_outer_classname = "SpecialistPoolProto";
option java_package = "com.google.cloud.aiplatform.v1";
option php_namespace = "Google\\Cloud\\AIPlatform\\V1";
option ruby_package = "Google::Cloud::AIPlatform::V1";

// SpecialistPool represents customers' own workforce to work on their data
// labeling jobs. It includes a group of specialist managers and workers.
// Managers are responsible for managing the workers in this pool as well as
// customers' data labeling jobs associated with this pool. Customers create
// specialist pool as well as start data labeling jobs on Cloud, managers and
// workers handle the jobs using CrowdCompute console.
message SpecialistPool {
  option (google.api.resource) = {
    type: "aiplatform.googleapis.com/SpecialistPool"
    pattern: "projects/{project}/locations/{location}/specialistPools/{specialist_pool}"
  };

  // Required. The resource name of the SpecialistPool.
  string name = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The user-defined name of the SpecialistPool.
  // The name can be up to 128 characters long and can consist of any UTF-8
  // characters.
  // This field should be unique on project-level.
  string display_name = 2 [(google.api.field_behavior) = REQUIRED];

  // Output only. The number of managers in this SpecialistPool.
  int32 specialist_managers_count = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // The email addresses of the managers in the SpecialistPool.
  repeated string specialist_manager_emails = 4;

  // Output only. The resource name of the pending data labeling jobs.
  repeated string pending_data_labeling_jobs = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // The email addresses of workers in the SpecialistPool.
  repeated string specialist_worker_emails = 7;
}
