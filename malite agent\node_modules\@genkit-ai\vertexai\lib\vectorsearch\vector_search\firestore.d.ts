import { Firestore } from 'firebase-admin/firestore';
import { b as DocumentRetriever, D as DocumentIndexer } from '../../types-gLlb90fM.js';
import 'genkit';
import '../../types-Bc0LKM8D.js';
import 'google-auth-library';
import '@google-cloud/vertexai';
import 'genkit/model';
import '@google-cloud/aiplatform';
import 'genkit/embedder';
import 'genkit/retriever';

/**
 * Copyright 2024 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

/**
 * Creates a Firestore Document Retriever.
 *
 * This function returns a DocumentRetriever function that retrieves documents
 * from a Firestore collection based on the provided Vertex AI Vector Search neighbors.
 *
 * @param {Firestore} db - The Firestore instance.
 * @param {string} collectionName - The name of the Firestore collection.
 * @returns {DocumentRetriever} - The DocumentRetriever function.
 */
declare const getFirestoreDocumentRetriever: (db: Firestore, collectionName: string) => DocumentRetriever;
/**
 * Creates a Firestore Document Indexer.
 *
 * This function returns a DocumentIndexer function that indexes documents
 * into a Firestore collection.
 *
 * @param {Firestore} db - The Firestore instance.
 * @param {string} collectionName - The name of the Firestore collection.
 * @returns {DocumentIndexer} - The DocumentIndexer function.
 */
declare const getFirestoreDocumentIndexer: (db: Firestore, collectionName: string) => DocumentIndexer<{}>;

export { getFirestoreDocumentIndexer, getFirestoreDocumentRetriever };
