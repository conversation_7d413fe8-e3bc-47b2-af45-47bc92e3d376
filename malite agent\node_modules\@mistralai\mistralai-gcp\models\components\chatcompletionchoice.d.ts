import * as z from "zod";
import { OpenEnum } from "../../types/enums.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import { AssistantMessage, AssistantMessage$Outbound } from "./assistantmessage.js";
export declare const ChatCompletionChoiceFinishReason: {
    readonly Stop: "stop";
    readonly Length: "length";
    readonly ModelLength: "model_length";
    readonly Error: "error";
    readonly ToolCalls: "tool_calls";
};
export type ChatCompletionChoiceFinishReason = OpenEnum<typeof ChatCompletionChoiceFinishReason>;
export type ChatCompletionChoice = {
    index: number;
    message: AssistantMessage;
    finishReason: ChatCompletionChoiceFinishReason;
};
/** @internal */
export declare const ChatCompletionChoiceFinishReason$inboundSchema: z.ZodType<ChatCompletionChoiceFinishReason, z.ZodTypeDef, unknown>;
/** @internal */
export declare const ChatCompletionChoiceFinishReason$outboundSchema: z.ZodType<ChatCompletionChoiceFinishReason, z.ZodTypeDef, ChatCompletionChoiceFinishReason>;
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export declare namespace ChatCompletionChoiceFinishReason$ {
    /** @deprecated use `ChatCompletionChoiceFinishReason$inboundSchema` instead. */
    const inboundSchema: z.ZodType<ChatCompletionChoiceFinishReason, z.ZodTypeDef, unknown>;
    /** @deprecated use `ChatCompletionChoiceFinishReason$outboundSchema` instead. */
    const outboundSchema: z.ZodType<ChatCompletionChoiceFinishReason, z.ZodTypeDef, ChatCompletionChoiceFinishReason>;
}
/** @internal */
export declare const ChatCompletionChoice$inboundSchema: z.ZodType<ChatCompletionChoice, z.ZodTypeDef, unknown>;
/** @internal */
export type ChatCompletionChoice$Outbound = {
    index: number;
    message: AssistantMessage$Outbound;
    finish_reason: string;
};
/** @internal */
export declare const ChatCompletionChoice$outboundSchema: z.ZodType<ChatCompletionChoice$Outbound, z.ZodTypeDef, ChatCompletionChoice>;
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export declare namespace ChatCompletionChoice$ {
    /** @deprecated use `ChatCompletionChoice$inboundSchema` instead. */
    const inboundSchema: z.ZodType<ChatCompletionChoice, z.ZodTypeDef, unknown>;
    /** @deprecated use `ChatCompletionChoice$outboundSchema` instead. */
    const outboundSchema: z.ZodType<ChatCompletionChoice$Outbound, z.ZodTypeDef, ChatCompletionChoice>;
    /** @deprecated use `ChatCompletionChoice$Outbound` instead. */
    type Outbound = ChatCompletionChoice$Outbound;
}
export declare function chatCompletionChoiceToJSON(chatCompletionChoice: ChatCompletionChoice): string;
export declare function chatCompletionChoiceFromJSON(jsonString: string): SafeParseResult<ChatCompletionChoice, SDKValidationError>;
//# sourceMappingURL=chatcompletionchoice.d.ts.map