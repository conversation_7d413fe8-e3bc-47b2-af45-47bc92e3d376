{"version": 3, "sources": ["../src/predict.ts"], "sourcesContent": ["/**\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { GENKIT_CLIENT_HEADER } from 'genkit';\nimport { GoogleAuth } from 'google-auth-library';\nimport { PluginOptions } from './common/types.js';\n\nfunction endpoint(options: {\n  projectId: string;\n  location: string;\n  model: string;\n}) {\n  return (\n    `https://${options.location}-aiplatform.googleapis.com/v1/` +\n    `projects/${options.projectId}/locations/${options.location}/` +\n    `publishers/google/models/${options.model}:predict`\n  );\n}\n\ninterface PredictionResponse<R> {\n  predictions: R[];\n}\n\nexport type PredictClient<I = unknown, R = unknown, P = unknown> = (\n  instances: I[],\n  parameters: P\n) => Promise<PredictionResponse<R>>;\n\nexport function predictModel<I = unknown, R = unknown, P = unknown>(\n  auth: GoogleAuth,\n  { location, projectId }: PluginOptions,\n  model: string\n): PredictClient<I, R, P> {\n  return async (\n    instances: I[],\n    parameters: P\n  ): Promise<PredictionResponse<R>> => {\n    const fetch = (await import('node-fetch')).default;\n\n    const accessToken = await auth.getAccessToken();\n    const req = {\n      instances,\n      parameters,\n    };\n\n    const response = await fetch(\n      endpoint({\n        projectId: projectId!,\n        location,\n        model,\n      }),\n      {\n        method: 'POST',\n        body: JSON.stringify(req),\n        headers: {\n          Authorization: `Bearer ${accessToken}`,\n          'Content-Type': 'application/json',\n          'X-Goog-Api-Client': GENKIT_CLIENT_HEADER,\n        },\n      }\n    );\n\n    if (!response.ok) {\n      throw new Error(\n        `Error from Vertex AI predict: HTTP ${\n          response.status\n        }: ${await response.text()}`\n      );\n    }\n\n    return (await response.json()) as PredictionResponse<R>;\n  };\n}\n"], "mappings": "AAgBA,SAAS,4BAA4B;AAIrC,SAAS,SAAS,SAIf;AACD,SACE,WAAW,QAAQ,QAAQ,0CACf,QAAQ,SAAS,cAAc,QAAQ,QAAQ,6BAC/B,QAAQ,KAAK;AAE7C;AAWO,SAAS,aACd,MACA,EAAE,UAAU,UAAU,GACtB,OACwB;AACxB,SAAO,OACL,WACA,eACmC;AACnC,UAAM,SAAS,MAAM,OAAO,YAAY,GAAG;AAE3C,UAAM,cAAc,MAAM,KAAK,eAAe;AAC9C,UAAM,MAAM;AAAA,MACV;AAAA,MACA;AAAA,IACF;AAEA,UAAM,WAAW,MAAM;AAAA,MACrB,SAAS;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,MACF,CAAC;AAAA,MACD;AAAA,QACE,QAAQ;AAAA,QACR,MAAM,KAAK,UAAU,GAAG;AAAA,QACxB,SAAS;AAAA,UACP,eAAe,UAAU,WAAW;AAAA,UACpC,gBAAgB;AAAA,UAChB,qBAAqB;AAAA,QACvB;AAAA,MACF;AAAA,IACF;AAEA,QAAI,CAAC,SAAS,IAAI;AAChB,YAAM,IAAI;AAAA,QACR,sCACE,SAAS,MACX,KAAK,MAAM,SAAS,KAAK,CAAC;AAAA,MAC5B;AAAA,IACF;AAEA,WAAQ,MAAM,SAAS,KAAK;AAAA,EAC9B;AACF;", "names": []}