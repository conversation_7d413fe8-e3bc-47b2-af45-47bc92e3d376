{"version": 3, "file": "types.js", "sourceRoot": "", "sources": ["../../../src/logs/types.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport type {\n  Fixed64,\n  IAnyValue,\n  IInstrumentationScope,\n  IKeyValue,\n} from '../common/types';\nimport type { IResource } from '../resource/types';\n\n/** Properties of an ExportLogsServiceRequest. */\nexport interface IExportLogsServiceRequest {\n  /** ExportLogsServiceRequest resourceLogs */\n  resourceLogs?: IResourceLogs[];\n}\n\nexport interface IExportLogsServiceResponse {\n  /** ExportLogsServiceResponse partialSuccess */\n  partialSuccess?: IExportLogsPartialSuccess;\n}\n\nexport interface IExportLogsPartialSuccess {\n  /** ExportLogsPartialSuccess rejectedLogRecords */\n  rejectedLogRecords?: number;\n\n  /** ExportLogsPartialSuccess errorMessage */\n  errorMessage?: string;\n}\n\n/** Properties of a ResourceLogs. */\nexport interface IResourceLogs {\n  /** ResourceLogs resource */\n  resource?: IResource;\n\n  /** ResourceLogs scopeLogs */\n  scopeLogs: IScopeLogs[];\n\n  /** ResourceLogs schemaUrl */\n  schemaUrl?: string;\n}\n\n/** Properties of an ScopeLogs. */\nexport interface IScopeLogs {\n  /** IScopeLogs scope */\n  scope?: IInstrumentationScope;\n\n  /** IScopeLogs logRecords */\n  logRecords?: ILogRecord[];\n\n  /** IScopeLogs schemaUrl */\n  schemaUrl?: string | null;\n}\n\n/** Properties of a LogRecord. */\nexport interface ILogRecord {\n  /** LogRecord timeUnixNano */\n  timeUnixNano: Fixed64;\n\n  /** LogRecord observedTimeUnixNano */\n  observedTimeUnixNano: Fixed64;\n\n  /** LogRecord severityNumber */\n  severityNumber?: ESeverityNumber;\n\n  /** LogRecord severityText */\n  severityText?: string;\n\n  /** LogRecord body */\n  body?: IAnyValue;\n\n  /** LogRecord attributes */\n  attributes: IKeyValue[];\n\n  /** LogRecord droppedAttributesCount */\n  droppedAttributesCount: number;\n\n  /** LogRecord flags */\n  flags?: number;\n\n  /** LogRecord traceId */\n  traceId?: string | Uint8Array;\n\n  /** LogRecord spanId */\n  spanId?: string | Uint8Array;\n}\n\n/**\n * Numerical value of the severity, normalized to values described in Log Data Model.\n */\nexport const enum ESeverityNumber {\n  /** Unspecified. Do NOT use as default */\n  SEVERITY_NUMBER_UNSPECIFIED = 0,\n  SEVERITY_NUMBER_TRACE = 1,\n  SEVERITY_NUMBER_TRACE2 = 2,\n  SEVERITY_NUMBER_TRACE3 = 3,\n  SEVERITY_NUMBER_TRACE4 = 4,\n  SEVERITY_NUMBER_DEBUG = 5,\n  SEVERITY_NUMBER_DEBUG2 = 6,\n  SEVERITY_NUMBER_DEBUG3 = 7,\n  SEVERITY_NUMBER_DEBUG4 = 8,\n  SEVERITY_NUMBER_INFO = 9,\n  SEVERITY_NUMBER_INFO2 = 10,\n  SEVERITY_NUMBER_INFO3 = 11,\n  SEVERITY_NUMBER_INFO4 = 12,\n  SEVERITY_NUMBER_WARN = 13,\n  SEVERITY_NUMBER_WARN2 = 14,\n  SEVERITY_NUMBER_WARN3 = 15,\n  SEVERITY_NUMBER_WARN4 = 16,\n  SEVERITY_NUMBER_ERROR = 17,\n  SEVERITY_NUMBER_ERROR2 = 18,\n  SEVERITY_NUMBER_ERROR3 = 19,\n  SEVERITY_NUMBER_ERROR4 = 20,\n  SEVERITY_NUMBER_FATAL = 21,\n  SEVERITY_NUMBER_FATAL2 = 22,\n  SEVERITY_NUMBER_FATAL3 = 23,\n  SEVERITY_NUMBER_FATAL4 = 24,\n}\n"]}