"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
var query_public_endpoint_exports = {};
__export(query_public_endpoint_exports, {
  queryPublicEndpoint: () => queryPublicEndpoint
});
module.exports = __toCommonJS(query_public_endpoint_exports);
async function queryPublicEndpoint(params) {
  const {
    featureVector,
    neighborCount,
    accessToken,
    indexEndpointId,
    publicDomainName,
    projectNumber,
    deployedIndexId,
    location,
    restricts,
    numericRestricts
  } = params;
  const url = new URL(
    `https://${publicDomainName}/v1/projects/${projectNumber}/locations/${location}/indexEndpoints/${indexEndpointId}:findNeighbors`
  );
  const requestBody = {
    deployed_index_id: deployedIndexId,
    queries: [
      {
        datapoint: {
          datapoint_id: "0",
          feature_vector: featureVector,
          restricts: restricts?.map((r) => ({
            namespace: r.namespace,
            allow_list: r.allowList,
            deny_list: r.denyList
          })) || [],
          numeric_restricts: numericRestricts?.map((nr) => {
            const newNR = {
              namespace: nr.namespace
            };
            if (nr.valueInt !== void 0) {
              newNR.value_int = nr.valueInt;
            }
            if (nr.valueFloat !== void 0) {
              newNR.value_float = nr.valueFloat;
            }
            if (nr.valueDouble !== void 0) {
              newNR.value_double = nr.valueDouble;
            }
            newNR.op = nr.op;
            return newNR;
          }) || []
        },
        neighbor_count: neighborCount
      }
    ]
  };
  const response = await fetch(url, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${accessToken}`
    },
    body: JSON.stringify(requestBody)
  });
  if (!response.ok) {
    const errMsg = (await response.json()).error?.message || "";
    throw new Error(`Error querying index: ${response.statusText}. ${errMsg}`);
  }
  return await response.json();
}
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  queryPublicEndpoint
});
//# sourceMappingURL=query_public_endpoint.js.map