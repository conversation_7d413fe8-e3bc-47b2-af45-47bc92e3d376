{"version": 3, "file": "pre_fetch_processing.js", "sourceRoot": "", "sources": ["../../../src/functions/pre_fetch_processing.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;GAeG;;;AASH,4CAA4C;AAC5C,+CAA+C;AAE/C,SAAgB,oBAAoB,CAClC,OAAwC,EACxC,gBAAmC,EACnC,cAAgC;IAEhC,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE;QAC/B,OAAO;YACL,QAAQ,EAAE,CAAC,EAAC,IAAI,EAAE,SAAS,CAAC,SAAS,EAAE,KAAK,EAAE,CAAC,EAAC,IAAI,EAAE,OAAO,EAAC,CAAC,EAAC,CAAC;YACjE,gBAAgB,EAAE,gBAAgB;YAClC,cAAc,EAAE,cAAc;SAC/B,CAAC;KACH;SAAM;QACL,OAAO,OAAO,CAAC;KAChB;AACH,CAAC;AAdD,oDAcC;AAED,SAAgB,8BAA8B,CAC5C,OAA+B;IAE/B,IAAI,iBAAiB,CAAC,OAAO,CAAC,IAAI,iBAAiB,CAAC,OAAO,CAAC,EAAE;QAC5D,MAAM,IAAI,oBAAW,CACnB,kHAAkH,CACnH,CAAC;KACH;AACH,CAAC;AARD,wEAQC;AAED,SAAgB,wBAAwB,CACtC,gBAAkC;IAElC,IAAI,MAAM,IAAI,gBAAgB,EAAE;QAC9B,IAAI,CAAC,CAAC,gBAAgB,CAAC,IAAK,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,gBAAgB,CAAC,IAAK,IAAI,EAAE,CAAC,EAAE;YACpE,OAAO,gBAAgB,CAAC,IAAI,CAAC;SAC9B;KACF;IACD,OAAO,gBAAgB,CAAC;AAC1B,CAAC;AATD,4DASC;AAED,SAAgB,aAAa,CAC3B,OAA+B;IAE/B,OAAO,iBAAiB,CAAC,OAAO,CAAC,IAAI,gBAAgB,CAAC,OAAO,CAAC;QAC5D,CAAC,CAAC,SAAS;QACX,CAAC,CAAC,IAAI,CAAC;AACX,CAAC;AAND,sCAMC;AAED,SAAgB,iBAAiB,CAAC,OAA+B;;IAC/D,KAAK,MAAM,IAAI,IAAI,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,KAAK,mCAAI,EAAE,EAAE;QACvC,MAAM,SAAS,GAAI,IAAsB,CAAC,SAAS,CAAC;QACpD,IAAI,CAAC,SAAS;YAAE,SAAS;QACzB,IAAI,SAAS,CAAC,cAAc,EAAE;YAC5B,OAAO,IAAI,CAAC;SACb;KACF;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AATD,8CASC;AAED,SAAS,gBAAgB,CAAC,OAA+B;IACvD,OAAO,CAAC,CAAC,OAAO,CAAC,aAAa,CAAC;AACjC,CAAC;AAED,SAAgB,iBAAiB,CAAC,OAA+B;;IAC/D,KAAK,MAAM,IAAI,IAAI,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,KAAK,mCAAI,EAAE,EAAE;QACvC,MAAM,SAAS,GAAI,IAAsB,CAAC,SAAS,CAAC;QACpD,IAAI,CAAC,SAAS;YAAE,SAAS;QACzB,IAAI,SAAS,CAAC,cAAc,EAAE;YAC5B,OAAO,IAAI,CAAC;SACb;KACF;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AATD,8CASC"}