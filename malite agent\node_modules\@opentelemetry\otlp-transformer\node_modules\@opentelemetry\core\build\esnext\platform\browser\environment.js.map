{"version": 3, "file": "environment.js", "sourceRoot": "", "sources": ["../../../../src/platform/browser/environment.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAEH,OAAO,EACL,mBAAmB,EAGnB,gBAAgB,GACjB,MAAM,yBAAyB,CAAC;AACjC,OAAO,EAAE,WAAW,EAAE,MAAM,cAAc,CAAC;AAE3C;;GAEG;AACH,MAAM,UAAU,MAAM;IACpB,MAAM,SAAS,GAAG,gBAAgB,CAChC,WAAkD,CACnD,CAAC;IACF,OAAO,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,mBAAmB,EAAE,SAAS,CAAC,CAAC;AAC3D,CAAC;AAED,MAAM,UAAU,qBAAqB;IACnC,OAAO,gBAAgB,CAAC,WAAkD,CAAC,CAAC;AAC9E,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  DEFAULT_ENVIRONMENT,\n  ENVIRONMENT,\n  RAW_ENVIRONMENT,\n  parseEnvironment,\n} from '../../utils/environment';\nimport { _globalThis } from './globalThis';\n\n/**\n * Gets the environment variables\n */\nexport function getEnv(): Required<ENVIRONMENT> {\n  const globalEnv = parseEnvironment(\n    _globalThis as typeof globalThis & RAW_ENVIRONMENT\n  );\n  return Object.assign({}, DEFAULT_ENVIRONMENT, globalEnv);\n}\n\nexport function getEnvWithoutDefaults(): ENVIRONMENT {\n  return parseEnvironment(_globalThis as typeof globalThis & RAW_ENVIRONMENT);\n}\n"]}