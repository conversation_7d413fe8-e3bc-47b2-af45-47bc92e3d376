{"version": 3, "file": "instrumentationNodeModuleFile.js", "sourceRoot": "", "sources": ["../../src/instrumentationNodeModuleFile.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAGH,4CAA6C;AAE7C,MAAa,6BAA6B;IAIxC,YACE,IAAY,EACL,iBAA2B;IAClC,8DAA8D;IACvD,KAA0D;IACjE,8DAA8D;IACvD,OAA8D;QAJ9D,sBAAiB,GAAjB,iBAAiB,CAAU;QAE3B,UAAK,GAAL,KAAK,CAAqD;QAE1D,YAAO,GAAP,OAAO,CAAuD;QAErE,IAAI,CAAC,IAAI,GAAG,IAAA,iBAAS,EAAC,IAAI,CAAC,CAAC;IAC9B,CAAC;CACF;AAdD,sEAcC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { InstrumentationModuleFile } from './types';\nimport { normalize } from './platform/index';\n\nexport class InstrumentationNodeModuleFile\n  implements InstrumentationModuleFile\n{\n  public name: string;\n  constructor(\n    name: string,\n    public supportedVersions: string[],\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    public patch: (moduleExports: any, moduleVersion?: string) => any,\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    public unpatch: (moduleExports?: any, moduleVersion?: string) => void\n  ) {\n    this.name = normalize(name);\n  }\n}\n"]}