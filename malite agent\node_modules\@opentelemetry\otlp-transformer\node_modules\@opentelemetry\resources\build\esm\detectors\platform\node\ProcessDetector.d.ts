import { Detector } from '../../../types';
import { ResourceDetectionConfig } from '../../../config';
import { IResource } from '../../../IResource';
/**
 * ProcessDetector will be used to detect the resources related current process running
 * and being instrumented from the NodeJS Process module.
 */
declare class ProcessDetector implements Detector {
    detect(config?: ResourceDetectionConfig): Promise<IResource>;
}
export declare const processDetector: ProcessDetector;
export {};
//# sourceMappingURL=ProcessDetector.d.ts.map