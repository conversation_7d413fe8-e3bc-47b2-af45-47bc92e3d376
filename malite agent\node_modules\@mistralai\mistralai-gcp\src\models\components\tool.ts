/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  FunctionT,
  FunctionT$inboundSchema,
  FunctionT$Outbound,
  FunctionT$outboundSchema,
} from "./function.js";
import {
  ToolTypes,
  ToolTypes$inboundSchema,
  ToolTypes$outboundSchema,
} from "./tooltypes.js";

export type Tool = {
  type?: ToolTypes | undefined;
  function: FunctionT;
};

/** @internal */
export const Tool$inboundSchema: z.ZodType<Tool, z.ZodTypeDef, unknown> = z
  .object({
    type: ToolTypes$inboundSchema.optional(),
    function: FunctionT$inboundSchema,
  });

/** @internal */
export type Tool$Outbound = {
  type?: string | undefined;
  function: FunctionT$Outbound;
};

/** @internal */
export const Tool$outboundSchema: z.ZodType<Tool$Outbound, z.ZodTypeDef, Tool> =
  z.object({
    type: ToolTypes$outboundSchema.optional(),
    function: FunctionT$outboundSchema,
  });

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace Tool$ {
  /** @deprecated use `Tool$inboundSchema` instead. */
  export const inboundSchema = Tool$inboundSchema;
  /** @deprecated use `Tool$outboundSchema` instead. */
  export const outboundSchema = Tool$outboundSchema;
  /** @deprecated use `Tool$Outbound` instead. */
  export type Outbound = Tool$Outbound;
}

export function toolToJSON(tool: Tool): string {
  return JSON.stringify(Tool$outboundSchema.parse(tool));
}

export function toolFromJSON(
  jsonString: string,
): SafeParseResult<Tool, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => Tool$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'Tool' from JSON`,
  );
}
