{"version": 3, "file": "NoopDetectorSync.js", "sourceRoot": "", "sources": ["../../../src/detectors/NoopDetectorSync.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,0CAAuC;AAIvC,MAAa,gBAAgB;IAC3B,MAAM;QACJ,OAAO,IAAI,mBAAQ,CAAC,EAAE,CAAC,CAAC;IAC1B,CAAC;CACF;AAJD,4CAIC;AAEY,QAAA,gBAAgB,GAAG,IAAI,gBAAgB,EAAE,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Resource } from '../Resource';\nimport { DetectorSync } from '../types';\nimport { IResource } from '../IResource';\n\nexport class NoopDetectorSync implements DetectorSync {\n  detect(): IResource {\n    return new Resource({});\n  }\n}\n\nexport const noopDetectorSync = new NoopDetectorSync();\n"]}