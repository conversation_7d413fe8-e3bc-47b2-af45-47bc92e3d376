{"interfaces": {"google.cloud.aiplatform.v1beta1.FeaturestoreService": {"retry_codes": {"non_idempotent": [], "idempotent": ["DEADLINE_EXCEEDED", "UNAVAILABLE"]}, "retry_params": {"default": {"initial_retry_delay_millis": 100, "retry_delay_multiplier": 1.3, "max_retry_delay_millis": 60000, "initial_rpc_timeout_millis": 60000, "rpc_timeout_multiplier": 1, "max_rpc_timeout_millis": 60000, "total_timeout_millis": 600000}}, "methods": {"CreateFeaturestore": {"timeout_millis": 5000, "retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "GetFeaturestore": {"timeout_millis": 5000, "retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "ListFeaturestores": {"timeout_millis": 5000, "retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "UpdateFeaturestore": {"timeout_millis": 5000, "retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "DeleteFeaturestore": {"timeout_millis": 5000, "retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "CreateEntityType": {"timeout_millis": 5000, "retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "GetEntityType": {"timeout_millis": 5000, "retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "ListEntityTypes": {"timeout_millis": 5000, "retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "UpdateEntityType": {"timeout_millis": 5000, "retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "DeleteEntityType": {"timeout_millis": 5000, "retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "CreateFeature": {"timeout_millis": 5000, "retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "BatchCreateFeatures": {"timeout_millis": 5000, "retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "GetFeature": {"timeout_millis": 5000, "retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "ListFeatures": {"timeout_millis": 5000, "retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "UpdateFeature": {"timeout_millis": 5000, "retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "DeleteFeature": {"timeout_millis": 5000, "retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "ImportFeatureValues": {"timeout_millis": 5000, "retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "BatchReadFeatureValues": {"timeout_millis": 5000, "retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "ExportFeatureValues": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "DeleteFeatureValues": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "SearchFeatures": {"timeout_millis": 5000, "retry_codes_name": "non_idempotent", "retry_params_name": "default"}}}}}