// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.aiplatform.v1.schema.predict.params;


option csharp_namespace = "Google.Cloud.AIPlatform.V1.Schema.Predict.Params";
option go_package = "cloud.google.com/go/aiplatform/apiv1/schema/predict/params/paramspb;paramspb";
option java_multiple_files = true;
option java_outer_classname = "VideoClassificationPredictionParamsProto";
option java_package = "com.google.cloud.aiplatform.v1.schema.predict.params";
option php_namespace = "Google\\Cloud\\AIPlatform\\V1\\Schema\\Predict\\Params";
option ruby_package = "Google::Cloud::AIPlatform::V1::Schema::Predict::Params";

// Prediction model parameters for Video Classification.
message VideoClassificationPredictionParams {
  // The Model only returns predictions with at least this confidence score.
  // Default value is 0.0
  float confidence_threshold = 1;

  // The Model only returns up to that many top, by confidence score,
  // predictions per instance. If this number is very high, the Model may return
  // fewer predictions. Default value is 10,000.
  int32 max_predictions = 2;

  // Set to true to request segment-level classification. Vertex AI returns
  // labels and their confidence scores for the entire time segment of the
  // video that user specified in the input instance.
  // Default value is true
  bool segment_classification = 3;

  // Set to true to request shot-level classification. Vertex AI determines
  // the boundaries for each camera shot in the entire time segment of the
  // video that user specified in the input instance. Vertex AI then
  // returns labels and their confidence scores for each detected shot, along
  // with the start and end time of the shot.
  // WARNING: Model evaluation is not done for this classification type,
  // the quality of it depends on the training data, but there are no metrics
  // provided to describe that quality.
  // Default value is false
  bool shot_classification = 4;

  // Set to true to request classification for a video at one-second intervals.
  // Vertex AI returns labels and their confidence scores for each second of
  // the entire time segment of the video that user specified in the input
  // WARNING: Model evaluation is not done for this classification type, the
  // quality of it depends on the training data, but there are no metrics
  // provided to describe that quality. Default value is false
  bool one_sec_interval_classification = 5;
}
