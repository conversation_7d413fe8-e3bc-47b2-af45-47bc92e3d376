import { ISerializer } from '../common/i-serializer';
import { ReadableSpan } from '@opentelemetry/sdk-trace-base';
import { IExportTraceServiceResponse } from '../trace/types';
import { ResourceMetrics } from '@opentelemetry/sdk-metrics';
import { ReadableLogRecord } from '@opentelemetry/sdk-logs';
import { IExportMetricsServiceResponse } from '../metrics/types';
import { IExportLogsServiceResponse } from '../logs/types';
export declare const JsonTraceSerializer: ISerializer<ReadableSpan[], IExportTraceServiceResponse>;
export declare const JsonMetricsSerializer: ISerializer<ResourceMetrics[], IExportMetricsServiceResponse>;
export declare const JsonLogsSerializer: ISerializer<ReadableLogRecord[], IExportLogsServiceResponse>;
//# sourceMappingURL=serializers.d.ts.map