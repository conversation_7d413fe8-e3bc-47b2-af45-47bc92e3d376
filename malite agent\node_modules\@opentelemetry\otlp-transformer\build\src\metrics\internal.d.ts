import type { OtlpEncodingOptions } from '../common/types';
import { MetricData, ResourceMetrics, ScopeMetrics } from '@opentelemetry/sdk-metrics';
import { IMetric, IResourceMetrics, IScopeMetrics } from './types';
import { Encoder } from '../common';
export declare function toResourceMetrics(resourceMetrics: ResourceMetrics, options?: OtlpEncodingOptions): IResourceMetrics;
export declare function toScopeMetrics(scopeMetrics: ScopeMetrics[], encoder: Encoder): IScopeMetrics[];
export declare function toMetric(metricData: MetricData, encoder: Encoder): IMetric;
//# sourceMappingURL=internal.d.ts.map