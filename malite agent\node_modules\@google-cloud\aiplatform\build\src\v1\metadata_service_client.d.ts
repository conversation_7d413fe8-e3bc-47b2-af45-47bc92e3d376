import type * as gax from 'google-gax';
import type { Callback, CallOptions, Descriptors, ClientOptions, LROperation, PaginationCallback, IamClient, IamProtos, LocationsClient, LocationProtos } from 'google-gax';
import { Transform } from 'stream';
import * as protos from '../../protos/protos';
/**
 *  Service for reading and writing metadata entries.
 * @class
 * @memberof v1
 */
export declare class MetadataServiceClient {
    private _terminated;
    private _opts;
    private _providedCustomServicePath;
    private _gaxModule;
    private _gaxGrpc;
    private _protos;
    private _defaults;
    private _universeDomain;
    private _servicePath;
    auth: gax.GoogleAuth;
    descriptors: Descriptors;
    warn: (code: string, message: string, warnType?: string) => void;
    innerApiCalls: {
        [name: string]: Function;
    };
    iamClient: IamClient;
    locationsClient: LocationsClient;
    pathTemplates: {
        [name: string]: gax.PathTemplate;
    };
    operationsClient: gax.OperationsClient;
    metadataServiceStub?: Promise<{
        [name: string]: Function;
    }>;
    /**
     * Construct an instance of MetadataServiceClient.
     *
     * @param {object} [options] - The configuration object.
     * The options accepted by the constructor are described in detail
     * in [this document](https://github.com/googleapis/gax-nodejs/blob/main/client-libraries.md#creating-the-client-instance).
     * The common options are:
     * @param {object} [options.credentials] - Credentials object.
     * @param {string} [options.credentials.client_email]
     * @param {string} [options.credentials.private_key]
     * @param {string} [options.email] - Account email address. Required when
     *     using a .pem or .p12 keyFilename.
     * @param {string} [options.keyFilename] - Full path to the a .json, .pem, or
     *     .p12 key downloaded from the Google Developers Console. If you provide
     *     a path to a JSON file, the projectId option below is not necessary.
     *     NOTE: .pem and .p12 require you to specify options.email as well.
     * @param {number} [options.port] - The port on which to connect to
     *     the remote host.
     * @param {string} [options.projectId] - The project ID from the Google
     *     Developer's Console, e.g. 'grape-spaceship-123'. We will also check
     *     the environment variable GCLOUD_PROJECT for your project ID. If your
     *     app is running in an environment which supports
     *     {@link https://developers.google.com/identity/protocols/application-default-credentials Application Default Credentials},
     *     your project ID will be detected automatically.
     * @param {string} [options.apiEndpoint] - The domain name of the
     *     API remote host.
     * @param {gax.ClientConfig} [options.clientConfig] - Client configuration override.
     *     Follows the structure of {@link gapicConfig}.
     * @param {boolean} [options.fallback] - Use HTTP/1.1 REST mode.
     *     For more information, please check the
     *     {@link https://github.com/googleapis/gax-nodejs/blob/main/client-libraries.md#http11-rest-api-mode documentation}.
     * @param {gax} [gaxInstance]: loaded instance of `google-gax`. Useful if you
     *     need to avoid loading the default gRPC version and want to use the fallback
     *     HTTP implementation. Load only fallback version and pass it to the constructor:
     *     ```
     *     const gax = require('google-gax/build/src/fallback'); // avoids loading google-gax with gRPC
     *     const client = new MetadataServiceClient({fallback: true}, gax);
     *     ```
     */
    constructor(opts?: ClientOptions, gaxInstance?: typeof gax | typeof gax.fallback);
    /**
     * Initialize the client.
     * Performs asynchronous operations (such as authentication) and prepares the client.
     * This function will be called automatically when any class method is called for the
     * first time, but if you need to initialize it before calling an actual method,
     * feel free to call initialize() directly.
     *
     * You can await on this method if you want to make sure the client is initialized.
     *
     * @returns {Promise} A promise that resolves to an authenticated service stub.
     */
    initialize(): Promise<{
        [name: string]: Function;
    }>;
    /**
     * The DNS address for this API service.
     * @deprecated Use the apiEndpoint method of the client instance.
     * @returns {string} The DNS address for this service.
     */
    static get servicePath(): string;
    /**
     * The DNS address for this API service - same as servicePath.
     * @deprecated Use the apiEndpoint method of the client instance.
     * @returns {string} The DNS address for this service.
     */
    static get apiEndpoint(): string;
    /**
     * The DNS address for this API service.
     * @returns {string} The DNS address for this service.
     */
    get apiEndpoint(): string;
    get universeDomain(): string;
    /**
     * The port for this API service.
     * @returns {number} The default port for this service.
     */
    static get port(): number;
    /**
     * The scopes needed to make gRPC calls for every method defined
     * in this service.
     * @returns {string[]} List of default scopes.
     */
    static get scopes(): string[];
    getProjectId(): Promise<string>;
    getProjectId(callback: Callback<string, undefined, undefined>): void;
    /**
     * Retrieves a specific MetadataStore.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.name
     *   Required. The resource name of the MetadataStore to retrieve.
     *   Format:
     *   `projects/{project}/locations/{location}/metadataStores/{metadatastore}`
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing {@link protos.google.cloud.aiplatform.v1.MetadataStore|MetadataStore}.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#regular-methods | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1/metadata_service.get_metadata_store.js</caption>
     * region_tag:aiplatform_v1_generated_MetadataService_GetMetadataStore_async
     */
    getMetadataStore(request?: protos.google.cloud.aiplatform.v1.IGetMetadataStoreRequest, options?: CallOptions): Promise<[
        protos.google.cloud.aiplatform.v1.IMetadataStore,
        protos.google.cloud.aiplatform.v1.IGetMetadataStoreRequest | undefined,
        {} | undefined
    ]>;
    getMetadataStore(request: protos.google.cloud.aiplatform.v1.IGetMetadataStoreRequest, options: CallOptions, callback: Callback<protos.google.cloud.aiplatform.v1.IMetadataStore, protos.google.cloud.aiplatform.v1.IGetMetadataStoreRequest | null | undefined, {} | null | undefined>): void;
    getMetadataStore(request: protos.google.cloud.aiplatform.v1.IGetMetadataStoreRequest, callback: Callback<protos.google.cloud.aiplatform.v1.IMetadataStore, protos.google.cloud.aiplatform.v1.IGetMetadataStoreRequest | null | undefined, {} | null | undefined>): void;
    /**
     * Creates an Artifact associated with a MetadataStore.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. The resource name of the MetadataStore where the Artifact should
     *   be created.
     *   Format:
     *   `projects/{project}/locations/{location}/metadataStores/{metadatastore}`
     * @param {google.cloud.aiplatform.v1.Artifact} request.artifact
     *   Required. The Artifact to create.
     * @param {string} request.artifactId
     *   The {artifact} portion of the resource name with the format:
     *   `projects/{project}/locations/{location}/metadataStores/{metadatastore}/artifacts/{artifact}`
     *   If not provided, the Artifact's ID will be a UUID generated by the service.
     *   Must be 4-128 characters in length. Valid characters are `/{@link protos.0-9|a-z}-/`.
     *   Must be unique across all Artifacts in the parent MetadataStore. (Otherwise
     *   the request will fail with ALREADY_EXISTS, or PERMISSION_DENIED if the
     *   caller can't view the preexisting Artifact.)
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing {@link protos.google.cloud.aiplatform.v1.Artifact|Artifact}.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#regular-methods | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1/metadata_service.create_artifact.js</caption>
     * region_tag:aiplatform_v1_generated_MetadataService_CreateArtifact_async
     */
    createArtifact(request?: protos.google.cloud.aiplatform.v1.ICreateArtifactRequest, options?: CallOptions): Promise<[
        protos.google.cloud.aiplatform.v1.IArtifact,
        protos.google.cloud.aiplatform.v1.ICreateArtifactRequest | undefined,
        {} | undefined
    ]>;
    createArtifact(request: protos.google.cloud.aiplatform.v1.ICreateArtifactRequest, options: CallOptions, callback: Callback<protos.google.cloud.aiplatform.v1.IArtifact, protos.google.cloud.aiplatform.v1.ICreateArtifactRequest | null | undefined, {} | null | undefined>): void;
    createArtifact(request: protos.google.cloud.aiplatform.v1.ICreateArtifactRequest, callback: Callback<protos.google.cloud.aiplatform.v1.IArtifact, protos.google.cloud.aiplatform.v1.ICreateArtifactRequest | null | undefined, {} | null | undefined>): void;
    /**
     * Retrieves a specific Artifact.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.name
     *   Required. The resource name of the Artifact to retrieve.
     *   Format:
     *   `projects/{project}/locations/{location}/metadataStores/{metadatastore}/artifacts/{artifact}`
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing {@link protos.google.cloud.aiplatform.v1.Artifact|Artifact}.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#regular-methods | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1/metadata_service.get_artifact.js</caption>
     * region_tag:aiplatform_v1_generated_MetadataService_GetArtifact_async
     */
    getArtifact(request?: protos.google.cloud.aiplatform.v1.IGetArtifactRequest, options?: CallOptions): Promise<[
        protos.google.cloud.aiplatform.v1.IArtifact,
        protos.google.cloud.aiplatform.v1.IGetArtifactRequest | undefined,
        {} | undefined
    ]>;
    getArtifact(request: protos.google.cloud.aiplatform.v1.IGetArtifactRequest, options: CallOptions, callback: Callback<protos.google.cloud.aiplatform.v1.IArtifact, protos.google.cloud.aiplatform.v1.IGetArtifactRequest | null | undefined, {} | null | undefined>): void;
    getArtifact(request: protos.google.cloud.aiplatform.v1.IGetArtifactRequest, callback: Callback<protos.google.cloud.aiplatform.v1.IArtifact, protos.google.cloud.aiplatform.v1.IGetArtifactRequest | null | undefined, {} | null | undefined>): void;
    /**
     * Updates a stored Artifact.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {google.cloud.aiplatform.v1.Artifact} request.artifact
     *   Required. The Artifact containing updates.
     *   The Artifact's {@link protos.google.cloud.aiplatform.v1.Artifact.name|Artifact.name}
     *   field is used to identify the Artifact to be updated. Format:
     *   `projects/{project}/locations/{location}/metadataStores/{metadatastore}/artifacts/{artifact}`
     * @param {google.protobuf.FieldMask} [request.updateMask]
     *   Optional. A FieldMask indicating which fields should be updated.
     * @param {boolean} request.allowMissing
     *   If set to true, and the {@link protos.google.cloud.aiplatform.v1.Artifact|Artifact} is
     *   not found, a new {@link protos.google.cloud.aiplatform.v1.Artifact|Artifact} is
     *   created.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing {@link protos.google.cloud.aiplatform.v1.Artifact|Artifact}.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#regular-methods | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1/metadata_service.update_artifact.js</caption>
     * region_tag:aiplatform_v1_generated_MetadataService_UpdateArtifact_async
     */
    updateArtifact(request?: protos.google.cloud.aiplatform.v1.IUpdateArtifactRequest, options?: CallOptions): Promise<[
        protos.google.cloud.aiplatform.v1.IArtifact,
        protos.google.cloud.aiplatform.v1.IUpdateArtifactRequest | undefined,
        {} | undefined
    ]>;
    updateArtifact(request: protos.google.cloud.aiplatform.v1.IUpdateArtifactRequest, options: CallOptions, callback: Callback<protos.google.cloud.aiplatform.v1.IArtifact, protos.google.cloud.aiplatform.v1.IUpdateArtifactRequest | null | undefined, {} | null | undefined>): void;
    updateArtifact(request: protos.google.cloud.aiplatform.v1.IUpdateArtifactRequest, callback: Callback<protos.google.cloud.aiplatform.v1.IArtifact, protos.google.cloud.aiplatform.v1.IUpdateArtifactRequest | null | undefined, {} | null | undefined>): void;
    /**
     * Creates a Context associated with a MetadataStore.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. The resource name of the MetadataStore where the Context should
     *   be created. Format:
     *   `projects/{project}/locations/{location}/metadataStores/{metadatastore}`
     * @param {google.cloud.aiplatform.v1.Context} request.context
     *   Required. The Context to create.
     * @param {string} request.contextId
     *   The {context} portion of the resource name with the format:
     *   `projects/{project}/locations/{location}/metadataStores/{metadatastore}/contexts/{context}`.
     *   If not provided, the Context's ID will be a UUID generated by the service.
     *   Must be 4-128 characters in length. Valid characters are `/{@link protos.0-9|a-z}-/`.
     *   Must be unique across all Contexts in the parent MetadataStore. (Otherwise
     *   the request will fail with ALREADY_EXISTS, or PERMISSION_DENIED if the
     *   caller can't view the preexisting Context.)
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing {@link protos.google.cloud.aiplatform.v1.Context|Context}.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#regular-methods | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1/metadata_service.create_context.js</caption>
     * region_tag:aiplatform_v1_generated_MetadataService_CreateContext_async
     */
    createContext(request?: protos.google.cloud.aiplatform.v1.ICreateContextRequest, options?: CallOptions): Promise<[
        protos.google.cloud.aiplatform.v1.IContext,
        protos.google.cloud.aiplatform.v1.ICreateContextRequest | undefined,
        {} | undefined
    ]>;
    createContext(request: protos.google.cloud.aiplatform.v1.ICreateContextRequest, options: CallOptions, callback: Callback<protos.google.cloud.aiplatform.v1.IContext, protos.google.cloud.aiplatform.v1.ICreateContextRequest | null | undefined, {} | null | undefined>): void;
    createContext(request: protos.google.cloud.aiplatform.v1.ICreateContextRequest, callback: Callback<protos.google.cloud.aiplatform.v1.IContext, protos.google.cloud.aiplatform.v1.ICreateContextRequest | null | undefined, {} | null | undefined>): void;
    /**
     * Retrieves a specific Context.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.name
     *   Required. The resource name of the Context to retrieve.
     *   Format:
     *   `projects/{project}/locations/{location}/metadataStores/{metadatastore}/contexts/{context}`
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing {@link protos.google.cloud.aiplatform.v1.Context|Context}.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#regular-methods | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1/metadata_service.get_context.js</caption>
     * region_tag:aiplatform_v1_generated_MetadataService_GetContext_async
     */
    getContext(request?: protos.google.cloud.aiplatform.v1.IGetContextRequest, options?: CallOptions): Promise<[
        protos.google.cloud.aiplatform.v1.IContext,
        protos.google.cloud.aiplatform.v1.IGetContextRequest | undefined,
        {} | undefined
    ]>;
    getContext(request: protos.google.cloud.aiplatform.v1.IGetContextRequest, options: CallOptions, callback: Callback<protos.google.cloud.aiplatform.v1.IContext, protos.google.cloud.aiplatform.v1.IGetContextRequest | null | undefined, {} | null | undefined>): void;
    getContext(request: protos.google.cloud.aiplatform.v1.IGetContextRequest, callback: Callback<protos.google.cloud.aiplatform.v1.IContext, protos.google.cloud.aiplatform.v1.IGetContextRequest | null | undefined, {} | null | undefined>): void;
    /**
     * Updates a stored Context.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {google.cloud.aiplatform.v1.Context} request.context
     *   Required. The Context containing updates.
     *   The Context's {@link protos.google.cloud.aiplatform.v1.Context.name|Context.name} field
     *   is used to identify the Context to be updated. Format:
     *   `projects/{project}/locations/{location}/metadataStores/{metadatastore}/contexts/{context}`
     * @param {google.protobuf.FieldMask} [request.updateMask]
     *   Optional. A FieldMask indicating which fields should be updated.
     * @param {boolean} request.allowMissing
     *   If set to true, and the {@link protos.google.cloud.aiplatform.v1.Context|Context} is
     *   not found, a new {@link protos.google.cloud.aiplatform.v1.Context|Context} is created.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing {@link protos.google.cloud.aiplatform.v1.Context|Context}.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#regular-methods | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1/metadata_service.update_context.js</caption>
     * region_tag:aiplatform_v1_generated_MetadataService_UpdateContext_async
     */
    updateContext(request?: protos.google.cloud.aiplatform.v1.IUpdateContextRequest, options?: CallOptions): Promise<[
        protos.google.cloud.aiplatform.v1.IContext,
        protos.google.cloud.aiplatform.v1.IUpdateContextRequest | undefined,
        {} | undefined
    ]>;
    updateContext(request: protos.google.cloud.aiplatform.v1.IUpdateContextRequest, options: CallOptions, callback: Callback<protos.google.cloud.aiplatform.v1.IContext, protos.google.cloud.aiplatform.v1.IUpdateContextRequest | null | undefined, {} | null | undefined>): void;
    updateContext(request: protos.google.cloud.aiplatform.v1.IUpdateContextRequest, callback: Callback<protos.google.cloud.aiplatform.v1.IContext, protos.google.cloud.aiplatform.v1.IUpdateContextRequest | null | undefined, {} | null | undefined>): void;
    /**
     * Adds a set of Artifacts and Executions to a Context. If any of the
     * Artifacts or Executions have already been added to a Context, they are
     * simply skipped.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.context
     *   Required. The resource name of the Context that the Artifacts and
     *   Executions belong to. Format:
     *   `projects/{project}/locations/{location}/metadataStores/{metadatastore}/contexts/{context}`
     * @param {string[]} request.artifacts
     *   The resource names of the Artifacts to attribute to the Context.
     *
     *   Format:
     *   `projects/{project}/locations/{location}/metadataStores/{metadatastore}/artifacts/{artifact}`
     * @param {string[]} request.executions
     *   The resource names of the Executions to associate with the
     *   Context.
     *
     *   Format:
     *   `projects/{project}/locations/{location}/metadataStores/{metadatastore}/executions/{execution}`
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing {@link protos.google.cloud.aiplatform.v1.AddContextArtifactsAndExecutionsResponse|AddContextArtifactsAndExecutionsResponse}.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#regular-methods | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1/metadata_service.add_context_artifacts_and_executions.js</caption>
     * region_tag:aiplatform_v1_generated_MetadataService_AddContextArtifactsAndExecutions_async
     */
    addContextArtifactsAndExecutions(request?: protos.google.cloud.aiplatform.v1.IAddContextArtifactsAndExecutionsRequest, options?: CallOptions): Promise<[
        protos.google.cloud.aiplatform.v1.IAddContextArtifactsAndExecutionsResponse,
        (protos.google.cloud.aiplatform.v1.IAddContextArtifactsAndExecutionsRequest | undefined),
        {} | undefined
    ]>;
    addContextArtifactsAndExecutions(request: protos.google.cloud.aiplatform.v1.IAddContextArtifactsAndExecutionsRequest, options: CallOptions, callback: Callback<protos.google.cloud.aiplatform.v1.IAddContextArtifactsAndExecutionsResponse, protos.google.cloud.aiplatform.v1.IAddContextArtifactsAndExecutionsRequest | null | undefined, {} | null | undefined>): void;
    addContextArtifactsAndExecutions(request: protos.google.cloud.aiplatform.v1.IAddContextArtifactsAndExecutionsRequest, callback: Callback<protos.google.cloud.aiplatform.v1.IAddContextArtifactsAndExecutionsResponse, protos.google.cloud.aiplatform.v1.IAddContextArtifactsAndExecutionsRequest | null | undefined, {} | null | undefined>): void;
    /**
     * Adds a set of Contexts as children to a parent Context. If any of the
     * child Contexts have already been added to the parent Context, they are
     * simply skipped. If this call would create a cycle or cause any Context to
     * have more than 10 parents, the request will fail with an INVALID_ARGUMENT
     * error.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.context
     *   Required. The resource name of the parent Context.
     *
     *   Format:
     *   `projects/{project}/locations/{location}/metadataStores/{metadatastore}/contexts/{context}`
     * @param {string[]} request.childContexts
     *   The resource names of the child Contexts.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing {@link protos.google.cloud.aiplatform.v1.AddContextChildrenResponse|AddContextChildrenResponse}.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#regular-methods | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1/metadata_service.add_context_children.js</caption>
     * region_tag:aiplatform_v1_generated_MetadataService_AddContextChildren_async
     */
    addContextChildren(request?: protos.google.cloud.aiplatform.v1.IAddContextChildrenRequest, options?: CallOptions): Promise<[
        protos.google.cloud.aiplatform.v1.IAddContextChildrenResponse,
        protos.google.cloud.aiplatform.v1.IAddContextChildrenRequest | undefined,
        {} | undefined
    ]>;
    addContextChildren(request: protos.google.cloud.aiplatform.v1.IAddContextChildrenRequest, options: CallOptions, callback: Callback<protos.google.cloud.aiplatform.v1.IAddContextChildrenResponse, protos.google.cloud.aiplatform.v1.IAddContextChildrenRequest | null | undefined, {} | null | undefined>): void;
    addContextChildren(request: protos.google.cloud.aiplatform.v1.IAddContextChildrenRequest, callback: Callback<protos.google.cloud.aiplatform.v1.IAddContextChildrenResponse, protos.google.cloud.aiplatform.v1.IAddContextChildrenRequest | null | undefined, {} | null | undefined>): void;
    /**
     * Remove a set of children contexts from a parent Context. If any of the
     * child Contexts were NOT added to the parent Context, they are
     * simply skipped.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.context
     *   Required. The resource name of the parent Context.
     *
     *   Format:
     *   `projects/{project}/locations/{location}/metadataStores/{metadatastore}/contexts/{context}`
     * @param {string[]} request.childContexts
     *   The resource names of the child Contexts.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing {@link protos.google.cloud.aiplatform.v1.RemoveContextChildrenResponse|RemoveContextChildrenResponse}.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#regular-methods | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1/metadata_service.remove_context_children.js</caption>
     * region_tag:aiplatform_v1_generated_MetadataService_RemoveContextChildren_async
     */
    removeContextChildren(request?: protos.google.cloud.aiplatform.v1.IRemoveContextChildrenRequest, options?: CallOptions): Promise<[
        protos.google.cloud.aiplatform.v1.IRemoveContextChildrenResponse,
        (protos.google.cloud.aiplatform.v1.IRemoveContextChildrenRequest | undefined),
        {} | undefined
    ]>;
    removeContextChildren(request: protos.google.cloud.aiplatform.v1.IRemoveContextChildrenRequest, options: CallOptions, callback: Callback<protos.google.cloud.aiplatform.v1.IRemoveContextChildrenResponse, protos.google.cloud.aiplatform.v1.IRemoveContextChildrenRequest | null | undefined, {} | null | undefined>): void;
    removeContextChildren(request: protos.google.cloud.aiplatform.v1.IRemoveContextChildrenRequest, callback: Callback<protos.google.cloud.aiplatform.v1.IRemoveContextChildrenResponse, protos.google.cloud.aiplatform.v1.IRemoveContextChildrenRequest | null | undefined, {} | null | undefined>): void;
    /**
     * Retrieves Artifacts and Executions within the specified Context, connected
     * by Event edges and returned as a LineageSubgraph.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.context
     *   Required. The resource name of the Context whose Artifacts and Executions
     *   should be retrieved as a LineageSubgraph.
     *   Format:
     *   `projects/{project}/locations/{location}/metadataStores/{metadatastore}/contexts/{context}`
     *
     *   The request may error with FAILED_PRECONDITION if the number of Artifacts,
     *   the number of Executions, or the number of Events that would be returned
     *   for the Context exceeds 1000.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing {@link protos.google.cloud.aiplatform.v1.LineageSubgraph|LineageSubgraph}.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#regular-methods | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1/metadata_service.query_context_lineage_subgraph.js</caption>
     * region_tag:aiplatform_v1_generated_MetadataService_QueryContextLineageSubgraph_async
     */
    queryContextLineageSubgraph(request?: protos.google.cloud.aiplatform.v1.IQueryContextLineageSubgraphRequest, options?: CallOptions): Promise<[
        protos.google.cloud.aiplatform.v1.ILineageSubgraph,
        (protos.google.cloud.aiplatform.v1.IQueryContextLineageSubgraphRequest | undefined),
        {} | undefined
    ]>;
    queryContextLineageSubgraph(request: protos.google.cloud.aiplatform.v1.IQueryContextLineageSubgraphRequest, options: CallOptions, callback: Callback<protos.google.cloud.aiplatform.v1.ILineageSubgraph, protos.google.cloud.aiplatform.v1.IQueryContextLineageSubgraphRequest | null | undefined, {} | null | undefined>): void;
    queryContextLineageSubgraph(request: protos.google.cloud.aiplatform.v1.IQueryContextLineageSubgraphRequest, callback: Callback<protos.google.cloud.aiplatform.v1.ILineageSubgraph, protos.google.cloud.aiplatform.v1.IQueryContextLineageSubgraphRequest | null | undefined, {} | null | undefined>): void;
    /**
     * Creates an Execution associated with a MetadataStore.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. The resource name of the MetadataStore where the Execution should
     *   be created.
     *   Format:
     *   `projects/{project}/locations/{location}/metadataStores/{metadatastore}`
     * @param {google.cloud.aiplatform.v1.Execution} request.execution
     *   Required. The Execution to create.
     * @param {string} request.executionId
     *   The {execution} portion of the resource name with the format:
     *   `projects/{project}/locations/{location}/metadataStores/{metadatastore}/executions/{execution}`
     *   If not provided, the Execution's ID will be a UUID generated by the
     *   service.
     *   Must be 4-128 characters in length. Valid characters are `/{@link protos.0-9|a-z}-/`.
     *   Must be unique across all Executions in the parent MetadataStore.
     *   (Otherwise the request will fail with ALREADY_EXISTS, or PERMISSION_DENIED
     *   if the caller can't view the preexisting Execution.)
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing {@link protos.google.cloud.aiplatform.v1.Execution|Execution}.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#regular-methods | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1/metadata_service.create_execution.js</caption>
     * region_tag:aiplatform_v1_generated_MetadataService_CreateExecution_async
     */
    createExecution(request?: protos.google.cloud.aiplatform.v1.ICreateExecutionRequest, options?: CallOptions): Promise<[
        protos.google.cloud.aiplatform.v1.IExecution,
        protos.google.cloud.aiplatform.v1.ICreateExecutionRequest | undefined,
        {} | undefined
    ]>;
    createExecution(request: protos.google.cloud.aiplatform.v1.ICreateExecutionRequest, options: CallOptions, callback: Callback<protos.google.cloud.aiplatform.v1.IExecution, protos.google.cloud.aiplatform.v1.ICreateExecutionRequest | null | undefined, {} | null | undefined>): void;
    createExecution(request: protos.google.cloud.aiplatform.v1.ICreateExecutionRequest, callback: Callback<protos.google.cloud.aiplatform.v1.IExecution, protos.google.cloud.aiplatform.v1.ICreateExecutionRequest | null | undefined, {} | null | undefined>): void;
    /**
     * Retrieves a specific Execution.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.name
     *   Required. The resource name of the Execution to retrieve.
     *   Format:
     *   `projects/{project}/locations/{location}/metadataStores/{metadatastore}/executions/{execution}`
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing {@link protos.google.cloud.aiplatform.v1.Execution|Execution}.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#regular-methods | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1/metadata_service.get_execution.js</caption>
     * region_tag:aiplatform_v1_generated_MetadataService_GetExecution_async
     */
    getExecution(request?: protos.google.cloud.aiplatform.v1.IGetExecutionRequest, options?: CallOptions): Promise<[
        protos.google.cloud.aiplatform.v1.IExecution,
        protos.google.cloud.aiplatform.v1.IGetExecutionRequest | undefined,
        {} | undefined
    ]>;
    getExecution(request: protos.google.cloud.aiplatform.v1.IGetExecutionRequest, options: CallOptions, callback: Callback<protos.google.cloud.aiplatform.v1.IExecution, protos.google.cloud.aiplatform.v1.IGetExecutionRequest | null | undefined, {} | null | undefined>): void;
    getExecution(request: protos.google.cloud.aiplatform.v1.IGetExecutionRequest, callback: Callback<protos.google.cloud.aiplatform.v1.IExecution, protos.google.cloud.aiplatform.v1.IGetExecutionRequest | null | undefined, {} | null | undefined>): void;
    /**
     * Updates a stored Execution.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {google.cloud.aiplatform.v1.Execution} request.execution
     *   Required. The Execution containing updates.
     *   The Execution's {@link protos.google.cloud.aiplatform.v1.Execution.name|Execution.name}
     *   field is used to identify the Execution to be updated. Format:
     *   `projects/{project}/locations/{location}/metadataStores/{metadatastore}/executions/{execution}`
     * @param {google.protobuf.FieldMask} [request.updateMask]
     *   Optional. A FieldMask indicating which fields should be updated.
     * @param {boolean} request.allowMissing
     *   If set to true, and the {@link protos.google.cloud.aiplatform.v1.Execution|Execution}
     *   is not found, a new {@link protos.google.cloud.aiplatform.v1.Execution|Execution} is
     *   created.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing {@link protos.google.cloud.aiplatform.v1.Execution|Execution}.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#regular-methods | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1/metadata_service.update_execution.js</caption>
     * region_tag:aiplatform_v1_generated_MetadataService_UpdateExecution_async
     */
    updateExecution(request?: protos.google.cloud.aiplatform.v1.IUpdateExecutionRequest, options?: CallOptions): Promise<[
        protos.google.cloud.aiplatform.v1.IExecution,
        protos.google.cloud.aiplatform.v1.IUpdateExecutionRequest | undefined,
        {} | undefined
    ]>;
    updateExecution(request: protos.google.cloud.aiplatform.v1.IUpdateExecutionRequest, options: CallOptions, callback: Callback<protos.google.cloud.aiplatform.v1.IExecution, protos.google.cloud.aiplatform.v1.IUpdateExecutionRequest | null | undefined, {} | null | undefined>): void;
    updateExecution(request: protos.google.cloud.aiplatform.v1.IUpdateExecutionRequest, callback: Callback<protos.google.cloud.aiplatform.v1.IExecution, protos.google.cloud.aiplatform.v1.IUpdateExecutionRequest | null | undefined, {} | null | undefined>): void;
    /**
     * Adds Events to the specified Execution. An Event indicates whether an
     * Artifact was used as an input or output for an Execution. If an Event
     * already exists between the Execution and the Artifact, the Event is
     * skipped.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.execution
     *   Required. The resource name of the Execution that the Events connect
     *   Artifacts with.
     *   Format:
     *   `projects/{project}/locations/{location}/metadataStores/{metadatastore}/executions/{execution}`
     * @param {number[]} request.events
     *   The Events to create and add.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing {@link protos.google.cloud.aiplatform.v1.AddExecutionEventsResponse|AddExecutionEventsResponse}.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#regular-methods | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1/metadata_service.add_execution_events.js</caption>
     * region_tag:aiplatform_v1_generated_MetadataService_AddExecutionEvents_async
     */
    addExecutionEvents(request?: protos.google.cloud.aiplatform.v1.IAddExecutionEventsRequest, options?: CallOptions): Promise<[
        protos.google.cloud.aiplatform.v1.IAddExecutionEventsResponse,
        protos.google.cloud.aiplatform.v1.IAddExecutionEventsRequest | undefined,
        {} | undefined
    ]>;
    addExecutionEvents(request: protos.google.cloud.aiplatform.v1.IAddExecutionEventsRequest, options: CallOptions, callback: Callback<protos.google.cloud.aiplatform.v1.IAddExecutionEventsResponse, protos.google.cloud.aiplatform.v1.IAddExecutionEventsRequest | null | undefined, {} | null | undefined>): void;
    addExecutionEvents(request: protos.google.cloud.aiplatform.v1.IAddExecutionEventsRequest, callback: Callback<protos.google.cloud.aiplatform.v1.IAddExecutionEventsResponse, protos.google.cloud.aiplatform.v1.IAddExecutionEventsRequest | null | undefined, {} | null | undefined>): void;
    /**
     * Obtains the set of input and output Artifacts for this Execution, in the
     * form of LineageSubgraph that also contains the Execution and connecting
     * Events.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.execution
     *   Required. The resource name of the Execution whose input and output
     *   Artifacts should be retrieved as a LineageSubgraph. Format:
     *   `projects/{project}/locations/{location}/metadataStores/{metadatastore}/executions/{execution}`
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing {@link protos.google.cloud.aiplatform.v1.LineageSubgraph|LineageSubgraph}.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#regular-methods | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1/metadata_service.query_execution_inputs_and_outputs.js</caption>
     * region_tag:aiplatform_v1_generated_MetadataService_QueryExecutionInputsAndOutputs_async
     */
    queryExecutionInputsAndOutputs(request?: protos.google.cloud.aiplatform.v1.IQueryExecutionInputsAndOutputsRequest, options?: CallOptions): Promise<[
        protos.google.cloud.aiplatform.v1.ILineageSubgraph,
        (protos.google.cloud.aiplatform.v1.IQueryExecutionInputsAndOutputsRequest | undefined),
        {} | undefined
    ]>;
    queryExecutionInputsAndOutputs(request: protos.google.cloud.aiplatform.v1.IQueryExecutionInputsAndOutputsRequest, options: CallOptions, callback: Callback<protos.google.cloud.aiplatform.v1.ILineageSubgraph, protos.google.cloud.aiplatform.v1.IQueryExecutionInputsAndOutputsRequest | null | undefined, {} | null | undefined>): void;
    queryExecutionInputsAndOutputs(request: protos.google.cloud.aiplatform.v1.IQueryExecutionInputsAndOutputsRequest, callback: Callback<protos.google.cloud.aiplatform.v1.ILineageSubgraph, protos.google.cloud.aiplatform.v1.IQueryExecutionInputsAndOutputsRequest | null | undefined, {} | null | undefined>): void;
    /**
     * Creates a MetadataSchema.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. The resource name of the MetadataStore where the MetadataSchema
     *   should be created. Format:
     *   `projects/{project}/locations/{location}/metadataStores/{metadatastore}`
     * @param {google.cloud.aiplatform.v1.MetadataSchema} request.metadataSchema
     *   Required. The MetadataSchema to create.
     * @param {string} request.metadataSchemaId
     *   The {metadata_schema} portion of the resource name with the format:
     *   `projects/{project}/locations/{location}/metadataStores/{metadatastore}/metadataSchemas/{metadataschema}`
     *   If not provided, the MetadataStore's ID will be a UUID generated by the
     *   service.
     *   Must be 4-128 characters in length. Valid characters are `/{@link protos.0-9|a-z}-/`.
     *   Must be unique across all MetadataSchemas in the parent Location.
     *   (Otherwise the request will fail with ALREADY_EXISTS, or PERMISSION_DENIED
     *   if the caller can't view the preexisting MetadataSchema.)
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing {@link protos.google.cloud.aiplatform.v1.MetadataSchema|MetadataSchema}.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#regular-methods | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1/metadata_service.create_metadata_schema.js</caption>
     * region_tag:aiplatform_v1_generated_MetadataService_CreateMetadataSchema_async
     */
    createMetadataSchema(request?: protos.google.cloud.aiplatform.v1.ICreateMetadataSchemaRequest, options?: CallOptions): Promise<[
        protos.google.cloud.aiplatform.v1.IMetadataSchema,
        (protos.google.cloud.aiplatform.v1.ICreateMetadataSchemaRequest | undefined),
        {} | undefined
    ]>;
    createMetadataSchema(request: protos.google.cloud.aiplatform.v1.ICreateMetadataSchemaRequest, options: CallOptions, callback: Callback<protos.google.cloud.aiplatform.v1.IMetadataSchema, protos.google.cloud.aiplatform.v1.ICreateMetadataSchemaRequest | null | undefined, {} | null | undefined>): void;
    createMetadataSchema(request: protos.google.cloud.aiplatform.v1.ICreateMetadataSchemaRequest, callback: Callback<protos.google.cloud.aiplatform.v1.IMetadataSchema, protos.google.cloud.aiplatform.v1.ICreateMetadataSchemaRequest | null | undefined, {} | null | undefined>): void;
    /**
     * Retrieves a specific MetadataSchema.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.name
     *   Required. The resource name of the MetadataSchema to retrieve.
     *   Format:
     *   `projects/{project}/locations/{location}/metadataStores/{metadatastore}/metadataSchemas/{metadataschema}`
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing {@link protos.google.cloud.aiplatform.v1.MetadataSchema|MetadataSchema}.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#regular-methods | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1/metadata_service.get_metadata_schema.js</caption>
     * region_tag:aiplatform_v1_generated_MetadataService_GetMetadataSchema_async
     */
    getMetadataSchema(request?: protos.google.cloud.aiplatform.v1.IGetMetadataSchemaRequest, options?: CallOptions): Promise<[
        protos.google.cloud.aiplatform.v1.IMetadataSchema,
        protos.google.cloud.aiplatform.v1.IGetMetadataSchemaRequest | undefined,
        {} | undefined
    ]>;
    getMetadataSchema(request: protos.google.cloud.aiplatform.v1.IGetMetadataSchemaRequest, options: CallOptions, callback: Callback<protos.google.cloud.aiplatform.v1.IMetadataSchema, protos.google.cloud.aiplatform.v1.IGetMetadataSchemaRequest | null | undefined, {} | null | undefined>): void;
    getMetadataSchema(request: protos.google.cloud.aiplatform.v1.IGetMetadataSchemaRequest, callback: Callback<protos.google.cloud.aiplatform.v1.IMetadataSchema, protos.google.cloud.aiplatform.v1.IGetMetadataSchemaRequest | null | undefined, {} | null | undefined>): void;
    /**
     * Retrieves lineage of an Artifact represented through Artifacts and
     * Executions connected by Event edges and returned as a LineageSubgraph.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.artifact
     *   Required. The resource name of the Artifact whose Lineage needs to be
     *   retrieved as a LineageSubgraph. Format:
     *   `projects/{project}/locations/{location}/metadataStores/{metadatastore}/artifacts/{artifact}`
     *
     *   The request may error with FAILED_PRECONDITION if the number of Artifacts,
     *   the number of Executions, or the number of Events that would be returned
     *   for the Context exceeds 1000.
     * @param {number} request.maxHops
     *   Specifies the size of the lineage graph in terms of number of hops from the
     *   specified artifact.
     *   Negative Value: INVALID_ARGUMENT error is returned
     *   0: Only input artifact is returned.
     *   No value: Transitive closure is performed to return the complete graph.
     * @param {string} request.filter
     *   Filter specifying the boolean condition for the Artifacts to satisfy in
     *   order to be part of the Lineage Subgraph.
     *   The syntax to define filter query is based on https://google.aip.dev/160.
     *   The supported set of filters include the following:
     *
     *   *  **Attribute filtering**:
     *      For example: `display_name = "test"`
     *      Supported fields include: `name`, `display_name`, `uri`, `state`,
     *      `schema_title`, `create_time`, and `update_time`.
     *      Time fields, such as `create_time` and `update_time`, require values
     *      specified in RFC-3339 format.
     *      For example: `create_time = "2020-11-19T11:30:00-04:00"`
     *   *  **Metadata field**:
     *      To filter on metadata fields use traversal operation as follows:
     *      `metadata.<field_name>.<type_value>`.
     *      For example: `metadata.field_1.number_value = 10.0`
     *      In case the field name contains special characters (such as colon), one
     *      can embed it inside double quote.
     *      For example: `metadata."field:1".number_value = 10.0`
     *
     *   Each of the above supported filter types can be combined together using
     *   logical operators (`AND` & `OR`). Maximum nested expression depth allowed
     *   is 5.
     *
     *   For example: `display_name = "test" AND metadata.field1.bool_value = true`.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing {@link protos.google.cloud.aiplatform.v1.LineageSubgraph|LineageSubgraph}.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#regular-methods | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1/metadata_service.query_artifact_lineage_subgraph.js</caption>
     * region_tag:aiplatform_v1_generated_MetadataService_QueryArtifactLineageSubgraph_async
     */
    queryArtifactLineageSubgraph(request?: protos.google.cloud.aiplatform.v1.IQueryArtifactLineageSubgraphRequest, options?: CallOptions): Promise<[
        protos.google.cloud.aiplatform.v1.ILineageSubgraph,
        (protos.google.cloud.aiplatform.v1.IQueryArtifactLineageSubgraphRequest | undefined),
        {} | undefined
    ]>;
    queryArtifactLineageSubgraph(request: protos.google.cloud.aiplatform.v1.IQueryArtifactLineageSubgraphRequest, options: CallOptions, callback: Callback<protos.google.cloud.aiplatform.v1.ILineageSubgraph, protos.google.cloud.aiplatform.v1.IQueryArtifactLineageSubgraphRequest | null | undefined, {} | null | undefined>): void;
    queryArtifactLineageSubgraph(request: protos.google.cloud.aiplatform.v1.IQueryArtifactLineageSubgraphRequest, callback: Callback<protos.google.cloud.aiplatform.v1.ILineageSubgraph, protos.google.cloud.aiplatform.v1.IQueryArtifactLineageSubgraphRequest | null | undefined, {} | null | undefined>): void;
    /**
     * Initializes a MetadataStore, including allocation of resources.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. The resource name of the Location where the MetadataStore should
     *   be created.
     *   Format: `projects/{project}/locations/{location}/`
     * @param {google.cloud.aiplatform.v1.MetadataStore} request.metadataStore
     *   Required. The MetadataStore to create.
     * @param {string} request.metadataStoreId
     *   The {metadatastore} portion of the resource name with the format:
     *   `projects/{project}/locations/{location}/metadataStores/{metadatastore}`
     *   If not provided, the MetadataStore's ID will be a UUID generated by the
     *   service.
     *   Must be 4-128 characters in length. Valid characters are `/{@link protos.0-9|a-z}-/`.
     *   Must be unique across all MetadataStores in the parent Location.
     *   (Otherwise the request will fail with ALREADY_EXISTS, or PERMISSION_DENIED
     *   if the caller can't view the preexisting MetadataStore.)
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing
     *   a long running operation. Its `promise()` method returns a promise
     *   you can `await` for.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#long-running-operations | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1/metadata_service.create_metadata_store.js</caption>
     * region_tag:aiplatform_v1_generated_MetadataService_CreateMetadataStore_async
     */
    createMetadataStore(request?: protos.google.cloud.aiplatform.v1.ICreateMetadataStoreRequest, options?: CallOptions): Promise<[
        LROperation<protos.google.cloud.aiplatform.v1.IMetadataStore, protos.google.cloud.aiplatform.v1.ICreateMetadataStoreOperationMetadata>,
        protos.google.longrunning.IOperation | undefined,
        {} | undefined
    ]>;
    createMetadataStore(request: protos.google.cloud.aiplatform.v1.ICreateMetadataStoreRequest, options: CallOptions, callback: Callback<LROperation<protos.google.cloud.aiplatform.v1.IMetadataStore, protos.google.cloud.aiplatform.v1.ICreateMetadataStoreOperationMetadata>, protos.google.longrunning.IOperation | null | undefined, {} | null | undefined>): void;
    createMetadataStore(request: protos.google.cloud.aiplatform.v1.ICreateMetadataStoreRequest, callback: Callback<LROperation<protos.google.cloud.aiplatform.v1.IMetadataStore, protos.google.cloud.aiplatform.v1.ICreateMetadataStoreOperationMetadata>, protos.google.longrunning.IOperation | null | undefined, {} | null | undefined>): void;
    /**
     * Check the status of the long running operation returned by `createMetadataStore()`.
     * @param {String} name
     *   The operation name that will be passed.
     * @returns {Promise} - The promise which resolves to an object.
     *   The decoded operation object has result and metadata field to get information from.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#long-running-operations | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1/metadata_service.create_metadata_store.js</caption>
     * region_tag:aiplatform_v1_generated_MetadataService_CreateMetadataStore_async
     */
    checkCreateMetadataStoreProgress(name: string): Promise<LROperation<protos.google.cloud.aiplatform.v1.MetadataStore, protos.google.cloud.aiplatform.v1.CreateMetadataStoreOperationMetadata>>;
    /**
     * Deletes a single MetadataStore and all its child resources (Artifacts,
     * Executions, and Contexts).
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.name
     *   Required. The resource name of the MetadataStore to delete.
     *   Format:
     *   `projects/{project}/locations/{location}/metadataStores/{metadatastore}`
     * @param {boolean} request.force
     *   Deprecated: Field is no longer supported.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing
     *   a long running operation. Its `promise()` method returns a promise
     *   you can `await` for.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#long-running-operations | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1/metadata_service.delete_metadata_store.js</caption>
     * region_tag:aiplatform_v1_generated_MetadataService_DeleteMetadataStore_async
     */
    deleteMetadataStore(request?: protos.google.cloud.aiplatform.v1.IDeleteMetadataStoreRequest, options?: CallOptions): Promise<[
        LROperation<protos.google.protobuf.IEmpty, protos.google.cloud.aiplatform.v1.IDeleteMetadataStoreOperationMetadata>,
        protos.google.longrunning.IOperation | undefined,
        {} | undefined
    ]>;
    deleteMetadataStore(request: protos.google.cloud.aiplatform.v1.IDeleteMetadataStoreRequest, options: CallOptions, callback: Callback<LROperation<protos.google.protobuf.IEmpty, protos.google.cloud.aiplatform.v1.IDeleteMetadataStoreOperationMetadata>, protos.google.longrunning.IOperation | null | undefined, {} | null | undefined>): void;
    deleteMetadataStore(request: protos.google.cloud.aiplatform.v1.IDeleteMetadataStoreRequest, callback: Callback<LROperation<protos.google.protobuf.IEmpty, protos.google.cloud.aiplatform.v1.IDeleteMetadataStoreOperationMetadata>, protos.google.longrunning.IOperation | null | undefined, {} | null | undefined>): void;
    /**
     * Check the status of the long running operation returned by `deleteMetadataStore()`.
     * @param {String} name
     *   The operation name that will be passed.
     * @returns {Promise} - The promise which resolves to an object.
     *   The decoded operation object has result and metadata field to get information from.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#long-running-operations | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1/metadata_service.delete_metadata_store.js</caption>
     * region_tag:aiplatform_v1_generated_MetadataService_DeleteMetadataStore_async
     */
    checkDeleteMetadataStoreProgress(name: string): Promise<LROperation<protos.google.protobuf.Empty, protos.google.cloud.aiplatform.v1.DeleteMetadataStoreOperationMetadata>>;
    /**
     * Deletes an Artifact.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.name
     *   Required. The resource name of the Artifact to delete.
     *   Format:
     *   `projects/{project}/locations/{location}/metadataStores/{metadatastore}/artifacts/{artifact}`
     * @param {string} [request.etag]
     *   Optional. The etag of the Artifact to delete.
     *   If this is provided, it must match the server's etag. Otherwise, the
     *   request will fail with a FAILED_PRECONDITION.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing
     *   a long running operation. Its `promise()` method returns a promise
     *   you can `await` for.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#long-running-operations | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1/metadata_service.delete_artifact.js</caption>
     * region_tag:aiplatform_v1_generated_MetadataService_DeleteArtifact_async
     */
    deleteArtifact(request?: protos.google.cloud.aiplatform.v1.IDeleteArtifactRequest, options?: CallOptions): Promise<[
        LROperation<protos.google.protobuf.IEmpty, protos.google.cloud.aiplatform.v1.IDeleteOperationMetadata>,
        protos.google.longrunning.IOperation | undefined,
        {} | undefined
    ]>;
    deleteArtifact(request: protos.google.cloud.aiplatform.v1.IDeleteArtifactRequest, options: CallOptions, callback: Callback<LROperation<protos.google.protobuf.IEmpty, protos.google.cloud.aiplatform.v1.IDeleteOperationMetadata>, protos.google.longrunning.IOperation | null | undefined, {} | null | undefined>): void;
    deleteArtifact(request: protos.google.cloud.aiplatform.v1.IDeleteArtifactRequest, callback: Callback<LROperation<protos.google.protobuf.IEmpty, protos.google.cloud.aiplatform.v1.IDeleteOperationMetadata>, protos.google.longrunning.IOperation | null | undefined, {} | null | undefined>): void;
    /**
     * Check the status of the long running operation returned by `deleteArtifact()`.
     * @param {String} name
     *   The operation name that will be passed.
     * @returns {Promise} - The promise which resolves to an object.
     *   The decoded operation object has result and metadata field to get information from.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#long-running-operations | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1/metadata_service.delete_artifact.js</caption>
     * region_tag:aiplatform_v1_generated_MetadataService_DeleteArtifact_async
     */
    checkDeleteArtifactProgress(name: string): Promise<LROperation<protos.google.protobuf.Empty, protos.google.cloud.aiplatform.v1.DeleteOperationMetadata>>;
    /**
     * Purges Artifacts.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. The metadata store to purge Artifacts from.
     *   Format:
     *   `projects/{project}/locations/{location}/metadataStores/{metadatastore}`
     * @param {string} request.filter
     *   Required. A required filter matching the Artifacts to be purged.
     *   E.g., `update_time <= 2020-11-19T11:30:00-04:00`.
     * @param {boolean} [request.force]
     *   Optional. Flag to indicate to actually perform the purge.
     *   If `force` is set to false, the method will return a sample of
     *   Artifact names that would be deleted.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing
     *   a long running operation. Its `promise()` method returns a promise
     *   you can `await` for.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#long-running-operations | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1/metadata_service.purge_artifacts.js</caption>
     * region_tag:aiplatform_v1_generated_MetadataService_PurgeArtifacts_async
     */
    purgeArtifacts(request?: protos.google.cloud.aiplatform.v1.IPurgeArtifactsRequest, options?: CallOptions): Promise<[
        LROperation<protos.google.cloud.aiplatform.v1.IPurgeArtifactsResponse, protos.google.cloud.aiplatform.v1.IPurgeArtifactsMetadata>,
        protos.google.longrunning.IOperation | undefined,
        {} | undefined
    ]>;
    purgeArtifacts(request: protos.google.cloud.aiplatform.v1.IPurgeArtifactsRequest, options: CallOptions, callback: Callback<LROperation<protos.google.cloud.aiplatform.v1.IPurgeArtifactsResponse, protos.google.cloud.aiplatform.v1.IPurgeArtifactsMetadata>, protos.google.longrunning.IOperation | null | undefined, {} | null | undefined>): void;
    purgeArtifacts(request: protos.google.cloud.aiplatform.v1.IPurgeArtifactsRequest, callback: Callback<LROperation<protos.google.cloud.aiplatform.v1.IPurgeArtifactsResponse, protos.google.cloud.aiplatform.v1.IPurgeArtifactsMetadata>, protos.google.longrunning.IOperation | null | undefined, {} | null | undefined>): void;
    /**
     * Check the status of the long running operation returned by `purgeArtifacts()`.
     * @param {String} name
     *   The operation name that will be passed.
     * @returns {Promise} - The promise which resolves to an object.
     *   The decoded operation object has result and metadata field to get information from.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#long-running-operations | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1/metadata_service.purge_artifacts.js</caption>
     * region_tag:aiplatform_v1_generated_MetadataService_PurgeArtifacts_async
     */
    checkPurgeArtifactsProgress(name: string): Promise<LROperation<protos.google.cloud.aiplatform.v1.PurgeArtifactsResponse, protos.google.cloud.aiplatform.v1.PurgeArtifactsMetadata>>;
    /**
     * Deletes a stored Context.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.name
     *   Required. The resource name of the Context to delete.
     *   Format:
     *   `projects/{project}/locations/{location}/metadataStores/{metadatastore}/contexts/{context}`
     * @param {boolean} request.force
     *   The force deletion semantics is still undefined.
     *   Users should not use this field.
     * @param {string} [request.etag]
     *   Optional. The etag of the Context to delete.
     *   If this is provided, it must match the server's etag. Otherwise, the
     *   request will fail with a FAILED_PRECONDITION.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing
     *   a long running operation. Its `promise()` method returns a promise
     *   you can `await` for.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#long-running-operations | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1/metadata_service.delete_context.js</caption>
     * region_tag:aiplatform_v1_generated_MetadataService_DeleteContext_async
     */
    deleteContext(request?: protos.google.cloud.aiplatform.v1.IDeleteContextRequest, options?: CallOptions): Promise<[
        LROperation<protos.google.protobuf.IEmpty, protos.google.cloud.aiplatform.v1.IDeleteOperationMetadata>,
        protos.google.longrunning.IOperation | undefined,
        {} | undefined
    ]>;
    deleteContext(request: protos.google.cloud.aiplatform.v1.IDeleteContextRequest, options: CallOptions, callback: Callback<LROperation<protos.google.protobuf.IEmpty, protos.google.cloud.aiplatform.v1.IDeleteOperationMetadata>, protos.google.longrunning.IOperation | null | undefined, {} | null | undefined>): void;
    deleteContext(request: protos.google.cloud.aiplatform.v1.IDeleteContextRequest, callback: Callback<LROperation<protos.google.protobuf.IEmpty, protos.google.cloud.aiplatform.v1.IDeleteOperationMetadata>, protos.google.longrunning.IOperation | null | undefined, {} | null | undefined>): void;
    /**
     * Check the status of the long running operation returned by `deleteContext()`.
     * @param {String} name
     *   The operation name that will be passed.
     * @returns {Promise} - The promise which resolves to an object.
     *   The decoded operation object has result and metadata field to get information from.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#long-running-operations | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1/metadata_service.delete_context.js</caption>
     * region_tag:aiplatform_v1_generated_MetadataService_DeleteContext_async
     */
    checkDeleteContextProgress(name: string): Promise<LROperation<protos.google.protobuf.Empty, protos.google.cloud.aiplatform.v1.DeleteOperationMetadata>>;
    /**
     * Purges Contexts.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. The metadata store to purge Contexts from.
     *   Format:
     *   `projects/{project}/locations/{location}/metadataStores/{metadatastore}`
     * @param {string} request.filter
     *   Required. A required filter matching the Contexts to be purged.
     *   E.g., `update_time <= 2020-11-19T11:30:00-04:00`.
     * @param {boolean} [request.force]
     *   Optional. Flag to indicate to actually perform the purge.
     *   If `force` is set to false, the method will return a sample of
     *   Context names that would be deleted.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing
     *   a long running operation. Its `promise()` method returns a promise
     *   you can `await` for.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#long-running-operations | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1/metadata_service.purge_contexts.js</caption>
     * region_tag:aiplatform_v1_generated_MetadataService_PurgeContexts_async
     */
    purgeContexts(request?: protos.google.cloud.aiplatform.v1.IPurgeContextsRequest, options?: CallOptions): Promise<[
        LROperation<protos.google.cloud.aiplatform.v1.IPurgeContextsResponse, protos.google.cloud.aiplatform.v1.IPurgeContextsMetadata>,
        protos.google.longrunning.IOperation | undefined,
        {} | undefined
    ]>;
    purgeContexts(request: protos.google.cloud.aiplatform.v1.IPurgeContextsRequest, options: CallOptions, callback: Callback<LROperation<protos.google.cloud.aiplatform.v1.IPurgeContextsResponse, protos.google.cloud.aiplatform.v1.IPurgeContextsMetadata>, protos.google.longrunning.IOperation | null | undefined, {} | null | undefined>): void;
    purgeContexts(request: protos.google.cloud.aiplatform.v1.IPurgeContextsRequest, callback: Callback<LROperation<protos.google.cloud.aiplatform.v1.IPurgeContextsResponse, protos.google.cloud.aiplatform.v1.IPurgeContextsMetadata>, protos.google.longrunning.IOperation | null | undefined, {} | null | undefined>): void;
    /**
     * Check the status of the long running operation returned by `purgeContexts()`.
     * @param {String} name
     *   The operation name that will be passed.
     * @returns {Promise} - The promise which resolves to an object.
     *   The decoded operation object has result and metadata field to get information from.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#long-running-operations | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1/metadata_service.purge_contexts.js</caption>
     * region_tag:aiplatform_v1_generated_MetadataService_PurgeContexts_async
     */
    checkPurgeContextsProgress(name: string): Promise<LROperation<protos.google.cloud.aiplatform.v1.PurgeContextsResponse, protos.google.cloud.aiplatform.v1.PurgeContextsMetadata>>;
    /**
     * Deletes an Execution.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.name
     *   Required. The resource name of the Execution to delete.
     *   Format:
     *   `projects/{project}/locations/{location}/metadataStores/{metadatastore}/executions/{execution}`
     * @param {string} [request.etag]
     *   Optional. The etag of the Execution to delete.
     *   If this is provided, it must match the server's etag. Otherwise, the
     *   request will fail with a FAILED_PRECONDITION.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing
     *   a long running operation. Its `promise()` method returns a promise
     *   you can `await` for.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#long-running-operations | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1/metadata_service.delete_execution.js</caption>
     * region_tag:aiplatform_v1_generated_MetadataService_DeleteExecution_async
     */
    deleteExecution(request?: protos.google.cloud.aiplatform.v1.IDeleteExecutionRequest, options?: CallOptions): Promise<[
        LROperation<protos.google.protobuf.IEmpty, protos.google.cloud.aiplatform.v1.IDeleteOperationMetadata>,
        protos.google.longrunning.IOperation | undefined,
        {} | undefined
    ]>;
    deleteExecution(request: protos.google.cloud.aiplatform.v1.IDeleteExecutionRequest, options: CallOptions, callback: Callback<LROperation<protos.google.protobuf.IEmpty, protos.google.cloud.aiplatform.v1.IDeleteOperationMetadata>, protos.google.longrunning.IOperation | null | undefined, {} | null | undefined>): void;
    deleteExecution(request: protos.google.cloud.aiplatform.v1.IDeleteExecutionRequest, callback: Callback<LROperation<protos.google.protobuf.IEmpty, protos.google.cloud.aiplatform.v1.IDeleteOperationMetadata>, protos.google.longrunning.IOperation | null | undefined, {} | null | undefined>): void;
    /**
     * Check the status of the long running operation returned by `deleteExecution()`.
     * @param {String} name
     *   The operation name that will be passed.
     * @returns {Promise} - The promise which resolves to an object.
     *   The decoded operation object has result and metadata field to get information from.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#long-running-operations | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1/metadata_service.delete_execution.js</caption>
     * region_tag:aiplatform_v1_generated_MetadataService_DeleteExecution_async
     */
    checkDeleteExecutionProgress(name: string): Promise<LROperation<protos.google.protobuf.Empty, protos.google.cloud.aiplatform.v1.DeleteOperationMetadata>>;
    /**
     * Purges Executions.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. The metadata store to purge Executions from.
     *   Format:
     *   `projects/{project}/locations/{location}/metadataStores/{metadatastore}`
     * @param {string} request.filter
     *   Required. A required filter matching the Executions to be purged.
     *   E.g., `update_time <= 2020-11-19T11:30:00-04:00`.
     * @param {boolean} [request.force]
     *   Optional. Flag to indicate to actually perform the purge.
     *   If `force` is set to false, the method will return a sample of
     *   Execution names that would be deleted.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing
     *   a long running operation. Its `promise()` method returns a promise
     *   you can `await` for.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#long-running-operations | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1/metadata_service.purge_executions.js</caption>
     * region_tag:aiplatform_v1_generated_MetadataService_PurgeExecutions_async
     */
    purgeExecutions(request?: protos.google.cloud.aiplatform.v1.IPurgeExecutionsRequest, options?: CallOptions): Promise<[
        LROperation<protos.google.cloud.aiplatform.v1.IPurgeExecutionsResponse, protos.google.cloud.aiplatform.v1.IPurgeExecutionsMetadata>,
        protos.google.longrunning.IOperation | undefined,
        {} | undefined
    ]>;
    purgeExecutions(request: protos.google.cloud.aiplatform.v1.IPurgeExecutionsRequest, options: CallOptions, callback: Callback<LROperation<protos.google.cloud.aiplatform.v1.IPurgeExecutionsResponse, protos.google.cloud.aiplatform.v1.IPurgeExecutionsMetadata>, protos.google.longrunning.IOperation | null | undefined, {} | null | undefined>): void;
    purgeExecutions(request: protos.google.cloud.aiplatform.v1.IPurgeExecutionsRequest, callback: Callback<LROperation<protos.google.cloud.aiplatform.v1.IPurgeExecutionsResponse, protos.google.cloud.aiplatform.v1.IPurgeExecutionsMetadata>, protos.google.longrunning.IOperation | null | undefined, {} | null | undefined>): void;
    /**
     * Check the status of the long running operation returned by `purgeExecutions()`.
     * @param {String} name
     *   The operation name that will be passed.
     * @returns {Promise} - The promise which resolves to an object.
     *   The decoded operation object has result and metadata field to get information from.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#long-running-operations | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1/metadata_service.purge_executions.js</caption>
     * region_tag:aiplatform_v1_generated_MetadataService_PurgeExecutions_async
     */
    checkPurgeExecutionsProgress(name: string): Promise<LROperation<protos.google.cloud.aiplatform.v1.PurgeExecutionsResponse, protos.google.cloud.aiplatform.v1.PurgeExecutionsMetadata>>;
    /**
     * Lists MetadataStores for a Location.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. The Location whose MetadataStores should be listed.
     *   Format:
     *   `projects/{project}/locations/{location}`
     * @param {number} request.pageSize
     *   The maximum number of Metadata Stores to return. The service may return
     *   fewer.
     *   Must be in range 1-1000, inclusive. Defaults to 100.
     * @param {string} request.pageToken
     *   A page token, received from a previous
     *   {@link protos.google.cloud.aiplatform.v1.MetadataService.ListMetadataStores|MetadataService.ListMetadataStores}
     *   call. Provide this to retrieve the subsequent page.
     *
     *   When paginating, all other provided parameters must match the call that
     *   provided the page token. (Otherwise the request will fail with
     *   INVALID_ARGUMENT error.)
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is Array of {@link protos.google.cloud.aiplatform.v1.MetadataStore|MetadataStore}.
     *   The client library will perform auto-pagination by default: it will call the API as many
     *   times as needed and will merge results from all the pages into this array.
     *   Note that it can affect your quota.
     *   We recommend using `listMetadataStoresAsync()`
     *   method described below for async iteration which you can stop as needed.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     */
    listMetadataStores(request?: protos.google.cloud.aiplatform.v1.IListMetadataStoresRequest, options?: CallOptions): Promise<[
        protos.google.cloud.aiplatform.v1.IMetadataStore[],
        protos.google.cloud.aiplatform.v1.IListMetadataStoresRequest | null,
        protos.google.cloud.aiplatform.v1.IListMetadataStoresResponse
    ]>;
    listMetadataStores(request: protos.google.cloud.aiplatform.v1.IListMetadataStoresRequest, options: CallOptions, callback: PaginationCallback<protos.google.cloud.aiplatform.v1.IListMetadataStoresRequest, protos.google.cloud.aiplatform.v1.IListMetadataStoresResponse | null | undefined, protos.google.cloud.aiplatform.v1.IMetadataStore>): void;
    listMetadataStores(request: protos.google.cloud.aiplatform.v1.IListMetadataStoresRequest, callback: PaginationCallback<protos.google.cloud.aiplatform.v1.IListMetadataStoresRequest, protos.google.cloud.aiplatform.v1.IListMetadataStoresResponse | null | undefined, protos.google.cloud.aiplatform.v1.IMetadataStore>): void;
    /**
     * Equivalent to `listMetadataStores`, but returns a NodeJS Stream object.
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. The Location whose MetadataStores should be listed.
     *   Format:
     *   `projects/{project}/locations/{location}`
     * @param {number} request.pageSize
     *   The maximum number of Metadata Stores to return. The service may return
     *   fewer.
     *   Must be in range 1-1000, inclusive. Defaults to 100.
     * @param {string} request.pageToken
     *   A page token, received from a previous
     *   {@link protos.google.cloud.aiplatform.v1.MetadataService.ListMetadataStores|MetadataService.ListMetadataStores}
     *   call. Provide this to retrieve the subsequent page.
     *
     *   When paginating, all other provided parameters must match the call that
     *   provided the page token. (Otherwise the request will fail with
     *   INVALID_ARGUMENT error.)
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Stream}
     *   An object stream which emits an object representing {@link protos.google.cloud.aiplatform.v1.MetadataStore|MetadataStore} on 'data' event.
     *   The client library will perform auto-pagination by default: it will call the API as many
     *   times as needed. Note that it can affect your quota.
     *   We recommend using `listMetadataStoresAsync()`
     *   method described below for async iteration which you can stop as needed.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     */
    listMetadataStoresStream(request?: protos.google.cloud.aiplatform.v1.IListMetadataStoresRequest, options?: CallOptions): Transform;
    /**
     * Equivalent to `listMetadataStores`, but returns an iterable object.
     *
     * `for`-`await`-`of` syntax is used with the iterable to get response elements on-demand.
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. The Location whose MetadataStores should be listed.
     *   Format:
     *   `projects/{project}/locations/{location}`
     * @param {number} request.pageSize
     *   The maximum number of Metadata Stores to return. The service may return
     *   fewer.
     *   Must be in range 1-1000, inclusive. Defaults to 100.
     * @param {string} request.pageToken
     *   A page token, received from a previous
     *   {@link protos.google.cloud.aiplatform.v1.MetadataService.ListMetadataStores|MetadataService.ListMetadataStores}
     *   call. Provide this to retrieve the subsequent page.
     *
     *   When paginating, all other provided parameters must match the call that
     *   provided the page token. (Otherwise the request will fail with
     *   INVALID_ARGUMENT error.)
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Object}
     *   An iterable Object that allows {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Iteration_protocols | async iteration }.
     *   When you iterate the returned iterable, each element will be an object representing
     *   {@link protos.google.cloud.aiplatform.v1.MetadataStore|MetadataStore}. The API will be called under the hood as needed, once per the page,
     *   so you can stop the iteration when you don't need more results.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1/metadata_service.list_metadata_stores.js</caption>
     * region_tag:aiplatform_v1_generated_MetadataService_ListMetadataStores_async
     */
    listMetadataStoresAsync(request?: protos.google.cloud.aiplatform.v1.IListMetadataStoresRequest, options?: CallOptions): AsyncIterable<protos.google.cloud.aiplatform.v1.IMetadataStore>;
    /**
     * Lists Artifacts in the MetadataStore.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. The MetadataStore whose Artifacts should be listed.
     *   Format:
     *   `projects/{project}/locations/{location}/metadataStores/{metadatastore}`
     * @param {number} request.pageSize
     *   The maximum number of Artifacts to return. The service may return fewer.
     *   Must be in range 1-1000, inclusive. Defaults to 100.
     * @param {string} request.pageToken
     *   A page token, received from a previous
     *   {@link protos.google.cloud.aiplatform.v1.MetadataService.ListArtifacts|MetadataService.ListArtifacts}
     *   call. Provide this to retrieve the subsequent page.
     *
     *   When paginating, all other provided parameters must match the call that
     *   provided the page token. (Otherwise the request will fail with
     *   INVALID_ARGUMENT error.)
     * @param {string} request.filter
     *   Filter specifying the boolean condition for the Artifacts to satisfy in
     *   order to be part of the result set.
     *   The syntax to define filter query is based on https://google.aip.dev/160.
     *   The supported set of filters include the following:
     *
     *   *   **Attribute filtering**:
     *       For example: `display_name = "test"`.
     *       Supported fields include: `name`, `display_name`, `uri`, `state`,
     *       `schema_title`, `create_time`, and `update_time`.
     *       Time fields, such as `create_time` and `update_time`, require values
     *       specified in RFC-3339 format.
     *       For example: `create_time = "2020-11-19T11:30:00-04:00"`
     *   *   **Metadata field**:
     *       To filter on metadata fields use traversal operation as follows:
     *       `metadata.<field_name>.<type_value>`.
     *       For example: `metadata.field_1.number_value = 10.0`
     *       In case the field name contains special characters (such as colon), one
     *       can embed it inside double quote.
     *       For example: `metadata."field:1".number_value = 10.0`
     *   *   **Context based filtering**:
     *       To filter Artifacts based on the contexts to which they belong, use the
     *       function operator with the full resource name
     *       `in_context(<context-name>)`.
     *       For example:
     *       `in_context("projects/<project_number>/locations/<location>/metadataStores/<metadatastore_name>/contexts/<context-id>")`
     *
     *   Each of the above supported filter types can be combined together using
     *   logical operators (`AND` & `OR`). Maximum nested expression depth allowed
     *   is 5.
     *
     *   For example: `display_name = "test" AND metadata.field1.bool_value = true`.
     * @param {string} request.orderBy
     *   How the list of messages is ordered. Specify the values to order by and an
     *   ordering operation. The default sorting order is ascending. To specify
     *   descending order for a field, users append a " desc" suffix; for example:
     *   "foo desc, bar".
     *   Subfields are specified with a `.` character, such as foo.bar.
     *   see https://google.aip.dev/132#ordering for more details.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is Array of {@link protos.google.cloud.aiplatform.v1.Artifact|Artifact}.
     *   The client library will perform auto-pagination by default: it will call the API as many
     *   times as needed and will merge results from all the pages into this array.
     *   Note that it can affect your quota.
     *   We recommend using `listArtifactsAsync()`
     *   method described below for async iteration which you can stop as needed.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     */
    listArtifacts(request?: protos.google.cloud.aiplatform.v1.IListArtifactsRequest, options?: CallOptions): Promise<[
        protos.google.cloud.aiplatform.v1.IArtifact[],
        protos.google.cloud.aiplatform.v1.IListArtifactsRequest | null,
        protos.google.cloud.aiplatform.v1.IListArtifactsResponse
    ]>;
    listArtifacts(request: protos.google.cloud.aiplatform.v1.IListArtifactsRequest, options: CallOptions, callback: PaginationCallback<protos.google.cloud.aiplatform.v1.IListArtifactsRequest, protos.google.cloud.aiplatform.v1.IListArtifactsResponse | null | undefined, protos.google.cloud.aiplatform.v1.IArtifact>): void;
    listArtifacts(request: protos.google.cloud.aiplatform.v1.IListArtifactsRequest, callback: PaginationCallback<protos.google.cloud.aiplatform.v1.IListArtifactsRequest, protos.google.cloud.aiplatform.v1.IListArtifactsResponse | null | undefined, protos.google.cloud.aiplatform.v1.IArtifact>): void;
    /**
     * Equivalent to `listArtifacts`, but returns a NodeJS Stream object.
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. The MetadataStore whose Artifacts should be listed.
     *   Format:
     *   `projects/{project}/locations/{location}/metadataStores/{metadatastore}`
     * @param {number} request.pageSize
     *   The maximum number of Artifacts to return. The service may return fewer.
     *   Must be in range 1-1000, inclusive. Defaults to 100.
     * @param {string} request.pageToken
     *   A page token, received from a previous
     *   {@link protos.google.cloud.aiplatform.v1.MetadataService.ListArtifacts|MetadataService.ListArtifacts}
     *   call. Provide this to retrieve the subsequent page.
     *
     *   When paginating, all other provided parameters must match the call that
     *   provided the page token. (Otherwise the request will fail with
     *   INVALID_ARGUMENT error.)
     * @param {string} request.filter
     *   Filter specifying the boolean condition for the Artifacts to satisfy in
     *   order to be part of the result set.
     *   The syntax to define filter query is based on https://google.aip.dev/160.
     *   The supported set of filters include the following:
     *
     *   *   **Attribute filtering**:
     *       For example: `display_name = "test"`.
     *       Supported fields include: `name`, `display_name`, `uri`, `state`,
     *       `schema_title`, `create_time`, and `update_time`.
     *       Time fields, such as `create_time` and `update_time`, require values
     *       specified in RFC-3339 format.
     *       For example: `create_time = "2020-11-19T11:30:00-04:00"`
     *   *   **Metadata field**:
     *       To filter on metadata fields use traversal operation as follows:
     *       `metadata.<field_name>.<type_value>`.
     *       For example: `metadata.field_1.number_value = 10.0`
     *       In case the field name contains special characters (such as colon), one
     *       can embed it inside double quote.
     *       For example: `metadata."field:1".number_value = 10.0`
     *   *   **Context based filtering**:
     *       To filter Artifacts based on the contexts to which they belong, use the
     *       function operator with the full resource name
     *       `in_context(<context-name>)`.
     *       For example:
     *       `in_context("projects/<project_number>/locations/<location>/metadataStores/<metadatastore_name>/contexts/<context-id>")`
     *
     *   Each of the above supported filter types can be combined together using
     *   logical operators (`AND` & `OR`). Maximum nested expression depth allowed
     *   is 5.
     *
     *   For example: `display_name = "test" AND metadata.field1.bool_value = true`.
     * @param {string} request.orderBy
     *   How the list of messages is ordered. Specify the values to order by and an
     *   ordering operation. The default sorting order is ascending. To specify
     *   descending order for a field, users append a " desc" suffix; for example:
     *   "foo desc, bar".
     *   Subfields are specified with a `.` character, such as foo.bar.
     *   see https://google.aip.dev/132#ordering for more details.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Stream}
     *   An object stream which emits an object representing {@link protos.google.cloud.aiplatform.v1.Artifact|Artifact} on 'data' event.
     *   The client library will perform auto-pagination by default: it will call the API as many
     *   times as needed. Note that it can affect your quota.
     *   We recommend using `listArtifactsAsync()`
     *   method described below for async iteration which you can stop as needed.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     */
    listArtifactsStream(request?: protos.google.cloud.aiplatform.v1.IListArtifactsRequest, options?: CallOptions): Transform;
    /**
     * Equivalent to `listArtifacts`, but returns an iterable object.
     *
     * `for`-`await`-`of` syntax is used with the iterable to get response elements on-demand.
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. The MetadataStore whose Artifacts should be listed.
     *   Format:
     *   `projects/{project}/locations/{location}/metadataStores/{metadatastore}`
     * @param {number} request.pageSize
     *   The maximum number of Artifacts to return. The service may return fewer.
     *   Must be in range 1-1000, inclusive. Defaults to 100.
     * @param {string} request.pageToken
     *   A page token, received from a previous
     *   {@link protos.google.cloud.aiplatform.v1.MetadataService.ListArtifacts|MetadataService.ListArtifacts}
     *   call. Provide this to retrieve the subsequent page.
     *
     *   When paginating, all other provided parameters must match the call that
     *   provided the page token. (Otherwise the request will fail with
     *   INVALID_ARGUMENT error.)
     * @param {string} request.filter
     *   Filter specifying the boolean condition for the Artifacts to satisfy in
     *   order to be part of the result set.
     *   The syntax to define filter query is based on https://google.aip.dev/160.
     *   The supported set of filters include the following:
     *
     *   *   **Attribute filtering**:
     *       For example: `display_name = "test"`.
     *       Supported fields include: `name`, `display_name`, `uri`, `state`,
     *       `schema_title`, `create_time`, and `update_time`.
     *       Time fields, such as `create_time` and `update_time`, require values
     *       specified in RFC-3339 format.
     *       For example: `create_time = "2020-11-19T11:30:00-04:00"`
     *   *   **Metadata field**:
     *       To filter on metadata fields use traversal operation as follows:
     *       `metadata.<field_name>.<type_value>`.
     *       For example: `metadata.field_1.number_value = 10.0`
     *       In case the field name contains special characters (such as colon), one
     *       can embed it inside double quote.
     *       For example: `metadata."field:1".number_value = 10.0`
     *   *   **Context based filtering**:
     *       To filter Artifacts based on the contexts to which they belong, use the
     *       function operator with the full resource name
     *       `in_context(<context-name>)`.
     *       For example:
     *       `in_context("projects/<project_number>/locations/<location>/metadataStores/<metadatastore_name>/contexts/<context-id>")`
     *
     *   Each of the above supported filter types can be combined together using
     *   logical operators (`AND` & `OR`). Maximum nested expression depth allowed
     *   is 5.
     *
     *   For example: `display_name = "test" AND metadata.field1.bool_value = true`.
     * @param {string} request.orderBy
     *   How the list of messages is ordered. Specify the values to order by and an
     *   ordering operation. The default sorting order is ascending. To specify
     *   descending order for a field, users append a " desc" suffix; for example:
     *   "foo desc, bar".
     *   Subfields are specified with a `.` character, such as foo.bar.
     *   see https://google.aip.dev/132#ordering for more details.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Object}
     *   An iterable Object that allows {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Iteration_protocols | async iteration }.
     *   When you iterate the returned iterable, each element will be an object representing
     *   {@link protos.google.cloud.aiplatform.v1.Artifact|Artifact}. The API will be called under the hood as needed, once per the page,
     *   so you can stop the iteration when you don't need more results.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1/metadata_service.list_artifacts.js</caption>
     * region_tag:aiplatform_v1_generated_MetadataService_ListArtifacts_async
     */
    listArtifactsAsync(request?: protos.google.cloud.aiplatform.v1.IListArtifactsRequest, options?: CallOptions): AsyncIterable<protos.google.cloud.aiplatform.v1.IArtifact>;
    /**
     * Lists Contexts on the MetadataStore.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. The MetadataStore whose Contexts should be listed.
     *   Format:
     *   `projects/{project}/locations/{location}/metadataStores/{metadatastore}`
     * @param {number} request.pageSize
     *   The maximum number of Contexts to return. The service may return fewer.
     *   Must be in range 1-1000, inclusive. Defaults to 100.
     * @param {string} request.pageToken
     *   A page token, received from a previous
     *   {@link protos.google.cloud.aiplatform.v1.MetadataService.ListContexts|MetadataService.ListContexts}
     *   call. Provide this to retrieve the subsequent page.
     *
     *   When paginating, all other provided parameters must match the call that
     *   provided the page token. (Otherwise the request will fail with
     *   INVALID_ARGUMENT error.)
     * @param {string} request.filter
     *   Filter specifying the boolean condition for the Contexts to satisfy in
     *   order to be part of the result set.
     *   The syntax to define filter query is based on https://google.aip.dev/160.
     *   Following are the supported set of filters:
     *
     *   *  **Attribute filtering**:
     *      For example: `display_name = "test"`.
     *      Supported fields include: `name`, `display_name`, `schema_title`,
     *      `create_time`, and `update_time`.
     *      Time fields, such as `create_time` and `update_time`, require values
     *      specified in RFC-3339 format.
     *      For example: `create_time = "2020-11-19T11:30:00-04:00"`.
     *   *  **Metadata field**:
     *      To filter on metadata fields use traversal operation as follows:
     *      `metadata.<field_name>.<type_value>`.
     *      For example: `metadata.field_1.number_value = 10.0`.
     *      In case the field name contains special characters (such as colon), one
     *      can embed it inside double quote.
     *      For example: `metadata."field:1".number_value = 10.0`
     *   *  **Parent Child filtering**:
     *      To filter Contexts based on parent-child relationship use the HAS
     *      operator as follows:
     *
     *      ```
     *      parent_contexts:
     *      "projects/<project_number>/locations/<location>/metadataStores/<metadatastore_name>/contexts/<context_id>"
     *      child_contexts:
     *      "projects/<project_number>/locations/<location>/metadataStores/<metadatastore_name>/contexts/<context_id>"
     *      ```
     *
     *   Each of the above supported filters can be combined together using
     *   logical operators (`AND` & `OR`). Maximum nested expression depth allowed
     *   is 5.
     *
     *   For example: `display_name = "test" AND metadata.field1.bool_value = true`.
     * @param {string} request.orderBy
     *   How the list of messages is ordered. Specify the values to order by and an
     *   ordering operation. The default sorting order is ascending. To specify
     *   descending order for a field, users append a " desc" suffix; for example:
     *   "foo desc, bar".
     *   Subfields are specified with a `.` character, such as foo.bar.
     *   see https://google.aip.dev/132#ordering for more details.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is Array of {@link protos.google.cloud.aiplatform.v1.Context|Context}.
     *   The client library will perform auto-pagination by default: it will call the API as many
     *   times as needed and will merge results from all the pages into this array.
     *   Note that it can affect your quota.
     *   We recommend using `listContextsAsync()`
     *   method described below for async iteration which you can stop as needed.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     */
    listContexts(request?: protos.google.cloud.aiplatform.v1.IListContextsRequest, options?: CallOptions): Promise<[
        protos.google.cloud.aiplatform.v1.IContext[],
        protos.google.cloud.aiplatform.v1.IListContextsRequest | null,
        protos.google.cloud.aiplatform.v1.IListContextsResponse
    ]>;
    listContexts(request: protos.google.cloud.aiplatform.v1.IListContextsRequest, options: CallOptions, callback: PaginationCallback<protos.google.cloud.aiplatform.v1.IListContextsRequest, protos.google.cloud.aiplatform.v1.IListContextsResponse | null | undefined, protos.google.cloud.aiplatform.v1.IContext>): void;
    listContexts(request: protos.google.cloud.aiplatform.v1.IListContextsRequest, callback: PaginationCallback<protos.google.cloud.aiplatform.v1.IListContextsRequest, protos.google.cloud.aiplatform.v1.IListContextsResponse | null | undefined, protos.google.cloud.aiplatform.v1.IContext>): void;
    /**
     * Equivalent to `listContexts`, but returns a NodeJS Stream object.
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. The MetadataStore whose Contexts should be listed.
     *   Format:
     *   `projects/{project}/locations/{location}/metadataStores/{metadatastore}`
     * @param {number} request.pageSize
     *   The maximum number of Contexts to return. The service may return fewer.
     *   Must be in range 1-1000, inclusive. Defaults to 100.
     * @param {string} request.pageToken
     *   A page token, received from a previous
     *   {@link protos.google.cloud.aiplatform.v1.MetadataService.ListContexts|MetadataService.ListContexts}
     *   call. Provide this to retrieve the subsequent page.
     *
     *   When paginating, all other provided parameters must match the call that
     *   provided the page token. (Otherwise the request will fail with
     *   INVALID_ARGUMENT error.)
     * @param {string} request.filter
     *   Filter specifying the boolean condition for the Contexts to satisfy in
     *   order to be part of the result set.
     *   The syntax to define filter query is based on https://google.aip.dev/160.
     *   Following are the supported set of filters:
     *
     *   *  **Attribute filtering**:
     *      For example: `display_name = "test"`.
     *      Supported fields include: `name`, `display_name`, `schema_title`,
     *      `create_time`, and `update_time`.
     *      Time fields, such as `create_time` and `update_time`, require values
     *      specified in RFC-3339 format.
     *      For example: `create_time = "2020-11-19T11:30:00-04:00"`.
     *   *  **Metadata field**:
     *      To filter on metadata fields use traversal operation as follows:
     *      `metadata.<field_name>.<type_value>`.
     *      For example: `metadata.field_1.number_value = 10.0`.
     *      In case the field name contains special characters (such as colon), one
     *      can embed it inside double quote.
     *      For example: `metadata."field:1".number_value = 10.0`
     *   *  **Parent Child filtering**:
     *      To filter Contexts based on parent-child relationship use the HAS
     *      operator as follows:
     *
     *      ```
     *      parent_contexts:
     *      "projects/<project_number>/locations/<location>/metadataStores/<metadatastore_name>/contexts/<context_id>"
     *      child_contexts:
     *      "projects/<project_number>/locations/<location>/metadataStores/<metadatastore_name>/contexts/<context_id>"
     *      ```
     *
     *   Each of the above supported filters can be combined together using
     *   logical operators (`AND` & `OR`). Maximum nested expression depth allowed
     *   is 5.
     *
     *   For example: `display_name = "test" AND metadata.field1.bool_value = true`.
     * @param {string} request.orderBy
     *   How the list of messages is ordered. Specify the values to order by and an
     *   ordering operation. The default sorting order is ascending. To specify
     *   descending order for a field, users append a " desc" suffix; for example:
     *   "foo desc, bar".
     *   Subfields are specified with a `.` character, such as foo.bar.
     *   see https://google.aip.dev/132#ordering for more details.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Stream}
     *   An object stream which emits an object representing {@link protos.google.cloud.aiplatform.v1.Context|Context} on 'data' event.
     *   The client library will perform auto-pagination by default: it will call the API as many
     *   times as needed. Note that it can affect your quota.
     *   We recommend using `listContextsAsync()`
     *   method described below for async iteration which you can stop as needed.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     */
    listContextsStream(request?: protos.google.cloud.aiplatform.v1.IListContextsRequest, options?: CallOptions): Transform;
    /**
     * Equivalent to `listContexts`, but returns an iterable object.
     *
     * `for`-`await`-`of` syntax is used with the iterable to get response elements on-demand.
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. The MetadataStore whose Contexts should be listed.
     *   Format:
     *   `projects/{project}/locations/{location}/metadataStores/{metadatastore}`
     * @param {number} request.pageSize
     *   The maximum number of Contexts to return. The service may return fewer.
     *   Must be in range 1-1000, inclusive. Defaults to 100.
     * @param {string} request.pageToken
     *   A page token, received from a previous
     *   {@link protos.google.cloud.aiplatform.v1.MetadataService.ListContexts|MetadataService.ListContexts}
     *   call. Provide this to retrieve the subsequent page.
     *
     *   When paginating, all other provided parameters must match the call that
     *   provided the page token. (Otherwise the request will fail with
     *   INVALID_ARGUMENT error.)
     * @param {string} request.filter
     *   Filter specifying the boolean condition for the Contexts to satisfy in
     *   order to be part of the result set.
     *   The syntax to define filter query is based on https://google.aip.dev/160.
     *   Following are the supported set of filters:
     *
     *   *  **Attribute filtering**:
     *      For example: `display_name = "test"`.
     *      Supported fields include: `name`, `display_name`, `schema_title`,
     *      `create_time`, and `update_time`.
     *      Time fields, such as `create_time` and `update_time`, require values
     *      specified in RFC-3339 format.
     *      For example: `create_time = "2020-11-19T11:30:00-04:00"`.
     *   *  **Metadata field**:
     *      To filter on metadata fields use traversal operation as follows:
     *      `metadata.<field_name>.<type_value>`.
     *      For example: `metadata.field_1.number_value = 10.0`.
     *      In case the field name contains special characters (such as colon), one
     *      can embed it inside double quote.
     *      For example: `metadata."field:1".number_value = 10.0`
     *   *  **Parent Child filtering**:
     *      To filter Contexts based on parent-child relationship use the HAS
     *      operator as follows:
     *
     *      ```
     *      parent_contexts:
     *      "projects/<project_number>/locations/<location>/metadataStores/<metadatastore_name>/contexts/<context_id>"
     *      child_contexts:
     *      "projects/<project_number>/locations/<location>/metadataStores/<metadatastore_name>/contexts/<context_id>"
     *      ```
     *
     *   Each of the above supported filters can be combined together using
     *   logical operators (`AND` & `OR`). Maximum nested expression depth allowed
     *   is 5.
     *
     *   For example: `display_name = "test" AND metadata.field1.bool_value = true`.
     * @param {string} request.orderBy
     *   How the list of messages is ordered. Specify the values to order by and an
     *   ordering operation. The default sorting order is ascending. To specify
     *   descending order for a field, users append a " desc" suffix; for example:
     *   "foo desc, bar".
     *   Subfields are specified with a `.` character, such as foo.bar.
     *   see https://google.aip.dev/132#ordering for more details.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Object}
     *   An iterable Object that allows {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Iteration_protocols | async iteration }.
     *   When you iterate the returned iterable, each element will be an object representing
     *   {@link protos.google.cloud.aiplatform.v1.Context|Context}. The API will be called under the hood as needed, once per the page,
     *   so you can stop the iteration when you don't need more results.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1/metadata_service.list_contexts.js</caption>
     * region_tag:aiplatform_v1_generated_MetadataService_ListContexts_async
     */
    listContextsAsync(request?: protos.google.cloud.aiplatform.v1.IListContextsRequest, options?: CallOptions): AsyncIterable<protos.google.cloud.aiplatform.v1.IContext>;
    /**
     * Lists Executions in the MetadataStore.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. The MetadataStore whose Executions should be listed.
     *   Format:
     *   `projects/{project}/locations/{location}/metadataStores/{metadatastore}`
     * @param {number} request.pageSize
     *   The maximum number of Executions to return. The service may return fewer.
     *   Must be in range 1-1000, inclusive. Defaults to 100.
     * @param {string} request.pageToken
     *   A page token, received from a previous
     *   {@link protos.google.cloud.aiplatform.v1.MetadataService.ListExecutions|MetadataService.ListExecutions}
     *   call. Provide this to retrieve the subsequent page.
     *
     *   When paginating, all other provided parameters must match the call that
     *   provided the page token. (Otherwise the request will fail with an
     *   INVALID_ARGUMENT error.)
     * @param {string} request.filter
     *   Filter specifying the boolean condition for the Executions to satisfy in
     *   order to be part of the result set.
     *   The syntax to define filter query is based on https://google.aip.dev/160.
     *   Following are the supported set of filters:
     *
     *   *  **Attribute filtering**:
     *      For example: `display_name = "test"`.
     *      Supported fields include: `name`, `display_name`, `state`,
     *      `schema_title`, `create_time`, and `update_time`.
     *      Time fields, such as `create_time` and `update_time`, require values
     *      specified in RFC-3339 format.
     *      For example: `create_time = "2020-11-19T11:30:00-04:00"`.
     *   *  **Metadata field**:
     *      To filter on metadata fields use traversal operation as follows:
     *      `metadata.<field_name>.<type_value>`
     *      For example: `metadata.field_1.number_value = 10.0`
     *      In case the field name contains special characters (such as colon), one
     *      can embed it inside double quote.
     *      For example: `metadata."field:1".number_value = 10.0`
     *   *  **Context based filtering**:
     *      To filter Executions based on the contexts to which they belong use
     *      the function operator with the full resource name:
     *      `in_context(<context-name>)`.
     *      For example:
     *      `in_context("projects/<project_number>/locations/<location>/metadataStores/<metadatastore_name>/contexts/<context-id>")`
     *
     *   Each of the above supported filters can be combined together using
     *   logical operators (`AND` & `OR`). Maximum nested expression depth allowed
     *   is 5.
     *
     *   For example: `display_name = "test" AND metadata.field1.bool_value = true`.
     * @param {string} request.orderBy
     *   How the list of messages is ordered. Specify the values to order by and an
     *   ordering operation. The default sorting order is ascending. To specify
     *   descending order for a field, users append a " desc" suffix; for example:
     *   "foo desc, bar".
     *   Subfields are specified with a `.` character, such as foo.bar.
     *   see https://google.aip.dev/132#ordering for more details.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is Array of {@link protos.google.cloud.aiplatform.v1.Execution|Execution}.
     *   The client library will perform auto-pagination by default: it will call the API as many
     *   times as needed and will merge results from all the pages into this array.
     *   Note that it can affect your quota.
     *   We recommend using `listExecutionsAsync()`
     *   method described below for async iteration which you can stop as needed.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     */
    listExecutions(request?: protos.google.cloud.aiplatform.v1.IListExecutionsRequest, options?: CallOptions): Promise<[
        protos.google.cloud.aiplatform.v1.IExecution[],
        protos.google.cloud.aiplatform.v1.IListExecutionsRequest | null,
        protos.google.cloud.aiplatform.v1.IListExecutionsResponse
    ]>;
    listExecutions(request: protos.google.cloud.aiplatform.v1.IListExecutionsRequest, options: CallOptions, callback: PaginationCallback<protos.google.cloud.aiplatform.v1.IListExecutionsRequest, protos.google.cloud.aiplatform.v1.IListExecutionsResponse | null | undefined, protos.google.cloud.aiplatform.v1.IExecution>): void;
    listExecutions(request: protos.google.cloud.aiplatform.v1.IListExecutionsRequest, callback: PaginationCallback<protos.google.cloud.aiplatform.v1.IListExecutionsRequest, protos.google.cloud.aiplatform.v1.IListExecutionsResponse | null | undefined, protos.google.cloud.aiplatform.v1.IExecution>): void;
    /**
     * Equivalent to `listExecutions`, but returns a NodeJS Stream object.
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. The MetadataStore whose Executions should be listed.
     *   Format:
     *   `projects/{project}/locations/{location}/metadataStores/{metadatastore}`
     * @param {number} request.pageSize
     *   The maximum number of Executions to return. The service may return fewer.
     *   Must be in range 1-1000, inclusive. Defaults to 100.
     * @param {string} request.pageToken
     *   A page token, received from a previous
     *   {@link protos.google.cloud.aiplatform.v1.MetadataService.ListExecutions|MetadataService.ListExecutions}
     *   call. Provide this to retrieve the subsequent page.
     *
     *   When paginating, all other provided parameters must match the call that
     *   provided the page token. (Otherwise the request will fail with an
     *   INVALID_ARGUMENT error.)
     * @param {string} request.filter
     *   Filter specifying the boolean condition for the Executions to satisfy in
     *   order to be part of the result set.
     *   The syntax to define filter query is based on https://google.aip.dev/160.
     *   Following are the supported set of filters:
     *
     *   *  **Attribute filtering**:
     *      For example: `display_name = "test"`.
     *      Supported fields include: `name`, `display_name`, `state`,
     *      `schema_title`, `create_time`, and `update_time`.
     *      Time fields, such as `create_time` and `update_time`, require values
     *      specified in RFC-3339 format.
     *      For example: `create_time = "2020-11-19T11:30:00-04:00"`.
     *   *  **Metadata field**:
     *      To filter on metadata fields use traversal operation as follows:
     *      `metadata.<field_name>.<type_value>`
     *      For example: `metadata.field_1.number_value = 10.0`
     *      In case the field name contains special characters (such as colon), one
     *      can embed it inside double quote.
     *      For example: `metadata."field:1".number_value = 10.0`
     *   *  **Context based filtering**:
     *      To filter Executions based on the contexts to which they belong use
     *      the function operator with the full resource name:
     *      `in_context(<context-name>)`.
     *      For example:
     *      `in_context("projects/<project_number>/locations/<location>/metadataStores/<metadatastore_name>/contexts/<context-id>")`
     *
     *   Each of the above supported filters can be combined together using
     *   logical operators (`AND` & `OR`). Maximum nested expression depth allowed
     *   is 5.
     *
     *   For example: `display_name = "test" AND metadata.field1.bool_value = true`.
     * @param {string} request.orderBy
     *   How the list of messages is ordered. Specify the values to order by and an
     *   ordering operation. The default sorting order is ascending. To specify
     *   descending order for a field, users append a " desc" suffix; for example:
     *   "foo desc, bar".
     *   Subfields are specified with a `.` character, such as foo.bar.
     *   see https://google.aip.dev/132#ordering for more details.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Stream}
     *   An object stream which emits an object representing {@link protos.google.cloud.aiplatform.v1.Execution|Execution} on 'data' event.
     *   The client library will perform auto-pagination by default: it will call the API as many
     *   times as needed. Note that it can affect your quota.
     *   We recommend using `listExecutionsAsync()`
     *   method described below for async iteration which you can stop as needed.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     */
    listExecutionsStream(request?: protos.google.cloud.aiplatform.v1.IListExecutionsRequest, options?: CallOptions): Transform;
    /**
     * Equivalent to `listExecutions`, but returns an iterable object.
     *
     * `for`-`await`-`of` syntax is used with the iterable to get response elements on-demand.
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. The MetadataStore whose Executions should be listed.
     *   Format:
     *   `projects/{project}/locations/{location}/metadataStores/{metadatastore}`
     * @param {number} request.pageSize
     *   The maximum number of Executions to return. The service may return fewer.
     *   Must be in range 1-1000, inclusive. Defaults to 100.
     * @param {string} request.pageToken
     *   A page token, received from a previous
     *   {@link protos.google.cloud.aiplatform.v1.MetadataService.ListExecutions|MetadataService.ListExecutions}
     *   call. Provide this to retrieve the subsequent page.
     *
     *   When paginating, all other provided parameters must match the call that
     *   provided the page token. (Otherwise the request will fail with an
     *   INVALID_ARGUMENT error.)
     * @param {string} request.filter
     *   Filter specifying the boolean condition for the Executions to satisfy in
     *   order to be part of the result set.
     *   The syntax to define filter query is based on https://google.aip.dev/160.
     *   Following are the supported set of filters:
     *
     *   *  **Attribute filtering**:
     *      For example: `display_name = "test"`.
     *      Supported fields include: `name`, `display_name`, `state`,
     *      `schema_title`, `create_time`, and `update_time`.
     *      Time fields, such as `create_time` and `update_time`, require values
     *      specified in RFC-3339 format.
     *      For example: `create_time = "2020-11-19T11:30:00-04:00"`.
     *   *  **Metadata field**:
     *      To filter on metadata fields use traversal operation as follows:
     *      `metadata.<field_name>.<type_value>`
     *      For example: `metadata.field_1.number_value = 10.0`
     *      In case the field name contains special characters (such as colon), one
     *      can embed it inside double quote.
     *      For example: `metadata."field:1".number_value = 10.0`
     *   *  **Context based filtering**:
     *      To filter Executions based on the contexts to which they belong use
     *      the function operator with the full resource name:
     *      `in_context(<context-name>)`.
     *      For example:
     *      `in_context("projects/<project_number>/locations/<location>/metadataStores/<metadatastore_name>/contexts/<context-id>")`
     *
     *   Each of the above supported filters can be combined together using
     *   logical operators (`AND` & `OR`). Maximum nested expression depth allowed
     *   is 5.
     *
     *   For example: `display_name = "test" AND metadata.field1.bool_value = true`.
     * @param {string} request.orderBy
     *   How the list of messages is ordered. Specify the values to order by and an
     *   ordering operation. The default sorting order is ascending. To specify
     *   descending order for a field, users append a " desc" suffix; for example:
     *   "foo desc, bar".
     *   Subfields are specified with a `.` character, such as foo.bar.
     *   see https://google.aip.dev/132#ordering for more details.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Object}
     *   An iterable Object that allows {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Iteration_protocols | async iteration }.
     *   When you iterate the returned iterable, each element will be an object representing
     *   {@link protos.google.cloud.aiplatform.v1.Execution|Execution}. The API will be called under the hood as needed, once per the page,
     *   so you can stop the iteration when you don't need more results.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1/metadata_service.list_executions.js</caption>
     * region_tag:aiplatform_v1_generated_MetadataService_ListExecutions_async
     */
    listExecutionsAsync(request?: protos.google.cloud.aiplatform.v1.IListExecutionsRequest, options?: CallOptions): AsyncIterable<protos.google.cloud.aiplatform.v1.IExecution>;
    /**
     * Lists MetadataSchemas.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. The MetadataStore whose MetadataSchemas should be listed.
     *   Format:
     *   `projects/{project}/locations/{location}/metadataStores/{metadatastore}`
     * @param {number} request.pageSize
     *   The maximum number of MetadataSchemas to return. The service may return
     *   fewer.
     *   Must be in range 1-1000, inclusive. Defaults to 100.
     * @param {string} request.pageToken
     *   A page token, received from a previous
     *   {@link protos.google.cloud.aiplatform.v1.MetadataService.ListMetadataSchemas|MetadataService.ListMetadataSchemas}
     *   call. Provide this to retrieve the next page.
     *
     *   When paginating, all other provided parameters must match the call that
     *   provided the page token. (Otherwise the request will fail with
     *   INVALID_ARGUMENT error.)
     * @param {string} request.filter
     *   A query to filter available MetadataSchemas for matching results.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is Array of {@link protos.google.cloud.aiplatform.v1.MetadataSchema|MetadataSchema}.
     *   The client library will perform auto-pagination by default: it will call the API as many
     *   times as needed and will merge results from all the pages into this array.
     *   Note that it can affect your quota.
     *   We recommend using `listMetadataSchemasAsync()`
     *   method described below for async iteration which you can stop as needed.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     */
    listMetadataSchemas(request?: protos.google.cloud.aiplatform.v1.IListMetadataSchemasRequest, options?: CallOptions): Promise<[
        protos.google.cloud.aiplatform.v1.IMetadataSchema[],
        protos.google.cloud.aiplatform.v1.IListMetadataSchemasRequest | null,
        protos.google.cloud.aiplatform.v1.IListMetadataSchemasResponse
    ]>;
    listMetadataSchemas(request: protos.google.cloud.aiplatform.v1.IListMetadataSchemasRequest, options: CallOptions, callback: PaginationCallback<protos.google.cloud.aiplatform.v1.IListMetadataSchemasRequest, protos.google.cloud.aiplatform.v1.IListMetadataSchemasResponse | null | undefined, protos.google.cloud.aiplatform.v1.IMetadataSchema>): void;
    listMetadataSchemas(request: protos.google.cloud.aiplatform.v1.IListMetadataSchemasRequest, callback: PaginationCallback<protos.google.cloud.aiplatform.v1.IListMetadataSchemasRequest, protos.google.cloud.aiplatform.v1.IListMetadataSchemasResponse | null | undefined, protos.google.cloud.aiplatform.v1.IMetadataSchema>): void;
    /**
     * Equivalent to `listMetadataSchemas`, but returns a NodeJS Stream object.
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. The MetadataStore whose MetadataSchemas should be listed.
     *   Format:
     *   `projects/{project}/locations/{location}/metadataStores/{metadatastore}`
     * @param {number} request.pageSize
     *   The maximum number of MetadataSchemas to return. The service may return
     *   fewer.
     *   Must be in range 1-1000, inclusive. Defaults to 100.
     * @param {string} request.pageToken
     *   A page token, received from a previous
     *   {@link protos.google.cloud.aiplatform.v1.MetadataService.ListMetadataSchemas|MetadataService.ListMetadataSchemas}
     *   call. Provide this to retrieve the next page.
     *
     *   When paginating, all other provided parameters must match the call that
     *   provided the page token. (Otherwise the request will fail with
     *   INVALID_ARGUMENT error.)
     * @param {string} request.filter
     *   A query to filter available MetadataSchemas for matching results.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Stream}
     *   An object stream which emits an object representing {@link protos.google.cloud.aiplatform.v1.MetadataSchema|MetadataSchema} on 'data' event.
     *   The client library will perform auto-pagination by default: it will call the API as many
     *   times as needed. Note that it can affect your quota.
     *   We recommend using `listMetadataSchemasAsync()`
     *   method described below for async iteration which you can stop as needed.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     */
    listMetadataSchemasStream(request?: protos.google.cloud.aiplatform.v1.IListMetadataSchemasRequest, options?: CallOptions): Transform;
    /**
     * Equivalent to `listMetadataSchemas`, but returns an iterable object.
     *
     * `for`-`await`-`of` syntax is used with the iterable to get response elements on-demand.
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. The MetadataStore whose MetadataSchemas should be listed.
     *   Format:
     *   `projects/{project}/locations/{location}/metadataStores/{metadatastore}`
     * @param {number} request.pageSize
     *   The maximum number of MetadataSchemas to return. The service may return
     *   fewer.
     *   Must be in range 1-1000, inclusive. Defaults to 100.
     * @param {string} request.pageToken
     *   A page token, received from a previous
     *   {@link protos.google.cloud.aiplatform.v1.MetadataService.ListMetadataSchemas|MetadataService.ListMetadataSchemas}
     *   call. Provide this to retrieve the next page.
     *
     *   When paginating, all other provided parameters must match the call that
     *   provided the page token. (Otherwise the request will fail with
     *   INVALID_ARGUMENT error.)
     * @param {string} request.filter
     *   A query to filter available MetadataSchemas for matching results.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Object}
     *   An iterable Object that allows {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Iteration_protocols | async iteration }.
     *   When you iterate the returned iterable, each element will be an object representing
     *   {@link protos.google.cloud.aiplatform.v1.MetadataSchema|MetadataSchema}. The API will be called under the hood as needed, once per the page,
     *   so you can stop the iteration when you don't need more results.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1/metadata_service.list_metadata_schemas.js</caption>
     * region_tag:aiplatform_v1_generated_MetadataService_ListMetadataSchemas_async
     */
    listMetadataSchemasAsync(request?: protos.google.cloud.aiplatform.v1.IListMetadataSchemasRequest, options?: CallOptions): AsyncIterable<protos.google.cloud.aiplatform.v1.IMetadataSchema>;
    /**
     * Gets the access control policy for a resource. Returns an empty policy
     * if the resource exists and does not have a policy set.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.resource
     *   REQUIRED: The resource for which the policy is being requested.
     *   See the operation documentation for the appropriate value for this field.
     * @param {Object} [request.options]
     *   OPTIONAL: A `GetPolicyOptions` object for specifying options to
     *   `GetIamPolicy`. This field is only used by Cloud IAM.
     *
     *   This object should have the same structure as {@link google.iam.v1.GetPolicyOptions | GetPolicyOptions}.
     * @param {Object} [options]
     *   Optional parameters. You can override the default settings for this call, e.g, timeout,
     *   retries, paginations, etc. See {@link https://googleapis.github.io/gax-nodejs/interfaces/CallOptions.html | gax.CallOptions} for the details.
     * @param {function(?Error, ?Object)} [callback]
     *   The function which will be called with the result of the API call.
     *
     *   The second parameter to the callback is an object representing {@link google.iam.v1.Policy | Policy}.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing {@link google.iam.v1.Policy | Policy}.
     *   The promise has a method named "cancel" which cancels the ongoing API call.
     */
    getIamPolicy(request: IamProtos.google.iam.v1.GetIamPolicyRequest, options?: gax.CallOptions | Callback<IamProtos.google.iam.v1.Policy, IamProtos.google.iam.v1.GetIamPolicyRequest | null | undefined, {} | null | undefined>, callback?: Callback<IamProtos.google.iam.v1.Policy, IamProtos.google.iam.v1.GetIamPolicyRequest | null | undefined, {} | null | undefined>): Promise<[IamProtos.google.iam.v1.Policy]>;
    /**
     * Returns permissions that a caller has on the specified resource. If the
     * resource does not exist, this will return an empty set of
     * permissions, not a NOT_FOUND error.
     *
     * Note: This operation is designed to be used for building
     * permission-aware UIs and command-line tools, not for authorization
     * checking. This operation may "fail open" without warning.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.resource
     *   REQUIRED: The resource for which the policy detail is being requested.
     *   See the operation documentation for the appropriate value for this field.
     * @param {string[]} request.permissions
     *   The set of permissions to check for the `resource`. Permissions with
     *   wildcards (such as '*' or 'storage.*') are not allowed. For more
     *   information see {@link https://cloud.google.com/iam/docs/overview#permissions | IAM Overview }.
     * @param {Object} [options]
     *   Optional parameters. You can override the default settings for this call, e.g, timeout,
     *   retries, paginations, etc. See {@link https://googleapis.github.io/gax-nodejs/interfaces/CallOptions.html | gax.CallOptions} for the details.
     * @param {function(?Error, ?Object)} [callback]
     *   The function which will be called with the result of the API call.
     *
     *   The second parameter to the callback is an object representing {@link google.iam.v1.TestIamPermissionsResponse | TestIamPermissionsResponse}.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing {@link google.iam.v1.TestIamPermissionsResponse | TestIamPermissionsResponse}.
     *   The promise has a method named "cancel" which cancels the ongoing API call.
     */
    setIamPolicy(request: IamProtos.google.iam.v1.SetIamPolicyRequest, options?: gax.CallOptions | Callback<IamProtos.google.iam.v1.Policy, IamProtos.google.iam.v1.SetIamPolicyRequest | null | undefined, {} | null | undefined>, callback?: Callback<IamProtos.google.iam.v1.Policy, IamProtos.google.iam.v1.SetIamPolicyRequest | null | undefined, {} | null | undefined>): Promise<[IamProtos.google.iam.v1.Policy]>;
    /**
     * Returns permissions that a caller has on the specified resource. If the
     * resource does not exist, this will return an empty set of
     * permissions, not a NOT_FOUND error.
     *
     * Note: This operation is designed to be used for building
     * permission-aware UIs and command-line tools, not for authorization
     * checking. This operation may "fail open" without warning.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.resource
     *   REQUIRED: The resource for which the policy detail is being requested.
     *   See the operation documentation for the appropriate value for this field.
     * @param {string[]} request.permissions
     *   The set of permissions to check for the `resource`. Permissions with
     *   wildcards (such as '*' or 'storage.*') are not allowed. For more
     *   information see {@link https://cloud.google.com/iam/docs/overview#permissions | IAM Overview }.
     * @param {Object} [options]
     *   Optional parameters. You can override the default settings for this call, e.g, timeout,
     *   retries, paginations, etc. See {@link https://googleapis.github.io/gax-nodejs/interfaces/CallOptions.html | gax.CallOptions} for the details.
     * @param {function(?Error, ?Object)} [callback]
     *   The function which will be called with the result of the API call.
     *
     *   The second parameter to the callback is an object representing {@link google.iam.v1.TestIamPermissionsResponse | TestIamPermissionsResponse}.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing {@link google.iam.v1.TestIamPermissionsResponse | TestIamPermissionsResponse}.
     *   The promise has a method named "cancel" which cancels the ongoing API call.
     *
     */
    testIamPermissions(request: IamProtos.google.iam.v1.TestIamPermissionsRequest, options?: gax.CallOptions | Callback<IamProtos.google.iam.v1.TestIamPermissionsResponse, IamProtos.google.iam.v1.TestIamPermissionsRequest | null | undefined, {} | null | undefined>, callback?: Callback<IamProtos.google.iam.v1.TestIamPermissionsResponse, IamProtos.google.iam.v1.TestIamPermissionsRequest | null | undefined, {} | null | undefined>): Promise<[IamProtos.google.iam.v1.TestIamPermissionsResponse]>;
    /**
     * Gets information about a location.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.name
     *   Resource name for the location.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html | CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing {@link google.cloud.location.Location | Location}.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#regular-methods | documentation }
     *   for more details and examples.
     * @example
     * ```
     * const [response] = await client.getLocation(request);
     * ```
     */
    getLocation(request: LocationProtos.google.cloud.location.IGetLocationRequest, options?: gax.CallOptions | Callback<LocationProtos.google.cloud.location.ILocation, LocationProtos.google.cloud.location.IGetLocationRequest | null | undefined, {} | null | undefined>, callback?: Callback<LocationProtos.google.cloud.location.ILocation, LocationProtos.google.cloud.location.IGetLocationRequest | null | undefined, {} | null | undefined>): Promise<LocationProtos.google.cloud.location.ILocation>;
    /**
     * Lists information about the supported locations for this service. Returns an iterable object.
     *
     * `for`-`await`-`of` syntax is used with the iterable to get response elements on-demand.
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.name
     *   The resource that owns the locations collection, if applicable.
     * @param {string} request.filter
     *   The standard list filter.
     * @param {number} request.pageSize
     *   The standard list page size.
     * @param {string} request.pageToken
     *   The standard list page token.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Object}
     *   An iterable Object that allows {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Iteration_protocols | async iteration }.
     *   When you iterate the returned iterable, each element will be an object representing
     *   {@link google.cloud.location.Location | Location}. The API will be called under the hood as needed, once per the page,
     *   so you can stop the iteration when you don't need more results.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     * @example
     * ```
     * const iterable = client.listLocationsAsync(request);
     * for await (const response of iterable) {
     *   // process response
     * }
     * ```
     */
    listLocationsAsync(request: LocationProtos.google.cloud.location.IListLocationsRequest, options?: CallOptions): AsyncIterable<LocationProtos.google.cloud.location.ILocation>;
    /**
     * Gets the latest state of a long-running operation.  Clients can use this
     * method to poll the operation result at intervals as recommended by the API
     * service.
     *
     * @param {Object} request - The request object that will be sent.
     * @param {string} request.name - The name of the operation resource.
     * @param {Object=} options
     *   Optional parameters. You can override the default settings for this call,
     *   e.g, timeout, retries, paginations, etc. See {@link
     *   https://googleapis.github.io/gax-nodejs/global.html#CallOptions | gax.CallOptions}
     *   for the details.
     * @param {function(?Error, ?Object)=} callback
     *   The function which will be called with the result of the API call.
     *
     *   The second parameter to the callback is an object representing
     *   {@link google.longrunning.Operation | google.longrunning.Operation}.
     * @return {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing
     * {@link google.longrunning.Operation | google.longrunning.Operation}.
     * The promise has a method named "cancel" which cancels the ongoing API call.
     *
     * @example
     * ```
     * const client = longrunning.operationsClient();
     * const name = '';
     * const [response] = await client.getOperation({name});
     * // doThingsWith(response)
     * ```
     */
    getOperation(request: protos.google.longrunning.GetOperationRequest, optionsOrCallback?: gax.CallOptions | Callback<protos.google.longrunning.Operation, protos.google.longrunning.GetOperationRequest, {} | null | undefined>, callback?: Callback<protos.google.longrunning.Operation, protos.google.longrunning.GetOperationRequest, {} | null | undefined>): Promise<[protos.google.longrunning.Operation]>;
    /**
     * Lists operations that match the specified filter in the request. If the
     * server doesn't support this method, it returns `UNIMPLEMENTED`. Returns an iterable object.
     *
     * For-await-of syntax is used with the iterable to recursively get response element on-demand.
     *
     * @param {Object} request - The request object that will be sent.
     * @param {string} request.name - The name of the operation collection.
     * @param {string} request.filter - The standard list filter.
     * @param {number=} request.pageSize -
     *   The maximum number of resources contained in the underlying API
     *   response. If page streaming is performed per-resource, this
     *   parameter does not affect the return value. If page streaming is
     *   performed per-page, this determines the maximum number of
     *   resources in a page.
     * @param {Object=} options
     *   Optional parameters. You can override the default settings for this call,
     *   e.g, timeout, retries, paginations, etc. See {@link
     *   https://googleapis.github.io/gax-nodejs/global.html#CallOptions | gax.CallOptions} for the
     *   details.
     * @returns {Object}
     *   An iterable Object that conforms to {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Iteration_protocols | iteration protocols}.
     *
     * @example
     * ```
     * const client = longrunning.operationsClient();
     * for await (const response of client.listOperationsAsync(request));
     * // doThingsWith(response)
     * ```
     */
    listOperationsAsync(request: protos.google.longrunning.ListOperationsRequest, options?: gax.CallOptions): AsyncIterable<protos.google.longrunning.ListOperationsResponse>;
    /**
     * Starts asynchronous cancellation on a long-running operation.  The server
     * makes a best effort to cancel the operation, but success is not
     * guaranteed.  If the server doesn't support this method, it returns
     * `google.rpc.Code.UNIMPLEMENTED`.  Clients can use
     * {@link Operations.GetOperation} or
     * other methods to check whether the cancellation succeeded or whether the
     * operation completed despite cancellation. On successful cancellation,
     * the operation is not deleted; instead, it becomes an operation with
     * an {@link Operation.error} value with a {@link google.rpc.Status.code} of
     * 1, corresponding to `Code.CANCELLED`.
     *
     * @param {Object} request - The request object that will be sent.
     * @param {string} request.name - The name of the operation resource to be cancelled.
     * @param {Object=} options
     *   Optional parameters. You can override the default settings for this call,
     * e.g, timeout, retries, paginations, etc. See {@link
     * https://googleapis.github.io/gax-nodejs/global.html#CallOptions | gax.CallOptions} for the
     * details.
     * @param {function(?Error)=} callback
     *   The function which will be called with the result of the API call.
     * @return {Promise} - The promise which resolves when API call finishes.
     *   The promise has a method named "cancel" which cancels the ongoing API
     * call.
     *
     * @example
     * ```
     * const client = longrunning.operationsClient();
     * await client.cancelOperation({name: ''});
     * ```
     */
    cancelOperation(request: protos.google.longrunning.CancelOperationRequest, optionsOrCallback?: gax.CallOptions | Callback<protos.google.longrunning.CancelOperationRequest, protos.google.protobuf.Empty, {} | undefined | null>, callback?: Callback<protos.google.longrunning.CancelOperationRequest, protos.google.protobuf.Empty, {} | undefined | null>): Promise<protos.google.protobuf.Empty>;
    /**
     * Deletes a long-running operation. This method indicates that the client is
     * no longer interested in the operation result. It does not cancel the
     * operation. If the server doesn't support this method, it returns
     * `google.rpc.Code.UNIMPLEMENTED`.
     *
     * @param {Object} request - The request object that will be sent.
     * @param {string} request.name - The name of the operation resource to be deleted.
     * @param {Object=} options
     *   Optional parameters. You can override the default settings for this call,
     * e.g, timeout, retries, paginations, etc. See {@link
     * https://googleapis.github.io/gax-nodejs/global.html#CallOptions | gax.CallOptions}
     * for the details.
     * @param {function(?Error)=} callback
     *   The function which will be called with the result of the API call.
     * @return {Promise} - The promise which resolves when API call finishes.
     *   The promise has a method named "cancel" which cancels the ongoing API
     * call.
     *
     * @example
     * ```
     * const client = longrunning.operationsClient();
     * await client.deleteOperation({name: ''});
     * ```
     */
    deleteOperation(request: protos.google.longrunning.DeleteOperationRequest, optionsOrCallback?: gax.CallOptions | Callback<protos.google.protobuf.Empty, protos.google.longrunning.DeleteOperationRequest, {} | null | undefined>, callback?: Callback<protos.google.protobuf.Empty, protos.google.longrunning.DeleteOperationRequest, {} | null | undefined>): Promise<protos.google.protobuf.Empty>;
    /**
     * Return a fully-qualified annotation resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} dataset
     * @param {string} data_item
     * @param {string} annotation
     * @returns {string} Resource name string.
     */
    annotationPath(project: string, location: string, dataset: string, dataItem: string, annotation: string): string;
    /**
     * Parse the project from Annotation resource.
     *
     * @param {string} annotationName
     *   A fully-qualified path representing Annotation resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromAnnotationName(annotationName: string): string | number;
    /**
     * Parse the location from Annotation resource.
     *
     * @param {string} annotationName
     *   A fully-qualified path representing Annotation resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromAnnotationName(annotationName: string): string | number;
    /**
     * Parse the dataset from Annotation resource.
     *
     * @param {string} annotationName
     *   A fully-qualified path representing Annotation resource.
     * @returns {string} A string representing the dataset.
     */
    matchDatasetFromAnnotationName(annotationName: string): string | number;
    /**
     * Parse the data_item from Annotation resource.
     *
     * @param {string} annotationName
     *   A fully-qualified path representing Annotation resource.
     * @returns {string} A string representing the data_item.
     */
    matchDataItemFromAnnotationName(annotationName: string): string | number;
    /**
     * Parse the annotation from Annotation resource.
     *
     * @param {string} annotationName
     *   A fully-qualified path representing Annotation resource.
     * @returns {string} A string representing the annotation.
     */
    matchAnnotationFromAnnotationName(annotationName: string): string | number;
    /**
     * Return a fully-qualified annotationSpec resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} dataset
     * @param {string} annotation_spec
     * @returns {string} Resource name string.
     */
    annotationSpecPath(project: string, location: string, dataset: string, annotationSpec: string): string;
    /**
     * Parse the project from AnnotationSpec resource.
     *
     * @param {string} annotationSpecName
     *   A fully-qualified path representing AnnotationSpec resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromAnnotationSpecName(annotationSpecName: string): string | number;
    /**
     * Parse the location from AnnotationSpec resource.
     *
     * @param {string} annotationSpecName
     *   A fully-qualified path representing AnnotationSpec resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromAnnotationSpecName(annotationSpecName: string): string | number;
    /**
     * Parse the dataset from AnnotationSpec resource.
     *
     * @param {string} annotationSpecName
     *   A fully-qualified path representing AnnotationSpec resource.
     * @returns {string} A string representing the dataset.
     */
    matchDatasetFromAnnotationSpecName(annotationSpecName: string): string | number;
    /**
     * Parse the annotation_spec from AnnotationSpec resource.
     *
     * @param {string} annotationSpecName
     *   A fully-qualified path representing AnnotationSpec resource.
     * @returns {string} A string representing the annotation_spec.
     */
    matchAnnotationSpecFromAnnotationSpecName(annotationSpecName: string): string | number;
    /**
     * Return a fully-qualified artifact resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} metadata_store
     * @param {string} artifact
     * @returns {string} Resource name string.
     */
    artifactPath(project: string, location: string, metadataStore: string, artifact: string): string;
    /**
     * Parse the project from Artifact resource.
     *
     * @param {string} artifactName
     *   A fully-qualified path representing Artifact resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromArtifactName(artifactName: string): string | number;
    /**
     * Parse the location from Artifact resource.
     *
     * @param {string} artifactName
     *   A fully-qualified path representing Artifact resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromArtifactName(artifactName: string): string | number;
    /**
     * Parse the metadata_store from Artifact resource.
     *
     * @param {string} artifactName
     *   A fully-qualified path representing Artifact resource.
     * @returns {string} A string representing the metadata_store.
     */
    matchMetadataStoreFromArtifactName(artifactName: string): string | number;
    /**
     * Parse the artifact from Artifact resource.
     *
     * @param {string} artifactName
     *   A fully-qualified path representing Artifact resource.
     * @returns {string} A string representing the artifact.
     */
    matchArtifactFromArtifactName(artifactName: string): string | number;
    /**
     * Return a fully-qualified batchPredictionJob resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} batch_prediction_job
     * @returns {string} Resource name string.
     */
    batchPredictionJobPath(project: string, location: string, batchPredictionJob: string): string;
    /**
     * Parse the project from BatchPredictionJob resource.
     *
     * @param {string} batchPredictionJobName
     *   A fully-qualified path representing BatchPredictionJob resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromBatchPredictionJobName(batchPredictionJobName: string): string | number;
    /**
     * Parse the location from BatchPredictionJob resource.
     *
     * @param {string} batchPredictionJobName
     *   A fully-qualified path representing BatchPredictionJob resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromBatchPredictionJobName(batchPredictionJobName: string): string | number;
    /**
     * Parse the batch_prediction_job from BatchPredictionJob resource.
     *
     * @param {string} batchPredictionJobName
     *   A fully-qualified path representing BatchPredictionJob resource.
     * @returns {string} A string representing the batch_prediction_job.
     */
    matchBatchPredictionJobFromBatchPredictionJobName(batchPredictionJobName: string): string | number;
    /**
     * Return a fully-qualified cachedContent resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} cached_content
     * @returns {string} Resource name string.
     */
    cachedContentPath(project: string, location: string, cachedContent: string): string;
    /**
     * Parse the project from CachedContent resource.
     *
     * @param {string} cachedContentName
     *   A fully-qualified path representing CachedContent resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromCachedContentName(cachedContentName: string): string | number;
    /**
     * Parse the location from CachedContent resource.
     *
     * @param {string} cachedContentName
     *   A fully-qualified path representing CachedContent resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromCachedContentName(cachedContentName: string): string | number;
    /**
     * Parse the cached_content from CachedContent resource.
     *
     * @param {string} cachedContentName
     *   A fully-qualified path representing CachedContent resource.
     * @returns {string} A string representing the cached_content.
     */
    matchCachedContentFromCachedContentName(cachedContentName: string): string | number;
    /**
     * Return a fully-qualified context resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} metadata_store
     * @param {string} context
     * @returns {string} Resource name string.
     */
    contextPath(project: string, location: string, metadataStore: string, context: string): string;
    /**
     * Parse the project from Context resource.
     *
     * @param {string} contextName
     *   A fully-qualified path representing Context resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromContextName(contextName: string): string | number;
    /**
     * Parse the location from Context resource.
     *
     * @param {string} contextName
     *   A fully-qualified path representing Context resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromContextName(contextName: string): string | number;
    /**
     * Parse the metadata_store from Context resource.
     *
     * @param {string} contextName
     *   A fully-qualified path representing Context resource.
     * @returns {string} A string representing the metadata_store.
     */
    matchMetadataStoreFromContextName(contextName: string): string | number;
    /**
     * Parse the context from Context resource.
     *
     * @param {string} contextName
     *   A fully-qualified path representing Context resource.
     * @returns {string} A string representing the context.
     */
    matchContextFromContextName(contextName: string): string | number;
    /**
     * Return a fully-qualified customJob resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} custom_job
     * @returns {string} Resource name string.
     */
    customJobPath(project: string, location: string, customJob: string): string;
    /**
     * Parse the project from CustomJob resource.
     *
     * @param {string} customJobName
     *   A fully-qualified path representing CustomJob resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromCustomJobName(customJobName: string): string | number;
    /**
     * Parse the location from CustomJob resource.
     *
     * @param {string} customJobName
     *   A fully-qualified path representing CustomJob resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromCustomJobName(customJobName: string): string | number;
    /**
     * Parse the custom_job from CustomJob resource.
     *
     * @param {string} customJobName
     *   A fully-qualified path representing CustomJob resource.
     * @returns {string} A string representing the custom_job.
     */
    matchCustomJobFromCustomJobName(customJobName: string): string | number;
    /**
     * Return a fully-qualified dataItem resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} dataset
     * @param {string} data_item
     * @returns {string} Resource name string.
     */
    dataItemPath(project: string, location: string, dataset: string, dataItem: string): string;
    /**
     * Parse the project from DataItem resource.
     *
     * @param {string} dataItemName
     *   A fully-qualified path representing DataItem resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromDataItemName(dataItemName: string): string | number;
    /**
     * Parse the location from DataItem resource.
     *
     * @param {string} dataItemName
     *   A fully-qualified path representing DataItem resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromDataItemName(dataItemName: string): string | number;
    /**
     * Parse the dataset from DataItem resource.
     *
     * @param {string} dataItemName
     *   A fully-qualified path representing DataItem resource.
     * @returns {string} A string representing the dataset.
     */
    matchDatasetFromDataItemName(dataItemName: string): string | number;
    /**
     * Parse the data_item from DataItem resource.
     *
     * @param {string} dataItemName
     *   A fully-qualified path representing DataItem resource.
     * @returns {string} A string representing the data_item.
     */
    matchDataItemFromDataItemName(dataItemName: string): string | number;
    /**
     * Return a fully-qualified dataLabelingJob resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} data_labeling_job
     * @returns {string} Resource name string.
     */
    dataLabelingJobPath(project: string, location: string, dataLabelingJob: string): string;
    /**
     * Parse the project from DataLabelingJob resource.
     *
     * @param {string} dataLabelingJobName
     *   A fully-qualified path representing DataLabelingJob resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromDataLabelingJobName(dataLabelingJobName: string): string | number;
    /**
     * Parse the location from DataLabelingJob resource.
     *
     * @param {string} dataLabelingJobName
     *   A fully-qualified path representing DataLabelingJob resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromDataLabelingJobName(dataLabelingJobName: string): string | number;
    /**
     * Parse the data_labeling_job from DataLabelingJob resource.
     *
     * @param {string} dataLabelingJobName
     *   A fully-qualified path representing DataLabelingJob resource.
     * @returns {string} A string representing the data_labeling_job.
     */
    matchDataLabelingJobFromDataLabelingJobName(dataLabelingJobName: string): string | number;
    /**
     * Return a fully-qualified dataset resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} dataset
     * @returns {string} Resource name string.
     */
    datasetPath(project: string, location: string, dataset: string): string;
    /**
     * Parse the project from Dataset resource.
     *
     * @param {string} datasetName
     *   A fully-qualified path representing Dataset resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromDatasetName(datasetName: string): string | number;
    /**
     * Parse the location from Dataset resource.
     *
     * @param {string} datasetName
     *   A fully-qualified path representing Dataset resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromDatasetName(datasetName: string): string | number;
    /**
     * Parse the dataset from Dataset resource.
     *
     * @param {string} datasetName
     *   A fully-qualified path representing Dataset resource.
     * @returns {string} A string representing the dataset.
     */
    matchDatasetFromDatasetName(datasetName: string): string | number;
    /**
     * Return a fully-qualified datasetVersion resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} dataset
     * @param {string} dataset_version
     * @returns {string} Resource name string.
     */
    datasetVersionPath(project: string, location: string, dataset: string, datasetVersion: string): string;
    /**
     * Parse the project from DatasetVersion resource.
     *
     * @param {string} datasetVersionName
     *   A fully-qualified path representing DatasetVersion resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromDatasetVersionName(datasetVersionName: string): string | number;
    /**
     * Parse the location from DatasetVersion resource.
     *
     * @param {string} datasetVersionName
     *   A fully-qualified path representing DatasetVersion resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromDatasetVersionName(datasetVersionName: string): string | number;
    /**
     * Parse the dataset from DatasetVersion resource.
     *
     * @param {string} datasetVersionName
     *   A fully-qualified path representing DatasetVersion resource.
     * @returns {string} A string representing the dataset.
     */
    matchDatasetFromDatasetVersionName(datasetVersionName: string): string | number;
    /**
     * Parse the dataset_version from DatasetVersion resource.
     *
     * @param {string} datasetVersionName
     *   A fully-qualified path representing DatasetVersion resource.
     * @returns {string} A string representing the dataset_version.
     */
    matchDatasetVersionFromDatasetVersionName(datasetVersionName: string): string | number;
    /**
     * Return a fully-qualified deploymentResourcePool resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} deployment_resource_pool
     * @returns {string} Resource name string.
     */
    deploymentResourcePoolPath(project: string, location: string, deploymentResourcePool: string): string;
    /**
     * Parse the project from DeploymentResourcePool resource.
     *
     * @param {string} deploymentResourcePoolName
     *   A fully-qualified path representing DeploymentResourcePool resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromDeploymentResourcePoolName(deploymentResourcePoolName: string): string | number;
    /**
     * Parse the location from DeploymentResourcePool resource.
     *
     * @param {string} deploymentResourcePoolName
     *   A fully-qualified path representing DeploymentResourcePool resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromDeploymentResourcePoolName(deploymentResourcePoolName: string): string | number;
    /**
     * Parse the deployment_resource_pool from DeploymentResourcePool resource.
     *
     * @param {string} deploymentResourcePoolName
     *   A fully-qualified path representing DeploymentResourcePool resource.
     * @returns {string} A string representing the deployment_resource_pool.
     */
    matchDeploymentResourcePoolFromDeploymentResourcePoolName(deploymentResourcePoolName: string): string | number;
    /**
     * Return a fully-qualified entityType resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} featurestore
     * @param {string} entity_type
     * @returns {string} Resource name string.
     */
    entityTypePath(project: string, location: string, featurestore: string, entityType: string): string;
    /**
     * Parse the project from EntityType resource.
     *
     * @param {string} entityTypeName
     *   A fully-qualified path representing EntityType resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromEntityTypeName(entityTypeName: string): string | number;
    /**
     * Parse the location from EntityType resource.
     *
     * @param {string} entityTypeName
     *   A fully-qualified path representing EntityType resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromEntityTypeName(entityTypeName: string): string | number;
    /**
     * Parse the featurestore from EntityType resource.
     *
     * @param {string} entityTypeName
     *   A fully-qualified path representing EntityType resource.
     * @returns {string} A string representing the featurestore.
     */
    matchFeaturestoreFromEntityTypeName(entityTypeName: string): string | number;
    /**
     * Parse the entity_type from EntityType resource.
     *
     * @param {string} entityTypeName
     *   A fully-qualified path representing EntityType resource.
     * @returns {string} A string representing the entity_type.
     */
    matchEntityTypeFromEntityTypeName(entityTypeName: string): string | number;
    /**
     * Return a fully-qualified execution resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} metadata_store
     * @param {string} execution
     * @returns {string} Resource name string.
     */
    executionPath(project: string, location: string, metadataStore: string, execution: string): string;
    /**
     * Parse the project from Execution resource.
     *
     * @param {string} executionName
     *   A fully-qualified path representing Execution resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromExecutionName(executionName: string): string | number;
    /**
     * Parse the location from Execution resource.
     *
     * @param {string} executionName
     *   A fully-qualified path representing Execution resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromExecutionName(executionName: string): string | number;
    /**
     * Parse the metadata_store from Execution resource.
     *
     * @param {string} executionName
     *   A fully-qualified path representing Execution resource.
     * @returns {string} A string representing the metadata_store.
     */
    matchMetadataStoreFromExecutionName(executionName: string): string | number;
    /**
     * Parse the execution from Execution resource.
     *
     * @param {string} executionName
     *   A fully-qualified path representing Execution resource.
     * @returns {string} A string representing the execution.
     */
    matchExecutionFromExecutionName(executionName: string): string | number;
    /**
     * Return a fully-qualified featureGroup resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} feature_group
     * @returns {string} Resource name string.
     */
    featureGroupPath(project: string, location: string, featureGroup: string): string;
    /**
     * Parse the project from FeatureGroup resource.
     *
     * @param {string} featureGroupName
     *   A fully-qualified path representing FeatureGroup resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromFeatureGroupName(featureGroupName: string): string | number;
    /**
     * Parse the location from FeatureGroup resource.
     *
     * @param {string} featureGroupName
     *   A fully-qualified path representing FeatureGroup resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromFeatureGroupName(featureGroupName: string): string | number;
    /**
     * Parse the feature_group from FeatureGroup resource.
     *
     * @param {string} featureGroupName
     *   A fully-qualified path representing FeatureGroup resource.
     * @returns {string} A string representing the feature_group.
     */
    matchFeatureGroupFromFeatureGroupName(featureGroupName: string): string | number;
    /**
     * Return a fully-qualified featureOnlineStore resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} feature_online_store
     * @returns {string} Resource name string.
     */
    featureOnlineStorePath(project: string, location: string, featureOnlineStore: string): string;
    /**
     * Parse the project from FeatureOnlineStore resource.
     *
     * @param {string} featureOnlineStoreName
     *   A fully-qualified path representing FeatureOnlineStore resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromFeatureOnlineStoreName(featureOnlineStoreName: string): string | number;
    /**
     * Parse the location from FeatureOnlineStore resource.
     *
     * @param {string} featureOnlineStoreName
     *   A fully-qualified path representing FeatureOnlineStore resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromFeatureOnlineStoreName(featureOnlineStoreName: string): string | number;
    /**
     * Parse the feature_online_store from FeatureOnlineStore resource.
     *
     * @param {string} featureOnlineStoreName
     *   A fully-qualified path representing FeatureOnlineStore resource.
     * @returns {string} A string representing the feature_online_store.
     */
    matchFeatureOnlineStoreFromFeatureOnlineStoreName(featureOnlineStoreName: string): string | number;
    /**
     * Return a fully-qualified featureView resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} feature_online_store
     * @param {string} feature_view
     * @returns {string} Resource name string.
     */
    featureViewPath(project: string, location: string, featureOnlineStore: string, featureView: string): string;
    /**
     * Parse the project from FeatureView resource.
     *
     * @param {string} featureViewName
     *   A fully-qualified path representing FeatureView resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromFeatureViewName(featureViewName: string): string | number;
    /**
     * Parse the location from FeatureView resource.
     *
     * @param {string} featureViewName
     *   A fully-qualified path representing FeatureView resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromFeatureViewName(featureViewName: string): string | number;
    /**
     * Parse the feature_online_store from FeatureView resource.
     *
     * @param {string} featureViewName
     *   A fully-qualified path representing FeatureView resource.
     * @returns {string} A string representing the feature_online_store.
     */
    matchFeatureOnlineStoreFromFeatureViewName(featureViewName: string): string | number;
    /**
     * Parse the feature_view from FeatureView resource.
     *
     * @param {string} featureViewName
     *   A fully-qualified path representing FeatureView resource.
     * @returns {string} A string representing the feature_view.
     */
    matchFeatureViewFromFeatureViewName(featureViewName: string): string | number;
    /**
     * Return a fully-qualified featureViewSync resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} feature_online_store
     * @param {string} feature_view
     * @returns {string} Resource name string.
     */
    featureViewSyncPath(project: string, location: string, featureOnlineStore: string, featureView: string): string;
    /**
     * Parse the project from FeatureViewSync resource.
     *
     * @param {string} featureViewSyncName
     *   A fully-qualified path representing FeatureViewSync resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromFeatureViewSyncName(featureViewSyncName: string): string | number;
    /**
     * Parse the location from FeatureViewSync resource.
     *
     * @param {string} featureViewSyncName
     *   A fully-qualified path representing FeatureViewSync resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromFeatureViewSyncName(featureViewSyncName: string): string | number;
    /**
     * Parse the feature_online_store from FeatureViewSync resource.
     *
     * @param {string} featureViewSyncName
     *   A fully-qualified path representing FeatureViewSync resource.
     * @returns {string} A string representing the feature_online_store.
     */
    matchFeatureOnlineStoreFromFeatureViewSyncName(featureViewSyncName: string): string | number;
    /**
     * Parse the feature_view from FeatureViewSync resource.
     *
     * @param {string} featureViewSyncName
     *   A fully-qualified path representing FeatureViewSync resource.
     * @returns {string} A string representing the feature_view.
     */
    matchFeatureViewFromFeatureViewSyncName(featureViewSyncName: string): string | number;
    /**
     * Return a fully-qualified featurestore resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} featurestore
     * @returns {string} Resource name string.
     */
    featurestorePath(project: string, location: string, featurestore: string): string;
    /**
     * Parse the project from Featurestore resource.
     *
     * @param {string} featurestoreName
     *   A fully-qualified path representing Featurestore resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromFeaturestoreName(featurestoreName: string): string | number;
    /**
     * Parse the location from Featurestore resource.
     *
     * @param {string} featurestoreName
     *   A fully-qualified path representing Featurestore resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromFeaturestoreName(featurestoreName: string): string | number;
    /**
     * Parse the featurestore from Featurestore resource.
     *
     * @param {string} featurestoreName
     *   A fully-qualified path representing Featurestore resource.
     * @returns {string} A string representing the featurestore.
     */
    matchFeaturestoreFromFeaturestoreName(featurestoreName: string): string | number;
    /**
     * Return a fully-qualified hyperparameterTuningJob resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} hyperparameter_tuning_job
     * @returns {string} Resource name string.
     */
    hyperparameterTuningJobPath(project: string, location: string, hyperparameterTuningJob: string): string;
    /**
     * Parse the project from HyperparameterTuningJob resource.
     *
     * @param {string} hyperparameterTuningJobName
     *   A fully-qualified path representing HyperparameterTuningJob resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromHyperparameterTuningJobName(hyperparameterTuningJobName: string): string | number;
    /**
     * Parse the location from HyperparameterTuningJob resource.
     *
     * @param {string} hyperparameterTuningJobName
     *   A fully-qualified path representing HyperparameterTuningJob resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromHyperparameterTuningJobName(hyperparameterTuningJobName: string): string | number;
    /**
     * Parse the hyperparameter_tuning_job from HyperparameterTuningJob resource.
     *
     * @param {string} hyperparameterTuningJobName
     *   A fully-qualified path representing HyperparameterTuningJob resource.
     * @returns {string} A string representing the hyperparameter_tuning_job.
     */
    matchHyperparameterTuningJobFromHyperparameterTuningJobName(hyperparameterTuningJobName: string): string | number;
    /**
     * Return a fully-qualified index resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} index
     * @returns {string} Resource name string.
     */
    indexPath(project: string, location: string, index: string): string;
    /**
     * Parse the project from Index resource.
     *
     * @param {string} indexName
     *   A fully-qualified path representing Index resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromIndexName(indexName: string): string | number;
    /**
     * Parse the location from Index resource.
     *
     * @param {string} indexName
     *   A fully-qualified path representing Index resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromIndexName(indexName: string): string | number;
    /**
     * Parse the index from Index resource.
     *
     * @param {string} indexName
     *   A fully-qualified path representing Index resource.
     * @returns {string} A string representing the index.
     */
    matchIndexFromIndexName(indexName: string): string | number;
    /**
     * Return a fully-qualified indexEndpoint resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} index_endpoint
     * @returns {string} Resource name string.
     */
    indexEndpointPath(project: string, location: string, indexEndpoint: string): string;
    /**
     * Parse the project from IndexEndpoint resource.
     *
     * @param {string} indexEndpointName
     *   A fully-qualified path representing IndexEndpoint resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromIndexEndpointName(indexEndpointName: string): string | number;
    /**
     * Parse the location from IndexEndpoint resource.
     *
     * @param {string} indexEndpointName
     *   A fully-qualified path representing IndexEndpoint resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromIndexEndpointName(indexEndpointName: string): string | number;
    /**
     * Parse the index_endpoint from IndexEndpoint resource.
     *
     * @param {string} indexEndpointName
     *   A fully-qualified path representing IndexEndpoint resource.
     * @returns {string} A string representing the index_endpoint.
     */
    matchIndexEndpointFromIndexEndpointName(indexEndpointName: string): string | number;
    /**
     * Return a fully-qualified location resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @returns {string} Resource name string.
     */
    locationPath(project: string, location: string): string;
    /**
     * Parse the project from Location resource.
     *
     * @param {string} locationName
     *   A fully-qualified path representing Location resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromLocationName(locationName: string): string | number;
    /**
     * Parse the location from Location resource.
     *
     * @param {string} locationName
     *   A fully-qualified path representing Location resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromLocationName(locationName: string): string | number;
    /**
     * Return a fully-qualified metadataSchema resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} metadata_store
     * @param {string} metadata_schema
     * @returns {string} Resource name string.
     */
    metadataSchemaPath(project: string, location: string, metadataStore: string, metadataSchema: string): string;
    /**
     * Parse the project from MetadataSchema resource.
     *
     * @param {string} metadataSchemaName
     *   A fully-qualified path representing MetadataSchema resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromMetadataSchemaName(metadataSchemaName: string): string | number;
    /**
     * Parse the location from MetadataSchema resource.
     *
     * @param {string} metadataSchemaName
     *   A fully-qualified path representing MetadataSchema resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromMetadataSchemaName(metadataSchemaName: string): string | number;
    /**
     * Parse the metadata_store from MetadataSchema resource.
     *
     * @param {string} metadataSchemaName
     *   A fully-qualified path representing MetadataSchema resource.
     * @returns {string} A string representing the metadata_store.
     */
    matchMetadataStoreFromMetadataSchemaName(metadataSchemaName: string): string | number;
    /**
     * Parse the metadata_schema from MetadataSchema resource.
     *
     * @param {string} metadataSchemaName
     *   A fully-qualified path representing MetadataSchema resource.
     * @returns {string} A string representing the metadata_schema.
     */
    matchMetadataSchemaFromMetadataSchemaName(metadataSchemaName: string): string | number;
    /**
     * Return a fully-qualified metadataStore resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} metadata_store
     * @returns {string} Resource name string.
     */
    metadataStorePath(project: string, location: string, metadataStore: string): string;
    /**
     * Parse the project from MetadataStore resource.
     *
     * @param {string} metadataStoreName
     *   A fully-qualified path representing MetadataStore resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromMetadataStoreName(metadataStoreName: string): string | number;
    /**
     * Parse the location from MetadataStore resource.
     *
     * @param {string} metadataStoreName
     *   A fully-qualified path representing MetadataStore resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromMetadataStoreName(metadataStoreName: string): string | number;
    /**
     * Parse the metadata_store from MetadataStore resource.
     *
     * @param {string} metadataStoreName
     *   A fully-qualified path representing MetadataStore resource.
     * @returns {string} A string representing the metadata_store.
     */
    matchMetadataStoreFromMetadataStoreName(metadataStoreName: string): string | number;
    /**
     * Return a fully-qualified model resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} model
     * @returns {string} Resource name string.
     */
    modelPath(project: string, location: string, model: string): string;
    /**
     * Parse the project from Model resource.
     *
     * @param {string} modelName
     *   A fully-qualified path representing Model resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromModelName(modelName: string): string | number;
    /**
     * Parse the location from Model resource.
     *
     * @param {string} modelName
     *   A fully-qualified path representing Model resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromModelName(modelName: string): string | number;
    /**
     * Parse the model from Model resource.
     *
     * @param {string} modelName
     *   A fully-qualified path representing Model resource.
     * @returns {string} A string representing the model.
     */
    matchModelFromModelName(modelName: string): string | number;
    /**
     * Return a fully-qualified modelDeploymentMonitoringJob resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} model_deployment_monitoring_job
     * @returns {string} Resource name string.
     */
    modelDeploymentMonitoringJobPath(project: string, location: string, modelDeploymentMonitoringJob: string): string;
    /**
     * Parse the project from ModelDeploymentMonitoringJob resource.
     *
     * @param {string} modelDeploymentMonitoringJobName
     *   A fully-qualified path representing ModelDeploymentMonitoringJob resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromModelDeploymentMonitoringJobName(modelDeploymentMonitoringJobName: string): string | number;
    /**
     * Parse the location from ModelDeploymentMonitoringJob resource.
     *
     * @param {string} modelDeploymentMonitoringJobName
     *   A fully-qualified path representing ModelDeploymentMonitoringJob resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromModelDeploymentMonitoringJobName(modelDeploymentMonitoringJobName: string): string | number;
    /**
     * Parse the model_deployment_monitoring_job from ModelDeploymentMonitoringJob resource.
     *
     * @param {string} modelDeploymentMonitoringJobName
     *   A fully-qualified path representing ModelDeploymentMonitoringJob resource.
     * @returns {string} A string representing the model_deployment_monitoring_job.
     */
    matchModelDeploymentMonitoringJobFromModelDeploymentMonitoringJobName(modelDeploymentMonitoringJobName: string): string | number;
    /**
     * Return a fully-qualified modelEvaluation resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} model
     * @param {string} evaluation
     * @returns {string} Resource name string.
     */
    modelEvaluationPath(project: string, location: string, model: string, evaluation: string): string;
    /**
     * Parse the project from ModelEvaluation resource.
     *
     * @param {string} modelEvaluationName
     *   A fully-qualified path representing ModelEvaluation resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromModelEvaluationName(modelEvaluationName: string): string | number;
    /**
     * Parse the location from ModelEvaluation resource.
     *
     * @param {string} modelEvaluationName
     *   A fully-qualified path representing ModelEvaluation resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromModelEvaluationName(modelEvaluationName: string): string | number;
    /**
     * Parse the model from ModelEvaluation resource.
     *
     * @param {string} modelEvaluationName
     *   A fully-qualified path representing ModelEvaluation resource.
     * @returns {string} A string representing the model.
     */
    matchModelFromModelEvaluationName(modelEvaluationName: string): string | number;
    /**
     * Parse the evaluation from ModelEvaluation resource.
     *
     * @param {string} modelEvaluationName
     *   A fully-qualified path representing ModelEvaluation resource.
     * @returns {string} A string representing the evaluation.
     */
    matchEvaluationFromModelEvaluationName(modelEvaluationName: string): string | number;
    /**
     * Return a fully-qualified modelEvaluationSlice resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} model
     * @param {string} evaluation
     * @param {string} slice
     * @returns {string} Resource name string.
     */
    modelEvaluationSlicePath(project: string, location: string, model: string, evaluation: string, slice: string): string;
    /**
     * Parse the project from ModelEvaluationSlice resource.
     *
     * @param {string} modelEvaluationSliceName
     *   A fully-qualified path representing ModelEvaluationSlice resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromModelEvaluationSliceName(modelEvaluationSliceName: string): string | number;
    /**
     * Parse the location from ModelEvaluationSlice resource.
     *
     * @param {string} modelEvaluationSliceName
     *   A fully-qualified path representing ModelEvaluationSlice resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromModelEvaluationSliceName(modelEvaluationSliceName: string): string | number;
    /**
     * Parse the model from ModelEvaluationSlice resource.
     *
     * @param {string} modelEvaluationSliceName
     *   A fully-qualified path representing ModelEvaluationSlice resource.
     * @returns {string} A string representing the model.
     */
    matchModelFromModelEvaluationSliceName(modelEvaluationSliceName: string): string | number;
    /**
     * Parse the evaluation from ModelEvaluationSlice resource.
     *
     * @param {string} modelEvaluationSliceName
     *   A fully-qualified path representing ModelEvaluationSlice resource.
     * @returns {string} A string representing the evaluation.
     */
    matchEvaluationFromModelEvaluationSliceName(modelEvaluationSliceName: string): string | number;
    /**
     * Parse the slice from ModelEvaluationSlice resource.
     *
     * @param {string} modelEvaluationSliceName
     *   A fully-qualified path representing ModelEvaluationSlice resource.
     * @returns {string} A string representing the slice.
     */
    matchSliceFromModelEvaluationSliceName(modelEvaluationSliceName: string): string | number;
    /**
     * Return a fully-qualified nasJob resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} nas_job
     * @returns {string} Resource name string.
     */
    nasJobPath(project: string, location: string, nasJob: string): string;
    /**
     * Parse the project from NasJob resource.
     *
     * @param {string} nasJobName
     *   A fully-qualified path representing NasJob resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromNasJobName(nasJobName: string): string | number;
    /**
     * Parse the location from NasJob resource.
     *
     * @param {string} nasJobName
     *   A fully-qualified path representing NasJob resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromNasJobName(nasJobName: string): string | number;
    /**
     * Parse the nas_job from NasJob resource.
     *
     * @param {string} nasJobName
     *   A fully-qualified path representing NasJob resource.
     * @returns {string} A string representing the nas_job.
     */
    matchNasJobFromNasJobName(nasJobName: string): string | number;
    /**
     * Return a fully-qualified nasTrialDetail resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} nas_job
     * @param {string} nas_trial_detail
     * @returns {string} Resource name string.
     */
    nasTrialDetailPath(project: string, location: string, nasJob: string, nasTrialDetail: string): string;
    /**
     * Parse the project from NasTrialDetail resource.
     *
     * @param {string} nasTrialDetailName
     *   A fully-qualified path representing NasTrialDetail resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromNasTrialDetailName(nasTrialDetailName: string): string | number;
    /**
     * Parse the location from NasTrialDetail resource.
     *
     * @param {string} nasTrialDetailName
     *   A fully-qualified path representing NasTrialDetail resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromNasTrialDetailName(nasTrialDetailName: string): string | number;
    /**
     * Parse the nas_job from NasTrialDetail resource.
     *
     * @param {string} nasTrialDetailName
     *   A fully-qualified path representing NasTrialDetail resource.
     * @returns {string} A string representing the nas_job.
     */
    matchNasJobFromNasTrialDetailName(nasTrialDetailName: string): string | number;
    /**
     * Parse the nas_trial_detail from NasTrialDetail resource.
     *
     * @param {string} nasTrialDetailName
     *   A fully-qualified path representing NasTrialDetail resource.
     * @returns {string} A string representing the nas_trial_detail.
     */
    matchNasTrialDetailFromNasTrialDetailName(nasTrialDetailName: string): string | number;
    /**
     * Return a fully-qualified notebookExecutionJob resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} notebook_execution_job
     * @returns {string} Resource name string.
     */
    notebookExecutionJobPath(project: string, location: string, notebookExecutionJob: string): string;
    /**
     * Parse the project from NotebookExecutionJob resource.
     *
     * @param {string} notebookExecutionJobName
     *   A fully-qualified path representing NotebookExecutionJob resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromNotebookExecutionJobName(notebookExecutionJobName: string): string | number;
    /**
     * Parse the location from NotebookExecutionJob resource.
     *
     * @param {string} notebookExecutionJobName
     *   A fully-qualified path representing NotebookExecutionJob resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromNotebookExecutionJobName(notebookExecutionJobName: string): string | number;
    /**
     * Parse the notebook_execution_job from NotebookExecutionJob resource.
     *
     * @param {string} notebookExecutionJobName
     *   A fully-qualified path representing NotebookExecutionJob resource.
     * @returns {string} A string representing the notebook_execution_job.
     */
    matchNotebookExecutionJobFromNotebookExecutionJobName(notebookExecutionJobName: string): string | number;
    /**
     * Return a fully-qualified notebookRuntime resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} notebook_runtime
     * @returns {string} Resource name string.
     */
    notebookRuntimePath(project: string, location: string, notebookRuntime: string): string;
    /**
     * Parse the project from NotebookRuntime resource.
     *
     * @param {string} notebookRuntimeName
     *   A fully-qualified path representing NotebookRuntime resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromNotebookRuntimeName(notebookRuntimeName: string): string | number;
    /**
     * Parse the location from NotebookRuntime resource.
     *
     * @param {string} notebookRuntimeName
     *   A fully-qualified path representing NotebookRuntime resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromNotebookRuntimeName(notebookRuntimeName: string): string | number;
    /**
     * Parse the notebook_runtime from NotebookRuntime resource.
     *
     * @param {string} notebookRuntimeName
     *   A fully-qualified path representing NotebookRuntime resource.
     * @returns {string} A string representing the notebook_runtime.
     */
    matchNotebookRuntimeFromNotebookRuntimeName(notebookRuntimeName: string): string | number;
    /**
     * Return a fully-qualified notebookRuntimeTemplate resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} notebook_runtime_template
     * @returns {string} Resource name string.
     */
    notebookRuntimeTemplatePath(project: string, location: string, notebookRuntimeTemplate: string): string;
    /**
     * Parse the project from NotebookRuntimeTemplate resource.
     *
     * @param {string} notebookRuntimeTemplateName
     *   A fully-qualified path representing NotebookRuntimeTemplate resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromNotebookRuntimeTemplateName(notebookRuntimeTemplateName: string): string | number;
    /**
     * Parse the location from NotebookRuntimeTemplate resource.
     *
     * @param {string} notebookRuntimeTemplateName
     *   A fully-qualified path representing NotebookRuntimeTemplate resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromNotebookRuntimeTemplateName(notebookRuntimeTemplateName: string): string | number;
    /**
     * Parse the notebook_runtime_template from NotebookRuntimeTemplate resource.
     *
     * @param {string} notebookRuntimeTemplateName
     *   A fully-qualified path representing NotebookRuntimeTemplate resource.
     * @returns {string} A string representing the notebook_runtime_template.
     */
    matchNotebookRuntimeTemplateFromNotebookRuntimeTemplateName(notebookRuntimeTemplateName: string): string | number;
    /**
     * Return a fully-qualified persistentResource resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} persistent_resource
     * @returns {string} Resource name string.
     */
    persistentResourcePath(project: string, location: string, persistentResource: string): string;
    /**
     * Parse the project from PersistentResource resource.
     *
     * @param {string} persistentResourceName
     *   A fully-qualified path representing PersistentResource resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromPersistentResourceName(persistentResourceName: string): string | number;
    /**
     * Parse the location from PersistentResource resource.
     *
     * @param {string} persistentResourceName
     *   A fully-qualified path representing PersistentResource resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromPersistentResourceName(persistentResourceName: string): string | number;
    /**
     * Parse the persistent_resource from PersistentResource resource.
     *
     * @param {string} persistentResourceName
     *   A fully-qualified path representing PersistentResource resource.
     * @returns {string} A string representing the persistent_resource.
     */
    matchPersistentResourceFromPersistentResourceName(persistentResourceName: string): string | number;
    /**
     * Return a fully-qualified pipelineJob resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} pipeline_job
     * @returns {string} Resource name string.
     */
    pipelineJobPath(project: string, location: string, pipelineJob: string): string;
    /**
     * Parse the project from PipelineJob resource.
     *
     * @param {string} pipelineJobName
     *   A fully-qualified path representing PipelineJob resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromPipelineJobName(pipelineJobName: string): string | number;
    /**
     * Parse the location from PipelineJob resource.
     *
     * @param {string} pipelineJobName
     *   A fully-qualified path representing PipelineJob resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromPipelineJobName(pipelineJobName: string): string | number;
    /**
     * Parse the pipeline_job from PipelineJob resource.
     *
     * @param {string} pipelineJobName
     *   A fully-qualified path representing PipelineJob resource.
     * @returns {string} A string representing the pipeline_job.
     */
    matchPipelineJobFromPipelineJobName(pipelineJobName: string): string | number;
    /**
     * Return a fully-qualified project resource name string.
     *
     * @param {string} project
     * @returns {string} Resource name string.
     */
    projectPath(project: string): string;
    /**
     * Parse the project from Project resource.
     *
     * @param {string} projectName
     *   A fully-qualified path representing Project resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromProjectName(projectName: string): string | number;
    /**
     * Return a fully-qualified projectLocationEndpoint resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} endpoint
     * @returns {string} Resource name string.
     */
    projectLocationEndpointPath(project: string, location: string, endpoint: string): string;
    /**
     * Parse the project from ProjectLocationEndpoint resource.
     *
     * @param {string} projectLocationEndpointName
     *   A fully-qualified path representing project_location_endpoint resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromProjectLocationEndpointName(projectLocationEndpointName: string): string | number;
    /**
     * Parse the location from ProjectLocationEndpoint resource.
     *
     * @param {string} projectLocationEndpointName
     *   A fully-qualified path representing project_location_endpoint resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromProjectLocationEndpointName(projectLocationEndpointName: string): string | number;
    /**
     * Parse the endpoint from ProjectLocationEndpoint resource.
     *
     * @param {string} projectLocationEndpointName
     *   A fully-qualified path representing project_location_endpoint resource.
     * @returns {string} A string representing the endpoint.
     */
    matchEndpointFromProjectLocationEndpointName(projectLocationEndpointName: string): string | number;
    /**
     * Return a fully-qualified projectLocationFeatureGroupFeature resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} feature_group
     * @param {string} feature
     * @returns {string} Resource name string.
     */
    projectLocationFeatureGroupFeaturePath(project: string, location: string, featureGroup: string, feature: string): string;
    /**
     * Parse the project from ProjectLocationFeatureGroupFeature resource.
     *
     * @param {string} projectLocationFeatureGroupFeatureName
     *   A fully-qualified path representing project_location_feature_group_feature resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromProjectLocationFeatureGroupFeatureName(projectLocationFeatureGroupFeatureName: string): string | number;
    /**
     * Parse the location from ProjectLocationFeatureGroupFeature resource.
     *
     * @param {string} projectLocationFeatureGroupFeatureName
     *   A fully-qualified path representing project_location_feature_group_feature resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromProjectLocationFeatureGroupFeatureName(projectLocationFeatureGroupFeatureName: string): string | number;
    /**
     * Parse the feature_group from ProjectLocationFeatureGroupFeature resource.
     *
     * @param {string} projectLocationFeatureGroupFeatureName
     *   A fully-qualified path representing project_location_feature_group_feature resource.
     * @returns {string} A string representing the feature_group.
     */
    matchFeatureGroupFromProjectLocationFeatureGroupFeatureName(projectLocationFeatureGroupFeatureName: string): string | number;
    /**
     * Parse the feature from ProjectLocationFeatureGroupFeature resource.
     *
     * @param {string} projectLocationFeatureGroupFeatureName
     *   A fully-qualified path representing project_location_feature_group_feature resource.
     * @returns {string} A string representing the feature.
     */
    matchFeatureFromProjectLocationFeatureGroupFeatureName(projectLocationFeatureGroupFeatureName: string): string | number;
    /**
     * Return a fully-qualified projectLocationFeaturestoreEntityTypeFeature resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} featurestore
     * @param {string} entity_type
     * @param {string} feature
     * @returns {string} Resource name string.
     */
    projectLocationFeaturestoreEntityTypeFeaturePath(project: string, location: string, featurestore: string, entityType: string, feature: string): string;
    /**
     * Parse the project from ProjectLocationFeaturestoreEntityTypeFeature resource.
     *
     * @param {string} projectLocationFeaturestoreEntityTypeFeatureName
     *   A fully-qualified path representing project_location_featurestore_entity_type_feature resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromProjectLocationFeaturestoreEntityTypeFeatureName(projectLocationFeaturestoreEntityTypeFeatureName: string): string | number;
    /**
     * Parse the location from ProjectLocationFeaturestoreEntityTypeFeature resource.
     *
     * @param {string} projectLocationFeaturestoreEntityTypeFeatureName
     *   A fully-qualified path representing project_location_featurestore_entity_type_feature resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromProjectLocationFeaturestoreEntityTypeFeatureName(projectLocationFeaturestoreEntityTypeFeatureName: string): string | number;
    /**
     * Parse the featurestore from ProjectLocationFeaturestoreEntityTypeFeature resource.
     *
     * @param {string} projectLocationFeaturestoreEntityTypeFeatureName
     *   A fully-qualified path representing project_location_featurestore_entity_type_feature resource.
     * @returns {string} A string representing the featurestore.
     */
    matchFeaturestoreFromProjectLocationFeaturestoreEntityTypeFeatureName(projectLocationFeaturestoreEntityTypeFeatureName: string): string | number;
    /**
     * Parse the entity_type from ProjectLocationFeaturestoreEntityTypeFeature resource.
     *
     * @param {string} projectLocationFeaturestoreEntityTypeFeatureName
     *   A fully-qualified path representing project_location_featurestore_entity_type_feature resource.
     * @returns {string} A string representing the entity_type.
     */
    matchEntityTypeFromProjectLocationFeaturestoreEntityTypeFeatureName(projectLocationFeaturestoreEntityTypeFeatureName: string): string | number;
    /**
     * Parse the feature from ProjectLocationFeaturestoreEntityTypeFeature resource.
     *
     * @param {string} projectLocationFeaturestoreEntityTypeFeatureName
     *   A fully-qualified path representing project_location_featurestore_entity_type_feature resource.
     * @returns {string} A string representing the feature.
     */
    matchFeatureFromProjectLocationFeaturestoreEntityTypeFeatureName(projectLocationFeaturestoreEntityTypeFeatureName: string): string | number;
    /**
     * Return a fully-qualified projectLocationPublisherModel resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} publisher
     * @param {string} model
     * @returns {string} Resource name string.
     */
    projectLocationPublisherModelPath(project: string, location: string, publisher: string, model: string): string;
    /**
     * Parse the project from ProjectLocationPublisherModel resource.
     *
     * @param {string} projectLocationPublisherModelName
     *   A fully-qualified path representing project_location_publisher_model resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromProjectLocationPublisherModelName(projectLocationPublisherModelName: string): string | number;
    /**
     * Parse the location from ProjectLocationPublisherModel resource.
     *
     * @param {string} projectLocationPublisherModelName
     *   A fully-qualified path representing project_location_publisher_model resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromProjectLocationPublisherModelName(projectLocationPublisherModelName: string): string | number;
    /**
     * Parse the publisher from ProjectLocationPublisherModel resource.
     *
     * @param {string} projectLocationPublisherModelName
     *   A fully-qualified path representing project_location_publisher_model resource.
     * @returns {string} A string representing the publisher.
     */
    matchPublisherFromProjectLocationPublisherModelName(projectLocationPublisherModelName: string): string | number;
    /**
     * Parse the model from ProjectLocationPublisherModel resource.
     *
     * @param {string} projectLocationPublisherModelName
     *   A fully-qualified path representing project_location_publisher_model resource.
     * @returns {string} A string representing the model.
     */
    matchModelFromProjectLocationPublisherModelName(projectLocationPublisherModelName: string): string | number;
    /**
     * Return a fully-qualified publisherModel resource name string.
     *
     * @param {string} publisher
     * @param {string} model
     * @returns {string} Resource name string.
     */
    publisherModelPath(publisher: string, model: string): string;
    /**
     * Parse the publisher from PublisherModel resource.
     *
     * @param {string} publisherModelName
     *   A fully-qualified path representing PublisherModel resource.
     * @returns {string} A string representing the publisher.
     */
    matchPublisherFromPublisherModelName(publisherModelName: string): string | number;
    /**
     * Parse the model from PublisherModel resource.
     *
     * @param {string} publisherModelName
     *   A fully-qualified path representing PublisherModel resource.
     * @returns {string} A string representing the model.
     */
    matchModelFromPublisherModelName(publisherModelName: string): string | number;
    /**
     * Return a fully-qualified ragCorpus resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} rag_corpus
     * @returns {string} Resource name string.
     */
    ragCorpusPath(project: string, location: string, ragCorpus: string): string;
    /**
     * Parse the project from RagCorpus resource.
     *
     * @param {string} ragCorpusName
     *   A fully-qualified path representing RagCorpus resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromRagCorpusName(ragCorpusName: string): string | number;
    /**
     * Parse the location from RagCorpus resource.
     *
     * @param {string} ragCorpusName
     *   A fully-qualified path representing RagCorpus resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromRagCorpusName(ragCorpusName: string): string | number;
    /**
     * Parse the rag_corpus from RagCorpus resource.
     *
     * @param {string} ragCorpusName
     *   A fully-qualified path representing RagCorpus resource.
     * @returns {string} A string representing the rag_corpus.
     */
    matchRagCorpusFromRagCorpusName(ragCorpusName: string): string | number;
    /**
     * Return a fully-qualified ragFile resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} rag_corpus
     * @param {string} rag_file
     * @returns {string} Resource name string.
     */
    ragFilePath(project: string, location: string, ragCorpus: string, ragFile: string): string;
    /**
     * Parse the project from RagFile resource.
     *
     * @param {string} ragFileName
     *   A fully-qualified path representing RagFile resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromRagFileName(ragFileName: string): string | number;
    /**
     * Parse the location from RagFile resource.
     *
     * @param {string} ragFileName
     *   A fully-qualified path representing RagFile resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromRagFileName(ragFileName: string): string | number;
    /**
     * Parse the rag_corpus from RagFile resource.
     *
     * @param {string} ragFileName
     *   A fully-qualified path representing RagFile resource.
     * @returns {string} A string representing the rag_corpus.
     */
    matchRagCorpusFromRagFileName(ragFileName: string): string | number;
    /**
     * Parse the rag_file from RagFile resource.
     *
     * @param {string} ragFileName
     *   A fully-qualified path representing RagFile resource.
     * @returns {string} A string representing the rag_file.
     */
    matchRagFileFromRagFileName(ragFileName: string): string | number;
    /**
     * Return a fully-qualified reasoningEngine resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} reasoning_engine
     * @returns {string} Resource name string.
     */
    reasoningEnginePath(project: string, location: string, reasoningEngine: string): string;
    /**
     * Parse the project from ReasoningEngine resource.
     *
     * @param {string} reasoningEngineName
     *   A fully-qualified path representing ReasoningEngine resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromReasoningEngineName(reasoningEngineName: string): string | number;
    /**
     * Parse the location from ReasoningEngine resource.
     *
     * @param {string} reasoningEngineName
     *   A fully-qualified path representing ReasoningEngine resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromReasoningEngineName(reasoningEngineName: string): string | number;
    /**
     * Parse the reasoning_engine from ReasoningEngine resource.
     *
     * @param {string} reasoningEngineName
     *   A fully-qualified path representing ReasoningEngine resource.
     * @returns {string} A string representing the reasoning_engine.
     */
    matchReasoningEngineFromReasoningEngineName(reasoningEngineName: string): string | number;
    /**
     * Return a fully-qualified savedQuery resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} dataset
     * @param {string} saved_query
     * @returns {string} Resource name string.
     */
    savedQueryPath(project: string, location: string, dataset: string, savedQuery: string): string;
    /**
     * Parse the project from SavedQuery resource.
     *
     * @param {string} savedQueryName
     *   A fully-qualified path representing SavedQuery resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromSavedQueryName(savedQueryName: string): string | number;
    /**
     * Parse the location from SavedQuery resource.
     *
     * @param {string} savedQueryName
     *   A fully-qualified path representing SavedQuery resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromSavedQueryName(savedQueryName: string): string | number;
    /**
     * Parse the dataset from SavedQuery resource.
     *
     * @param {string} savedQueryName
     *   A fully-qualified path representing SavedQuery resource.
     * @returns {string} A string representing the dataset.
     */
    matchDatasetFromSavedQueryName(savedQueryName: string): string | number;
    /**
     * Parse the saved_query from SavedQuery resource.
     *
     * @param {string} savedQueryName
     *   A fully-qualified path representing SavedQuery resource.
     * @returns {string} A string representing the saved_query.
     */
    matchSavedQueryFromSavedQueryName(savedQueryName: string): string | number;
    /**
     * Return a fully-qualified schedule resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} schedule
     * @returns {string} Resource name string.
     */
    schedulePath(project: string, location: string, schedule: string): string;
    /**
     * Parse the project from Schedule resource.
     *
     * @param {string} scheduleName
     *   A fully-qualified path representing Schedule resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromScheduleName(scheduleName: string): string | number;
    /**
     * Parse the location from Schedule resource.
     *
     * @param {string} scheduleName
     *   A fully-qualified path representing Schedule resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromScheduleName(scheduleName: string): string | number;
    /**
     * Parse the schedule from Schedule resource.
     *
     * @param {string} scheduleName
     *   A fully-qualified path representing Schedule resource.
     * @returns {string} A string representing the schedule.
     */
    matchScheduleFromScheduleName(scheduleName: string): string | number;
    /**
     * Return a fully-qualified specialistPool resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} specialist_pool
     * @returns {string} Resource name string.
     */
    specialistPoolPath(project: string, location: string, specialistPool: string): string;
    /**
     * Parse the project from SpecialistPool resource.
     *
     * @param {string} specialistPoolName
     *   A fully-qualified path representing SpecialistPool resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromSpecialistPoolName(specialistPoolName: string): string | number;
    /**
     * Parse the location from SpecialistPool resource.
     *
     * @param {string} specialistPoolName
     *   A fully-qualified path representing SpecialistPool resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromSpecialistPoolName(specialistPoolName: string): string | number;
    /**
     * Parse the specialist_pool from SpecialistPool resource.
     *
     * @param {string} specialistPoolName
     *   A fully-qualified path representing SpecialistPool resource.
     * @returns {string} A string representing the specialist_pool.
     */
    matchSpecialistPoolFromSpecialistPoolName(specialistPoolName: string): string | number;
    /**
     * Return a fully-qualified study resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} study
     * @returns {string} Resource name string.
     */
    studyPath(project: string, location: string, study: string): string;
    /**
     * Parse the project from Study resource.
     *
     * @param {string} studyName
     *   A fully-qualified path representing Study resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromStudyName(studyName: string): string | number;
    /**
     * Parse the location from Study resource.
     *
     * @param {string} studyName
     *   A fully-qualified path representing Study resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromStudyName(studyName: string): string | number;
    /**
     * Parse the study from Study resource.
     *
     * @param {string} studyName
     *   A fully-qualified path representing Study resource.
     * @returns {string} A string representing the study.
     */
    matchStudyFromStudyName(studyName: string): string | number;
    /**
     * Return a fully-qualified tensorboard resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} tensorboard
     * @returns {string} Resource name string.
     */
    tensorboardPath(project: string, location: string, tensorboard: string): string;
    /**
     * Parse the project from Tensorboard resource.
     *
     * @param {string} tensorboardName
     *   A fully-qualified path representing Tensorboard resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromTensorboardName(tensorboardName: string): string | number;
    /**
     * Parse the location from Tensorboard resource.
     *
     * @param {string} tensorboardName
     *   A fully-qualified path representing Tensorboard resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromTensorboardName(tensorboardName: string): string | number;
    /**
     * Parse the tensorboard from Tensorboard resource.
     *
     * @param {string} tensorboardName
     *   A fully-qualified path representing Tensorboard resource.
     * @returns {string} A string representing the tensorboard.
     */
    matchTensorboardFromTensorboardName(tensorboardName: string): string | number;
    /**
     * Return a fully-qualified tensorboardExperiment resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} tensorboard
     * @param {string} experiment
     * @returns {string} Resource name string.
     */
    tensorboardExperimentPath(project: string, location: string, tensorboard: string, experiment: string): string;
    /**
     * Parse the project from TensorboardExperiment resource.
     *
     * @param {string} tensorboardExperimentName
     *   A fully-qualified path representing TensorboardExperiment resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromTensorboardExperimentName(tensorboardExperimentName: string): string | number;
    /**
     * Parse the location from TensorboardExperiment resource.
     *
     * @param {string} tensorboardExperimentName
     *   A fully-qualified path representing TensorboardExperiment resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromTensorboardExperimentName(tensorboardExperimentName: string): string | number;
    /**
     * Parse the tensorboard from TensorboardExperiment resource.
     *
     * @param {string} tensorboardExperimentName
     *   A fully-qualified path representing TensorboardExperiment resource.
     * @returns {string} A string representing the tensorboard.
     */
    matchTensorboardFromTensorboardExperimentName(tensorboardExperimentName: string): string | number;
    /**
     * Parse the experiment from TensorboardExperiment resource.
     *
     * @param {string} tensorboardExperimentName
     *   A fully-qualified path representing TensorboardExperiment resource.
     * @returns {string} A string representing the experiment.
     */
    matchExperimentFromTensorboardExperimentName(tensorboardExperimentName: string): string | number;
    /**
     * Return a fully-qualified tensorboardRun resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} tensorboard
     * @param {string} experiment
     * @param {string} run
     * @returns {string} Resource name string.
     */
    tensorboardRunPath(project: string, location: string, tensorboard: string, experiment: string, run: string): string;
    /**
     * Parse the project from TensorboardRun resource.
     *
     * @param {string} tensorboardRunName
     *   A fully-qualified path representing TensorboardRun resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromTensorboardRunName(tensorboardRunName: string): string | number;
    /**
     * Parse the location from TensorboardRun resource.
     *
     * @param {string} tensorboardRunName
     *   A fully-qualified path representing TensorboardRun resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromTensorboardRunName(tensorboardRunName: string): string | number;
    /**
     * Parse the tensorboard from TensorboardRun resource.
     *
     * @param {string} tensorboardRunName
     *   A fully-qualified path representing TensorboardRun resource.
     * @returns {string} A string representing the tensorboard.
     */
    matchTensorboardFromTensorboardRunName(tensorboardRunName: string): string | number;
    /**
     * Parse the experiment from TensorboardRun resource.
     *
     * @param {string} tensorboardRunName
     *   A fully-qualified path representing TensorboardRun resource.
     * @returns {string} A string representing the experiment.
     */
    matchExperimentFromTensorboardRunName(tensorboardRunName: string): string | number;
    /**
     * Parse the run from TensorboardRun resource.
     *
     * @param {string} tensorboardRunName
     *   A fully-qualified path representing TensorboardRun resource.
     * @returns {string} A string representing the run.
     */
    matchRunFromTensorboardRunName(tensorboardRunName: string): string | number;
    /**
     * Return a fully-qualified tensorboardTimeSeries resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} tensorboard
     * @param {string} experiment
     * @param {string} run
     * @param {string} time_series
     * @returns {string} Resource name string.
     */
    tensorboardTimeSeriesPath(project: string, location: string, tensorboard: string, experiment: string, run: string, timeSeries: string): string;
    /**
     * Parse the project from TensorboardTimeSeries resource.
     *
     * @param {string} tensorboardTimeSeriesName
     *   A fully-qualified path representing TensorboardTimeSeries resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromTensorboardTimeSeriesName(tensorboardTimeSeriesName: string): string | number;
    /**
     * Parse the location from TensorboardTimeSeries resource.
     *
     * @param {string} tensorboardTimeSeriesName
     *   A fully-qualified path representing TensorboardTimeSeries resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromTensorboardTimeSeriesName(tensorboardTimeSeriesName: string): string | number;
    /**
     * Parse the tensorboard from TensorboardTimeSeries resource.
     *
     * @param {string} tensorboardTimeSeriesName
     *   A fully-qualified path representing TensorboardTimeSeries resource.
     * @returns {string} A string representing the tensorboard.
     */
    matchTensorboardFromTensorboardTimeSeriesName(tensorboardTimeSeriesName: string): string | number;
    /**
     * Parse the experiment from TensorboardTimeSeries resource.
     *
     * @param {string} tensorboardTimeSeriesName
     *   A fully-qualified path representing TensorboardTimeSeries resource.
     * @returns {string} A string representing the experiment.
     */
    matchExperimentFromTensorboardTimeSeriesName(tensorboardTimeSeriesName: string): string | number;
    /**
     * Parse the run from TensorboardTimeSeries resource.
     *
     * @param {string} tensorboardTimeSeriesName
     *   A fully-qualified path representing TensorboardTimeSeries resource.
     * @returns {string} A string representing the run.
     */
    matchRunFromTensorboardTimeSeriesName(tensorboardTimeSeriesName: string): string | number;
    /**
     * Parse the time_series from TensorboardTimeSeries resource.
     *
     * @param {string} tensorboardTimeSeriesName
     *   A fully-qualified path representing TensorboardTimeSeries resource.
     * @returns {string} A string representing the time_series.
     */
    matchTimeSeriesFromTensorboardTimeSeriesName(tensorboardTimeSeriesName: string): string | number;
    /**
     * Return a fully-qualified trainingPipeline resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} training_pipeline
     * @returns {string} Resource name string.
     */
    trainingPipelinePath(project: string, location: string, trainingPipeline: string): string;
    /**
     * Parse the project from TrainingPipeline resource.
     *
     * @param {string} trainingPipelineName
     *   A fully-qualified path representing TrainingPipeline resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromTrainingPipelineName(trainingPipelineName: string): string | number;
    /**
     * Parse the location from TrainingPipeline resource.
     *
     * @param {string} trainingPipelineName
     *   A fully-qualified path representing TrainingPipeline resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromTrainingPipelineName(trainingPipelineName: string): string | number;
    /**
     * Parse the training_pipeline from TrainingPipeline resource.
     *
     * @param {string} trainingPipelineName
     *   A fully-qualified path representing TrainingPipeline resource.
     * @returns {string} A string representing the training_pipeline.
     */
    matchTrainingPipelineFromTrainingPipelineName(trainingPipelineName: string): string | number;
    /**
     * Return a fully-qualified trial resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} study
     * @param {string} trial
     * @returns {string} Resource name string.
     */
    trialPath(project: string, location: string, study: string, trial: string): string;
    /**
     * Parse the project from Trial resource.
     *
     * @param {string} trialName
     *   A fully-qualified path representing Trial resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromTrialName(trialName: string): string | number;
    /**
     * Parse the location from Trial resource.
     *
     * @param {string} trialName
     *   A fully-qualified path representing Trial resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromTrialName(trialName: string): string | number;
    /**
     * Parse the study from Trial resource.
     *
     * @param {string} trialName
     *   A fully-qualified path representing Trial resource.
     * @returns {string} A string representing the study.
     */
    matchStudyFromTrialName(trialName: string): string | number;
    /**
     * Parse the trial from Trial resource.
     *
     * @param {string} trialName
     *   A fully-qualified path representing Trial resource.
     * @returns {string} A string representing the trial.
     */
    matchTrialFromTrialName(trialName: string): string | number;
    /**
     * Return a fully-qualified tuningJob resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} tuning_job
     * @returns {string} Resource name string.
     */
    tuningJobPath(project: string, location: string, tuningJob: string): string;
    /**
     * Parse the project from TuningJob resource.
     *
     * @param {string} tuningJobName
     *   A fully-qualified path representing TuningJob resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromTuningJobName(tuningJobName: string): string | number;
    /**
     * Parse the location from TuningJob resource.
     *
     * @param {string} tuningJobName
     *   A fully-qualified path representing TuningJob resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromTuningJobName(tuningJobName: string): string | number;
    /**
     * Parse the tuning_job from TuningJob resource.
     *
     * @param {string} tuningJobName
     *   A fully-qualified path representing TuningJob resource.
     * @returns {string} A string representing the tuning_job.
     */
    matchTuningJobFromTuningJobName(tuningJobName: string): string | number;
    /**
     * Terminate the gRPC channel and close the client.
     *
     * The client will no longer be usable and all future behavior is undefined.
     * @returns {Promise} A promise that resolves when the client is closed.
     */
    close(): Promise<void>;
}
