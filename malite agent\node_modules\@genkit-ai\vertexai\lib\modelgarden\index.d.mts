import { GenkitPlugin } from 'genkit/plugin';
import { PluginOptions } from './types.mjs';
export { claude35Sonnet, claude35SonnetV2, claude3Hai<PERSON>, claude3Opus, claude3Sonnet } from './anthropic.mjs';
export { codestral, mistral<PERSON><PERSON>ge, mistralNemo } from './mistral.mjs';
export { llama3, llama31, llama32 } from './model_garden.mjs';
import 'genkit';
import '../types-Bc0LKM8D.mjs';
import 'google-auth-library';
import '@google-cloud/vertexai';
import 'genkit/model';
import '@anthropic-ai/sdk/resources/messages';
import '@mistralai/mistralai-gcp/models/components';

/**
 * Copyright 2024 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

/**
 * Add Google Cloud Vertex AI Rerankers API to Genkit.
 */
declare function vertexAIModelGarden(options: PluginOptions): GenkitPlugin;

export { PluginOptions, vertexAIModelGarden };
