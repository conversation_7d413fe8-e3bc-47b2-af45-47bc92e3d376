import type { ReadableLogRecord } from '@opentelemetry/sdk-logs';
import { IExportLogsServiceRequest } from './types';
import { OtlpEncodingOptions, IKeyValue } from '../common/types';
import { LogAttributes } from '@opentelemetry/api-logs';
export declare function createExportLogsServiceRequest(logRecords: ReadableLogRecord[], options?: OtlpEncodingOptions): IExportLogsServiceRequest;
export declare function toLogAttributes(attributes: LogAttributes): IKeyValue[];
//# sourceMappingURL=index.d.ts.map