/**
 * @deprecated Use the one defined in @opentelemetry/sdk-trace-base instead.
 * IdGenerator provides an interface for generating Trace Id and Span Id.
 */
export interface IdGenerator {
    /** Returns a trace ID composed of 32 lowercase hex characters. */
    generateTraceId(): string;
    /** Returns a span ID composed of 16 lowercase hex characters. */
    generateSpanId(): string;
}
//# sourceMappingURL=IdGenerator.d.ts.map