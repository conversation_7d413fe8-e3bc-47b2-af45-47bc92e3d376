// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.aiplatform.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/aiplatform/v1/openapi.proto";
import "google/protobuf/struct.proto";
import "google/type/latlng.proto";

option csharp_namespace = "Google.Cloud.AIPlatform.V1";
option go_package = "cloud.google.com/go/aiplatform/apiv1/aiplatformpb;aiplatformpb";
option java_multiple_files = true;
option java_outer_classname = "ToolProto";
option java_package = "com.google.cloud.aiplatform.v1";
option php_namespace = "Google\\Cloud\\AIPlatform\\V1";
option ruby_package = "Google::Cloud::AIPlatform::V1";

// Tool details that the model may use to generate response.
//
// A `Tool` is a piece of code that enables the system to interact with
// external systems to perform an action, or set of actions, outside of
// knowledge and scope of the model. A Tool object should contain exactly
// one type of Tool (e.g FunctionDeclaration, Retrieval or
// GoogleSearchRetrieval).
message Tool {
  // Tool that executes code generated by the model, and automatically returns
  // the result to the model.
  //
  // See also [ExecutableCode]and [CodeExecutionResult] which are input and
  // output to this tool.
  message CodeExecution {}

  // Optional. Function tool type.
  // One or more function declarations to be passed to the model along with the
  // current user query. Model may decide to call a subset of these functions
  // by populating [FunctionCall][content.part.function_call] in the response.
  // User should provide a [FunctionResponse][content.part.function_response]
  // for each function call in the next turn. Based on the function responses,
  // Model will generate the final response back to the user.
  // Maximum 128 function declarations can be provided.
  repeated FunctionDeclaration function_declarations = 1
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. Retrieval tool type.
  // System will always execute the provided retrieval tool(s) to get external
  // knowledge to answer the prompt. Retrieval results are presented to the
  // model for generation.
  Retrieval retrieval = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. GoogleSearchRetrieval tool type.
  // Specialized retrieval tool that is powered by Google search.
  GoogleSearchRetrieval google_search_retrieval = 3
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. CodeExecution tool type.
  // Enables the model to execute code as part of generation.
  // This field is only used by the Gemini Developer API services.
  CodeExecution code_execution = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Structured representation of a function declaration as defined by the
// [OpenAPI 3.0 specification](https://spec.openapis.org/oas/v3.0.3). Included
// in this declaration are the function name, description, parameters and
// response type. This FunctionDeclaration is a representation of a block of
// code that can be used as a `Tool` by the model and executed by the client.
message FunctionDeclaration {
  // Required. The name of the function to call.
  // Must start with a letter or an underscore.
  // Must be a-z, A-Z, 0-9, or contain underscores, dots and dashes, with a
  // maximum length of 64.
  string name = 1 [(google.api.field_behavior) = REQUIRED];

  // Optional. Description and purpose of the function.
  // Model uses it to decide how and whether to call the function.
  string description = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Describes the parameters to this function in JSON Schema Object
  // format. Reflects the Open API 3.03 Parameter Object. string Key: the name
  // of the parameter. Parameter names are case sensitive. Schema Value: the
  // Schema defining the type used for the parameter. For function with no
  // parameters, this can be left unset. Parameter names must start with a
  // letter or an underscore and must only contain chars a-z, A-Z, 0-9, or
  // underscores with a maximum length of 64. Example with 1 required and 1
  // optional parameter: type: OBJECT properties:
  //  param1:
  //    type: STRING
  //  param2:
  //    type: INTEGER
  // required:
  //  - param1
  Schema parameters = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Describes the output from this function in JSON Schema format.
  // Reflects the Open API 3.03 Response Object. The Schema defines the type
  // used for the response value of the function.
  Schema response = 4 [(google.api.field_behavior) = OPTIONAL];
}

// A predicted [FunctionCall] returned from the model that contains a string
// representing the [FunctionDeclaration.name] and a structured JSON object
// containing the parameters and their values.
message FunctionCall {
  // Required. The name of the function to call.
  // Matches [FunctionDeclaration.name].
  string name = 1 [(google.api.field_behavior) = REQUIRED];

  // Optional. Required. The function parameters and values in JSON object
  // format. See [FunctionDeclaration.parameters] for parameter details.
  google.protobuf.Struct args = 2 [(google.api.field_behavior) = OPTIONAL];
}

// The result output from a [FunctionCall] that contains a string representing
// the [FunctionDeclaration.name] and a structured JSON object containing any
// output from the function is used as context to the model. This should contain
// the result of a [FunctionCall] made based on model prediction.
message FunctionResponse {
  // Required. The name of the function to call.
  // Matches [FunctionDeclaration.name] and [FunctionCall.name].
  string name = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The function response in JSON object format.
  // Use "output" key to specify function output and "error" key to specify
  // error details (if any). If "output" and "error" keys are not specified,
  // then whole "response" is treated as function output.
  google.protobuf.Struct response = 2 [(google.api.field_behavior) = REQUIRED];
}

// Code generated by the model that is meant to be executed, and the result
// returned to the model.
//
// Generated when using the [FunctionDeclaration] tool and
// [FunctionCallingConfig] mode is set to [Mode.CODE].
message ExecutableCode {
  // Supported programming languages for the generated code.
  enum Language {
    // Unspecified language. This value should not be used.
    LANGUAGE_UNSPECIFIED = 0;

    // Python >= 3.10, with numpy and simpy available.
    PYTHON = 1;
  }

  // Required. Programming language of the `code`.
  Language language = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The code to be executed.
  string code = 2 [(google.api.field_behavior) = REQUIRED];
}

// Result of executing the [ExecutableCode].
//
// Always follows a `part` containing the [ExecutableCode].
message CodeExecutionResult {
  // Enumeration of possible outcomes of the code execution.
  enum Outcome {
    // Unspecified status. This value should not be used.
    OUTCOME_UNSPECIFIED = 0;

    // Code execution completed successfully.
    OUTCOME_OK = 1;

    // Code execution finished but with a failure. `stderr` should contain the
    // reason.
    OUTCOME_FAILED = 2;

    // Code execution ran for too long, and was cancelled. There may or may not
    // be a partial output present.
    OUTCOME_DEADLINE_EXCEEDED = 3;
  }

  // Required. Outcome of the code execution.
  Outcome outcome = 1 [(google.api.field_behavior) = REQUIRED];

  // Optional. Contains stdout when code execution is successful, stderr or
  // other description otherwise.
  string output = 2 [(google.api.field_behavior) = OPTIONAL];
}

// Defines a retrieval tool that model can call to access external knowledge.
message Retrieval {
  // The source of the retrieval.
  oneof source {
    // Set to use data source powered by Vertex AI Search.
    VertexAISearch vertex_ai_search = 2;

    // Set to use data source powered by Vertex RAG store.
    // User data is uploaded via the VertexRagDataService.
    VertexRagStore vertex_rag_store = 4;
  }

  // Optional. Deprecated. This option is no longer supported.
  bool disable_attribution = 3
      [deprecated = true, (google.api.field_behavior) = OPTIONAL];
}

// Retrieve from Vertex RAG Store for grounding.
message VertexRagStore {
  // The definition of the Rag resource.
  message RagResource {
    // Optional. RagCorpora resource name.
    // Format:
    // `projects/{project}/locations/{location}/ragCorpora/{rag_corpus}`
    string rag_corpus = 1 [
      (google.api.field_behavior) = OPTIONAL,
      (google.api.resource_reference) = {
        type: "aiplatform.googleapis.com/RagCorpus"
      }
    ];

    // Optional. rag_file_id. The files should be in the same rag_corpus set in
    // rag_corpus field.
    repeated string rag_file_ids = 2 [(google.api.field_behavior) = OPTIONAL];
  }

  // Optional. The representation of the rag source. It can be used to specify
  // corpus only or ragfiles. Currently only support one corpus or multiple
  // files from one corpus. In the future we may open up multiple corpora
  // support.
  repeated RagResource rag_resources = 4
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. Number of top k results to return from the selected corpora.
  optional int32 similarity_top_k = 2
      [deprecated = true, (google.api.field_behavior) = OPTIONAL];

  // Optional. Only return results with vector distance smaller than the
  // threshold.
  optional double vector_distance_threshold = 3
      [deprecated = true, (google.api.field_behavior) = OPTIONAL];

  // Optional. The retrieval config for the Rag query.
  RagRetrievalConfig rag_retrieval_config = 6
      [(google.api.field_behavior) = OPTIONAL];
}

// Retrieve from Vertex AI Search datastore for grounding.
// See https://cloud.google.com/products/agent-builder
message VertexAISearch {
  // Required. Fully-qualified Vertex AI Search data store resource ID.
  // Format:
  // `projects/{project}/locations/{location}/collections/{collection}/dataStores/{dataStore}`
  string datastore = 1 [(google.api.field_behavior) = REQUIRED];
}

// Tool to retrieve public web data for grounding, powered by Google.
message GoogleSearchRetrieval {
  // Specifies the dynamic retrieval configuration for the given source.
  DynamicRetrievalConfig dynamic_retrieval_config = 2;
}

// Describes the options to customize dynamic retrieval.
message DynamicRetrievalConfig {
  // The mode of the predictor to be used in dynamic retrieval.
  enum Mode {
    // Always trigger retrieval.
    MODE_UNSPECIFIED = 0;

    // Run retrieval only when system decides it is necessary.
    MODE_DYNAMIC = 1;
  }

  // The mode of the predictor to be used in dynamic retrieval.
  Mode mode = 1;

  // Optional. The threshold to be used in dynamic retrieval.
  // If not set, a system default value is used.
  optional float dynamic_threshold = 2 [(google.api.field_behavior) = OPTIONAL];
}

// Tool config. This config is shared for all tools provided in the request.
message ToolConfig {
  // Optional. Function calling config.
  FunctionCallingConfig function_calling_config = 1
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. Retrieval config.
  RetrievalConfig retrieval_config = 2 [(google.api.field_behavior) = OPTIONAL];
}

// Function calling config.
message FunctionCallingConfig {
  // Function calling mode.
  enum Mode {
    // Unspecified function calling mode. This value should not be used.
    MODE_UNSPECIFIED = 0;

    // Default model behavior, model decides to predict either a function call
    // or a natural language response.
    AUTO = 1;

    // Model is constrained to always predicting a function call only.
    // If "allowed_function_names" are set, the predicted function call will be
    // limited to any one of "allowed_function_names", else the predicted
    // function call will be any one of the provided "function_declarations".
    ANY = 2;

    // Model will not predict any function call. Model behavior is same as when
    // not passing any function declarations.
    NONE = 3;
  }

  // Optional. Function calling mode.
  Mode mode = 1 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Function names to call. Only set when the Mode is ANY. Function
  // names should match [FunctionDeclaration.name]. With mode set to ANY, model
  // will predict a function call from the set of function names provided.
  repeated string allowed_function_names = 2
      [(google.api.field_behavior) = OPTIONAL];
}

// Retrieval config.
message RetrievalConfig {
  // The location of the user.
  optional google.type.LatLng lat_lng = 1;

  // The language code of the user.
  optional string language_code = 2;
}

// Specifies the context retrieval config.
message RagRetrievalConfig {
  // Config for filters.
  message Filter {
    // Filter contexts retrieved from the vector DB based on either vector
    // distance or vector similarity.
    oneof vector_db_threshold {
      // Optional. Only returns contexts with vector distance smaller than the
      // threshold.
      double vector_distance_threshold = 3
          [(google.api.field_behavior) = OPTIONAL];

      // Optional. Only returns contexts with vector similarity larger than the
      // threshold.
      double vector_similarity_threshold = 4
          [(google.api.field_behavior) = OPTIONAL];
    }

    // Optional. String for metadata filtering.
    string metadata_filter = 2 [(google.api.field_behavior) = OPTIONAL];
  }

  // Optional. The number of contexts to retrieve.
  int32 top_k = 1 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Config for filters.
  Filter filter = 3 [(google.api.field_behavior) = OPTIONAL];
}
