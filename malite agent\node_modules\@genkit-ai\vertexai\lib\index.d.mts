import { z, ModelReference, EmbedderReference } from 'genkit';
import { GenkitPlugin } from 'genkit/plugin';
import { P as PluginOptions, S as SUPPORTED_GEMINI_MODELS, G as GeminiConfigSchema } from './types-Bc0LKM8D.mjs';
export { n as GeminiConfig, o as GeminiVersionString, m as SafetySettingsSchema, g as gemini, a as gemini10Pro, b as gemini15Flash, c as gemini15Pro, d as gemini20Flash, e as gemini20Flash001, f as gemini20FlashLite, h as gemini20FlashLitePreview0205, i as gemini20ProExp0205, j as gemini25FlashPreview0417, k as gemini25ProExp0325, l as gemini25ProPreview0325 } from './types-Bc0LKM8D.mjs';
import { VertexEmbeddingConfig, VertexEmbeddingConfigSchema } from './embedder.mjs';
export { multimodalEmbedding001, textEmbedding004, textEmbedding005, textEmbeddingGecko003, textEmbeddingGeckoMultilingual001, textMultilingualEmbedding002 } from './embedder.mjs';
import { ACTUAL_IMAGEN_MODELS, ImagenConfigSchema } from './imagen.mjs';
export { imagen2, imagen3, imagen3Fast } from './imagen.mjs';
import 'google-auth-library';
import '@google-cloud/vertexai';
import 'genkit/model';
import 'genkit/embedder';

/**
 * @license
 *
 * Copyright 2024 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
/**
 * @module /
 */

type VertexAIPlugin = {
    (params?: PluginOptions): GenkitPlugin;
    model(name: keyof typeof SUPPORTED_GEMINI_MODELS | (`gemini-${string}` & {}), config?: z.infer<typeof GeminiConfigSchema>): ModelReference<typeof GeminiConfigSchema>;
    model(name: keyof typeof ACTUAL_IMAGEN_MODELS | (`imagen${string}` & {}), config?: z.infer<typeof ImagenConfigSchema>): ModelReference<typeof ImagenConfigSchema>;
    model(name: string, config?: any): ModelReference<z.ZodTypeAny>;
    embedder(name: string, config?: VertexEmbeddingConfig): EmbedderReference<typeof VertexEmbeddingConfigSchema>;
};
/**
 * Google Cloud Vertex AI plugin for Genkit.
 * Includes Gemini and Imagen models and text embedder.
 */
declare const vertexAI: VertexAIPlugin;

export { GeminiConfigSchema, ImagenConfigSchema, PluginOptions, type VertexAIPlugin, vertexAI as default, vertexAI };
