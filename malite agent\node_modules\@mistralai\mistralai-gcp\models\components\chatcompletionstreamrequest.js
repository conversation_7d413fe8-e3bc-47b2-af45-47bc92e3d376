"use strict";
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ChatCompletionStreamRequest$ = exports.ChatCompletionStreamRequest$outboundSchema = exports.ChatCompletionStreamRequest$inboundSchema = exports.ChatCompletionStreamRequestToolChoice$ = exports.ChatCompletionStreamRequestToolChoice$outboundSchema = exports.ChatCompletionStreamRequestToolChoice$inboundSchema = exports.Messages$ = exports.Messages$outboundSchema = exports.Messages$inboundSchema = exports.Stop$ = exports.Stop$outboundSchema = exports.Stop$inboundSchema = void 0;
exports.stopToJSON = stopToJSON;
exports.stopFromJSON = stopFromJSON;
exports.messagesToJSON = messagesToJSON;
exports.messagesFromJSON = messagesFromJSON;
exports.chatCompletionStreamRequestToolChoiceToJSON = chatCompletionStreamRequestToolChoiceToJSON;
exports.chatCompletionStreamRequestToolChoiceFromJSON = chatCompletionStreamRequestToolChoiceFromJSON;
exports.chatCompletionStreamRequestToJSON = chatCompletionStreamRequestToJSON;
exports.chatCompletionStreamRequestFromJSON = chatCompletionStreamRequestFromJSON;
const z = __importStar(require("zod"));
const primitives_js_1 = require("../../lib/primitives.js");
const schemas_js_1 = require("../../lib/schemas.js");
const assistantmessage_js_1 = require("./assistantmessage.js");
const prediction_js_1 = require("./prediction.js");
const responseformat_js_1 = require("./responseformat.js");
const systemmessage_js_1 = require("./systemmessage.js");
const tool_js_1 = require("./tool.js");
const toolchoice_js_1 = require("./toolchoice.js");
const toolchoiceenum_js_1 = require("./toolchoiceenum.js");
const toolmessage_js_1 = require("./toolmessage.js");
const usermessage_js_1 = require("./usermessage.js");
/** @internal */
exports.Stop$inboundSchema = z
    .union([z.string(), z.array(z.string())]);
/** @internal */
exports.Stop$outboundSchema = z.union([z.string(), z.array(z.string())]);
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var Stop$;
(function (Stop$) {
    /** @deprecated use `Stop$inboundSchema` instead. */
    Stop$.inboundSchema = exports.Stop$inboundSchema;
    /** @deprecated use `Stop$outboundSchema` instead. */
    Stop$.outboundSchema = exports.Stop$outboundSchema;
})(Stop$ || (exports.Stop$ = Stop$ = {}));
function stopToJSON(stop) {
    return JSON.stringify(exports.Stop$outboundSchema.parse(stop));
}
function stopFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.Stop$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'Stop' from JSON`);
}
/** @internal */
exports.Messages$inboundSchema = z.union([
    systemmessage_js_1.SystemMessage$inboundSchema.and(z.object({ role: z.literal("system") }).transform((v) => ({
        role: v.role,
    }))),
    usermessage_js_1.UserMessage$inboundSchema.and(z.object({ role: z.literal("user") }).transform((v) => ({ role: v.role }))),
    assistantmessage_js_1.AssistantMessage$inboundSchema.and(z.object({ role: z.literal("assistant") }).transform((v) => ({
        role: v.role,
    }))),
    toolmessage_js_1.ToolMessage$inboundSchema.and(z.object({ role: z.literal("tool") }).transform((v) => ({ role: v.role }))),
]);
/** @internal */
exports.Messages$outboundSchema = z.union([
    systemmessage_js_1.SystemMessage$outboundSchema.and(z.object({ role: z.literal("system") }).transform((v) => ({
        role: v.role,
    }))),
    usermessage_js_1.UserMessage$outboundSchema.and(z.object({ role: z.literal("user") }).transform((v) => ({ role: v.role }))),
    assistantmessage_js_1.AssistantMessage$outboundSchema.and(z.object({ role: z.literal("assistant") }).transform((v) => ({
        role: v.role,
    }))),
    toolmessage_js_1.ToolMessage$outboundSchema.and(z.object({ role: z.literal("tool") }).transform((v) => ({ role: v.role }))),
]);
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var Messages$;
(function (Messages$) {
    /** @deprecated use `Messages$inboundSchema` instead. */
    Messages$.inboundSchema = exports.Messages$inboundSchema;
    /** @deprecated use `Messages$outboundSchema` instead. */
    Messages$.outboundSchema = exports.Messages$outboundSchema;
})(Messages$ || (exports.Messages$ = Messages$ = {}));
function messagesToJSON(messages) {
    return JSON.stringify(exports.Messages$outboundSchema.parse(messages));
}
function messagesFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.Messages$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'Messages' from JSON`);
}
/** @internal */
exports.ChatCompletionStreamRequestToolChoice$inboundSchema = z.union([toolchoice_js_1.ToolChoice$inboundSchema, toolchoiceenum_js_1.ToolChoiceEnum$inboundSchema]);
/** @internal */
exports.ChatCompletionStreamRequestToolChoice$outboundSchema = z.union([toolchoice_js_1.ToolChoice$outboundSchema, toolchoiceenum_js_1.ToolChoiceEnum$outboundSchema]);
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var ChatCompletionStreamRequestToolChoice$;
(function (ChatCompletionStreamRequestToolChoice$) {
    /** @deprecated use `ChatCompletionStreamRequestToolChoice$inboundSchema` instead. */
    ChatCompletionStreamRequestToolChoice$.inboundSchema = exports.ChatCompletionStreamRequestToolChoice$inboundSchema;
    /** @deprecated use `ChatCompletionStreamRequestToolChoice$outboundSchema` instead. */
    ChatCompletionStreamRequestToolChoice$.outboundSchema = exports.ChatCompletionStreamRequestToolChoice$outboundSchema;
})(ChatCompletionStreamRequestToolChoice$ || (exports.ChatCompletionStreamRequestToolChoice$ = ChatCompletionStreamRequestToolChoice$ = {}));
function chatCompletionStreamRequestToolChoiceToJSON(chatCompletionStreamRequestToolChoice) {
    return JSON.stringify(exports.ChatCompletionStreamRequestToolChoice$outboundSchema.parse(chatCompletionStreamRequestToolChoice));
}
function chatCompletionStreamRequestToolChoiceFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.ChatCompletionStreamRequestToolChoice$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'ChatCompletionStreamRequestToolChoice' from JSON`);
}
/** @internal */
exports.ChatCompletionStreamRequest$inboundSchema = z.object({
    model: z.string(),
    temperature: z.nullable(z.number()).optional(),
    top_p: z.number().optional(),
    max_tokens: z.nullable(z.number().int()).optional(),
    stream: z.boolean().default(true),
    stop: z.union([z.string(), z.array(z.string())]).optional(),
    random_seed: z.nullable(z.number().int()).optional(),
    messages: z.array(z.union([
        systemmessage_js_1.SystemMessage$inboundSchema.and(z.object({ role: z.literal("system") }).transform((v) => ({
            role: v.role,
        }))),
        usermessage_js_1.UserMessage$inboundSchema.and(z.object({ role: z.literal("user") }).transform((v) => ({
            role: v.role,
        }))),
        assistantmessage_js_1.AssistantMessage$inboundSchema.and(z.object({ role: z.literal("assistant") }).transform((v) => ({
            role: v.role,
        }))),
        toolmessage_js_1.ToolMessage$inboundSchema.and(z.object({ role: z.literal("tool") }).transform((v) => ({
            role: v.role,
        }))),
    ])),
    response_format: responseformat_js_1.ResponseFormat$inboundSchema.optional(),
    tools: z.nullable(z.array(tool_js_1.Tool$inboundSchema)).optional(),
    tool_choice: z.union([toolchoice_js_1.ToolChoice$inboundSchema, toolchoiceenum_js_1.ToolChoiceEnum$inboundSchema])
        .optional(),
    presence_penalty: z.number().optional(),
    frequency_penalty: z.number().optional(),
    n: z.nullable(z.number().int()).optional(),
    prediction: prediction_js_1.Prediction$inboundSchema.optional(),
    parallel_tool_calls: z.boolean().optional(),
}).transform((v) => {
    return (0, primitives_js_1.remap)(v, {
        "top_p": "topP",
        "max_tokens": "maxTokens",
        "random_seed": "randomSeed",
        "response_format": "responseFormat",
        "tool_choice": "toolChoice",
        "presence_penalty": "presencePenalty",
        "frequency_penalty": "frequencyPenalty",
        "parallel_tool_calls": "parallelToolCalls",
    });
});
/** @internal */
exports.ChatCompletionStreamRequest$outboundSchema = z.object({
    model: z.string(),
    temperature: z.nullable(z.number()).optional(),
    topP: z.number().optional(),
    maxTokens: z.nullable(z.number().int()).optional(),
    stream: z.boolean().default(true),
    stop: z.union([z.string(), z.array(z.string())]).optional(),
    randomSeed: z.nullable(z.number().int()).optional(),
    messages: z.array(z.union([
        systemmessage_js_1.SystemMessage$outboundSchema.and(z.object({ role: z.literal("system") }).transform((v) => ({
            role: v.role,
        }))),
        usermessage_js_1.UserMessage$outboundSchema.and(z.object({ role: z.literal("user") }).transform((v) => ({
            role: v.role,
        }))),
        assistantmessage_js_1.AssistantMessage$outboundSchema.and(z.object({ role: z.literal("assistant") }).transform((v) => ({
            role: v.role,
        }))),
        toolmessage_js_1.ToolMessage$outboundSchema.and(z.object({ role: z.literal("tool") }).transform((v) => ({
            role: v.role,
        }))),
    ])),
    responseFormat: responseformat_js_1.ResponseFormat$outboundSchema.optional(),
    tools: z.nullable(z.array(tool_js_1.Tool$outboundSchema)).optional(),
    toolChoice: z.union([
        toolchoice_js_1.ToolChoice$outboundSchema,
        toolchoiceenum_js_1.ToolChoiceEnum$outboundSchema,
    ]).optional(),
    presencePenalty: z.number().optional(),
    frequencyPenalty: z.number().optional(),
    n: z.nullable(z.number().int()).optional(),
    prediction: prediction_js_1.Prediction$outboundSchema.optional(),
    parallelToolCalls: z.boolean().optional(),
}).transform((v) => {
    return (0, primitives_js_1.remap)(v, {
        topP: "top_p",
        maxTokens: "max_tokens",
        randomSeed: "random_seed",
        responseFormat: "response_format",
        toolChoice: "tool_choice",
        presencePenalty: "presence_penalty",
        frequencyPenalty: "frequency_penalty",
        parallelToolCalls: "parallel_tool_calls",
    });
});
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var ChatCompletionStreamRequest$;
(function (ChatCompletionStreamRequest$) {
    /** @deprecated use `ChatCompletionStreamRequest$inboundSchema` instead. */
    ChatCompletionStreamRequest$.inboundSchema = exports.ChatCompletionStreamRequest$inboundSchema;
    /** @deprecated use `ChatCompletionStreamRequest$outboundSchema` instead. */
    ChatCompletionStreamRequest$.outboundSchema = exports.ChatCompletionStreamRequest$outboundSchema;
})(ChatCompletionStreamRequest$ || (exports.ChatCompletionStreamRequest$ = ChatCompletionStreamRequest$ = {}));
function chatCompletionStreamRequestToJSON(chatCompletionStreamRequest) {
    return JSON.stringify(exports.ChatCompletionStreamRequest$outboundSchema.parse(chatCompletionStreamRequest));
}
function chatCompletionStreamRequestFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.ChatCompletionStreamRequest$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'ChatCompletionStreamRequest' from JSON`);
}
//# sourceMappingURL=chatcompletionstreamrequest.js.map