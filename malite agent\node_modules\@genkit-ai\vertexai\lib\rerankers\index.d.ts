import { GenkitPlugin } from 'genkit/plugin';
import { C as CommonPluginOptions } from '../types-Bc0LKM8D.js';
import { RerankerOptions } from './types.js';
import 'genkit';
import 'google-auth-library';
import '@google-cloud/vertexai';
import 'genkit/model';

/**
 * Copyright 2024 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

interface PluginOptions extends CommonPluginOptions, RerankerOptions {
}
/**
 * Add Google Cloud Vertex AI Rerankers API to Genkit.
 */
declare function vertexAIRerankers(options: PluginOptions): GenkitPlugin;

export { type PluginOptions, vertexAIRerankers };
