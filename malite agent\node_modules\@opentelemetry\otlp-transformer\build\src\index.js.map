{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/index.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAYH,mCAUkB;AARhB,oGAAA,UAAU,OAAA;AAEV,wGAAA,cAAc,OAAA;AAGd,0GAAA,gBAAgB,OAAA;AAChB,wGAAA,cAAc,OAAA;AACd,uGAAA,aAAa,OAAA;AAwBf,uCAYuB;AAHrB,kGAAA,SAAS,OAAA;AAcX,iCAA0D;AAAjD,wHAAA,+BAA+B,OAAA;AACxC,qCAA8D;AAArD,4HAAA,iCAAiC,OAAA;AAC1C,+BAAwD;AAA/C,sHAAA,8BAA8B,OAAA;AAEvC,sDAIgC;AAH9B,qHAAA,sBAAsB,OAAA;AACtB,wHAAA,yBAAyB,OAAA;AACzB,sHAAA,uBAAuB,OAAA;AAGzB,kDAI4B;AAH1B,kHAAA,mBAAmB,OAAA;AACnB,iHAAA,kBAAkB,OAAA;AAClB,oHAAA,qBAAqB,OAAA", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport {\n  OtlpEncodingOptions,\n  IKeyValueList,\n  IKeyValue,\n  IInstrumentationScope,\n  IArrayValue,\n  LongBits,\n  IAnyValue,\n  Fixed64,\n} from './common/types';\nexport {\n  SpanContextEncodeFunction,\n  toLongBits,\n  OptionalSpanContextEncodeFunction,\n  getOtlpEncoder,\n  Encoder,\n  HrTimeEncodeFunction,\n  encodeAsLongBits,\n  encodeAsString,\n  hrTimeToNanos,\n} from './common';\nexport {\n  IExportMetricsPartialSuccess,\n  IValueAtQuantile,\n  ISummaryDataPoint,\n  ISummary,\n  ISum,\n  IScopeMetrics,\n  IResourceMetrics,\n  INumberDataPoint,\n  IHistogramDataPoint,\n  IHistogram,\n  IExponentialHistogramDataPoint,\n  IExponentialHistogram,\n  IMetric,\n  IGauge,\n  IExemplar,\n  EAggregationTemporality,\n  IExportMetricsServiceRequest,\n  IExportMetricsServiceResponse,\n  IBuckets,\n} from './metrics/types';\nexport { IResource } from './resource/types';\nexport {\n  IExportTracePartialSuccess,\n  IStatus,\n  EStatusCode,\n  ILink,\n  IEvent,\n  IScopeSpans,\n  ISpan,\n  IResourceSpans,\n  ESpanKind,\n  IExportTraceServiceResponse,\n  IExportTraceServiceRequest,\n} from './trace/types';\nexport {\n  IExportLogsServiceResponse,\n  IScopeLogs,\n  IExportLogsServiceRequest,\n  IResourceLogs,\n  ILogRecord,\n  IExportLogsPartialSuccess,\n  ESeverityNumber,\n} from './logs/types';\n\nexport { createExportTraceServiceRequest } from './trace';\nexport { createExportMetricsServiceRequest } from './metrics';\nexport { createExportLogsServiceRequest } from './logs';\n\nexport {\n  ProtobufLogsSerializer,\n  ProtobufMetricsSerializer,\n  ProtobufTraceSerializer,\n} from './protobuf/serializers';\n\nexport {\n  JsonTraceSerializer,\n  JsonLogsSerializer,\n  JsonMetricsSerializer,\n} from './json/serializers';\n\nexport { ISerializer } from './common/i-serializer';\n"]}