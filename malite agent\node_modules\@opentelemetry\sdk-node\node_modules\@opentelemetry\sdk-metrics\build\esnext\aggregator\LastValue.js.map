{"version": 3, "file": "LastValue.js", "sourceRoot": "", "sources": ["../../../src/aggregator/LastValue.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAEH,OAAO,EAIL,cAAc,GAEf,MAAM,SAAS,CAAC;AAEjB,OAAO,EAAE,cAAc,EAAE,oBAAoB,EAAE,MAAM,qBAAqB,CAAC;AAC3E,OAAO,EACL,aAAa,GAGd,MAAM,sBAAsB,CAAC;AAI9B,MAAM,OAAO,qBAAqB;IAChC,YACS,SAAiB,EAChB,WAAmB,CAAC,EACrB,aAAqB,CAAC,CAAC,EAAE,CAAC,CAAC;QAF3B,cAAS,GAAT,SAAS,CAAQ;QAChB,aAAQ,GAAR,QAAQ,CAAY;QACrB,eAAU,GAAV,UAAU,CAAiB;IACjC,CAAC;IAEJ,MAAM,CAAC,KAAa;QAClB,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;QACtB,IAAI,CAAC,UAAU,GAAG,cAAc,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC;IAC/C,CAAC;IAED,YAAY,CAAC,SAAiB;QAC5B,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;IAC7B,CAAC;IAED,YAAY;QACV,OAAO,IAAI,CAAC,QAAQ,CAAC;IACvB,CAAC;CACF;AAED,kFAAkF;AAClF,MAAM,OAAO,mBAAmB;IAAhC;QACS,SAAI,GAA8B,cAAc,CAAC,UAAU,CAAC;IAuErE,CAAC;IArEC,kBAAkB,CAAC,SAAiB;QAClC,OAAO,IAAI,qBAAqB,CAAC,SAAS,CAAC,CAAC;IAC9C,CAAC;IAED;;;;OAIG;IACH,KAAK,CACH,QAA+B,EAC/B,KAA4B;QAE5B,mCAAmC;QACnC,MAAM,kBAAkB,GACtB,oBAAoB,CAAC,KAAK,CAAC,UAAU,CAAC;YACtC,oBAAoB,CAAC,QAAQ,CAAC,UAAU,CAAC;YACvC,CAAC,CAAC,KAAK;YACP,CAAC,CAAC,QAAQ,CAAC;QACf,OAAO,IAAI,qBAAqB,CAC9B,QAAQ,CAAC,SAAS,EAClB,kBAAkB,CAAC,YAAY,EAAE,EACjC,kBAAkB,CAAC,UAAU,CAC9B,CAAC;IACJ,CAAC;IAED;;;;;OAKG;IACH,IAAI,CACF,QAA+B,EAC/B,OAA8B;QAE9B,mCAAmC;QACnC,MAAM,kBAAkB,GACtB,oBAAoB,CAAC,OAAO,CAAC,UAAU,CAAC;YACxC,oBAAoB,CAAC,QAAQ,CAAC,UAAU,CAAC;YACvC,CAAC,CAAC,OAAO;YACT,CAAC,CAAC,QAAQ,CAAC;QACf,OAAO,IAAI,qBAAqB,CAC9B,OAAO,CAAC,SAAS,EACjB,kBAAkB,CAAC,YAAY,EAAE,EACjC,kBAAkB,CAAC,UAAU,CAC9B,CAAC;IACJ,CAAC;IAED,YAAY,CACV,UAA4B,EAC5B,sBAA8C,EAC9C,wBAAqE,EACrE,OAAe;QAEf,OAAO;YACL,UAAU;YACV,sBAAsB;YACtB,aAAa,EAAE,aAAa,CAAC,KAAK;YAClC,UAAU,EAAE,wBAAwB,CAAC,GAAG,CAAC,CAAC,CAAC,UAAU,EAAE,YAAY,CAAC,EAAE,EAAE;gBACtE,OAAO;oBACL,UAAU;oBACV,SAAS,EAAE,YAAY,CAAC,SAAS;oBACjC,OAAO;oBACP,KAAK,EAAE,YAAY,CAAC,YAAY,EAAE;iBACnC,CAAC;YACJ,CAAC,CAAC;SACH,CAAC;IACJ,CAAC;CACF", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  Accumulation,\n  AccumulationRecord,\n  Aggregator,\n  AggregatorKind,\n  LastValue,\n} from './types';\nimport { HrTime } from '@opentelemetry/api';\nimport { millisToHrTime, hrTimeToMicroseconds } from '@opentelemetry/core';\nimport {\n  DataPointType,\n  GaugeMetricData,\n  MetricDescriptor,\n} from '../export/MetricData';\nimport { Maybe } from '../utils';\nimport { AggregationTemporality } from '../export/AggregationTemporality';\n\nexport class LastValueAccumulation implements Accumulation {\n  constructor(\n    public startTime: HrTime,\n    private _current: number = 0,\n    public sampleTime: HrTime = [0, 0]\n  ) {}\n\n  record(value: number): void {\n    this._current = value;\n    this.sampleTime = millisToHrTime(Date.now());\n  }\n\n  setStartTime(startTime: HrTime): void {\n    this.startTime = startTime;\n  }\n\n  toPointValue(): LastValue {\n    return this._current;\n  }\n}\n\n/** Basic aggregator which calculates a LastValue from individual measurements. */\nexport class LastValueAggregator implements Aggregator<LastValueAccumulation> {\n  public kind: AggregatorKind.LAST_VALUE = AggregatorKind.LAST_VALUE;\n\n  createAccumulation(startTime: HrTime) {\n    return new LastValueAccumulation(startTime);\n  }\n\n  /**\n   * Returns the result of the merge of the given accumulations.\n   *\n   * Return the newly captured (delta) accumulation for LastValueAggregator.\n   */\n  merge(\n    previous: LastValueAccumulation,\n    delta: LastValueAccumulation\n  ): LastValueAccumulation {\n    // nanoseconds may lose precisions.\n    const latestAccumulation =\n      hrTimeToMicroseconds(delta.sampleTime) >=\n      hrTimeToMicroseconds(previous.sampleTime)\n        ? delta\n        : previous;\n    return new LastValueAccumulation(\n      previous.startTime,\n      latestAccumulation.toPointValue(),\n      latestAccumulation.sampleTime\n    );\n  }\n\n  /**\n   * Returns a new DELTA aggregation by comparing two cumulative measurements.\n   *\n   * A delta aggregation is not meaningful to LastValueAggregator, just return\n   * the newly captured (delta) accumulation for LastValueAggregator.\n   */\n  diff(\n    previous: LastValueAccumulation,\n    current: LastValueAccumulation\n  ): LastValueAccumulation {\n    // nanoseconds may lose precisions.\n    const latestAccumulation =\n      hrTimeToMicroseconds(current.sampleTime) >=\n      hrTimeToMicroseconds(previous.sampleTime)\n        ? current\n        : previous;\n    return new LastValueAccumulation(\n      current.startTime,\n      latestAccumulation.toPointValue(),\n      latestAccumulation.sampleTime\n    );\n  }\n\n  toMetricData(\n    descriptor: MetricDescriptor,\n    aggregationTemporality: AggregationTemporality,\n    accumulationByAttributes: AccumulationRecord<LastValueAccumulation>[],\n    endTime: HrTime\n  ): Maybe<GaugeMetricData> {\n    return {\n      descriptor,\n      aggregationTemporality,\n      dataPointType: DataPointType.GAUGE,\n      dataPoints: accumulationByAttributes.map(([attributes, accumulation]) => {\n        return {\n          attributes,\n          startTime: accumulation.startTime,\n          endTime,\n          value: accumulation.toPointValue(),\n        };\n      }),\n    };\n  }\n}\n"]}