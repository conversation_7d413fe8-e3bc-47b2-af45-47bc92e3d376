{"version": 3, "file": "OTLPTraceExporter.js", "sourceRoot": "", "sources": ["../../../../src/platform/node/OTLPTraceExporter.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAGH,OAAO,EAAE,MAAM,EAAE,YAAY,EAAE,MAAM,qBAAqB,CAAC;AAC3D,OAAO,EAEL,uBAAuB,EACvB,2BAA2B,EAC3B,oBAAoB,EACpB,YAAY,GACb,MAAM,mCAAmC,CAAC;AAC3C,OAAO,EAEL,uBAAuB,GACxB,MAAM,iCAAiC,CAAC;AACzC,OAAO,EAAE,OAAO,EAAE,MAAM,eAAe,CAAC;AAExC,MAAM,+BAA+B,GAAG,WAAW,CAAC;AACpD,MAAM,qBAAqB,GAAG,yBAAyB,+BAA+B,EAAE,CAAC;AACzF,MAAM,UAAU,GAAG;IACjB,YAAY,EAAE,iCAAiC,OAAO,EAAE;CACzD,CAAC;AAEF;;GAEG;AACH,MAAM,OAAO,iBACX,SAAQ,oBAA+D;IAGvE,YAAY,SAAqC,EAAE;QACjD,KAAK,CAAC,MAAM,EAAE,uBAAuB,EAAE,wBAAwB,CAAC,CAAC;QACjE,IAAI,CAAC,OAAO,+DACP,IAAI,CAAC,OAAO,GACZ,UAAU,GACV,YAAY,CAAC,uBAAuB,CACrC,MAAM,EAAE,CAAC,iCAAiC,CAC3C,GACE,YAAY,CAAC,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,OAAO,CAAC,CACjC,CAAC;IACJ,CAAC;IAED,aAAa,CAAC,MAAkC;QAC9C,OAAO,OAAO,MAAM,CAAC,GAAG,KAAK,QAAQ;YACnC,CAAC,CAAC,MAAM,CAAC,GAAG;YACZ,CAAC,CAAC,MAAM,EAAE,CAAC,kCAAkC,CAAC,MAAM,GAAG,CAAC;gBACxD,CAAC,CAAC,2BAA2B,CAAC,MAAM,EAAE,CAAC,kCAAkC,CAAC;gBAC1E,CAAC,CAAC,MAAM,EAAE,CAAC,2BAA2B,CAAC,MAAM,GAAG,CAAC;oBACjD,CAAC,CAAC,uBAAuB,CACrB,MAAM,EAAE,CAAC,2BAA2B,EACpC,+BAA+B,CAChC;oBACH,CAAC,CAAC,qBAAqB,CAAC;IAC5B,CAAC;CACF", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ReadableSpan, SpanExporter } from '@opentelemetry/sdk-trace-base';\nimport { getEnv, baggageUtils } from '@opentelemetry/core';\nimport {\n  OTLPExporterNodeConfigBase,\n  appendResourcePathToUrl,\n  appendRootPathToUrlIfNeeded,\n  OTLPExporterNodeBase,\n  parseHeaders,\n} from '@opentelemetry/otlp-exporter-base';\nimport {\n  IExportTraceServiceResponse,\n  ProtobufTraceSerializer,\n} from '@opentelemetry/otlp-transformer';\nimport { VERSION } from '../../version';\n\nconst DEFAULT_COLLECTOR_RESOURCE_PATH = 'v1/traces';\nconst DEFAULT_COLLECTOR_URL = `http://localhost:4318/${DEFAULT_COLLECTOR_RESOURCE_PATH}`;\nconst USER_AGENT = {\n  'User-Agent': `OTel-OTLP-Exporter-JavaScript/${VERSION}`,\n};\n\n/**\n * Collector Trace Exporter for Node with protobuf\n */\nexport class OTLPTraceExporter\n  extends OTLPExporterNodeBase<ReadableSpan, IExportTraceServiceResponse>\n  implements SpanExporter\n{\n  constructor(config: OTLPExporterNodeConfigBase = {}) {\n    super(config, ProtobufTraceSerializer, 'application/x-protobuf');\n    this.headers = {\n      ...this.headers,\n      ...USER_AGENT,\n      ...baggageUtils.parseKeyPairsIntoRecord(\n        getEnv().OTEL_EXPORTER_OTLP_TRACES_HEADERS\n      ),\n      ...parseHeaders(config?.headers),\n    };\n  }\n\n  getDefaultUrl(config: OTLPExporterNodeConfigBase) {\n    return typeof config.url === 'string'\n      ? config.url\n      : getEnv().OTEL_EXPORTER_OTLP_TRACES_ENDPOINT.length > 0\n      ? appendRootPathToUrlIfNeeded(getEnv().OTEL_EXPORTER_OTLP_TRACES_ENDPOINT)\n      : getEnv().OTEL_EXPORTER_OTLP_ENDPOINT.length > 0\n      ? appendResourcePathToUrl(\n          getEnv().OTEL_EXPORTER_OTLP_ENDPOINT,\n          DEFAULT_COLLECTOR_RESOURCE_PATH\n        )\n      : DEFAULT_COLLECTOR_URL;\n  }\n}\n"]}