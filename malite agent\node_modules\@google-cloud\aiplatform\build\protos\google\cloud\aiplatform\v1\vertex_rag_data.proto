// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.aiplatform.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/aiplatform/v1/api_auth.proto";
import "google/cloud/aiplatform/v1/io.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.AIPlatform.V1";
option go_package = "cloud.google.com/go/aiplatform/apiv1/aiplatformpb;aiplatformpb";
option java_multiple_files = true;
option java_outer_classname = "VertexRagDataProto";
option java_package = "com.google.cloud.aiplatform.v1";
option php_namespace = "Google\\Cloud\\AIPlatform\\V1";
option ruby_package = "Google::Cloud::AIPlatform::V1";

// Config for the embedding model to use for RAG.
message RagEmbeddingModelConfig {
  // Config representing a model hosted on Vertex Prediction Endpoint.
  message VertexPredictionEndpoint {
    // Required. The endpoint resource name.
    // Format:
    // `projects/{project}/locations/{location}/publishers/{publisher}/models/{model}`
    // or
    // `projects/{project}/locations/{location}/endpoints/{endpoint}`
    string endpoint = 1 [
      (google.api.field_behavior) = REQUIRED,
      (google.api.resource_reference) = {
        type: "aiplatform.googleapis.com/Endpoint"
      }
    ];

    // Output only. The resource name of the model that is deployed on the
    // endpoint. Present only when the endpoint is not a publisher model.
    // Pattern:
    // `projects/{project}/locations/{location}/models/{model}`
    string model = 2 [
      (google.api.field_behavior) = OUTPUT_ONLY,
      (google.api.resource_reference) = {
        type: "aiplatform.googleapis.com/Model"
      }
    ];

    // Output only. Version ID of the model that is deployed on the endpoint.
    // Present only when the endpoint is not a publisher model.
    string model_version_id = 3 [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // The model config to use.
  oneof model_config {
    // The Vertex AI Prediction Endpoint that either refers to a publisher model
    // or an endpoint that is hosting a 1P fine-tuned text embedding model.
    // Endpoints hosting non-1P fine-tuned text embedding models are
    // currently not supported.
    // This is used for dense vector search.
    VertexPredictionEndpoint vertex_prediction_endpoint = 1;
  }
}

// Config for the Vector DB to use for RAG.
message RagVectorDbConfig {
  // The config for the default RAG-managed Vector DB.
  message RagManagedDb {}

  // The config for the Pinecone.
  message Pinecone {
    // Pinecone index name.
    // This value cannot be changed after it's set.
    string index_name = 1;
  }

  // The config for the Vertex Vector Search.
  message VertexVectorSearch {
    // The resource name of the Index Endpoint.
    // Format:
    // `projects/{project}/locations/{location}/indexEndpoints/{index_endpoint}`
    string index_endpoint = 1;

    // The resource name of the Index.
    // Format:
    // `projects/{project}/locations/{location}/indexes/{index}`
    string index = 2;
  }

  // The config for the Vector DB.
  oneof vector_db {
    // The config for the RAG-managed Vector DB.
    RagManagedDb rag_managed_db = 1;

    // The config for the Pinecone.
    Pinecone pinecone = 3;

    // The config for the Vertex Vector Search.
    VertexVectorSearch vertex_vector_search = 6;
  }

  // Authentication config for the chosen Vector DB.
  ApiAuth api_auth = 5;

  // Optional. Immutable. The embedding model config of the Vector DB.
  RagEmbeddingModelConfig rag_embedding_model_config = 7 [
    (google.api.field_behavior) = OPTIONAL,
    (google.api.field_behavior) = IMMUTABLE
  ];
}

// RagFile status.
message FileStatus {
  // RagFile state.
  enum State {
    // RagFile state is unspecified.
    STATE_UNSPECIFIED = 0;

    // RagFile resource has been created and indexed successfully.
    ACTIVE = 1;

    // RagFile resource is in a problematic state.
    // See `error_message` field for details.
    ERROR = 2;
  }

  // Output only. RagFile state.
  State state = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Only when the `state` field is ERROR.
  string error_status = 2 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// RagCorpus status.
message CorpusStatus {
  // RagCorpus life state.
  enum State {
    // This state is not supposed to happen.
    UNKNOWN = 0;

    // RagCorpus resource entry is initialized, but hasn't done validation.
    INITIALIZED = 1;

    // RagCorpus is provisioned successfully and is ready to serve.
    ACTIVE = 2;

    // RagCorpus is in a problematic situation.
    // See `error_message` field for details.
    ERROR = 3;
  }

  // Output only. RagCorpus life state.
  State state = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Only when the `state` field is ERROR.
  string error_status = 2 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// A RagCorpus is a RagFile container and a project can have multiple
// RagCorpora.
message RagCorpus {
  option (google.api.resource) = {
    type: "aiplatform.googleapis.com/RagCorpus"
    pattern: "projects/{project}/locations/{location}/ragCorpora/{rag_corpus}"
    plural: "ragCorpora"
    singular: "ragCorpus"
  };

  // Output only. The resource name of the RagCorpus.
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. The display name of the RagCorpus.
  // The name can be up to 128 characters long and can consist of any UTF-8
  // characters.
  string display_name = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. The description of the RagCorpus.
  string description = 3 [(google.api.field_behavior) = OPTIONAL];

  // Output only. Timestamp when this RagCorpus was created.
  google.protobuf.Timestamp create_time = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Timestamp when this RagCorpus was last updated.
  google.protobuf.Timestamp update_time = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. RagCorpus state.
  CorpusStatus corpus_status = 8 [(google.api.field_behavior) = OUTPUT_ONLY];

  // The backend config of the RagCorpus.
  // It can be data store and/or retrieval engine.
  oneof backend_config {
    // Optional. Immutable. The config for the Vector DBs.
    RagVectorDbConfig vector_db_config = 9 [
      (google.api.field_behavior) = OPTIONAL,
      (google.api.field_behavior) = IMMUTABLE
    ];
  }
}

// A RagFile contains user data for chunking, embedding and indexing.
message RagFile {
  option (google.api.resource) = {
    type: "aiplatform.googleapis.com/RagFile"
    pattern: "projects/{project}/locations/{location}/ragCorpora/{rag_corpus}/ragFiles/{rag_file}"
    plural: "ragFiles"
    singular: "ragFile"
  };

  // The origin location of the RagFile if it is imported from Google Cloud
  // Storage or Google Drive.
  oneof rag_file_source {
    // Output only. Google Cloud Storage location of the RagFile.
    // It does not support wildcards in the Cloud Storage uri for now.
    GcsSource gcs_source = 8 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Google Drive location. Supports importing individual files
    // as well as Google Drive folders.
    GoogleDriveSource google_drive_source = 9
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The RagFile is encapsulated and uploaded in the
    // UploadRagFile request.
    DirectUploadSource direct_upload_source = 10
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // The RagFile is imported from a Slack channel.
    SlackSource slack_source = 11;

    // The RagFile is imported from a Jira query.
    JiraSource jira_source = 12;

    // The RagFile is imported from a SharePoint source.
    SharePointSources share_point_sources = 14;
  }

  // Output only. The resource name of the RagFile.
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. The display name of the RagFile.
  // The name can be up to 128 characters long and can consist of any UTF-8
  // characters.
  string display_name = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. The description of the RagFile.
  string description = 3 [(google.api.field_behavior) = OPTIONAL];

  // Output only. Timestamp when this RagFile was created.
  google.protobuf.Timestamp create_time = 6
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Timestamp when this RagFile was last updated.
  google.protobuf.Timestamp update_time = 7
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. State of the RagFile.
  FileStatus file_status = 13 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Specifies the size and overlap of chunks for RagFiles.
message RagFileChunkingConfig {
  // Specifies the fixed length chunking config.
  message FixedLengthChunking {
    // The size of the chunks.
    int32 chunk_size = 1;

    // The overlap between chunks.
    int32 chunk_overlap = 2;
  }

  // Specifies the chunking config for RagFiles.
  oneof chunking_config {
    // Specifies the fixed length chunking config.
    FixedLengthChunking fixed_length_chunking = 3;
  }
}

// Specifies the transformation config for RagFiles.
message RagFileTransformationConfig {
  // Specifies the chunking config for RagFiles.
  RagFileChunkingConfig rag_file_chunking_config = 1;
}

// Config for uploading RagFile.
message UploadRagFileConfig {
  // Specifies the transformation config for RagFiles.
  RagFileTransformationConfig rag_file_transformation_config = 3;
}

// Config for importing RagFiles.
message ImportRagFilesConfig {
  // The source of the import.
  oneof import_source {
    // Google Cloud Storage location. Supports importing individual files as
    // well as entire Google Cloud Storage directories. Sample formats:
    // - `gs://bucket_name/my_directory/object_name/my_file.txt`
    // - `gs://bucket_name/my_directory`
    GcsSource gcs_source = 2;

    // Google Drive location. Supports importing individual files as
    // well as Google Drive folders.
    GoogleDriveSource google_drive_source = 3;

    // Slack channels with their corresponding access tokens.
    SlackSource slack_source = 6;

    // Jira queries with their corresponding authentication.
    JiraSource jira_source = 7;

    // SharePoint sources.
    SharePointSources share_point_sources = 13;
  }

  // Optional. If provided, all partial failures are written to the sink.
  // Deprecated. Prefer to use the `import_result_sink`.
  oneof partial_failure_sink {
    // The Cloud Storage path to write partial failures to.
    // Deprecated. Prefer to use `import_result_gcs_sink`.
    GcsDestination partial_failure_gcs_sink = 11 [deprecated = true];

    // The BigQuery destination to write partial failures to. It should be a
    // bigquery table resource name (e.g.
    // "bq://projectId.bqDatasetId.bqTableId"). The dataset must exist. If the
    // table does not exist, it will be created with the expected schema. If the
    // table exists, the schema will be validated and data will be added to this
    // existing table.
    // Deprecated. Prefer to use `import_result_bq_sink`.
    BigQueryDestination partial_failure_bigquery_sink = 12 [deprecated = true];
  }

  // Specifies the transformation config for RagFiles.
  RagFileTransformationConfig rag_file_transformation_config = 16;

  // Optional. The max number of queries per minute that this job is allowed to
  // make to the embedding model specified on the corpus. This value is specific
  // to this job and not shared across other import jobs. Consult the Quotas
  // page on the project to set an appropriate value here.
  // If unspecified, a default value of 1,000 QPM would be used.
  int32 max_embedding_requests_per_min = 5
      [(google.api.field_behavior) = OPTIONAL];
}
