"use strict";
// Copyright 2020 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     https://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//
Object.defineProperty(exports, "__esModule", { value: true });
exports.googleProtobufValueFromObject = googleProtobufValueFromObject;
exports.googleProtobufValueToObject = googleProtobufValueToObject;
// INTERNAL ONLY. This function is not exposed to external callers.
function googleProtobufValueFromObject(object, create) {
    if (object === null) {
        return create({
            kind: 'nullValue',
            nullValue: 0,
        });
    }
    if (typeof object === 'boolean') {
        return create({
            kind: 'boolValue',
            boolValue: object,
        });
    }
    if (typeof object === 'number') {
        return create({
            kind: 'numberValue',
            numberValue: object,
        });
    }
    if (typeof object === 'string') {
        return create({
            kind: 'stringValue',
            stringValue: object,
        });
    }
    if (Array.isArray(object)) {
        const array = object.map(element => {
            return googleProtobufValueFromObject(element, create);
        });
        return create({
            kind: 'listValue',
            listValue: {
                values: array,
            },
        });
    }
    if (typeof object === 'object') {
        // tslint:disable-next-line no-explicit-any
        const fields = {}, names = Object.keys(object);
        for (let i = 0; i < names.length; ++i) {
            const fieldName = names[i];
            fields[fieldName] = googleProtobufValueFromObject(object[fieldName], create);
        }
        return create({
            kind: 'structValue',
            structValue: {
                fields: fields,
            },
        });
    }
    return null;
}
// INTERNAL ONLY. This function not exposed to external callers.
// recursive google.protobuf.Value to plain JS object
function googleProtobufValueToObject(message) {
    var _a, _b, _c;
    if (message.kind === 'boolValue') {
        return message.boolValue;
    }
    if (message.kind === 'nullValue') {
        return null;
    }
    if (message.kind === 'numberValue') {
        return message.numberValue;
    }
    if (message.kind === 'stringValue') {
        return message.stringValue;
    }
    if (message.kind === 'listValue') {
        return (_b = (_a = message.listValue) === null || _a === void 0 ? void 0 : _a.values) === null || _b === void 0 ? void 0 : _b.map(googleProtobufValueToObject);
    }
    if (message.kind === 'structValue') {
        if (!((_c = message.structValue) === null || _c === void 0 ? void 0 : _c.fields)) {
            return {};
        }
        const names = Object.keys(message.structValue.fields), 
        // tslint:disable-next-line no-explicit-any
        struct = {};
        for (let i = 0; i < names.length; ++i) {
            struct[names[i]] = googleProtobufValueToObject(message.structValue['fields'][names[i]]);
        }
        return struct;
    }
    return undefined;
}
//# sourceMappingURL=value-converter.js.map