{"version": 3, "sources": ["../../src/modelgarden/model_garden.ts"], "sourcesContent": ["/**\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Genkit, GENKIT_CLIENT_HEADER, ModelReference, z } from 'genkit';\nimport { GenerateRequest, ModelAction, modelRef } from 'genkit/model';\nimport { GoogleAuth } from 'google-auth-library';\nimport OpenAI from 'openai';\nimport {\n  openaiCompatibleModel,\n  OpenAIConfigSchema,\n} from './openai_compatibility.js';\n\nexport const ModelGardenModelConfigSchema = OpenAIConfigSchema.extend({\n  location: z.string().optional(),\n});\n\nexport const llama31 = modelRef({\n  name: 'vertexai/llama-3.1',\n  info: {\n    label: 'Llama 3.1',\n    supports: {\n      multiturn: true,\n      tools: true,\n      media: false,\n      systemRole: true,\n      output: ['text', 'json'],\n    },\n    versions: [\n      'meta/llama3-405b-instruct-maas',\n      // 8b and 70b versions are coming soon\n    ],\n  },\n  configSchema: ModelGardenModelConfigSchema,\n  version: 'meta/llama3-405b-instruct-maas',\n}) as ModelReference<typeof ModelGardenModelConfigSchema>;\n\nexport const llama32 = modelRef({\n  name: 'vertexai/llama-3.2',\n  info: {\n    label: 'Llama 3.2',\n    supports: {\n      multiturn: true,\n      tools: true,\n      media: true,\n      systemRole: true,\n      output: ['text', 'json'],\n    },\n    versions: ['meta/llama-3.2-90b-vision-instruct-maas'],\n  },\n  configSchema: ModelGardenModelConfigSchema,\n  version: 'meta/llama-3.2-90b-vision-instruct-maas',\n}) as ModelReference<typeof ModelGardenModelConfigSchema>;\n\n/**\n * @deprecated use `llama31` instead\n */\nexport const llama3 = modelRef({\n  name: 'vertexai/llama3-405b',\n  info: {\n    label: 'Llama 3.1 405b',\n    supports: {\n      multiturn: true,\n      tools: true,\n      media: false,\n      systemRole: true,\n      output: ['text'],\n    },\n    versions: ['meta/llama3-405b-instruct-maas'],\n  },\n  configSchema: ModelGardenModelConfigSchema,\n  version: 'meta/llama3-405b-instruct-maas',\n}) as ModelReference<typeof ModelGardenModelConfigSchema>;\n\nexport const SUPPORTED_OPENAI_FORMAT_MODELS = {\n  'llama3-405b': llama3,\n  'llama-3.1': llama31,\n  'llama-3.2': llama32,\n};\n\nexport function modelGardenOpenaiCompatibleModel(\n  ai: Genkit,\n  name: string,\n  projectId: string,\n  location: string,\n  googleAuth: GoogleAuth,\n  baseUrlTemplate: string | undefined\n): ModelAction<typeof ModelGardenModelConfigSchema> {\n  const model = SUPPORTED_OPENAI_FORMAT_MODELS[name];\n  if (!model) throw new Error(`Unsupported model: ${name}`);\n  if (!baseUrlTemplate) {\n    baseUrlTemplate =\n      'https://{location}-aiplatform.googleapis.com/v1beta1/projects/{projectId}/locations/{location}/endpoints/openapi';\n  }\n\n  const clientFactory = async (\n    request: GenerateRequest<typeof ModelGardenModelConfigSchema>\n  ): Promise<OpenAI> => {\n    const requestLocation = request.config?.location || location;\n    return new OpenAI({\n      baseURL: baseUrlTemplate!\n        .replace(/{location}/g, requestLocation)\n        .replace(/{projectId}/g, projectId),\n      apiKey: (await googleAuth.getAccessToken())!,\n      defaultHeaders: {\n        'X-Goog-Api-Client': GENKIT_CLIENT_HEADER,\n      },\n    });\n  };\n  return openaiCompatibleModel(ai, model, clientFactory);\n}\n"], "mappings": "AAgBA,SAAiB,sBAAsC,SAAS;AAChE,SAAuC,gBAAgB;AAEvD,OAAO,YAAY;AACnB;AAAA,EACE;AAAA,EACA;AAAA,OACK;AAEA,MAAM,+BAA+B,mBAAmB,OAAO;AAAA,EACpE,UAAU,EAAE,OAAO,EAAE,SAAS;AAChC,CAAC;AAEM,MAAM,UAAU,SAAS;AAAA,EAC9B,MAAM;AAAA,EACN,MAAM;AAAA,IACJ,OAAO;AAAA,IACP,UAAU;AAAA,MACR,WAAW;AAAA,MACX,OAAO;AAAA,MACP,OAAO;AAAA,MACP,YAAY;AAAA,MACZ,QAAQ,CAAC,QAAQ,MAAM;AAAA,IACzB;AAAA,IACA,UAAU;AAAA,MACR;AAAA;AAAA,IAEF;AAAA,EACF;AAAA,EACA,cAAc;AAAA,EACd,SAAS;AACX,CAAC;AAEM,MAAM,UAAU,SAAS;AAAA,EAC9B,MAAM;AAAA,EACN,MAAM;AAAA,IACJ,OAAO;AAAA,IACP,UAAU;AAAA,MACR,WAAW;AAAA,MACX,OAAO;AAAA,MACP,OAAO;AAAA,MACP,YAAY;AAAA,MACZ,QAAQ,CAAC,QAAQ,MAAM;AAAA,IACzB;AAAA,IACA,UAAU,CAAC,yCAAyC;AAAA,EACtD;AAAA,EACA,cAAc;AAAA,EACd,SAAS;AACX,CAAC;AAKM,MAAM,SAAS,SAAS;AAAA,EAC7B,MAAM;AAAA,EACN,MAAM;AAAA,IACJ,OAAO;AAAA,IACP,UAAU;AAAA,MACR,WAAW;AAAA,MACX,OAAO;AAAA,MACP,OAAO;AAAA,MACP,YAAY;AAAA,MACZ,QAAQ,CAAC,MAAM;AAAA,IACjB;AAAA,IACA,UAAU,CAAC,gCAAgC;AAAA,EAC7C;AAAA,EACA,cAAc;AAAA,EACd,SAAS;AACX,CAAC;AAEM,MAAM,iCAAiC;AAAA,EAC5C,eAAe;AAAA,EACf,aAAa;AAAA,EACb,aAAa;AACf;AAEO,SAAS,iCACd,IACA,MACA,WACA,UACA,YACA,iBACkD;AAClD,QAAM,QAAQ,+BAA+B,IAAI;AACjD,MAAI,CAAC,MAAO,OAAM,IAAI,MAAM,sBAAsB,IAAI,EAAE;AACxD,MAAI,CAAC,iBAAiB;AACpB,sBACE;AAAA,EACJ;AAEA,QAAM,gBAAgB,OACpB,YACoB;AACpB,UAAM,kBAAkB,QAAQ,QAAQ,YAAY;AACpD,WAAO,IAAI,OAAO;AAAA,MAChB,SAAS,gBACN,QAAQ,eAAe,eAAe,EACtC,QAAQ,gBAAgB,SAAS;AAAA,MACpC,QAAS,MAAM,WAAW,eAAe;AAAA,MACzC,gBAAgB;AAAA,QACd,qBAAqB;AAAA,MACvB;AAAA,IACF,CAAC;AAAA,EACH;AACA,SAAO,sBAAsB,IAAI,OAAO,aAAa;AACvD;", "names": []}