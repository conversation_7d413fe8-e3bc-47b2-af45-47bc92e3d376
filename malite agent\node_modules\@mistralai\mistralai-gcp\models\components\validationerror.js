"use strict";
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ValidationError$ = exports.ValidationError$outboundSchema = exports.ValidationError$inboundSchema = exports.Loc$ = exports.Loc$outboundSchema = exports.Loc$inboundSchema = void 0;
exports.locToJSON = locToJSON;
exports.locFromJSON = locFromJSON;
exports.validationErrorToJSON = validationErrorToJSON;
exports.validationErrorFromJSON = validationErrorFromJSON;
const z = __importStar(require("zod"));
const schemas_js_1 = require("../../lib/schemas.js");
/** @internal */
exports.Loc$inboundSchema = z.union([z.string(), z.number().int()]);
/** @internal */
exports.Loc$outboundSchema = z
    .union([z.string(), z.number().int()]);
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var Loc$;
(function (Loc$) {
    /** @deprecated use `Loc$inboundSchema` instead. */
    Loc$.inboundSchema = exports.Loc$inboundSchema;
    /** @deprecated use `Loc$outboundSchema` instead. */
    Loc$.outboundSchema = exports.Loc$outboundSchema;
})(Loc$ || (exports.Loc$ = Loc$ = {}));
function locToJSON(loc) {
    return JSON.stringify(exports.Loc$outboundSchema.parse(loc));
}
function locFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.Loc$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'Loc' from JSON`);
}
/** @internal */
exports.ValidationError$inboundSchema = z.object({
    loc: z.array(z.union([z.string(), z.number().int()])),
    msg: z.string(),
    type: z.string(),
});
/** @internal */
exports.ValidationError$outboundSchema = z.object({
    loc: z.array(z.union([z.string(), z.number().int()])),
    msg: z.string(),
    type: z.string(),
});
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var ValidationError$;
(function (ValidationError$) {
    /** @deprecated use `ValidationError$inboundSchema` instead. */
    ValidationError$.inboundSchema = exports.ValidationError$inboundSchema;
    /** @deprecated use `ValidationError$outboundSchema` instead. */
    ValidationError$.outboundSchema = exports.ValidationError$outboundSchema;
})(ValidationError$ || (exports.ValidationError$ = ValidationError$ = {}));
function validationErrorToJSON(validationError) {
    return JSON.stringify(exports.ValidationError$outboundSchema.parse(validationError));
}
function validationErrorFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.ValidationError$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'ValidationError' from JSON`);
}
//# sourceMappingURL=validationerror.js.map