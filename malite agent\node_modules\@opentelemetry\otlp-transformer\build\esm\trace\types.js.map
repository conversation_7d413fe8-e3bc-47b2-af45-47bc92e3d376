{"version": 3, "file": "types.js", "sourceRoot": "", "sources": ["../../../src/trace/types.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAgGH;;;GAGG;AACH,MAAM,CAAN,IAAY,SA8BX;AA9BD,WAAY,SAAS;IACnB,yHAAyH;IACzH,2EAAyB,CAAA;IAEzB;;OAEG;IACH,qEAAsB,CAAA;IAEtB;;OAEG;IACH,iEAAoB,CAAA;IAEpB;OACG;IACH,iEAAoB,CAAA;IAEpB;;;;OAIG;IACH,qEAAsB,CAAA;IAEtB;;;OAGG;IACH,qEAAsB,CAAA;AACxB,CAAC,EA9BW,SAAS,KAAT,SAAS,QA8BpB", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Fixed64, IInstrumentationScope, IKeyValue } from '../common/types';\nimport { IResource } from '../resource/types';\n\n/** Properties of an ExportTraceServiceRequest. */\nexport interface IExportTraceServiceRequest {\n  /** ExportTraceServiceRequest resourceSpans */\n  resourceSpans?: IResourceSpans[];\n}\n\nexport interface IExportTraceServiceResponse {\n  /** ExportTraceServiceResponse partialSuccess */\n  partialSuccess?: IExportTracePartialSuccess;\n}\n\nexport interface IExportTracePartialSuccess {\n  /** ExportLogsServiceResponse rejectedLogRecords */\n  rejectedSpans?: number;\n\n  /** ExportLogsServiceResponse errorMessage */\n  errorMessage?: string;\n}\n\n/** Properties of a ResourceSpans. */\nexport interface IResourceSpans {\n  /** ResourceSpans resource */\n  resource?: IResource;\n\n  /** ResourceSpans scopeSpans */\n  scopeSpans: IScopeSpans[];\n\n  /** ResourceSpans schemaUrl */\n  schemaUrl?: string;\n}\n\n/** Properties of an ScopeSpans. */\nexport interface IScopeSpans {\n  /** IScopeSpans scope */\n  scope?: IInstrumentationScope;\n\n  /** IScopeSpans spans */\n  spans?: ISpan[];\n\n  /** IScopeSpans schemaUrl */\n  schemaUrl?: string | null;\n}\n\n/** Properties of a Span. */\nexport interface ISpan {\n  /** Span traceId */\n  traceId: string | Uint8Array;\n\n  /** Span spanId */\n  spanId: string | Uint8Array;\n\n  /** Span traceState */\n  traceState?: string | null;\n\n  /** Span parentSpanId */\n  parentSpanId?: string | Uint8Array;\n\n  /** Span name */\n  name: string;\n\n  /** Span kind */\n  kind: ESpanKind;\n\n  /** Span startTimeUnixNano */\n  startTimeUnixNano: Fixed64;\n\n  /** Span endTimeUnixNano */\n  endTimeUnixNano: Fixed64;\n\n  /** Span attributes */\n  attributes: IKeyValue[];\n\n  /** Span droppedAttributesCount */\n  droppedAttributesCount: number;\n\n  /** Span events */\n  events: IEvent[];\n\n  /** Span droppedEventsCount */\n  droppedEventsCount: number;\n\n  /** Span links */\n  links: ILink[];\n\n  /** Span droppedLinksCount */\n  droppedLinksCount: number;\n\n  /** Span status */\n  status: IStatus;\n}\n\n/**\n * SpanKind is the type of span. Can be used to specify additional relationships between spans\n * in addition to a parent/child relationship.\n */\nexport enum ESpanKind {\n  /** Unspecified. Do NOT use as default. Implementations MAY assume SpanKind to be INTERNAL when receiving UNSPECIFIED. */\n  SPAN_KIND_UNSPECIFIED = 0,\n\n  /** Indicates that the span represents an internal operation within an application,\n   * as opposed to an operation happening at the boundaries. Default value.\n   */\n  SPAN_KIND_INTERNAL = 1,\n\n  /** Indicates that the span covers server-side handling of an RPC or other\n   * remote network request.\n   */\n  SPAN_KIND_SERVER = 2,\n\n  /** Indicates that the span describes a request to some remote service.\n   */\n  SPAN_KIND_CLIENT = 3,\n\n  /** Indicates that the span describes a producer sending a message to a broker.\n   * Unlike CLIENT and SERVER, there is often no direct critical path latency relationship\n   * between producer and consumer spans. A PRODUCER span ends when the message was accepted\n   * by the broker while the logical processing of the message might span a much longer time.\n   */\n  SPAN_KIND_PRODUCER = 4,\n\n  /** Indicates that the span describes consumer receiving a message from a broker.\n   * Like the PRODUCER kind, there is often no direct critical path latency relationship\n   * between producer and consumer spans.\n   */\n  SPAN_KIND_CONSUMER = 5,\n}\n\n/** Properties of a Status. */\nexport interface IStatus {\n  /** Status message */\n  message?: string;\n\n  /** Status code */\n  code: EStatusCode;\n}\n\n/** StatusCode enum. */\nexport const enum EStatusCode {\n  /** The default status. */\n  STATUS_CODE_UNSET = 0,\n  /** The Span has been evaluated by an Application developers or Operator to have completed successfully. */\n  STATUS_CODE_OK = 1,\n  /** The Span contains an error. */\n  STATUS_CODE_ERROR = 2,\n}\n\n/** Properties of an Event. */\nexport interface IEvent {\n  /** Event timeUnixNano */\n  timeUnixNano: Fixed64;\n\n  /** Event name */\n  name: string;\n\n  /** Event attributes */\n  attributes: IKeyValue[];\n\n  /** Event droppedAttributesCount */\n  droppedAttributesCount: number;\n}\n\n/** Properties of a Link. */\nexport interface ILink {\n  /** Link traceId */\n  traceId: string | Uint8Array;\n\n  /** Link spanId */\n  spanId: string | Uint8Array;\n\n  /** Link traceState */\n  traceState?: string;\n\n  /** Link attributes */\n  attributes: IKeyValue[];\n\n  /** Link droppedAttributesCount */\n  droppedAttributesCount: number;\n}\n"]}