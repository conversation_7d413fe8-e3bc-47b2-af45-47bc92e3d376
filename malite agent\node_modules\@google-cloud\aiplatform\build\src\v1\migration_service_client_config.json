{"interfaces": {"google.cloud.aiplatform.v1.MigrationService": {"retry_codes": {"non_idempotent": [], "idempotent": ["DEADLINE_EXCEEDED", "UNAVAILABLE"]}, "retry_params": {"default": {"initial_retry_delay_millis": 100, "retry_delay_multiplier": 1.3, "max_retry_delay_millis": 60000, "initial_rpc_timeout_millis": 60000, "rpc_timeout_multiplier": 1, "max_rpc_timeout_millis": 60000, "total_timeout_millis": 600000}}, "methods": {"SearchMigratableResources": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "BatchMigrateResources": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}}}}}