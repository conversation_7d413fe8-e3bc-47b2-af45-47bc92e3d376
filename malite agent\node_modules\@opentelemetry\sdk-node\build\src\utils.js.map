{"version": 3, "file": "utils.js", "sourceRoot": "", "sources": ["../../src/utils.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,4CAA0C;AAC1C,wDAOkC;AAElC,MAAM,6BAA6B,GAAG,KAAK,CAAC;AAC5C,MAAM,sBAAsB,GAAG,MAAM,CAAC;AACtC,MAAM,oBAAoB,GAAG,IAAI,CAAC;AAClC,MAAM,yBAAyB,GAAG,SAAS,CAAC;AAC5C,MAAM,qCAAqC,GAAG,iBAAiB,CAAC;AAEhE,SAAgB,2BAA2B;;IACzC,+FAA+F;IAC/F,MAAM,iBAAiB,GAAG,IAAI,GAAG,CAAuB;QACtD,CAAC,6BAA6B,EAAE,2BAAe,CAAC;QAChD,CAAC,sBAAsB,EAAE,4BAAgB,CAAC;QAC1C,CAAC,oBAAoB,EAAE,0BAAc,CAAC;QACtC,CAAC,qCAAqC,EAAE,yCAA6B,CAAC;QACtE,CAAC,yBAAyB,EAAE,+BAAmB,CAAC;KACjD,CAAC,CAAC;IAEH,MAAM,wBAAwB,GAC5B,MAAA,MAAA,OAAO,CAAC,GAAG,CAAC,4BAA4B,0CAAE,KAAK,CAAC,GAAG,CAAC,mCAAI,CAAC,KAAK,CAAC,CAAC;IAElE,IAAI,wBAAwB,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE;QAC5C,OAAO,CAAC,GAAG,iBAAiB,CAAC,MAAM,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;KAC/C;IAED,IAAI,wBAAwB,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE;QAC7C,OAAO,EAAE,CAAC;KACX;IAED,OAAO,wBAAwB,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;QACjD,MAAM,gBAAgB,GAAG,iBAAiB,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QACzD,IAAI,CAAC,gBAAgB,EAAE;YACrB,UAAI,CAAC,KAAK,CACR,8BAA8B,QAAQ,sEAAsE,CAC7G,CAAC;SACH;QACD,OAAO,gBAAgB,IAAI,EAAE,CAAC;IAChC,CAAC,CAAC,CAAC;AACL,CAAC;AA9BD,kEA8BC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { diag } from '@opentelemetry/api';\nimport {\n  DetectorSync,\n  envDetectorSync,\n  hostDetectorSync,\n  osDetectorSync,\n  processDetectorSync,\n  serviceInstanceIdDetectorSync,\n} from '@opentelemetry/resources';\n\nconst RESOURCE_DETECTOR_ENVIRONMENT = 'env';\nconst RESOURCE_DETECTOR_HOST = 'host';\nconst RESOURCE_DETECTOR_OS = 'os';\nconst RESOURCE_DETECTOR_PROCESS = 'process';\nconst RESOURCE_DETECTOR_SERVICE_INSTANCE_ID = 'serviceinstance';\n\nexport function getResourceDetectorsFromEnv(): Array<DetectorSync> {\n  // When updating this list, make sure to also update the section `resourceDetectors` on README.\n  const resourceDetectors = new Map<string, DetectorSync>([\n    [RESOURCE_DETECTOR_ENVIRONMENT, envDetectorSync],\n    [RESOURCE_DETECTOR_HOST, hostDetectorSync],\n    [RESOURCE_DETECTOR_OS, osDetectorSync],\n    [RESOURCE_DETECTOR_SERVICE_INSTANCE_ID, serviceInstanceIdDetectorSync],\n    [RESOURCE_DETECTOR_PROCESS, processDetectorSync],\n  ]);\n\n  const resourceDetectorsFromEnv =\n    process.env.OTEL_NODE_RESOURCE_DETECTORS?.split(',') ?? ['all'];\n\n  if (resourceDetectorsFromEnv.includes('all')) {\n    return [...resourceDetectors.values()].flat();\n  }\n\n  if (resourceDetectorsFromEnv.includes('none')) {\n    return [];\n  }\n\n  return resourceDetectorsFromEnv.flatMap(detector => {\n    const resourceDetector = resourceDetectors.get(detector);\n    if (!resourceDetector) {\n      diag.error(\n        `Invalid resource detector \"${detector}\" specified in the environment variable OTEL_NODE_RESOURCE_DETECTORS`\n      );\n    }\n    return resourceDetector || [];\n  });\n}\n"]}