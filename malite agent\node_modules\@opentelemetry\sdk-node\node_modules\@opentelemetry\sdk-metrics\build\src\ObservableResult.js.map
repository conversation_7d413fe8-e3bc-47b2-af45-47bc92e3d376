{"version": 3, "file": "ObservableResult.js", "sourceRoot": "", "sources": ["../../src/ObservableResult.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,4CAO4B;AAC5B,6CAAmD;AACnD,+CAA6E;AAE7E;;GAEG;AACH,MAAa,oBAAoB;IAM/B,YACU,eAAuB,EACvB,UAAqB;QADrB,oBAAe,GAAf,eAAe,CAAQ;QACvB,eAAU,GAAV,UAAU,CAAW;QAP/B;;WAEG;QACH,YAAO,GAAG,IAAI,0BAAgB,EAAU,CAAC;IAKtC,CAAC;IAEJ;;OAEG;IACH,OAAO,CAAC,KAAa,EAAE,aAA+B,EAAE;QACtD,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;YAC7B,UAAI,CAAC,IAAI,CACP,uCAAuC,IAAI,CAAC,eAAe,KAAK,KAAK,EAAE,CACxE,CAAC;YACF,OAAO;SACR;QACD,IAAI,IAAI,CAAC,UAAU,KAAK,eAAS,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE;YACjE,UAAI,CAAC,IAAI,CACP,2DAA2D,IAAI,CAAC,eAAe,mCAAmC,CACnH,CAAC;YACF,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YAC1B,4BAA4B;YAC5B,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE;gBAC5B,OAAO;aACR;SACF;QACD,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;IACtC,CAAC;CACF;AAjCD,oDAiCC;AAED;;GAEG;AACH,MAAa,yBAAyB;IAAtC;QACE;;WAEG;QACH,YAAO,GAAwD,IAAI,GAAG,EAAE,CAAC;IAuC3E,CAAC;IArCC;;OAEG;IACH,OAAO,CACL,MAAkB,EAClB,KAAa,EACb,aAA+B,EAAE;QAEjC,IAAI,CAAC,IAAA,oCAAsB,EAAC,MAAM,CAAC,EAAE;YACnC,OAAO;SACR;QACD,IAAI,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QACnC,IAAI,GAAG,IAAI,IAAI,EAAE;YACf,GAAG,GAAG,IAAI,0BAAgB,EAAE,CAAC;YAC7B,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;SAC/B;QACD,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;YAC7B,UAAI,CAAC,IAAI,CACP,uCAAuC,MAAM,CAAC,WAAW,CAAC,IAAI,KAAK,KAAK,EAAE,CAC3E,CAAC;YACF,OAAO;SACR;QACD,IACE,MAAM,CAAC,WAAW,CAAC,SAAS,KAAK,eAAS,CAAC,GAAG;YAC9C,CAAC,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,EACxB;YACA,UAAI,CAAC,IAAI,CACP,2DAA2D,MAAM,CAAC,WAAW,CAAC,IAAI,mCAAmC,CACtH,CAAC;YACF,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YAC1B,4BAA4B;YAC5B,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE;gBAC5B,OAAO;aACR;SACF;QACD,GAAG,CAAC,GAAG,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;IAC7B,CAAC;CACF;AA3CD,8DA2CC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  diag,\n  ObservableResult,\n  MetricAttributes,\n  ValueType,\n  BatchObservableResult,\n  Observable,\n} from '@opentelemetry/api';\nimport { AttributeHashMap } from './state/HashMap';\nimport { isObservableInstrument, ObservableInstrument } from './Instruments';\n\n/**\n * The class implements {@link ObservableResult} interface.\n */\nexport class ObservableResultImpl implements ObservableResult {\n  /**\n   * @internal\n   */\n  _buffer = new AttributeHashMap<number>();\n\n  constructor(\n    private _instrumentName: string,\n    private _valueType: ValueType\n  ) {}\n\n  /**\n   * Observe a measurement of the value associated with the given attributes.\n   */\n  observe(value: number, attributes: MetricAttributes = {}): void {\n    if (typeof value !== 'number') {\n      diag.warn(\n        `non-number value provided to metric ${this._instrumentName}: ${value}`\n      );\n      return;\n    }\n    if (this._valueType === ValueType.INT && !Number.isInteger(value)) {\n      diag.warn(\n        `INT value type cannot accept a floating-point value for ${this._instrumentName}, ignoring the fractional digits.`\n      );\n      value = Math.trunc(value);\n      // ignore non-finite values.\n      if (!Number.isInteger(value)) {\n        return;\n      }\n    }\n    this._buffer.set(attributes, value);\n  }\n}\n\n/**\n * The class implements {@link BatchObservableCallback} interface.\n */\nexport class BatchObservableResultImpl implements BatchObservableResult {\n  /**\n   * @internal\n   */\n  _buffer: Map<ObservableInstrument, AttributeHashMap<number>> = new Map();\n\n  /**\n   * Observe a measurement of the value associated with the given attributes.\n   */\n  observe(\n    metric: Observable,\n    value: number,\n    attributes: MetricAttributes = {}\n  ): void {\n    if (!isObservableInstrument(metric)) {\n      return;\n    }\n    let map = this._buffer.get(metric);\n    if (map == null) {\n      map = new AttributeHashMap();\n      this._buffer.set(metric, map);\n    }\n    if (typeof value !== 'number') {\n      diag.warn(\n        `non-number value provided to metric ${metric._descriptor.name}: ${value}`\n      );\n      return;\n    }\n    if (\n      metric._descriptor.valueType === ValueType.INT &&\n      !Number.isInteger(value)\n    ) {\n      diag.warn(\n        `INT value type cannot accept a floating-point value for ${metric._descriptor.name}, ignoring the fractional digits.`\n      );\n      value = Math.trunc(value);\n      // ignore non-finite values.\n      if (!Number.isInteger(value)) {\n        return;\n      }\n    }\n    map.set(attributes, value);\n  }\n}\n"]}