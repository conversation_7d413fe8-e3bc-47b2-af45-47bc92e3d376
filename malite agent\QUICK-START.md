# ⚡ دليل التشغيل السريع - Malite Agent

## 🚀 البدء السريع (5 دقائق)

### 1️⃣ التحقق من الإعدادات
```bash
npm run setup
```

### 2️⃣ التشغيل الأساسي
```bash
npm start
```

### 3️⃣ عرض توضيحي سريع
```bash
npm run demo
```

### 4️⃣ واجهة المطور
```bash
npm run dev
```
ثم افتح: http://localhost:4000

---

## 📋 جدول الأوامر السريع

| الأمر | الوصف | الاستخدام |
|-------|--------|-----------|
| `npm start` | تشغيل أساسي | للاختبار السريع |
| `npm run demo` | عرض توضيحي | لرؤية الإمكانيات |
| `npm run dev` | واجهة المطور | للتطوير والتصحيح |
| `npm run info` | معلومات المشروع | للمساعدة |
| `npm run setup` | فحص الإعدادات | لحل المشاكل |

---

## 🎯 أمثلة سريعة

### أمثلة عربية
```bash
npm run vertex:arabic
```

### تطبيقات عملية
```bash
npm run vertex:practical
```

### معالجة الصور
```bash
npm run vertex:multimodal
```

---

## 🔧 حل المشاكل السريع

### ❌ خطأ في مفتاح API
1. تحقق من ملف `.env`
2. تأكد من وجود `GEMINI_API_KEY`
3. شغل: `npm run setup`

### ❌ مشكلة في التبعيات
```bash
rm -rf node_modules
npm install
```

### ❌ واجهة المطور لا تعمل
```bash
npm run dev-open
```

---

## 📚 الخطوات التالية

1. **جرب الأمثلة المختلفة** - `npm run vertex:*`
2. **استكشف واجهة المطور** - `npm run dev`
3. **اقرأ التوثيق الكامل** - `README.md`
4. **طور تطبيقك الخاص** - استخدم الأمثلة كنقطة بداية

---

## 💡 نصائح سريعة

- استخدم `npm run info` لعرض جميع الأوامر المتاحة
- جرب `npm run demo` للحصول على فكرة سريعة عن الإمكانيات
- واجهة المطور على `localhost:4000` مفيدة جداً للتصحيح
- جميع الأمثلة تدعم اللغة العربية بشكل كامل

---

**🎉 مرحباً بك في عالم الذكاء الاصطناعي باللغة العربية!**
