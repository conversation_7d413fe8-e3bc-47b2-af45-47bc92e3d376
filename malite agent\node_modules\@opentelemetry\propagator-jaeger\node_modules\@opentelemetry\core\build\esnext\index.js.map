{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/index.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAEH,cAAc,4CAA4C,CAAC;AAC3D,cAAc,yBAAyB,CAAC;AACxC,cAAc,qBAAqB,CAAC;AACpC,cAAc,+BAA+B,CAAC;AAC9C,cAAc,gCAAgC,CAAC;AAC/C,cAAc,eAAe,CAAC;AAC9B,cAAc,gBAAgB,CAAC;AAC/B,cAAc,wBAAwB,CAAC;AACvC,cAAc,gBAAgB,CAAC;AAC/B,OAAO,KAAK,YAAY,MAAM,iBAAiB,CAAC;AAChD,cAAc,YAAY,CAAC;AAC3B,cAAc,yBAAyB,CAAC;AACxC,cAAc,mCAAmC,CAAC;AAClD,cAAc,qBAAqB,CAAC;AACpC,cAAc,sBAAsB,CAAC;AACrC,cAAc,kCAAkC,CAAC;AACjD,cAAc,iCAAiC,CAAC;AAChD,cAAc,oCAAoC,CAAC;AACnD,cAAc,0CAA0C,CAAC;AACzD,cAAc,0BAA0B,CAAC;AACzC,cAAc,oBAAoB,CAAC;AACnC,cAAc,qBAAqB,CAAC;AACpC,cAAc,eAAe,CAAC;AAC9B,cAAc,kBAAkB,CAAC;AACjC,cAAc,iBAAiB,CAAC;AAChC,cAAc,aAAa,CAAC;AAC5B,cAAc,cAAc,CAAC;AAC7B,cAAc,kBAAkB,CAAC;AACjC,cAAc,WAAW,CAAC;AAC1B,OAAO,EAAE,OAAO,EAAE,MAAM,qBAAqB,CAAC;AAC9C,MAAM,CAAC,MAAM,QAAQ,GAAG;IACtB,OAAO;CACR,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport * from './baggage/propagation/W3CBaggagePropagator';\nexport * from './common/anchored-clock';\nexport * from './common/attributes';\nexport * from './common/global-error-handler';\nexport * from './common/logging-error-handler';\nexport * from './common/time';\nexport * from './common/types';\nexport * from './common/hex-to-binary';\nexport * from './ExportResult';\nexport * as baggageUtils from './baggage/utils';\nexport * from './platform';\nexport * from './propagation/composite';\nexport * from './trace/W3CTraceContextPropagator';\nexport * from './trace/IdGenerator';\nexport * from './trace/rpc-metadata';\nexport * from './trace/sampler/AlwaysOffSampler';\nexport * from './trace/sampler/AlwaysOnSampler';\nexport * from './trace/sampler/ParentBasedSampler';\nexport * from './trace/sampler/TraceIdRatioBasedSampler';\nexport * from './trace/suppress-tracing';\nexport * from './trace/TraceState';\nexport * from './utils/environment';\nexport * from './utils/merge';\nexport * from './utils/sampling';\nexport * from './utils/timeout';\nexport * from './utils/url';\nexport * from './utils/wrap';\nexport * from './utils/callback';\nexport * from './version';\nimport { _export } from './internal/exporter';\nexport const internal = {\n  _export,\n};\n"]}