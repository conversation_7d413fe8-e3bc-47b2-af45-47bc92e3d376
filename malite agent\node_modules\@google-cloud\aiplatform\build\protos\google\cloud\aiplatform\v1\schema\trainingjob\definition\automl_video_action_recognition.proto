// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.aiplatform.v1.schema.trainingjob.definition;


option csharp_namespace = "Google.Cloud.AIPlatform.V1.Schema.TrainingJob.Definition";
option go_package = "cloud.google.com/go/aiplatform/apiv1/schema/trainingjob/definition/definitionpb;definitionpb";
option java_multiple_files = true;
option java_outer_classname = "AutoMLVideoActionRecognitionProto";
option java_package = "com.google.cloud.aiplatform.v1.schema.trainingjob.definition";
option php_namespace = "Google\\Cloud\\AIPlatform\\V1\\Schema\\TrainingJob\\Definition";
option ruby_package = "Google::Cloud::AIPlatform::V1::Schema::TrainingJob::Definition";

// A TrainingJob that trains and uploads an AutoML Video Action Recognition
// Model.
message AutoMlVideoActionRecognition {
  // The input parameters of this TrainingJob.
  AutoMlVideoActionRecognitionInputs inputs = 1;
}

message AutoMlVideoActionRecognitionInputs {
  enum ModelType {
    // Should not be set.
    MODEL_TYPE_UNSPECIFIED = 0;

    // A model best tailored to be used within Google Cloud, and which c annot
    // be exported. Default.
    CLOUD = 1;

    // A model that, in addition to being available within Google Cloud, can
    // also be exported (see ModelService.ExportModel) as a TensorFlow or
    // TensorFlow Lite model and used on a mobile or edge device afterwards.
    MOBILE_VERSATILE_1 = 2;

    // A model that, in addition to being available within Google Cloud, can
    // also be exported (see ModelService.ExportModel) to a Jetson device
    // afterwards.
    MOBILE_JETSON_VERSATILE_1 = 3;

    // A model that, in addition to being available within Google Cloud, can
    // also be exported (see ModelService.ExportModel) as a TensorFlow or
    // TensorFlow Lite model and used on a Coral device afterwards.
    MOBILE_CORAL_VERSATILE_1 = 4;
  }

  ModelType model_type = 1;
}
