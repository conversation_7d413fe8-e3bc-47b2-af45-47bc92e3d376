/*!
 * Copyright 2020 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
import { ServiceObject, ResponseCallback, SetMetadataResponse } from '@google-cloud/common';
import { Dataset, RoutineMetadata } from './dataset';
/**
 * Routine objects are returned by methods such as
 * {@link Dataset#routine}, {@link Dataset#createRoutine}, and
 * {@link Dataset#getRoutines}.
 *
 * @class
 * @param {Dataset} dataset {@link Dataset} instance.
 * @param {string} id The ID of the routine.
 *
 * @example
 * ```
 * const {BigQuery} = require('@google-cloud/bigquery');
 * const bigquery = new BigQuery();
 * const dataset = bigquery.dataset('my-dataset');
 *
 * const routine = dataset.routine('my_routine');
 * ```
 */
declare class Routine extends ServiceObject {
    constructor(dataset: Dataset, id: string);
    setMetadata(metadata: RoutineMetadata): Promise<SetMetadataResponse>;
    setMetadata(metadata: RoutineMetadata, callback: ResponseCallback): void;
}
export { Routine };
