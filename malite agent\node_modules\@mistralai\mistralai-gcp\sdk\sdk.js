"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MistralGoogleCloud = void 0;
const google_auth_library_1 = require("google-auth-library");
const sdks_js_1 = require("../lib/sdks.js");
const chat_1 = require("./chat");
const fim_1 = require("./fim");
const hooks_1 = require("../hooks");
const LEGACY_MODEL_ID_FORMAT = {
    "codestral-2405": "codestral@2405",
    "mistral-large-2407": "mistral-large@2407",
    "mistral-nemo-2407": "mistral-nemo@2407",
};
function getModelInfo(model) {
    const modelId = LEGACY_MODEL_ID_FORMAT[model];
    if (modelId === undefined) {
        return [model, model];
    }
    model = model.split("-").slice(0, -1).join("-");
    return [model, modelId];
}
class MistralGoogleCloud extends sdks_js_1.ClientSDK {
    constructor(options = {}) {
        let projectId = options.projectId ?? "";
        options.region = options.region ?? "europe-west4";
        options.serverURL = `https://${options.region}-aiplatform.googleapis.com`;
        if (options.apiKey) {
            if (!options.projectId) {
                throw new Error("if apiKey is provided, projectId must also be provided to be able to use the Google Cloud API");
            }
        }
        else {
            const auth = new google_auth_library_1.GoogleAuth({
                scopes: "https://www.googleapis.com/auth/cloud-platform",
            });
            options.apiKey = async () => {
                const authClient = await auth.getClient();
                const authHeaders = await authClient.getRequestHeaders();
                const token = authHeaders["Authorization"];
                if (!token) {
                    throw new Error("failed to get Google Cloud API key from the default authorization provider, check you are authenticated");
                }
                if (!options.projectId) {
                    if (!authHeaders["x-goog-user-project"]) {
                        throw new Error("no project id available in default google credentials. Please provide a project id in the input arguments to MistralGoogleCloud.");
                    }
                    projectId = authHeaders["x-goog-user-project"];
                }
                return token;
            };
        }
        const hooks = new hooks_1.SDKHooks();
        const superOptions = options;
        superOptions.hooks = hooks;
        super(options);
        hooks.registerBeforeCreateRequestHook({
            beforeCreateRequest: (_, input) => {
                if (!projectId) {
                    throw new Error("projectId was not resolved by the auth hook");
                }
                if (!input.options?.body) {
                    throw new Error("Expected body");
                }
                let body = {};
                const bodyStream = input.options.body;
                if (bodyStream) {
                    body = JSON.parse(bodyStream.toString());
                }
                if (typeof body !== "object" || !body) {
                    throw new Error("Expected object body");
                }
                const rawPredictType = input.url.pathname.includes("streamRawPredict")
                    ? "streamRawPredict"
                    : "rawPredict";
                if (!("model" in body) || typeof body.model !== "string") {
                    throw new Error("body.model is required and must be a string");
                }
                const [model, modelId] = getModelInfo(body.model);
                if (!model || !modelId) {
                    throw new Error("model must be in the format 'model-version'");
                }
                input.url.pathname = `v1/projects/${projectId}/locations/${options.region}/publishers/mistralai/models/${modelId}:${rawPredictType}`;
                body.model = model;
                input.options.body = JSON.stringify(body);
                return input;
            },
        });
    }
    get chat() {
        return (this._chat ?? (this._chat = new chat_1.Chat(this._options)));
    }
    get fim() {
        return (this._fim ?? (this._fim = new fim_1.Fim(this._options)));
    }
}
exports.MistralGoogleCloud = MistralGoogleCloud;
//# sourceMappingURL=sdk.js.map