{"version": 3, "file": "common.js", "sourceRoot": "", "sources": ["../../src/common.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,4CAAsD;AAEtD,4DAA4D;AAC/C,QAAA,iBAAiB,GAAG,IAAA,sBAAgB,EAC/C,yCAAyC,CAC1C,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { createContextKey } from '@opentelemetry/api';\n\n/** shared context for storing an extracted b3 debug flag */\nexport const B3_DEBUG_FLAG_KEY = createContextKey(\n  'OpenTelemetry Context Key B3 Debug Flag'\n);\n"]}