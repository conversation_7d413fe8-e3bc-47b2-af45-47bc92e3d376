import type * as gax from 'google-gax';
import type { Callback, CallOptions, Descriptors, ClientOptions, LROperation, PaginationCallback, IamClient, IamProtos, LocationsClient, LocationProtos } from 'google-gax';
import { Transform } from 'stream';
import * as protos from '../../protos/protos';
/**
 *  The interface for Vertex Notebook service (a.k.a. Colab on Workbench).
 * @class
 * @memberof v1beta1
 */
export declare class NotebookServiceClient {
    private _terminated;
    private _opts;
    private _providedCustomServicePath;
    private _gaxModule;
    private _gaxGrpc;
    private _protos;
    private _defaults;
    private _universeDomain;
    private _servicePath;
    auth: gax.GoogleAuth;
    descriptors: Descriptors;
    warn: (code: string, message: string, warnType?: string) => void;
    innerApiCalls: {
        [name: string]: Function;
    };
    iamClient: IamClient;
    locationsClient: LocationsClient;
    pathTemplates: {
        [name: string]: gax.PathTemplate;
    };
    operationsClient: gax.OperationsClient;
    notebookServiceStub?: Promise<{
        [name: string]: Function;
    }>;
    /**
     * Construct an instance of NotebookServiceClient.
     *
     * @param {object} [options] - The configuration object.
     * The options accepted by the constructor are described in detail
     * in [this document](https://github.com/googleapis/gax-nodejs/blob/main/client-libraries.md#creating-the-client-instance).
     * The common options are:
     * @param {object} [options.credentials] - Credentials object.
     * @param {string} [options.credentials.client_email]
     * @param {string} [options.credentials.private_key]
     * @param {string} [options.email] - Account email address. Required when
     *     using a .pem or .p12 keyFilename.
     * @param {string} [options.keyFilename] - Full path to the a .json, .pem, or
     *     .p12 key downloaded from the Google Developers Console. If you provide
     *     a path to a JSON file, the projectId option below is not necessary.
     *     NOTE: .pem and .p12 require you to specify options.email as well.
     * @param {number} [options.port] - The port on which to connect to
     *     the remote host.
     * @param {string} [options.projectId] - The project ID from the Google
     *     Developer's Console, e.g. 'grape-spaceship-123'. We will also check
     *     the environment variable GCLOUD_PROJECT for your project ID. If your
     *     app is running in an environment which supports
     *     {@link https://developers.google.com/identity/protocols/application-default-credentials Application Default Credentials},
     *     your project ID will be detected automatically.
     * @param {string} [options.apiEndpoint] - The domain name of the
     *     API remote host.
     * @param {gax.ClientConfig} [options.clientConfig] - Client configuration override.
     *     Follows the structure of {@link gapicConfig}.
     * @param {boolean} [options.fallback] - Use HTTP/1.1 REST mode.
     *     For more information, please check the
     *     {@link https://github.com/googleapis/gax-nodejs/blob/main/client-libraries.md#http11-rest-api-mode documentation}.
     * @param {gax} [gaxInstance]: loaded instance of `google-gax`. Useful if you
     *     need to avoid loading the default gRPC version and want to use the fallback
     *     HTTP implementation. Load only fallback version and pass it to the constructor:
     *     ```
     *     const gax = require('google-gax/build/src/fallback'); // avoids loading google-gax with gRPC
     *     const client = new NotebookServiceClient({fallback: true}, gax);
     *     ```
     */
    constructor(opts?: ClientOptions, gaxInstance?: typeof gax | typeof gax.fallback);
    /**
     * Initialize the client.
     * Performs asynchronous operations (such as authentication) and prepares the client.
     * This function will be called automatically when any class method is called for the
     * first time, but if you need to initialize it before calling an actual method,
     * feel free to call initialize() directly.
     *
     * You can await on this method if you want to make sure the client is initialized.
     *
     * @returns {Promise} A promise that resolves to an authenticated service stub.
     */
    initialize(): Promise<{
        [name: string]: Function;
    }>;
    /**
     * The DNS address for this API service.
     * @deprecated Use the apiEndpoint method of the client instance.
     * @returns {string} The DNS address for this service.
     */
    static get servicePath(): string;
    /**
     * The DNS address for this API service - same as servicePath.
     * @deprecated Use the apiEndpoint method of the client instance.
     * @returns {string} The DNS address for this service.
     */
    static get apiEndpoint(): string;
    /**
     * The DNS address for this API service.
     * @returns {string} The DNS address for this service.
     */
    get apiEndpoint(): string;
    get universeDomain(): string;
    /**
     * The port for this API service.
     * @returns {number} The default port for this service.
     */
    static get port(): number;
    /**
     * The scopes needed to make gRPC calls for every method defined
     * in this service.
     * @returns {string[]} List of default scopes.
     */
    static get scopes(): string[];
    getProjectId(): Promise<string>;
    getProjectId(callback: Callback<string, undefined, undefined>): void;
    /**
     * Gets a NotebookRuntimeTemplate.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.name
     *   Required. The name of the NotebookRuntimeTemplate resource.
     *   Format:
     *   `projects/{project}/locations/{location}/notebookRuntimeTemplates/{notebook_runtime_template}`
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing {@link protos.google.cloud.aiplatform.v1beta1.NotebookRuntimeTemplate|NotebookRuntimeTemplate}.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#regular-methods | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1beta1/notebook_service.get_notebook_runtime_template.js</caption>
     * region_tag:aiplatform_v1beta1_generated_NotebookService_GetNotebookRuntimeTemplate_async
     */
    getNotebookRuntimeTemplate(request?: protos.google.cloud.aiplatform.v1beta1.IGetNotebookRuntimeTemplateRequest, options?: CallOptions): Promise<[
        protos.google.cloud.aiplatform.v1beta1.INotebookRuntimeTemplate,
        (protos.google.cloud.aiplatform.v1beta1.IGetNotebookRuntimeTemplateRequest | undefined),
        {} | undefined
    ]>;
    getNotebookRuntimeTemplate(request: protos.google.cloud.aiplatform.v1beta1.IGetNotebookRuntimeTemplateRequest, options: CallOptions, callback: Callback<protos.google.cloud.aiplatform.v1beta1.INotebookRuntimeTemplate, protos.google.cloud.aiplatform.v1beta1.IGetNotebookRuntimeTemplateRequest | null | undefined, {} | null | undefined>): void;
    getNotebookRuntimeTemplate(request: protos.google.cloud.aiplatform.v1beta1.IGetNotebookRuntimeTemplateRequest, callback: Callback<protos.google.cloud.aiplatform.v1beta1.INotebookRuntimeTemplate, protos.google.cloud.aiplatform.v1beta1.IGetNotebookRuntimeTemplateRequest | null | undefined, {} | null | undefined>): void;
    /**
     * Updates a NotebookRuntimeTemplate.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {google.cloud.aiplatform.v1beta1.NotebookRuntimeTemplate} request.notebookRuntimeTemplate
     *   Required. The NotebookRuntimeTemplate to update.
     * @param {google.protobuf.FieldMask} request.updateMask
     *   Required. The update mask applies to the resource.
     *   For the `FieldMask` definition, see
     *   {@link protos.google.protobuf.FieldMask|google.protobuf.FieldMask}. Input format:
     *   `{paths: "${updated_filed}"}` Updatable fields:
     *
     *     * `encryption_spec.kms_key_name`
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing {@link protos.google.cloud.aiplatform.v1beta1.NotebookRuntimeTemplate|NotebookRuntimeTemplate}.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#regular-methods | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1beta1/notebook_service.update_notebook_runtime_template.js</caption>
     * region_tag:aiplatform_v1beta1_generated_NotebookService_UpdateNotebookRuntimeTemplate_async
     */
    updateNotebookRuntimeTemplate(request?: protos.google.cloud.aiplatform.v1beta1.IUpdateNotebookRuntimeTemplateRequest, options?: CallOptions): Promise<[
        protos.google.cloud.aiplatform.v1beta1.INotebookRuntimeTemplate,
        (protos.google.cloud.aiplatform.v1beta1.IUpdateNotebookRuntimeTemplateRequest | undefined),
        {} | undefined
    ]>;
    updateNotebookRuntimeTemplate(request: protos.google.cloud.aiplatform.v1beta1.IUpdateNotebookRuntimeTemplateRequest, options: CallOptions, callback: Callback<protos.google.cloud.aiplatform.v1beta1.INotebookRuntimeTemplate, protos.google.cloud.aiplatform.v1beta1.IUpdateNotebookRuntimeTemplateRequest | null | undefined, {} | null | undefined>): void;
    updateNotebookRuntimeTemplate(request: protos.google.cloud.aiplatform.v1beta1.IUpdateNotebookRuntimeTemplateRequest, callback: Callback<protos.google.cloud.aiplatform.v1beta1.INotebookRuntimeTemplate, protos.google.cloud.aiplatform.v1beta1.IUpdateNotebookRuntimeTemplateRequest | null | undefined, {} | null | undefined>): void;
    /**
     * Gets a NotebookRuntime.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.name
     *   Required. The name of the NotebookRuntime resource.
     *   Instead of checking whether the name is in valid NotebookRuntime resource
     *   name format, directly throw NotFound exception if there is no such
     *   NotebookRuntime in spanner.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing {@link protos.google.cloud.aiplatform.v1beta1.NotebookRuntime|NotebookRuntime}.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#regular-methods | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1beta1/notebook_service.get_notebook_runtime.js</caption>
     * region_tag:aiplatform_v1beta1_generated_NotebookService_GetNotebookRuntime_async
     */
    getNotebookRuntime(request?: protos.google.cloud.aiplatform.v1beta1.IGetNotebookRuntimeRequest, options?: CallOptions): Promise<[
        protos.google.cloud.aiplatform.v1beta1.INotebookRuntime,
        (protos.google.cloud.aiplatform.v1beta1.IGetNotebookRuntimeRequest | undefined),
        {} | undefined
    ]>;
    getNotebookRuntime(request: protos.google.cloud.aiplatform.v1beta1.IGetNotebookRuntimeRequest, options: CallOptions, callback: Callback<protos.google.cloud.aiplatform.v1beta1.INotebookRuntime, protos.google.cloud.aiplatform.v1beta1.IGetNotebookRuntimeRequest | null | undefined, {} | null | undefined>): void;
    getNotebookRuntime(request: protos.google.cloud.aiplatform.v1beta1.IGetNotebookRuntimeRequest, callback: Callback<protos.google.cloud.aiplatform.v1beta1.INotebookRuntime, protos.google.cloud.aiplatform.v1beta1.IGetNotebookRuntimeRequest | null | undefined, {} | null | undefined>): void;
    /**
     * Gets a NotebookExecutionJob.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.name
     *   Required. The name of the NotebookExecutionJob resource.
     * @param {google.cloud.aiplatform.v1beta1.NotebookExecutionJobView} [request.view]
     *   Optional. The NotebookExecutionJob view. Defaults to BASIC.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing {@link protos.google.cloud.aiplatform.v1beta1.NotebookExecutionJob|NotebookExecutionJob}.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#regular-methods | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1beta1/notebook_service.get_notebook_execution_job.js</caption>
     * region_tag:aiplatform_v1beta1_generated_NotebookService_GetNotebookExecutionJob_async
     */
    getNotebookExecutionJob(request?: protos.google.cloud.aiplatform.v1beta1.IGetNotebookExecutionJobRequest, options?: CallOptions): Promise<[
        protos.google.cloud.aiplatform.v1beta1.INotebookExecutionJob,
        (protos.google.cloud.aiplatform.v1beta1.IGetNotebookExecutionJobRequest | undefined),
        {} | undefined
    ]>;
    getNotebookExecutionJob(request: protos.google.cloud.aiplatform.v1beta1.IGetNotebookExecutionJobRequest, options: CallOptions, callback: Callback<protos.google.cloud.aiplatform.v1beta1.INotebookExecutionJob, protos.google.cloud.aiplatform.v1beta1.IGetNotebookExecutionJobRequest | null | undefined, {} | null | undefined>): void;
    getNotebookExecutionJob(request: protos.google.cloud.aiplatform.v1beta1.IGetNotebookExecutionJobRequest, callback: Callback<protos.google.cloud.aiplatform.v1beta1.INotebookExecutionJob, protos.google.cloud.aiplatform.v1beta1.IGetNotebookExecutionJobRequest | null | undefined, {} | null | undefined>): void;
    /**
     * Creates a NotebookRuntimeTemplate.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. The resource name of the Location to create the
     *   NotebookRuntimeTemplate. Format: `projects/{project}/locations/{location}`
     * @param {google.cloud.aiplatform.v1beta1.NotebookRuntimeTemplate} request.notebookRuntimeTemplate
     *   Required. The NotebookRuntimeTemplate to create.
     * @param {string} [request.notebookRuntimeTemplateId]
     *   Optional. User specified ID for the notebook runtime template.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing
     *   a long running operation. Its `promise()` method returns a promise
     *   you can `await` for.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#long-running-operations | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1beta1/notebook_service.create_notebook_runtime_template.js</caption>
     * region_tag:aiplatform_v1beta1_generated_NotebookService_CreateNotebookRuntimeTemplate_async
     */
    createNotebookRuntimeTemplate(request?: protos.google.cloud.aiplatform.v1beta1.ICreateNotebookRuntimeTemplateRequest, options?: CallOptions): Promise<[
        LROperation<protos.google.cloud.aiplatform.v1beta1.INotebookRuntimeTemplate, protos.google.cloud.aiplatform.v1beta1.ICreateNotebookRuntimeTemplateOperationMetadata>,
        protos.google.longrunning.IOperation | undefined,
        {} | undefined
    ]>;
    createNotebookRuntimeTemplate(request: protos.google.cloud.aiplatform.v1beta1.ICreateNotebookRuntimeTemplateRequest, options: CallOptions, callback: Callback<LROperation<protos.google.cloud.aiplatform.v1beta1.INotebookRuntimeTemplate, protos.google.cloud.aiplatform.v1beta1.ICreateNotebookRuntimeTemplateOperationMetadata>, protos.google.longrunning.IOperation | null | undefined, {} | null | undefined>): void;
    createNotebookRuntimeTemplate(request: protos.google.cloud.aiplatform.v1beta1.ICreateNotebookRuntimeTemplateRequest, callback: Callback<LROperation<protos.google.cloud.aiplatform.v1beta1.INotebookRuntimeTemplate, protos.google.cloud.aiplatform.v1beta1.ICreateNotebookRuntimeTemplateOperationMetadata>, protos.google.longrunning.IOperation | null | undefined, {} | null | undefined>): void;
    /**
     * Check the status of the long running operation returned by `createNotebookRuntimeTemplate()`.
     * @param {String} name
     *   The operation name that will be passed.
     * @returns {Promise} - The promise which resolves to an object.
     *   The decoded operation object has result and metadata field to get information from.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#long-running-operations | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1beta1/notebook_service.create_notebook_runtime_template.js</caption>
     * region_tag:aiplatform_v1beta1_generated_NotebookService_CreateNotebookRuntimeTemplate_async
     */
    checkCreateNotebookRuntimeTemplateProgress(name: string): Promise<LROperation<protos.google.cloud.aiplatform.v1beta1.NotebookRuntimeTemplate, protos.google.cloud.aiplatform.v1beta1.CreateNotebookRuntimeTemplateOperationMetadata>>;
    /**
     * Deletes a NotebookRuntimeTemplate.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.name
     *   Required. The name of the NotebookRuntimeTemplate resource to be deleted.
     *   Format:
     *   `projects/{project}/locations/{location}/notebookRuntimeTemplates/{notebook_runtime_template}`
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing
     *   a long running operation. Its `promise()` method returns a promise
     *   you can `await` for.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#long-running-operations | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1beta1/notebook_service.delete_notebook_runtime_template.js</caption>
     * region_tag:aiplatform_v1beta1_generated_NotebookService_DeleteNotebookRuntimeTemplate_async
     */
    deleteNotebookRuntimeTemplate(request?: protos.google.cloud.aiplatform.v1beta1.IDeleteNotebookRuntimeTemplateRequest, options?: CallOptions): Promise<[
        LROperation<protos.google.protobuf.IEmpty, protos.google.cloud.aiplatform.v1beta1.IDeleteOperationMetadata>,
        protos.google.longrunning.IOperation | undefined,
        {} | undefined
    ]>;
    deleteNotebookRuntimeTemplate(request: protos.google.cloud.aiplatform.v1beta1.IDeleteNotebookRuntimeTemplateRequest, options: CallOptions, callback: Callback<LROperation<protos.google.protobuf.IEmpty, protos.google.cloud.aiplatform.v1beta1.IDeleteOperationMetadata>, protos.google.longrunning.IOperation | null | undefined, {} | null | undefined>): void;
    deleteNotebookRuntimeTemplate(request: protos.google.cloud.aiplatform.v1beta1.IDeleteNotebookRuntimeTemplateRequest, callback: Callback<LROperation<protos.google.protobuf.IEmpty, protos.google.cloud.aiplatform.v1beta1.IDeleteOperationMetadata>, protos.google.longrunning.IOperation | null | undefined, {} | null | undefined>): void;
    /**
     * Check the status of the long running operation returned by `deleteNotebookRuntimeTemplate()`.
     * @param {String} name
     *   The operation name that will be passed.
     * @returns {Promise} - The promise which resolves to an object.
     *   The decoded operation object has result and metadata field to get information from.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#long-running-operations | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1beta1/notebook_service.delete_notebook_runtime_template.js</caption>
     * region_tag:aiplatform_v1beta1_generated_NotebookService_DeleteNotebookRuntimeTemplate_async
     */
    checkDeleteNotebookRuntimeTemplateProgress(name: string): Promise<LROperation<protos.google.protobuf.Empty, protos.google.cloud.aiplatform.v1beta1.DeleteOperationMetadata>>;
    /**
     * Assigns a NotebookRuntime to a user for a particular Notebook file. This
     * method will either returns an existing assignment or generates a new one.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. The resource name of the Location to get the NotebookRuntime
     *   assignment. Format: `projects/{project}/locations/{location}`
     * @param {string} request.notebookRuntimeTemplate
     *   Required. The resource name of the NotebookRuntimeTemplate based on which a
     *   NotebookRuntime will be assigned (reuse or create a new one).
     * @param {google.cloud.aiplatform.v1beta1.NotebookRuntime} request.notebookRuntime
     *   Required. Provide runtime specific information (e.g. runtime owner,
     *   notebook id) used for NotebookRuntime assignment.
     * @param {string} [request.notebookRuntimeId]
     *   Optional. User specified ID for the notebook runtime.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing
     *   a long running operation. Its `promise()` method returns a promise
     *   you can `await` for.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#long-running-operations | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1beta1/notebook_service.assign_notebook_runtime.js</caption>
     * region_tag:aiplatform_v1beta1_generated_NotebookService_AssignNotebookRuntime_async
     */
    assignNotebookRuntime(request?: protos.google.cloud.aiplatform.v1beta1.IAssignNotebookRuntimeRequest, options?: CallOptions): Promise<[
        LROperation<protos.google.cloud.aiplatform.v1beta1.INotebookRuntime, protos.google.cloud.aiplatform.v1beta1.IAssignNotebookRuntimeOperationMetadata>,
        protos.google.longrunning.IOperation | undefined,
        {} | undefined
    ]>;
    assignNotebookRuntime(request: protos.google.cloud.aiplatform.v1beta1.IAssignNotebookRuntimeRequest, options: CallOptions, callback: Callback<LROperation<protos.google.cloud.aiplatform.v1beta1.INotebookRuntime, protos.google.cloud.aiplatform.v1beta1.IAssignNotebookRuntimeOperationMetadata>, protos.google.longrunning.IOperation | null | undefined, {} | null | undefined>): void;
    assignNotebookRuntime(request: protos.google.cloud.aiplatform.v1beta1.IAssignNotebookRuntimeRequest, callback: Callback<LROperation<protos.google.cloud.aiplatform.v1beta1.INotebookRuntime, protos.google.cloud.aiplatform.v1beta1.IAssignNotebookRuntimeOperationMetadata>, protos.google.longrunning.IOperation | null | undefined, {} | null | undefined>): void;
    /**
     * Check the status of the long running operation returned by `assignNotebookRuntime()`.
     * @param {String} name
     *   The operation name that will be passed.
     * @returns {Promise} - The promise which resolves to an object.
     *   The decoded operation object has result and metadata field to get information from.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#long-running-operations | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1beta1/notebook_service.assign_notebook_runtime.js</caption>
     * region_tag:aiplatform_v1beta1_generated_NotebookService_AssignNotebookRuntime_async
     */
    checkAssignNotebookRuntimeProgress(name: string): Promise<LROperation<protos.google.cloud.aiplatform.v1beta1.NotebookRuntime, protos.google.cloud.aiplatform.v1beta1.AssignNotebookRuntimeOperationMetadata>>;
    /**
     * Deletes a NotebookRuntime.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.name
     *   Required. The name of the NotebookRuntime resource to be deleted.
     *   Instead of checking whether the name is in valid NotebookRuntime resource
     *   name format, directly throw NotFound exception if there is no such
     *   NotebookRuntime in spanner.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing
     *   a long running operation. Its `promise()` method returns a promise
     *   you can `await` for.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#long-running-operations | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1beta1/notebook_service.delete_notebook_runtime.js</caption>
     * region_tag:aiplatform_v1beta1_generated_NotebookService_DeleteNotebookRuntime_async
     */
    deleteNotebookRuntime(request?: protos.google.cloud.aiplatform.v1beta1.IDeleteNotebookRuntimeRequest, options?: CallOptions): Promise<[
        LROperation<protos.google.protobuf.IEmpty, protos.google.cloud.aiplatform.v1beta1.IDeleteOperationMetadata>,
        protos.google.longrunning.IOperation | undefined,
        {} | undefined
    ]>;
    deleteNotebookRuntime(request: protos.google.cloud.aiplatform.v1beta1.IDeleteNotebookRuntimeRequest, options: CallOptions, callback: Callback<LROperation<protos.google.protobuf.IEmpty, protos.google.cloud.aiplatform.v1beta1.IDeleteOperationMetadata>, protos.google.longrunning.IOperation | null | undefined, {} | null | undefined>): void;
    deleteNotebookRuntime(request: protos.google.cloud.aiplatform.v1beta1.IDeleteNotebookRuntimeRequest, callback: Callback<LROperation<protos.google.protobuf.IEmpty, protos.google.cloud.aiplatform.v1beta1.IDeleteOperationMetadata>, protos.google.longrunning.IOperation | null | undefined, {} | null | undefined>): void;
    /**
     * Check the status of the long running operation returned by `deleteNotebookRuntime()`.
     * @param {String} name
     *   The operation name that will be passed.
     * @returns {Promise} - The promise which resolves to an object.
     *   The decoded operation object has result and metadata field to get information from.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#long-running-operations | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1beta1/notebook_service.delete_notebook_runtime.js</caption>
     * region_tag:aiplatform_v1beta1_generated_NotebookService_DeleteNotebookRuntime_async
     */
    checkDeleteNotebookRuntimeProgress(name: string): Promise<LROperation<protos.google.protobuf.Empty, protos.google.cloud.aiplatform.v1beta1.DeleteOperationMetadata>>;
    /**
     * Upgrades a NotebookRuntime.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.name
     *   Required. The name of the NotebookRuntime resource to be upgrade.
     *   Instead of checking whether the name is in valid NotebookRuntime resource
     *   name format, directly throw NotFound exception if there is no such
     *   NotebookRuntime in spanner.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing
     *   a long running operation. Its `promise()` method returns a promise
     *   you can `await` for.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#long-running-operations | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1beta1/notebook_service.upgrade_notebook_runtime.js</caption>
     * region_tag:aiplatform_v1beta1_generated_NotebookService_UpgradeNotebookRuntime_async
     */
    upgradeNotebookRuntime(request?: protos.google.cloud.aiplatform.v1beta1.IUpgradeNotebookRuntimeRequest, options?: CallOptions): Promise<[
        LROperation<protos.google.cloud.aiplatform.v1beta1.IUpgradeNotebookRuntimeResponse, protos.google.cloud.aiplatform.v1beta1.IUpgradeNotebookRuntimeOperationMetadata>,
        protos.google.longrunning.IOperation | undefined,
        {} | undefined
    ]>;
    upgradeNotebookRuntime(request: protos.google.cloud.aiplatform.v1beta1.IUpgradeNotebookRuntimeRequest, options: CallOptions, callback: Callback<LROperation<protos.google.cloud.aiplatform.v1beta1.IUpgradeNotebookRuntimeResponse, protos.google.cloud.aiplatform.v1beta1.IUpgradeNotebookRuntimeOperationMetadata>, protos.google.longrunning.IOperation | null | undefined, {} | null | undefined>): void;
    upgradeNotebookRuntime(request: protos.google.cloud.aiplatform.v1beta1.IUpgradeNotebookRuntimeRequest, callback: Callback<LROperation<protos.google.cloud.aiplatform.v1beta1.IUpgradeNotebookRuntimeResponse, protos.google.cloud.aiplatform.v1beta1.IUpgradeNotebookRuntimeOperationMetadata>, protos.google.longrunning.IOperation | null | undefined, {} | null | undefined>): void;
    /**
     * Check the status of the long running operation returned by `upgradeNotebookRuntime()`.
     * @param {String} name
     *   The operation name that will be passed.
     * @returns {Promise} - The promise which resolves to an object.
     *   The decoded operation object has result and metadata field to get information from.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#long-running-operations | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1beta1/notebook_service.upgrade_notebook_runtime.js</caption>
     * region_tag:aiplatform_v1beta1_generated_NotebookService_UpgradeNotebookRuntime_async
     */
    checkUpgradeNotebookRuntimeProgress(name: string): Promise<LROperation<protos.google.cloud.aiplatform.v1beta1.UpgradeNotebookRuntimeResponse, protos.google.cloud.aiplatform.v1beta1.UpgradeNotebookRuntimeOperationMetadata>>;
    /**
     * Starts a NotebookRuntime.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.name
     *   Required. The name of the NotebookRuntime resource to be started.
     *   Instead of checking whether the name is in valid NotebookRuntime resource
     *   name format, directly throw NotFound exception if there is no such
     *   NotebookRuntime in spanner.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing
     *   a long running operation. Its `promise()` method returns a promise
     *   you can `await` for.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#long-running-operations | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1beta1/notebook_service.start_notebook_runtime.js</caption>
     * region_tag:aiplatform_v1beta1_generated_NotebookService_StartNotebookRuntime_async
     */
    startNotebookRuntime(request?: protos.google.cloud.aiplatform.v1beta1.IStartNotebookRuntimeRequest, options?: CallOptions): Promise<[
        LROperation<protos.google.cloud.aiplatform.v1beta1.IStartNotebookRuntimeResponse, protos.google.cloud.aiplatform.v1beta1.IStartNotebookRuntimeOperationMetadata>,
        protos.google.longrunning.IOperation | undefined,
        {} | undefined
    ]>;
    startNotebookRuntime(request: protos.google.cloud.aiplatform.v1beta1.IStartNotebookRuntimeRequest, options: CallOptions, callback: Callback<LROperation<protos.google.cloud.aiplatform.v1beta1.IStartNotebookRuntimeResponse, protos.google.cloud.aiplatform.v1beta1.IStartNotebookRuntimeOperationMetadata>, protos.google.longrunning.IOperation | null | undefined, {} | null | undefined>): void;
    startNotebookRuntime(request: protos.google.cloud.aiplatform.v1beta1.IStartNotebookRuntimeRequest, callback: Callback<LROperation<protos.google.cloud.aiplatform.v1beta1.IStartNotebookRuntimeResponse, protos.google.cloud.aiplatform.v1beta1.IStartNotebookRuntimeOperationMetadata>, protos.google.longrunning.IOperation | null | undefined, {} | null | undefined>): void;
    /**
     * Check the status of the long running operation returned by `startNotebookRuntime()`.
     * @param {String} name
     *   The operation name that will be passed.
     * @returns {Promise} - The promise which resolves to an object.
     *   The decoded operation object has result and metadata field to get information from.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#long-running-operations | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1beta1/notebook_service.start_notebook_runtime.js</caption>
     * region_tag:aiplatform_v1beta1_generated_NotebookService_StartNotebookRuntime_async
     */
    checkStartNotebookRuntimeProgress(name: string): Promise<LROperation<protos.google.cloud.aiplatform.v1beta1.StartNotebookRuntimeResponse, protos.google.cloud.aiplatform.v1beta1.StartNotebookRuntimeOperationMetadata>>;
    /**
     * Stops a NotebookRuntime.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.name
     *   Required. The name of the NotebookRuntime resource to be stopped.
     *   Instead of checking whether the name is in valid NotebookRuntime resource
     *   name format, directly throw NotFound exception if there is no such
     *   NotebookRuntime in spanner.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing
     *   a long running operation. Its `promise()` method returns a promise
     *   you can `await` for.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#long-running-operations | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1beta1/notebook_service.stop_notebook_runtime.js</caption>
     * region_tag:aiplatform_v1beta1_generated_NotebookService_StopNotebookRuntime_async
     */
    stopNotebookRuntime(request?: protos.google.cloud.aiplatform.v1beta1.IStopNotebookRuntimeRequest, options?: CallOptions): Promise<[
        LROperation<protos.google.cloud.aiplatform.v1beta1.IStopNotebookRuntimeResponse, protos.google.cloud.aiplatform.v1beta1.IStopNotebookRuntimeOperationMetadata>,
        protos.google.longrunning.IOperation | undefined,
        {} | undefined
    ]>;
    stopNotebookRuntime(request: protos.google.cloud.aiplatform.v1beta1.IStopNotebookRuntimeRequest, options: CallOptions, callback: Callback<LROperation<protos.google.cloud.aiplatform.v1beta1.IStopNotebookRuntimeResponse, protos.google.cloud.aiplatform.v1beta1.IStopNotebookRuntimeOperationMetadata>, protos.google.longrunning.IOperation | null | undefined, {} | null | undefined>): void;
    stopNotebookRuntime(request: protos.google.cloud.aiplatform.v1beta1.IStopNotebookRuntimeRequest, callback: Callback<LROperation<protos.google.cloud.aiplatform.v1beta1.IStopNotebookRuntimeResponse, protos.google.cloud.aiplatform.v1beta1.IStopNotebookRuntimeOperationMetadata>, protos.google.longrunning.IOperation | null | undefined, {} | null | undefined>): void;
    /**
     * Check the status of the long running operation returned by `stopNotebookRuntime()`.
     * @param {String} name
     *   The operation name that will be passed.
     * @returns {Promise} - The promise which resolves to an object.
     *   The decoded operation object has result and metadata field to get information from.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#long-running-operations | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1beta1/notebook_service.stop_notebook_runtime.js</caption>
     * region_tag:aiplatform_v1beta1_generated_NotebookService_StopNotebookRuntime_async
     */
    checkStopNotebookRuntimeProgress(name: string): Promise<LROperation<protos.google.cloud.aiplatform.v1beta1.StopNotebookRuntimeResponse, protos.google.cloud.aiplatform.v1beta1.StopNotebookRuntimeOperationMetadata>>;
    /**
     * Creates a NotebookExecutionJob.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. The resource name of the Location to create the
     *   NotebookExecutionJob. Format: `projects/{project}/locations/{location}`
     * @param {google.cloud.aiplatform.v1beta1.NotebookExecutionJob} request.notebookExecutionJob
     *   Required. The NotebookExecutionJob to create.
     * @param {string} [request.notebookExecutionJobId]
     *   Optional. User specified ID for the NotebookExecutionJob.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing
     *   a long running operation. Its `promise()` method returns a promise
     *   you can `await` for.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#long-running-operations | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1beta1/notebook_service.create_notebook_execution_job.js</caption>
     * region_tag:aiplatform_v1beta1_generated_NotebookService_CreateNotebookExecutionJob_async
     */
    createNotebookExecutionJob(request?: protos.google.cloud.aiplatform.v1beta1.ICreateNotebookExecutionJobRequest, options?: CallOptions): Promise<[
        LROperation<protos.google.cloud.aiplatform.v1beta1.INotebookExecutionJob, protos.google.cloud.aiplatform.v1beta1.ICreateNotebookExecutionJobOperationMetadata>,
        protos.google.longrunning.IOperation | undefined,
        {} | undefined
    ]>;
    createNotebookExecutionJob(request: protos.google.cloud.aiplatform.v1beta1.ICreateNotebookExecutionJobRequest, options: CallOptions, callback: Callback<LROperation<protos.google.cloud.aiplatform.v1beta1.INotebookExecutionJob, protos.google.cloud.aiplatform.v1beta1.ICreateNotebookExecutionJobOperationMetadata>, protos.google.longrunning.IOperation | null | undefined, {} | null | undefined>): void;
    createNotebookExecutionJob(request: protos.google.cloud.aiplatform.v1beta1.ICreateNotebookExecutionJobRequest, callback: Callback<LROperation<protos.google.cloud.aiplatform.v1beta1.INotebookExecutionJob, protos.google.cloud.aiplatform.v1beta1.ICreateNotebookExecutionJobOperationMetadata>, protos.google.longrunning.IOperation | null | undefined, {} | null | undefined>): void;
    /**
     * Check the status of the long running operation returned by `createNotebookExecutionJob()`.
     * @param {String} name
     *   The operation name that will be passed.
     * @returns {Promise} - The promise which resolves to an object.
     *   The decoded operation object has result and metadata field to get information from.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#long-running-operations | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1beta1/notebook_service.create_notebook_execution_job.js</caption>
     * region_tag:aiplatform_v1beta1_generated_NotebookService_CreateNotebookExecutionJob_async
     */
    checkCreateNotebookExecutionJobProgress(name: string): Promise<LROperation<protos.google.cloud.aiplatform.v1beta1.NotebookExecutionJob, protos.google.cloud.aiplatform.v1beta1.CreateNotebookExecutionJobOperationMetadata>>;
    /**
     * Deletes a NotebookExecutionJob.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.name
     *   Required. The name of the NotebookExecutionJob resource to be deleted.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing
     *   a long running operation. Its `promise()` method returns a promise
     *   you can `await` for.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#long-running-operations | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1beta1/notebook_service.delete_notebook_execution_job.js</caption>
     * region_tag:aiplatform_v1beta1_generated_NotebookService_DeleteNotebookExecutionJob_async
     */
    deleteNotebookExecutionJob(request?: protos.google.cloud.aiplatform.v1beta1.IDeleteNotebookExecutionJobRequest, options?: CallOptions): Promise<[
        LROperation<protos.google.protobuf.IEmpty, protos.google.cloud.aiplatform.v1beta1.IDeleteOperationMetadata>,
        protos.google.longrunning.IOperation | undefined,
        {} | undefined
    ]>;
    deleteNotebookExecutionJob(request: protos.google.cloud.aiplatform.v1beta1.IDeleteNotebookExecutionJobRequest, options: CallOptions, callback: Callback<LROperation<protos.google.protobuf.IEmpty, protos.google.cloud.aiplatform.v1beta1.IDeleteOperationMetadata>, protos.google.longrunning.IOperation | null | undefined, {} | null | undefined>): void;
    deleteNotebookExecutionJob(request: protos.google.cloud.aiplatform.v1beta1.IDeleteNotebookExecutionJobRequest, callback: Callback<LROperation<protos.google.protobuf.IEmpty, protos.google.cloud.aiplatform.v1beta1.IDeleteOperationMetadata>, protos.google.longrunning.IOperation | null | undefined, {} | null | undefined>): void;
    /**
     * Check the status of the long running operation returned by `deleteNotebookExecutionJob()`.
     * @param {String} name
     *   The operation name that will be passed.
     * @returns {Promise} - The promise which resolves to an object.
     *   The decoded operation object has result and metadata field to get information from.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#long-running-operations | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1beta1/notebook_service.delete_notebook_execution_job.js</caption>
     * region_tag:aiplatform_v1beta1_generated_NotebookService_DeleteNotebookExecutionJob_async
     */
    checkDeleteNotebookExecutionJobProgress(name: string): Promise<LROperation<protos.google.protobuf.Empty, protos.google.cloud.aiplatform.v1beta1.DeleteOperationMetadata>>;
    /**
     * Lists NotebookRuntimeTemplates in a Location.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. The resource name of the Location from which to list the
     *   NotebookRuntimeTemplates.
     *   Format: `projects/{project}/locations/{location}`
     * @param {string} [request.filter]
     *   Optional. An expression for filtering the results of the request. For field
     *   names both snake_case and camelCase are supported.
     *
     *     * `notebookRuntimeTemplate` supports = and !=. `notebookRuntimeTemplate`
     *       represents the NotebookRuntimeTemplate ID,
     *       i.e. the last segment of the NotebookRuntimeTemplate's [resource name]
     *       [google.cloud.aiplatform.v1beta1.NotebookRuntimeTemplate.name].
     *     * `display_name` supports = and !=
     *     * `labels` supports general map functions that is:
     *       * `labels.key=value` - key:value equality
     *       * `labels.key:* or labels:key - key existence
     *       * A key including a space must be quoted. `labels."a key"`.
     *     * `notebookRuntimeType` supports = and !=. notebookRuntimeType enum:
     *     [USER_DEFINED, ONE_CLICK].
     *     * `machineType` supports = and !=.
     *     * `acceleratorType` supports = and !=.
     *
     *   Some examples:
     *
     *     * `notebookRuntimeTemplate=notebookRuntimeTemplate123`
     *     * `displayName="myDisplayName"`
     *     * `labels.myKey="myValue"`
     *     * `notebookRuntimeType=USER_DEFINED`
     *     * `machineType=e2-standard-4`
     *     * `acceleratorType=NVIDIA_TESLA_T4`
     * @param {number} [request.pageSize]
     *   Optional. The standard list page size.
     * @param {string} [request.pageToken]
     *   Optional. The standard list page token.
     *   Typically obtained via
     *   {@link protos.google.cloud.aiplatform.v1beta1.ListNotebookRuntimeTemplatesResponse.next_page_token|ListNotebookRuntimeTemplatesResponse.next_page_token}
     *   of the previous
     *   {@link protos.google.cloud.aiplatform.v1beta1.NotebookService.ListNotebookRuntimeTemplates|NotebookService.ListNotebookRuntimeTemplates}
     *   call.
     * @param {google.protobuf.FieldMask} [request.readMask]
     *   Optional. Mask specifying which fields to read.
     * @param {string} [request.orderBy]
     *   Optional. A comma-separated list of fields to order by, sorted in ascending
     *   order. Use "desc" after a field name for descending. Supported fields:
     *
     *     * `display_name`
     *     * `create_time`
     *     * `update_time`
     *
     *   Example: `display_name, create_time desc`.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is Array of {@link protos.google.cloud.aiplatform.v1beta1.NotebookRuntimeTemplate|NotebookRuntimeTemplate}.
     *   The client library will perform auto-pagination by default: it will call the API as many
     *   times as needed and will merge results from all the pages into this array.
     *   Note that it can affect your quota.
     *   We recommend using `listNotebookRuntimeTemplatesAsync()`
     *   method described below for async iteration which you can stop as needed.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     */
    listNotebookRuntimeTemplates(request?: protos.google.cloud.aiplatform.v1beta1.IListNotebookRuntimeTemplatesRequest, options?: CallOptions): Promise<[
        protos.google.cloud.aiplatform.v1beta1.INotebookRuntimeTemplate[],
        protos.google.cloud.aiplatform.v1beta1.IListNotebookRuntimeTemplatesRequest | null,
        protos.google.cloud.aiplatform.v1beta1.IListNotebookRuntimeTemplatesResponse
    ]>;
    listNotebookRuntimeTemplates(request: protos.google.cloud.aiplatform.v1beta1.IListNotebookRuntimeTemplatesRequest, options: CallOptions, callback: PaginationCallback<protos.google.cloud.aiplatform.v1beta1.IListNotebookRuntimeTemplatesRequest, protos.google.cloud.aiplatform.v1beta1.IListNotebookRuntimeTemplatesResponse | null | undefined, protos.google.cloud.aiplatform.v1beta1.INotebookRuntimeTemplate>): void;
    listNotebookRuntimeTemplates(request: protos.google.cloud.aiplatform.v1beta1.IListNotebookRuntimeTemplatesRequest, callback: PaginationCallback<protos.google.cloud.aiplatform.v1beta1.IListNotebookRuntimeTemplatesRequest, protos.google.cloud.aiplatform.v1beta1.IListNotebookRuntimeTemplatesResponse | null | undefined, protos.google.cloud.aiplatform.v1beta1.INotebookRuntimeTemplate>): void;
    /**
     * Equivalent to `listNotebookRuntimeTemplates`, but returns a NodeJS Stream object.
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. The resource name of the Location from which to list the
     *   NotebookRuntimeTemplates.
     *   Format: `projects/{project}/locations/{location}`
     * @param {string} [request.filter]
     *   Optional. An expression for filtering the results of the request. For field
     *   names both snake_case and camelCase are supported.
     *
     *     * `notebookRuntimeTemplate` supports = and !=. `notebookRuntimeTemplate`
     *       represents the NotebookRuntimeTemplate ID,
     *       i.e. the last segment of the NotebookRuntimeTemplate's [resource name]
     *       [google.cloud.aiplatform.v1beta1.NotebookRuntimeTemplate.name].
     *     * `display_name` supports = and !=
     *     * `labels` supports general map functions that is:
     *       * `labels.key=value` - key:value equality
     *       * `labels.key:* or labels:key - key existence
     *       * A key including a space must be quoted. `labels."a key"`.
     *     * `notebookRuntimeType` supports = and !=. notebookRuntimeType enum:
     *     [USER_DEFINED, ONE_CLICK].
     *     * `machineType` supports = and !=.
     *     * `acceleratorType` supports = and !=.
     *
     *   Some examples:
     *
     *     * `notebookRuntimeTemplate=notebookRuntimeTemplate123`
     *     * `displayName="myDisplayName"`
     *     * `labels.myKey="myValue"`
     *     * `notebookRuntimeType=USER_DEFINED`
     *     * `machineType=e2-standard-4`
     *     * `acceleratorType=NVIDIA_TESLA_T4`
     * @param {number} [request.pageSize]
     *   Optional. The standard list page size.
     * @param {string} [request.pageToken]
     *   Optional. The standard list page token.
     *   Typically obtained via
     *   {@link protos.google.cloud.aiplatform.v1beta1.ListNotebookRuntimeTemplatesResponse.next_page_token|ListNotebookRuntimeTemplatesResponse.next_page_token}
     *   of the previous
     *   {@link protos.google.cloud.aiplatform.v1beta1.NotebookService.ListNotebookRuntimeTemplates|NotebookService.ListNotebookRuntimeTemplates}
     *   call.
     * @param {google.protobuf.FieldMask} [request.readMask]
     *   Optional. Mask specifying which fields to read.
     * @param {string} [request.orderBy]
     *   Optional. A comma-separated list of fields to order by, sorted in ascending
     *   order. Use "desc" after a field name for descending. Supported fields:
     *
     *     * `display_name`
     *     * `create_time`
     *     * `update_time`
     *
     *   Example: `display_name, create_time desc`.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Stream}
     *   An object stream which emits an object representing {@link protos.google.cloud.aiplatform.v1beta1.NotebookRuntimeTemplate|NotebookRuntimeTemplate} on 'data' event.
     *   The client library will perform auto-pagination by default: it will call the API as many
     *   times as needed. Note that it can affect your quota.
     *   We recommend using `listNotebookRuntimeTemplatesAsync()`
     *   method described below for async iteration which you can stop as needed.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     */
    listNotebookRuntimeTemplatesStream(request?: protos.google.cloud.aiplatform.v1beta1.IListNotebookRuntimeTemplatesRequest, options?: CallOptions): Transform;
    /**
     * Equivalent to `listNotebookRuntimeTemplates`, but returns an iterable object.
     *
     * `for`-`await`-`of` syntax is used with the iterable to get response elements on-demand.
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. The resource name of the Location from which to list the
     *   NotebookRuntimeTemplates.
     *   Format: `projects/{project}/locations/{location}`
     * @param {string} [request.filter]
     *   Optional. An expression for filtering the results of the request. For field
     *   names both snake_case and camelCase are supported.
     *
     *     * `notebookRuntimeTemplate` supports = and !=. `notebookRuntimeTemplate`
     *       represents the NotebookRuntimeTemplate ID,
     *       i.e. the last segment of the NotebookRuntimeTemplate's [resource name]
     *       [google.cloud.aiplatform.v1beta1.NotebookRuntimeTemplate.name].
     *     * `display_name` supports = and !=
     *     * `labels` supports general map functions that is:
     *       * `labels.key=value` - key:value equality
     *       * `labels.key:* or labels:key - key existence
     *       * A key including a space must be quoted. `labels."a key"`.
     *     * `notebookRuntimeType` supports = and !=. notebookRuntimeType enum:
     *     [USER_DEFINED, ONE_CLICK].
     *     * `machineType` supports = and !=.
     *     * `acceleratorType` supports = and !=.
     *
     *   Some examples:
     *
     *     * `notebookRuntimeTemplate=notebookRuntimeTemplate123`
     *     * `displayName="myDisplayName"`
     *     * `labels.myKey="myValue"`
     *     * `notebookRuntimeType=USER_DEFINED`
     *     * `machineType=e2-standard-4`
     *     * `acceleratorType=NVIDIA_TESLA_T4`
     * @param {number} [request.pageSize]
     *   Optional. The standard list page size.
     * @param {string} [request.pageToken]
     *   Optional. The standard list page token.
     *   Typically obtained via
     *   {@link protos.google.cloud.aiplatform.v1beta1.ListNotebookRuntimeTemplatesResponse.next_page_token|ListNotebookRuntimeTemplatesResponse.next_page_token}
     *   of the previous
     *   {@link protos.google.cloud.aiplatform.v1beta1.NotebookService.ListNotebookRuntimeTemplates|NotebookService.ListNotebookRuntimeTemplates}
     *   call.
     * @param {google.protobuf.FieldMask} [request.readMask]
     *   Optional. Mask specifying which fields to read.
     * @param {string} [request.orderBy]
     *   Optional. A comma-separated list of fields to order by, sorted in ascending
     *   order. Use "desc" after a field name for descending. Supported fields:
     *
     *     * `display_name`
     *     * `create_time`
     *     * `update_time`
     *
     *   Example: `display_name, create_time desc`.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Object}
     *   An iterable Object that allows {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Iteration_protocols | async iteration }.
     *   When you iterate the returned iterable, each element will be an object representing
     *   {@link protos.google.cloud.aiplatform.v1beta1.NotebookRuntimeTemplate|NotebookRuntimeTemplate}. The API will be called under the hood as needed, once per the page,
     *   so you can stop the iteration when you don't need more results.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1beta1/notebook_service.list_notebook_runtime_templates.js</caption>
     * region_tag:aiplatform_v1beta1_generated_NotebookService_ListNotebookRuntimeTemplates_async
     */
    listNotebookRuntimeTemplatesAsync(request?: protos.google.cloud.aiplatform.v1beta1.IListNotebookRuntimeTemplatesRequest, options?: CallOptions): AsyncIterable<protos.google.cloud.aiplatform.v1beta1.INotebookRuntimeTemplate>;
    /**
     * Lists NotebookRuntimes in a Location.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. The resource name of the Location from which to list the
     *   NotebookRuntimes.
     *   Format: `projects/{project}/locations/{location}`
     * @param {string} [request.filter]
     *   Optional. An expression for filtering the results of the request. For field
     *   names both snake_case and camelCase are supported.
     *
     *     * `notebookRuntime` supports = and !=. `notebookRuntime` represents the
     *       NotebookRuntime ID,
     *       i.e. the last segment of the NotebookRuntime's [resource name]
     *       [google.cloud.aiplatform.v1beta1.NotebookRuntime.name].
     *     * `displayName` supports = and != and regex.
     *     * `notebookRuntimeTemplate` supports = and !=. `notebookRuntimeTemplate`
     *       represents the NotebookRuntimeTemplate ID,
     *       i.e. the last segment of the NotebookRuntimeTemplate's [resource name]
     *       [google.cloud.aiplatform.v1beta1.NotebookRuntimeTemplate.name].
     *     * `healthState` supports = and !=. healthState enum: [HEALTHY, UNHEALTHY,
     *     HEALTH_STATE_UNSPECIFIED].
     *     * `runtimeState` supports = and !=. runtimeState enum:
     *     [RUNTIME_STATE_UNSPECIFIED, RUNNING, BEING_STARTED, BEING_STOPPED,
     *     STOPPED, BEING_UPGRADED, ERROR, INVALID].
     *     * `runtimeUser` supports = and !=.
     *     * API version is UI only: `uiState` supports = and !=. uiState enum:
     *     [UI_RESOURCE_STATE_UNSPECIFIED, UI_RESOURCE_STATE_BEING_CREATED,
     *     UI_RESOURCE_STATE_ACTIVE, UI_RESOURCE_STATE_BEING_DELETED,
     *     UI_RESOURCE_STATE_CREATION_FAILED].
     *     * `notebookRuntimeType` supports = and !=. notebookRuntimeType enum:
     *     [USER_DEFINED, ONE_CLICK].
     *     * `machineType` supports = and !=.
     *     * `acceleratorType` supports = and !=.
     *
     *   Some examples:
     *
     *     * `notebookRuntime="notebookRuntime123"`
     *     * `displayName="myDisplayName"` and `displayName=~"myDisplayNameRegex"`
     *     * `notebookRuntimeTemplate="notebookRuntimeTemplate321"`
     *     * `healthState=HEALTHY`
     *     * `runtimeState=RUNNING`
     *     * `runtimeUser="<EMAIL>"`
     *     * `uiState=UI_RESOURCE_STATE_BEING_DELETED`
     *     * `notebookRuntimeType=USER_DEFINED`
     *     * `machineType=e2-standard-4`
     *     * `acceleratorType=NVIDIA_TESLA_T4`
     * @param {number} [request.pageSize]
     *   Optional. The standard list page size.
     * @param {string} [request.pageToken]
     *   Optional. The standard list page token.
     *   Typically obtained via
     *   {@link protos.google.cloud.aiplatform.v1beta1.ListNotebookRuntimesResponse.next_page_token|ListNotebookRuntimesResponse.next_page_token}
     *   of the previous
     *   {@link protos.google.cloud.aiplatform.v1beta1.NotebookService.ListNotebookRuntimes|NotebookService.ListNotebookRuntimes}
     *   call.
     * @param {google.protobuf.FieldMask} [request.readMask]
     *   Optional. Mask specifying which fields to read.
     * @param {string} [request.orderBy]
     *   Optional. A comma-separated list of fields to order by, sorted in ascending
     *   order. Use "desc" after a field name for descending. Supported fields:
     *
     *     * `display_name`
     *     * `create_time`
     *     * `update_time`
     *
     *   Example: `display_name, create_time desc`.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is Array of {@link protos.google.cloud.aiplatform.v1beta1.NotebookRuntime|NotebookRuntime}.
     *   The client library will perform auto-pagination by default: it will call the API as many
     *   times as needed and will merge results from all the pages into this array.
     *   Note that it can affect your quota.
     *   We recommend using `listNotebookRuntimesAsync()`
     *   method described below for async iteration which you can stop as needed.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     */
    listNotebookRuntimes(request?: protos.google.cloud.aiplatform.v1beta1.IListNotebookRuntimesRequest, options?: CallOptions): Promise<[
        protos.google.cloud.aiplatform.v1beta1.INotebookRuntime[],
        protos.google.cloud.aiplatform.v1beta1.IListNotebookRuntimesRequest | null,
        protos.google.cloud.aiplatform.v1beta1.IListNotebookRuntimesResponse
    ]>;
    listNotebookRuntimes(request: protos.google.cloud.aiplatform.v1beta1.IListNotebookRuntimesRequest, options: CallOptions, callback: PaginationCallback<protos.google.cloud.aiplatform.v1beta1.IListNotebookRuntimesRequest, protos.google.cloud.aiplatform.v1beta1.IListNotebookRuntimesResponse | null | undefined, protos.google.cloud.aiplatform.v1beta1.INotebookRuntime>): void;
    listNotebookRuntimes(request: protos.google.cloud.aiplatform.v1beta1.IListNotebookRuntimesRequest, callback: PaginationCallback<protos.google.cloud.aiplatform.v1beta1.IListNotebookRuntimesRequest, protos.google.cloud.aiplatform.v1beta1.IListNotebookRuntimesResponse | null | undefined, protos.google.cloud.aiplatform.v1beta1.INotebookRuntime>): void;
    /**
     * Equivalent to `listNotebookRuntimes`, but returns a NodeJS Stream object.
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. The resource name of the Location from which to list the
     *   NotebookRuntimes.
     *   Format: `projects/{project}/locations/{location}`
     * @param {string} [request.filter]
     *   Optional. An expression for filtering the results of the request. For field
     *   names both snake_case and camelCase are supported.
     *
     *     * `notebookRuntime` supports = and !=. `notebookRuntime` represents the
     *       NotebookRuntime ID,
     *       i.e. the last segment of the NotebookRuntime's [resource name]
     *       [google.cloud.aiplatform.v1beta1.NotebookRuntime.name].
     *     * `displayName` supports = and != and regex.
     *     * `notebookRuntimeTemplate` supports = and !=. `notebookRuntimeTemplate`
     *       represents the NotebookRuntimeTemplate ID,
     *       i.e. the last segment of the NotebookRuntimeTemplate's [resource name]
     *       [google.cloud.aiplatform.v1beta1.NotebookRuntimeTemplate.name].
     *     * `healthState` supports = and !=. healthState enum: [HEALTHY, UNHEALTHY,
     *     HEALTH_STATE_UNSPECIFIED].
     *     * `runtimeState` supports = and !=. runtimeState enum:
     *     [RUNTIME_STATE_UNSPECIFIED, RUNNING, BEING_STARTED, BEING_STOPPED,
     *     STOPPED, BEING_UPGRADED, ERROR, INVALID].
     *     * `runtimeUser` supports = and !=.
     *     * API version is UI only: `uiState` supports = and !=. uiState enum:
     *     [UI_RESOURCE_STATE_UNSPECIFIED, UI_RESOURCE_STATE_BEING_CREATED,
     *     UI_RESOURCE_STATE_ACTIVE, UI_RESOURCE_STATE_BEING_DELETED,
     *     UI_RESOURCE_STATE_CREATION_FAILED].
     *     * `notebookRuntimeType` supports = and !=. notebookRuntimeType enum:
     *     [USER_DEFINED, ONE_CLICK].
     *     * `machineType` supports = and !=.
     *     * `acceleratorType` supports = and !=.
     *
     *   Some examples:
     *
     *     * `notebookRuntime="notebookRuntime123"`
     *     * `displayName="myDisplayName"` and `displayName=~"myDisplayNameRegex"`
     *     * `notebookRuntimeTemplate="notebookRuntimeTemplate321"`
     *     * `healthState=HEALTHY`
     *     * `runtimeState=RUNNING`
     *     * `runtimeUser="<EMAIL>"`
     *     * `uiState=UI_RESOURCE_STATE_BEING_DELETED`
     *     * `notebookRuntimeType=USER_DEFINED`
     *     * `machineType=e2-standard-4`
     *     * `acceleratorType=NVIDIA_TESLA_T4`
     * @param {number} [request.pageSize]
     *   Optional. The standard list page size.
     * @param {string} [request.pageToken]
     *   Optional. The standard list page token.
     *   Typically obtained via
     *   {@link protos.google.cloud.aiplatform.v1beta1.ListNotebookRuntimesResponse.next_page_token|ListNotebookRuntimesResponse.next_page_token}
     *   of the previous
     *   {@link protos.google.cloud.aiplatform.v1beta1.NotebookService.ListNotebookRuntimes|NotebookService.ListNotebookRuntimes}
     *   call.
     * @param {google.protobuf.FieldMask} [request.readMask]
     *   Optional. Mask specifying which fields to read.
     * @param {string} [request.orderBy]
     *   Optional. A comma-separated list of fields to order by, sorted in ascending
     *   order. Use "desc" after a field name for descending. Supported fields:
     *
     *     * `display_name`
     *     * `create_time`
     *     * `update_time`
     *
     *   Example: `display_name, create_time desc`.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Stream}
     *   An object stream which emits an object representing {@link protos.google.cloud.aiplatform.v1beta1.NotebookRuntime|NotebookRuntime} on 'data' event.
     *   The client library will perform auto-pagination by default: it will call the API as many
     *   times as needed. Note that it can affect your quota.
     *   We recommend using `listNotebookRuntimesAsync()`
     *   method described below for async iteration which you can stop as needed.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     */
    listNotebookRuntimesStream(request?: protos.google.cloud.aiplatform.v1beta1.IListNotebookRuntimesRequest, options?: CallOptions): Transform;
    /**
     * Equivalent to `listNotebookRuntimes`, but returns an iterable object.
     *
     * `for`-`await`-`of` syntax is used with the iterable to get response elements on-demand.
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. The resource name of the Location from which to list the
     *   NotebookRuntimes.
     *   Format: `projects/{project}/locations/{location}`
     * @param {string} [request.filter]
     *   Optional. An expression for filtering the results of the request. For field
     *   names both snake_case and camelCase are supported.
     *
     *     * `notebookRuntime` supports = and !=. `notebookRuntime` represents the
     *       NotebookRuntime ID,
     *       i.e. the last segment of the NotebookRuntime's [resource name]
     *       [google.cloud.aiplatform.v1beta1.NotebookRuntime.name].
     *     * `displayName` supports = and != and regex.
     *     * `notebookRuntimeTemplate` supports = and !=. `notebookRuntimeTemplate`
     *       represents the NotebookRuntimeTemplate ID,
     *       i.e. the last segment of the NotebookRuntimeTemplate's [resource name]
     *       [google.cloud.aiplatform.v1beta1.NotebookRuntimeTemplate.name].
     *     * `healthState` supports = and !=. healthState enum: [HEALTHY, UNHEALTHY,
     *     HEALTH_STATE_UNSPECIFIED].
     *     * `runtimeState` supports = and !=. runtimeState enum:
     *     [RUNTIME_STATE_UNSPECIFIED, RUNNING, BEING_STARTED, BEING_STOPPED,
     *     STOPPED, BEING_UPGRADED, ERROR, INVALID].
     *     * `runtimeUser` supports = and !=.
     *     * API version is UI only: `uiState` supports = and !=. uiState enum:
     *     [UI_RESOURCE_STATE_UNSPECIFIED, UI_RESOURCE_STATE_BEING_CREATED,
     *     UI_RESOURCE_STATE_ACTIVE, UI_RESOURCE_STATE_BEING_DELETED,
     *     UI_RESOURCE_STATE_CREATION_FAILED].
     *     * `notebookRuntimeType` supports = and !=. notebookRuntimeType enum:
     *     [USER_DEFINED, ONE_CLICK].
     *     * `machineType` supports = and !=.
     *     * `acceleratorType` supports = and !=.
     *
     *   Some examples:
     *
     *     * `notebookRuntime="notebookRuntime123"`
     *     * `displayName="myDisplayName"` and `displayName=~"myDisplayNameRegex"`
     *     * `notebookRuntimeTemplate="notebookRuntimeTemplate321"`
     *     * `healthState=HEALTHY`
     *     * `runtimeState=RUNNING`
     *     * `runtimeUser="<EMAIL>"`
     *     * `uiState=UI_RESOURCE_STATE_BEING_DELETED`
     *     * `notebookRuntimeType=USER_DEFINED`
     *     * `machineType=e2-standard-4`
     *     * `acceleratorType=NVIDIA_TESLA_T4`
     * @param {number} [request.pageSize]
     *   Optional. The standard list page size.
     * @param {string} [request.pageToken]
     *   Optional. The standard list page token.
     *   Typically obtained via
     *   {@link protos.google.cloud.aiplatform.v1beta1.ListNotebookRuntimesResponse.next_page_token|ListNotebookRuntimesResponse.next_page_token}
     *   of the previous
     *   {@link protos.google.cloud.aiplatform.v1beta1.NotebookService.ListNotebookRuntimes|NotebookService.ListNotebookRuntimes}
     *   call.
     * @param {google.protobuf.FieldMask} [request.readMask]
     *   Optional. Mask specifying which fields to read.
     * @param {string} [request.orderBy]
     *   Optional. A comma-separated list of fields to order by, sorted in ascending
     *   order. Use "desc" after a field name for descending. Supported fields:
     *
     *     * `display_name`
     *     * `create_time`
     *     * `update_time`
     *
     *   Example: `display_name, create_time desc`.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Object}
     *   An iterable Object that allows {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Iteration_protocols | async iteration }.
     *   When you iterate the returned iterable, each element will be an object representing
     *   {@link protos.google.cloud.aiplatform.v1beta1.NotebookRuntime|NotebookRuntime}. The API will be called under the hood as needed, once per the page,
     *   so you can stop the iteration when you don't need more results.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1beta1/notebook_service.list_notebook_runtimes.js</caption>
     * region_tag:aiplatform_v1beta1_generated_NotebookService_ListNotebookRuntimes_async
     */
    listNotebookRuntimesAsync(request?: protos.google.cloud.aiplatform.v1beta1.IListNotebookRuntimesRequest, options?: CallOptions): AsyncIterable<protos.google.cloud.aiplatform.v1beta1.INotebookRuntime>;
    /**
     * Lists NotebookExecutionJobs in a Location.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. The resource name of the Location from which to list the
     *   NotebookExecutionJobs.
     *   Format: `projects/{project}/locations/{location}`
     * @param {string} [request.filter]
     *   Optional. An expression for filtering the results of the request. For field
     *   names both snake_case and camelCase are supported.
     *
     *     * `notebookExecutionJob` supports = and !=. `notebookExecutionJob`
     *     represents the NotebookExecutionJob ID.
     *     * `displayName` supports = and != and regex.
     *     * `schedule` supports = and != and regex.
     *
     *   Some examples:
     *     * `notebookExecutionJob="123"`
     *     * `notebookExecutionJob="my-execution-job"`
     *     * `displayName="myDisplayName"` and `displayName=~"myDisplayNameRegex"`
     * @param {number} [request.pageSize]
     *   Optional. The standard list page size.
     * @param {string} [request.pageToken]
     *   Optional. The standard list page token.
     *   Typically obtained via
     *   {@link protos.google.cloud.aiplatform.v1beta1.ListNotebookExecutionJobsResponse.next_page_token|ListNotebookExecutionJobsResponse.next_page_token}
     *   of the previous
     *   {@link protos.google.cloud.aiplatform.v1beta1.NotebookService.ListNotebookExecutionJobs|NotebookService.ListNotebookExecutionJobs}
     *   call.
     * @param {string} [request.orderBy]
     *   Optional. A comma-separated list of fields to order by, sorted in ascending
     *   order. Use "desc" after a field name for descending. Supported fields:
     *
     *     * `display_name`
     *     * `create_time`
     *     * `update_time`
     *
     *   Example: `display_name, create_time desc`.
     * @param {google.cloud.aiplatform.v1beta1.NotebookExecutionJobView} [request.view]
     *   Optional. The NotebookExecutionJob view. Defaults to BASIC.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is Array of {@link protos.google.cloud.aiplatform.v1beta1.NotebookExecutionJob|NotebookExecutionJob}.
     *   The client library will perform auto-pagination by default: it will call the API as many
     *   times as needed and will merge results from all the pages into this array.
     *   Note that it can affect your quota.
     *   We recommend using `listNotebookExecutionJobsAsync()`
     *   method described below for async iteration which you can stop as needed.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     */
    listNotebookExecutionJobs(request?: protos.google.cloud.aiplatform.v1beta1.IListNotebookExecutionJobsRequest, options?: CallOptions): Promise<[
        protos.google.cloud.aiplatform.v1beta1.INotebookExecutionJob[],
        protos.google.cloud.aiplatform.v1beta1.IListNotebookExecutionJobsRequest | null,
        protos.google.cloud.aiplatform.v1beta1.IListNotebookExecutionJobsResponse
    ]>;
    listNotebookExecutionJobs(request: protos.google.cloud.aiplatform.v1beta1.IListNotebookExecutionJobsRequest, options: CallOptions, callback: PaginationCallback<protos.google.cloud.aiplatform.v1beta1.IListNotebookExecutionJobsRequest, protos.google.cloud.aiplatform.v1beta1.IListNotebookExecutionJobsResponse | null | undefined, protos.google.cloud.aiplatform.v1beta1.INotebookExecutionJob>): void;
    listNotebookExecutionJobs(request: protos.google.cloud.aiplatform.v1beta1.IListNotebookExecutionJobsRequest, callback: PaginationCallback<protos.google.cloud.aiplatform.v1beta1.IListNotebookExecutionJobsRequest, protos.google.cloud.aiplatform.v1beta1.IListNotebookExecutionJobsResponse | null | undefined, protos.google.cloud.aiplatform.v1beta1.INotebookExecutionJob>): void;
    /**
     * Equivalent to `listNotebookExecutionJobs`, but returns a NodeJS Stream object.
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. The resource name of the Location from which to list the
     *   NotebookExecutionJobs.
     *   Format: `projects/{project}/locations/{location}`
     * @param {string} [request.filter]
     *   Optional. An expression for filtering the results of the request. For field
     *   names both snake_case and camelCase are supported.
     *
     *     * `notebookExecutionJob` supports = and !=. `notebookExecutionJob`
     *     represents the NotebookExecutionJob ID.
     *     * `displayName` supports = and != and regex.
     *     * `schedule` supports = and != and regex.
     *
     *   Some examples:
     *     * `notebookExecutionJob="123"`
     *     * `notebookExecutionJob="my-execution-job"`
     *     * `displayName="myDisplayName"` and `displayName=~"myDisplayNameRegex"`
     * @param {number} [request.pageSize]
     *   Optional. The standard list page size.
     * @param {string} [request.pageToken]
     *   Optional. The standard list page token.
     *   Typically obtained via
     *   {@link protos.google.cloud.aiplatform.v1beta1.ListNotebookExecutionJobsResponse.next_page_token|ListNotebookExecutionJobsResponse.next_page_token}
     *   of the previous
     *   {@link protos.google.cloud.aiplatform.v1beta1.NotebookService.ListNotebookExecutionJobs|NotebookService.ListNotebookExecutionJobs}
     *   call.
     * @param {string} [request.orderBy]
     *   Optional. A comma-separated list of fields to order by, sorted in ascending
     *   order. Use "desc" after a field name for descending. Supported fields:
     *
     *     * `display_name`
     *     * `create_time`
     *     * `update_time`
     *
     *   Example: `display_name, create_time desc`.
     * @param {google.cloud.aiplatform.v1beta1.NotebookExecutionJobView} [request.view]
     *   Optional. The NotebookExecutionJob view. Defaults to BASIC.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Stream}
     *   An object stream which emits an object representing {@link protos.google.cloud.aiplatform.v1beta1.NotebookExecutionJob|NotebookExecutionJob} on 'data' event.
     *   The client library will perform auto-pagination by default: it will call the API as many
     *   times as needed. Note that it can affect your quota.
     *   We recommend using `listNotebookExecutionJobsAsync()`
     *   method described below for async iteration which you can stop as needed.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     */
    listNotebookExecutionJobsStream(request?: protos.google.cloud.aiplatform.v1beta1.IListNotebookExecutionJobsRequest, options?: CallOptions): Transform;
    /**
     * Equivalent to `listNotebookExecutionJobs`, but returns an iterable object.
     *
     * `for`-`await`-`of` syntax is used with the iterable to get response elements on-demand.
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. The resource name of the Location from which to list the
     *   NotebookExecutionJobs.
     *   Format: `projects/{project}/locations/{location}`
     * @param {string} [request.filter]
     *   Optional. An expression for filtering the results of the request. For field
     *   names both snake_case and camelCase are supported.
     *
     *     * `notebookExecutionJob` supports = and !=. `notebookExecutionJob`
     *     represents the NotebookExecutionJob ID.
     *     * `displayName` supports = and != and regex.
     *     * `schedule` supports = and != and regex.
     *
     *   Some examples:
     *     * `notebookExecutionJob="123"`
     *     * `notebookExecutionJob="my-execution-job"`
     *     * `displayName="myDisplayName"` and `displayName=~"myDisplayNameRegex"`
     * @param {number} [request.pageSize]
     *   Optional. The standard list page size.
     * @param {string} [request.pageToken]
     *   Optional. The standard list page token.
     *   Typically obtained via
     *   {@link protos.google.cloud.aiplatform.v1beta1.ListNotebookExecutionJobsResponse.next_page_token|ListNotebookExecutionJobsResponse.next_page_token}
     *   of the previous
     *   {@link protos.google.cloud.aiplatform.v1beta1.NotebookService.ListNotebookExecutionJobs|NotebookService.ListNotebookExecutionJobs}
     *   call.
     * @param {string} [request.orderBy]
     *   Optional. A comma-separated list of fields to order by, sorted in ascending
     *   order. Use "desc" after a field name for descending. Supported fields:
     *
     *     * `display_name`
     *     * `create_time`
     *     * `update_time`
     *
     *   Example: `display_name, create_time desc`.
     * @param {google.cloud.aiplatform.v1beta1.NotebookExecutionJobView} [request.view]
     *   Optional. The NotebookExecutionJob view. Defaults to BASIC.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Object}
     *   An iterable Object that allows {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Iteration_protocols | async iteration }.
     *   When you iterate the returned iterable, each element will be an object representing
     *   {@link protos.google.cloud.aiplatform.v1beta1.NotebookExecutionJob|NotebookExecutionJob}. The API will be called under the hood as needed, once per the page,
     *   so you can stop the iteration when you don't need more results.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1beta1/notebook_service.list_notebook_execution_jobs.js</caption>
     * region_tag:aiplatform_v1beta1_generated_NotebookService_ListNotebookExecutionJobs_async
     */
    listNotebookExecutionJobsAsync(request?: protos.google.cloud.aiplatform.v1beta1.IListNotebookExecutionJobsRequest, options?: CallOptions): AsyncIterable<protos.google.cloud.aiplatform.v1beta1.INotebookExecutionJob>;
    /**
     * Gets the access control policy for a resource. Returns an empty policy
     * if the resource exists and does not have a policy set.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.resource
     *   REQUIRED: The resource for which the policy is being requested.
     *   See the operation documentation for the appropriate value for this field.
     * @param {Object} [request.options]
     *   OPTIONAL: A `GetPolicyOptions` object for specifying options to
     *   `GetIamPolicy`. This field is only used by Cloud IAM.
     *
     *   This object should have the same structure as {@link google.iam.v1.GetPolicyOptions | GetPolicyOptions}.
     * @param {Object} [options]
     *   Optional parameters. You can override the default settings for this call, e.g, timeout,
     *   retries, paginations, etc. See {@link https://googleapis.github.io/gax-nodejs/interfaces/CallOptions.html | gax.CallOptions} for the details.
     * @param {function(?Error, ?Object)} [callback]
     *   The function which will be called with the result of the API call.
     *
     *   The second parameter to the callback is an object representing {@link google.iam.v1.Policy | Policy}.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing {@link google.iam.v1.Policy | Policy}.
     *   The promise has a method named "cancel" which cancels the ongoing API call.
     */
    getIamPolicy(request: IamProtos.google.iam.v1.GetIamPolicyRequest, options?: gax.CallOptions | Callback<IamProtos.google.iam.v1.Policy, IamProtos.google.iam.v1.GetIamPolicyRequest | null | undefined, {} | null | undefined>, callback?: Callback<IamProtos.google.iam.v1.Policy, IamProtos.google.iam.v1.GetIamPolicyRequest | null | undefined, {} | null | undefined>): Promise<[IamProtos.google.iam.v1.Policy]>;
    /**
     * Returns permissions that a caller has on the specified resource. If the
     * resource does not exist, this will return an empty set of
     * permissions, not a NOT_FOUND error.
     *
     * Note: This operation is designed to be used for building
     * permission-aware UIs and command-line tools, not for authorization
     * checking. This operation may "fail open" without warning.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.resource
     *   REQUIRED: The resource for which the policy detail is being requested.
     *   See the operation documentation for the appropriate value for this field.
     * @param {string[]} request.permissions
     *   The set of permissions to check for the `resource`. Permissions with
     *   wildcards (such as '*' or 'storage.*') are not allowed. For more
     *   information see {@link https://cloud.google.com/iam/docs/overview#permissions | IAM Overview }.
     * @param {Object} [options]
     *   Optional parameters. You can override the default settings for this call, e.g, timeout,
     *   retries, paginations, etc. See {@link https://googleapis.github.io/gax-nodejs/interfaces/CallOptions.html | gax.CallOptions} for the details.
     * @param {function(?Error, ?Object)} [callback]
     *   The function which will be called with the result of the API call.
     *
     *   The second parameter to the callback is an object representing {@link google.iam.v1.TestIamPermissionsResponse | TestIamPermissionsResponse}.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing {@link google.iam.v1.TestIamPermissionsResponse | TestIamPermissionsResponse}.
     *   The promise has a method named "cancel" which cancels the ongoing API call.
     */
    setIamPolicy(request: IamProtos.google.iam.v1.SetIamPolicyRequest, options?: gax.CallOptions | Callback<IamProtos.google.iam.v1.Policy, IamProtos.google.iam.v1.SetIamPolicyRequest | null | undefined, {} | null | undefined>, callback?: Callback<IamProtos.google.iam.v1.Policy, IamProtos.google.iam.v1.SetIamPolicyRequest | null | undefined, {} | null | undefined>): Promise<[IamProtos.google.iam.v1.Policy]>;
    /**
     * Returns permissions that a caller has on the specified resource. If the
     * resource does not exist, this will return an empty set of
     * permissions, not a NOT_FOUND error.
     *
     * Note: This operation is designed to be used for building
     * permission-aware UIs and command-line tools, not for authorization
     * checking. This operation may "fail open" without warning.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.resource
     *   REQUIRED: The resource for which the policy detail is being requested.
     *   See the operation documentation for the appropriate value for this field.
     * @param {string[]} request.permissions
     *   The set of permissions to check for the `resource`. Permissions with
     *   wildcards (such as '*' or 'storage.*') are not allowed. For more
     *   information see {@link https://cloud.google.com/iam/docs/overview#permissions | IAM Overview }.
     * @param {Object} [options]
     *   Optional parameters. You can override the default settings for this call, e.g, timeout,
     *   retries, paginations, etc. See {@link https://googleapis.github.io/gax-nodejs/interfaces/CallOptions.html | gax.CallOptions} for the details.
     * @param {function(?Error, ?Object)} [callback]
     *   The function which will be called with the result of the API call.
     *
     *   The second parameter to the callback is an object representing {@link google.iam.v1.TestIamPermissionsResponse | TestIamPermissionsResponse}.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing {@link google.iam.v1.TestIamPermissionsResponse | TestIamPermissionsResponse}.
     *   The promise has a method named "cancel" which cancels the ongoing API call.
     *
     */
    testIamPermissions(request: IamProtos.google.iam.v1.TestIamPermissionsRequest, options?: gax.CallOptions | Callback<IamProtos.google.iam.v1.TestIamPermissionsResponse, IamProtos.google.iam.v1.TestIamPermissionsRequest | null | undefined, {} | null | undefined>, callback?: Callback<IamProtos.google.iam.v1.TestIamPermissionsResponse, IamProtos.google.iam.v1.TestIamPermissionsRequest | null | undefined, {} | null | undefined>): Promise<[IamProtos.google.iam.v1.TestIamPermissionsResponse]>;
    /**
     * Gets information about a location.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.name
     *   Resource name for the location.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html | CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing {@link google.cloud.location.Location | Location}.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#regular-methods | documentation }
     *   for more details and examples.
     * @example
     * ```
     * const [response] = await client.getLocation(request);
     * ```
     */
    getLocation(request: LocationProtos.google.cloud.location.IGetLocationRequest, options?: gax.CallOptions | Callback<LocationProtos.google.cloud.location.ILocation, LocationProtos.google.cloud.location.IGetLocationRequest | null | undefined, {} | null | undefined>, callback?: Callback<LocationProtos.google.cloud.location.ILocation, LocationProtos.google.cloud.location.IGetLocationRequest | null | undefined, {} | null | undefined>): Promise<LocationProtos.google.cloud.location.ILocation>;
    /**
     * Lists information about the supported locations for this service. Returns an iterable object.
     *
     * `for`-`await`-`of` syntax is used with the iterable to get response elements on-demand.
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.name
     *   The resource that owns the locations collection, if applicable.
     * @param {string} request.filter
     *   The standard list filter.
     * @param {number} request.pageSize
     *   The standard list page size.
     * @param {string} request.pageToken
     *   The standard list page token.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Object}
     *   An iterable Object that allows {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Iteration_protocols | async iteration }.
     *   When you iterate the returned iterable, each element will be an object representing
     *   {@link google.cloud.location.Location | Location}. The API will be called under the hood as needed, once per the page,
     *   so you can stop the iteration when you don't need more results.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     * @example
     * ```
     * const iterable = client.listLocationsAsync(request);
     * for await (const response of iterable) {
     *   // process response
     * }
     * ```
     */
    listLocationsAsync(request: LocationProtos.google.cloud.location.IListLocationsRequest, options?: CallOptions): AsyncIterable<LocationProtos.google.cloud.location.ILocation>;
    /**
     * Gets the latest state of a long-running operation.  Clients can use this
     * method to poll the operation result at intervals as recommended by the API
     * service.
     *
     * @param {Object} request - The request object that will be sent.
     * @param {string} request.name - The name of the operation resource.
     * @param {Object=} options
     *   Optional parameters. You can override the default settings for this call,
     *   e.g, timeout, retries, paginations, etc. See {@link
     *   https://googleapis.github.io/gax-nodejs/global.html#CallOptions | gax.CallOptions}
     *   for the details.
     * @param {function(?Error, ?Object)=} callback
     *   The function which will be called with the result of the API call.
     *
     *   The second parameter to the callback is an object representing
     *   {@link google.longrunning.Operation | google.longrunning.Operation}.
     * @return {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing
     * {@link google.longrunning.Operation | google.longrunning.Operation}.
     * The promise has a method named "cancel" which cancels the ongoing API call.
     *
     * @example
     * ```
     * const client = longrunning.operationsClient();
     * const name = '';
     * const [response] = await client.getOperation({name});
     * // doThingsWith(response)
     * ```
     */
    getOperation(request: protos.google.longrunning.GetOperationRequest, optionsOrCallback?: gax.CallOptions | Callback<protos.google.longrunning.Operation, protos.google.longrunning.GetOperationRequest, {} | null | undefined>, callback?: Callback<protos.google.longrunning.Operation, protos.google.longrunning.GetOperationRequest, {} | null | undefined>): Promise<[protos.google.longrunning.Operation]>;
    /**
     * Lists operations that match the specified filter in the request. If the
     * server doesn't support this method, it returns `UNIMPLEMENTED`. Returns an iterable object.
     *
     * For-await-of syntax is used with the iterable to recursively get response element on-demand.
     *
     * @param {Object} request - The request object that will be sent.
     * @param {string} request.name - The name of the operation collection.
     * @param {string} request.filter - The standard list filter.
     * @param {number=} request.pageSize -
     *   The maximum number of resources contained in the underlying API
     *   response. If page streaming is performed per-resource, this
     *   parameter does not affect the return value. If page streaming is
     *   performed per-page, this determines the maximum number of
     *   resources in a page.
     * @param {Object=} options
     *   Optional parameters. You can override the default settings for this call,
     *   e.g, timeout, retries, paginations, etc. See {@link
     *   https://googleapis.github.io/gax-nodejs/global.html#CallOptions | gax.CallOptions} for the
     *   details.
     * @returns {Object}
     *   An iterable Object that conforms to {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Iteration_protocols | iteration protocols}.
     *
     * @example
     * ```
     * const client = longrunning.operationsClient();
     * for await (const response of client.listOperationsAsync(request));
     * // doThingsWith(response)
     * ```
     */
    listOperationsAsync(request: protos.google.longrunning.ListOperationsRequest, options?: gax.CallOptions): AsyncIterable<protos.google.longrunning.ListOperationsResponse>;
    /**
     * Starts asynchronous cancellation on a long-running operation.  The server
     * makes a best effort to cancel the operation, but success is not
     * guaranteed.  If the server doesn't support this method, it returns
     * `google.rpc.Code.UNIMPLEMENTED`.  Clients can use
     * {@link Operations.GetOperation} or
     * other methods to check whether the cancellation succeeded or whether the
     * operation completed despite cancellation. On successful cancellation,
     * the operation is not deleted; instead, it becomes an operation with
     * an {@link Operation.error} value with a {@link google.rpc.Status.code} of
     * 1, corresponding to `Code.CANCELLED`.
     *
     * @param {Object} request - The request object that will be sent.
     * @param {string} request.name - The name of the operation resource to be cancelled.
     * @param {Object=} options
     *   Optional parameters. You can override the default settings for this call,
     * e.g, timeout, retries, paginations, etc. See {@link
     * https://googleapis.github.io/gax-nodejs/global.html#CallOptions | gax.CallOptions} for the
     * details.
     * @param {function(?Error)=} callback
     *   The function which will be called with the result of the API call.
     * @return {Promise} - The promise which resolves when API call finishes.
     *   The promise has a method named "cancel" which cancels the ongoing API
     * call.
     *
     * @example
     * ```
     * const client = longrunning.operationsClient();
     * await client.cancelOperation({name: ''});
     * ```
     */
    cancelOperation(request: protos.google.longrunning.CancelOperationRequest, optionsOrCallback?: gax.CallOptions | Callback<protos.google.longrunning.CancelOperationRequest, protos.google.protobuf.Empty, {} | undefined | null>, callback?: Callback<protos.google.longrunning.CancelOperationRequest, protos.google.protobuf.Empty, {} | undefined | null>): Promise<protos.google.protobuf.Empty>;
    /**
     * Deletes a long-running operation. This method indicates that the client is
     * no longer interested in the operation result. It does not cancel the
     * operation. If the server doesn't support this method, it returns
     * `google.rpc.Code.UNIMPLEMENTED`.
     *
     * @param {Object} request - The request object that will be sent.
     * @param {string} request.name - The name of the operation resource to be deleted.
     * @param {Object=} options
     *   Optional parameters. You can override the default settings for this call,
     * e.g, timeout, retries, paginations, etc. See {@link
     * https://googleapis.github.io/gax-nodejs/global.html#CallOptions | gax.CallOptions}
     * for the details.
     * @param {function(?Error)=} callback
     *   The function which will be called with the result of the API call.
     * @return {Promise} - The promise which resolves when API call finishes.
     *   The promise has a method named "cancel" which cancels the ongoing API
     * call.
     *
     * @example
     * ```
     * const client = longrunning.operationsClient();
     * await client.deleteOperation({name: ''});
     * ```
     */
    deleteOperation(request: protos.google.longrunning.DeleteOperationRequest, optionsOrCallback?: gax.CallOptions | Callback<protos.google.protobuf.Empty, protos.google.longrunning.DeleteOperationRequest, {} | null | undefined>, callback?: Callback<protos.google.protobuf.Empty, protos.google.longrunning.DeleteOperationRequest, {} | null | undefined>): Promise<protos.google.protobuf.Empty>;
    /**
     * Return a fully-qualified annotation resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} dataset
     * @param {string} data_item
     * @param {string} annotation
     * @returns {string} Resource name string.
     */
    annotationPath(project: string, location: string, dataset: string, dataItem: string, annotation: string): string;
    /**
     * Parse the project from Annotation resource.
     *
     * @param {string} annotationName
     *   A fully-qualified path representing Annotation resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromAnnotationName(annotationName: string): string | number;
    /**
     * Parse the location from Annotation resource.
     *
     * @param {string} annotationName
     *   A fully-qualified path representing Annotation resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromAnnotationName(annotationName: string): string | number;
    /**
     * Parse the dataset from Annotation resource.
     *
     * @param {string} annotationName
     *   A fully-qualified path representing Annotation resource.
     * @returns {string} A string representing the dataset.
     */
    matchDatasetFromAnnotationName(annotationName: string): string | number;
    /**
     * Parse the data_item from Annotation resource.
     *
     * @param {string} annotationName
     *   A fully-qualified path representing Annotation resource.
     * @returns {string} A string representing the data_item.
     */
    matchDataItemFromAnnotationName(annotationName: string): string | number;
    /**
     * Parse the annotation from Annotation resource.
     *
     * @param {string} annotationName
     *   A fully-qualified path representing Annotation resource.
     * @returns {string} A string representing the annotation.
     */
    matchAnnotationFromAnnotationName(annotationName: string): string | number;
    /**
     * Return a fully-qualified annotationSpec resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} dataset
     * @param {string} annotation_spec
     * @returns {string} Resource name string.
     */
    annotationSpecPath(project: string, location: string, dataset: string, annotationSpec: string): string;
    /**
     * Parse the project from AnnotationSpec resource.
     *
     * @param {string} annotationSpecName
     *   A fully-qualified path representing AnnotationSpec resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromAnnotationSpecName(annotationSpecName: string): string | number;
    /**
     * Parse the location from AnnotationSpec resource.
     *
     * @param {string} annotationSpecName
     *   A fully-qualified path representing AnnotationSpec resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromAnnotationSpecName(annotationSpecName: string): string | number;
    /**
     * Parse the dataset from AnnotationSpec resource.
     *
     * @param {string} annotationSpecName
     *   A fully-qualified path representing AnnotationSpec resource.
     * @returns {string} A string representing the dataset.
     */
    matchDatasetFromAnnotationSpecName(annotationSpecName: string): string | number;
    /**
     * Parse the annotation_spec from AnnotationSpec resource.
     *
     * @param {string} annotationSpecName
     *   A fully-qualified path representing AnnotationSpec resource.
     * @returns {string} A string representing the annotation_spec.
     */
    matchAnnotationSpecFromAnnotationSpecName(annotationSpecName: string): string | number;
    /**
     * Return a fully-qualified artifact resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} metadata_store
     * @param {string} artifact
     * @returns {string} Resource name string.
     */
    artifactPath(project: string, location: string, metadataStore: string, artifact: string): string;
    /**
     * Parse the project from Artifact resource.
     *
     * @param {string} artifactName
     *   A fully-qualified path representing Artifact resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromArtifactName(artifactName: string): string | number;
    /**
     * Parse the location from Artifact resource.
     *
     * @param {string} artifactName
     *   A fully-qualified path representing Artifact resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromArtifactName(artifactName: string): string | number;
    /**
     * Parse the metadata_store from Artifact resource.
     *
     * @param {string} artifactName
     *   A fully-qualified path representing Artifact resource.
     * @returns {string} A string representing the metadata_store.
     */
    matchMetadataStoreFromArtifactName(artifactName: string): string | number;
    /**
     * Parse the artifact from Artifact resource.
     *
     * @param {string} artifactName
     *   A fully-qualified path representing Artifact resource.
     * @returns {string} A string representing the artifact.
     */
    matchArtifactFromArtifactName(artifactName: string): string | number;
    /**
     * Return a fully-qualified batchPredictionJob resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} batch_prediction_job
     * @returns {string} Resource name string.
     */
    batchPredictionJobPath(project: string, location: string, batchPredictionJob: string): string;
    /**
     * Parse the project from BatchPredictionJob resource.
     *
     * @param {string} batchPredictionJobName
     *   A fully-qualified path representing BatchPredictionJob resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromBatchPredictionJobName(batchPredictionJobName: string): string | number;
    /**
     * Parse the location from BatchPredictionJob resource.
     *
     * @param {string} batchPredictionJobName
     *   A fully-qualified path representing BatchPredictionJob resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromBatchPredictionJobName(batchPredictionJobName: string): string | number;
    /**
     * Parse the batch_prediction_job from BatchPredictionJob resource.
     *
     * @param {string} batchPredictionJobName
     *   A fully-qualified path representing BatchPredictionJob resource.
     * @returns {string} A string representing the batch_prediction_job.
     */
    matchBatchPredictionJobFromBatchPredictionJobName(batchPredictionJobName: string): string | number;
    /**
     * Return a fully-qualified cachedContent resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} cached_content
     * @returns {string} Resource name string.
     */
    cachedContentPath(project: string, location: string, cachedContent: string): string;
    /**
     * Parse the project from CachedContent resource.
     *
     * @param {string} cachedContentName
     *   A fully-qualified path representing CachedContent resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromCachedContentName(cachedContentName: string): string | number;
    /**
     * Parse the location from CachedContent resource.
     *
     * @param {string} cachedContentName
     *   A fully-qualified path representing CachedContent resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromCachedContentName(cachedContentName: string): string | number;
    /**
     * Parse the cached_content from CachedContent resource.
     *
     * @param {string} cachedContentName
     *   A fully-qualified path representing CachedContent resource.
     * @returns {string} A string representing the cached_content.
     */
    matchCachedContentFromCachedContentName(cachedContentName: string): string | number;
    /**
     * Return a fully-qualified context resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} metadata_store
     * @param {string} context
     * @returns {string} Resource name string.
     */
    contextPath(project: string, location: string, metadataStore: string, context: string): string;
    /**
     * Parse the project from Context resource.
     *
     * @param {string} contextName
     *   A fully-qualified path representing Context resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromContextName(contextName: string): string | number;
    /**
     * Parse the location from Context resource.
     *
     * @param {string} contextName
     *   A fully-qualified path representing Context resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromContextName(contextName: string): string | number;
    /**
     * Parse the metadata_store from Context resource.
     *
     * @param {string} contextName
     *   A fully-qualified path representing Context resource.
     * @returns {string} A string representing the metadata_store.
     */
    matchMetadataStoreFromContextName(contextName: string): string | number;
    /**
     * Parse the context from Context resource.
     *
     * @param {string} contextName
     *   A fully-qualified path representing Context resource.
     * @returns {string} A string representing the context.
     */
    matchContextFromContextName(contextName: string): string | number;
    /**
     * Return a fully-qualified customJob resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} custom_job
     * @returns {string} Resource name string.
     */
    customJobPath(project: string, location: string, customJob: string): string;
    /**
     * Parse the project from CustomJob resource.
     *
     * @param {string} customJobName
     *   A fully-qualified path representing CustomJob resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromCustomJobName(customJobName: string): string | number;
    /**
     * Parse the location from CustomJob resource.
     *
     * @param {string} customJobName
     *   A fully-qualified path representing CustomJob resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromCustomJobName(customJobName: string): string | number;
    /**
     * Parse the custom_job from CustomJob resource.
     *
     * @param {string} customJobName
     *   A fully-qualified path representing CustomJob resource.
     * @returns {string} A string representing the custom_job.
     */
    matchCustomJobFromCustomJobName(customJobName: string): string | number;
    /**
     * Return a fully-qualified dataItem resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} dataset
     * @param {string} data_item
     * @returns {string} Resource name string.
     */
    dataItemPath(project: string, location: string, dataset: string, dataItem: string): string;
    /**
     * Parse the project from DataItem resource.
     *
     * @param {string} dataItemName
     *   A fully-qualified path representing DataItem resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromDataItemName(dataItemName: string): string | number;
    /**
     * Parse the location from DataItem resource.
     *
     * @param {string} dataItemName
     *   A fully-qualified path representing DataItem resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromDataItemName(dataItemName: string): string | number;
    /**
     * Parse the dataset from DataItem resource.
     *
     * @param {string} dataItemName
     *   A fully-qualified path representing DataItem resource.
     * @returns {string} A string representing the dataset.
     */
    matchDatasetFromDataItemName(dataItemName: string): string | number;
    /**
     * Parse the data_item from DataItem resource.
     *
     * @param {string} dataItemName
     *   A fully-qualified path representing DataItem resource.
     * @returns {string} A string representing the data_item.
     */
    matchDataItemFromDataItemName(dataItemName: string): string | number;
    /**
     * Return a fully-qualified dataLabelingJob resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} data_labeling_job
     * @returns {string} Resource name string.
     */
    dataLabelingJobPath(project: string, location: string, dataLabelingJob: string): string;
    /**
     * Parse the project from DataLabelingJob resource.
     *
     * @param {string} dataLabelingJobName
     *   A fully-qualified path representing DataLabelingJob resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromDataLabelingJobName(dataLabelingJobName: string): string | number;
    /**
     * Parse the location from DataLabelingJob resource.
     *
     * @param {string} dataLabelingJobName
     *   A fully-qualified path representing DataLabelingJob resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromDataLabelingJobName(dataLabelingJobName: string): string | number;
    /**
     * Parse the data_labeling_job from DataLabelingJob resource.
     *
     * @param {string} dataLabelingJobName
     *   A fully-qualified path representing DataLabelingJob resource.
     * @returns {string} A string representing the data_labeling_job.
     */
    matchDataLabelingJobFromDataLabelingJobName(dataLabelingJobName: string): string | number;
    /**
     * Return a fully-qualified dataset resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} dataset
     * @returns {string} Resource name string.
     */
    datasetPath(project: string, location: string, dataset: string): string;
    /**
     * Parse the project from Dataset resource.
     *
     * @param {string} datasetName
     *   A fully-qualified path representing Dataset resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromDatasetName(datasetName: string): string | number;
    /**
     * Parse the location from Dataset resource.
     *
     * @param {string} datasetName
     *   A fully-qualified path representing Dataset resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromDatasetName(datasetName: string): string | number;
    /**
     * Parse the dataset from Dataset resource.
     *
     * @param {string} datasetName
     *   A fully-qualified path representing Dataset resource.
     * @returns {string} A string representing the dataset.
     */
    matchDatasetFromDatasetName(datasetName: string): string | number;
    /**
     * Return a fully-qualified datasetVersion resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} dataset
     * @param {string} dataset_version
     * @returns {string} Resource name string.
     */
    datasetVersionPath(project: string, location: string, dataset: string, datasetVersion: string): string;
    /**
     * Parse the project from DatasetVersion resource.
     *
     * @param {string} datasetVersionName
     *   A fully-qualified path representing DatasetVersion resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromDatasetVersionName(datasetVersionName: string): string | number;
    /**
     * Parse the location from DatasetVersion resource.
     *
     * @param {string} datasetVersionName
     *   A fully-qualified path representing DatasetVersion resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromDatasetVersionName(datasetVersionName: string): string | number;
    /**
     * Parse the dataset from DatasetVersion resource.
     *
     * @param {string} datasetVersionName
     *   A fully-qualified path representing DatasetVersion resource.
     * @returns {string} A string representing the dataset.
     */
    matchDatasetFromDatasetVersionName(datasetVersionName: string): string | number;
    /**
     * Parse the dataset_version from DatasetVersion resource.
     *
     * @param {string} datasetVersionName
     *   A fully-qualified path representing DatasetVersion resource.
     * @returns {string} A string representing the dataset_version.
     */
    matchDatasetVersionFromDatasetVersionName(datasetVersionName: string): string | number;
    /**
     * Return a fully-qualified deploymentResourcePool resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} deployment_resource_pool
     * @returns {string} Resource name string.
     */
    deploymentResourcePoolPath(project: string, location: string, deploymentResourcePool: string): string;
    /**
     * Parse the project from DeploymentResourcePool resource.
     *
     * @param {string} deploymentResourcePoolName
     *   A fully-qualified path representing DeploymentResourcePool resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromDeploymentResourcePoolName(deploymentResourcePoolName: string): string | number;
    /**
     * Parse the location from DeploymentResourcePool resource.
     *
     * @param {string} deploymentResourcePoolName
     *   A fully-qualified path representing DeploymentResourcePool resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromDeploymentResourcePoolName(deploymentResourcePoolName: string): string | number;
    /**
     * Parse the deployment_resource_pool from DeploymentResourcePool resource.
     *
     * @param {string} deploymentResourcePoolName
     *   A fully-qualified path representing DeploymentResourcePool resource.
     * @returns {string} A string representing the deployment_resource_pool.
     */
    matchDeploymentResourcePoolFromDeploymentResourcePoolName(deploymentResourcePoolName: string): string | number;
    /**
     * Return a fully-qualified entityType resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} featurestore
     * @param {string} entity_type
     * @returns {string} Resource name string.
     */
    entityTypePath(project: string, location: string, featurestore: string, entityType: string): string;
    /**
     * Parse the project from EntityType resource.
     *
     * @param {string} entityTypeName
     *   A fully-qualified path representing EntityType resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromEntityTypeName(entityTypeName: string): string | number;
    /**
     * Parse the location from EntityType resource.
     *
     * @param {string} entityTypeName
     *   A fully-qualified path representing EntityType resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromEntityTypeName(entityTypeName: string): string | number;
    /**
     * Parse the featurestore from EntityType resource.
     *
     * @param {string} entityTypeName
     *   A fully-qualified path representing EntityType resource.
     * @returns {string} A string representing the featurestore.
     */
    matchFeaturestoreFromEntityTypeName(entityTypeName: string): string | number;
    /**
     * Parse the entity_type from EntityType resource.
     *
     * @param {string} entityTypeName
     *   A fully-qualified path representing EntityType resource.
     * @returns {string} A string representing the entity_type.
     */
    matchEntityTypeFromEntityTypeName(entityTypeName: string): string | number;
    /**
     * Return a fully-qualified execution resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} metadata_store
     * @param {string} execution
     * @returns {string} Resource name string.
     */
    executionPath(project: string, location: string, metadataStore: string, execution: string): string;
    /**
     * Parse the project from Execution resource.
     *
     * @param {string} executionName
     *   A fully-qualified path representing Execution resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromExecutionName(executionName: string): string | number;
    /**
     * Parse the location from Execution resource.
     *
     * @param {string} executionName
     *   A fully-qualified path representing Execution resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromExecutionName(executionName: string): string | number;
    /**
     * Parse the metadata_store from Execution resource.
     *
     * @param {string} executionName
     *   A fully-qualified path representing Execution resource.
     * @returns {string} A string representing the metadata_store.
     */
    matchMetadataStoreFromExecutionName(executionName: string): string | number;
    /**
     * Parse the execution from Execution resource.
     *
     * @param {string} executionName
     *   A fully-qualified path representing Execution resource.
     * @returns {string} A string representing the execution.
     */
    matchExecutionFromExecutionName(executionName: string): string | number;
    /**
     * Return a fully-qualified extension resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} extension
     * @returns {string} Resource name string.
     */
    extensionPath(project: string, location: string, extension: string): string;
    /**
     * Parse the project from Extension resource.
     *
     * @param {string} extensionName
     *   A fully-qualified path representing Extension resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromExtensionName(extensionName: string): string | number;
    /**
     * Parse the location from Extension resource.
     *
     * @param {string} extensionName
     *   A fully-qualified path representing Extension resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromExtensionName(extensionName: string): string | number;
    /**
     * Parse the extension from Extension resource.
     *
     * @param {string} extensionName
     *   A fully-qualified path representing Extension resource.
     * @returns {string} A string representing the extension.
     */
    matchExtensionFromExtensionName(extensionName: string): string | number;
    /**
     * Return a fully-qualified featureGroup resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} feature_group
     * @returns {string} Resource name string.
     */
    featureGroupPath(project: string, location: string, featureGroup: string): string;
    /**
     * Parse the project from FeatureGroup resource.
     *
     * @param {string} featureGroupName
     *   A fully-qualified path representing FeatureGroup resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromFeatureGroupName(featureGroupName: string): string | number;
    /**
     * Parse the location from FeatureGroup resource.
     *
     * @param {string} featureGroupName
     *   A fully-qualified path representing FeatureGroup resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromFeatureGroupName(featureGroupName: string): string | number;
    /**
     * Parse the feature_group from FeatureGroup resource.
     *
     * @param {string} featureGroupName
     *   A fully-qualified path representing FeatureGroup resource.
     * @returns {string} A string representing the feature_group.
     */
    matchFeatureGroupFromFeatureGroupName(featureGroupName: string): string | number;
    /**
     * Return a fully-qualified featureMonitor resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} feature_group
     * @param {string} feature_monitor
     * @returns {string} Resource name string.
     */
    featureMonitorPath(project: string, location: string, featureGroup: string, featureMonitor: string): string;
    /**
     * Parse the project from FeatureMonitor resource.
     *
     * @param {string} featureMonitorName
     *   A fully-qualified path representing FeatureMonitor resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromFeatureMonitorName(featureMonitorName: string): string | number;
    /**
     * Parse the location from FeatureMonitor resource.
     *
     * @param {string} featureMonitorName
     *   A fully-qualified path representing FeatureMonitor resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromFeatureMonitorName(featureMonitorName: string): string | number;
    /**
     * Parse the feature_group from FeatureMonitor resource.
     *
     * @param {string} featureMonitorName
     *   A fully-qualified path representing FeatureMonitor resource.
     * @returns {string} A string representing the feature_group.
     */
    matchFeatureGroupFromFeatureMonitorName(featureMonitorName: string): string | number;
    /**
     * Parse the feature_monitor from FeatureMonitor resource.
     *
     * @param {string} featureMonitorName
     *   A fully-qualified path representing FeatureMonitor resource.
     * @returns {string} A string representing the feature_monitor.
     */
    matchFeatureMonitorFromFeatureMonitorName(featureMonitorName: string): string | number;
    /**
     * Return a fully-qualified featureMonitorJob resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} feature_group
     * @param {string} feature_monitor
     * @param {string} feature_monitor_job
     * @returns {string} Resource name string.
     */
    featureMonitorJobPath(project: string, location: string, featureGroup: string, featureMonitor: string, featureMonitorJob: string): string;
    /**
     * Parse the project from FeatureMonitorJob resource.
     *
     * @param {string} featureMonitorJobName
     *   A fully-qualified path representing FeatureMonitorJob resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromFeatureMonitorJobName(featureMonitorJobName: string): string | number;
    /**
     * Parse the location from FeatureMonitorJob resource.
     *
     * @param {string} featureMonitorJobName
     *   A fully-qualified path representing FeatureMonitorJob resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromFeatureMonitorJobName(featureMonitorJobName: string): string | number;
    /**
     * Parse the feature_group from FeatureMonitorJob resource.
     *
     * @param {string} featureMonitorJobName
     *   A fully-qualified path representing FeatureMonitorJob resource.
     * @returns {string} A string representing the feature_group.
     */
    matchFeatureGroupFromFeatureMonitorJobName(featureMonitorJobName: string): string | number;
    /**
     * Parse the feature_monitor from FeatureMonitorJob resource.
     *
     * @param {string} featureMonitorJobName
     *   A fully-qualified path representing FeatureMonitorJob resource.
     * @returns {string} A string representing the feature_monitor.
     */
    matchFeatureMonitorFromFeatureMonitorJobName(featureMonitorJobName: string): string | number;
    /**
     * Parse the feature_monitor_job from FeatureMonitorJob resource.
     *
     * @param {string} featureMonitorJobName
     *   A fully-qualified path representing FeatureMonitorJob resource.
     * @returns {string} A string representing the feature_monitor_job.
     */
    matchFeatureMonitorJobFromFeatureMonitorJobName(featureMonitorJobName: string): string | number;
    /**
     * Return a fully-qualified featureOnlineStore resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} feature_online_store
     * @returns {string} Resource name string.
     */
    featureOnlineStorePath(project: string, location: string, featureOnlineStore: string): string;
    /**
     * Parse the project from FeatureOnlineStore resource.
     *
     * @param {string} featureOnlineStoreName
     *   A fully-qualified path representing FeatureOnlineStore resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromFeatureOnlineStoreName(featureOnlineStoreName: string): string | number;
    /**
     * Parse the location from FeatureOnlineStore resource.
     *
     * @param {string} featureOnlineStoreName
     *   A fully-qualified path representing FeatureOnlineStore resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromFeatureOnlineStoreName(featureOnlineStoreName: string): string | number;
    /**
     * Parse the feature_online_store from FeatureOnlineStore resource.
     *
     * @param {string} featureOnlineStoreName
     *   A fully-qualified path representing FeatureOnlineStore resource.
     * @returns {string} A string representing the feature_online_store.
     */
    matchFeatureOnlineStoreFromFeatureOnlineStoreName(featureOnlineStoreName: string): string | number;
    /**
     * Return a fully-qualified featureView resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} feature_online_store
     * @param {string} feature_view
     * @returns {string} Resource name string.
     */
    featureViewPath(project: string, location: string, featureOnlineStore: string, featureView: string): string;
    /**
     * Parse the project from FeatureView resource.
     *
     * @param {string} featureViewName
     *   A fully-qualified path representing FeatureView resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromFeatureViewName(featureViewName: string): string | number;
    /**
     * Parse the location from FeatureView resource.
     *
     * @param {string} featureViewName
     *   A fully-qualified path representing FeatureView resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromFeatureViewName(featureViewName: string): string | number;
    /**
     * Parse the feature_online_store from FeatureView resource.
     *
     * @param {string} featureViewName
     *   A fully-qualified path representing FeatureView resource.
     * @returns {string} A string representing the feature_online_store.
     */
    matchFeatureOnlineStoreFromFeatureViewName(featureViewName: string): string | number;
    /**
     * Parse the feature_view from FeatureView resource.
     *
     * @param {string} featureViewName
     *   A fully-qualified path representing FeatureView resource.
     * @returns {string} A string representing the feature_view.
     */
    matchFeatureViewFromFeatureViewName(featureViewName: string): string | number;
    /**
     * Return a fully-qualified featureViewSync resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} feature_online_store
     * @param {string} feature_view
     * @returns {string} Resource name string.
     */
    featureViewSyncPath(project: string, location: string, featureOnlineStore: string, featureView: string): string;
    /**
     * Parse the project from FeatureViewSync resource.
     *
     * @param {string} featureViewSyncName
     *   A fully-qualified path representing FeatureViewSync resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromFeatureViewSyncName(featureViewSyncName: string): string | number;
    /**
     * Parse the location from FeatureViewSync resource.
     *
     * @param {string} featureViewSyncName
     *   A fully-qualified path representing FeatureViewSync resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromFeatureViewSyncName(featureViewSyncName: string): string | number;
    /**
     * Parse the feature_online_store from FeatureViewSync resource.
     *
     * @param {string} featureViewSyncName
     *   A fully-qualified path representing FeatureViewSync resource.
     * @returns {string} A string representing the feature_online_store.
     */
    matchFeatureOnlineStoreFromFeatureViewSyncName(featureViewSyncName: string): string | number;
    /**
     * Parse the feature_view from FeatureViewSync resource.
     *
     * @param {string} featureViewSyncName
     *   A fully-qualified path representing FeatureViewSync resource.
     * @returns {string} A string representing the feature_view.
     */
    matchFeatureViewFromFeatureViewSyncName(featureViewSyncName: string): string | number;
    /**
     * Return a fully-qualified featurestore resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} featurestore
     * @returns {string} Resource name string.
     */
    featurestorePath(project: string, location: string, featurestore: string): string;
    /**
     * Parse the project from Featurestore resource.
     *
     * @param {string} featurestoreName
     *   A fully-qualified path representing Featurestore resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromFeaturestoreName(featurestoreName: string): string | number;
    /**
     * Parse the location from Featurestore resource.
     *
     * @param {string} featurestoreName
     *   A fully-qualified path representing Featurestore resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromFeaturestoreName(featurestoreName: string): string | number;
    /**
     * Parse the featurestore from Featurestore resource.
     *
     * @param {string} featurestoreName
     *   A fully-qualified path representing Featurestore resource.
     * @returns {string} A string representing the featurestore.
     */
    matchFeaturestoreFromFeaturestoreName(featurestoreName: string): string | number;
    /**
     * Return a fully-qualified hyperparameterTuningJob resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} hyperparameter_tuning_job
     * @returns {string} Resource name string.
     */
    hyperparameterTuningJobPath(project: string, location: string, hyperparameterTuningJob: string): string;
    /**
     * Parse the project from HyperparameterTuningJob resource.
     *
     * @param {string} hyperparameterTuningJobName
     *   A fully-qualified path representing HyperparameterTuningJob resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromHyperparameterTuningJobName(hyperparameterTuningJobName: string): string | number;
    /**
     * Parse the location from HyperparameterTuningJob resource.
     *
     * @param {string} hyperparameterTuningJobName
     *   A fully-qualified path representing HyperparameterTuningJob resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromHyperparameterTuningJobName(hyperparameterTuningJobName: string): string | number;
    /**
     * Parse the hyperparameter_tuning_job from HyperparameterTuningJob resource.
     *
     * @param {string} hyperparameterTuningJobName
     *   A fully-qualified path representing HyperparameterTuningJob resource.
     * @returns {string} A string representing the hyperparameter_tuning_job.
     */
    matchHyperparameterTuningJobFromHyperparameterTuningJobName(hyperparameterTuningJobName: string): string | number;
    /**
     * Return a fully-qualified index resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} index
     * @returns {string} Resource name string.
     */
    indexPath(project: string, location: string, index: string): string;
    /**
     * Parse the project from Index resource.
     *
     * @param {string} indexName
     *   A fully-qualified path representing Index resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromIndexName(indexName: string): string | number;
    /**
     * Parse the location from Index resource.
     *
     * @param {string} indexName
     *   A fully-qualified path representing Index resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromIndexName(indexName: string): string | number;
    /**
     * Parse the index from Index resource.
     *
     * @param {string} indexName
     *   A fully-qualified path representing Index resource.
     * @returns {string} A string representing the index.
     */
    matchIndexFromIndexName(indexName: string): string | number;
    /**
     * Return a fully-qualified indexEndpoint resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} index_endpoint
     * @returns {string} Resource name string.
     */
    indexEndpointPath(project: string, location: string, indexEndpoint: string): string;
    /**
     * Parse the project from IndexEndpoint resource.
     *
     * @param {string} indexEndpointName
     *   A fully-qualified path representing IndexEndpoint resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromIndexEndpointName(indexEndpointName: string): string | number;
    /**
     * Parse the location from IndexEndpoint resource.
     *
     * @param {string} indexEndpointName
     *   A fully-qualified path representing IndexEndpoint resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromIndexEndpointName(indexEndpointName: string): string | number;
    /**
     * Parse the index_endpoint from IndexEndpoint resource.
     *
     * @param {string} indexEndpointName
     *   A fully-qualified path representing IndexEndpoint resource.
     * @returns {string} A string representing the index_endpoint.
     */
    matchIndexEndpointFromIndexEndpointName(indexEndpointName: string): string | number;
    /**
     * Return a fully-qualified location resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @returns {string} Resource name string.
     */
    locationPath(project: string, location: string): string;
    /**
     * Parse the project from Location resource.
     *
     * @param {string} locationName
     *   A fully-qualified path representing Location resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromLocationName(locationName: string): string | number;
    /**
     * Parse the location from Location resource.
     *
     * @param {string} locationName
     *   A fully-qualified path representing Location resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromLocationName(locationName: string): string | number;
    /**
     * Return a fully-qualified metadataSchema resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} metadata_store
     * @param {string} metadata_schema
     * @returns {string} Resource name string.
     */
    metadataSchemaPath(project: string, location: string, metadataStore: string, metadataSchema: string): string;
    /**
     * Parse the project from MetadataSchema resource.
     *
     * @param {string} metadataSchemaName
     *   A fully-qualified path representing MetadataSchema resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromMetadataSchemaName(metadataSchemaName: string): string | number;
    /**
     * Parse the location from MetadataSchema resource.
     *
     * @param {string} metadataSchemaName
     *   A fully-qualified path representing MetadataSchema resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromMetadataSchemaName(metadataSchemaName: string): string | number;
    /**
     * Parse the metadata_store from MetadataSchema resource.
     *
     * @param {string} metadataSchemaName
     *   A fully-qualified path representing MetadataSchema resource.
     * @returns {string} A string representing the metadata_store.
     */
    matchMetadataStoreFromMetadataSchemaName(metadataSchemaName: string): string | number;
    /**
     * Parse the metadata_schema from MetadataSchema resource.
     *
     * @param {string} metadataSchemaName
     *   A fully-qualified path representing MetadataSchema resource.
     * @returns {string} A string representing the metadata_schema.
     */
    matchMetadataSchemaFromMetadataSchemaName(metadataSchemaName: string): string | number;
    /**
     * Return a fully-qualified metadataStore resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} metadata_store
     * @returns {string} Resource name string.
     */
    metadataStorePath(project: string, location: string, metadataStore: string): string;
    /**
     * Parse the project from MetadataStore resource.
     *
     * @param {string} metadataStoreName
     *   A fully-qualified path representing MetadataStore resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromMetadataStoreName(metadataStoreName: string): string | number;
    /**
     * Parse the location from MetadataStore resource.
     *
     * @param {string} metadataStoreName
     *   A fully-qualified path representing MetadataStore resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromMetadataStoreName(metadataStoreName: string): string | number;
    /**
     * Parse the metadata_store from MetadataStore resource.
     *
     * @param {string} metadataStoreName
     *   A fully-qualified path representing MetadataStore resource.
     * @returns {string} A string representing the metadata_store.
     */
    matchMetadataStoreFromMetadataStoreName(metadataStoreName: string): string | number;
    /**
     * Return a fully-qualified model resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} model
     * @returns {string} Resource name string.
     */
    modelPath(project: string, location: string, model: string): string;
    /**
     * Parse the project from Model resource.
     *
     * @param {string} modelName
     *   A fully-qualified path representing Model resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromModelName(modelName: string): string | number;
    /**
     * Parse the location from Model resource.
     *
     * @param {string} modelName
     *   A fully-qualified path representing Model resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromModelName(modelName: string): string | number;
    /**
     * Parse the model from Model resource.
     *
     * @param {string} modelName
     *   A fully-qualified path representing Model resource.
     * @returns {string} A string representing the model.
     */
    matchModelFromModelName(modelName: string): string | number;
    /**
     * Return a fully-qualified modelDeploymentMonitoringJob resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} model_deployment_monitoring_job
     * @returns {string} Resource name string.
     */
    modelDeploymentMonitoringJobPath(project: string, location: string, modelDeploymentMonitoringJob: string): string;
    /**
     * Parse the project from ModelDeploymentMonitoringJob resource.
     *
     * @param {string} modelDeploymentMonitoringJobName
     *   A fully-qualified path representing ModelDeploymentMonitoringJob resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromModelDeploymentMonitoringJobName(modelDeploymentMonitoringJobName: string): string | number;
    /**
     * Parse the location from ModelDeploymentMonitoringJob resource.
     *
     * @param {string} modelDeploymentMonitoringJobName
     *   A fully-qualified path representing ModelDeploymentMonitoringJob resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromModelDeploymentMonitoringJobName(modelDeploymentMonitoringJobName: string): string | number;
    /**
     * Parse the model_deployment_monitoring_job from ModelDeploymentMonitoringJob resource.
     *
     * @param {string} modelDeploymentMonitoringJobName
     *   A fully-qualified path representing ModelDeploymentMonitoringJob resource.
     * @returns {string} A string representing the model_deployment_monitoring_job.
     */
    matchModelDeploymentMonitoringJobFromModelDeploymentMonitoringJobName(modelDeploymentMonitoringJobName: string): string | number;
    /**
     * Return a fully-qualified modelEvaluation resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} model
     * @param {string} evaluation
     * @returns {string} Resource name string.
     */
    modelEvaluationPath(project: string, location: string, model: string, evaluation: string): string;
    /**
     * Parse the project from ModelEvaluation resource.
     *
     * @param {string} modelEvaluationName
     *   A fully-qualified path representing ModelEvaluation resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromModelEvaluationName(modelEvaluationName: string): string | number;
    /**
     * Parse the location from ModelEvaluation resource.
     *
     * @param {string} modelEvaluationName
     *   A fully-qualified path representing ModelEvaluation resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromModelEvaluationName(modelEvaluationName: string): string | number;
    /**
     * Parse the model from ModelEvaluation resource.
     *
     * @param {string} modelEvaluationName
     *   A fully-qualified path representing ModelEvaluation resource.
     * @returns {string} A string representing the model.
     */
    matchModelFromModelEvaluationName(modelEvaluationName: string): string | number;
    /**
     * Parse the evaluation from ModelEvaluation resource.
     *
     * @param {string} modelEvaluationName
     *   A fully-qualified path representing ModelEvaluation resource.
     * @returns {string} A string representing the evaluation.
     */
    matchEvaluationFromModelEvaluationName(modelEvaluationName: string): string | number;
    /**
     * Return a fully-qualified modelEvaluationSlice resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} model
     * @param {string} evaluation
     * @param {string} slice
     * @returns {string} Resource name string.
     */
    modelEvaluationSlicePath(project: string, location: string, model: string, evaluation: string, slice: string): string;
    /**
     * Parse the project from ModelEvaluationSlice resource.
     *
     * @param {string} modelEvaluationSliceName
     *   A fully-qualified path representing ModelEvaluationSlice resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromModelEvaluationSliceName(modelEvaluationSliceName: string): string | number;
    /**
     * Parse the location from ModelEvaluationSlice resource.
     *
     * @param {string} modelEvaluationSliceName
     *   A fully-qualified path representing ModelEvaluationSlice resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromModelEvaluationSliceName(modelEvaluationSliceName: string): string | number;
    /**
     * Parse the model from ModelEvaluationSlice resource.
     *
     * @param {string} modelEvaluationSliceName
     *   A fully-qualified path representing ModelEvaluationSlice resource.
     * @returns {string} A string representing the model.
     */
    matchModelFromModelEvaluationSliceName(modelEvaluationSliceName: string): string | number;
    /**
     * Parse the evaluation from ModelEvaluationSlice resource.
     *
     * @param {string} modelEvaluationSliceName
     *   A fully-qualified path representing ModelEvaluationSlice resource.
     * @returns {string} A string representing the evaluation.
     */
    matchEvaluationFromModelEvaluationSliceName(modelEvaluationSliceName: string): string | number;
    /**
     * Parse the slice from ModelEvaluationSlice resource.
     *
     * @param {string} modelEvaluationSliceName
     *   A fully-qualified path representing ModelEvaluationSlice resource.
     * @returns {string} A string representing the slice.
     */
    matchSliceFromModelEvaluationSliceName(modelEvaluationSliceName: string): string | number;
    /**
     * Return a fully-qualified modelMonitor resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} model_monitor
     * @returns {string} Resource name string.
     */
    modelMonitorPath(project: string, location: string, modelMonitor: string): string;
    /**
     * Parse the project from ModelMonitor resource.
     *
     * @param {string} modelMonitorName
     *   A fully-qualified path representing ModelMonitor resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromModelMonitorName(modelMonitorName: string): string | number;
    /**
     * Parse the location from ModelMonitor resource.
     *
     * @param {string} modelMonitorName
     *   A fully-qualified path representing ModelMonitor resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromModelMonitorName(modelMonitorName: string): string | number;
    /**
     * Parse the model_monitor from ModelMonitor resource.
     *
     * @param {string} modelMonitorName
     *   A fully-qualified path representing ModelMonitor resource.
     * @returns {string} A string representing the model_monitor.
     */
    matchModelMonitorFromModelMonitorName(modelMonitorName: string): string | number;
    /**
     * Return a fully-qualified modelMonitoringJob resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} model_monitor
     * @param {string} model_monitoring_job
     * @returns {string} Resource name string.
     */
    modelMonitoringJobPath(project: string, location: string, modelMonitor: string, modelMonitoringJob: string): string;
    /**
     * Parse the project from ModelMonitoringJob resource.
     *
     * @param {string} modelMonitoringJobName
     *   A fully-qualified path representing ModelMonitoringJob resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromModelMonitoringJobName(modelMonitoringJobName: string): string | number;
    /**
     * Parse the location from ModelMonitoringJob resource.
     *
     * @param {string} modelMonitoringJobName
     *   A fully-qualified path representing ModelMonitoringJob resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromModelMonitoringJobName(modelMonitoringJobName: string): string | number;
    /**
     * Parse the model_monitor from ModelMonitoringJob resource.
     *
     * @param {string} modelMonitoringJobName
     *   A fully-qualified path representing ModelMonitoringJob resource.
     * @returns {string} A string representing the model_monitor.
     */
    matchModelMonitorFromModelMonitoringJobName(modelMonitoringJobName: string): string | number;
    /**
     * Parse the model_monitoring_job from ModelMonitoringJob resource.
     *
     * @param {string} modelMonitoringJobName
     *   A fully-qualified path representing ModelMonitoringJob resource.
     * @returns {string} A string representing the model_monitoring_job.
     */
    matchModelMonitoringJobFromModelMonitoringJobName(modelMonitoringJobName: string): string | number;
    /**
     * Return a fully-qualified nasJob resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} nas_job
     * @returns {string} Resource name string.
     */
    nasJobPath(project: string, location: string, nasJob: string): string;
    /**
     * Parse the project from NasJob resource.
     *
     * @param {string} nasJobName
     *   A fully-qualified path representing NasJob resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromNasJobName(nasJobName: string): string | number;
    /**
     * Parse the location from NasJob resource.
     *
     * @param {string} nasJobName
     *   A fully-qualified path representing NasJob resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromNasJobName(nasJobName: string): string | number;
    /**
     * Parse the nas_job from NasJob resource.
     *
     * @param {string} nasJobName
     *   A fully-qualified path representing NasJob resource.
     * @returns {string} A string representing the nas_job.
     */
    matchNasJobFromNasJobName(nasJobName: string): string | number;
    /**
     * Return a fully-qualified nasTrialDetail resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} nas_job
     * @param {string} nas_trial_detail
     * @returns {string} Resource name string.
     */
    nasTrialDetailPath(project: string, location: string, nasJob: string, nasTrialDetail: string): string;
    /**
     * Parse the project from NasTrialDetail resource.
     *
     * @param {string} nasTrialDetailName
     *   A fully-qualified path representing NasTrialDetail resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromNasTrialDetailName(nasTrialDetailName: string): string | number;
    /**
     * Parse the location from NasTrialDetail resource.
     *
     * @param {string} nasTrialDetailName
     *   A fully-qualified path representing NasTrialDetail resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromNasTrialDetailName(nasTrialDetailName: string): string | number;
    /**
     * Parse the nas_job from NasTrialDetail resource.
     *
     * @param {string} nasTrialDetailName
     *   A fully-qualified path representing NasTrialDetail resource.
     * @returns {string} A string representing the nas_job.
     */
    matchNasJobFromNasTrialDetailName(nasTrialDetailName: string): string | number;
    /**
     * Parse the nas_trial_detail from NasTrialDetail resource.
     *
     * @param {string} nasTrialDetailName
     *   A fully-qualified path representing NasTrialDetail resource.
     * @returns {string} A string representing the nas_trial_detail.
     */
    matchNasTrialDetailFromNasTrialDetailName(nasTrialDetailName: string): string | number;
    /**
     * Return a fully-qualified notebookExecutionJob resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} notebook_execution_job
     * @returns {string} Resource name string.
     */
    notebookExecutionJobPath(project: string, location: string, notebookExecutionJob: string): string;
    /**
     * Parse the project from NotebookExecutionJob resource.
     *
     * @param {string} notebookExecutionJobName
     *   A fully-qualified path representing NotebookExecutionJob resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromNotebookExecutionJobName(notebookExecutionJobName: string): string | number;
    /**
     * Parse the location from NotebookExecutionJob resource.
     *
     * @param {string} notebookExecutionJobName
     *   A fully-qualified path representing NotebookExecutionJob resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromNotebookExecutionJobName(notebookExecutionJobName: string): string | number;
    /**
     * Parse the notebook_execution_job from NotebookExecutionJob resource.
     *
     * @param {string} notebookExecutionJobName
     *   A fully-qualified path representing NotebookExecutionJob resource.
     * @returns {string} A string representing the notebook_execution_job.
     */
    matchNotebookExecutionJobFromNotebookExecutionJobName(notebookExecutionJobName: string): string | number;
    /**
     * Return a fully-qualified notebookRuntime resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} notebook_runtime
     * @returns {string} Resource name string.
     */
    notebookRuntimePath(project: string, location: string, notebookRuntime: string): string;
    /**
     * Parse the project from NotebookRuntime resource.
     *
     * @param {string} notebookRuntimeName
     *   A fully-qualified path representing NotebookRuntime resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromNotebookRuntimeName(notebookRuntimeName: string): string | number;
    /**
     * Parse the location from NotebookRuntime resource.
     *
     * @param {string} notebookRuntimeName
     *   A fully-qualified path representing NotebookRuntime resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromNotebookRuntimeName(notebookRuntimeName: string): string | number;
    /**
     * Parse the notebook_runtime from NotebookRuntime resource.
     *
     * @param {string} notebookRuntimeName
     *   A fully-qualified path representing NotebookRuntime resource.
     * @returns {string} A string representing the notebook_runtime.
     */
    matchNotebookRuntimeFromNotebookRuntimeName(notebookRuntimeName: string): string | number;
    /**
     * Return a fully-qualified notebookRuntimeTemplate resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} notebook_runtime_template
     * @returns {string} Resource name string.
     */
    notebookRuntimeTemplatePath(project: string, location: string, notebookRuntimeTemplate: string): string;
    /**
     * Parse the project from NotebookRuntimeTemplate resource.
     *
     * @param {string} notebookRuntimeTemplateName
     *   A fully-qualified path representing NotebookRuntimeTemplate resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromNotebookRuntimeTemplateName(notebookRuntimeTemplateName: string): string | number;
    /**
     * Parse the location from NotebookRuntimeTemplate resource.
     *
     * @param {string} notebookRuntimeTemplateName
     *   A fully-qualified path representing NotebookRuntimeTemplate resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromNotebookRuntimeTemplateName(notebookRuntimeTemplateName: string): string | number;
    /**
     * Parse the notebook_runtime_template from NotebookRuntimeTemplate resource.
     *
     * @param {string} notebookRuntimeTemplateName
     *   A fully-qualified path representing NotebookRuntimeTemplate resource.
     * @returns {string} A string representing the notebook_runtime_template.
     */
    matchNotebookRuntimeTemplateFromNotebookRuntimeTemplateName(notebookRuntimeTemplateName: string): string | number;
    /**
     * Return a fully-qualified persistentResource resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} persistent_resource
     * @returns {string} Resource name string.
     */
    persistentResourcePath(project: string, location: string, persistentResource: string): string;
    /**
     * Parse the project from PersistentResource resource.
     *
     * @param {string} persistentResourceName
     *   A fully-qualified path representing PersistentResource resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromPersistentResourceName(persistentResourceName: string): string | number;
    /**
     * Parse the location from PersistentResource resource.
     *
     * @param {string} persistentResourceName
     *   A fully-qualified path representing PersistentResource resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromPersistentResourceName(persistentResourceName: string): string | number;
    /**
     * Parse the persistent_resource from PersistentResource resource.
     *
     * @param {string} persistentResourceName
     *   A fully-qualified path representing PersistentResource resource.
     * @returns {string} A string representing the persistent_resource.
     */
    matchPersistentResourceFromPersistentResourceName(persistentResourceName: string): string | number;
    /**
     * Return a fully-qualified pipelineJob resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} pipeline_job
     * @returns {string} Resource name string.
     */
    pipelineJobPath(project: string, location: string, pipelineJob: string): string;
    /**
     * Parse the project from PipelineJob resource.
     *
     * @param {string} pipelineJobName
     *   A fully-qualified path representing PipelineJob resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromPipelineJobName(pipelineJobName: string): string | number;
    /**
     * Parse the location from PipelineJob resource.
     *
     * @param {string} pipelineJobName
     *   A fully-qualified path representing PipelineJob resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromPipelineJobName(pipelineJobName: string): string | number;
    /**
     * Parse the pipeline_job from PipelineJob resource.
     *
     * @param {string} pipelineJobName
     *   A fully-qualified path representing PipelineJob resource.
     * @returns {string} A string representing the pipeline_job.
     */
    matchPipelineJobFromPipelineJobName(pipelineJobName: string): string | number;
    /**
     * Return a fully-qualified projectLocationEndpoint resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} endpoint
     * @returns {string} Resource name string.
     */
    projectLocationEndpointPath(project: string, location: string, endpoint: string): string;
    /**
     * Parse the project from ProjectLocationEndpoint resource.
     *
     * @param {string} projectLocationEndpointName
     *   A fully-qualified path representing project_location_endpoint resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromProjectLocationEndpointName(projectLocationEndpointName: string): string | number;
    /**
     * Parse the location from ProjectLocationEndpoint resource.
     *
     * @param {string} projectLocationEndpointName
     *   A fully-qualified path representing project_location_endpoint resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromProjectLocationEndpointName(projectLocationEndpointName: string): string | number;
    /**
     * Parse the endpoint from ProjectLocationEndpoint resource.
     *
     * @param {string} projectLocationEndpointName
     *   A fully-qualified path representing project_location_endpoint resource.
     * @returns {string} A string representing the endpoint.
     */
    matchEndpointFromProjectLocationEndpointName(projectLocationEndpointName: string): string | number;
    /**
     * Return a fully-qualified projectLocationFeatureGroupFeature resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} feature_group
     * @param {string} feature
     * @returns {string} Resource name string.
     */
    projectLocationFeatureGroupFeaturePath(project: string, location: string, featureGroup: string, feature: string): string;
    /**
     * Parse the project from ProjectLocationFeatureGroupFeature resource.
     *
     * @param {string} projectLocationFeatureGroupFeatureName
     *   A fully-qualified path representing project_location_feature_group_feature resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromProjectLocationFeatureGroupFeatureName(projectLocationFeatureGroupFeatureName: string): string | number;
    /**
     * Parse the location from ProjectLocationFeatureGroupFeature resource.
     *
     * @param {string} projectLocationFeatureGroupFeatureName
     *   A fully-qualified path representing project_location_feature_group_feature resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromProjectLocationFeatureGroupFeatureName(projectLocationFeatureGroupFeatureName: string): string | number;
    /**
     * Parse the feature_group from ProjectLocationFeatureGroupFeature resource.
     *
     * @param {string} projectLocationFeatureGroupFeatureName
     *   A fully-qualified path representing project_location_feature_group_feature resource.
     * @returns {string} A string representing the feature_group.
     */
    matchFeatureGroupFromProjectLocationFeatureGroupFeatureName(projectLocationFeatureGroupFeatureName: string): string | number;
    /**
     * Parse the feature from ProjectLocationFeatureGroupFeature resource.
     *
     * @param {string} projectLocationFeatureGroupFeatureName
     *   A fully-qualified path representing project_location_feature_group_feature resource.
     * @returns {string} A string representing the feature.
     */
    matchFeatureFromProjectLocationFeatureGroupFeatureName(projectLocationFeatureGroupFeatureName: string): string | number;
    /**
     * Return a fully-qualified projectLocationFeaturestoreEntityTypeFeature resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} featurestore
     * @param {string} entity_type
     * @param {string} feature
     * @returns {string} Resource name string.
     */
    projectLocationFeaturestoreEntityTypeFeaturePath(project: string, location: string, featurestore: string, entityType: string, feature: string): string;
    /**
     * Parse the project from ProjectLocationFeaturestoreEntityTypeFeature resource.
     *
     * @param {string} projectLocationFeaturestoreEntityTypeFeatureName
     *   A fully-qualified path representing project_location_featurestore_entity_type_feature resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromProjectLocationFeaturestoreEntityTypeFeatureName(projectLocationFeaturestoreEntityTypeFeatureName: string): string | number;
    /**
     * Parse the location from ProjectLocationFeaturestoreEntityTypeFeature resource.
     *
     * @param {string} projectLocationFeaturestoreEntityTypeFeatureName
     *   A fully-qualified path representing project_location_featurestore_entity_type_feature resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromProjectLocationFeaturestoreEntityTypeFeatureName(projectLocationFeaturestoreEntityTypeFeatureName: string): string | number;
    /**
     * Parse the featurestore from ProjectLocationFeaturestoreEntityTypeFeature resource.
     *
     * @param {string} projectLocationFeaturestoreEntityTypeFeatureName
     *   A fully-qualified path representing project_location_featurestore_entity_type_feature resource.
     * @returns {string} A string representing the featurestore.
     */
    matchFeaturestoreFromProjectLocationFeaturestoreEntityTypeFeatureName(projectLocationFeaturestoreEntityTypeFeatureName: string): string | number;
    /**
     * Parse the entity_type from ProjectLocationFeaturestoreEntityTypeFeature resource.
     *
     * @param {string} projectLocationFeaturestoreEntityTypeFeatureName
     *   A fully-qualified path representing project_location_featurestore_entity_type_feature resource.
     * @returns {string} A string representing the entity_type.
     */
    matchEntityTypeFromProjectLocationFeaturestoreEntityTypeFeatureName(projectLocationFeaturestoreEntityTypeFeatureName: string): string | number;
    /**
     * Parse the feature from ProjectLocationFeaturestoreEntityTypeFeature resource.
     *
     * @param {string} projectLocationFeaturestoreEntityTypeFeatureName
     *   A fully-qualified path representing project_location_featurestore_entity_type_feature resource.
     * @returns {string} A string representing the feature.
     */
    matchFeatureFromProjectLocationFeaturestoreEntityTypeFeatureName(projectLocationFeaturestoreEntityTypeFeatureName: string): string | number;
    /**
     * Return a fully-qualified projectLocationPublisherModel resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} publisher
     * @param {string} model
     * @returns {string} Resource name string.
     */
    projectLocationPublisherModelPath(project: string, location: string, publisher: string, model: string): string;
    /**
     * Parse the project from ProjectLocationPublisherModel resource.
     *
     * @param {string} projectLocationPublisherModelName
     *   A fully-qualified path representing project_location_publisher_model resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromProjectLocationPublisherModelName(projectLocationPublisherModelName: string): string | number;
    /**
     * Parse the location from ProjectLocationPublisherModel resource.
     *
     * @param {string} projectLocationPublisherModelName
     *   A fully-qualified path representing project_location_publisher_model resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromProjectLocationPublisherModelName(projectLocationPublisherModelName: string): string | number;
    /**
     * Parse the publisher from ProjectLocationPublisherModel resource.
     *
     * @param {string} projectLocationPublisherModelName
     *   A fully-qualified path representing project_location_publisher_model resource.
     * @returns {string} A string representing the publisher.
     */
    matchPublisherFromProjectLocationPublisherModelName(projectLocationPublisherModelName: string): string | number;
    /**
     * Parse the model from ProjectLocationPublisherModel resource.
     *
     * @param {string} projectLocationPublisherModelName
     *   A fully-qualified path representing project_location_publisher_model resource.
     * @returns {string} A string representing the model.
     */
    matchModelFromProjectLocationPublisherModelName(projectLocationPublisherModelName: string): string | number;
    /**
     * Return a fully-qualified publisherModel resource name string.
     *
     * @param {string} publisher
     * @param {string} model
     * @returns {string} Resource name string.
     */
    publisherModelPath(publisher: string, model: string): string;
    /**
     * Parse the publisher from PublisherModel resource.
     *
     * @param {string} publisherModelName
     *   A fully-qualified path representing PublisherModel resource.
     * @returns {string} A string representing the publisher.
     */
    matchPublisherFromPublisherModelName(publisherModelName: string): string | number;
    /**
     * Parse the model from PublisherModel resource.
     *
     * @param {string} publisherModelName
     *   A fully-qualified path representing PublisherModel resource.
     * @returns {string} A string representing the model.
     */
    matchModelFromPublisherModelName(publisherModelName: string): string | number;
    /**
     * Return a fully-qualified ragCorpus resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} rag_corpus
     * @returns {string} Resource name string.
     */
    ragCorpusPath(project: string, location: string, ragCorpus: string): string;
    /**
     * Parse the project from RagCorpus resource.
     *
     * @param {string} ragCorpusName
     *   A fully-qualified path representing RagCorpus resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromRagCorpusName(ragCorpusName: string): string | number;
    /**
     * Parse the location from RagCorpus resource.
     *
     * @param {string} ragCorpusName
     *   A fully-qualified path representing RagCorpus resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromRagCorpusName(ragCorpusName: string): string | number;
    /**
     * Parse the rag_corpus from RagCorpus resource.
     *
     * @param {string} ragCorpusName
     *   A fully-qualified path representing RagCorpus resource.
     * @returns {string} A string representing the rag_corpus.
     */
    matchRagCorpusFromRagCorpusName(ragCorpusName: string): string | number;
    /**
     * Return a fully-qualified ragFile resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} rag_corpus
     * @param {string} rag_file
     * @returns {string} Resource name string.
     */
    ragFilePath(project: string, location: string, ragCorpus: string, ragFile: string): string;
    /**
     * Parse the project from RagFile resource.
     *
     * @param {string} ragFileName
     *   A fully-qualified path representing RagFile resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromRagFileName(ragFileName: string): string | number;
    /**
     * Parse the location from RagFile resource.
     *
     * @param {string} ragFileName
     *   A fully-qualified path representing RagFile resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromRagFileName(ragFileName: string): string | number;
    /**
     * Parse the rag_corpus from RagFile resource.
     *
     * @param {string} ragFileName
     *   A fully-qualified path representing RagFile resource.
     * @returns {string} A string representing the rag_corpus.
     */
    matchRagCorpusFromRagFileName(ragFileName: string): string | number;
    /**
     * Parse the rag_file from RagFile resource.
     *
     * @param {string} ragFileName
     *   A fully-qualified path representing RagFile resource.
     * @returns {string} A string representing the rag_file.
     */
    matchRagFileFromRagFileName(ragFileName: string): string | number;
    /**
     * Return a fully-qualified reasoningEngine resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} reasoning_engine
     * @returns {string} Resource name string.
     */
    reasoningEnginePath(project: string, location: string, reasoningEngine: string): string;
    /**
     * Parse the project from ReasoningEngine resource.
     *
     * @param {string} reasoningEngineName
     *   A fully-qualified path representing ReasoningEngine resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromReasoningEngineName(reasoningEngineName: string): string | number;
    /**
     * Parse the location from ReasoningEngine resource.
     *
     * @param {string} reasoningEngineName
     *   A fully-qualified path representing ReasoningEngine resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromReasoningEngineName(reasoningEngineName: string): string | number;
    /**
     * Parse the reasoning_engine from ReasoningEngine resource.
     *
     * @param {string} reasoningEngineName
     *   A fully-qualified path representing ReasoningEngine resource.
     * @returns {string} A string representing the reasoning_engine.
     */
    matchReasoningEngineFromReasoningEngineName(reasoningEngineName: string): string | number;
    /**
     * Return a fully-qualified savedQuery resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} dataset
     * @param {string} saved_query
     * @returns {string} Resource name string.
     */
    savedQueryPath(project: string, location: string, dataset: string, savedQuery: string): string;
    /**
     * Parse the project from SavedQuery resource.
     *
     * @param {string} savedQueryName
     *   A fully-qualified path representing SavedQuery resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromSavedQueryName(savedQueryName: string): string | number;
    /**
     * Parse the location from SavedQuery resource.
     *
     * @param {string} savedQueryName
     *   A fully-qualified path representing SavedQuery resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromSavedQueryName(savedQueryName: string): string | number;
    /**
     * Parse the dataset from SavedQuery resource.
     *
     * @param {string} savedQueryName
     *   A fully-qualified path representing SavedQuery resource.
     * @returns {string} A string representing the dataset.
     */
    matchDatasetFromSavedQueryName(savedQueryName: string): string | number;
    /**
     * Parse the saved_query from SavedQuery resource.
     *
     * @param {string} savedQueryName
     *   A fully-qualified path representing SavedQuery resource.
     * @returns {string} A string representing the saved_query.
     */
    matchSavedQueryFromSavedQueryName(savedQueryName: string): string | number;
    /**
     * Return a fully-qualified schedule resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} schedule
     * @returns {string} Resource name string.
     */
    schedulePath(project: string, location: string, schedule: string): string;
    /**
     * Parse the project from Schedule resource.
     *
     * @param {string} scheduleName
     *   A fully-qualified path representing Schedule resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromScheduleName(scheduleName: string): string | number;
    /**
     * Parse the location from Schedule resource.
     *
     * @param {string} scheduleName
     *   A fully-qualified path representing Schedule resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromScheduleName(scheduleName: string): string | number;
    /**
     * Parse the schedule from Schedule resource.
     *
     * @param {string} scheduleName
     *   A fully-qualified path representing Schedule resource.
     * @returns {string} A string representing the schedule.
     */
    matchScheduleFromScheduleName(scheduleName: string): string | number;
    /**
     * Return a fully-qualified specialistPool resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} specialist_pool
     * @returns {string} Resource name string.
     */
    specialistPoolPath(project: string, location: string, specialistPool: string): string;
    /**
     * Parse the project from SpecialistPool resource.
     *
     * @param {string} specialistPoolName
     *   A fully-qualified path representing SpecialistPool resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromSpecialistPoolName(specialistPoolName: string): string | number;
    /**
     * Parse the location from SpecialistPool resource.
     *
     * @param {string} specialistPoolName
     *   A fully-qualified path representing SpecialistPool resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromSpecialistPoolName(specialistPoolName: string): string | number;
    /**
     * Parse the specialist_pool from SpecialistPool resource.
     *
     * @param {string} specialistPoolName
     *   A fully-qualified path representing SpecialistPool resource.
     * @returns {string} A string representing the specialist_pool.
     */
    matchSpecialistPoolFromSpecialistPoolName(specialistPoolName: string): string | number;
    /**
     * Return a fully-qualified study resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} study
     * @returns {string} Resource name string.
     */
    studyPath(project: string, location: string, study: string): string;
    /**
     * Parse the project from Study resource.
     *
     * @param {string} studyName
     *   A fully-qualified path representing Study resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromStudyName(studyName: string): string | number;
    /**
     * Parse the location from Study resource.
     *
     * @param {string} studyName
     *   A fully-qualified path representing Study resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromStudyName(studyName: string): string | number;
    /**
     * Parse the study from Study resource.
     *
     * @param {string} studyName
     *   A fully-qualified path representing Study resource.
     * @returns {string} A string representing the study.
     */
    matchStudyFromStudyName(studyName: string): string | number;
    /**
     * Return a fully-qualified tensorboard resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} tensorboard
     * @returns {string} Resource name string.
     */
    tensorboardPath(project: string, location: string, tensorboard: string): string;
    /**
     * Parse the project from Tensorboard resource.
     *
     * @param {string} tensorboardName
     *   A fully-qualified path representing Tensorboard resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromTensorboardName(tensorboardName: string): string | number;
    /**
     * Parse the location from Tensorboard resource.
     *
     * @param {string} tensorboardName
     *   A fully-qualified path representing Tensorboard resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromTensorboardName(tensorboardName: string): string | number;
    /**
     * Parse the tensorboard from Tensorboard resource.
     *
     * @param {string} tensorboardName
     *   A fully-qualified path representing Tensorboard resource.
     * @returns {string} A string representing the tensorboard.
     */
    matchTensorboardFromTensorboardName(tensorboardName: string): string | number;
    /**
     * Return a fully-qualified tensorboardExperiment resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} tensorboard
     * @param {string} experiment
     * @returns {string} Resource name string.
     */
    tensorboardExperimentPath(project: string, location: string, tensorboard: string, experiment: string): string;
    /**
     * Parse the project from TensorboardExperiment resource.
     *
     * @param {string} tensorboardExperimentName
     *   A fully-qualified path representing TensorboardExperiment resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromTensorboardExperimentName(tensorboardExperimentName: string): string | number;
    /**
     * Parse the location from TensorboardExperiment resource.
     *
     * @param {string} tensorboardExperimentName
     *   A fully-qualified path representing TensorboardExperiment resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromTensorboardExperimentName(tensorboardExperimentName: string): string | number;
    /**
     * Parse the tensorboard from TensorboardExperiment resource.
     *
     * @param {string} tensorboardExperimentName
     *   A fully-qualified path representing TensorboardExperiment resource.
     * @returns {string} A string representing the tensorboard.
     */
    matchTensorboardFromTensorboardExperimentName(tensorboardExperimentName: string): string | number;
    /**
     * Parse the experiment from TensorboardExperiment resource.
     *
     * @param {string} tensorboardExperimentName
     *   A fully-qualified path representing TensorboardExperiment resource.
     * @returns {string} A string representing the experiment.
     */
    matchExperimentFromTensorboardExperimentName(tensorboardExperimentName: string): string | number;
    /**
     * Return a fully-qualified tensorboardRun resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} tensorboard
     * @param {string} experiment
     * @param {string} run
     * @returns {string} Resource name string.
     */
    tensorboardRunPath(project: string, location: string, tensorboard: string, experiment: string, run: string): string;
    /**
     * Parse the project from TensorboardRun resource.
     *
     * @param {string} tensorboardRunName
     *   A fully-qualified path representing TensorboardRun resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromTensorboardRunName(tensorboardRunName: string): string | number;
    /**
     * Parse the location from TensorboardRun resource.
     *
     * @param {string} tensorboardRunName
     *   A fully-qualified path representing TensorboardRun resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromTensorboardRunName(tensorboardRunName: string): string | number;
    /**
     * Parse the tensorboard from TensorboardRun resource.
     *
     * @param {string} tensorboardRunName
     *   A fully-qualified path representing TensorboardRun resource.
     * @returns {string} A string representing the tensorboard.
     */
    matchTensorboardFromTensorboardRunName(tensorboardRunName: string): string | number;
    /**
     * Parse the experiment from TensorboardRun resource.
     *
     * @param {string} tensorboardRunName
     *   A fully-qualified path representing TensorboardRun resource.
     * @returns {string} A string representing the experiment.
     */
    matchExperimentFromTensorboardRunName(tensorboardRunName: string): string | number;
    /**
     * Parse the run from TensorboardRun resource.
     *
     * @param {string} tensorboardRunName
     *   A fully-qualified path representing TensorboardRun resource.
     * @returns {string} A string representing the run.
     */
    matchRunFromTensorboardRunName(tensorboardRunName: string): string | number;
    /**
     * Return a fully-qualified tensorboardTimeSeries resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} tensorboard
     * @param {string} experiment
     * @param {string} run
     * @param {string} time_series
     * @returns {string} Resource name string.
     */
    tensorboardTimeSeriesPath(project: string, location: string, tensorboard: string, experiment: string, run: string, timeSeries: string): string;
    /**
     * Parse the project from TensorboardTimeSeries resource.
     *
     * @param {string} tensorboardTimeSeriesName
     *   A fully-qualified path representing TensorboardTimeSeries resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromTensorboardTimeSeriesName(tensorboardTimeSeriesName: string): string | number;
    /**
     * Parse the location from TensorboardTimeSeries resource.
     *
     * @param {string} tensorboardTimeSeriesName
     *   A fully-qualified path representing TensorboardTimeSeries resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromTensorboardTimeSeriesName(tensorboardTimeSeriesName: string): string | number;
    /**
     * Parse the tensorboard from TensorboardTimeSeries resource.
     *
     * @param {string} tensorboardTimeSeriesName
     *   A fully-qualified path representing TensorboardTimeSeries resource.
     * @returns {string} A string representing the tensorboard.
     */
    matchTensorboardFromTensorboardTimeSeriesName(tensorboardTimeSeriesName: string): string | number;
    /**
     * Parse the experiment from TensorboardTimeSeries resource.
     *
     * @param {string} tensorboardTimeSeriesName
     *   A fully-qualified path representing TensorboardTimeSeries resource.
     * @returns {string} A string representing the experiment.
     */
    matchExperimentFromTensorboardTimeSeriesName(tensorboardTimeSeriesName: string): string | number;
    /**
     * Parse the run from TensorboardTimeSeries resource.
     *
     * @param {string} tensorboardTimeSeriesName
     *   A fully-qualified path representing TensorboardTimeSeries resource.
     * @returns {string} A string representing the run.
     */
    matchRunFromTensorboardTimeSeriesName(tensorboardTimeSeriesName: string): string | number;
    /**
     * Parse the time_series from TensorboardTimeSeries resource.
     *
     * @param {string} tensorboardTimeSeriesName
     *   A fully-qualified path representing TensorboardTimeSeries resource.
     * @returns {string} A string representing the time_series.
     */
    matchTimeSeriesFromTensorboardTimeSeriesName(tensorboardTimeSeriesName: string): string | number;
    /**
     * Return a fully-qualified trainingPipeline resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} training_pipeline
     * @returns {string} Resource name string.
     */
    trainingPipelinePath(project: string, location: string, trainingPipeline: string): string;
    /**
     * Parse the project from TrainingPipeline resource.
     *
     * @param {string} trainingPipelineName
     *   A fully-qualified path representing TrainingPipeline resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromTrainingPipelineName(trainingPipelineName: string): string | number;
    /**
     * Parse the location from TrainingPipeline resource.
     *
     * @param {string} trainingPipelineName
     *   A fully-qualified path representing TrainingPipeline resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromTrainingPipelineName(trainingPipelineName: string): string | number;
    /**
     * Parse the training_pipeline from TrainingPipeline resource.
     *
     * @param {string} trainingPipelineName
     *   A fully-qualified path representing TrainingPipeline resource.
     * @returns {string} A string representing the training_pipeline.
     */
    matchTrainingPipelineFromTrainingPipelineName(trainingPipelineName: string): string | number;
    /**
     * Return a fully-qualified trial resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} study
     * @param {string} trial
     * @returns {string} Resource name string.
     */
    trialPath(project: string, location: string, study: string, trial: string): string;
    /**
     * Parse the project from Trial resource.
     *
     * @param {string} trialName
     *   A fully-qualified path representing Trial resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromTrialName(trialName: string): string | number;
    /**
     * Parse the location from Trial resource.
     *
     * @param {string} trialName
     *   A fully-qualified path representing Trial resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromTrialName(trialName: string): string | number;
    /**
     * Parse the study from Trial resource.
     *
     * @param {string} trialName
     *   A fully-qualified path representing Trial resource.
     * @returns {string} A string representing the study.
     */
    matchStudyFromTrialName(trialName: string): string | number;
    /**
     * Parse the trial from Trial resource.
     *
     * @param {string} trialName
     *   A fully-qualified path representing Trial resource.
     * @returns {string} A string representing the trial.
     */
    matchTrialFromTrialName(trialName: string): string | number;
    /**
     * Return a fully-qualified tuningJob resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} tuning_job
     * @returns {string} Resource name string.
     */
    tuningJobPath(project: string, location: string, tuningJob: string): string;
    /**
     * Parse the project from TuningJob resource.
     *
     * @param {string} tuningJobName
     *   A fully-qualified path representing TuningJob resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromTuningJobName(tuningJobName: string): string | number;
    /**
     * Parse the location from TuningJob resource.
     *
     * @param {string} tuningJobName
     *   A fully-qualified path representing TuningJob resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromTuningJobName(tuningJobName: string): string | number;
    /**
     * Parse the tuning_job from TuningJob resource.
     *
     * @param {string} tuningJobName
     *   A fully-qualified path representing TuningJob resource.
     * @returns {string} A string representing the tuning_job.
     */
    matchTuningJobFromTuningJobName(tuningJobName: string): string | number;
    /**
     * Terminate the gRPC channel and close the client.
     *
     * The client will no longer be usable and all future behavior is undefined.
     * @returns {Promise} A promise that resolves when the client is closed.
     */
    close(): Promise<void>;
}
