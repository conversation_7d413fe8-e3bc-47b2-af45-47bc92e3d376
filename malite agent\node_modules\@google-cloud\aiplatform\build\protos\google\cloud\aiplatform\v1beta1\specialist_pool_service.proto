// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.aiplatform.v1beta1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/aiplatform/v1beta1/operation.proto";
import "google/cloud/aiplatform/v1beta1/specialist_pool.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";

option csharp_namespace = "Google.Cloud.AIPlatform.V1Beta1";
option go_package = "cloud.google.com/go/aiplatform/apiv1beta1/aiplatformpb;aiplatformpb";
option java_multiple_files = true;
option java_outer_classname = "SpecialistPoolServiceProto";
option java_package = "com.google.cloud.aiplatform.v1beta1";
option php_namespace = "Google\\Cloud\\AIPlatform\\V1beta1";
option ruby_package = "Google::Cloud::AIPlatform::V1beta1";

// A service for creating and managing Customer SpecialistPools.
// When customers start Data Labeling jobs, they can reuse/create Specialist
// Pools to bring their own Specialists to label the data.
// Customers can add/remove Managers for the Specialist Pool on Cloud console,
// then Managers will get email notifications to manage Specialists and tasks on
// CrowdCompute console.
service SpecialistPoolService {
  option (google.api.default_host) = "aiplatform.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Creates a SpecialistPool.
  rpc CreateSpecialistPool(CreateSpecialistPoolRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1beta1/{parent=projects/*/locations/*}/specialistPools"
      body: "specialist_pool"
    };
    option (google.api.method_signature) = "parent,specialist_pool";
    option (google.longrunning.operation_info) = {
      response_type: "SpecialistPool"
      metadata_type: "CreateSpecialistPoolOperationMetadata"
    };
  }

  // Gets a SpecialistPool.
  rpc GetSpecialistPool(GetSpecialistPoolRequest) returns (SpecialistPool) {
    option (google.api.http) = {
      get: "/v1beta1/{name=projects/*/locations/*/specialistPools/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists SpecialistPools in a Location.
  rpc ListSpecialistPools(ListSpecialistPoolsRequest)
      returns (ListSpecialistPoolsResponse) {
    option (google.api.http) = {
      get: "/v1beta1/{parent=projects/*/locations/*}/specialistPools"
    };
    option (google.api.method_signature) = "parent";
  }

  // Deletes a SpecialistPool as well as all Specialists in the pool.
  rpc DeleteSpecialistPool(DeleteSpecialistPoolRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1beta1/{name=projects/*/locations/*/specialistPools/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "DeleteOperationMetadata"
    };
  }

  // Updates a SpecialistPool.
  rpc UpdateSpecialistPool(UpdateSpecialistPoolRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1beta1/{specialist_pool.name=projects/*/locations/*/specialistPools/*}"
      body: "specialist_pool"
    };
    option (google.api.method_signature) = "specialist_pool,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "SpecialistPool"
      metadata_type: "UpdateSpecialistPoolOperationMetadata"
    };
  }
}

// Request message for
// [SpecialistPoolService.CreateSpecialistPool][google.cloud.aiplatform.v1beta1.SpecialistPoolService.CreateSpecialistPool].
message CreateSpecialistPoolRequest {
  // Required. The parent Project name for the new SpecialistPool.
  // The form is `projects/{project}/locations/{location}`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Required. The SpecialistPool to create.
  SpecialistPool specialist_pool = 2 [(google.api.field_behavior) = REQUIRED];
}

// Runtime operation information for
// [SpecialistPoolService.CreateSpecialistPool][google.cloud.aiplatform.v1beta1.SpecialistPoolService.CreateSpecialistPool].
message CreateSpecialistPoolOperationMetadata {
  // The operation generic information.
  GenericOperationMetadata generic_metadata = 1;
}

// Request message for
// [SpecialistPoolService.GetSpecialistPool][google.cloud.aiplatform.v1beta1.SpecialistPoolService.GetSpecialistPool].
message GetSpecialistPoolRequest {
  // Required. The name of the SpecialistPool resource.
  // The form is
  // `projects/{project}/locations/{location}/specialistPools/{specialist_pool}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/SpecialistPool"
    }
  ];
}

// Request message for
// [SpecialistPoolService.ListSpecialistPools][google.cloud.aiplatform.v1beta1.SpecialistPoolService.ListSpecialistPools].
message ListSpecialistPoolsRequest {
  // Required. The name of the SpecialistPool's parent resource.
  // Format: `projects/{project}/locations/{location}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // The standard list page size.
  int32 page_size = 2;

  // The standard list page token.
  // Typically obtained by
  // [ListSpecialistPoolsResponse.next_page_token][google.cloud.aiplatform.v1beta1.ListSpecialistPoolsResponse.next_page_token]
  // of the previous
  // [SpecialistPoolService.ListSpecialistPools][google.cloud.aiplatform.v1beta1.SpecialistPoolService.ListSpecialistPools]
  // call. Return first page if empty.
  string page_token = 3;

  // Mask specifying which fields to read. FieldMask represents a set of
  google.protobuf.FieldMask read_mask = 4;
}

// Response message for
// [SpecialistPoolService.ListSpecialistPools][google.cloud.aiplatform.v1beta1.SpecialistPoolService.ListSpecialistPools].
message ListSpecialistPoolsResponse {
  // A list of SpecialistPools that matches the specified filter in the request.
  repeated SpecialistPool specialist_pools = 1;

  // The standard List next-page token.
  string next_page_token = 2;
}

// Request message for
// [SpecialistPoolService.DeleteSpecialistPool][google.cloud.aiplatform.v1beta1.SpecialistPoolService.DeleteSpecialistPool].
message DeleteSpecialistPoolRequest {
  // Required. The resource name of the SpecialistPool to delete. Format:
  // `projects/{project}/locations/{location}/specialistPools/{specialist_pool}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/SpecialistPool"
    }
  ];

  // If set to true, any specialist managers in this SpecialistPool will also be
  // deleted. (Otherwise, the request will only work if the SpecialistPool has
  // no specialist managers.)
  bool force = 2;
}

// Request message for
// [SpecialistPoolService.UpdateSpecialistPool][google.cloud.aiplatform.v1beta1.SpecialistPoolService.UpdateSpecialistPool].
message UpdateSpecialistPoolRequest {
  // Required. The SpecialistPool which replaces the resource on the server.
  SpecialistPool specialist_pool = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The update mask applies to the resource.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Runtime operation metadata for
// [SpecialistPoolService.UpdateSpecialistPool][google.cloud.aiplatform.v1beta1.SpecialistPoolService.UpdateSpecialistPool].
message UpdateSpecialistPoolOperationMetadata {
  // Output only. The name of the SpecialistPool to which the specialists are
  // being added. Format:
  // `projects/{project_id}/locations/{location_id}/specialistPools/{specialist_pool}`
  string specialist_pool = 1 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/SpecialistPool"
    }
  ];

  // The operation generic information.
  GenericOperationMetadata generic_metadata = 2;
}
