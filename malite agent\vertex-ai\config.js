/**
 * تكوين نماذج Vertex AI للاستخدام مع Genkit
 *
 * يوفر هذا الملف تكوينًا موحدًا لنماذج Vertex AI المختلفة
 * ويسهل استخدامها في التطبيقات المختلفة
 */

import { vertexAI } from '@genkit-ai/vertexai';
import dotenv from 'dotenv';

// تحميل متغيرات البيئة
dotenv.config();

// تكوين النماذج المتاحة
const models = {
  // نماذج النصوص
  text: {
    // نماذج Gemini (Google)
    gemini: {
      pro: vertexAI.model('gemini-1.0-pro'),
      ultra: vertexAI.model('gemini-1.0-ultra'),
      flash: vertexAI.model('gemini-1.5-flash'),
      pro15: vertexAI.model('gemini-1.5-pro'),
      flash8b: vertexAI.model('gemini-1.5-flash-8b'),
      pro002: vertexAI.model('gemini-1.5-pro-002'),
    },

    // نماذج PaLM (Google)
    palm: {
      text: vertexAI.model('text-bison'),
      chat: vertexAI.model('chat-bison'),
      textUnicorn: vertexAI.model('text-unicorn'),
      chatUnicorn: vertexAI.model('chat-unicorn'),
    },

    // نماذج Claude (Anthropic)
    claude: {
      haiku: vertexAI.model('claude-3-haiku@20240307'),
      sonnet: vertexAI.model('claude-3-sonnet@20240229'),
      opus: vertexAI.model('claude-3-opus@20240229'),
      sonnet35: vertexAI.model('claude-3-5-sonnet@20240620'),
      sonnet35v2: vertexAI.model('claude-3-5-sonnet-v2@20241022'),
      haiku35: vertexAI.model('claude-3-5-haiku@20241022'),
    },

    // نماذج Llama (Meta)
    llama: {
      llama3_8b: vertexAI.model('llama3-8b-instruct'),
      llama3_70b: vertexAI.model('llama3-70b-instruct'),
      llama31_8b: vertexAI.model('llama3-1-8b-instruct'),
      llama31_70b: vertexAI.model('llama3-1-70b-instruct'),
      llama31_405b: vertexAI.model('llama3-1-405b-instruct'),
      llama32_1b: vertexAI.model('llama3-2-1b-instruct'),
      llama32_3b: vertexAI.model('llama3-2-3b-instruct'),
      llama32_11b: vertexAI.model('llama3-2-11b-vision-instruct'),
      llama32_90b: vertexAI.model('llama3-2-90b-vision-instruct'),
    },

    // نماذج Mistral (Mistral AI)
    mistral: {
      mistral7b: vertexAI.model('mistral-7b-instruct'),
      mixtral8x7b: vertexAI.model('mixtral-8x7b-instruct'),
      mixtral8x22b: vertexAI.model('mixtral-8x22b-instruct'),
      mistralLarge: vertexAI.model('mistral-large'),
      mistralNemo: vertexAI.model('mistral-nemo'),
      codestral: vertexAI.model('codestral'),
    },

    // نماذج Cohere
    cohere: {
      command: vertexAI.model('command'),
      commandLight: vertexAI.model('command-light'),
      commandR: vertexAI.model('command-r'),
      commandRPlus: vertexAI.model('command-r-plus'),
    },

    // نماذج أخرى
    other: {
      // Falcon
      falcon7b: vertexAI.model('falcon-7b-instruct'),
      falcon40b: vertexAI.model('falcon-40b-instruct'),
      // MPT
      mpt7b: vertexAI.model('mpt-7b-instruct'),
      mpt30b: vertexAI.model('mpt-30b-instruct'),
      // Vicuna
      vicuna7b: vertexAI.model('vicuna-7b'),
      vicuna13b: vertexAI.model('vicuna-13b'),
    },
  },

  // نماذج الرؤية (الصور والوسائط المتعددة)
  vision: {
    // Gemini Vision
    geminiVision: vertexAI.model('gemini-1.0-pro-vision'),
    geminiVision15: vertexAI.model('gemini-1.5-pro-vision'),
    geminiFlashVision: vertexAI.model('gemini-1.5-flash'),

    // Claude Vision
    claudeSonnetVision: vertexAI.model('claude-3-sonnet@20240229'),
    claudeOpusVision: vertexAI.model('claude-3-opus@20240229'),
    claudeHaikuVision: vertexAI.model('claude-3-haiku@20240307'),

    // Llama Vision
    llama32Vision11b: vertexAI.model('llama3-2-11b-vision-instruct'),
    llama32Vision90b: vertexAI.model('llama3-2-90b-vision-instruct'),
  },

  // نماذج التضمين (Embedding)
  embedding: {
    // Text Embeddings
    textEmbedding: vertexAI.model('textembedding-gecko'),
    textEmbedding004: vertexAI.model('textembedding-gecko@004'),
    textEmbeddingMultilingual: vertexAI.model('textembedding-gecko-multilingual'),

    // Multimodal Embeddings
    multimodalEmbedding: vertexAI.model('multimodalembedding'),

    // Specialized Embeddings
    textEmbeddingPreview: vertexAI.model('text-embedding-preview-0409'),
    textMultilingualEmbedding: vertexAI.model('text-multilingual-embedding-002'),
  },

  // نماذج متخصصة
  specialized: {
    // Code Generation
    codey: vertexAI.model('code-bison'),
    codeyChat: vertexAI.model('codechat-bison'),
    codeGemma: vertexAI.model('codegemma-7b-it'),

    // Image Generation
    imagen2: vertexAI.model('imagen-2.0'),
    imagen3: vertexAI.model('imagen-3.0'),

    // Audio/Speech
    chirp: vertexAI.model('chirp'),
    speechT5: vertexAI.model('speecht5'),

    // Translation
    translate: vertexAI.model('translate'),

    // Summarization
    pegasus: vertexAI.model('pegasus'),
  },
};

// إعدادات افتراضية للنماذج
const defaultSettings = {
  text: {
    temperature: 0.7,
    maxOutputTokens: 1024,
    topK: 40,
    topP: 0.95,
  },
  vision: {
    temperature: 0.4,
    maxOutputTokens: 1024,
    topK: 32,
    topP: 0.8,
  },
  embedding: {
    // لا توجد إعدادات خاصة للتضمين
  },
  specialized: {
    // إعدادات للنماذج المتخصصة
    temperature: 0.3,
    maxOutputTokens: 2048,
    topK: 20,
    topP: 0.9,
  },
};

/**
 * دالة مساعدة للحصول على نموذج مع الإعدادات المناسبة
 * @param {string} modelType - نوع النموذج (text, vision, embedding, specialized)
 * @param {string} modelName - اسم النموذج (e.g., 'gemini.pro', 'geminiVision', 'codey')
 * @param {Object} customSettings - إعدادات مخصصة (اختياري)
 * @returns {Object} كائن يحتوي على النموذج والإعدادات
 */
function getModel(modelType, modelName, customSettings = {}) {
  // التحقق من صحة نوع النموذج
  if (!['text', 'vision', 'embedding', 'specialized'].includes(modelType)) {
    throw new Error(`نوع النموذج غير صالح: ${modelType}. الأنواع المتاحة هي: text, vision, embedding, specialized`);
  }

  // الحصول على النموذج المطلوب
  let model;
  if (modelType === 'text') {
    const [provider, modelVariant] = modelName.split('.');
    if (!models.text[provider]?.hasOwnProperty(modelVariant)) {
      throw new Error(`النموذج غير موجود: ${modelName}`);
    }
    model = models.text[provider][modelVariant];
  } else if (modelType === 'vision') {
    if (!models.vision[modelName]) {
      throw new Error(`نموذج الرؤية غير موجود: ${modelName}`);
    }
    model = models.vision[modelName];
  } else if (modelType === 'embedding') {
    if (!models.embedding[modelName]) {
      throw new Error(`نموذج التضمين غير موجود: ${modelName}`);
    }
    model = models.embedding[modelName];
  } else if (modelType === 'specialized') {
    if (!models.specialized[modelName]) {
      throw new Error(`النموذج المتخصص غير موجود: ${modelName}`);
    }
    model = models.specialized[modelName];
  }

  // دمج الإعدادات الافتراضية مع الإعدادات المخصصة
  const settings = { ...defaultSettings[modelType], ...customSettings };

  return { model, settings };
}

/**
 * دالة مساعدة للحصول على قائمة بجميع النماذج المتاحة
 * @returns {Object} كائن يحتوي على قوائم النماذج المتاحة حسب النوع
 */
function listAvailableModels() {
  const availableModels = {
    text: [],
    vision: [],
    embedding: [],
    specialized: [],
  };

  // إضافة نماذج النصوص
  Object.keys(models.text).forEach(provider => {
    Object.keys(models.text[provider]).forEach(variant => {
      availableModels.text.push(`${provider}.${variant}`);
    });
  });

  // إضافة نماذج الرؤية
  Object.keys(models.vision).forEach(model => {
    availableModels.vision.push(model);
  });

  // إضافة نماذج التضمين
  Object.keys(models.embedding).forEach(model => {
    availableModels.embedding.push(model);
  });

  // إضافة النماذج المتخصصة
  Object.keys(models.specialized).forEach(model => {
    availableModels.specialized.push(model);
  });

  return availableModels;
}

/**
 * دالة مساعدة للتحقق من توفر نموذج معين
 * @param {string} modelType - نوع النموذج
 * @param {string} modelName - اسم النموذج
 * @returns {boolean} هل النموذج متوفر أم لا
 */
function isModelAvailable(modelType, modelName) {
  try {
    getModel(modelType, modelName);
    return true;
  } catch (error) {
    console.warn(`النموذج غير متوفر: ${modelType}/${modelName}`, error.message);
    return false;
  }
}

/**
 * دالة للحصول على معلومات مفصلة عن النماذج المتاحة
 * @returns {Object} معلومات مفصلة عن النماذج
 */
function getModelInfo() {
  return {
    totalModels: {
      text: Object.keys(models.text).reduce((count, provider) =>
        count + Object.keys(models.text[provider]).length, 0),
      vision: Object.keys(models.vision).length,
      embedding: Object.keys(models.embedding).length,
      specialized: Object.keys(models.specialized).length,
    },
    providers: {
      text: Object.keys(models.text),
      vision: ['Google Gemini', 'Anthropic Claude', 'Meta Llama'],
      embedding: ['Google', 'Specialized'],
      specialized: ['Google', 'Various'],
    },
    capabilities: {
      text: ['محادثة', 'كتابة', 'تحليل', 'ترجمة'],
      vision: ['تحليل الصور', 'وصف المحتوى', 'استخراج النص'],
      embedding: ['تضمين النصوص', 'البحث الدلالي', 'التشابه'],
      specialized: ['توليد الكود', 'توليد الصور', 'معالجة الصوت', 'الترجمة'],
    },
  };
}

// تصدير الدوال والكائنات
export {
  models,
  defaultSettings,
  getModel,
  listAvailableModels,
  isModelAvailable,
  getModelInfo,
};