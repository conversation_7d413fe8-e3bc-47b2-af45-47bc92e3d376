{"version": 3, "sources": ["../../src/tracing/types.ts"], "sourcesContent": ["/**\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { z } from 'zod';\n\n// NOTE: Keep this file in sync with genkit-tools/common/src/types/trace.ts!\n// Eventually tools will be source of truth for these types (by generating a\n// JSON schema) but until then this file must be manually kept in sync\n\nexport const PathMetadataSchema = z.object({\n  path: z.string(),\n  status: z.string(),\n  error: z.string().optional(),\n  latency: z.number(),\n});\nexport type PathMetadata = z.infer<typeof PathMetadataSchema>;\n\nexport const TraceMetadataSchema = z.object({\n  featureName: z.string().optional(),\n  paths: z.set(PathMetadataSchema).optional(),\n  timestamp: z.number(),\n});\nexport type TraceMetadata = z.infer<typeof TraceMetadataSchema>;\n\nexport const SpanMetadataSchema = z.object({\n  name: z.string(),\n  state: z.enum(['success', 'error']).optional(),\n  input: z.any().optional(),\n  output: z.any().optional(),\n  isRoot: z.boolean().optional(),\n  metadata: z.record(z.string(), z.string()).optional(),\n  path: z.string().optional(),\n});\nexport type SpanMetadata = z.infer<typeof SpanMetadataSchema>;\n\nexport const SpanStatusSchema = z.object({\n  code: z.number(),\n  message: z.string().optional(),\n});\n\nexport const TimeEventSchema = z.object({\n  time: z.number(),\n  annotation: z.object({\n    attributes: z.record(z.string(), z.any()),\n    description: z.string(),\n  }),\n});\n\nexport const SpanContextSchema = z.object({\n  traceId: z.string(),\n  spanId: z.string(),\n  isRemote: z.boolean().optional(),\n  traceFlags: z.number(),\n});\n\nexport const LinkSchema = z.object({\n  context: SpanContextSchema.optional(),\n  attributes: z.record(z.string(), z.any()).optional(),\n  droppedAttributesCount: z.number().optional(),\n});\n\nexport const InstrumentationLibrarySchema = z.object({\n  name: z.string().readonly(),\n  version: z.string().optional().readonly(),\n  schemaUrl: z.string().optional().readonly(),\n});\n\nexport const SpanDataSchema = z.object({\n  spanId: z.string(),\n  traceId: z.string(),\n  parentSpanId: z.string().optional(),\n  startTime: z.number(),\n  endTime: z.number(),\n  attributes: z.record(z.string(), z.any()),\n  displayName: z.string(),\n  links: z.array(LinkSchema).optional(),\n  instrumentationLibrary: InstrumentationLibrarySchema,\n  spanKind: z.string(),\n  sameProcessAsParentSpan: z.object({ value: z.boolean() }).optional(),\n  status: SpanStatusSchema.optional(),\n  timeEvents: z\n    .object({\n      timeEvent: z.array(TimeEventSchema),\n    })\n    .optional(),\n  truncated: z.boolean().optional(),\n});\nexport type SpanData = z.infer<typeof SpanDataSchema>;\n\nexport const TraceDataSchema = z.object({\n  traceId: z.string(),\n  displayName: z.string().optional(),\n  startTime: z\n    .number()\n    .optional()\n    .describe('trace start time in milliseconds since the epoch'),\n  endTime: z\n    .number()\n    .optional()\n    .describe('end time in milliseconds since the epoch'),\n  spans: z.record(z.string(), SpanDataSchema),\n});\n\nexport type TraceData = z.infer<typeof TraceDataSchema>;\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAgBA,iBAAkB;AAMX,MAAM,qBAAqB,aAAE,OAAO;AAAA,EACzC,MAAM,aAAE,OAAO;AAAA,EACf,QAAQ,aAAE,OAAO;AAAA,EACjB,OAAO,aAAE,OAAO,EAAE,SAAS;AAAA,EAC3B,SAAS,aAAE,OAAO;AACpB,CAAC;AAGM,MAAM,sBAAsB,aAAE,OAAO;AAAA,EAC1C,aAAa,aAAE,OAAO,EAAE,SAAS;AAAA,EACjC,OAAO,aAAE,IAAI,kBAAkB,EAAE,SAAS;AAAA,EAC1C,WAAW,aAAE,OAAO;AACtB,CAAC;AAGM,MAAM,qBAAqB,aAAE,OAAO;AAAA,EACzC,MAAM,aAAE,OAAO;AAAA,EACf,OAAO,aAAE,KAAK,CAAC,WAAW,OAAO,CAAC,EAAE,SAAS;AAAA,EAC7C,OAAO,aAAE,IAAI,EAAE,SAAS;AAAA,EACxB,QAAQ,aAAE,IAAI,EAAE,SAAS;AAAA,EACzB,QAAQ,aAAE,QAAQ,EAAE,SAAS;AAAA,EAC7B,UAAU,aAAE,OAAO,aAAE,OAAO,GAAG,aAAE,OAAO,CAAC,EAAE,SAAS;AAAA,EACpD,MAAM,aAAE,OAAO,EAAE,SAAS;AAC5B,CAAC;AAGM,MAAM,mBAAmB,aAAE,OAAO;AAAA,EACvC,MAAM,aAAE,OAAO;AAAA,EACf,SAAS,aAAE,OAAO,EAAE,SAAS;AAC/B,CAAC;AAEM,MAAM,kBAAkB,aAAE,OAAO;AAAA,EACtC,MAAM,aAAE,OAAO;AAAA,EACf,YAAY,aAAE,OAAO;AAAA,IACnB,YAAY,aAAE,OAAO,aAAE,OAAO,GAAG,aAAE,IAAI,CAAC;AAAA,IACxC,aAAa,aAAE,OAAO;AAAA,EACxB,CAAC;AACH,CAAC;AAEM,MAAM,oBAAoB,aAAE,OAAO;AAAA,EACxC,SAAS,aAAE,OAAO;AAAA,EAClB,QAAQ,aAAE,OAAO;AAAA,EACjB,UAAU,aAAE,QAAQ,EAAE,SAAS;AAAA,EAC/B,YAAY,aAAE,OAAO;AACvB,CAAC;AAEM,MAAM,aAAa,aAAE,OAAO;AAAA,EACjC,SAAS,kBAAkB,SAAS;AAAA,EACpC,YAAY,aAAE,OAAO,aAAE,OAAO,GAAG,aAAE,IAAI,CAAC,EAAE,SAAS;AAAA,EACnD,wBAAwB,aAAE,OAAO,EAAE,SAAS;AAC9C,CAAC;AAEM,MAAM,+BAA+B,aAAE,OAAO;AAAA,EACnD,MAAM,aAAE,OAAO,EAAE,SAAS;AAAA,EAC1B,SAAS,aAAE,OAAO,EAAE,SAAS,EAAE,SAAS;AAAA,EACxC,WAAW,aAAE,OAAO,EAAE,SAAS,EAAE,SAAS;AAC5C,CAAC;AAEM,MAAM,iBAAiB,aAAE,OAAO;AAAA,EACrC,QAAQ,aAAE,OAAO;AAAA,EACjB,SAAS,aAAE,OAAO;AAAA,EAClB,cAAc,aAAE,OAAO,EAAE,SAAS;AAAA,EAClC,WAAW,aAAE,OAAO;AAAA,EACpB,SAAS,aAAE,OAAO;AAAA,EAClB,YAAY,aAAE,OAAO,aAAE,OAAO,GAAG,aAAE,IAAI,CAAC;AAAA,EACxC,aAAa,aAAE,OAAO;AAAA,EACtB,OAAO,aAAE,MAAM,UAAU,EAAE,SAAS;AAAA,EACpC,wBAAwB;AAAA,EACxB,UAAU,aAAE,OAAO;AAAA,EACnB,yBAAyB,aAAE,OAAO,EAAE,OAAO,aAAE,QAAQ,EAAE,CAAC,EAAE,SAAS;AAAA,EACnE,QAAQ,iBAAiB,SAAS;AAAA,EAClC,YAAY,aACT,OAAO;AAAA,IACN,WAAW,aAAE,MAAM,eAAe;AAAA,EACpC,CAAC,EACA,SAAS;AAAA,EACZ,WAAW,aAAE,QAAQ,EAAE,SAAS;AAClC,CAAC;AAGM,MAAM,kBAAkB,aAAE,OAAO;AAAA,EACtC,SAAS,aAAE,OAAO;AAAA,EAClB,aAAa,aAAE,OAAO,EAAE,SAAS;AAAA,EACjC,WAAW,aACR,OAAO,EACP,SAAS,EACT,SAAS,kDAAkD;AAAA,EAC9D,SAAS,aACN,OAAO,EACP,SAAS,EACT,SAAS,0CAA0C;AAAA,EACtD,OAAO,aAAE,OAAO,aAAE,OAAO,GAAG,cAAc;AAC5C,CAAC;", "names": []}