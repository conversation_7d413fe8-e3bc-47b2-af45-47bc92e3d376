{"version": 3, "file": "Instruments.js", "sourceRoot": "", "sources": ["../../src/Instruments.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,4CAc4B;AAC5B,8CAAqD;AASrD,MAAa,cAAc;IACzB,YACU,sBAA6C,EAC3C,WAAiC;QADnC,2BAAsB,GAAtB,sBAAsB,CAAuB;QAC3C,gBAAW,GAAX,WAAW,CAAsB;IAC1C,CAAC;IAEM,OAAO,CACf,KAAa,EACb,aAAyB,EAAE,EAC3B,UAAmB,aAAU,CAAC,MAAM,EAAE;QAEtC,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;YAC7B,UAAI,CAAC,IAAI,CACP,uCAAuC,IAAI,CAAC,WAAW,CAAC,IAAI,KAAK,KAAK,EAAE,CACzE,CAAC;YACF,OAAO;SACR;QACD,IACE,IAAI,CAAC,WAAW,CAAC,SAAS,KAAK,eAAS,CAAC,GAAG;YAC5C,CAAC,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,EACxB;YACA,UAAI,CAAC,IAAI,CACP,2DAA2D,IAAI,CAAC,WAAW,CAAC,IAAI,mCAAmC,CACpH,CAAC;YACF,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YAC1B,4BAA4B;YAC5B,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE;gBAC5B,OAAO;aACR;SACF;QACD,IAAI,CAAC,sBAAsB,CAAC,MAAM,CAChC,KAAK,EACL,UAAU,EACV,OAAO,EACP,IAAA,qBAAc,EAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAC3B,CAAC;IACJ,CAAC;CACF;AArCD,wCAqCC;AAED;;GAEG;AACH,MAAa,uBACX,SAAQ,cAAc;IAGtB;;OAEG;IACH,GAAG,CAAC,KAAa,EAAE,UAAuB,EAAE,GAAa;QACvD,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,UAAU,EAAE,GAAG,CAAC,CAAC;IACvC,CAAC;CACF;AAVD,0DAUC;AAED;;GAEG;AACH,MAAa,iBAAkB,SAAQ,cAAc;IACnD;;OAEG;IACH,GAAG,CAAC,KAAa,EAAE,UAAuB,EAAE,GAAa;QACvD,IAAI,KAAK,GAAG,CAAC,EAAE;YACb,UAAI,CAAC,IAAI,CACP,sCAAsC,IAAI,CAAC,WAAW,CAAC,IAAI,KAAK,KAAK,EAAE,CACxE,CAAC;YACF,OAAO;SACR;QAED,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,UAAU,EAAE,GAAG,CAAC,CAAC;IACvC,CAAC;CACF;AAdD,8CAcC;AAED;;GAEG;AACH,MAAa,eAAgB,SAAQ,cAAc;IACjD;;OAEG;IACH,MAAM,CAAC,KAAa,EAAE,UAAuB,EAAE,GAAa;QAC1D,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,UAAU,EAAE,GAAG,CAAC,CAAC;IACvC,CAAC;CACF;AAPD,0CAOC;AAED;;GAEG;AACH,MAAa,mBAAoB,SAAQ,cAAc;IACrD;;OAEG;IACH,MAAM,CAAC,KAAa,EAAE,UAAuB,EAAE,GAAa;QAC1D,IAAI,KAAK,GAAG,CAAC,EAAE;YACb,UAAI,CAAC,IAAI,CACP,wCAAwC,IAAI,CAAC,WAAW,CAAC,IAAI,KAAK,KAAK,EAAE,CAC1E,CAAC;YACF,OAAO;SACR;QACD,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,UAAU,EAAE,GAAG,CAAC,CAAC;IACvC,CAAC;CACF;AAbD,kDAaC;AAED,MAAa,oBAAoB;IAM/B,YACE,UAAgC,EAChC,cAA4C,EACpC,mBAAuC;QAAvC,wBAAmB,GAAnB,mBAAmB,CAAoB;QAE/C,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC;QAC9B,IAAI,CAAC,eAAe,GAAG,cAAc,CAAC;IACxC,CAAC;IAED;;OAEG;IACH,WAAW,CAAC,QAA4B;QACtC,IAAI,CAAC,mBAAmB,CAAC,WAAW,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;IACvD,CAAC;IAED;;OAEG;IACH,cAAc,CAAC,QAA4B;QACzC,IAAI,CAAC,mBAAmB,CAAC,cAAc,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;IAC1D,CAAC;CACF;AA5BD,oDA4BC;AAED,MAAa,2BACX,SAAQ,oBAAoB;CACG;AAFjC,kEAEiC;AACjC,MAAa,yBACX,SAAQ,oBAAoB;CACC;AAF/B,8DAE+B;AAC/B,MAAa,iCACX,SAAQ,oBAAoB;CACS;AAFvC,8EAEuC;AAEvC,SAAgB,sBAAsB,CACpC,EAAW;IAEX,OAAO,EAAE,YAAY,oBAAoB,CAAC;AAC5C,CAAC;AAJD,wDAIC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  context as contextApi,\n  diag,\n  Context,\n  Attributes,\n  ValueType,\n  UpDownCounter,\n  Counter,\n  Histogram,\n  Observable,\n  ObservableCallback,\n  ObservableCounter,\n  ObservableGauge,\n  ObservableUpDownCounter,\n} from '@opentelemetry/api';\nimport { millisToHrTime } from '@opentelemetry/core';\nimport { InstrumentDescriptor } from './InstrumentDescriptor';\nimport { ObservableRegistry } from './state/ObservableRegistry';\nimport {\n  AsyncWritableMetricStorage,\n  WritableMetricStorage,\n} from './state/WritableMetricStorage';\nimport { Gauge } from './types';\n\nexport class SyncInstrument {\n  constructor(\n    private _writableMetricStorage: WritableMetricStorage,\n    protected _descriptor: InstrumentDescriptor\n  ) {}\n\n  protected _record(\n    value: number,\n    attributes: Attributes = {},\n    context: Context = contextApi.active()\n  ) {\n    if (typeof value !== 'number') {\n      diag.warn(\n        `non-number value provided to metric ${this._descriptor.name}: ${value}`\n      );\n      return;\n    }\n    if (\n      this._descriptor.valueType === ValueType.INT &&\n      !Number.isInteger(value)\n    ) {\n      diag.warn(\n        `INT value type cannot accept a floating-point value for ${this._descriptor.name}, ignoring the fractional digits.`\n      );\n      value = Math.trunc(value);\n      // ignore non-finite values.\n      if (!Number.isInteger(value)) {\n        return;\n      }\n    }\n    this._writableMetricStorage.record(\n      value,\n      attributes,\n      context,\n      millisToHrTime(Date.now())\n    );\n  }\n}\n\n/**\n * The class implements {@link UpDownCounter} interface.\n */\nexport class UpDownCounterInstrument\n  extends SyncInstrument\n  implements UpDownCounter\n{\n  /**\n   * Increment value of counter by the input. Inputs may be negative.\n   */\n  add(value: number, attributes?: Attributes, ctx?: Context): void {\n    this._record(value, attributes, ctx);\n  }\n}\n\n/**\n * The class implements {@link Counter} interface.\n */\nexport class CounterInstrument extends SyncInstrument implements Counter {\n  /**\n   * Increment value of counter by the input. Inputs may not be negative.\n   */\n  add(value: number, attributes?: Attributes, ctx?: Context): void {\n    if (value < 0) {\n      diag.warn(\n        `negative value provided to counter ${this._descriptor.name}: ${value}`\n      );\n      return;\n    }\n\n    this._record(value, attributes, ctx);\n  }\n}\n\n/**\n * The class implements {@link Gauge} interface.\n */\nexport class GaugeInstrument extends SyncInstrument implements Gauge {\n  /**\n   * Records a measurement.\n   */\n  record(value: number, attributes?: Attributes, ctx?: Context): void {\n    this._record(value, attributes, ctx);\n  }\n}\n\n/**\n * The class implements {@link Histogram} interface.\n */\nexport class HistogramInstrument extends SyncInstrument implements Histogram {\n  /**\n   * Records a measurement. Value of the measurement must not be negative.\n   */\n  record(value: number, attributes?: Attributes, ctx?: Context): void {\n    if (value < 0) {\n      diag.warn(\n        `negative value provided to histogram ${this._descriptor.name}: ${value}`\n      );\n      return;\n    }\n    this._record(value, attributes, ctx);\n  }\n}\n\nexport class ObservableInstrument implements Observable {\n  /** @internal */\n  _metricStorages: AsyncWritableMetricStorage[];\n  /** @internal */\n  _descriptor: InstrumentDescriptor;\n\n  constructor(\n    descriptor: InstrumentDescriptor,\n    metricStorages: AsyncWritableMetricStorage[],\n    private _observableRegistry: ObservableRegistry\n  ) {\n    this._descriptor = descriptor;\n    this._metricStorages = metricStorages;\n  }\n\n  /**\n   * @see {Observable.addCallback}\n   */\n  addCallback(callback: ObservableCallback) {\n    this._observableRegistry.addCallback(callback, this);\n  }\n\n  /**\n   * @see {Observable.removeCallback}\n   */\n  removeCallback(callback: ObservableCallback) {\n    this._observableRegistry.removeCallback(callback, this);\n  }\n}\n\nexport class ObservableCounterInstrument\n  extends ObservableInstrument\n  implements ObservableCounter {}\nexport class ObservableGaugeInstrument\n  extends ObservableInstrument\n  implements ObservableGauge {}\nexport class ObservableUpDownCounterInstrument\n  extends ObservableInstrument\n  implements ObservableUpDownCounter {}\n\nexport function isObservableInstrument(\n  it: unknown\n): it is ObservableInstrument {\n  return it instanceof ObservableInstrument;\n}\n"]}