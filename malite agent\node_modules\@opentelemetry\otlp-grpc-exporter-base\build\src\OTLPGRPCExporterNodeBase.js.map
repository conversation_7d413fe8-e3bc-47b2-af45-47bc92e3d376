{"version": 3, "file": "OTLPGRPCExporterNodeBase.js", "sourceRoot": "", "sources": ["../../src/OTLPGRPCExporterNodeBase.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,4CAA0C;AAE1C,8CAA2D;AAC3D,0EAI2C;AAC3C,uEAGmC;AACnC,iCAAoE;AAIpE;;GAEG;AACH,MAAsB,wBAGpB,SAAQ,qCAAwD;IAMhE,YACE,SAAqC,EAAE,EACvC,sBAA8C,EAC9C,QAAgB,EAChB,QAAgB,EAChB,UAAsD;;QAEtD,KAAK,CAAC,MAAM,CAAC,CAAC;QAZhB,cAAS,GAAgC,EAAE,CAAC;QAa1C,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC;QAC9B,IAAI,MAAM,CAAC,OAAO,EAAE;YAClB,UAAI,CAAC,IAAI,CAAC,uCAAuC,CAAC,CAAC;SACpD;QACD,MAAM,yBAAyB,GAAG,mBAAY,CAAC,uBAAuB,CACpE,IAAA,aAAM,GAAE,CAAC,0BAA0B,CACpC,CAAC;QACF,MAAM,WAAW,GAAG,MAAM,CAAC,MAAM,CAC/B,EAAE,EACF,yBAAyB,EACzB,sBAAsB,CACvB,CAAC;QAEF,IAAI,kBAAkB,GAAG,GAAG,EAAE;YAC5B,OAAO,IAAA,2BAAoB,EAAC,SAAS,EAAE,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC,CAAC;QACxE,CAAC,CAAC;QAEF,IAAI,MAAM,CAAC,WAAW,IAAI,IAAI,EAAE;YAC9B,MAAM,WAAW,GAAG,MAAM,CAAC,WAAW,CAAC;YACvC,kBAAkB,GAAG,GAAG,EAAE;gBACxB,OAAO,WAAW,CAAC;YACrB,CAAC,CAAC;SACH;QAED,uCAAuC;QACvC,MAAM,cAAc,GAAG,MAAA,MAAM,CAAC,QAAQ,0CAAE,KAAK,EAAE,CAAC;QAChD,MAAM,gBAAgB,GAAG,GAAG,EAAE;YAC5B,MAAM,QAAQ,GAAG,cAAc,aAAd,cAAc,cAAd,cAAc,GAAI,IAAA,6CAAmB,GAAE,CAAC;YACzD,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE;gBACtD,4DAA4D;gBAC5D,0DAA0D;gBAC1D,IAAI,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE;oBAChC,QAAQ,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;iBAC1B;aACF;YAED,OAAO,QAAQ,CAAC;QAClB,CAAC,CAAC;QAEF,IAAI,CAAC,WAAW,GAAG,IAAA,2BAAoB,EAAC,MAAM,CAAC,WAAW,CAAC,CAAC;QAC5D,IAAI,CAAC,UAAU,GAAG,IAAI,+CAAqB,CAAC;YAC1C,OAAO,EAAE,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC;YACnC,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,WAAW,EAAE,kBAAkB;YAC/B,QAAQ,EAAE,QAAQ;YAClB,QAAQ,EAAE,QAAQ;YAClB,QAAQ,EAAE,gBAAgB;YAC1B,aAAa,EAAE,IAAI,CAAC,aAAa;SAClC,CAAC,CAAC;IACL,CAAC;IAED,MAAM;QACJ,2CAA2C;IAC7C,CAAC;IAEQ,UAAU;QACjB,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC;IAC7B,CAAC;IAED,IAAI,CACF,OAAqB,EACrB,SAAqB,EACrB,OAA2C;QAE3C,IAAI,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE;YAC/B,UAAI,CAAC,KAAK,CAAC,+CAA+C,CAAC,CAAC;YAC5D,OAAO;SACR;QAED,MAAM,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;QAExD,IAAI,IAAI,IAAI,IAAI,EAAE;YAChB,OAAO,CAAC,IAAI,KAAK,CAAC,6BAA6B,CAAC,CAAC,CAAC;YAClD,OAAO;SACR;QAED,MAAM,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE;YACzD,IAAI,QAAQ,CAAC,MAAM,KAAK,SAAS,EAAE;gBACjC,SAAS,EAAE,CAAC;gBACZ,OAAO;aACR;YACD,IAAI,QAAQ,CAAC,MAAM,KAAK,SAAS,IAAI,QAAQ,CAAC,KAAK,EAAE;gBACnD,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;aACzB;YACD,OAAO,CAAC,IAAI,sCAAiB,CAAC,kCAAkC,CAAC,CAAC,CAAC;QACrE,CAAC,EAAE,OAAO,CAAC,CAAC;QAEZ,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACpC,MAAM,UAAU,GAAG,GAAG,EAAE;YACtB,MAAM,KAAK,GAAG,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;YACrD,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;QACzC,CAAC,CAAC;QACF,OAAO,CAAC,IAAI,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC;IACvC,CAAC;CAGF;AAjHD,4DAiHC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { diag } from '@opentelemetry/api';\nimport { GRPCQueueItem, OTLPGRPCExporterConfigNode } from './types';\nimport { baggageUtils, getEnv } from '@opentelemetry/core';\nimport {\n  CompressionAlgorithm,\n  OTLPExporterBase,\n  OTLPExporterError,\n} from '@opentelemetry/otlp-exporter-base';\nimport {\n  createEmptyMetadata,\n  GrpcExporterTransport,\n} from './grpc-exporter-transport';\nimport { configureCompression, configureCredentials } from './util';\nimport { ISerializer } from '@opentelemetry/otlp-transformer';\nimport { IExporterTransport } from './exporter-transport';\n\n/**\n * OTLP Exporter abstract base class\n */\nexport abstract class OTLPGRPCExporterNodeBase<\n  ExportItem,\n  ServiceResponse,\n> extends OTLPExporterBase<OTLPGRPCExporterConfigNode, ExportItem> {\n  grpcQueue: GRPCQueueItem<ExportItem>[] = [];\n  compression: CompressionAlgorithm;\n  private _transport: IExporterTransport;\n  private _serializer: ISerializer<ExportItem[], ServiceResponse>;\n\n  constructor(\n    config: OTLPGRPCExporterConfigNode = {},\n    signalSpecificMetadata: Record<string, string>,\n    grpcName: string,\n    grpcPath: string,\n    serializer: ISerializer<ExportItem[], ServiceResponse>\n  ) {\n    super(config);\n    this._serializer = serializer;\n    if (config.headers) {\n      diag.warn('Headers cannot be set when using grpc');\n    }\n    const nonSignalSpecificMetadata = baggageUtils.parseKeyPairsIntoRecord(\n      getEnv().OTEL_EXPORTER_OTLP_HEADERS\n    );\n    const rawMetadata = Object.assign(\n      {},\n      nonSignalSpecificMetadata,\n      signalSpecificMetadata\n    );\n\n    let credentialProvider = () => {\n      return configureCredentials(undefined, this.getUrlFromConfig(config));\n    };\n\n    if (config.credentials != null) {\n      const credentials = config.credentials;\n      credentialProvider = () => {\n        return credentials;\n      };\n    }\n\n    // Ensure we don't modify the original.\n    const configMetadata = config.metadata?.clone();\n    const metadataProvider = () => {\n      const metadata = configMetadata ?? createEmptyMetadata();\n      for (const [key, value] of Object.entries(rawMetadata)) {\n        // only override with env var data if the key has no values.\n        // not using Metadata.merge() as it will keep both values.\n        if (metadata.get(key).length < 1) {\n          metadata.set(key, value);\n        }\n      }\n\n      return metadata;\n    };\n\n    this.compression = configureCompression(config.compression);\n    this._transport = new GrpcExporterTransport({\n      address: this.getDefaultUrl(config),\n      compression: this.compression,\n      credentials: credentialProvider,\n      grpcName: grpcName,\n      grpcPath: grpcPath,\n      metadata: metadataProvider,\n      timeoutMillis: this.timeoutMillis,\n    });\n  }\n\n  onInit() {\n    // Intentionally left empty; nothing to do.\n  }\n\n  override onShutdown() {\n    this._transport.shutdown();\n  }\n\n  send(\n    objects: ExportItem[],\n    onSuccess: () => void,\n    onError: (error: OTLPExporterError) => void\n  ): void {\n    if (this._shutdownOnce.isCalled) {\n      diag.debug('Shutdown already started. Cannot send objects');\n      return;\n    }\n\n    const data = this._serializer.serializeRequest(objects);\n\n    if (data == null) {\n      onError(new Error('Could not serialize message'));\n      return;\n    }\n\n    const promise = this._transport.send(data).then(response => {\n      if (response.status === 'success') {\n        onSuccess();\n        return;\n      }\n      if (response.status === 'failure' && response.error) {\n        onError(response.error);\n      }\n      onError(new OTLPExporterError('Export failed with unknown error'));\n    }, onError);\n\n    this._sendingPromises.push(promise);\n    const popPromise = () => {\n      const index = this._sendingPromises.indexOf(promise);\n      this._sendingPromises.splice(index, 1);\n    };\n    promise.then(popPromise, popPromise);\n  }\n\n  abstract getUrlFromConfig(config: OTLPGRPCExporterConfigNode): string;\n}\n"]}