import { z, Genkit } from 'genkit';
import { Role, Part, MessageData, ToolRequestPart, CandidateData, ModelReference, GenerateRequest, ModelAction } from 'genkit/model';
import OpenAI from 'openai';
import { ChatCompletionRole, ChatCompletionContentPart, ChatCompletionMessageParam, ChatCompletionMessageToolCall, ChatCompletionChunk, ChatCompletion } from 'openai/resources/index.mjs';

/**
 * Copyright 2024 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

/**
 * See https://platform.openai.com/docs/api-reference/chat/create.
 */
declare const OpenAIConfigSchema: z.ZodObject<z.objectUtil.extendShape<{
    version: z.ZodOptional<z.ZodString>;
    temperature: z.ZodOptional<z.ZodNumber>;
    maxOutputTokens: z.ZodOptional<z.ZodNumber>;
    topK: z.ZodOptional<z.ZodNumber>;
    topP: z.ZodOptional<z.ZodNumber>;
    stopSequences: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
}, {
    frequencyPenalty: z.ZodOptional<z.ZodNumber>;
    logitBias: z.ZodOptional<z.ZodRecord<z.ZodString, z.ZodNumber>>;
    logProbs: z.ZodOptional<z.ZodBoolean>;
    presencePenalty: z.ZodOptional<z.ZodNumber>;
    seed: z.ZodOptional<z.ZodNumber>;
    topLogProbs: z.ZodOptional<z.ZodNumber>;
    user: z.ZodOptional<z.ZodString>;
}>, "passthrough", z.ZodTypeAny, z.objectOutputType<z.objectUtil.extendShape<{
    version: z.ZodOptional<z.ZodString>;
    temperature: z.ZodOptional<z.ZodNumber>;
    maxOutputTokens: z.ZodOptional<z.ZodNumber>;
    topK: z.ZodOptional<z.ZodNumber>;
    topP: z.ZodOptional<z.ZodNumber>;
    stopSequences: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
}, {
    frequencyPenalty: z.ZodOptional<z.ZodNumber>;
    logitBias: z.ZodOptional<z.ZodRecord<z.ZodString, z.ZodNumber>>;
    logProbs: z.ZodOptional<z.ZodBoolean>;
    presencePenalty: z.ZodOptional<z.ZodNumber>;
    seed: z.ZodOptional<z.ZodNumber>;
    topLogProbs: z.ZodOptional<z.ZodNumber>;
    user: z.ZodOptional<z.ZodString>;
}>, z.ZodTypeAny, "passthrough">, z.objectInputType<z.objectUtil.extendShape<{
    version: z.ZodOptional<z.ZodString>;
    temperature: z.ZodOptional<z.ZodNumber>;
    maxOutputTokens: z.ZodOptional<z.ZodNumber>;
    topK: z.ZodOptional<z.ZodNumber>;
    topP: z.ZodOptional<z.ZodNumber>;
    stopSequences: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
}, {
    frequencyPenalty: z.ZodOptional<z.ZodNumber>;
    logitBias: z.ZodOptional<z.ZodRecord<z.ZodString, z.ZodNumber>>;
    logProbs: z.ZodOptional<z.ZodBoolean>;
    presencePenalty: z.ZodOptional<z.ZodNumber>;
    seed: z.ZodOptional<z.ZodNumber>;
    topLogProbs: z.ZodOptional<z.ZodNumber>;
    user: z.ZodOptional<z.ZodString>;
}>, z.ZodTypeAny, "passthrough">>;
declare function toOpenAIRole(role: Role): ChatCompletionRole;
declare function toOpenAiTextAndMedia(part: Part): ChatCompletionContentPart;
declare function toOpenAiMessages(messages: MessageData[]): ChatCompletionMessageParam[];
declare function fromOpenAiToolCall(toolCall: ChatCompletionMessageToolCall | ChatCompletionChunk.Choice.Delta.ToolCall): ToolRequestPart;
declare function fromOpenAiChoice(choice: ChatCompletion.Choice, jsonMode?: boolean): CandidateData;
declare function fromOpenAiChunkChoice(choice: ChatCompletionChunk.Choice, jsonMode?: boolean): CandidateData;
declare function toRequestBody(model: ModelReference<typeof OpenAIConfigSchema>, request: GenerateRequest<typeof OpenAIConfigSchema>): OpenAI.Chat.Completions.ChatCompletionCreateParamsNonStreaming;
declare function openaiCompatibleModel<C extends typeof OpenAIConfigSchema>(ai: Genkit, model: ModelReference<any>, clientFactory: (request: GenerateRequest<C>) => Promise<OpenAI>): ModelAction<C>;

export { OpenAIConfigSchema, fromOpenAiChoice, fromOpenAiChunkChoice, fromOpenAiToolCall, openaiCompatibleModel, toOpenAIRole, toOpenAiMessages, toOpenAiTextAndMedia, toRequestBody };
