{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../src/platform/index.ts"], "names": [], "mappings": ";;;AAAA;;;;;;;;;;;;;;GAcG;AACH,+BASgB;AARd,yGAAA,iBAAiB,OAAA;AACjB,gGAAA,QAAQ,OAAA;AACR,mGAAA,WAAW,OAAA;AACX,8FAAA,MAAM,OAAA;AACN,6GAAA,qBAAqB,OAAA;AACrB,mGAAA,WAAW,OAAA;AACX,qGAAA,aAAa,OAAA;AACb,kGAAA,UAAU,OAAA", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nexport {\n  RandomIdGenerator,\n  SDK_INFO,\n  _globalThis,\n  getEnv,\n  getEnvWithoutDefaults,\n  hexToBase64,\n  otperformance,\n  unrefTimer,\n} from './node';\n"]}