{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../src/aggregator/index.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAEH,OAAO,EAAE,cAAc,EAAE,MAAM,QAAQ,CAAC;AACxC,OAAO,EAAE,qBAAqB,EAAE,mBAAmB,EAAE,MAAM,aAAa,CAAC;AACzE,OAAO,EACL,gCAAgC,EAChC,8BAA8B,GAC/B,MAAM,wBAAwB,CAAC;AAChC,OAAO,EAAE,qBAAqB,EAAE,mBAAmB,EAAE,MAAM,aAAa,CAAC;AACzE,OAAO,EAAE,eAAe,EAAE,aAAa,EAAE,MAAM,OAAO,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport { DropAggregator } from './Drop';\nexport { HistogramAccumulation, HistogramAggregator } from './Histogram';\nexport {\n  ExponentialHistogramAccumulation,\n  ExponentialHistogramAggregator,\n} from './ExponentialHistogram';\nexport { LastValueAccumulation, LastValueAggregator } from './LastValue';\nexport { SumAccumulation, SumAggregator } from './Sum';\nexport { Aggregator } from './types';\n"]}