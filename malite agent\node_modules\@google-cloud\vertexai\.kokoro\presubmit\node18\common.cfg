# Format: //devtools/kokoro/config/proto/build.proto

# Build logs will be here
action {
  define_artifacts {
    regex: "**/*sponge_log.xml"
  }
}

# Download trampoline resources.
gfile_resources: "/bigstore/cloud-devrel-kokoro-resources/trampoline"

# Use the trampoline script to run in docker.
build_file: "nodejs-vertexai/.kokoro/trampoline_v2.sh"

# Configure the docker image for kokoro-trampoline.
env_vars: {
    key: "TRAMPOLINE_IMAGE"
    value: "gcr.io/cloud-devrel-kokoro-resources/node:18-user"
}
env_vars: {
    key: "TRAMPOLINE_BUILD_FILE"
    value: "github/nodejs-vertexai/.kokoro/test.sh"
}