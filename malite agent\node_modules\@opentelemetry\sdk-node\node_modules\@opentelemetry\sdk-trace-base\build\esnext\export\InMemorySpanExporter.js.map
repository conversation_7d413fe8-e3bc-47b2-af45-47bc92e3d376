{"version": 3, "file": "InMemorySpanExporter.js", "sourceRoot": "", "sources": ["../../../src/export/InMemorySpanExporter.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAIH,OAAO,EAAgB,gBAAgB,EAAE,MAAM,qBAAqB,CAAC;AAErE;;;;GAIG;AACH,MAAM,OAAO,oBAAoB;IAAjC;QACU,mBAAc,GAAmB,EAAE,CAAC;QAC5C;;;WAGG;QACO,aAAQ,GAAG,KAAK,CAAC;IAoC7B,CAAC;IAlCC,MAAM,CACJ,KAAqB,EACrB,cAA8C;QAE9C,IAAI,IAAI,CAAC,QAAQ;YACf,OAAO,cAAc,CAAC;gBACpB,IAAI,EAAE,gBAAgB,CAAC,MAAM;gBAC7B,KAAK,EAAE,IAAI,KAAK,CAAC,2BAA2B,CAAC;aAC9C,CAAC,CAAC;QACL,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,CAAC;QAEnC,UAAU,CAAC,GAAG,EAAE,CAAC,cAAc,CAAC,EAAE,IAAI,EAAE,gBAAgB,CAAC,OAAO,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IAC1E,CAAC;IAED,QAAQ;QACN,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QACrB,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC;QACzB,OAAO,IAAI,CAAC,UAAU,EAAE,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,UAAU;QACR,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;IAC3B,CAAC;IAED,KAAK;QACH,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC;IAC3B,CAAC;IAED,gBAAgB;QACd,OAAO,IAAI,CAAC,cAAc,CAAC;IAC7B,CAAC;CACF", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { SpanExporter } from './SpanExporter';\nimport { ReadableSpan } from './ReadableSpan';\nimport { ExportResult, ExportResultCode } from '@opentelemetry/core';\n\n/**\n * This class can be used for testing purposes. It stores the exported spans\n * in a list in memory that can be retrieved using the `getFinishedSpans()`\n * method.\n */\nexport class InMemorySpanExporter implements SpanExporter {\n  private _finishedSpans: ReadableSpan[] = [];\n  /**\n   * Indicates if the exporter has been \"shutdown.\"\n   * When false, exported spans will not be stored in-memory.\n   */\n  protected _stopped = false;\n\n  export(\n    spans: ReadableSpan[],\n    resultCallback: (result: ExportResult) => void\n  ): void {\n    if (this._stopped)\n      return resultCallback({\n        code: ExportResultCode.FAILED,\n        error: new Error('Exporter has been stopped'),\n      });\n    this._finishedSpans.push(...spans);\n\n    setTimeout(() => resultCallback({ code: ExportResultCode.SUCCESS }), 0);\n  }\n\n  shutdown(): Promise<void> {\n    this._stopped = true;\n    this._finishedSpans = [];\n    return this.forceFlush();\n  }\n\n  /**\n   * Exports any pending spans in the exporter\n   */\n  forceFlush(): Promise<void> {\n    return Promise.resolve();\n  }\n\n  reset(): void {\n    this._finishedSpans = [];\n  }\n\n  getFinishedSpans(): ReadableSpan[] {\n    return this._finishedSpans;\n  }\n}\n"]}