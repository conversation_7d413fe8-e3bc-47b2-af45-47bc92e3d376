# PowerShell Script لتشغيل حاوية Malite Agent

param(
    [string]$ImageName = "malite-agent",
    [string]$Tag = "latest",
    [string]$ContainerName = "malite-app",
    [int]$Port = 8080,
    [string]$Environment = "development",
    [switch]$Detached,
    [switch]$Help
)

function Write-Info {
    param([string]$Message)
    Write-Host "ℹ️  $Message" -ForegroundColor Blue
}

function Write-Success {
    param([string]$Message)
    Write-Host "✅ $Message" -ForegroundColor Green
}

function Write-Error {
    param([string]$Message)
    Write-Host "❌ $Message" -ForegroundColor Red
}

function Show-Help {
    Write-Host "الاستخدام: .\docker-run.ps1 [OPTIONS]"
    Write-Host ""
    Write-Host "الخيارات:"
    Write-Host "  -ImageName NAME          اسم الصورة (افتراضي: malite-agent)"
    Write-Host "  -Tag TAG                علامة الصورة (افتراضي: latest)"
    Write-Host "  -ContainerName NAME      اسم الحاوية (افتراضي: malite-app)"
    Write-Host "  -Port PORT              منفذ التطبيق (افتراضي: 8080)"
    Write-Host "  -Environment ENV         بيئة التشغيل (development|production)"
    Write-Host "  -Detached               تشغيل في الخلفية"
    Write-Host "  -Help                   عرض هذه المساعدة"
    Write-Host ""
    Write-Host "أمثلة:"
    Write-Host "  .\docker-run.ps1                           # تشغيل أساسي"
    Write-Host "  .\docker-run.ps1 -Detached                # تشغيل في الخلفية"
    Write-Host "  .\docker-run.ps1 -Port 3000               # تشغيل على منفذ مختلف"
    Write-Host "  .\docker-run.ps1 -Environment production  # تشغيل في بيئة الإنتاج"
}

function Test-Docker {
    try {
        docker --version | Out-Null
        return $true
    }
    catch {
        Write-Error "Docker غير مثبت أو غير يعمل"
        return $false
    }
}

function Test-Image {
    param([string]$ImageName, [string]$Tag)
    
    $images = docker images --format "{{.Repository}}:{{.Tag}}" | Where-Object { $_ -eq "$ImageName`:$Tag" }
    return $images.Count -gt 0
}

function Stop-ExistingContainer {
    param([string]$ContainerName)
    
    $existing = docker ps -a --format "{{.Names}}" | Where-Object { $_ -eq $ContainerName }
    if ($existing) {
        Write-Info "إيقاف الحاوية الموجودة: $ContainerName"
        docker stop $ContainerName | Out-Null
        docker rm $ContainerName | Out-Null
    }
}

function Start-Container {
    param(
        [string]$ImageName,
        [string]$Tag,
        [string]$ContainerName,
        [int]$Port,
        [string]$Environment,
        [bool]$Detached
    )
    
    # إعداد أمر التشغيل
    $runCommand = "docker run"
    
    if ($Detached) {
        $runCommand += " -d"
    }
    
    $runCommand += " --name $ContainerName"
    $runCommand += " -p $Port`:8080"
    $runCommand += " -e NODE_ENV=$Environment"
    $runCommand += " -e GENKIT_ENV=$Environment"
    $runCommand += " -e PORT=8080"
    
    # إضافة ملف البيئة إذا كان موجوداً
    if (Test-Path ".env") {
        $runCommand += " --env-file .env"
    }
    
    # إضافة ربط مجلد السجلات
    if (-not (Test-Path "logs")) {
        New-Item -ItemType Directory -Path "logs" | Out-Null
    }
    $runCommand += " -v ${PWD}\logs:/app/logs"
    
    $runCommand += " $ImageName`:$Tag"
    
    Write-Info "تنفيذ: $runCommand"
    
    try {
        Invoke-Expression $runCommand
        
        if ($Detached) {
            Write-Success "تم تشغيل الحاوية في الخلفية"
            Write-Info "التطبيق متاح على: http://localhost:$Port"
            Write-Info "فحص الصحة: http://localhost:$Port/health"
            Write-Info "عرض السجلات: docker logs -f $ContainerName"
        }
        else {
            Write-Success "تم تشغيل الحاوية"
        }
    }
    catch {
        Write-Error "فشل في تشغيل الحاوية: $_"
        exit 1
    }
}

function Main {
    if ($Help) {
        Show-Help
        return
    }
    
    Write-Host "🚀 تشغيل حاوية Malite Agent" -ForegroundColor Blue
    Write-Host ""
    
    # التحقق من Docker
    if (-not (Test-Docker)) {
        exit 1
    }
    
    # التحقق من وجود الصورة
    if (-not (Test-Image -ImageName $ImageName -Tag $Tag)) {
        Write-Error "الصورة غير موجودة: $ImageName`:$Tag"
        Write-Info "قم ببناء الصورة أولاً: .\docker-build.ps1"
        exit 1
    }
    
    # إيقاف الحاوية الموجودة
    Stop-ExistingContainer -ContainerName $ContainerName
    
    # تشغيل الحاوية الجديدة
    Start-Container -ImageName $ImageName -Tag $Tag -ContainerName $ContainerName -Port $Port -Environment $Environment -Detached $Detached
    
    if ($Detached) {
        Write-Host ""
        Write-Host "🛠️ أوامر مفيدة:" -ForegroundColor Yellow
        Write-Host "  عرض الحاويات: docker ps"
        Write-Host "  عرض السجلات: docker logs -f $ContainerName"
        Write-Host "  دخول الحاوية: docker exec -it $ContainerName sh"
        Write-Host "  إيقاف الحاوية: docker stop $ContainerName"
        Write-Host "  حذف الحاوية: docker rm $ContainerName"
    }
}

# تشغيل الدالة الرئيسية
Main
