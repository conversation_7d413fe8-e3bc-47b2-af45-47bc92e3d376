"use strict";
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.zodInbound = exports.zodOutbound = void 0;
exports.bytesToBase64 = bytesToBase64;
exports.bytesFromBase64 = bytesFromBase64;
exports.stringToBytes = stringToBytes;
exports.stringFromBytes = stringFromBytes;
exports.stringToBase64 = stringToBase64;
exports.stringFromBase64 = stringFromBase64;
const z = __importStar(require("zod"));
function bytesToBase64(u8arr) {
    return btoa(String.fromCodePoint(...u8arr));
}
function bytesFromBase64(encoded) {
    return Uint8Array.from(atob(encoded), (c) => c.charCodeAt(0));
}
function stringToBytes(str) {
    return new TextEncoder().encode(str);
}
function stringFromBytes(u8arr) {
    return new TextDecoder().decode(u8arr);
}
function stringToBase64(str) {
    return bytesToBase64(stringToBytes(str));
}
function stringFromBase64(b64str) {
    return stringFromBytes(bytesFromBase64(b64str));
}
exports.zodOutbound = z
    .instanceof(Uint8Array)
    .or(z.string().transform(stringToBytes));
exports.zodInbound = z
    .instanceof(Uint8Array)
    .or(z.string().transform(bytesFromBase64));
//# sourceMappingURL=base64.js.map