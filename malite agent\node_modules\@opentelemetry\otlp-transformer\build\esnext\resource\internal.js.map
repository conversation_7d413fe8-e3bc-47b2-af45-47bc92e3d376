{"version": 3, "file": "internal.js", "sourceRoot": "", "sources": ["../../../src/resource/internal.ts"], "names": [], "mappings": "AAgBA,OAAO,EAAE,YAAY,EAAE,MAAM,oBAAoB,CAAC;AAGlD,MAAM,UAAU,cAAc,CAAC,QAAsB;IACnD,OAAO;QACL,UAAU,EAAE,YAAY,CAAC,QAAQ,CAAC,UAAU,CAAC;QAC7C,sBAAsB,EAAE,CAAC;KAC1B,CAAC;AACJ,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { IResource as ISdkResource } from '@opentelemetry/resources';\nimport { toAttributes } from '../common/internal';\nimport { IResource } from './types';\n\nexport function createResource(resource: ISdkResource): IResource {\n  return {\n    attributes: toAttributes(resource.attributes),\n    droppedAttributesCount: 0,\n  };\n}\n"]}