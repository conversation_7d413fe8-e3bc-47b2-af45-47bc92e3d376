// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.aiplatform.v1beta1;

import "google/api/field_behavior.proto";

option csharp_namespace = "Google.Cloud.AIPlatform.V1Beta1";
option go_package = "cloud.google.com/go/aiplatform/apiv1beta1/aiplatformpb;aiplatformpb";
option java_multiple_files = true;
option java_outer_classname = "NotebookEucConfigProto";
option java_package = "com.google.cloud.aiplatform.v1beta1";
option php_namespace = "Google\\Cloud\\AIPlatform\\V1beta1";
option ruby_package = "Google::Cloud::AIPlatform::V1beta1";

// The euc configuration of NotebookRuntimeTemplate.
message NotebookEucConfig {
  // Input only. Whether EUC is disabled in this NotebookRuntimeTemplate.
  // In proto3, the default value of a boolean is false. In this way, by default
  // EUC will be enabled for NotebookRuntimeTemplate.
  bool euc_disabled = 1 [(google.api.field_behavior) = INPUT_ONLY];

  // Output only. Whether ActAs check is bypassed for service account attached
  // to the VM. If false, we need ActAs check for the default Compute Engine
  // Service account. When a Runtime is created, a VM is allocated using Default
  // Compute Engine Service Account. Any user requesting to use this Runtime
  // requires Service Account User (ActAs) permission over this SA. If true,
  // Runtime owner is using EUC and does not require the above permission as VM
  // no longer use default Compute Engine SA, but a P4SA.
  bool bypass_actas_check = 2 [(google.api.field_behavior) = OUTPUT_ONLY];
}
