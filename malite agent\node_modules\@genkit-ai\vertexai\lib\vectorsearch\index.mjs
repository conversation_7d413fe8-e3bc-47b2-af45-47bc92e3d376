import { genkitPlugin } from "genkit/plugin";
import { getDerivedParams } from "../common/index.js";
import { vertexAiIndexers, vertexAiRetrievers } from "./vector_search/index.js";
import {
  getBigQueryDocumentIndexer,
  getBigQueryDocumentRetriever,
  getFirestoreDocumentIndexer,
  getFirestoreDocumentRetriever,
  vertexAiIndexerRef,
  vertexAiIndexers as vertexAiIndexers2,
  vertexAiRetrieverRef,
  vertexAiRetrievers as vertexAiRetrievers2
} from "./vector_search/index.js";
function vertexAIVectorSearch(options) {
  return genkitPlugin("vertexAIVectorSearch", async (ai) => {
    const { authClient } = await getDerivedParams(options);
    if (options?.vectorSearchOptions && options.vectorSearchOptions.length > 0) {
      vertexAiIndexers(ai, {
        pluginOptions: options,
        authClient,
        defaultEmbedder: options.embedder
      });
      vertexAiRetrievers(ai, {
        pluginOptions: options,
        authClient,
        defaultEmbedder: options.embedder
      });
    }
  });
}
export {
  getBigQueryDocumentIndexer,
  getBigQueryDocumentRetriever,
  getFirestoreDocumentIndexer,
  getFirestoreDocumentRetriever,
  vertexAIVectorSearch,
  vertexAiIndexerRef,
  vertexAiIndexers2 as vertexAiIndexers,
  vertexAiRetrieverRef,
  vertexAiRetrievers2 as vertexAiRetrievers
};
//# sourceMappingURL=index.mjs.map