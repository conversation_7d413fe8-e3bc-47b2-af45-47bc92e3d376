{"version": 3, "file": "utils.js", "sourceRoot": "", "sources": ["../../../src/internal/utils.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEH;;;;GAIG;AACH,wBAAwB;AACxB,SAAgB,cAAc,CAAI,MAAyB;IACzD,4EAA4E;IAC5E,IAAI,GAAG,GAAQ,EAAE,CAAC;IAClB,MAAM,GAAG,GAAG,MAAM,CAAC,MAAM,CAAC;IAC1B,KAAK,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,GAAG,EAAE,EAAE,EAAE,EAAE;QAC/B,MAAM,GAAG,GAAG,MAAM,CAAC,EAAE,CAAC,CAAC;QACvB,IAAI,GAAG,EAAE;YACP,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC;SAC5D;KACF;IAED,OAAO,GAAQ,CAAC;AAClB,CAAC;AAZD,wCAYC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * Creates a const map from the given values\n * @param values - An array of values to be used as keys and values in the map.\n * @returns A populated version of the map with the values and keys derived from the values.\n */\n/*#__NO_SIDE_EFFECTS__*/\nexport function createConstMap<T>(values: Array<T[keyof T]>): T {\n  // eslint-disable-next-line prefer-const, @typescript-eslint/no-explicit-any\n  let res: any = {};\n  const len = values.length;\n  for (let lp = 0; lp < len; lp++) {\n    const val = values[lp];\n    if (val) {\n      res[String(val).toUpperCase().replace(/[-.]/g, '_')] = val;\n    }\n  }\n\n  return res as T;\n}\n"]}