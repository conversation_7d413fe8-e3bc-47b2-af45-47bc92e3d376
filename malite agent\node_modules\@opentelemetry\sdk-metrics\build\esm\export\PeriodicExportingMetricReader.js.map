{"version": 3, "file": "PeriodicExportingMetricReader.js", "sourceRoot": "", "sources": ["../../../src/export/PeriodicExportingMetricReader.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEH,OAAO,KAAK,GAAG,MAAM,oBAAoB,CAAC;AAC1C,OAAO,EACL,QAAQ,EACR,gBAAgB,EAChB,kBAAkB,EAClB,UAAU,GACX,MAAM,qBAAqB,CAAC;AAC7B,OAAO,EAAE,YAAY,EAAE,MAAM,gBAAgB,CAAC;AAE9C,OAAO,EAAE,eAAe,EAAE,YAAY,EAAE,MAAM,UAAU,CAAC;AA0BzD;;;GAGG;AACH;IAAmD,iDAAY;IAM7D,uCAAY,OAA6C;;QAAzD,YACE,kBAAM;YACJ,mBAAmB,EAAE,MAAA,OAAO,CAAC,QAAQ,CAAC,iBAAiB,0CAAE,IAAI,CAC3D,OAAO,CAAC,QAAQ,CACjB;YACD,8BAA8B,EAC5B,MAAA,OAAO,CAAC,QAAQ,CAAC,4BAA4B,0CAAE,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC;YACvE,eAAe,EAAE,OAAO,CAAC,eAAe;SACzC,CAAC,SA6BH;QA3BC,IACE,OAAO,CAAC,oBAAoB,KAAK,SAAS;YAC1C,OAAO,CAAC,oBAAoB,IAAI,CAAC,EACjC;YACA,MAAM,KAAK,CAAC,6CAA6C,CAAC,CAAC;SAC5D;QAED,IACE,OAAO,CAAC,mBAAmB,KAAK,SAAS;YACzC,OAAO,CAAC,mBAAmB,IAAI,CAAC,EAChC;YACA,MAAM,KAAK,CAAC,4CAA4C,CAAC,CAAC;SAC3D;QAED,IACE,OAAO,CAAC,mBAAmB,KAAK,SAAS;YACzC,OAAO,CAAC,oBAAoB,KAAK,SAAS;YAC1C,OAAO,CAAC,oBAAoB,GAAG,OAAO,CAAC,mBAAmB,EAC1D;YACA,MAAM,KAAK,CACT,2EAA2E,CAC5E,CAAC;SACH;QAED,KAAI,CAAC,eAAe,GAAG,MAAA,OAAO,CAAC,oBAAoB,mCAAI,KAAK,CAAC;QAC7D,KAAI,CAAC,cAAc,GAAG,MAAA,OAAO,CAAC,mBAAmB,mCAAI,KAAK,CAAC;QAC3D,KAAI,CAAC,SAAS,GAAG,OAAO,CAAC,QAAQ,CAAC;;IACpC,CAAC;IAEa,gDAAQ,GAAtB;;;;;;;wBAEI,qBAAM,eAAe,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,IAAI,CAAC,cAAc,CAAC,EAAA;;wBAAzD,SAAyD,CAAC;;;;wBAE1D,IAAI,KAAG,YAAY,YAAY,EAAE;4BAC/B,GAAG,CAAC,IAAI,CAAC,KAAK,CACZ,wDAAwD,EACxD,IAAI,CAAC,cAAc,CACpB,CAAC;4BACF,sBAAO;yBACR;wBAED,kBAAkB,CAAC,KAAG,CAAC,CAAC;;;;;;KAE3B;IAEa,8CAAM,GAApB;;;;;;;4BACsC,qBAAM,IAAI,CAAC,OAAO,CAAC;4BACrD,aAAa,EAAE,IAAI,CAAC,cAAc;yBACnC,CAAC,EAAA;;wBAFI,KAA8B,SAElC,EAFM,eAAe,qBAAA,EAAE,MAAM,YAAA;wBAI/B,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE;4BACrB,CAAA,KAAA,GAAG,CAAC,IAAI,CAAA,CAAC,KAAK,0BACZ,0DAA0D,UACvD,MAAM,WACT;yBACH;6BAEG,eAAe,CAAC,QAAQ,CAAC,sBAAsB,EAA/C,wBAA+C;;;;wBAE/C,qBAAM,CAAA,MAAA,MAAA,eAAe,CAAC,QAAQ,EAAC,sBAAsB,kDAAI,CAAA,EAAA;;wBAAzD,SAAyD,CAAC;;;;wBAE1D,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,mDAAmD,EAAE,GAAC,CAAC,CAAC;wBACvE,kBAAkB,CAAC,GAAC,CAAC,CAAC;;;wBAI1B,IAAI,eAAe,CAAC,YAAY,CAAC,MAAM,KAAK,CAAC,EAAE;4BAC7C,sBAAO;yBACR;wBAEc,qBAAM,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,EAAE,eAAe,CAAC,EAAA;;wBAAhE,MAAM,GAAG,SAAuD;wBACtE,IAAI,MAAM,CAAC,IAAI,KAAK,gBAAgB,CAAC,OAAO,EAAE;4BAC5C,MAAM,IAAI,KAAK,CACb,iEAA+D,MAAM,CAAC,KAAK,MAAG,CAC/E,CAAC;yBACH;;;;;KACF;IAEkB,qDAAa,GAAhC;QAAA,iBAOC;QANC,iGAAiG;QACjG,IAAI,CAAC,SAAS,GAAG,WAAW,CAAC;YAC3B,wGAAwG;YACxG,KAAK,KAAI,CAAC,QAAQ,EAAE,CAAC;QACvB,CAAC,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;QACzB,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IAC7B,CAAC;IAEe,oDAAY,GAA5B;;;;4BACE,qBAAM,IAAI,CAAC,QAAQ,EAAE,EAAA;;wBAArB,SAAqB,CAAC;wBACtB,qBAAM,IAAI,CAAC,SAAS,CAAC,UAAU,EAAE,EAAA;;wBAAjC,SAAiC,CAAC;;;;;KACnC;IAEe,kDAAU,GAA1B;;;;;wBACE,IAAI,IAAI,CAAC,SAAS,EAAE;4BAClB,aAAa,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;yBAC/B;wBACD,qBAAM,IAAI,CAAC,YAAY,EAAE,EAAA;;wBAAzB,SAAyB,CAAC;wBAC1B,qBAAM,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,EAAA;;wBAA/B,SAA+B,CAAC;;;;;KACjC;IACH,oCAAC;AAAD,CAAC,AAnHD,CAAmD,YAAY,GAmH9D", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport * as api from '@opentelemetry/api';\nimport {\n  internal,\n  ExportResultCode,\n  globalErrorHandler,\n  unrefTimer,\n} from '@opentelemetry/core';\nimport { MetricReader } from './MetricReader';\nimport { PushMetricExporter } from './MetricExporter';\nimport { callWithTimeout, TimeoutError } from '../utils';\nimport { MetricProducer } from './MetricProducer';\n\nexport type PeriodicExportingMetricReaderOptions = {\n  /**\n   * The backing exporter for the metric reader.\n   */\n  exporter: PushMetricExporter;\n  /**\n   * An internal milliseconds for the metric reader to initiate metric\n   * collection.\n   */\n  exportIntervalMillis?: number;\n  /**\n   * Milliseconds for the async observable callback to timeout.\n   */\n  exportTimeoutMillis?: number;\n  /**\n   * **Note, this option is experimental**. Additional MetricProducers to use as a source of\n   * aggregated metric data in addition to the SDK's metric data. The resource returned by\n   * these MetricProducers is ignored; the SDK's resource will be used instead.\n   * @experimental\n   */\n  metricProducers?: MetricProducer[];\n};\n\n/**\n * {@link MetricReader} which collects metrics based on a user-configurable time interval, and passes the metrics to\n * the configured {@link PushMetricExporter}\n */\nexport class PeriodicExportingMetricReader extends MetricReader {\n  private _interval?: ReturnType<typeof setInterval>;\n  private _exporter: PushMetricExporter;\n  private readonly _exportInterval: number;\n  private readonly _exportTimeout: number;\n\n  constructor(options: PeriodicExportingMetricReaderOptions) {\n    super({\n      aggregationSelector: options.exporter.selectAggregation?.bind(\n        options.exporter\n      ),\n      aggregationTemporalitySelector:\n        options.exporter.selectAggregationTemporality?.bind(options.exporter),\n      metricProducers: options.metricProducers,\n    });\n\n    if (\n      options.exportIntervalMillis !== undefined &&\n      options.exportIntervalMillis <= 0\n    ) {\n      throw Error('exportIntervalMillis must be greater than 0');\n    }\n\n    if (\n      options.exportTimeoutMillis !== undefined &&\n      options.exportTimeoutMillis <= 0\n    ) {\n      throw Error('exportTimeoutMillis must be greater than 0');\n    }\n\n    if (\n      options.exportTimeoutMillis !== undefined &&\n      options.exportIntervalMillis !== undefined &&\n      options.exportIntervalMillis < options.exportTimeoutMillis\n    ) {\n      throw Error(\n        'exportIntervalMillis must be greater than or equal to exportTimeoutMillis'\n      );\n    }\n\n    this._exportInterval = options.exportIntervalMillis ?? 60000;\n    this._exportTimeout = options.exportTimeoutMillis ?? 30000;\n    this._exporter = options.exporter;\n  }\n\n  private async _runOnce(): Promise<void> {\n    try {\n      await callWithTimeout(this._doRun(), this._exportTimeout);\n    } catch (err) {\n      if (err instanceof TimeoutError) {\n        api.diag.error(\n          'Export took longer than %s milliseconds and timed out.',\n          this._exportTimeout\n        );\n        return;\n      }\n\n      globalErrorHandler(err);\n    }\n  }\n\n  private async _doRun(): Promise<void> {\n    const { resourceMetrics, errors } = await this.collect({\n      timeoutMillis: this._exportTimeout,\n    });\n\n    if (errors.length > 0) {\n      api.diag.error(\n        'PeriodicExportingMetricReader: metrics collection errors',\n        ...errors\n      );\n    }\n\n    if (resourceMetrics.resource.asyncAttributesPending) {\n      try {\n        await resourceMetrics.resource.waitForAsyncAttributes?.();\n      } catch (e) {\n        api.diag.debug('Error while resolving async portion of resource: ', e);\n        globalErrorHandler(e);\n      }\n    }\n\n    if (resourceMetrics.scopeMetrics.length === 0) {\n      return;\n    }\n\n    const result = await internal._export(this._exporter, resourceMetrics);\n    if (result.code !== ExportResultCode.SUCCESS) {\n      throw new Error(\n        `PeriodicExportingMetricReader: metrics export failed (error ${result.error})`\n      );\n    }\n  }\n\n  protected override onInitialized(): void {\n    // start running the interval as soon as this reader is initialized and keep handle for shutdown.\n    this._interval = setInterval(() => {\n      // this._runOnce never rejects. Using void operator to suppress @typescript-eslint/no-floating-promises.\n      void this._runOnce();\n    }, this._exportInterval);\n    unrefTimer(this._interval);\n  }\n\n  protected async onForceFlush(): Promise<void> {\n    await this._runOnce();\n    await this._exporter.forceFlush();\n  }\n\n  protected async onShutdown(): Promise<void> {\n    if (this._interval) {\n      clearInterval(this._interval);\n    }\n    await this.onForceFlush();\n    await this._exporter.shutdown();\n  }\n}\n"]}