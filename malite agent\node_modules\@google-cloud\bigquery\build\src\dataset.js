"use strict";
/*!
 * Copyright 2014 Google Inc. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.Dataset = void 0;
const common_1 = require("@google-cloud/common");
const paginator_1 = require("@google-cloud/paginator");
const promisify_1 = require("@google-cloud/promisify");
const extend = require("extend");
const table_1 = require("./table");
const model_1 = require("./model");
const routine_1 = require("./routine");
/**
 * Interact with your BigQuery dataset. Create a Dataset instance with
 * {@link BigQuery#createDataset} or {@link BigQuery#dataset}.
 *
 * @class
 * @param {BigQuery} bigQuery {@link BigQuery} instance.
 * @param {string} id The ID of the Dataset.
 * @param {object} [options] Dataset options.
 * @param {string} [options.projectId] The GCP project ID.
 * @param {string} [options.location] The geographic location of the dataset.
 *      Defaults to US.
 *
 * @example
 * ```
 * const {BigQuery} = require('@google-cloud/bigquery');
 * const bigquery = new BigQuery();
 * const dataset = bigquery.dataset('institutions');
 * ```
 */
class Dataset extends common_1.ServiceObject {
    getModelsStream(options) {
        // placeholder body, overwritten in constructor
        return new paginator_1.ResourceStream({}, () => { });
    }
    getRoutinesStream(options) {
        // placeholder body, overwritten in constructor
        return new paginator_1.ResourceStream({}, () => { });
    }
    getTablesStream(options) {
        // placeholder body, overwritten in constructor
        return new paginator_1.ResourceStream({}, () => { });
    }
    constructor(bigQuery, id, options) {
        const methods = {
            /**
             * @callback CreateDatasetCallback
             * @param {?Error} err Request error, if any.
             * @param {Dataset} dataset The newly created dataset.
             * @param {object} apiResponse The full API response.
             */
            /**
             * @typedef {array} CreateDatasetResponse
             * @property {Dataset} 0 The newly created dataset.
             * @property {object} 1 The full API response body.
             */
            /**
             * Create a dataset.
             *
             * @method Dataset#create
             * @param {CreateDatasetCallback} [callback] The callback function.
             * @param {?error} callback.err An error returned while making this
             *     request.
             * @param {Dataset} callback.dataset The newly created dataset.
             * @param {object} callback.apiResponse The full API response.
             * @returns {Promise<CreateDatasetResponse>}
             *
             * @example
             * ```
             * const {BigQuery} = require('@google-cloud/bigquery');
             * const bigquery = new BigQuery();
             * const dataset = bigquery.dataset('institutions');
             * dataset.create((err, dataset, apiResponse) => {
             *   if (!err) {
             *     // The dataset was created successfully.
             *   }
             * });
             *
             * //-
             * // If the callback is omitted, we'll return a Promise.
             * //-
             * dataset.create().then((data) => {
             *   const dataset = data[0];
             *   const apiResponse = data[1];
             * });
             * ```
             */
            create: true,
            /**
             * @callback DatasetExistsCallback
             * @param {?Error} err Request error, if any.
             * @param {boolean} exists Indicates if the dataset exists.
             */
            /**
             * @typedef {array} DatasetExistsResponse
             * @property {boolean} 0 Indicates if the dataset exists.
             */
            /**
             * Check if the dataset exists.
             *
             * @method Dataset#exists
             * @param {DatasetExistsCallback} [callback] The callback function.
             * @param {?error} callback.err An error returned while making this
             *     request.
             * @param {boolean} callback.exists Whether the dataset exists or not.
             * @returns {Promise<DatasetExistsResponse>}
             *
             * @example
             * ```
             * const {BigQuery} = require('@google-cloud/bigquery');
             * const bigquery = new BigQuery();
             * const dataset = bigquery.dataset('institutions');
             * dataset.exists((err, exists) => {});
             *
             * //-
             * // If the callback is omitted, we'll return a Promise.
             * //-
             * dataset.exists().then((data) => {
             *   const exists = data[0];
             * });
             * ```
             */
            exists: true,
            /**
             * @callback GetDatasetCallback
             * @param {?Error} err Request error, if any.
             * @param {Dataset} dataset The dataset.
             * @param {object} apiResponse The full API response body.
             */
            /**
             * @typedef {array} GetDatasetResponse
             * @property {Dataset} 0 The dataset.
             * @property {object} 1 The full API response body.
             */
            /**
             * Get a dataset if it exists.
             *
             * You may optionally use this to "get or create" an object by providing
             * an object with `autoCreate` set to `true`. Any extra configuration that
             * is normally required for the `create` method must be contained within
             * this object as well.
             *
             * @method Dataset#get
             * @param {options} [options] Configuration object.
             * @param {boolean} [options.autoCreate=false] Automatically create the
             *     object if it does not exist.
             * @param {GetDatasetCallback} [callback] The callback function.
             * @param {?error} callback.err An error returned while making this
             *     request.
             * @param {Dataset} callback.dataset The dataset.
             * @param {object} callback.apiResponse The full API response.
             * @returns {Promise<GetDatasetResponse>}
             *
             * @example
             * ```
             * const {BigQuery} = require('@google-cloud/bigquery');
             * const bigquery = new BigQuery();
             * const dataset = bigquery.dataset('institutions');
             * dataset.get((err, dataset, apiResponse) => {
             *   if (!err) {
             *     // `dataset.metadata` has been populated.
             *   }
             * });
             *
             * //-
             * // If the callback is omitted, we'll return a Promise.
             * //-
             * dataset.get().then((data) => {
             *   const dataset = data[0];
             *   const apiResponse = data[1];
             * });
             * ```
             */
            get: true,
            /**
             * @callback GetDatasetMetadataCallback
             * @param {?Error} err Request error, if any.
             * @param {object} metadata The dataset metadata.
             * @param {object} apiResponse The full API response.
             */
            /**
             * @typedef {array} GetDatasetMetadataResponse
             * @property {object} 0 The dataset metadata.
             * @property {object} 1 The full API response.
             */
            /**
             * Get the metadata for the Dataset.
             *
             * See {@link https://cloud.google.com/bigquery/docs/reference/v2/datasets/get| Datasets: get API Documentation}
             *
             * @method Dataset#getMetadata
             * @param {GetDatasetMetadataCallback} [callback] The callback function.
             * @param {?error} callback.err An error returned while making this
             *     request.
             * @param {object} callback.metadata The dataset's metadata.
             * @param {object} callback.apiResponse The full API response.
             * @returns {Promise<GetDatasetMetadataResponse>}
             *
             * @example
             * ```
             * const {BigQuery} = require('@google-cloud/bigquery');
             * const bigquery = new BigQuery();
             * const dataset = bigquery.dataset('institutions');
             * dataset.getMetadata((err, metadata, apiResponse) => {});
             *
             * //-
             * // If the callback is omitted, we'll return a Promise.
             * //-
             * dataset.getMetadata().then((data) => {
             *   const metadata = data[0];
             *   const apiResponse = data[1];
             * });
             * ```
             */
            getMetadata: true,
            /**
             * @callback SetDatasetMetadataCallback
             * @param {?Error} err Request error, if any.
             * @param {object} apiResponse The full API response.
             */
            /**
             * @typedef {array} SetDatasetMetadataResponse
             * @property {object} 0 The full API response.
             */
            /**
             * Sets the metadata of the Dataset object.
             *
             * See {@link https://cloud.google.com/bigquery/docs/reference/v2/datasets/patch| Datasets: patch API Documentation}
             *
             * @method Dataset#setMetadata
             * @param {object} metadata Metadata to save on the Dataset.
             * @param {SetDatasetMetadataCallback} [callback] The callback function.
             * @param {?error} callback.err An error returned while making this
             *     request.
             * @param {object} callback.apiResponse The full API response.
             * @returns {Promise<SetDatasetMetadataResponse>}
             *
             * @example
             * ```
             * const {BigQuery} = require('@google-cloud/bigquery');
             * const bigquery = new BigQuery();
             * const dataset = bigquery.dataset('institutions');
             *
             * const metadata = {
             *   description: 'Info for every institution in the 2013 IPEDS universe'
             * };
             *
             * dataset.setMetadata(metadata, (err, apiResponse) => {});
             *
             * //-
             * // If the callback is omitted, we'll return a Promise.
             * //-
             * dataset.setMetadata(metadata).then((data) => {
             *   const apiResponse = data[0];
             * });
             * ```
             */
            setMetadata: true,
        };
        super({
            parent: bigQuery,
            baseUrl: '/datasets',
            id,
            methods,
            createMethod: (id, optionsOrCallback, cb) => {
                let options = typeof optionsOrCallback === 'object' ? optionsOrCallback : {};
                const callback = typeof optionsOrCallback === 'function'
                    ? optionsOrCallback
                    : cb;
                if (this.location) {
                    options = extend({}, options, { location: this.location });
                }
                if (this.projectId) {
                    options = extend({}, options, { projectId: this.projectId });
                }
                return bigQuery.createDataset(id, options, callback);
            },
        });
        if (options && options.location) {
            this.location = options.location;
        }
        if (options === null || options === void 0 ? void 0 : options.projectId) {
            this.projectId = options.projectId;
        }
        else {
            this.projectId = bigQuery.projectId;
        }
        this.bigQuery = bigQuery;
        // Catch all for read-modify-write cycle
        // https://cloud.google.com/bigquery/docs/api-performance#read-patch-write
        this.interceptors.push({
            request: (reqOpts) => {
                if (reqOpts.method === 'PATCH' && reqOpts.json.etag) {
                    reqOpts.headers = reqOpts.headers || {};
                    reqOpts.headers['If-Match'] = reqOpts.json.etag;
                }
                if (this.projectId) {
                    // Override projectId if provided
                    reqOpts.uri = reqOpts.uri.replace(`/projects/${this.bigQuery.projectId}/`, `/projects/${this.projectId}/`);
                }
                return reqOpts;
            },
        });
        /**
         * List all or some of the {@link Model} objects in your project
         * as a readable object stream.
         *
         * @method Dataset#getModelsStream
         * @param {object} [options] Configuration object. See
         *     {@link Dataset#getModels} for a complete list of options.
         * @return {stream}
         *
         * @example
         * ```
         * const {BigQuery} = require('@google-cloud/bigquery');
         * const bigquery = new BigQuery();
         * const dataset = bigquery.dataset('institutions');
         *
         * dataset.getModelsStream()
         *   .on('error', console.error)
         *   .on('data', (model) => {})
         *   .on('end', () => {
         *     // All models have been retrieved
         *   });
         *
         * ```
         * @example If you anticipate many results, you can end a stream early to prevent unnecessary processing and API requests.
         * ```
         * dataset.getModelsStream()
         *   .on('data', function(model) {
         *     this.end();
         *   });
         * ```
         */
        this.getModelsStream = paginator_1.paginator.streamify('getModels');
        /**
         * List all or some of the {@link Routine} objects in your project as a
         * readable object stream.
         *
         * @method Dataset#getRoutinesStream
         * @param {GetRoutinesOptions} [options] Configuration object.
         * @returns {stream}
         *
         * @example
         * ```
         * const {BigQuery} = require('@google-cloud/bigquery');
         * const bigquery = new BigQuery();
         * const dataset = bigquery.dataset('institutions');
         *
         * dataset.getRoutinesStream()
         *   .on('error', console.error)
         *   .on('data', (routine) => {})
         *   .on('end', () => {
         *     // All routines have been retrieved
         *   });
         *
         * ```
         * @example If you anticipate many results, you can end a stream early to prevent unnecessary processing and API requests.
         * ```
         * dataset.getRoutinesStream()
         *   .on('data', function(routine) {
         *     this.end();
         *   });
         * ```
         */
        this.getRoutinesStream = paginator_1.paginator.streamify('getRoutines');
        /**
         * List all or some of the {@link Table} objects in your project
         * as a readable object stream.
         *
         * @method Dataset#getTablesStream
         * @param {object} [options] Configuration object. See
         *     {@link Dataset#getTables} for a complete list of options.
         * @return {stream}
         *
         * @example
         * ```
         * const {BigQuery} = require('@google-cloud/bigquery');
         * const bigquery = new BigQuery();
         * const dataset = bigquery.dataset('institutions');
         *
         * dataset.getTablesStream()
         *   .on('error', console.error)
         *   .on('data', (table) => {})
         *   .on('end', () => {
         *     // All tables have been retrieved
         *   });
         *
         * //-
         * // If you anticipate many results, you can end a stream early to prevent
         * // unnecessary processing and API requests.
         * //-
         * dataset.getTablesStream()
         *   .on('data', function(table) {
         *     this.end();
         *   });
         * ```
         */
        this.getTablesStream = paginator_1.paginator.streamify('getTables');
    }
    createQueryJob(options, callback) {
        if (typeof options === 'string') {
            options = {
                query: options,
            };
        }
        options = extend(true, {}, options, {
            defaultDataset: {
                datasetId: this.id,
            },
            location: this.location,
        });
        return this.bigQuery.createQueryJob(options, callback);
    }
    /**
     * Run a query scoped to your dataset as a readable object stream.
     *
     * See {@link BigQuery#createQueryStream} for full documentation of this
     * method.
     *
     * @param {object} options See {@link BigQuery#createQueryStream} for full
     *     documentation of this method.
     * @returns {stream}
     */
    createQueryStream(options) {
        if (typeof options === 'string') {
            options = {
                query: options,
            };
        }
        options = extend(true, {}, options, {
            defaultDataset: {
                datasetId: this.id,
            },
            location: this.location,
        });
        return this.bigQuery.createQueryStream(options);
    }
    createRoutine(id, config, callback) {
        const json = Object.assign({}, config, {
            routineReference: {
                routineId: id,
                datasetId: this.id,
                projectId: this.projectId,
            },
        });
        this.request({
            method: 'POST',
            uri: '/routines',
            json,
        }, (err, resp) => {
            if (err) {
                callback(err, null, resp);
                return;
            }
            const routine = this.routine(resp.routineReference.routineId);
            routine.metadata = resp;
            callback(null, routine, resp);
        });
    }
    createTable(id, optionsOrCallback, cb) {
        const options = typeof optionsOrCallback === 'object' ? optionsOrCallback : {};
        const callback = typeof optionsOrCallback === 'function' ? optionsOrCallback : cb;
        const body = table_1.Table.formatMetadata_(options);
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        body.tableReference = {
            datasetId: this.id,
            projectId: this.projectId,
            tableId: id,
        };
        this.request({
            method: 'POST',
            uri: '/tables',
            json: body,
        }, (err, resp) => {
            if (err) {
                callback(err, null, resp);
                return;
            }
            const table = this.table(resp.tableReference.tableId, {
                location: resp.location,
            });
            table.metadata = resp;
            callback(null, table, resp);
        });
    }
    delete(optionsOrCallback, callback) {
        const options = typeof optionsOrCallback === 'object' ? optionsOrCallback : {};
        callback =
            typeof optionsOrCallback === 'function' ? optionsOrCallback : callback;
        const query = {
            deleteContents: !!options.force,
        };
        this.request({
            method: 'DELETE',
            uri: '',
            qs: query,
        }, callback);
    }
    getModels(optsOrCb, cb) {
        const options = typeof optsOrCb === 'object' ? optsOrCb : {};
        const callback = typeof optsOrCb === 'function' ? optsOrCb : cb;
        this.request({
            uri: '/models',
            qs: options,
        }, (err, resp) => {
            if (err) {
                callback(err, null, null, resp);
                return;
            }
            let nextQuery = null;
            if (resp.nextPageToken) {
                nextQuery = Object.assign({}, options, {
                    pageToken: resp.nextPageToken,
                });
            }
            const models = (resp.models || []).map(modelObject => {
                const model = this.model(modelObject.modelReference.modelId);
                model.metadata = modelObject;
                return model;
            });
            callback(null, models, nextQuery, resp);
        });
    }
    getRoutines(optsOrCb, cb) {
        const options = typeof optsOrCb === 'object' ? optsOrCb : {};
        const callback = typeof optsOrCb === 'function' ? optsOrCb : cb;
        this.request({
            uri: '/routines',
            qs: options,
        }, (err, resp) => {
            if (err) {
                callback(err, null, null, resp);
                return;
            }
            let nextQuery = null;
            if (resp.nextPageToken) {
                nextQuery = Object.assign({}, options, {
                    pageToken: resp.nextPageToken,
                });
            }
            const routines = (resp.routines || []).map(metadata => {
                const routine = this.routine(metadata.routineReference.routineId);
                routine.metadata = metadata;
                return routine;
            });
            callback(null, routines, nextQuery, resp);
        });
    }
    getTables(optionsOrCallback, cb) {
        const options = typeof optionsOrCallback === 'object' ? optionsOrCallback : {};
        const callback = typeof optionsOrCallback === 'function' ? optionsOrCallback : cb;
        this.request({
            uri: '/tables',
            qs: options,
        }, (err, resp) => {
            if (err) {
                callback(err, null, null, resp);
                return;
            }
            let nextQuery = null;
            if (resp.nextPageToken) {
                nextQuery = Object.assign({}, options, {
                    pageToken: resp.nextPageToken,
                });
            }
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            const tables = (resp.tables || []).map((tableObject) => {
                const table = this.table(tableObject.tableReference.tableId, {
                    location: tableObject.location,
                });
                table.metadata = tableObject;
                return table;
            });
            callback(null, tables, nextQuery, resp);
        });
    }
    /**
     * Create a {@link Model} object.
     *
     * @throws {TypeError} if model ID is missing.
     *
     * @param {string} id The ID of the model.
     * @return {Model}
     *
     * @example
     * ```
     * const {BigQuery} = require('@google-cloud/bigquery');
     * const bigquery = new BigQuery();
     * const dataset = bigquery.dataset('institutions');
     *
     * const model = dataset.model('my-model');
     * ```
     */
    model(id) {
        if (typeof id !== 'string') {
            throw new TypeError('A model ID is required.');
        }
        return new model_1.Model(this, id);
    }
    query(options, callback) {
        if (typeof options === 'string') {
            options = {
                query: options,
            };
        }
        options = extend(true, {}, options, {
            defaultDataset: {
                datasetId: this.id,
            },
            location: this.location,
        });
        return this.bigQuery.query(options, callback);
    }
    /**
     * Create a {@link Routine} object.
     *
     * @throws {TypeError} if routine ID is missing.
     *
     * @param {string} id The ID of the routine.
     * @returns {Routine}
     *
     * @example
     * ```
     * const {BigQuery} = require('@google-cloud/bigquery');
     * const bigquery = new BigQuery();
     * const dataset = bigquery.dataset('institutions');
     *
     * const routine = dataset.routine('my_routine');
     * ```
     */
    routine(id) {
        if (typeof id !== 'string') {
            throw new TypeError('A routine ID is required.');
        }
        return new routine_1.Routine(this, id);
    }
    /**
     * Create a {@link Table} object.
     *
     * @throws {TypeError} if table ID is missing.
     *
     * @param {string} id The ID of the table.
     * @param {object} [options] Table options.
     * @param {string} [options.location] The geographic location of the table, by
     *      default this value is inherited from the dataset. This can be used to
     *      configure the location of all jobs created through a table instance.
     * It cannot be used to set the actual location of the table. This value will
     *      be superseded by any API responses containing location data for the
     *      table.
     * @return {Table}
     *
     * @example
     * ```
     * const {BigQuery} = require('@google-cloud/bigquery');
     * const bigquery = new BigQuery();
     * const dataset = bigquery.dataset('institutions');
     *
     * const institutions = dataset.table('institution_data');
     * ```
     */
    table(id, options) {
        if (typeof id !== 'string') {
            throw new TypeError('A table ID is required.');
        }
        options = extend({
            location: this.location,
            projectId: this.projectId,
        }, options);
        return new table_1.Table(this, id, options);
    }
}
exports.Dataset = Dataset;
/*! Developer Documentation
 *
 * These methods can be auto-paginated.
 */
paginator_1.paginator.extend(Dataset, ['getModels', 'getRoutines', 'getTables']);
/*! Developer Documentation
 *
 * All async methods (except for streams) will return a Promise in the event
 * that a callback is omitted.
 */
(0, promisify_1.promisifyAll)(Dataset, {
    exclude: ['model', 'routine', 'table'],
});
//# sourceMappingURL=dataset.js.map