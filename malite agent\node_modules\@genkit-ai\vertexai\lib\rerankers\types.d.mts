import { z } from 'genkit';
import { GoogleAuth } from 'google-auth-library';
import { C as CommonPluginOptions } from '../types-Bc0LKM8D.mjs';
import '@google-cloud/vertexai';
import 'genkit/model';

/**
 * Copyright 2024 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

declare const VertexAIRerankerOptionsSchema: z.ZodObject<{
    k: z.ZodOptional<z.ZodNumber>;
    model: z.ZodOptional<z.ZodString>;
    location: z.ZodOptional<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    model?: string | undefined;
    location?: string | undefined;
    k?: number | undefined;
}, {
    model?: string | undefined;
    location?: string | undefined;
    k?: number | undefined;
}>;
type VertexAIRerankerOptions = z.infer<typeof VertexAIRerankerOptionsSchema>;
interface VertexRerankerConfig {
    name?: string;
    model?: string;
}
interface VertexRerankOptions {
    authClient: GoogleAuth;
    location: string;
    projectId: string;
    rerankOptions: VertexRerankerConfig[];
}
interface RerankerOptions {
    /** Configure reranker options */
    rerankers: (string | VertexRerankerConfig)[];
}
interface PluginOptions extends CommonPluginOptions, RerankerOptions {
}

export { type PluginOptions, type RerankerOptions, type VertexAIRerankerOptions, VertexAIRerankerOptionsSchema, type VertexRerankOptions, type VertexRerankerConfig };
