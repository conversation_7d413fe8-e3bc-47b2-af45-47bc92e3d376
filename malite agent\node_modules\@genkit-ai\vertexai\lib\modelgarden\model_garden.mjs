import { GENKIT_CLIENT_HEADER, z } from "genkit";
import { modelRef } from "genkit/model";
import OpenAI from "openai";
import {
  openaiCompatibleModel,
  OpenAIConfigSchema
} from "./openai_compatibility.js";
const ModelGardenModelConfigSchema = OpenAIConfigSchema.extend({
  location: z.string().optional()
});
const llama31 = modelRef({
  name: "vertexai/llama-3.1",
  info: {
    label: "Llama 3.1",
    supports: {
      multiturn: true,
      tools: true,
      media: false,
      systemRole: true,
      output: ["text", "json"]
    },
    versions: [
      "meta/llama3-405b-instruct-maas"
      // 8b and 70b versions are coming soon
    ]
  },
  configSchema: ModelGardenModelConfigSchema,
  version: "meta/llama3-405b-instruct-maas"
});
const llama32 = modelRef({
  name: "vertexai/llama-3.2",
  info: {
    label: "Llama 3.2",
    supports: {
      multiturn: true,
      tools: true,
      media: true,
      systemRole: true,
      output: ["text", "json"]
    },
    versions: ["meta/llama-3.2-90b-vision-instruct-maas"]
  },
  configSchema: ModelGardenModelConfigSchema,
  version: "meta/llama-3.2-90b-vision-instruct-maas"
});
const llama3 = modelRef({
  name: "vertexai/llama3-405b",
  info: {
    label: "Llama 3.1 405b",
    supports: {
      multiturn: true,
      tools: true,
      media: false,
      systemRole: true,
      output: ["text"]
    },
    versions: ["meta/llama3-405b-instruct-maas"]
  },
  configSchema: ModelGardenModelConfigSchema,
  version: "meta/llama3-405b-instruct-maas"
});
const SUPPORTED_OPENAI_FORMAT_MODELS = {
  "llama3-405b": llama3,
  "llama-3.1": llama31,
  "llama-3.2": llama32
};
function modelGardenOpenaiCompatibleModel(ai, name, projectId, location, googleAuth, baseUrlTemplate) {
  const model = SUPPORTED_OPENAI_FORMAT_MODELS[name];
  if (!model) throw new Error(`Unsupported model: ${name}`);
  if (!baseUrlTemplate) {
    baseUrlTemplate = "https://{location}-aiplatform.googleapis.com/v1beta1/projects/{projectId}/locations/{location}/endpoints/openapi";
  }
  const clientFactory = async (request) => {
    const requestLocation = request.config?.location || location;
    return new OpenAI({
      baseURL: baseUrlTemplate.replace(/{location}/g, requestLocation).replace(/{projectId}/g, projectId),
      apiKey: await googleAuth.getAccessToken(),
      defaultHeaders: {
        "X-Goog-Api-Client": GENKIT_CLIENT_HEADER
      }
    });
  };
  return openaiCompatibleModel(ai, model, clientFactory);
}
export {
  ModelGardenModelConfigSchema,
  SUPPORTED_OPENAI_FORMAT_MODELS,
  llama3,
  llama31,
  llama32,
  modelGardenOpenaiCompatibleModel
};
//# sourceMappingURL=model_garden.mjs.map