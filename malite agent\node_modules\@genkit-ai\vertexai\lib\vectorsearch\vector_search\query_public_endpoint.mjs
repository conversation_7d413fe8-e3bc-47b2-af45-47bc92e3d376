async function queryPublicEndpoint(params) {
  const {
    featureVector,
    neighborCount,
    accessToken,
    indexEndpointId,
    publicDomainName,
    projectNumber,
    deployedIndexId,
    location,
    restricts,
    numericRestricts
  } = params;
  const url = new URL(
    `https://${publicDomainName}/v1/projects/${projectNumber}/locations/${location}/indexEndpoints/${indexEndpointId}:findNeighbors`
  );
  const requestBody = {
    deployed_index_id: deployedIndexId,
    queries: [
      {
        datapoint: {
          datapoint_id: "0",
          feature_vector: featureVector,
          restricts: restricts?.map((r) => ({
            namespace: r.namespace,
            allow_list: r.allowList,
            deny_list: r.denyList
          })) || [],
          numeric_restricts: numericRestricts?.map((nr) => {
            const newNR = {
              namespace: nr.namespace
            };
            if (nr.valueInt !== void 0) {
              newNR.value_int = nr.valueInt;
            }
            if (nr.valueFloat !== void 0) {
              newNR.value_float = nr.valueFloat;
            }
            if (nr.valueDouble !== void 0) {
              newNR.value_double = nr.valueDouble;
            }
            newNR.op = nr.op;
            return newNR;
          }) || []
        },
        neighbor_count: neighborCount
      }
    ]
  };
  const response = await fetch(url, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${accessToken}`
    },
    body: JSON.stringify(requestBody)
  });
  if (!response.ok) {
    const errMsg = (await response.json()).error?.message || "";
    throw new Error(`Error querying index: ${response.statusText}. ${errMsg}`);
  }
  return await response.json();
}
export {
  queryPublicEndpoint
};
//# sourceMappingURL=query_public_endpoint.mjs.map