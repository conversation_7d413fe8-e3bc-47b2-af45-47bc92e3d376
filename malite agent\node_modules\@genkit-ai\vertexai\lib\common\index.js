"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
var common_exports = {};
__export(common_exports, {
  __setFakeDerivedParams: () => __setFakeDerivedParams,
  getDerivedParams: () => getDerivedParams
});
module.exports = __toCommonJS(common_exports);
var import_vertexai = require("@google-cloud/vertexai");
var import_google_auth_library = require("google-auth-library");
var import_constants = require("./constants.js");
function parseFirebaseProjectId() {
  if (!process.env.FIREBASE_CONFIG) return void 0;
  try {
    return JSON.parse(process.env.FIREBASE_CONFIG).projectId;
  } catch {
    return void 0;
  }
}
function __setFakeDerivedParams(params) {
  __fake_getDerivedParams = params;
}
let __fake_getDerivedParams;
async function getDerivedParams(options) {
  if (__fake_getDerivedParams) {
    return __fake_getDerivedParams;
  }
  let authOptions = options?.googleAuth;
  let authClient;
  const providedProjectId = options?.projectId || process.env.GCLOUD_PROJECT || parseFirebaseProjectId();
  if (process.env.GCLOUD_SERVICE_ACCOUNT_CREDS) {
    const serviceAccountCreds = JSON.parse(
      process.env.GCLOUD_SERVICE_ACCOUNT_CREDS
    );
    authOptions = {
      credentials: serviceAccountCreds,
      scopes: [import_constants.CLOUD_PLATFORM_OAUTH_SCOPE],
      projectId: providedProjectId
    };
    authClient = new import_google_auth_library.GoogleAuth(authOptions);
  } else {
    authClient = new import_google_auth_library.GoogleAuth(
      authOptions ?? {
        scopes: [import_constants.CLOUD_PLATFORM_OAUTH_SCOPE],
        projectId: providedProjectId
      }
    );
  }
  const projectId = options?.projectId || await authClient.getProjectId();
  const location = options?.location || "us-central1";
  if (!location) {
    throw new Error(
      `VertexAI Plugin is missing the 'location' configuration. Please set the 'GCLOUD_LOCATION' environment variable or explicitly pass 'location' into genkit config.`
    );
  }
  if (!projectId) {
    throw new Error(
      `VertexAI Plugin is missing the 'project' configuration. Please set the 'GCLOUD_PROJECT' environment variable or explicitly pass 'project' into genkit config.`
    );
  }
  const vertexClientFactoryCache = {};
  const vertexClientFactory = (request) => {
    const requestLocation = request.config?.location || location;
    if (!vertexClientFactoryCache[requestLocation]) {
      vertexClientFactoryCache[requestLocation] = new import_vertexai.VertexAI({
        project: projectId,
        location: requestLocation,
        googleAuthOptions: { projectId: providedProjectId, ...authOptions }
      });
    }
    return vertexClientFactoryCache[requestLocation];
  };
  return {
    location,
    projectId,
    vertexClientFactory,
    authClient
  };
}
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  __setFakeDerivedParams,
  getDerivedParams
});
//# sourceMappingURL=index.js.map