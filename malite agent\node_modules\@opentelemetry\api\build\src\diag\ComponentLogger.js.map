{"version": 3, "file": "ComponentLogger.js", "sourceRoot": "", "sources": ["../../../src/diag/ComponentLogger.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,2DAAqD;AAGrD;;;;;;;;GAQG;AACH,MAAa,mBAAmB;IAG9B,YAAY,KAA6B;QACvC,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC,SAAS,IAAI,qBAAqB,CAAC;IAC7D,CAAC;IAEM,KAAK,CAAC,GAAG,IAAW;QACzB,OAAO,QAAQ,CAAC,OAAO,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;IAClD,CAAC;IAEM,KAAK,CAAC,GAAG,IAAW;QACzB,OAAO,QAAQ,CAAC,OAAO,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;IAClD,CAAC;IAEM,IAAI,CAAC,GAAG,IAAW;QACxB,OAAO,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;IACjD,CAAC;IAEM,IAAI,CAAC,GAAG,IAAW;QACxB,OAAO,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;IACjD,CAAC;IAEM,OAAO,CAAC,GAAG,IAAW;QAC3B,OAAO,QAAQ,CAAC,SAAS,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;IACpD,CAAC;CACF;AA1BD,kDA0BC;AAED,SAAS,QAAQ,CACf,QAA0B,EAC1B,SAAiB,EACjB,IAAS;IAET,MAAM,MAAM,GAAG,IAAA,wBAAS,EAAC,MAAM,CAAC,CAAC;IACjC,6BAA6B;IAC7B,IAAI,CAAC,MAAM,EAAE;QACX,OAAO;KACR;IAED,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;IACxB,OAAO,MAAM,CAAC,QAAQ,CAAC,CAAC,GAAI,IAAoC,CAAC,CAAC;AACpE,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { getGlobal } from '../internal/global-utils';\nimport { ComponentLoggerOptions, DiagLogger, DiagLogFunction } from './types';\n\n/**\n * Component Logger which is meant to be used as part of any component which\n * will add automatically additional namespace in front of the log message.\n * It will then forward all message to global diag logger\n * @example\n * const cLogger = diag.createComponentLogger({ namespace: '@opentelemetry/instrumentation-http' });\n * cLogger.debug('test');\n * // @opentelemetry/instrumentation-http test\n */\nexport class DiagComponentLogger implements DiagLogger {\n  private _namespace: string;\n\n  constructor(props: ComponentLoggerOptions) {\n    this._namespace = props.namespace || 'DiagComponentLogger';\n  }\n\n  public debug(...args: any[]): void {\n    return logProxy('debug', this._namespace, args);\n  }\n\n  public error(...args: any[]): void {\n    return logProxy('error', this._namespace, args);\n  }\n\n  public info(...args: any[]): void {\n    return logProxy('info', this._namespace, args);\n  }\n\n  public warn(...args: any[]): void {\n    return logProxy('warn', this._namespace, args);\n  }\n\n  public verbose(...args: any[]): void {\n    return logProxy('verbose', this._namespace, args);\n  }\n}\n\nfunction logProxy(\n  funcName: keyof DiagLogger,\n  namespace: string,\n  args: any\n): void {\n  const logger = getGlobal('diag');\n  // shortcut if logger not set\n  if (!logger) {\n    return;\n  }\n\n  args.unshift(namespace);\n  return logger[funcName](...(args as Parameters<DiagLogFunction>));\n}\n"]}