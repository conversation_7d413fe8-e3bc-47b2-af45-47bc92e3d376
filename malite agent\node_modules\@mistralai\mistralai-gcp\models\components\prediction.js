"use strict";
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Prediction$ = exports.Prediction$outboundSchema = exports.Prediction$inboundSchema = void 0;
exports.predictionToJSON = predictionToJSON;
exports.predictionFromJSON = predictionFromJSON;
const z = __importStar(require("zod"));
const schemas_js_1 = require("../../lib/schemas.js");
/** @internal */
exports.Prediction$inboundSchema = z.object({
    type: z.literal("content").default("content"),
    content: z.string().default(""),
});
/** @internal */
exports.Prediction$outboundSchema = z.object({
    type: z.literal("content").default("content"),
    content: z.string().default(""),
});
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var Prediction$;
(function (Prediction$) {
    /** @deprecated use `Prediction$inboundSchema` instead. */
    Prediction$.inboundSchema = exports.Prediction$inboundSchema;
    /** @deprecated use `Prediction$outboundSchema` instead. */
    Prediction$.outboundSchema = exports.Prediction$outboundSchema;
})(Prediction$ || (exports.Prediction$ = Prediction$ = {}));
function predictionToJSON(prediction) {
    return JSON.stringify(exports.Prediction$outboundSchema.parse(prediction));
}
function predictionFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.Prediction$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'Prediction' from JSON`);
}
//# sourceMappingURL=prediction.js.map