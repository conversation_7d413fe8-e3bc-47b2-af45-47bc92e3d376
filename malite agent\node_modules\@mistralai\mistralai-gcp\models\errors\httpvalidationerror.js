"use strict";
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.HTTPValidationError$ = exports.HTTPValidationError$outboundSchema = exports.HTTPValidationError$inboundSchema = exports.HTTPValidationError = void 0;
const z = __importStar(require("zod"));
const components = __importStar(require("../components/index.js"));
class HTTPValidationError extends Error {
    constructor(err) {
        const message = "message" in err && typeof err.message === "string"
            ? err.message
            : `API error occurred: ${JSON.stringify(err)}`;
        super(message);
        this.data$ = err;
        if (err.detail != null)
            this.detail = err.detail;
        this.name = "HTTPValidationError";
    }
}
exports.HTTPValidationError = HTTPValidationError;
/** @internal */
exports.HTTPValidationError$inboundSchema = z.object({
    detail: z.array(components.ValidationError$inboundSchema).optional(),
})
    .transform((v) => {
    return new HTTPValidationError(v);
});
/** @internal */
exports.HTTPValidationError$outboundSchema = z.instanceof(HTTPValidationError)
    .transform(v => v.data$)
    .pipe(z.object({
    detail: z.array(components.ValidationError$outboundSchema).optional(),
}));
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var HTTPValidationError$;
(function (HTTPValidationError$) {
    /** @deprecated use `HTTPValidationError$inboundSchema` instead. */
    HTTPValidationError$.inboundSchema = exports.HTTPValidationError$inboundSchema;
    /** @deprecated use `HTTPValidationError$outboundSchema` instead. */
    HTTPValidationError$.outboundSchema = exports.HTTPValidationError$outboundSchema;
})(HTTPValidationError$ || (exports.HTTPValidationError$ = HTTPValidationError$ = {}));
//# sourceMappingURL=httpvalidationerror.js.map