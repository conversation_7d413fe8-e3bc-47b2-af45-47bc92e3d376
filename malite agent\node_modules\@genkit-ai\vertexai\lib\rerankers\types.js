"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
var types_exports = {};
__export(types_exports, {
  VertexAIRerankerOptionsSchema: () => VertexAIRerankerOptionsSchema
});
module.exports = __toCommonJS(types_exports);
var import_genkit = require("genkit");
const VertexAIRerankerOptionsSchema = import_genkit.z.object({
  k: import_genkit.z.number().optional().describe("Number of top documents to rerank"),
  // Optional: Number of documents to rerank
  model: import_genkit.z.string().optional().describe("Model name for reranking"),
  // Optional: Model name, defaults to a pre-defined model
  location: import_genkit.z.string().optional().describe('Google Cloud location, e.g., "us-central1"')
  // Optional: Location of the reranking model
});
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  VertexAIRerankerOptionsSchema
});
//# sourceMappingURL=types.js.map