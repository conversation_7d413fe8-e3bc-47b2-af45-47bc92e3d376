{"version": 3, "file": "JaegerPropagator.js", "sourceRoot": "", "sources": ["../../src/JaegerPropagator.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAEH,OAAO,EAML,WAAW,EACX,KAAK,EACL,UAAU,GACX,MAAM,oBAAoB,CAAC;AAC5B,OAAO,EAAE,mBAAmB,EAAE,MAAM,qBAAqB,CAAC;AAG1D,MAAM,CAAC,MAAM,oBAAoB,GAAG,eAAe,CAAC;AACpD,MAAM,CAAC,MAAM,0BAA0B,GAAG,SAAS,CAAC;AAEpD;;;;;;;;;;;;;;GAcG;AACH,MAAM,OAAO,gBAAgB;IAM3B,YAAY,MAAwC;QAClD,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE;YAC9B,IAAI,CAAC,kBAAkB,GAAG,MAAM,CAAC;YACjC,IAAI,CAAC,0BAA0B,GAAG,0BAA0B,CAAC;SAC9D;aAAM;YACL,IAAI,CAAC,kBAAkB;gBACrB,CAAA,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,iBAAiB,KAAI,oBAAoB,CAAC;YACpD,IAAI,CAAC,0BAA0B;gBAC7B,CAAA,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,yBAAyB,KAAI,0BAA0B,CAAC;SACnE;IACH,CAAC;IAED,MAAM,CAAC,OAAgB,EAAE,OAAgB,EAAE,MAAqB;QAC9D,MAAM,WAAW,GAAG,KAAK,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;QAClD,MAAM,OAAO,GAAG,WAAW,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;QAChD,IAAI,WAAW,IAAI,mBAAmB,CAAC,OAAO,CAAC,KAAK,KAAK,EAAE;YACzD,MAAM,UAAU,GAAG,IAAI,CACrB,WAAW,CAAC,UAAU,IAAI,UAAU,CAAC,IAAI,CAC1C,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAC;YAEjB,MAAM,CAAC,GAAG,CACR,OAAO,EACP,IAAI,CAAC,kBAAkB,EACvB,GAAG,WAAW,CAAC,OAAO,IAAI,WAAW,CAAC,MAAM,MAAM,UAAU,EAAE,CAC/D,CAAC;SACH;QAED,IAAI,OAAO,EAAE;YACX,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,OAAO,CAAC,aAAa,EAAE,EAAE;gBAClD,MAAM,CAAC,GAAG,CACR,OAAO,EACP,GAAG,IAAI,CAAC,0BAA0B,IAAI,GAAG,EAAE,EAC3C,kBAAkB,CAAC,KAAK,CAAC,KAAK,CAAC,CAChC,CAAC;aACH;SACF;IACH,CAAC;IAED,OAAO,CAAC,OAAgB,EAAE,OAAgB,EAAE,MAAqB;;QAC/D,MAAM,iBAAiB,GAAG,MAAM,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC;QACvE,MAAM,WAAW,GAAG,KAAK,CAAC,OAAO,CAAC,iBAAiB,CAAC;YAClD,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC;YACtB,CAAC,CAAC,iBAAiB,CAAC;QACtB,MAAM,aAAa,GAAG,MAAM;aACzB,IAAI,CAAC,OAAO,CAAC;aACb,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC,0BAA0B,GAAG,CAAC,CAAC;aACpE,GAAG,CAAC,GAAG,CAAC,EAAE;YACT,MAAM,KAAK,GAAG,MAAM,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;YACvC,OAAO;gBACL,GAAG,EAAE,GAAG,CAAC,SAAS,CAAC,IAAI,CAAC,0BAA0B,CAAC,MAAM,GAAG,CAAC,CAAC;gBAC9D,KAAK,EAAE,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK;aAC/C,CAAC;QACJ,CAAC,CAAC,CAAC;QAEL,IAAI,UAAU,GAAG,OAAO,CAAC;QACzB,0EAA0E;QAC1E,IAAI,OAAO,WAAW,KAAK,QAAQ,EAAE;YACnC,MAAM,WAAW,GAAG,sBAAsB,CAAC,WAAW,CAAC,CAAC;YACxD,IAAI,WAAW,EAAE;gBACf,UAAU,GAAG,KAAK,CAAC,cAAc,CAAC,UAAU,EAAE,WAAW,CAAC,CAAC;aAC5D;SACF;QACD,IAAI,aAAa,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO,UAAU,CAAC;QAElD,oEAAoE;QACpE,IAAI,cAAc,GAChB,MAAA,WAAW,CAAC,UAAU,CAAC,OAAO,CAAC,mCAAI,WAAW,CAAC,aAAa,EAAE,CAAC;QACjE,KAAK,MAAM,YAAY,IAAI,aAAa,EAAE;YACxC,IAAI,YAAY,CAAC,KAAK,KAAK,SAAS;gBAAE,SAAS;YAC/C,cAAc,GAAG,cAAc,CAAC,QAAQ,CAAC,YAAY,CAAC,GAAG,EAAE;gBACzD,KAAK,EAAE,kBAAkB,CAAC,YAAY,CAAC,KAAK,CAAC;aAC9C,CAAC,CAAC;SACJ;QACD,UAAU,GAAG,WAAW,CAAC,UAAU,CAAC,UAAU,EAAE,cAAc,CAAC,CAAC;QAEhE,OAAO,UAAU,CAAC;IACpB,CAAC;IAED,MAAM;QACJ,OAAO,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;IACnC,CAAC;CACF;AAED,MAAM,YAAY,GAAG,kBAAkB,CAAC;AAExC;;;IAGI;AACJ,SAAS,sBAAsB,CAAC,gBAAwB;IACtD,MAAM,OAAO,GAAG,kBAAkB,CAAC,gBAAgB,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IAChE,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE;QACxB,OAAO,IAAI,CAAC;KACb;IAED,MAAM,CAAC,QAAQ,EAAE,OAAO,EAAE,AAAD,EAAG,KAAK,CAAC,GAAG,OAAO,CAAC;IAE7C,MAAM,OAAO,GAAG,QAAQ,CAAC,QAAQ,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC;IAC3C,MAAM,MAAM,GAAG,OAAO,CAAC,QAAQ,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC;IACzC,MAAM,UAAU,GAAG,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAE1E,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,UAAU,EAAE,CAAC;AACzD,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  Context,\n  SpanContext,\n  TextMapGetter,\n  TextMapPropagator,\n  TextMapSetter,\n  propagation,\n  trace,\n  TraceFlags,\n} from '@opentelemetry/api';\nimport { isTracingSuppressed } from '@opentelemetry/core';\nimport { JaegerPropagatorConfig } from './types';\n\nexport const UBER_TRACE_ID_HEADER = 'uber-trace-id';\nexport const UBER_BAGGAGE_HEADER_PREFIX = 'uberctx';\n\n/**\n * Propagates {@link SpanContext} through Trace Context format propagation.\n * {trace-id}:{span-id}:{parent-span-id}:{flags}\n * {trace-id}\n * 64-bit or 128-bit random number in base16 format.\n * Can be variable length, shorter values are 0-padded on the left.\n * Value of 0 is invalid.\n * {span-id}\n * 64-bit random number in base16 format.\n * {parent-span-id}\n * Set to 0 because this field is deprecated.\n * {flags}\n * One byte bitmap, as two hex digits.\n * Inspired by jaeger-client-node project.\n */\nexport class JaegerPropagator implements TextMapPropagator {\n  private readonly _jaegerTraceHeader: string;\n  private readonly _jaegerBaggageHeaderPrefix: string;\n\n  constructor(customTraceHeader?: string);\n  constructor(config?: JaegerPropagatorConfig);\n  constructor(config?: JaegerPropagatorConfig | string) {\n    if (typeof config === 'string') {\n      this._jaegerTraceHeader = config;\n      this._jaegerBaggageHeaderPrefix = UBER_BAGGAGE_HEADER_PREFIX;\n    } else {\n      this._jaegerTraceHeader =\n        config?.customTraceHeader || UBER_TRACE_ID_HEADER;\n      this._jaegerBaggageHeaderPrefix =\n        config?.customBaggageHeaderPrefix || UBER_BAGGAGE_HEADER_PREFIX;\n    }\n  }\n\n  inject(context: Context, carrier: unknown, setter: TextMapSetter): void {\n    const spanContext = trace.getSpanContext(context);\n    const baggage = propagation.getBaggage(context);\n    if (spanContext && isTracingSuppressed(context) === false) {\n      const traceFlags = `0${(\n        spanContext.traceFlags || TraceFlags.NONE\n      ).toString(16)}`;\n\n      setter.set(\n        carrier,\n        this._jaegerTraceHeader,\n        `${spanContext.traceId}:${spanContext.spanId}:0:${traceFlags}`\n      );\n    }\n\n    if (baggage) {\n      for (const [key, entry] of baggage.getAllEntries()) {\n        setter.set(\n          carrier,\n          `${this._jaegerBaggageHeaderPrefix}-${key}`,\n          encodeURIComponent(entry.value)\n        );\n      }\n    }\n  }\n\n  extract(context: Context, carrier: unknown, getter: TextMapGetter): Context {\n    const uberTraceIdHeader = getter.get(carrier, this._jaegerTraceHeader);\n    const uberTraceId = Array.isArray(uberTraceIdHeader)\n      ? uberTraceIdHeader[0]\n      : uberTraceIdHeader;\n    const baggageValues = getter\n      .keys(carrier)\n      .filter(key => key.startsWith(`${this._jaegerBaggageHeaderPrefix}-`))\n      .map(key => {\n        const value = getter.get(carrier, key);\n        return {\n          key: key.substring(this._jaegerBaggageHeaderPrefix.length + 1),\n          value: Array.isArray(value) ? value[0] : value,\n        };\n      });\n\n    let newContext = context;\n    // if the trace id header is present and valid, inject it into the context\n    if (typeof uberTraceId === 'string') {\n      const spanContext = deserializeSpanContext(uberTraceId);\n      if (spanContext) {\n        newContext = trace.setSpanContext(newContext, spanContext);\n      }\n    }\n    if (baggageValues.length === 0) return newContext;\n\n    // if baggage values are present, inject it into the current baggage\n    let currentBaggage =\n      propagation.getBaggage(context) ?? propagation.createBaggage();\n    for (const baggageEntry of baggageValues) {\n      if (baggageEntry.value === undefined) continue;\n      currentBaggage = currentBaggage.setEntry(baggageEntry.key, {\n        value: decodeURIComponent(baggageEntry.value),\n      });\n    }\n    newContext = propagation.setBaggage(newContext, currentBaggage);\n\n    return newContext;\n  }\n\n  fields(): string[] {\n    return [this._jaegerTraceHeader];\n  }\n}\n\nconst VALID_HEX_RE = /^[0-9a-f]{1,2}$/i;\n\n/**\n * @param {string} serializedString - a serialized span context.\n * @return {SpanContext} - returns a span context represented by the serializedString.\n **/\nfunction deserializeSpanContext(serializedString: string): SpanContext | null {\n  const headers = decodeURIComponent(serializedString).split(':');\n  if (headers.length !== 4) {\n    return null;\n  }\n\n  const [_traceId, _spanId, , flags] = headers;\n\n  const traceId = _traceId.padStart(32, '0');\n  const spanId = _spanId.padStart(16, '0');\n  const traceFlags = VALID_HEX_RE.test(flags) ? parseInt(flags, 16) & 1 : 1;\n\n  return { traceId, spanId, isRemote: true, traceFlags };\n}\n"]}