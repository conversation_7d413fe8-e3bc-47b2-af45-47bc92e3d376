# ملف .gcloudignore لتجاهل الملفات عند النشر على Google Cloud

# ملفات Git
.git
.gitignore

# ملفات Node.js
node_modules/
npm-debug.log
yarn-error.log

# ملفات البيئة المحلية
.env.local
.env.development
.env.test

# ملفات التطوير
tests/
test/
docs/
coverage/
.nyc_output/

# ملفات IDE
.vscode/
.idea/
*.swp
*.swo

# ملفات مؤقتة
tmp/
temp/
*.tmp
logs/
*.log

# ملفات التوثيق
README.md
CHANGELOG.md
LICENSE

# ملفات التكوين المحلية
.genkit/

# ملفات النسخ الاحتياطية
*.backup
*.bak

# ملفات الضغط
*.zip
*.tar.gz

# ملفات النشر
Dockerfile
.dockerignore
cloudbuild.yaml

# ملفات النظام
.DS_Store
Thumbs.db

# ملفات أخرى غير مطلوبة
.eslintrc*
.prettierrc*
jest.config.js
