{"version": 3, "sources": ["../src/statusTypes.ts"], "sourcesContent": ["/**\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport * as z from 'zod';\n\n/**\n * Enumeration of response status codes.\n */\nexport enum StatusCodes {\n  // Not an error; returned on success.\n  //\n  // HTTP Mapping: 200 OK\n  OK = 0,\n\n  // The operation was cancelled, typically by the caller.\n  //\n  // HTTP Mapping: 499 Client Closed Request\n  CANCELLED = 1,\n\n  // Unknown error.  For example, this error may be returned when\n  // a `Status` value received from another address space belongs to\n  // an error space that is not known in this address space.  Also\n  // errors raised by APIs that do not return enough error information\n  // may be converted to this error.\n  //\n  // HTTP Mapping: 500 Internal Server Error\n  UNKNOWN = 2,\n\n  // The client specified an invalid argument.  Note that this differs\n  // from `FAILED_PRECONDITION`.  `INVALID_ARGUMENT` indicates arguments\n  // that are problematic regardless of the state of the system\n  // (e.g., a malformed file name).\n  //\n  // HTTP Mapping: 400 Bad Request\n  INVALID_ARGUMENT = 3,\n\n  // The deadline expired before the operation could complete. For operations\n  // that change the state of the system, this error may be returned\n  // even if the operation has completed successfully.  For example, a\n  // successful response from a server could have been delayed long\n  // enough for the deadline to expire.\n  //\n  // HTTP Mapping: 504 Gateway Timeout\n  DEADLINE_EXCEEDED = 4,\n\n  // Some requested entity (e.g., file or directory) was not found.\n  //\n  // Note to server developers: if a request is denied for an entire class\n  // of users, such as gradual feature rollout or undocumented allowlist,\n  // `NOT_FOUND` may be used. If a request is denied for some users within\n  // a class of users, such as user-based access control, `PERMISSION_DENIED`\n  // must be used.\n  //\n  // HTTP Mapping: 404 Not Found\n  NOT_FOUND = 5,\n\n  // The entity that a client attempted to create (e.g., file or directory)\n  // already exists.\n  //\n  // HTTP Mapping: 409 Conflict\n  ALREADY_EXISTS = 6,\n\n  // The caller does not have permission to execute the specified\n  // operation. `PERMISSION_DENIED` must not be used for rejections\n  // caused by exhausting some resource (use `RESOURCE_EXHAUSTED`\n  // instead for those errors). `PERMISSION_DENIED` must not be\n  // used if the caller can not be identified (use `UNAUTHENTICATED`\n  // instead for those errors). This error code does not imply the\n  // request is valid or the requested entity exists or satisfies\n  // other pre-conditions.\n  //\n  // HTTP Mapping: 403 Forbidden\n  PERMISSION_DENIED = 7,\n\n  // The request does not have valid authentication credentials for the\n  // operation.\n  //\n  // HTTP Mapping: 401 Unauthorized\n  UNAUTHENTICATED = 16,\n\n  // Some resource has been exhausted, perhaps a per-user quota, or\n  // perhaps the entire file system is out of space.\n  //\n  // HTTP Mapping: 429 Too Many Requests\n  RESOURCE_EXHAUSTED = 8,\n\n  // The operation was rejected because the system is not in a state\n  // required for the operation's execution.  For example, the directory\n  // to be deleted is non-empty, an rmdir operation is applied to\n  // a non-directory, etc.\n  //\n  // Service implementors can use the following guidelines to decide\n  // between `FAILED_PRECONDITION`, `ABORTED`, and `UNAVAILABLE`:\n  //  (a) Use `UNAVAILABLE` if the client can retry just the failing call.\n  //  (b) Use `ABORTED` if the client should retry at a higher level. For\n  //      example, when a client-specified test-and-set fails, indicating the\n  //      client should restart a read-modify-write sequence.\n  //  (c) Use `FAILED_PRECONDITION` if the client should not retry until\n  //      the system state has been explicitly fixed. For example, if an \"rmdir\"\n  //      fails because the directory is non-empty, `FAILED_PRECONDITION`\n  //      should be returned since the client should not retry unless\n  //      the files are deleted from the directory.\n  //\n  // HTTP Mapping: 400 Bad Request\n  FAILED_PRECONDITION = 9,\n\n  // The operation was aborted, typically due to a concurrency issue such as\n  // a sequencer check failure or transaction abort.\n  //\n  // See the guidelines above for deciding between `FAILED_PRECONDITION`,\n  // `ABORTED`, and `UNAVAILABLE`.\n  //\n  // HTTP Mapping: 409 Conflict\n  ABORTED = 10,\n\n  // The operation was attempted past the valid range.  E.g., seeking or\n  // reading past end-of-file.\n  //\n  // Unlike `INVALID_ARGUMENT`, this error indicates a problem that may\n  // be fixed if the system state changes. For example, a 32-bit file\n  // system will generate `INVALID_ARGUMENT` if asked to read at an\n  // offset that is not in the range [0,2^32-1], but it will generate\n  // `OUT_OF_RANGE` if asked to read from an offset past the current\n  // file size.\n  //\n  // There is a fair bit of overlap between `FAILED_PRECONDITION` and\n  // `OUT_OF_RANGE`.  We recommend using `OUT_OF_RANGE` (the more specific\n  // error) when it applies so that callers who are iterating through\n  // a space can easily look for an `OUT_OF_RANGE` error to detect when\n  // they are done.\n  //\n  // HTTP Mapping: 400 Bad Request\n  OUT_OF_RANGE = 11,\n\n  // The operation is not implemented or is not supported/enabled in this\n  // service.\n  //\n  // HTTP Mapping: 501 Not Implemented\n  UNIMPLEMENTED = 12,\n\n  // Internal errors.  This means that some invariants expected by the\n  // underlying system have been broken.  This error code is reserved\n  // for serious errors.\n  //\n  // HTTP Mapping: 500 Internal Server Error\n  INTERNAL = 13,\n\n  // The service is currently unavailable.  This is most likely a\n  // transient condition, which can be corrected by retrying with\n  // a backoff. Note that it is not always safe to retry\n  // non-idempotent operations.\n  //\n  // See the guidelines above for deciding between `FAILED_PRECONDITION`,\n  // `ABORTED`, and `UNAVAILABLE`.\n  //\n  // HTTP Mapping: 503 Service Unavailable\n  UNAVAILABLE = 14,\n\n  // Unrecoverable data loss or corruption.\n  //\n  // HTTP Mapping: 500 Internal Server Error\n  DATA_LOSS = 15,\n}\n\nexport const StatusNameSchema = z.enum([\n  'OK',\n  'CANCELLED',\n  'UNKNOWN',\n  'INVALID_ARGUMENT',\n  'DEADLINE_EXCEEDED',\n  'NOT_FOUND',\n  'ALREADY_EXISTS',\n  'PERMISSION_DENIED',\n  'UNAUTHENTICATED',\n  'RESOURCE_EXHAUSTED',\n  'FAILED_PRECONDITION',\n  'ABORTED',\n  'OUT_OF_RANGE',\n  'UNIMPLEMENTED',\n  'INTERNAL',\n  'UNAVAILABLE',\n  'DATA_LOSS',\n]);\nexport type StatusName = z.infer<typeof StatusNameSchema>;\n\nconst statusCodeMap: Record<StatusName, number> = {\n  OK: 200,\n  CANCELLED: 499,\n  UNKNOWN: 500,\n  INVALID_ARGUMENT: 400,\n  DEADLINE_EXCEEDED: 504,\n  NOT_FOUND: 404,\n  ALREADY_EXISTS: 409,\n  PERMISSION_DENIED: 403,\n  UNAUTHENTICATED: 401,\n  RESOURCE_EXHAUSTED: 429,\n  FAILED_PRECONDITION: 400,\n  ABORTED: 409,\n  OUT_OF_RANGE: 400,\n  UNIMPLEMENTED: 501,\n  INTERNAL: 500,\n  UNAVAILABLE: 503,\n  DATA_LOSS: 500,\n};\n\nexport function httpStatusCode(status: StatusName): number {\n  if (!(status in statusCodeMap)) {\n    throw new Error(`Invalid status code ${status}`);\n  }\n  return statusCodeMap[status];\n}\n\nconst StatusCodesSchema = z.nativeEnum(StatusCodes);\n\n// If changing below\nexport const StatusSchema = z.object({\n  code: StatusCodesSchema,\n  message: z.string(),\n  details: z.any().optional(),\n});\n// then also change: genkit-tools/src/types/status.ts\n\nexport type Status = z.infer<typeof StatusSchema>;\n"], "mappings": "AAgBA,YAAY,OAAO;AAKZ,IAAK,cAAL,kBAAKA,iBAAL;AAIL,EAAAA,0BAAA,QAAK,KAAL;AAKA,EAAAA,0BAAA,eAAY,KAAZ;AASA,EAAAA,0BAAA,aAAU,KAAV;AAQA,EAAAA,0BAAA,sBAAmB,KAAnB;AASA,EAAAA,0BAAA,uBAAoB,KAApB;AAWA,EAAAA,0BAAA,eAAY,KAAZ;AAMA,EAAAA,0BAAA,oBAAiB,KAAjB;AAYA,EAAAA,0BAAA,uBAAoB,KAApB;AAMA,EAAAA,0BAAA,qBAAkB,MAAlB;AAMA,EAAAA,0BAAA,wBAAqB,KAArB;AAoBA,EAAAA,0BAAA,yBAAsB,KAAtB;AASA,EAAAA,0BAAA,aAAU,MAAV;AAmBA,EAAAA,0BAAA,kBAAe,MAAf;AAMA,EAAAA,0BAAA,mBAAgB,MAAhB;AAOA,EAAAA,0BAAA,cAAW,MAAX;AAWA,EAAAA,0BAAA,iBAAc,MAAd;AAKA,EAAAA,0BAAA,eAAY,MAAZ;AAzJU,SAAAA;AAAA,GAAA;AA4JL,MAAM,mBAAmB,EAAE,KAAK;AAAA,EACrC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,CAAC;AAGD,MAAM,gBAA4C;AAAA,EAChD,IAAI;AAAA,EACJ,WAAW;AAAA,EACX,SAAS;AAAA,EACT,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,WAAW;AAAA,EACX,gBAAgB;AAAA,EAChB,mBAAmB;AAAA,EACnB,iBAAiB;AAAA,EACjB,oBAAoB;AAAA,EACpB,qBAAqB;AAAA,EACrB,SAAS;AAAA,EACT,cAAc;AAAA,EACd,eAAe;AAAA,EACf,UAAU;AAAA,EACV,aAAa;AAAA,EACb,WAAW;AACb;AAEO,SAAS,eAAe,QAA4B;AACzD,MAAI,EAAE,UAAU,gBAAgB;AAC9B,UAAM,IAAI,MAAM,uBAAuB,MAAM,EAAE;AAAA,EACjD;AACA,SAAO,cAAc,MAAM;AAC7B;AAEA,MAAM,oBAAoB,EAAE,WAAW,WAAW;AAG3C,MAAM,eAAe,EAAE,OAAO;AAAA,EACnC,MAAM;AAAA,EACN,SAAS,EAAE,OAAO;AAAA,EAClB,SAAS,EAAE,IAAI,EAAE,SAAS;AAC5B,CAAC;", "names": ["StatusCodes"]}