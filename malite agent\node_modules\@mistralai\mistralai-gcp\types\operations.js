"use strict";
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.URL_OVERRIDE = void 0;
exports.createPageIterator = createPageIterator;
exports.haltIterator = haltIterator;
exports.unwrapResultIterator = unwrapResultIterator;
function createPageIterator(page, halt) {
    return {
        [Symbol.asyncIterator]: async function* paginator() {
            yield page;
            if (halt(page)) {
                return;
            }
            let p = page;
            for (p = await p.next(); p != null; p = await p.next()) {
                yield p;
                if (halt(p)) {
                    return;
                }
            }
        },
    };
}
/**
 * This utility create a special iterator that yields a single value and
 * terminates. It is useful in paginated SDK functions that have early return
 * paths when things go wrong.
 */
function haltIterator(v) {
    return {
        ...v,
        next: () => null,
        [Symbol.asyncIterator]: async function* paginator() {
            yield v;
        },
    };
}
/**
 * Converts an async iterator of `Result<V, E>` into an async iterator of `V`.
 * When error results occur, the underlying error value is thrown.
 */
async function unwrapResultIterator(iteratorPromise) {
    const resultIter = await iteratorPromise;
    if (!resultIter.ok) {
        throw resultIter.error;
    }
    return {
        ...resultIter.value,
        next: unwrapPaginator(resultIter.next),
        "~next": resultIter["~next"],
        [Symbol.asyncIterator]: async function* paginator() {
            for await (const page of resultIter) {
                if (!page.ok) {
                    throw page.error;
                }
                yield page.value;
            }
        },
    };
}
function unwrapPaginator(paginator) {
    return () => {
        const nextResult = paginator();
        if (nextResult == null) {
            return null;
        }
        return nextResult.then((res) => {
            if (!res.ok) {
                throw res.error;
            }
            const out = {
                ...res.value,
                next: unwrapPaginator(res.next),
            };
            return out;
        });
    };
}
exports.URL_OVERRIDE = Symbol("URL_OVERRIDE");
//# sourceMappingURL=operations.js.map