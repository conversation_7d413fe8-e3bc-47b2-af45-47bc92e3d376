/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import {
  catchUnrecognizedEnum,
  OpenEnum,
  Unrecognized,
} from "../../types/enums.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  AssistantMessage,
  AssistantMessage$inboundSchema,
  AssistantMessage$Outbound,
  AssistantMessage$outboundSchema,
} from "./assistantmessage.js";

export const ChatCompletionChoiceFinishReason = {
  Stop: "stop",
  Length: "length",
  ModelLength: "model_length",
  Error: "error",
  ToolCalls: "tool_calls",
} as const;
export type ChatCompletionChoiceFinishReason = OpenEnum<
  typeof ChatCompletionChoiceFinishReason
>;

export type ChatCompletionChoice = {
  index: number;
  message: AssistantMessage;
  finishReason: ChatCompletionChoiceFinishReason;
};

/** @internal */
export const ChatCompletionChoiceFinishReason$inboundSchema: z.ZodType<
  ChatCompletionChoiceFinishReason,
  z.ZodTypeDef,
  unknown
> = z
  .union([
    z.nativeEnum(ChatCompletionChoiceFinishReason),
    z.string().transform(catchUnrecognizedEnum),
  ]);

/** @internal */
export const ChatCompletionChoiceFinishReason$outboundSchema: z.ZodType<
  ChatCompletionChoiceFinishReason,
  z.ZodTypeDef,
  ChatCompletionChoiceFinishReason
> = z.union([
  z.nativeEnum(ChatCompletionChoiceFinishReason),
  z.string().and(z.custom<Unrecognized<string>>()),
]);

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace ChatCompletionChoiceFinishReason$ {
  /** @deprecated use `ChatCompletionChoiceFinishReason$inboundSchema` instead. */
  export const inboundSchema = ChatCompletionChoiceFinishReason$inboundSchema;
  /** @deprecated use `ChatCompletionChoiceFinishReason$outboundSchema` instead. */
  export const outboundSchema = ChatCompletionChoiceFinishReason$outboundSchema;
}

/** @internal */
export const ChatCompletionChoice$inboundSchema: z.ZodType<
  ChatCompletionChoice,
  z.ZodTypeDef,
  unknown
> = z.object({
  index: z.number().int(),
  message: AssistantMessage$inboundSchema,
  finish_reason: ChatCompletionChoiceFinishReason$inboundSchema,
}).transform((v) => {
  return remap$(v, {
    "finish_reason": "finishReason",
  });
});

/** @internal */
export type ChatCompletionChoice$Outbound = {
  index: number;
  message: AssistantMessage$Outbound;
  finish_reason: string;
};

/** @internal */
export const ChatCompletionChoice$outboundSchema: z.ZodType<
  ChatCompletionChoice$Outbound,
  z.ZodTypeDef,
  ChatCompletionChoice
> = z.object({
  index: z.number().int(),
  message: AssistantMessage$outboundSchema,
  finishReason: ChatCompletionChoiceFinishReason$outboundSchema,
}).transform((v) => {
  return remap$(v, {
    finishReason: "finish_reason",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace ChatCompletionChoice$ {
  /** @deprecated use `ChatCompletionChoice$inboundSchema` instead. */
  export const inboundSchema = ChatCompletionChoice$inboundSchema;
  /** @deprecated use `ChatCompletionChoice$outboundSchema` instead. */
  export const outboundSchema = ChatCompletionChoice$outboundSchema;
  /** @deprecated use `ChatCompletionChoice$Outbound` instead. */
  export type Outbound = ChatCompletionChoice$Outbound;
}

export function chatCompletionChoiceToJSON(
  chatCompletionChoice: ChatCompletionChoice,
): string {
  return JSON.stringify(
    ChatCompletionChoice$outboundSchema.parse(chatCompletionChoice),
  );
}

export function chatCompletionChoiceFromJSON(
  jsonString: string,
): SafeParseResult<ChatCompletionChoice, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => ChatCompletionChoice$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'ChatCompletionChoice' from JSON`,
  );
}
