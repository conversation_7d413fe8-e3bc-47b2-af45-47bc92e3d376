{"traceId": "07aab497cd489b2ce5bbd05edff881ee", "spans": {"95b0715f0b8b1483": {"spanId": "95b0715f0b8b1483", "traceId": "07aab497cd489b2ce5bbd05edff881ee", "parentSpanId": "10f2e7145cfbf0f2", "startTime": 1748061820608, "endTime": 1748061831758.1824, "attributes": {"genkit:type": "action", "genkit:metadata:subtype": "model", "genkit:name": "googleai/gemini-2.0-flash", "genkit:path": "/{generate,t:util}/{googleai/gemini-2.0-flash,t:action,s:model}", "genkit:input": "{\"messages\":[{\"role\":\"user\",\"content\":[{\"text\":\"مرحباً، قدم نفسك باللغة العربية وأخبرني كيف يمكنني البدء باستخدام Genkit.\"}]}],\"config\":{},\"tools\":[],\"output\":{}}", "genkit:output": "{\"candidates\":[{\"index\":0,\"message\":{\"role\":\"model\",\"content\":[{\"text\":\"أهلاً بك! اسمي نموذج لغوي كبير، تم تدريبي بواسطة جوجل. مهمتي هي معالجة المعلومات وتقديمها، والإجابة على أسئلتك بطريقة شاملة وواضحة.\\n\\nيسعدني أن أساعدك في البدء باستخدام Genkit. إليك خطوات أولية ونصائح لبداية موفقة:\\n\\n**ما هو Genkit؟**\\n\\nقبل البدء، من المهم أن تفهم Genkit باختصار. Genkit هو إطار عمل تطوير مفتوح المصدر لتطبيقات الذكاء الاصطناعي التوليدية (Generative AI). يساعدك في بناء نماذج ذكاء اصطناعي متينة وقابلة للتطوير بسهولة، مع التركيز على:\\n\\n*   **التدفق:** تعريف سير العمل (workflow) لتطبيقك بشكل واضح.\\n*   **المراقبة:** تتبع أداء تطبيقك واكتشاف المشاكل.\\n*   **التقييم:** تقييم جودة نتائج الذكاء الاصطناعي.\\n\\n**كيف تبدأ باستخدام Genkit؟**\\n\\n1.  **المتطلبات الأساسية:**\\n\\n    *   **Node.js:** تأكد من تثبيت Node.js (الإصدار 18 أو أحدث) على جهازك. يمكنك تنزيله من الموقع الرسمي: [https://nodejs.org/](https://nodejs.org/)\\n    *   **npm أو Yarn أو pnpm:**  ستحتاج إلى مدير حزم Node.js.  npm يأتي مع Node.js، أو يمكنك استخدام Yarn أو pnpm إذا كنت تفضلهم.\\n    *   **محرك نماذج الذكاء الاصطناعي:** ستحتاج إلى الوصول إلى نموذج لغوي كبير (LLM). يمكنك استخدام:\\n        *   **واجهات برمجة تطبيقات Google Cloud AI Platform:** إذا كنت تفضل ذلك، قم بإعداد مشروع Google Cloud وقم بتمكين واجهات برمجة التطبيقات المطلوبة.\\n        *   **نماذج مفتوحة المصدر (OpenAI-compatible):** يمكنك استخدام نماذج مفتوحة المصدر مع واجهات برمجة تطبيقات متوافقة مع OpenAI.\\n\\n2.  **تثبيت Genkit:**\\n\\n    يمكنك تثبيت Genkit باستخدام npm أو Yarn أو pnpm.\\n\\n    *   **باستخدام npm:**\\n\\n        ```bash\\n        npm install -g @genkit-ai/cli\\n        ```\\n\\n    *   **باستخدام Yarn:**\\n\\n        ```bash\\n        yarn global add @genkit-ai/cli\\n        ```\\n\\n    *   **باستخدام pnpm:**\\n\\n        ```bash\\n        pnpm add -g @genkit-ai/cli\\n        ```\\n\\n    هذا سيقوم بتثبيت واجهة سطر الأوامر (CLI) الخاصة بـ Genkit بشكل عام.\\n\\n3.  **إنشاء مشروع جديد:**\\n\\n    استخدم واجهة سطر الأوامر Genkit لإنشاء مشروع جديد:\\n\\n    ```bash\\n    genkit init my-genkit-app\\n    ```\\n\\n    استبدل `my-genkit-app` باسم المشروع الذي تريده.\\n\\n4.  **استكشاف المشروع:**\\n\\n    انتقل إلى مجلد المشروع:\\n\\n    ```bash\\n    cd my-genkit-app\\n    ```\\n\\n    افتح المشروع في محرر التعليمات البرمجية المفضل لديك (مثل Visual Studio Code). ستجد بنية مشروع قياسية مع ملفات لتحديد التدفقات (flows)، والاختبارات، وغيرها.\\n\\n5.  **كتابة التدفق الأول (Flow):**\\n\\n    التدفق هو قلب تطبيق Genkit.  يمثل سلسلة من الخطوات التي تعالج البيانات وتولد النتائج.  في أبسط صورة، يمكن أن يكون التدفق هو مجرد طلب إلى نموذج لغوي كبير.\\n\\n    *   **ابحث عن ملف التدفقات (flows) في المشروع.**  قد يكون اسمه `src/flows.ts` أو ما شابه.\\n    *   **قم بتعريف تدفقًا بسيطًا:**\\n\\n        ```typescript\\n        import { defineFlow } from '@genkit-ai/core';\\n        import { prompt } from '@genkit-ai/prompt'; // إذا كنت تستخدم واجهة برمجة تطبيقات LLM\\n\\n        export const simpleFlow = defineFlow({\\n          name: 'simpleFlow',\\n          inputSchema: { type: 'string' },\\n          outputSchema: { type: 'string' },\\n          run: async (input) => {\\n            // استخدم نموذجًا لغويًا كبيرًا لإنشاء رد\\n            const response = await prompt(`Translate this to French: ${input}`);\\n            return response.text;\\n          },\\n        });\\n        ```\\n\\n        **شرح الكود:**\\n\\n        *   `defineFlow` تحدد تدفقًا جديدًا.\\n        *   `name` هو اسم فريد للتدفق.\\n        *   `inputSchema` يحدد نوع البيانات المدخلة (هنا سلسلة نصية).\\n        *   `outputSchema` يحدد نوع البيانات المخرجة (هنا سلسلة نصية).\\n        *   `run` هي الوظيفة التي تنفذ التدفق.  تأخذ المدخلات، وتعالجها (باستخدام نموذج لغوي كبير في هذا المثال)، وتعيد النتيجة.\\n        *   `prompt` (مثال) يفترض أنك تستخدم مكتبة مثل `@genkit-ai/prompt` للتفاعل مع نموذج لغوي كبير.  ستحتاج إلى تكوين هذه المكتبة للإشارة إلى نموذج LLM الخاص بك (مثل Google Cloud Vertex AI أو OpenAI).\\n\\n6.  **تكوين نموذج LLM:**\\n\\n    هذه الخطوة تعتمد على النموذج اللغوي الكبير الذي اخترته.  راجع وثائق Genkit وإعدادات المزود الخاص بك (مثل Google Cloud أو OpenAI) لتكوين المصادقة وعنوان URL الخاص بواجهة برمجة التطبيقات.  غالبًا ما يتضمن ذلك تعيين متغيرات البيئة بمفاتيح API وبيانات الاعتماد.\\n\\n7.  **تشغيل التطبيق:**\\n\\n    قم بتشغيل تطبيق Genkit (عادةً باستخدام أمر مثل):\\n\\n    ```bash\\n    npm run dev\\n    ```\\n\\n    أو ما يشابه ذلك بناءً على إعدادات مشروعك.\\n\\n8.  **اختبار التدفق:**\\n\\n    *   **واجهة المستخدم:** غالبًا ما يوفر Genkit واجهة مستخدم (UI) لتشغيل التدفقات وفحص النتائج.  ابحث عن عنوان URL في سجلات التطبيق أثناء تشغيله.\\n    *   **سطر الأوامر:** يمكنك أيضًا اختبار التدفق من سطر الأوامر باستخدام أداة `genkit run`.\\n\\n9.  **الخطوات التالية:**\\n\\n    *   **استكشف الوثائق الرسمية لـ Genkit:**  [https://genkit.dev/](https://genkit.dev/)\\n    *   **جرب المزيد من الأمثلة:**  ابحث عن أمثلة Genkit في المستودع الخاص بالمشروع أو في الوثائق.\\n    *   **تعلم المزيد عن التدفقات المعقدة:**  ابدأ في بناء تدفقات أكثر تعقيدًا تتضمن خطوات متعددة ومعالجة البيانات.\\n    *   **تعلم عن المراقبة والتقييم:**  استخدم أدوات Genkit لتتبع أداء تطبيقك وتحسين جودة النتائج.\\n\\n**نصائح إضافية:**\\n\\n*   **ابدأ صغيرًا:**  لا تحاول بناء تطبيق معقد دفعة واحدة.  ابدأ بتدفقات بسيطة واختبرها جيدًا.\\n*   **اقرأ الوثائق بعناية:**  توفر الوثائق الرسمية لـ Genkit معلومات مفصلة حول جميع الميزات والإعدادات.\\n*   **ابحث عن أمثلة:**  ابحث عن أمثلة Genkit عبر الإنترنت أو في المستودع الخاص بالمشروع.  هذه الأمثلة يمكن أن تكون نقطة انطلاق جيدة.\\n*   **انضم إلى المجتمع:**  ابحث عن منتديات أو قنوات دعم مجتمعية لـ Genkit.  يمكنك طرح الأسئلة والحصول على المساعدة من مطورين آخرين.\\n\\nآمل أن يكون هذا مفيدًا! إذا كان لديك أي أسئلة محددة، فلا تتردد في طرحها.  بالتوفيق في رحلتك مع Genkit!\\n\"}]},\"finishReason\":\"stop\",\"custom\":{}}],\"custom\":{\"candidates\":[{\"content\":{\"parts\":[{\"text\":\"أهلاً بك! اسمي نموذج لغوي كبير، تم تدريبي بواسطة جوجل. مهمتي هي معالجة المعلومات وتقديمها، والإجابة على أسئلتك بطريقة شاملة وواضحة.\\n\\nيسعدني أن أساعدك في البدء باستخدام Genkit. إليك خطوات أولية ونصائح لبداية موفقة:\\n\\n**ما هو Genkit؟**\\n\\nقبل البدء، من المهم أن تفهم Genkit باختصار. Genkit هو إطار عمل تطوير مفتوح المصدر لتطبيقات الذكاء الاصطناعي التوليدية (Generative AI). يساعدك في بناء نماذج ذكاء اصطناعي متينة وقابلة للتطوير بسهولة، مع التركيز على:\\n\\n*   **التدفق:** تعريف سير العمل (workflow) لتطبيقك بشكل واضح.\\n*   **المراقبة:** تتبع أداء تطبيقك واكتشاف المشاكل.\\n*   **التقييم:** تقييم جودة نتائج الذكاء الاصطناعي.\\n\\n**كيف تبدأ باستخدام Genkit؟**\\n\\n1.  **المتطلبات الأساسية:**\\n\\n    *   **Node.js:** تأكد من تثبيت Node.js (الإصدار 18 أو أحدث) على جهازك. يمكنك تنزيله من الموقع الرسمي: [https://nodejs.org/](https://nodejs.org/)\\n    *   **npm أو Yarn أو pnpm:**  ستحتاج إلى مدير حزم Node.js.  npm يأتي مع Node.js، أو يمكنك استخدام Yarn أو pnpm إذا كنت تفضلهم.\\n    *   **محرك نماذج الذكاء الاصطناعي:** ستحتاج إلى الوصول إلى نموذج لغوي كبير (LLM). يمكنك استخدام:\\n        *   **واجهات برمجة تطبيقات Google Cloud AI Platform:** إذا كنت تفضل ذلك، قم بإعداد مشروع Google Cloud وقم بتمكين واجهات برمجة التطبيقات المطلوبة.\\n        *   **نماذج مفتوحة المصدر (OpenAI-compatible):** يمكنك استخدام نماذج مفتوحة المصدر مع واجهات برمجة تطبيقات متوافقة مع OpenAI.\\n\\n2.  **تثبيت Genkit:**\\n\\n    يمكنك تثبيت Genkit باستخدام npm أو Yarn أو pnpm.\\n\\n    *   **باستخدام npm:**\\n\\n        ```bash\\n        npm install -g @genkit-ai/cli\\n        ```\\n\\n    *   **باستخدام Yarn:**\\n\\n        ```bash\\n        yarn global add @genkit-ai/cli\\n        ```\\n\\n    *   **باستخدام pnpm:**\\n\\n        ```bash\\n        pnpm add -g @genkit-ai/cli\\n        ```\\n\\n    هذا سيقوم بتثبيت واجهة سطر الأوامر (CLI) الخاصة بـ Genkit بشكل عام.\\n\\n3.  **إنشاء مشروع جديد:**\\n\\n    استخدم واجهة سطر الأوامر Genkit لإنشاء مشروع جديد:\\n\\n    ```bash\\n    genkit init my-genkit-app\\n    ```\\n\\n    استبدل `my-genkit-app` باسم المشروع الذي تريده.\\n\\n4.  **استكشاف المشروع:**\\n\\n    انتقل إلى مجلد المشروع:\\n\\n    ```bash\\n    cd my-genkit-app\\n    ```\\n\\n    افتح المشروع في محرر التعليمات البرمجية المفضل لديك (مثل Visual Studio Code). ستجد بنية مشروع قياسية مع ملفات لتحديد التدفقات (flows)، والاختبارات، وغيرها.\\n\\n5.  **كتابة التدفق الأول (Flow):**\\n\\n    التدفق هو قلب تطبيق Genkit.  يمثل سلسلة من الخطوات التي تعالج البيانات وتولد النتائج.  في أبسط صورة، يمكن أن يكون التدفق هو مجرد طلب إلى نموذج لغوي كبير.\\n\\n    *   **ابحث عن ملف التدفقات (flows) في المشروع.**  قد يكون اسمه `src/flows.ts` أو ما شابه.\\n    *   **قم بتعريف تدفقًا بسيطًا:**\\n\\n        ```typescript\\n        import { defineFlow } from '@genkit-ai/core';\\n        import { prompt } from '@genkit-ai/prompt'; // إذا كنت تستخدم واجهة برمجة تطبيقات LLM\\n\\n        export const simpleFlow = defineFlow({\\n          name: 'simpleFlow',\\n          inputSchema: { type: 'string' },\\n          outputSchema: { type: 'string' },\\n          run: async (input) => {\\n            // استخدم نموذجًا لغويًا كبيرًا لإنشاء رد\\n            const response = await prompt(`Translate this to French: ${input}`);\\n            return response.text;\\n          },\\n        });\\n        ```\\n\\n        **شرح الكود:**\\n\\n        *   `defineFlow` تحدد تدفقًا جديدًا.\\n        *   `name` هو اسم فريد للتدفق.\\n        *   `inputSchema` يحدد نوع البيانات المدخلة (هنا سلسلة نصية).\\n        *   `outputSchema` يحدد نوع البيانات المخرجة (هنا سلسلة نصية).\\n        *   `run` هي الوظيفة التي تنفذ التدفق.  تأخذ المدخلات، وتعالجها (باستخدام نموذج لغوي كبير في هذا المثال)، وتعيد النتيجة.\\n        *   `prompt` (مثال) يفترض أنك تستخدم مكتبة مثل `@genkit-ai/prompt` للتفاعل مع نموذج لغوي كبير.  ستحتاج إلى تكوين هذه المكتبة للإشارة إلى نموذج LLM الخاص بك (مثل Google Cloud Vertex AI أو OpenAI).\\n\\n6.  **تكوين نموذج LLM:**\\n\\n    هذه الخطوة تعتمد على النموذج اللغوي الكبير الذي اخترته.  راجع وثائق Genkit وإعدادات المزود الخاص بك (مثل Google Cloud أو OpenAI) لتكوين المصادقة وعنوان URL الخاص بواجهة برمجة التطبيقات.  غالبًا ما يتضمن ذلك تعيين متغيرات البيئة بمفاتيح API وبيانات الاعتماد.\\n\\n7.  **تشغيل التطبيق:**\\n\\n    قم بتشغيل تطبيق Genkit (عادةً باستخدام أمر مثل):\\n\\n    ```bash\\n    npm run dev\\n    ```\\n\\n    أو ما يشابه ذلك بناءً على إعدادات مشروعك.\\n\\n8.  **اختبار التدفق:**\\n\\n    *   **واجهة المستخدم:** غالبًا ما يوفر Genkit واجهة مستخدم (UI) لتشغيل التدفقات وفحص النتائج.  ابحث عن عنوان URL في سجلات التطبيق أثناء تشغيله.\\n    *   **سطر الأوامر:** يمكنك أيضًا اختبار التدفق من سطر الأوامر باستخدام أداة `genkit run`.\\n\\n9.  **الخطوات التالية:**\\n\\n    *   **استكشف الوثائق الرسمية لـ Genkit:**  [https://genkit.dev/](https://genkit.dev/)\\n    *   **جرب المزيد من الأمثلة:**  ابحث عن أمثلة Genkit في المستودع الخاص بالمشروع أو في الوثائق.\\n    *   **تعلم المزيد عن التدفقات المعقدة:**  ابدأ في بناء تدفقات أكثر تعقيدًا تتضمن خطوات متعددة ومعالجة البيانات.\\n    *   **تعلم عن المراقبة والتقييم:**  استخدم أدوات Genkit لتتبع أداء تطبيقك وتحسين جودة النتائج.\\n\\n**نصائح إضافية:**\\n\\n*   **ابدأ صغيرًا:**  لا تحاول بناء تطبيق معقد دفعة واحدة.  ابدأ بتدفقات بسيطة واختبرها جيدًا.\\n*   **اقرأ الوثائق بعناية:**  توفر الوثائق الرسمية لـ Genkit معلومات مفصلة حول جميع الميزات والإعدادات.\\n*   **ابحث عن أمثلة:**  ابحث عن أمثلة Genkit عبر الإنترنت أو في المستودع الخاص بالمشروع.  هذه الأمثلة يمكن أن تكون نقطة انطلاق جيدة.\\n*   **انضم إلى المجتمع:**  ابحث عن منتديات أو قنوات دعم مجتمعية لـ Genkit.  يمكنك طرح الأسئلة والحصول على المساعدة من مطورين آخرين.\\n\\nآمل أن يكون هذا مفيدًا! إذا كان لديك أي أسئلة محددة، فلا تتردد في طرحها.  بالتوفيق في رحلتك مع Genkit!\\n\"}],\"role\":\"model\"},\"finishReason\":\"STOP\",\"avgLogprobs\":-0.2805800875603865}],\"usageMetadata\":{\"promptTokenCount\":21,\"candidatesTokenCount\":1863,\"totalTokenCount\":1884,\"promptTokensDetails\":[{\"modality\":\"TEXT\",\"tokenCount\":21}],\"candidatesTokensDetails\":[{\"modality\":\"TEXT\",\"tokenCount\":1863}]},\"modelVersion\":\"gemini-2.0-flash\",\"responseId\":\"fE4xaPCjJofU1PIPnPTS-Qc\"},\"usage\":{\"inputCharacters\":73,\"inputImages\":0,\"inputVideos\":0,\"inputAudioFiles\":0,\"outputCharacters\":5445,\"outputImages\":0,\"outputVideos\":0,\"outputAudioFiles\":0,\"inputTokens\":21,\"outputTokens\":1863,\"totalTokens\":1884},\"latencyMs\":11148.1368}", "genkit:state": "success"}, "displayName": "googleai/gemini-2.0-flash", "links": [], "instrumentationLibrary": {"name": "genkit-tracer", "version": "v1"}, "spanKind": "INTERNAL", "sameProcessAsParentSpan": {"value": true}, "status": {"code": 0}, "timeEvents": {"timeEvent": []}}, "10f2e7145cfbf0f2": {"spanId": "10f2e7145cfbf0f2", "traceId": "07aab497cd489b2ce5bbd05edff881ee", "startTime": 1748061820280, "endTime": 1748061831889.2803, "attributes": {"genkit:type": "util", "genkit:name": "generate", "genkit:isRoot": true, "genkit:path": "/{generate,t:util}", "genkit:input": "{\"model\":\"googleai/gemini-2.0-flash\",\"messages\":[{\"role\":\"user\",\"content\":[{\"text\":\"مرحباً، قدم نفسك باللغة العربية وأخبرني كيف يمكنني البدء باستخدام Genkit.\"}]}],\"config\":{}}", "genkit:output": "{\"message\":{\"role\":\"model\",\"content\":[{\"text\":\"أهلاً بك! اسمي نموذج لغوي كبير، تم تدريبي بواسطة جوجل. مهمتي هي معالجة المعلومات وتقديمها، والإجابة على أسئلتك بطريقة شاملة وواضحة.\\n\\nيسعدني أن أساعدك في البدء باستخدام Genkit. إليك خطوات أولية ونصائح لبداية موفقة:\\n\\n**ما هو Genkit؟**\\n\\nقبل البدء، من المهم أن تفهم Genkit باختصار. Genkit هو إطار عمل تطوير مفتوح المصدر لتطبيقات الذكاء الاصطناعي التوليدية (Generative AI). يساعدك في بناء نماذج ذكاء اصطناعي متينة وقابلة للتطوير بسهولة، مع التركيز على:\\n\\n*   **التدفق:** تعريف سير العمل (workflow) لتطبيقك بشكل واضح.\\n*   **المراقبة:** تتبع أداء تطبيقك واكتشاف المشاكل.\\n*   **التقييم:** تقييم جودة نتائج الذكاء الاصطناعي.\\n\\n**كيف تبدأ باستخدام Genkit؟**\\n\\n1.  **المتطلبات الأساسية:**\\n\\n    *   **Node.js:** تأكد من تثبيت Node.js (الإصدار 18 أو أحدث) على جهازك. يمكنك تنزيله من الموقع الرسمي: [https://nodejs.org/](https://nodejs.org/)\\n    *   **npm أو Yarn أو pnpm:**  ستحتاج إلى مدير حزم Node.js.  npm يأتي مع Node.js، أو يمكنك استخدام Yarn أو pnpm إذا كنت تفضلهم.\\n    *   **محرك نماذج الذكاء الاصطناعي:** ستحتاج إلى الوصول إلى نموذج لغوي كبير (LLM). يمكنك استخدام:\\n        *   **واجهات برمجة تطبيقات Google Cloud AI Platform:** إذا كنت تفضل ذلك، قم بإعداد مشروع Google Cloud وقم بتمكين واجهات برمجة التطبيقات المطلوبة.\\n        *   **نماذج مفتوحة المصدر (OpenAI-compatible):** يمكنك استخدام نماذج مفتوحة المصدر مع واجهات برمجة تطبيقات متوافقة مع OpenAI.\\n\\n2.  **تثبيت Genkit:**\\n\\n    يمكنك تثبيت Genkit باستخدام npm أو Yarn أو pnpm.\\n\\n    *   **باستخدام npm:**\\n\\n        ```bash\\n        npm install -g @genkit-ai/cli\\n        ```\\n\\n    *   **باستخدام Yarn:**\\n\\n        ```bash\\n        yarn global add @genkit-ai/cli\\n        ```\\n\\n    *   **باستخدام pnpm:**\\n\\n        ```bash\\n        pnpm add -g @genkit-ai/cli\\n        ```\\n\\n    هذا سيقوم بتثبيت واجهة سطر الأوامر (CLI) الخاصة بـ Genkit بشكل عام.\\n\\n3.  **إنشاء مشروع جديد:**\\n\\n    استخدم واجهة سطر الأوامر Genkit لإنشاء مشروع جديد:\\n\\n    ```bash\\n    genkit init my-genkit-app\\n    ```\\n\\n    استبدل `my-genkit-app` باسم المشروع الذي تريده.\\n\\n4.  **استكشاف المشروع:**\\n\\n    انتقل إلى مجلد المشروع:\\n\\n    ```bash\\n    cd my-genkit-app\\n    ```\\n\\n    افتح المشروع في محرر التعليمات البرمجية المفضل لديك (مثل Visual Studio Code). ستجد بنية مشروع قياسية مع ملفات لتحديد التدفقات (flows)، والاختبارات، وغيرها.\\n\\n5.  **كتابة التدفق الأول (Flow):**\\n\\n    التدفق هو قلب تطبيق Genkit.  يمثل سلسلة من الخطوات التي تعالج البيانات وتولد النتائج.  في أبسط صورة، يمكن أن يكون التدفق هو مجرد طلب إلى نموذج لغوي كبير.\\n\\n    *   **ابحث عن ملف التدفقات (flows) في المشروع.**  قد يكون اسمه `src/flows.ts` أو ما شابه.\\n    *   **قم بتعريف تدفقًا بسيطًا:**\\n\\n        ```typescript\\n        import { defineFlow } from '@genkit-ai/core';\\n        import { prompt } from '@genkit-ai/prompt'; // إذا كنت تستخدم واجهة برمجة تطبيقات LLM\\n\\n        export const simpleFlow = defineFlow({\\n          name: 'simpleFlow',\\n          inputSchema: { type: 'string' },\\n          outputSchema: { type: 'string' },\\n          run: async (input) => {\\n            // استخدم نموذجًا لغويًا كبيرًا لإنشاء رد\\n            const response = await prompt(`Translate this to French: ${input}`);\\n            return response.text;\\n          },\\n        });\\n        ```\\n\\n        **شرح الكود:**\\n\\n        *   `defineFlow` تحدد تدفقًا جديدًا.\\n        *   `name` هو اسم فريد للتدفق.\\n        *   `inputSchema` يحدد نوع البيانات المدخلة (هنا سلسلة نصية).\\n        *   `outputSchema` يحدد نوع البيانات المخرجة (هنا سلسلة نصية).\\n        *   `run` هي الوظيفة التي تنفذ التدفق.  تأخذ المدخلات، وتعالجها (باستخدام نموذج لغوي كبير في هذا المثال)، وتعيد النتيجة.\\n        *   `prompt` (مثال) يفترض أنك تستخدم مكتبة مثل `@genkit-ai/prompt` للتفاعل مع نموذج لغوي كبير.  ستحتاج إلى تكوين هذه المكتبة للإشارة إلى نموذج LLM الخاص بك (مثل Google Cloud Vertex AI أو OpenAI).\\n\\n6.  **تكوين نموذج LLM:**\\n\\n    هذه الخطوة تعتمد على النموذج اللغوي الكبير الذي اخترته.  راجع وثائق Genkit وإعدادات المزود الخاص بك (مثل Google Cloud أو OpenAI) لتكوين المصادقة وعنوان URL الخاص بواجهة برمجة التطبيقات.  غالبًا ما يتضمن ذلك تعيين متغيرات البيئة بمفاتيح API وبيانات الاعتماد.\\n\\n7.  **تشغيل التطبيق:**\\n\\n    قم بتشغيل تطبيق Genkit (عادةً باستخدام أمر مثل):\\n\\n    ```bash\\n    npm run dev\\n    ```\\n\\n    أو ما يشابه ذلك بناءً على إعدادات مشروعك.\\n\\n8.  **اختبار التدفق:**\\n\\n    *   **واجهة المستخدم:** غالبًا ما يوفر Genkit واجهة مستخدم (UI) لتشغيل التدفقات وفحص النتائج.  ابحث عن عنوان URL في سجلات التطبيق أثناء تشغيله.\\n    *   **سطر الأوامر:** يمكنك أيضًا اختبار التدفق من سطر الأوامر باستخدام أداة `genkit run`.\\n\\n9.  **الخطوات التالية:**\\n\\n    *   **استكشف الوثائق الرسمية لـ Genkit:**  [https://genkit.dev/](https://genkit.dev/)\\n    *   **جرب المزيد من الأمثلة:**  ابحث عن أمثلة Genkit في المستودع الخاص بالمشروع أو في الوثائق.\\n    *   **تعلم المزيد عن التدفقات المعقدة:**  ابدأ في بناء تدفقات أكثر تعقيدًا تتضمن خطوات متعددة ومعالجة البيانات.\\n    *   **تعلم عن المراقبة والتقييم:**  استخدم أدوات Genkit لتتبع أداء تطبيقك وتحسين جودة النتائج.\\n\\n**نصائح إضافية:**\\n\\n*   **ابدأ صغيرًا:**  لا تحاول بناء تطبيق معقد دفعة واحدة.  ابدأ بتدفقات بسيطة واختبرها جيدًا.\\n*   **اقرأ الوثائق بعناية:**  توفر الوثائق الرسمية لـ Genkit معلومات مفصلة حول جميع الميزات والإعدادات.\\n*   **ابحث عن أمثلة:**  ابحث عن أمثلة Genkit عبر الإنترنت أو في المستودع الخاص بالمشروع.  هذه الأمثلة يمكن أن تكون نقطة انطلاق جيدة.\\n*   **انضم إلى المجتمع:**  ابحث عن منتديات أو قنوات دعم مجتمعية لـ Genkit.  يمكنك طرح الأسئلة والحصول على المساعدة من مطورين آخرين.\\n\\nآمل أن يكون هذا مفيدًا! إذا كان لديك أي أسئلة محددة، فلا تتردد في طرحها.  بالتوفيق في رحلتك مع Genkit!\\n\"}]},\"finishReason\":\"stop\",\"usage\":{\"inputCharacters\":73,\"inputImages\":0,\"inputVideos\":0,\"inputAudioFiles\":0,\"outputCharacters\":5445,\"outputImages\":0,\"outputVideos\":0,\"outputAudioFiles\":0,\"inputTokens\":21,\"outputTokens\":1863,\"totalTokens\":1884},\"custom\":{\"candidates\":[{\"content\":{\"parts\":[{\"text\":\"أهلاً بك! اسمي نموذج لغوي كبير، تم تدريبي بواسطة جوجل. مهمتي هي معالجة المعلومات وتقديمها، والإجابة على أسئلتك بطريقة شاملة وواضحة.\\n\\nيسعدني أن أساعدك في البدء باستخدام Genkit. إليك خطوات أولية ونصائح لبداية موفقة:\\n\\n**ما هو Genkit؟**\\n\\nقبل البدء، من المهم أن تفهم Genkit باختصار. Genkit هو إطار عمل تطوير مفتوح المصدر لتطبيقات الذكاء الاصطناعي التوليدية (Generative AI). يساعدك في بناء نماذج ذكاء اصطناعي متينة وقابلة للتطوير بسهولة، مع التركيز على:\\n\\n*   **التدفق:** تعريف سير العمل (workflow) لتطبيقك بشكل واضح.\\n*   **المراقبة:** تتبع أداء تطبيقك واكتشاف المشاكل.\\n*   **التقييم:** تقييم جودة نتائج الذكاء الاصطناعي.\\n\\n**كيف تبدأ باستخدام Genkit؟**\\n\\n1.  **المتطلبات الأساسية:**\\n\\n    *   **Node.js:** تأكد من تثبيت Node.js (الإصدار 18 أو أحدث) على جهازك. يمكنك تنزيله من الموقع الرسمي: [https://nodejs.org/](https://nodejs.org/)\\n    *   **npm أو Yarn أو pnpm:**  ستحتاج إلى مدير حزم Node.js.  npm يأتي مع Node.js، أو يمكنك استخدام Yarn أو pnpm إذا كنت تفضلهم.\\n    *   **محرك نماذج الذكاء الاصطناعي:** ستحتاج إلى الوصول إلى نموذج لغوي كبير (LLM). يمكنك استخدام:\\n        *   **واجهات برمجة تطبيقات Google Cloud AI Platform:** إذا كنت تفضل ذلك، قم بإعداد مشروع Google Cloud وقم بتمكين واجهات برمجة التطبيقات المطلوبة.\\n        *   **نماذج مفتوحة المصدر (OpenAI-compatible):** يمكنك استخدام نماذج مفتوحة المصدر مع واجهات برمجة تطبيقات متوافقة مع OpenAI.\\n\\n2.  **تثبيت Genkit:**\\n\\n    يمكنك تثبيت Genkit باستخدام npm أو Yarn أو pnpm.\\n\\n    *   **باستخدام npm:**\\n\\n        ```bash\\n        npm install -g @genkit-ai/cli\\n        ```\\n\\n    *   **باستخدام Yarn:**\\n\\n        ```bash\\n        yarn global add @genkit-ai/cli\\n        ```\\n\\n    *   **باستخدام pnpm:**\\n\\n        ```bash\\n        pnpm add -g @genkit-ai/cli\\n        ```\\n\\n    هذا سيقوم بتثبيت واجهة سطر الأوامر (CLI) الخاصة بـ Genkit بشكل عام.\\n\\n3.  **إنشاء مشروع جديد:**\\n\\n    استخدم واجهة سطر الأوامر Genkit لإنشاء مشروع جديد:\\n\\n    ```bash\\n    genkit init my-genkit-app\\n    ```\\n\\n    استبدل `my-genkit-app` باسم المشروع الذي تريده.\\n\\n4.  **استكشاف المشروع:**\\n\\n    انتقل إلى مجلد المشروع:\\n\\n    ```bash\\n    cd my-genkit-app\\n    ```\\n\\n    افتح المشروع في محرر التعليمات البرمجية المفضل لديك (مثل Visual Studio Code). ستجد بنية مشروع قياسية مع ملفات لتحديد التدفقات (flows)، والاختبارات، وغيرها.\\n\\n5.  **كتابة التدفق الأول (Flow):**\\n\\n    التدفق هو قلب تطبيق Genkit.  يمثل سلسلة من الخطوات التي تعالج البيانات وتولد النتائج.  في أبسط صورة، يمكن أن يكون التدفق هو مجرد طلب إلى نموذج لغوي كبير.\\n\\n    *   **ابحث عن ملف التدفقات (flows) في المشروع.**  قد يكون اسمه `src/flows.ts` أو ما شابه.\\n    *   **قم بتعريف تدفقًا بسيطًا:**\\n\\n        ```typescript\\n        import { defineFlow } from '@genkit-ai/core';\\n        import { prompt } from '@genkit-ai/prompt'; // إذا كنت تستخدم واجهة برمجة تطبيقات LLM\\n\\n        export const simpleFlow = defineFlow({\\n          name: 'simpleFlow',\\n          inputSchema: { type: 'string' },\\n          outputSchema: { type: 'string' },\\n          run: async (input) => {\\n            // استخدم نموذجًا لغويًا كبيرًا لإنشاء رد\\n            const response = await prompt(`Translate this to French: ${input}`);\\n            return response.text;\\n          },\\n        });\\n        ```\\n\\n        **شرح الكود:**\\n\\n        *   `defineFlow` تحدد تدفقًا جديدًا.\\n        *   `name` هو اسم فريد للتدفق.\\n        *   `inputSchema` يحدد نوع البيانات المدخلة (هنا سلسلة نصية).\\n        *   `outputSchema` يحدد نوع البيانات المخرجة (هنا سلسلة نصية).\\n        *   `run` هي الوظيفة التي تنفذ التدفق.  تأخذ المدخلات، وتعالجها (باستخدام نموذج لغوي كبير في هذا المثال)، وتعيد النتيجة.\\n        *   `prompt` (مثال) يفترض أنك تستخدم مكتبة مثل `@genkit-ai/prompt` للتفاعل مع نموذج لغوي كبير.  ستحتاج إلى تكوين هذه المكتبة للإشارة إلى نموذج LLM الخاص بك (مثل Google Cloud Vertex AI أو OpenAI).\\n\\n6.  **تكوين نموذج LLM:**\\n\\n    هذه الخطوة تعتمد على النموذج اللغوي الكبير الذي اخترته.  راجع وثائق Genkit وإعدادات المزود الخاص بك (مثل Google Cloud أو OpenAI) لتكوين المصادقة وعنوان URL الخاص بواجهة برمجة التطبيقات.  غالبًا ما يتضمن ذلك تعيين متغيرات البيئة بمفاتيح API وبيانات الاعتماد.\\n\\n7.  **تشغيل التطبيق:**\\n\\n    قم بتشغيل تطبيق Genkit (عادةً باستخدام أمر مثل):\\n\\n    ```bash\\n    npm run dev\\n    ```\\n\\n    أو ما يشابه ذلك بناءً على إعدادات مشروعك.\\n\\n8.  **اختبار التدفق:**\\n\\n    *   **واجهة المستخدم:** غالبًا ما يوفر Genkit واجهة مستخدم (UI) لتشغيل التدفقات وفحص النتائج.  ابحث عن عنوان URL في سجلات التطبيق أثناء تشغيله.\\n    *   **سطر الأوامر:** يمكنك أيضًا اختبار التدفق من سطر الأوامر باستخدام أداة `genkit run`.\\n\\n9.  **الخطوات التالية:**\\n\\n    *   **استكشف الوثائق الرسمية لـ Genkit:**  [https://genkit.dev/](https://genkit.dev/)\\n    *   **جرب المزيد من الأمثلة:**  ابحث عن أمثلة Genkit في المستودع الخاص بالمشروع أو في الوثائق.\\n    *   **تعلم المزيد عن التدفقات المعقدة:**  ابدأ في بناء تدفقات أكثر تعقيدًا تتضمن خطوات متعددة ومعالجة البيانات.\\n    *   **تعلم عن المراقبة والتقييم:**  استخدم أدوات Genkit لتتبع أداء تطبيقك وتحسين جودة النتائج.\\n\\n**نصائح إضافية:**\\n\\n*   **ابدأ صغيرًا:**  لا تحاول بناء تطبيق معقد دفعة واحدة.  ابدأ بتدفقات بسيطة واختبرها جيدًا.\\n*   **اقرأ الوثائق بعناية:**  توفر الوثائق الرسمية لـ Genkit معلومات مفصلة حول جميع الميزات والإعدادات.\\n*   **ابحث عن أمثلة:**  ابحث عن أمثلة Genkit عبر الإنترنت أو في المستودع الخاص بالمشروع.  هذه الأمثلة يمكن أن تكون نقطة انطلاق جيدة.\\n*   **انضم إلى المجتمع:**  ابحث عن منتديات أو قنوات دعم مجتمعية لـ Genkit.  يمكنك طرح الأسئلة والحصول على المساعدة من مطورين آخرين.\\n\\nآمل أن يكون هذا مفيدًا! إذا كان لديك أي أسئلة محددة، فلا تتردد في طرحها.  بالتوفيق في رحلتك مع Genkit!\\n\"}],\"role\":\"model\"},\"finishReason\":\"STOP\",\"avgLogprobs\":-0.2805800875603865}],\"usageMetadata\":{\"promptTokenCount\":21,\"candidatesTokenCount\":1863,\"totalTokenCount\":1884,\"promptTokensDetails\":[{\"modality\":\"TEXT\",\"tokenCount\":21}],\"candidatesTokensDetails\":[{\"modality\":\"TEXT\",\"tokenCount\":1863}]},\"modelVersion\":\"gemini-2.0-flash\",\"responseId\":\"fE4xaPCjJofU1PIPnPTS-Qc\"},\"request\":{\"messages\":[{\"role\":\"user\",\"content\":[{\"text\":\"مرحباً، قدم نفسك باللغة العربية وأخبرني كيف يمكنني البدء باستخدام Genkit.\"}]}],\"config\":{},\"tools\":[],\"output\":{}}}", "genkit:state": "success"}, "displayName": "generate", "links": [], "instrumentationLibrary": {"name": "genkit-tracer", "version": "v1"}, "spanKind": "INTERNAL", "sameProcessAsParentSpan": {"value": true}, "status": {"code": 0}, "timeEvents": {"timeEvent": []}}}, "displayName": "generate", "startTime": 1748061820280, "endTime": 1748061831889.2803}