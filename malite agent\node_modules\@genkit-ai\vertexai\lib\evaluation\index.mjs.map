{"version": 3, "sources": ["../../src/evaluation/index.ts"], "sourcesContent": ["/**\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Genkit } from 'genkit';\nimport { GenkitPlugin, genkitPlugin } from 'genkit/plugin';\nimport { getDerivedParams } from '../common/index.js';\nimport { vertexEvaluators } from './evaluation.js';\nimport { PluginOptions } from './types.js';\nexport { VertexAIEvaluationMetricType } from './types.js';\nexport { type PluginOptions };\n\n/**\n * Add Google Cloud Vertex AI Rerankers API to Genkit.\n */\nexport function vertexAIEvaluation(options: PluginOptions): GenkitPlugin {\n  return genkitPlugin('vertexAIEvaluation', async (ai: Genkit) => {\n    const { projectId, location, authClient } = await getDerivedParams(options);\n\n    vertexEvaluators(ai, authClient, options.metrics, projectId, location);\n  });\n}\n"], "mappings": "AAiBA,SAAuB,oBAAoB;AAC3C,SAAS,wBAAwB;AACjC,SAAS,wBAAwB;AAEjC,SAAS,oCAAoC;AAMtC,SAAS,mBAAmB,SAAsC;AACvE,SAAO,aAAa,sBAAsB,OAAO,OAAe;AAC9D,UAAM,EAAE,WAAW,UAAU,WAAW,IAAI,MAAM,iBAAiB,OAAO;AAE1E,qBAAiB,IAAI,YAAY,QAAQ,SAAS,WAAW,QAAQ;AAAA,EACvE,CAAC;AACH;", "names": []}