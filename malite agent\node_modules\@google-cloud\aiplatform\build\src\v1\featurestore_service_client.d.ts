import type * as gax from 'google-gax';
import type { Callback, CallOptions, Descriptors, ClientOptions, LROperation, PaginationCallback, IamClient, IamProtos, LocationsClient, LocationProtos } from 'google-gax';
import { Transform } from 'stream';
import * as protos from '../../protos/protos';
/**
 *  The service that handles CRUD and List for resources for Featurestore.
 * @class
 * @memberof v1
 */
export declare class FeaturestoreServiceClient {
    private _terminated;
    private _opts;
    private _providedCustomServicePath;
    private _gaxModule;
    private _gaxGrpc;
    private _protos;
    private _defaults;
    private _universeDomain;
    private _servicePath;
    auth: gax.GoogleAuth;
    descriptors: Descriptors;
    warn: (code: string, message: string, warnType?: string) => void;
    innerApiCalls: {
        [name: string]: Function;
    };
    iamClient: IamClient;
    locationsClient: LocationsClient;
    pathTemplates: {
        [name: string]: gax.PathTemplate;
    };
    operationsClient: gax.OperationsClient;
    featurestoreServiceStub?: Promise<{
        [name: string]: Function;
    }>;
    /**
     * Construct an instance of FeaturestoreServiceClient.
     *
     * @param {object} [options] - The configuration object.
     * The options accepted by the constructor are described in detail
     * in [this document](https://github.com/googleapis/gax-nodejs/blob/main/client-libraries.md#creating-the-client-instance).
     * The common options are:
     * @param {object} [options.credentials] - Credentials object.
     * @param {string} [options.credentials.client_email]
     * @param {string} [options.credentials.private_key]
     * @param {string} [options.email] - Account email address. Required when
     *     using a .pem or .p12 keyFilename.
     * @param {string} [options.keyFilename] - Full path to the a .json, .pem, or
     *     .p12 key downloaded from the Google Developers Console. If you provide
     *     a path to a JSON file, the projectId option below is not necessary.
     *     NOTE: .pem and .p12 require you to specify options.email as well.
     * @param {number} [options.port] - The port on which to connect to
     *     the remote host.
     * @param {string} [options.projectId] - The project ID from the Google
     *     Developer's Console, e.g. 'grape-spaceship-123'. We will also check
     *     the environment variable GCLOUD_PROJECT for your project ID. If your
     *     app is running in an environment which supports
     *     {@link https://developers.google.com/identity/protocols/application-default-credentials Application Default Credentials},
     *     your project ID will be detected automatically.
     * @param {string} [options.apiEndpoint] - The domain name of the
     *     API remote host.
     * @param {gax.ClientConfig} [options.clientConfig] - Client configuration override.
     *     Follows the structure of {@link gapicConfig}.
     * @param {boolean} [options.fallback] - Use HTTP/1.1 REST mode.
     *     For more information, please check the
     *     {@link https://github.com/googleapis/gax-nodejs/blob/main/client-libraries.md#http11-rest-api-mode documentation}.
     * @param {gax} [gaxInstance]: loaded instance of `google-gax`. Useful if you
     *     need to avoid loading the default gRPC version and want to use the fallback
     *     HTTP implementation. Load only fallback version and pass it to the constructor:
     *     ```
     *     const gax = require('google-gax/build/src/fallback'); // avoids loading google-gax with gRPC
     *     const client = new FeaturestoreServiceClient({fallback: true}, gax);
     *     ```
     */
    constructor(opts?: ClientOptions, gaxInstance?: typeof gax | typeof gax.fallback);
    /**
     * Initialize the client.
     * Performs asynchronous operations (such as authentication) and prepares the client.
     * This function will be called automatically when any class method is called for the
     * first time, but if you need to initialize it before calling an actual method,
     * feel free to call initialize() directly.
     *
     * You can await on this method if you want to make sure the client is initialized.
     *
     * @returns {Promise} A promise that resolves to an authenticated service stub.
     */
    initialize(): Promise<{
        [name: string]: Function;
    }>;
    /**
     * The DNS address for this API service.
     * @deprecated Use the apiEndpoint method of the client instance.
     * @returns {string} The DNS address for this service.
     */
    static get servicePath(): string;
    /**
     * The DNS address for this API service - same as servicePath.
     * @deprecated Use the apiEndpoint method of the client instance.
     * @returns {string} The DNS address for this service.
     */
    static get apiEndpoint(): string;
    /**
     * The DNS address for this API service.
     * @returns {string} The DNS address for this service.
     */
    get apiEndpoint(): string;
    get universeDomain(): string;
    /**
     * The port for this API service.
     * @returns {number} The default port for this service.
     */
    static get port(): number;
    /**
     * The scopes needed to make gRPC calls for every method defined
     * in this service.
     * @returns {string[]} List of default scopes.
     */
    static get scopes(): string[];
    getProjectId(): Promise<string>;
    getProjectId(callback: Callback<string, undefined, undefined>): void;
    /**
     * Gets details of a single Featurestore.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.name
     *   Required. The name of the Featurestore resource.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing {@link protos.google.cloud.aiplatform.v1.Featurestore|Featurestore}.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#regular-methods | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1/featurestore_service.get_featurestore.js</caption>
     * region_tag:aiplatform_v1_generated_FeaturestoreService_GetFeaturestore_async
     */
    getFeaturestore(request?: protos.google.cloud.aiplatform.v1.IGetFeaturestoreRequest, options?: CallOptions): Promise<[
        protos.google.cloud.aiplatform.v1.IFeaturestore,
        protos.google.cloud.aiplatform.v1.IGetFeaturestoreRequest | undefined,
        {} | undefined
    ]>;
    getFeaturestore(request: protos.google.cloud.aiplatform.v1.IGetFeaturestoreRequest, options: CallOptions, callback: Callback<protos.google.cloud.aiplatform.v1.IFeaturestore, protos.google.cloud.aiplatform.v1.IGetFeaturestoreRequest | null | undefined, {} | null | undefined>): void;
    getFeaturestore(request: protos.google.cloud.aiplatform.v1.IGetFeaturestoreRequest, callback: Callback<protos.google.cloud.aiplatform.v1.IFeaturestore, protos.google.cloud.aiplatform.v1.IGetFeaturestoreRequest | null | undefined, {} | null | undefined>): void;
    /**
     * Gets details of a single EntityType.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.name
     *   Required. The name of the EntityType resource.
     *   Format:
     *   `projects/{project}/locations/{location}/featurestores/{featurestore}/entityTypes/{entity_type}`
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing {@link protos.google.cloud.aiplatform.v1.EntityType|EntityType}.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#regular-methods | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1/featurestore_service.get_entity_type.js</caption>
     * region_tag:aiplatform_v1_generated_FeaturestoreService_GetEntityType_async
     */
    getEntityType(request?: protos.google.cloud.aiplatform.v1.IGetEntityTypeRequest, options?: CallOptions): Promise<[
        protos.google.cloud.aiplatform.v1.IEntityType,
        protos.google.cloud.aiplatform.v1.IGetEntityTypeRequest | undefined,
        {} | undefined
    ]>;
    getEntityType(request: protos.google.cloud.aiplatform.v1.IGetEntityTypeRequest, options: CallOptions, callback: Callback<protos.google.cloud.aiplatform.v1.IEntityType, protos.google.cloud.aiplatform.v1.IGetEntityTypeRequest | null | undefined, {} | null | undefined>): void;
    getEntityType(request: protos.google.cloud.aiplatform.v1.IGetEntityTypeRequest, callback: Callback<protos.google.cloud.aiplatform.v1.IEntityType, protos.google.cloud.aiplatform.v1.IGetEntityTypeRequest | null | undefined, {} | null | undefined>): void;
    /**
     * Updates the parameters of a single EntityType.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {google.cloud.aiplatform.v1.EntityType} request.entityType
     *   Required. The EntityType's `name` field is used to identify the EntityType
     *   to be updated. Format:
     *   `projects/{project}/locations/{location}/featurestores/{featurestore}/entityTypes/{entity_type}`
     * @param {google.protobuf.FieldMask} request.updateMask
     *   Field mask is used to specify the fields to be overwritten in the
     *   EntityType resource by the update.
     *   The fields specified in the update_mask are relative to the resource, not
     *   the full request. A field will be overwritten if it is in the mask. If the
     *   user does not provide a mask then only the non-empty fields present in the
     *   request will be overwritten. Set the update_mask to `*` to override all
     *   fields.
     *
     *   Updatable fields:
     *
     *     * `description`
     *     * `labels`
     *     * `monitoring_config.snapshot_analysis.disabled`
     *     * `monitoring_config.snapshot_analysis.monitoring_interval_days`
     *     * `monitoring_config.snapshot_analysis.staleness_days`
     *     * `monitoring_config.import_features_analysis.state`
     *     * `monitoring_config.import_features_analysis.anomaly_detection_baseline`
     *     * `monitoring_config.numerical_threshold_config.value`
     *     * `monitoring_config.categorical_threshold_config.value`
     *     * `offline_storage_ttl_days`
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing {@link protos.google.cloud.aiplatform.v1.EntityType|EntityType}.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#regular-methods | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1/featurestore_service.update_entity_type.js</caption>
     * region_tag:aiplatform_v1_generated_FeaturestoreService_UpdateEntityType_async
     */
    updateEntityType(request?: protos.google.cloud.aiplatform.v1.IUpdateEntityTypeRequest, options?: CallOptions): Promise<[
        protos.google.cloud.aiplatform.v1.IEntityType,
        protos.google.cloud.aiplatform.v1.IUpdateEntityTypeRequest | undefined,
        {} | undefined
    ]>;
    updateEntityType(request: protos.google.cloud.aiplatform.v1.IUpdateEntityTypeRequest, options: CallOptions, callback: Callback<protos.google.cloud.aiplatform.v1.IEntityType, protos.google.cloud.aiplatform.v1.IUpdateEntityTypeRequest | null | undefined, {} | null | undefined>): void;
    updateEntityType(request: protos.google.cloud.aiplatform.v1.IUpdateEntityTypeRequest, callback: Callback<protos.google.cloud.aiplatform.v1.IEntityType, protos.google.cloud.aiplatform.v1.IUpdateEntityTypeRequest | null | undefined, {} | null | undefined>): void;
    /**
     * Gets details of a single Feature.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.name
     *   Required. The name of the Feature resource.
     *   Format for entity_type as parent:
     *   `projects/{project}/locations/{location}/featurestores/{featurestore}/entityTypes/{entity_type}`
     *   Format for feature_group as parent:
     *   `projects/{project}/locations/{location}/featureGroups/{feature_group}`
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing {@link protos.google.cloud.aiplatform.v1.Feature|Feature}.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#regular-methods | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1/featurestore_service.get_feature.js</caption>
     * region_tag:aiplatform_v1_generated_FeaturestoreService_GetFeature_async
     */
    getFeature(request?: protos.google.cloud.aiplatform.v1.IGetFeatureRequest, options?: CallOptions): Promise<[
        protos.google.cloud.aiplatform.v1.IFeature,
        protos.google.cloud.aiplatform.v1.IGetFeatureRequest | undefined,
        {} | undefined
    ]>;
    getFeature(request: protos.google.cloud.aiplatform.v1.IGetFeatureRequest, options: CallOptions, callback: Callback<protos.google.cloud.aiplatform.v1.IFeature, protos.google.cloud.aiplatform.v1.IGetFeatureRequest | null | undefined, {} | null | undefined>): void;
    getFeature(request: protos.google.cloud.aiplatform.v1.IGetFeatureRequest, callback: Callback<protos.google.cloud.aiplatform.v1.IFeature, protos.google.cloud.aiplatform.v1.IGetFeatureRequest | null | undefined, {} | null | undefined>): void;
    /**
     * Updates the parameters of a single Feature.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {google.cloud.aiplatform.v1.Feature} request.feature
     *   Required. The Feature's `name` field is used to identify the Feature to be
     *   updated.
     *   Format:
     *   `projects/{project}/locations/{location}/featurestores/{featurestore}/entityTypes/{entity_type}/features/{feature}`
     *   `projects/{project}/locations/{location}/featureGroups/{feature_group}/features/{feature}`
     * @param {google.protobuf.FieldMask} request.updateMask
     *   Field mask is used to specify the fields to be overwritten in the
     *   Features resource by the update.
     *   The fields specified in the update_mask are relative to the resource, not
     *   the full request. A field will be overwritten if it is in the mask. If the
     *   user does not provide a mask then only the non-empty fields present in the
     *   request will be overwritten. Set the update_mask to `*` to override all
     *   fields.
     *
     *   Updatable fields:
     *
     *     * `description`
     *     * `labels`
     *     * `disable_monitoring` (Not supported for FeatureRegistryService Feature)
     *     * `point_of_contact` (Not supported for FeaturestoreService FeatureStore)
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing {@link protos.google.cloud.aiplatform.v1.Feature|Feature}.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#regular-methods | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1/featurestore_service.update_feature.js</caption>
     * region_tag:aiplatform_v1_generated_FeaturestoreService_UpdateFeature_async
     */
    updateFeature(request?: protos.google.cloud.aiplatform.v1.IUpdateFeatureRequest, options?: CallOptions): Promise<[
        protos.google.cloud.aiplatform.v1.IFeature,
        protos.google.cloud.aiplatform.v1.IUpdateFeatureRequest | undefined,
        {} | undefined
    ]>;
    updateFeature(request: protos.google.cloud.aiplatform.v1.IUpdateFeatureRequest, options: CallOptions, callback: Callback<protos.google.cloud.aiplatform.v1.IFeature, protos.google.cloud.aiplatform.v1.IUpdateFeatureRequest | null | undefined, {} | null | undefined>): void;
    updateFeature(request: protos.google.cloud.aiplatform.v1.IUpdateFeatureRequest, callback: Callback<protos.google.cloud.aiplatform.v1.IFeature, protos.google.cloud.aiplatform.v1.IUpdateFeatureRequest | null | undefined, {} | null | undefined>): void;
    /**
     * Creates a new Featurestore in a given project and location.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. The resource name of the Location to create Featurestores.
     *   Format:
     *   `projects/{project}/locations/{location}`
     * @param {google.cloud.aiplatform.v1.Featurestore} request.featurestore
     *   Required. The Featurestore to create.
     * @param {string} request.featurestoreId
     *   Required. The ID to use for this Featurestore, which will become the final
     *   component of the Featurestore's resource name.
     *
     *   This value may be up to 60 characters, and valid characters are
     *   `[a-z0-9_]`. The first character cannot be a number.
     *
     *   The value must be unique within the project and location.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing
     *   a long running operation. Its `promise()` method returns a promise
     *   you can `await` for.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#long-running-operations | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1/featurestore_service.create_featurestore.js</caption>
     * region_tag:aiplatform_v1_generated_FeaturestoreService_CreateFeaturestore_async
     */
    createFeaturestore(request?: protos.google.cloud.aiplatform.v1.ICreateFeaturestoreRequest, options?: CallOptions): Promise<[
        LROperation<protos.google.cloud.aiplatform.v1.IFeaturestore, protos.google.cloud.aiplatform.v1.ICreateFeaturestoreOperationMetadata>,
        protos.google.longrunning.IOperation | undefined,
        {} | undefined
    ]>;
    createFeaturestore(request: protos.google.cloud.aiplatform.v1.ICreateFeaturestoreRequest, options: CallOptions, callback: Callback<LROperation<protos.google.cloud.aiplatform.v1.IFeaturestore, protos.google.cloud.aiplatform.v1.ICreateFeaturestoreOperationMetadata>, protos.google.longrunning.IOperation | null | undefined, {} | null | undefined>): void;
    createFeaturestore(request: protos.google.cloud.aiplatform.v1.ICreateFeaturestoreRequest, callback: Callback<LROperation<protos.google.cloud.aiplatform.v1.IFeaturestore, protos.google.cloud.aiplatform.v1.ICreateFeaturestoreOperationMetadata>, protos.google.longrunning.IOperation | null | undefined, {} | null | undefined>): void;
    /**
     * Check the status of the long running operation returned by `createFeaturestore()`.
     * @param {String} name
     *   The operation name that will be passed.
     * @returns {Promise} - The promise which resolves to an object.
     *   The decoded operation object has result and metadata field to get information from.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#long-running-operations | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1/featurestore_service.create_featurestore.js</caption>
     * region_tag:aiplatform_v1_generated_FeaturestoreService_CreateFeaturestore_async
     */
    checkCreateFeaturestoreProgress(name: string): Promise<LROperation<protos.google.cloud.aiplatform.v1.Featurestore, protos.google.cloud.aiplatform.v1.CreateFeaturestoreOperationMetadata>>;
    /**
     * Updates the parameters of a single Featurestore.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {google.cloud.aiplatform.v1.Featurestore} request.featurestore
     *   Required. The Featurestore's `name` field is used to identify the
     *   Featurestore to be updated. Format:
     *   `projects/{project}/locations/{location}/featurestores/{featurestore}`
     * @param {google.protobuf.FieldMask} request.updateMask
     *   Field mask is used to specify the fields to be overwritten in the
     *   Featurestore resource by the update.
     *   The fields specified in the update_mask are relative to the resource, not
     *   the full request. A field will be overwritten if it is in the mask. If the
     *   user does not provide a mask then only the non-empty fields present in the
     *   request will be overwritten. Set the update_mask to `*` to override all
     *   fields.
     *
     *   Updatable fields:
     *
     *     * `labels`
     *     * `online_serving_config.fixed_node_count`
     *     * `online_serving_config.scaling`
     *     * `online_storage_ttl_days`
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing
     *   a long running operation. Its `promise()` method returns a promise
     *   you can `await` for.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#long-running-operations | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1/featurestore_service.update_featurestore.js</caption>
     * region_tag:aiplatform_v1_generated_FeaturestoreService_UpdateFeaturestore_async
     */
    updateFeaturestore(request?: protos.google.cloud.aiplatform.v1.IUpdateFeaturestoreRequest, options?: CallOptions): Promise<[
        LROperation<protos.google.cloud.aiplatform.v1.IFeaturestore, protos.google.cloud.aiplatform.v1.IUpdateFeaturestoreOperationMetadata>,
        protos.google.longrunning.IOperation | undefined,
        {} | undefined
    ]>;
    updateFeaturestore(request: protos.google.cloud.aiplatform.v1.IUpdateFeaturestoreRequest, options: CallOptions, callback: Callback<LROperation<protos.google.cloud.aiplatform.v1.IFeaturestore, protos.google.cloud.aiplatform.v1.IUpdateFeaturestoreOperationMetadata>, protos.google.longrunning.IOperation | null | undefined, {} | null | undefined>): void;
    updateFeaturestore(request: protos.google.cloud.aiplatform.v1.IUpdateFeaturestoreRequest, callback: Callback<LROperation<protos.google.cloud.aiplatform.v1.IFeaturestore, protos.google.cloud.aiplatform.v1.IUpdateFeaturestoreOperationMetadata>, protos.google.longrunning.IOperation | null | undefined, {} | null | undefined>): void;
    /**
     * Check the status of the long running operation returned by `updateFeaturestore()`.
     * @param {String} name
     *   The operation name that will be passed.
     * @returns {Promise} - The promise which resolves to an object.
     *   The decoded operation object has result and metadata field to get information from.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#long-running-operations | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1/featurestore_service.update_featurestore.js</caption>
     * region_tag:aiplatform_v1_generated_FeaturestoreService_UpdateFeaturestore_async
     */
    checkUpdateFeaturestoreProgress(name: string): Promise<LROperation<protos.google.cloud.aiplatform.v1.Featurestore, protos.google.cloud.aiplatform.v1.UpdateFeaturestoreOperationMetadata>>;
    /**
     * Deletes a single Featurestore. The Featurestore must not contain any
     * EntityTypes or `force` must be set to true for the request to succeed.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.name
     *   Required. The name of the Featurestore to be deleted.
     *   Format:
     *   `projects/{project}/locations/{location}/featurestores/{featurestore}`
     * @param {boolean} request.force
     *   If set to true, any EntityTypes and Features for this Featurestore will
     *   also be deleted. (Otherwise, the request will only work if the Featurestore
     *   has no EntityTypes.)
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing
     *   a long running operation. Its `promise()` method returns a promise
     *   you can `await` for.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#long-running-operations | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1/featurestore_service.delete_featurestore.js</caption>
     * region_tag:aiplatform_v1_generated_FeaturestoreService_DeleteFeaturestore_async
     */
    deleteFeaturestore(request?: protos.google.cloud.aiplatform.v1.IDeleteFeaturestoreRequest, options?: CallOptions): Promise<[
        LROperation<protos.google.protobuf.IEmpty, protos.google.cloud.aiplatform.v1.IDeleteOperationMetadata>,
        protos.google.longrunning.IOperation | undefined,
        {} | undefined
    ]>;
    deleteFeaturestore(request: protos.google.cloud.aiplatform.v1.IDeleteFeaturestoreRequest, options: CallOptions, callback: Callback<LROperation<protos.google.protobuf.IEmpty, protos.google.cloud.aiplatform.v1.IDeleteOperationMetadata>, protos.google.longrunning.IOperation | null | undefined, {} | null | undefined>): void;
    deleteFeaturestore(request: protos.google.cloud.aiplatform.v1.IDeleteFeaturestoreRequest, callback: Callback<LROperation<protos.google.protobuf.IEmpty, protos.google.cloud.aiplatform.v1.IDeleteOperationMetadata>, protos.google.longrunning.IOperation | null | undefined, {} | null | undefined>): void;
    /**
     * Check the status of the long running operation returned by `deleteFeaturestore()`.
     * @param {String} name
     *   The operation name that will be passed.
     * @returns {Promise} - The promise which resolves to an object.
     *   The decoded operation object has result and metadata field to get information from.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#long-running-operations | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1/featurestore_service.delete_featurestore.js</caption>
     * region_tag:aiplatform_v1_generated_FeaturestoreService_DeleteFeaturestore_async
     */
    checkDeleteFeaturestoreProgress(name: string): Promise<LROperation<protos.google.protobuf.Empty, protos.google.cloud.aiplatform.v1.DeleteOperationMetadata>>;
    /**
     * Creates a new EntityType in a given Featurestore.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. The resource name of the Featurestore to create EntityTypes.
     *   Format:
     *   `projects/{project}/locations/{location}/featurestores/{featurestore}`
     * @param {google.cloud.aiplatform.v1.EntityType} request.entityType
     *   The EntityType to create.
     * @param {string} request.entityTypeId
     *   Required. The ID to use for the EntityType, which will become the final
     *   component of the EntityType's resource name.
     *
     *   This value may be up to 60 characters, and valid characters are
     *   `[a-z0-9_]`. The first character cannot be a number.
     *
     *   The value must be unique within a featurestore.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing
     *   a long running operation. Its `promise()` method returns a promise
     *   you can `await` for.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#long-running-operations | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1/featurestore_service.create_entity_type.js</caption>
     * region_tag:aiplatform_v1_generated_FeaturestoreService_CreateEntityType_async
     */
    createEntityType(request?: protos.google.cloud.aiplatform.v1.ICreateEntityTypeRequest, options?: CallOptions): Promise<[
        LROperation<protos.google.cloud.aiplatform.v1.IEntityType, protos.google.cloud.aiplatform.v1.ICreateEntityTypeOperationMetadata>,
        protos.google.longrunning.IOperation | undefined,
        {} | undefined
    ]>;
    createEntityType(request: protos.google.cloud.aiplatform.v1.ICreateEntityTypeRequest, options: CallOptions, callback: Callback<LROperation<protos.google.cloud.aiplatform.v1.IEntityType, protos.google.cloud.aiplatform.v1.ICreateEntityTypeOperationMetadata>, protos.google.longrunning.IOperation | null | undefined, {} | null | undefined>): void;
    createEntityType(request: protos.google.cloud.aiplatform.v1.ICreateEntityTypeRequest, callback: Callback<LROperation<protos.google.cloud.aiplatform.v1.IEntityType, protos.google.cloud.aiplatform.v1.ICreateEntityTypeOperationMetadata>, protos.google.longrunning.IOperation | null | undefined, {} | null | undefined>): void;
    /**
     * Check the status of the long running operation returned by `createEntityType()`.
     * @param {String} name
     *   The operation name that will be passed.
     * @returns {Promise} - The promise which resolves to an object.
     *   The decoded operation object has result and metadata field to get information from.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#long-running-operations | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1/featurestore_service.create_entity_type.js</caption>
     * region_tag:aiplatform_v1_generated_FeaturestoreService_CreateEntityType_async
     */
    checkCreateEntityTypeProgress(name: string): Promise<LROperation<protos.google.cloud.aiplatform.v1.EntityType, protos.google.cloud.aiplatform.v1.CreateEntityTypeOperationMetadata>>;
    /**
     * Deletes a single EntityType. The EntityType must not have any Features
     * or `force` must be set to true for the request to succeed.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.name
     *   Required. The name of the EntityType to be deleted.
     *   Format:
     *   `projects/{project}/locations/{location}/featurestores/{featurestore}/entityTypes/{entity_type}`
     * @param {boolean} request.force
     *   If set to true, any Features for this EntityType will also be deleted.
     *   (Otherwise, the request will only work if the EntityType has no Features.)
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing
     *   a long running operation. Its `promise()` method returns a promise
     *   you can `await` for.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#long-running-operations | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1/featurestore_service.delete_entity_type.js</caption>
     * region_tag:aiplatform_v1_generated_FeaturestoreService_DeleteEntityType_async
     */
    deleteEntityType(request?: protos.google.cloud.aiplatform.v1.IDeleteEntityTypeRequest, options?: CallOptions): Promise<[
        LROperation<protos.google.protobuf.IEmpty, protos.google.cloud.aiplatform.v1.IDeleteOperationMetadata>,
        protos.google.longrunning.IOperation | undefined,
        {} | undefined
    ]>;
    deleteEntityType(request: protos.google.cloud.aiplatform.v1.IDeleteEntityTypeRequest, options: CallOptions, callback: Callback<LROperation<protos.google.protobuf.IEmpty, protos.google.cloud.aiplatform.v1.IDeleteOperationMetadata>, protos.google.longrunning.IOperation | null | undefined, {} | null | undefined>): void;
    deleteEntityType(request: protos.google.cloud.aiplatform.v1.IDeleteEntityTypeRequest, callback: Callback<LROperation<protos.google.protobuf.IEmpty, protos.google.cloud.aiplatform.v1.IDeleteOperationMetadata>, protos.google.longrunning.IOperation | null | undefined, {} | null | undefined>): void;
    /**
     * Check the status of the long running operation returned by `deleteEntityType()`.
     * @param {String} name
     *   The operation name that will be passed.
     * @returns {Promise} - The promise which resolves to an object.
     *   The decoded operation object has result and metadata field to get information from.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#long-running-operations | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1/featurestore_service.delete_entity_type.js</caption>
     * region_tag:aiplatform_v1_generated_FeaturestoreService_DeleteEntityType_async
     */
    checkDeleteEntityTypeProgress(name: string): Promise<LROperation<protos.google.protobuf.Empty, protos.google.cloud.aiplatform.v1.DeleteOperationMetadata>>;
    /**
     * Creates a new Feature in a given EntityType.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. The resource name of the EntityType or FeatureGroup to create a
     *   Feature. Format for entity_type as parent:
     *   `projects/{project}/locations/{location}/featurestores/{featurestore}/entityTypes/{entity_type}`
     *   Format for feature_group as parent:
     *   `projects/{project}/locations/{location}/featureGroups/{feature_group}`
     * @param {google.cloud.aiplatform.v1.Feature} request.feature
     *   Required. The Feature to create.
     * @param {string} request.featureId
     *   Required. The ID to use for the Feature, which will become the final
     *   component of the Feature's resource name.
     *
     *   This value may be up to 128 characters, and valid characters are
     *   `[a-z0-9_]`. The first character cannot be a number.
     *
     *   The value must be unique within an EntityType/FeatureGroup.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing
     *   a long running operation. Its `promise()` method returns a promise
     *   you can `await` for.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#long-running-operations | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1/featurestore_service.create_feature.js</caption>
     * region_tag:aiplatform_v1_generated_FeaturestoreService_CreateFeature_async
     */
    createFeature(request?: protos.google.cloud.aiplatform.v1.ICreateFeatureRequest, options?: CallOptions): Promise<[
        LROperation<protos.google.cloud.aiplatform.v1.IFeature, protos.google.cloud.aiplatform.v1.ICreateFeatureOperationMetadata>,
        protos.google.longrunning.IOperation | undefined,
        {} | undefined
    ]>;
    createFeature(request: protos.google.cloud.aiplatform.v1.ICreateFeatureRequest, options: CallOptions, callback: Callback<LROperation<protos.google.cloud.aiplatform.v1.IFeature, protos.google.cloud.aiplatform.v1.ICreateFeatureOperationMetadata>, protos.google.longrunning.IOperation | null | undefined, {} | null | undefined>): void;
    createFeature(request: protos.google.cloud.aiplatform.v1.ICreateFeatureRequest, callback: Callback<LROperation<protos.google.cloud.aiplatform.v1.IFeature, protos.google.cloud.aiplatform.v1.ICreateFeatureOperationMetadata>, protos.google.longrunning.IOperation | null | undefined, {} | null | undefined>): void;
    /**
     * Check the status of the long running operation returned by `createFeature()`.
     * @param {String} name
     *   The operation name that will be passed.
     * @returns {Promise} - The promise which resolves to an object.
     *   The decoded operation object has result and metadata field to get information from.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#long-running-operations | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1/featurestore_service.create_feature.js</caption>
     * region_tag:aiplatform_v1_generated_FeaturestoreService_CreateFeature_async
     */
    checkCreateFeatureProgress(name: string): Promise<LROperation<protos.google.cloud.aiplatform.v1.Feature, protos.google.cloud.aiplatform.v1.CreateFeatureOperationMetadata>>;
    /**
     * Creates a batch of Features in a given EntityType.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. The resource name of the EntityType/FeatureGroup to create the
     *   batch of Features under. Format:
     *   `projects/{project}/locations/{location}/featurestores/{featurestore}/entityTypes/{entity_type}`
     *   `projects/{project}/locations/{location}/featureGroups/{feature_group}`
     * @param {number[]} request.requests
     *   Required. The request message specifying the Features to create. All
     *   Features must be created under the same parent EntityType / FeatureGroup.
     *   The `parent` field in each child request message can be omitted. If
     *   `parent` is set in a child request, then the value must match the `parent`
     *   value in this request message.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing
     *   a long running operation. Its `promise()` method returns a promise
     *   you can `await` for.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#long-running-operations | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1/featurestore_service.batch_create_features.js</caption>
     * region_tag:aiplatform_v1_generated_FeaturestoreService_BatchCreateFeatures_async
     */
    batchCreateFeatures(request?: protos.google.cloud.aiplatform.v1.IBatchCreateFeaturesRequest, options?: CallOptions): Promise<[
        LROperation<protos.google.cloud.aiplatform.v1.IBatchCreateFeaturesResponse, protos.google.cloud.aiplatform.v1.IBatchCreateFeaturesOperationMetadata>,
        protos.google.longrunning.IOperation | undefined,
        {} | undefined
    ]>;
    batchCreateFeatures(request: protos.google.cloud.aiplatform.v1.IBatchCreateFeaturesRequest, options: CallOptions, callback: Callback<LROperation<protos.google.cloud.aiplatform.v1.IBatchCreateFeaturesResponse, protos.google.cloud.aiplatform.v1.IBatchCreateFeaturesOperationMetadata>, protos.google.longrunning.IOperation | null | undefined, {} | null | undefined>): void;
    batchCreateFeatures(request: protos.google.cloud.aiplatform.v1.IBatchCreateFeaturesRequest, callback: Callback<LROperation<protos.google.cloud.aiplatform.v1.IBatchCreateFeaturesResponse, protos.google.cloud.aiplatform.v1.IBatchCreateFeaturesOperationMetadata>, protos.google.longrunning.IOperation | null | undefined, {} | null | undefined>): void;
    /**
     * Check the status of the long running operation returned by `batchCreateFeatures()`.
     * @param {String} name
     *   The operation name that will be passed.
     * @returns {Promise} - The promise which resolves to an object.
     *   The decoded operation object has result and metadata field to get information from.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#long-running-operations | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1/featurestore_service.batch_create_features.js</caption>
     * region_tag:aiplatform_v1_generated_FeaturestoreService_BatchCreateFeatures_async
     */
    checkBatchCreateFeaturesProgress(name: string): Promise<LROperation<protos.google.cloud.aiplatform.v1.BatchCreateFeaturesResponse, protos.google.cloud.aiplatform.v1.BatchCreateFeaturesOperationMetadata>>;
    /**
     * Deletes a single Feature.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.name
     *   Required. The name of the Features to be deleted.
     *   Format:
     *   `projects/{project}/locations/{location}/featurestores/{featurestore}/entityTypes/{entity_type}/features/{feature}`
     *   `projects/{project}/locations/{location}/featureGroups/{feature_group}/features/{feature}`
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing
     *   a long running operation. Its `promise()` method returns a promise
     *   you can `await` for.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#long-running-operations | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1/featurestore_service.delete_feature.js</caption>
     * region_tag:aiplatform_v1_generated_FeaturestoreService_DeleteFeature_async
     */
    deleteFeature(request?: protos.google.cloud.aiplatform.v1.IDeleteFeatureRequest, options?: CallOptions): Promise<[
        LROperation<protos.google.protobuf.IEmpty, protos.google.cloud.aiplatform.v1.IDeleteOperationMetadata>,
        protos.google.longrunning.IOperation | undefined,
        {} | undefined
    ]>;
    deleteFeature(request: protos.google.cloud.aiplatform.v1.IDeleteFeatureRequest, options: CallOptions, callback: Callback<LROperation<protos.google.protobuf.IEmpty, protos.google.cloud.aiplatform.v1.IDeleteOperationMetadata>, protos.google.longrunning.IOperation | null | undefined, {} | null | undefined>): void;
    deleteFeature(request: protos.google.cloud.aiplatform.v1.IDeleteFeatureRequest, callback: Callback<LROperation<protos.google.protobuf.IEmpty, protos.google.cloud.aiplatform.v1.IDeleteOperationMetadata>, protos.google.longrunning.IOperation | null | undefined, {} | null | undefined>): void;
    /**
     * Check the status of the long running operation returned by `deleteFeature()`.
     * @param {String} name
     *   The operation name that will be passed.
     * @returns {Promise} - The promise which resolves to an object.
     *   The decoded operation object has result and metadata field to get information from.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#long-running-operations | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1/featurestore_service.delete_feature.js</caption>
     * region_tag:aiplatform_v1_generated_FeaturestoreService_DeleteFeature_async
     */
    checkDeleteFeatureProgress(name: string): Promise<LROperation<protos.google.protobuf.Empty, protos.google.cloud.aiplatform.v1.DeleteOperationMetadata>>;
    /**
     * Imports Feature values into the Featurestore from a source storage.
     *
     * The progress of the import is tracked by the returned operation. The
     * imported features are guaranteed to be visible to subsequent read
     * operations after the operation is marked as successfully done.
     *
     * If an import operation fails, the Feature values returned from
     * reads and exports may be inconsistent. If consistency is
     * required, the caller must retry the same import request again and wait till
     * the new operation returned is marked as successfully done.
     *
     * There are also scenarios where the caller can cause inconsistency.
     *
     *  - Source data for import contains multiple distinct Feature values for
     *    the same entity ID and timestamp.
     *  - Source is modified during an import. This includes adding, updating, or
     *  removing source data and/or metadata. Examples of updating metadata
     *  include but are not limited to changing storage location, storage class,
     *  or retention policy.
     *  - Online serving cluster is under-provisioned.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {google.cloud.aiplatform.v1.AvroSource} request.avroSource
     * @param {google.cloud.aiplatform.v1.BigQuerySource} request.bigquerySource
     * @param {google.cloud.aiplatform.v1.CsvSource} request.csvSource
     * @param {string} request.featureTimeField
     *   Source column that holds the Feature timestamp for all Feature
     *   values in each entity.
     * @param {google.protobuf.Timestamp} request.featureTime
     *   Single Feature timestamp for all entities being imported. The
     *   timestamp must not have higher than millisecond precision.
     * @param {string} request.entityType
     *   Required. The resource name of the EntityType grouping the Features for
     *   which values are being imported. Format:
     *   `projects/{project}/locations/{location}/featurestores/{featurestore}/entityTypes/{entityType}`
     * @param {string} request.entityIdField
     *   Source column that holds entity IDs. If not provided, entity IDs are
     *   extracted from the column named entity_id.
     * @param {number[]} request.featureSpecs
     *   Required. Specifications defining which Feature values to import from the
     *   entity. The request fails if no feature_specs are provided, and having
     *   multiple feature_specs for one Feature is not allowed.
     * @param {boolean} request.disableOnlineServing
     *   If set, data will not be imported for online serving. This
     *   is typically used for backfilling, where Feature generation timestamps are
     *   not in the timestamp range needed for online serving.
     * @param {number} request.workerCount
     *   Specifies the number of workers that are used to write data to the
     *   Featurestore. Consider the online serving capacity that you require to
     *   achieve the desired import throughput without interfering with online
     *   serving. The value must be positive, and less than or equal to 100.
     *   If not set, defaults to using 1 worker. The low count ensures minimal
     *   impact on online serving performance.
     * @param {boolean} request.disableIngestionAnalysis
     *   If true, API doesn't start ingestion analysis pipeline.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing
     *   a long running operation. Its `promise()` method returns a promise
     *   you can `await` for.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#long-running-operations | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1/featurestore_service.import_feature_values.js</caption>
     * region_tag:aiplatform_v1_generated_FeaturestoreService_ImportFeatureValues_async
     */
    importFeatureValues(request?: protos.google.cloud.aiplatform.v1.IImportFeatureValuesRequest, options?: CallOptions): Promise<[
        LROperation<protos.google.cloud.aiplatform.v1.IImportFeatureValuesResponse, protos.google.cloud.aiplatform.v1.IImportFeatureValuesOperationMetadata>,
        protos.google.longrunning.IOperation | undefined,
        {} | undefined
    ]>;
    importFeatureValues(request: protos.google.cloud.aiplatform.v1.IImportFeatureValuesRequest, options: CallOptions, callback: Callback<LROperation<protos.google.cloud.aiplatform.v1.IImportFeatureValuesResponse, protos.google.cloud.aiplatform.v1.IImportFeatureValuesOperationMetadata>, protos.google.longrunning.IOperation | null | undefined, {} | null | undefined>): void;
    importFeatureValues(request: protos.google.cloud.aiplatform.v1.IImportFeatureValuesRequest, callback: Callback<LROperation<protos.google.cloud.aiplatform.v1.IImportFeatureValuesResponse, protos.google.cloud.aiplatform.v1.IImportFeatureValuesOperationMetadata>, protos.google.longrunning.IOperation | null | undefined, {} | null | undefined>): void;
    /**
     * Check the status of the long running operation returned by `importFeatureValues()`.
     * @param {String} name
     *   The operation name that will be passed.
     * @returns {Promise} - The promise which resolves to an object.
     *   The decoded operation object has result and metadata field to get information from.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#long-running-operations | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1/featurestore_service.import_feature_values.js</caption>
     * region_tag:aiplatform_v1_generated_FeaturestoreService_ImportFeatureValues_async
     */
    checkImportFeatureValuesProgress(name: string): Promise<LROperation<protos.google.cloud.aiplatform.v1.ImportFeatureValuesResponse, protos.google.cloud.aiplatform.v1.ImportFeatureValuesOperationMetadata>>;
    /**
     * Batch reads Feature values from a Featurestore.
     *
     * This API enables batch reading Feature values, where each read
     * instance in the batch may read Feature values of entities from one or
     * more EntityTypes. Point-in-time correctness is guaranteed for Feature
     * values of each read instance as of each instance's read timestamp.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {google.cloud.aiplatform.v1.CsvSource} request.csvReadInstances
     *   Each read instance consists of exactly one read timestamp and one or more
     *   entity IDs identifying entities of the corresponding EntityTypes whose
     *   Features are requested.
     *
     *   Each output instance contains Feature values of requested entities
     *   concatenated together as of the read time.
     *
     *   An example read instance may be `foo_entity_id, bar_entity_id,
     *   2020-01-01T10:00:00.123Z`.
     *
     *   An example output instance may be `foo_entity_id, bar_entity_id,
     *   2020-01-01T10:00:00.123Z, foo_entity_feature1_value,
     *   bar_entity_feature2_value`.
     *
     *   Timestamp in each read instance must be millisecond-aligned.
     *
     *   `csv_read_instances` are read instances stored in a plain-text CSV file.
     *   The header should be:
     *       [ENTITY_TYPE_ID1], [ENTITY_TYPE_ID2], ..., timestamp
     *
     *   The columns can be in any order.
     *
     *   Values in the timestamp column must use the RFC 3339 format, e.g.
     *   `2012-07-30T10:43:17.123Z`.
     * @param {google.cloud.aiplatform.v1.BigQuerySource} request.bigqueryReadInstances
     *   Similar to csv_read_instances, but from BigQuery source.
     * @param {string} request.featurestore
     *   Required. The resource name of the Featurestore from which to query Feature
     *   values. Format:
     *   `projects/{project}/locations/{location}/featurestores/{featurestore}`
     * @param {google.cloud.aiplatform.v1.FeatureValueDestination} request.destination
     *   Required. Specifies output location and format.
     * @param {number[]} request.passThroughFields
     *   When not empty, the specified fields in the *_read_instances source will be
     *   joined as-is in the output, in addition to those fields from the
     *   Featurestore Entity.
     *
     *   For BigQuery source, the type of the pass-through values will be
     *   automatically inferred. For CSV source, the pass-through values will be
     *   passed as opaque bytes.
     * @param {number[]} request.entityTypeSpecs
     *   Required. Specifies EntityType grouping Features to read values of and
     *   settings.
     * @param {google.protobuf.Timestamp} [request.startTime]
     *   Optional. Excludes Feature values with feature generation timestamp before
     *   this timestamp. If not set, retrieve oldest values kept in Feature Store.
     *   Timestamp, if present, must not have higher than millisecond precision.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing
     *   a long running operation. Its `promise()` method returns a promise
     *   you can `await` for.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#long-running-operations | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1/featurestore_service.batch_read_feature_values.js</caption>
     * region_tag:aiplatform_v1_generated_FeaturestoreService_BatchReadFeatureValues_async
     */
    batchReadFeatureValues(request?: protos.google.cloud.aiplatform.v1.IBatchReadFeatureValuesRequest, options?: CallOptions): Promise<[
        LROperation<protos.google.cloud.aiplatform.v1.IBatchReadFeatureValuesResponse, protos.google.cloud.aiplatform.v1.IBatchReadFeatureValuesOperationMetadata>,
        protos.google.longrunning.IOperation | undefined,
        {} | undefined
    ]>;
    batchReadFeatureValues(request: protos.google.cloud.aiplatform.v1.IBatchReadFeatureValuesRequest, options: CallOptions, callback: Callback<LROperation<protos.google.cloud.aiplatform.v1.IBatchReadFeatureValuesResponse, protos.google.cloud.aiplatform.v1.IBatchReadFeatureValuesOperationMetadata>, protos.google.longrunning.IOperation | null | undefined, {} | null | undefined>): void;
    batchReadFeatureValues(request: protos.google.cloud.aiplatform.v1.IBatchReadFeatureValuesRequest, callback: Callback<LROperation<protos.google.cloud.aiplatform.v1.IBatchReadFeatureValuesResponse, protos.google.cloud.aiplatform.v1.IBatchReadFeatureValuesOperationMetadata>, protos.google.longrunning.IOperation | null | undefined, {} | null | undefined>): void;
    /**
     * Check the status of the long running operation returned by `batchReadFeatureValues()`.
     * @param {String} name
     *   The operation name that will be passed.
     * @returns {Promise} - The promise which resolves to an object.
     *   The decoded operation object has result and metadata field to get information from.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#long-running-operations | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1/featurestore_service.batch_read_feature_values.js</caption>
     * region_tag:aiplatform_v1_generated_FeaturestoreService_BatchReadFeatureValues_async
     */
    checkBatchReadFeatureValuesProgress(name: string): Promise<LROperation<protos.google.cloud.aiplatform.v1.BatchReadFeatureValuesResponse, protos.google.cloud.aiplatform.v1.BatchReadFeatureValuesOperationMetadata>>;
    /**
     * Exports Feature values from all the entities of a target EntityType.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {google.cloud.aiplatform.v1.ExportFeatureValuesRequest.SnapshotExport} request.snapshotExport
     *   Exports the latest Feature values of all entities of the EntityType
     *   within a time range.
     * @param {google.cloud.aiplatform.v1.ExportFeatureValuesRequest.FullExport} request.fullExport
     *   Exports all historical values of all entities of the EntityType within a
     *   time range
     * @param {string} request.entityType
     *   Required. The resource name of the EntityType from which to export Feature
     *   values. Format:
     *   `projects/{project}/locations/{location}/featurestores/{featurestore}/entityTypes/{entity_type}`
     * @param {google.cloud.aiplatform.v1.FeatureValueDestination} request.destination
     *   Required. Specifies destination location and format.
     * @param {google.cloud.aiplatform.v1.FeatureSelector} request.featureSelector
     *   Required. Selects Features to export values of.
     * @param {number[]} request.settings
     *   Per-Feature export settings.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing
     *   a long running operation. Its `promise()` method returns a promise
     *   you can `await` for.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#long-running-operations | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1/featurestore_service.export_feature_values.js</caption>
     * region_tag:aiplatform_v1_generated_FeaturestoreService_ExportFeatureValues_async
     */
    exportFeatureValues(request?: protos.google.cloud.aiplatform.v1.IExportFeatureValuesRequest, options?: CallOptions): Promise<[
        LROperation<protos.google.cloud.aiplatform.v1.IExportFeatureValuesResponse, protos.google.cloud.aiplatform.v1.IExportFeatureValuesOperationMetadata>,
        protos.google.longrunning.IOperation | undefined,
        {} | undefined
    ]>;
    exportFeatureValues(request: protos.google.cloud.aiplatform.v1.IExportFeatureValuesRequest, options: CallOptions, callback: Callback<LROperation<protos.google.cloud.aiplatform.v1.IExportFeatureValuesResponse, protos.google.cloud.aiplatform.v1.IExportFeatureValuesOperationMetadata>, protos.google.longrunning.IOperation | null | undefined, {} | null | undefined>): void;
    exportFeatureValues(request: protos.google.cloud.aiplatform.v1.IExportFeatureValuesRequest, callback: Callback<LROperation<protos.google.cloud.aiplatform.v1.IExportFeatureValuesResponse, protos.google.cloud.aiplatform.v1.IExportFeatureValuesOperationMetadata>, protos.google.longrunning.IOperation | null | undefined, {} | null | undefined>): void;
    /**
     * Check the status of the long running operation returned by `exportFeatureValues()`.
     * @param {String} name
     *   The operation name that will be passed.
     * @returns {Promise} - The promise which resolves to an object.
     *   The decoded operation object has result and metadata field to get information from.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#long-running-operations | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1/featurestore_service.export_feature_values.js</caption>
     * region_tag:aiplatform_v1_generated_FeaturestoreService_ExportFeatureValues_async
     */
    checkExportFeatureValuesProgress(name: string): Promise<LROperation<protos.google.cloud.aiplatform.v1.ExportFeatureValuesResponse, protos.google.cloud.aiplatform.v1.ExportFeatureValuesOperationMetadata>>;
    /**
     * Delete Feature values from Featurestore.
     *
     * The progress of the deletion is tracked by the returned operation. The
     * deleted feature values are guaranteed to be invisible to subsequent read
     * operations after the operation is marked as successfully done.
     *
     * If a delete feature values operation fails, the feature values
     * returned from reads and exports may be inconsistent. If consistency is
     * required, the caller must retry the same delete request again and wait till
     * the new operation returned is marked as successfully done.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {google.cloud.aiplatform.v1.DeleteFeatureValuesRequest.SelectEntity} request.selectEntity
     *   Select feature values to be deleted by specifying entities.
     * @param {google.cloud.aiplatform.v1.DeleteFeatureValuesRequest.SelectTimeRangeAndFeature} request.selectTimeRangeAndFeature
     *   Select feature values to be deleted by specifying time range and
     *   features.
     * @param {string} request.entityType
     *   Required. The resource name of the EntityType grouping the Features for
     *   which values are being deleted from. Format:
     *   `projects/{project}/locations/{location}/featurestores/{featurestore}/entityTypes/{entityType}`
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing
     *   a long running operation. Its `promise()` method returns a promise
     *   you can `await` for.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#long-running-operations | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1/featurestore_service.delete_feature_values.js</caption>
     * region_tag:aiplatform_v1_generated_FeaturestoreService_DeleteFeatureValues_async
     */
    deleteFeatureValues(request?: protos.google.cloud.aiplatform.v1.IDeleteFeatureValuesRequest, options?: CallOptions): Promise<[
        LROperation<protos.google.cloud.aiplatform.v1.IDeleteFeatureValuesResponse, protos.google.cloud.aiplatform.v1.IDeleteFeatureValuesOperationMetadata>,
        protos.google.longrunning.IOperation | undefined,
        {} | undefined
    ]>;
    deleteFeatureValues(request: protos.google.cloud.aiplatform.v1.IDeleteFeatureValuesRequest, options: CallOptions, callback: Callback<LROperation<protos.google.cloud.aiplatform.v1.IDeleteFeatureValuesResponse, protos.google.cloud.aiplatform.v1.IDeleteFeatureValuesOperationMetadata>, protos.google.longrunning.IOperation | null | undefined, {} | null | undefined>): void;
    deleteFeatureValues(request: protos.google.cloud.aiplatform.v1.IDeleteFeatureValuesRequest, callback: Callback<LROperation<protos.google.cloud.aiplatform.v1.IDeleteFeatureValuesResponse, protos.google.cloud.aiplatform.v1.IDeleteFeatureValuesOperationMetadata>, protos.google.longrunning.IOperation | null | undefined, {} | null | undefined>): void;
    /**
     * Check the status of the long running operation returned by `deleteFeatureValues()`.
     * @param {String} name
     *   The operation name that will be passed.
     * @returns {Promise} - The promise which resolves to an object.
     *   The decoded operation object has result and metadata field to get information from.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#long-running-operations | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1/featurestore_service.delete_feature_values.js</caption>
     * region_tag:aiplatform_v1_generated_FeaturestoreService_DeleteFeatureValues_async
     */
    checkDeleteFeatureValuesProgress(name: string): Promise<LROperation<protos.google.cloud.aiplatform.v1.DeleteFeatureValuesResponse, protos.google.cloud.aiplatform.v1.DeleteFeatureValuesOperationMetadata>>;
    /**
     * Lists Featurestores in a given project and location.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. The resource name of the Location to list Featurestores.
     *   Format:
     *   `projects/{project}/locations/{location}`
     * @param {string} request.filter
     *   Lists the featurestores that match the filter expression. The following
     *   fields are supported:
     *
     *   * `create_time`: Supports `=`, `!=`, `<`, `>`, `<=`, and `>=` comparisons.
     *   Values must be
     *     in RFC 3339 format.
     *   * `update_time`: Supports `=`, `!=`, `<`, `>`, `<=`, and `>=` comparisons.
     *   Values must be
     *     in RFC 3339 format.
     *   * `online_serving_config.fixed_node_count`: Supports `=`, `!=`, `<`, `>`,
     *   `<=`, and `>=` comparisons.
     *   * `labels`: Supports key-value equality and key presence.
     *
     *   Examples:
     *
     *   * `create_time > "2020-01-01" OR update_time > "2020-01-01"`
     *      Featurestores created or updated after 2020-01-01.
     *   * `labels.env = "prod"`
     *      Featurestores with label "env" set to "prod".
     * @param {number} request.pageSize
     *   The maximum number of Featurestores to return. The service may return fewer
     *   than this value. If unspecified, at most 100 Featurestores will be
     *   returned. The maximum value is 100; any value greater than 100 will be
     *   coerced to 100.
     * @param {string} request.pageToken
     *   A page token, received from a previous
     *   {@link protos.google.cloud.aiplatform.v1.FeaturestoreService.ListFeaturestores|FeaturestoreService.ListFeaturestores}
     *   call. Provide this to retrieve the subsequent page.
     *
     *   When paginating, all other parameters provided to
     *   {@link protos.google.cloud.aiplatform.v1.FeaturestoreService.ListFeaturestores|FeaturestoreService.ListFeaturestores}
     *   must match the call that provided the page token.
     * @param {string} request.orderBy
     *   A comma-separated list of fields to order by, sorted in ascending order.
     *   Use "desc" after a field name for descending.
     *   Supported Fields:
     *
     *     * `create_time`
     *     * `update_time`
     *     * `online_serving_config.fixed_node_count`
     * @param {google.protobuf.FieldMask} request.readMask
     *   Mask specifying which fields to read.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is Array of {@link protos.google.cloud.aiplatform.v1.Featurestore|Featurestore}.
     *   The client library will perform auto-pagination by default: it will call the API as many
     *   times as needed and will merge results from all the pages into this array.
     *   Note that it can affect your quota.
     *   We recommend using `listFeaturestoresAsync()`
     *   method described below for async iteration which you can stop as needed.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     */
    listFeaturestores(request?: protos.google.cloud.aiplatform.v1.IListFeaturestoresRequest, options?: CallOptions): Promise<[
        protos.google.cloud.aiplatform.v1.IFeaturestore[],
        protos.google.cloud.aiplatform.v1.IListFeaturestoresRequest | null,
        protos.google.cloud.aiplatform.v1.IListFeaturestoresResponse
    ]>;
    listFeaturestores(request: protos.google.cloud.aiplatform.v1.IListFeaturestoresRequest, options: CallOptions, callback: PaginationCallback<protos.google.cloud.aiplatform.v1.IListFeaturestoresRequest, protos.google.cloud.aiplatform.v1.IListFeaturestoresResponse | null | undefined, protos.google.cloud.aiplatform.v1.IFeaturestore>): void;
    listFeaturestores(request: protos.google.cloud.aiplatform.v1.IListFeaturestoresRequest, callback: PaginationCallback<protos.google.cloud.aiplatform.v1.IListFeaturestoresRequest, protos.google.cloud.aiplatform.v1.IListFeaturestoresResponse | null | undefined, protos.google.cloud.aiplatform.v1.IFeaturestore>): void;
    /**
     * Equivalent to `listFeaturestores`, but returns a NodeJS Stream object.
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. The resource name of the Location to list Featurestores.
     *   Format:
     *   `projects/{project}/locations/{location}`
     * @param {string} request.filter
     *   Lists the featurestores that match the filter expression. The following
     *   fields are supported:
     *
     *   * `create_time`: Supports `=`, `!=`, `<`, `>`, `<=`, and `>=` comparisons.
     *   Values must be
     *     in RFC 3339 format.
     *   * `update_time`: Supports `=`, `!=`, `<`, `>`, `<=`, and `>=` comparisons.
     *   Values must be
     *     in RFC 3339 format.
     *   * `online_serving_config.fixed_node_count`: Supports `=`, `!=`, `<`, `>`,
     *   `<=`, and `>=` comparisons.
     *   * `labels`: Supports key-value equality and key presence.
     *
     *   Examples:
     *
     *   * `create_time > "2020-01-01" OR update_time > "2020-01-01"`
     *      Featurestores created or updated after 2020-01-01.
     *   * `labels.env = "prod"`
     *      Featurestores with label "env" set to "prod".
     * @param {number} request.pageSize
     *   The maximum number of Featurestores to return. The service may return fewer
     *   than this value. If unspecified, at most 100 Featurestores will be
     *   returned. The maximum value is 100; any value greater than 100 will be
     *   coerced to 100.
     * @param {string} request.pageToken
     *   A page token, received from a previous
     *   {@link protos.google.cloud.aiplatform.v1.FeaturestoreService.ListFeaturestores|FeaturestoreService.ListFeaturestores}
     *   call. Provide this to retrieve the subsequent page.
     *
     *   When paginating, all other parameters provided to
     *   {@link protos.google.cloud.aiplatform.v1.FeaturestoreService.ListFeaturestores|FeaturestoreService.ListFeaturestores}
     *   must match the call that provided the page token.
     * @param {string} request.orderBy
     *   A comma-separated list of fields to order by, sorted in ascending order.
     *   Use "desc" after a field name for descending.
     *   Supported Fields:
     *
     *     * `create_time`
     *     * `update_time`
     *     * `online_serving_config.fixed_node_count`
     * @param {google.protobuf.FieldMask} request.readMask
     *   Mask specifying which fields to read.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Stream}
     *   An object stream which emits an object representing {@link protos.google.cloud.aiplatform.v1.Featurestore|Featurestore} on 'data' event.
     *   The client library will perform auto-pagination by default: it will call the API as many
     *   times as needed. Note that it can affect your quota.
     *   We recommend using `listFeaturestoresAsync()`
     *   method described below for async iteration which you can stop as needed.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     */
    listFeaturestoresStream(request?: protos.google.cloud.aiplatform.v1.IListFeaturestoresRequest, options?: CallOptions): Transform;
    /**
     * Equivalent to `listFeaturestores`, but returns an iterable object.
     *
     * `for`-`await`-`of` syntax is used with the iterable to get response elements on-demand.
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. The resource name of the Location to list Featurestores.
     *   Format:
     *   `projects/{project}/locations/{location}`
     * @param {string} request.filter
     *   Lists the featurestores that match the filter expression. The following
     *   fields are supported:
     *
     *   * `create_time`: Supports `=`, `!=`, `<`, `>`, `<=`, and `>=` comparisons.
     *   Values must be
     *     in RFC 3339 format.
     *   * `update_time`: Supports `=`, `!=`, `<`, `>`, `<=`, and `>=` comparisons.
     *   Values must be
     *     in RFC 3339 format.
     *   * `online_serving_config.fixed_node_count`: Supports `=`, `!=`, `<`, `>`,
     *   `<=`, and `>=` comparisons.
     *   * `labels`: Supports key-value equality and key presence.
     *
     *   Examples:
     *
     *   * `create_time > "2020-01-01" OR update_time > "2020-01-01"`
     *      Featurestores created or updated after 2020-01-01.
     *   * `labels.env = "prod"`
     *      Featurestores with label "env" set to "prod".
     * @param {number} request.pageSize
     *   The maximum number of Featurestores to return. The service may return fewer
     *   than this value. If unspecified, at most 100 Featurestores will be
     *   returned. The maximum value is 100; any value greater than 100 will be
     *   coerced to 100.
     * @param {string} request.pageToken
     *   A page token, received from a previous
     *   {@link protos.google.cloud.aiplatform.v1.FeaturestoreService.ListFeaturestores|FeaturestoreService.ListFeaturestores}
     *   call. Provide this to retrieve the subsequent page.
     *
     *   When paginating, all other parameters provided to
     *   {@link protos.google.cloud.aiplatform.v1.FeaturestoreService.ListFeaturestores|FeaturestoreService.ListFeaturestores}
     *   must match the call that provided the page token.
     * @param {string} request.orderBy
     *   A comma-separated list of fields to order by, sorted in ascending order.
     *   Use "desc" after a field name for descending.
     *   Supported Fields:
     *
     *     * `create_time`
     *     * `update_time`
     *     * `online_serving_config.fixed_node_count`
     * @param {google.protobuf.FieldMask} request.readMask
     *   Mask specifying which fields to read.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Object}
     *   An iterable Object that allows {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Iteration_protocols | async iteration }.
     *   When you iterate the returned iterable, each element will be an object representing
     *   {@link protos.google.cloud.aiplatform.v1.Featurestore|Featurestore}. The API will be called under the hood as needed, once per the page,
     *   so you can stop the iteration when you don't need more results.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1/featurestore_service.list_featurestores.js</caption>
     * region_tag:aiplatform_v1_generated_FeaturestoreService_ListFeaturestores_async
     */
    listFeaturestoresAsync(request?: protos.google.cloud.aiplatform.v1.IListFeaturestoresRequest, options?: CallOptions): AsyncIterable<protos.google.cloud.aiplatform.v1.IFeaturestore>;
    /**
     * Lists EntityTypes in a given Featurestore.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. The resource name of the Featurestore to list EntityTypes.
     *   Format:
     *   `projects/{project}/locations/{location}/featurestores/{featurestore}`
     * @param {string} request.filter
     *   Lists the EntityTypes that match the filter expression. The following
     *   filters are supported:
     *
     *   * `create_time`: Supports `=`, `!=`, `<`, `>`, `>=`, and `<=` comparisons.
     *   Values must be in RFC 3339 format.
     *   * `update_time`: Supports `=`, `!=`, `<`, `>`, `>=`, and `<=` comparisons.
     *   Values must be in RFC 3339 format.
     *   * `labels`: Supports key-value equality as well as key presence.
     *
     *   Examples:
     *
     *   * `create_time > \"2020-01-31T15:30:00.000000Z\" OR
     *        update_time > \"2020-01-31T15:30:00.000000Z\"` --> EntityTypes created
     *        or updated after 2020-01-31T15:30:00.000000Z.
     *   * `labels.active = yes AND labels.env = prod` --> EntityTypes having both
     *       (active: yes) and (env: prod) labels.
     *   * `labels.env: *` --> Any EntityType which has a label with 'env' as the
     *     key.
     * @param {number} request.pageSize
     *   The maximum number of EntityTypes to return. The service may return fewer
     *   than this value. If unspecified, at most 1000 EntityTypes will be returned.
     *   The maximum value is 1000; any value greater than 1000 will be coerced to
     *   1000.
     * @param {string} request.pageToken
     *   A page token, received from a previous
     *   {@link protos.google.cloud.aiplatform.v1.FeaturestoreService.ListEntityTypes|FeaturestoreService.ListEntityTypes}
     *   call. Provide this to retrieve the subsequent page.
     *
     *   When paginating, all other parameters provided to
     *   {@link protos.google.cloud.aiplatform.v1.FeaturestoreService.ListEntityTypes|FeaturestoreService.ListEntityTypes}
     *   must match the call that provided the page token.
     * @param {string} request.orderBy
     *   A comma-separated list of fields to order by, sorted in ascending order.
     *   Use "desc" after a field name for descending.
     *
     *   Supported fields:
     *
     *     * `entity_type_id`
     *     * `create_time`
     *     * `update_time`
     * @param {google.protobuf.FieldMask} request.readMask
     *   Mask specifying which fields to read.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is Array of {@link protos.google.cloud.aiplatform.v1.EntityType|EntityType}.
     *   The client library will perform auto-pagination by default: it will call the API as many
     *   times as needed and will merge results from all the pages into this array.
     *   Note that it can affect your quota.
     *   We recommend using `listEntityTypesAsync()`
     *   method described below for async iteration which you can stop as needed.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     */
    listEntityTypes(request?: protos.google.cloud.aiplatform.v1.IListEntityTypesRequest, options?: CallOptions): Promise<[
        protos.google.cloud.aiplatform.v1.IEntityType[],
        protos.google.cloud.aiplatform.v1.IListEntityTypesRequest | null,
        protos.google.cloud.aiplatform.v1.IListEntityTypesResponse
    ]>;
    listEntityTypes(request: protos.google.cloud.aiplatform.v1.IListEntityTypesRequest, options: CallOptions, callback: PaginationCallback<protos.google.cloud.aiplatform.v1.IListEntityTypesRequest, protos.google.cloud.aiplatform.v1.IListEntityTypesResponse | null | undefined, protos.google.cloud.aiplatform.v1.IEntityType>): void;
    listEntityTypes(request: protos.google.cloud.aiplatform.v1.IListEntityTypesRequest, callback: PaginationCallback<protos.google.cloud.aiplatform.v1.IListEntityTypesRequest, protos.google.cloud.aiplatform.v1.IListEntityTypesResponse | null | undefined, protos.google.cloud.aiplatform.v1.IEntityType>): void;
    /**
     * Equivalent to `listEntityTypes`, but returns a NodeJS Stream object.
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. The resource name of the Featurestore to list EntityTypes.
     *   Format:
     *   `projects/{project}/locations/{location}/featurestores/{featurestore}`
     * @param {string} request.filter
     *   Lists the EntityTypes that match the filter expression. The following
     *   filters are supported:
     *
     *   * `create_time`: Supports `=`, `!=`, `<`, `>`, `>=`, and `<=` comparisons.
     *   Values must be in RFC 3339 format.
     *   * `update_time`: Supports `=`, `!=`, `<`, `>`, `>=`, and `<=` comparisons.
     *   Values must be in RFC 3339 format.
     *   * `labels`: Supports key-value equality as well as key presence.
     *
     *   Examples:
     *
     *   * `create_time > \"2020-01-31T15:30:00.000000Z\" OR
     *        update_time > \"2020-01-31T15:30:00.000000Z\"` --> EntityTypes created
     *        or updated after 2020-01-31T15:30:00.000000Z.
     *   * `labels.active = yes AND labels.env = prod` --> EntityTypes having both
     *       (active: yes) and (env: prod) labels.
     *   * `labels.env: *` --> Any EntityType which has a label with 'env' as the
     *     key.
     * @param {number} request.pageSize
     *   The maximum number of EntityTypes to return. The service may return fewer
     *   than this value. If unspecified, at most 1000 EntityTypes will be returned.
     *   The maximum value is 1000; any value greater than 1000 will be coerced to
     *   1000.
     * @param {string} request.pageToken
     *   A page token, received from a previous
     *   {@link protos.google.cloud.aiplatform.v1.FeaturestoreService.ListEntityTypes|FeaturestoreService.ListEntityTypes}
     *   call. Provide this to retrieve the subsequent page.
     *
     *   When paginating, all other parameters provided to
     *   {@link protos.google.cloud.aiplatform.v1.FeaturestoreService.ListEntityTypes|FeaturestoreService.ListEntityTypes}
     *   must match the call that provided the page token.
     * @param {string} request.orderBy
     *   A comma-separated list of fields to order by, sorted in ascending order.
     *   Use "desc" after a field name for descending.
     *
     *   Supported fields:
     *
     *     * `entity_type_id`
     *     * `create_time`
     *     * `update_time`
     * @param {google.protobuf.FieldMask} request.readMask
     *   Mask specifying which fields to read.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Stream}
     *   An object stream which emits an object representing {@link protos.google.cloud.aiplatform.v1.EntityType|EntityType} on 'data' event.
     *   The client library will perform auto-pagination by default: it will call the API as many
     *   times as needed. Note that it can affect your quota.
     *   We recommend using `listEntityTypesAsync()`
     *   method described below for async iteration which you can stop as needed.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     */
    listEntityTypesStream(request?: protos.google.cloud.aiplatform.v1.IListEntityTypesRequest, options?: CallOptions): Transform;
    /**
     * Equivalent to `listEntityTypes`, but returns an iterable object.
     *
     * `for`-`await`-`of` syntax is used with the iterable to get response elements on-demand.
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. The resource name of the Featurestore to list EntityTypes.
     *   Format:
     *   `projects/{project}/locations/{location}/featurestores/{featurestore}`
     * @param {string} request.filter
     *   Lists the EntityTypes that match the filter expression. The following
     *   filters are supported:
     *
     *   * `create_time`: Supports `=`, `!=`, `<`, `>`, `>=`, and `<=` comparisons.
     *   Values must be in RFC 3339 format.
     *   * `update_time`: Supports `=`, `!=`, `<`, `>`, `>=`, and `<=` comparisons.
     *   Values must be in RFC 3339 format.
     *   * `labels`: Supports key-value equality as well as key presence.
     *
     *   Examples:
     *
     *   * `create_time > \"2020-01-31T15:30:00.000000Z\" OR
     *        update_time > \"2020-01-31T15:30:00.000000Z\"` --> EntityTypes created
     *        or updated after 2020-01-31T15:30:00.000000Z.
     *   * `labels.active = yes AND labels.env = prod` --> EntityTypes having both
     *       (active: yes) and (env: prod) labels.
     *   * `labels.env: *` --> Any EntityType which has a label with 'env' as the
     *     key.
     * @param {number} request.pageSize
     *   The maximum number of EntityTypes to return. The service may return fewer
     *   than this value. If unspecified, at most 1000 EntityTypes will be returned.
     *   The maximum value is 1000; any value greater than 1000 will be coerced to
     *   1000.
     * @param {string} request.pageToken
     *   A page token, received from a previous
     *   {@link protos.google.cloud.aiplatform.v1.FeaturestoreService.ListEntityTypes|FeaturestoreService.ListEntityTypes}
     *   call. Provide this to retrieve the subsequent page.
     *
     *   When paginating, all other parameters provided to
     *   {@link protos.google.cloud.aiplatform.v1.FeaturestoreService.ListEntityTypes|FeaturestoreService.ListEntityTypes}
     *   must match the call that provided the page token.
     * @param {string} request.orderBy
     *   A comma-separated list of fields to order by, sorted in ascending order.
     *   Use "desc" after a field name for descending.
     *
     *   Supported fields:
     *
     *     * `entity_type_id`
     *     * `create_time`
     *     * `update_time`
     * @param {google.protobuf.FieldMask} request.readMask
     *   Mask specifying which fields to read.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Object}
     *   An iterable Object that allows {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Iteration_protocols | async iteration }.
     *   When you iterate the returned iterable, each element will be an object representing
     *   {@link protos.google.cloud.aiplatform.v1.EntityType|EntityType}. The API will be called under the hood as needed, once per the page,
     *   so you can stop the iteration when you don't need more results.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1/featurestore_service.list_entity_types.js</caption>
     * region_tag:aiplatform_v1_generated_FeaturestoreService_ListEntityTypes_async
     */
    listEntityTypesAsync(request?: protos.google.cloud.aiplatform.v1.IListEntityTypesRequest, options?: CallOptions): AsyncIterable<protos.google.cloud.aiplatform.v1.IEntityType>;
    /**
     * Lists Features in a given EntityType.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. The resource name of the Location to list Features.
     *   Format for entity_type as parent:
     *   `projects/{project}/locations/{location}/featurestores/{featurestore}/entityTypes/{entity_type}`
     *   Format for feature_group as parent:
     *   `projects/{project}/locations/{location}/featureGroups/{feature_group}`
     * @param {string} request.filter
     *   Lists the Features that match the filter expression. The following
     *   filters are supported:
     *
     *   * `value_type`: Supports = and != comparisons.
     *   * `create_time`: Supports =, !=, <, >, >=, and <= comparisons. Values must
     *   be in RFC 3339 format.
     *   * `update_time`: Supports =, !=, <, >, >=, and <= comparisons. Values must
     *   be in RFC 3339 format.
     *   * `labels`: Supports key-value equality as well as key presence.
     *
     *   Examples:
     *
     *   * `value_type = DOUBLE` --> Features whose type is DOUBLE.
     *   * `create_time > \"2020-01-31T15:30:00.000000Z\" OR
     *        update_time > \"2020-01-31T15:30:00.000000Z\"` --> EntityTypes created
     *        or updated after 2020-01-31T15:30:00.000000Z.
     *   * `labels.active = yes AND labels.env = prod` --> Features having both
     *       (active: yes) and (env: prod) labels.
     *   * `labels.env: *` --> Any Feature which has a label with 'env' as the
     *     key.
     * @param {number} request.pageSize
     *   The maximum number of Features to return. The service may return fewer
     *   than this value. If unspecified, at most 1000 Features will be returned.
     *   The maximum value is 1000; any value greater than 1000 will be coerced to
     *   1000.
     * @param {string} request.pageToken
     *   A page token, received from a previous
     *   {@link protos.google.cloud.aiplatform.v1.FeaturestoreService.ListFeatures|FeaturestoreService.ListFeatures}
     *   call or
     *   {@link protos.google.cloud.aiplatform.v1.FeatureRegistryService.ListFeatures|FeatureRegistryService.ListFeatures}
     *   call. Provide this to retrieve the subsequent page.
     *
     *   When paginating, all other parameters provided to
     *   {@link protos.google.cloud.aiplatform.v1.FeaturestoreService.ListFeatures|FeaturestoreService.ListFeatures}
     *   or
     *   {@link protos.google.cloud.aiplatform.v1.FeatureRegistryService.ListFeatures|FeatureRegistryService.ListFeatures}
     *   must match the call that provided the page token.
     * @param {string} request.orderBy
     *   A comma-separated list of fields to order by, sorted in ascending order.
     *   Use "desc" after a field name for descending.
     *   Supported fields:
     *
     *     * `feature_id`
     *     * `value_type` (Not supported for FeatureRegistry Feature)
     *     * `create_time`
     *     * `update_time`
     * @param {google.protobuf.FieldMask} request.readMask
     *   Mask specifying which fields to read.
     * @param {number} request.latestStatsCount
     *   Only applicable for Vertex AI Feature Store (Legacy).
     *   If set, return the most recent
     *   {@link protos.google.cloud.aiplatform.v1.ListFeaturesRequest.latest_stats_count|ListFeaturesRequest.latest_stats_count}
     *   of stats for each Feature in response. Valid value is [0, 10]. If number of
     *   stats exists <
     *   {@link protos.google.cloud.aiplatform.v1.ListFeaturesRequest.latest_stats_count|ListFeaturesRequest.latest_stats_count},
     *   return all existing stats.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is Array of {@link protos.google.cloud.aiplatform.v1.Feature|Feature}.
     *   The client library will perform auto-pagination by default: it will call the API as many
     *   times as needed and will merge results from all the pages into this array.
     *   Note that it can affect your quota.
     *   We recommend using `listFeaturesAsync()`
     *   method described below for async iteration which you can stop as needed.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     */
    listFeatures(request?: protos.google.cloud.aiplatform.v1.IListFeaturesRequest, options?: CallOptions): Promise<[
        protos.google.cloud.aiplatform.v1.IFeature[],
        protos.google.cloud.aiplatform.v1.IListFeaturesRequest | null,
        protos.google.cloud.aiplatform.v1.IListFeaturesResponse
    ]>;
    listFeatures(request: protos.google.cloud.aiplatform.v1.IListFeaturesRequest, options: CallOptions, callback: PaginationCallback<protos.google.cloud.aiplatform.v1.IListFeaturesRequest, protos.google.cloud.aiplatform.v1.IListFeaturesResponse | null | undefined, protos.google.cloud.aiplatform.v1.IFeature>): void;
    listFeatures(request: protos.google.cloud.aiplatform.v1.IListFeaturesRequest, callback: PaginationCallback<protos.google.cloud.aiplatform.v1.IListFeaturesRequest, protos.google.cloud.aiplatform.v1.IListFeaturesResponse | null | undefined, protos.google.cloud.aiplatform.v1.IFeature>): void;
    /**
     * Equivalent to `listFeatures`, but returns a NodeJS Stream object.
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. The resource name of the Location to list Features.
     *   Format for entity_type as parent:
     *   `projects/{project}/locations/{location}/featurestores/{featurestore}/entityTypes/{entity_type}`
     *   Format for feature_group as parent:
     *   `projects/{project}/locations/{location}/featureGroups/{feature_group}`
     * @param {string} request.filter
     *   Lists the Features that match the filter expression. The following
     *   filters are supported:
     *
     *   * `value_type`: Supports = and != comparisons.
     *   * `create_time`: Supports =, !=, <, >, >=, and <= comparisons. Values must
     *   be in RFC 3339 format.
     *   * `update_time`: Supports =, !=, <, >, >=, and <= comparisons. Values must
     *   be in RFC 3339 format.
     *   * `labels`: Supports key-value equality as well as key presence.
     *
     *   Examples:
     *
     *   * `value_type = DOUBLE` --> Features whose type is DOUBLE.
     *   * `create_time > \"2020-01-31T15:30:00.000000Z\" OR
     *        update_time > \"2020-01-31T15:30:00.000000Z\"` --> EntityTypes created
     *        or updated after 2020-01-31T15:30:00.000000Z.
     *   * `labels.active = yes AND labels.env = prod` --> Features having both
     *       (active: yes) and (env: prod) labels.
     *   * `labels.env: *` --> Any Feature which has a label with 'env' as the
     *     key.
     * @param {number} request.pageSize
     *   The maximum number of Features to return. The service may return fewer
     *   than this value. If unspecified, at most 1000 Features will be returned.
     *   The maximum value is 1000; any value greater than 1000 will be coerced to
     *   1000.
     * @param {string} request.pageToken
     *   A page token, received from a previous
     *   {@link protos.google.cloud.aiplatform.v1.FeaturestoreService.ListFeatures|FeaturestoreService.ListFeatures}
     *   call or
     *   {@link protos.google.cloud.aiplatform.v1.FeatureRegistryService.ListFeatures|FeatureRegistryService.ListFeatures}
     *   call. Provide this to retrieve the subsequent page.
     *
     *   When paginating, all other parameters provided to
     *   {@link protos.google.cloud.aiplatform.v1.FeaturestoreService.ListFeatures|FeaturestoreService.ListFeatures}
     *   or
     *   {@link protos.google.cloud.aiplatform.v1.FeatureRegistryService.ListFeatures|FeatureRegistryService.ListFeatures}
     *   must match the call that provided the page token.
     * @param {string} request.orderBy
     *   A comma-separated list of fields to order by, sorted in ascending order.
     *   Use "desc" after a field name for descending.
     *   Supported fields:
     *
     *     * `feature_id`
     *     * `value_type` (Not supported for FeatureRegistry Feature)
     *     * `create_time`
     *     * `update_time`
     * @param {google.protobuf.FieldMask} request.readMask
     *   Mask specifying which fields to read.
     * @param {number} request.latestStatsCount
     *   Only applicable for Vertex AI Feature Store (Legacy).
     *   If set, return the most recent
     *   {@link protos.google.cloud.aiplatform.v1.ListFeaturesRequest.latest_stats_count|ListFeaturesRequest.latest_stats_count}
     *   of stats for each Feature in response. Valid value is [0, 10]. If number of
     *   stats exists <
     *   {@link protos.google.cloud.aiplatform.v1.ListFeaturesRequest.latest_stats_count|ListFeaturesRequest.latest_stats_count},
     *   return all existing stats.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Stream}
     *   An object stream which emits an object representing {@link protos.google.cloud.aiplatform.v1.Feature|Feature} on 'data' event.
     *   The client library will perform auto-pagination by default: it will call the API as many
     *   times as needed. Note that it can affect your quota.
     *   We recommend using `listFeaturesAsync()`
     *   method described below for async iteration which you can stop as needed.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     */
    listFeaturesStream(request?: protos.google.cloud.aiplatform.v1.IListFeaturesRequest, options?: CallOptions): Transform;
    /**
     * Equivalent to `listFeatures`, but returns an iterable object.
     *
     * `for`-`await`-`of` syntax is used with the iterable to get response elements on-demand.
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. The resource name of the Location to list Features.
     *   Format for entity_type as parent:
     *   `projects/{project}/locations/{location}/featurestores/{featurestore}/entityTypes/{entity_type}`
     *   Format for feature_group as parent:
     *   `projects/{project}/locations/{location}/featureGroups/{feature_group}`
     * @param {string} request.filter
     *   Lists the Features that match the filter expression. The following
     *   filters are supported:
     *
     *   * `value_type`: Supports = and != comparisons.
     *   * `create_time`: Supports =, !=, <, >, >=, and <= comparisons. Values must
     *   be in RFC 3339 format.
     *   * `update_time`: Supports =, !=, <, >, >=, and <= comparisons. Values must
     *   be in RFC 3339 format.
     *   * `labels`: Supports key-value equality as well as key presence.
     *
     *   Examples:
     *
     *   * `value_type = DOUBLE` --> Features whose type is DOUBLE.
     *   * `create_time > \"2020-01-31T15:30:00.000000Z\" OR
     *        update_time > \"2020-01-31T15:30:00.000000Z\"` --> EntityTypes created
     *        or updated after 2020-01-31T15:30:00.000000Z.
     *   * `labels.active = yes AND labels.env = prod` --> Features having both
     *       (active: yes) and (env: prod) labels.
     *   * `labels.env: *` --> Any Feature which has a label with 'env' as the
     *     key.
     * @param {number} request.pageSize
     *   The maximum number of Features to return. The service may return fewer
     *   than this value. If unspecified, at most 1000 Features will be returned.
     *   The maximum value is 1000; any value greater than 1000 will be coerced to
     *   1000.
     * @param {string} request.pageToken
     *   A page token, received from a previous
     *   {@link protos.google.cloud.aiplatform.v1.FeaturestoreService.ListFeatures|FeaturestoreService.ListFeatures}
     *   call or
     *   {@link protos.google.cloud.aiplatform.v1.FeatureRegistryService.ListFeatures|FeatureRegistryService.ListFeatures}
     *   call. Provide this to retrieve the subsequent page.
     *
     *   When paginating, all other parameters provided to
     *   {@link protos.google.cloud.aiplatform.v1.FeaturestoreService.ListFeatures|FeaturestoreService.ListFeatures}
     *   or
     *   {@link protos.google.cloud.aiplatform.v1.FeatureRegistryService.ListFeatures|FeatureRegistryService.ListFeatures}
     *   must match the call that provided the page token.
     * @param {string} request.orderBy
     *   A comma-separated list of fields to order by, sorted in ascending order.
     *   Use "desc" after a field name for descending.
     *   Supported fields:
     *
     *     * `feature_id`
     *     * `value_type` (Not supported for FeatureRegistry Feature)
     *     * `create_time`
     *     * `update_time`
     * @param {google.protobuf.FieldMask} request.readMask
     *   Mask specifying which fields to read.
     * @param {number} request.latestStatsCount
     *   Only applicable for Vertex AI Feature Store (Legacy).
     *   If set, return the most recent
     *   {@link protos.google.cloud.aiplatform.v1.ListFeaturesRequest.latest_stats_count|ListFeaturesRequest.latest_stats_count}
     *   of stats for each Feature in response. Valid value is [0, 10]. If number of
     *   stats exists <
     *   {@link protos.google.cloud.aiplatform.v1.ListFeaturesRequest.latest_stats_count|ListFeaturesRequest.latest_stats_count},
     *   return all existing stats.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Object}
     *   An iterable Object that allows {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Iteration_protocols | async iteration }.
     *   When you iterate the returned iterable, each element will be an object representing
     *   {@link protos.google.cloud.aiplatform.v1.Feature|Feature}. The API will be called under the hood as needed, once per the page,
     *   so you can stop the iteration when you don't need more results.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1/featurestore_service.list_features.js</caption>
     * region_tag:aiplatform_v1_generated_FeaturestoreService_ListFeatures_async
     */
    listFeaturesAsync(request?: protos.google.cloud.aiplatform.v1.IListFeaturesRequest, options?: CallOptions): AsyncIterable<protos.google.cloud.aiplatform.v1.IFeature>;
    /**
     * Searches Features matching a query in a given project.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.location
     *   Required. The resource name of the Location to search Features.
     *   Format:
     *   `projects/{project}/locations/{location}`
     * @param {string} request.query
     *   Query string that is a conjunction of field-restricted queries and/or
     *   field-restricted filters.  Field-restricted queries and filters can be
     *   combined using `AND` to form a conjunction.
     *
     *   A field query is in the form FIELD:QUERY. This implicitly checks if QUERY
     *   exists as a substring within Feature's FIELD. The QUERY
     *   and the FIELD are converted to a sequence of words (i.e. tokens) for
     *   comparison. This is done by:
     *
     *     * Removing leading/trailing whitespace and tokenizing the search value.
     *     Characters that are not one of alphanumeric `[a-zA-Z0-9]`, underscore
     *     `_`, or asterisk `*` are treated as delimiters for tokens. `*` is treated
     *     as a wildcard that matches characters within a token.
     *     * Ignoring case.
     *     * Prepending an asterisk to the first and appending an asterisk to the
     *     last token in QUERY.
     *
     *   A QUERY must be either a singular token or a phrase. A phrase is one or
     *   multiple words enclosed in double quotation marks ("). With phrases, the
     *   order of the words is important. Words in the phrase must be matching in
     *   order and consecutively.
     *
     *   Supported FIELDs for field-restricted queries:
     *
     *   * `feature_id`
     *   * `description`
     *   * `entity_type_id`
     *
     *   Examples:
     *
     *   * `feature_id: foo` --> Matches a Feature with ID containing the substring
     *   `foo` (eg. `foo`, `foofeature`, `barfoo`).
     *   * `feature_id: foo*feature` --> Matches a Feature with ID containing the
     *   substring `foo*feature` (eg. `foobarfeature`).
     *   * `feature_id: foo AND description: bar` --> Matches a Feature with ID
     *   containing the substring `foo` and description containing the substring
     *   `bar`.
     *
     *
     *   Besides field queries, the following exact-match filters are
     *   supported. The exact-match filters do not support wildcards. Unlike
     *   field-restricted queries, exact-match filters are case-sensitive.
     *
     *   * `feature_id`: Supports = comparisons.
     *   * `description`: Supports = comparisons. Multi-token filters should be
     *   enclosed in quotes.
     *   * `entity_type_id`: Supports = comparisons.
     *   * `value_type`: Supports = and != comparisons.
     *   * `labels`: Supports key-value equality as well as key presence.
     *   * `featurestore_id`: Supports = comparisons.
     *
     *   Examples:
     *
     *   * `description = "foo bar"` --> Any Feature with description exactly equal
     *   to `foo bar`
     *   * `value_type = DOUBLE` --> Features whose type is DOUBLE.
     *   * `labels.active = yes AND labels.env = prod` --> Features having both
     *       (active: yes) and (env: prod) labels.
     *   * `labels.env: *` --> Any Feature which has a label with `env` as the
     *     key.
     * @param {number} request.pageSize
     *   The maximum number of Features to return. The service may return fewer
     *   than this value. If unspecified, at most 100 Features will be returned.
     *   The maximum value is 100; any value greater than 100 will be coerced to
     *   100.
     * @param {string} request.pageToken
     *   A page token, received from a previous
     *   {@link protos.google.cloud.aiplatform.v1.FeaturestoreService.SearchFeatures|FeaturestoreService.SearchFeatures}
     *   call. Provide this to retrieve the subsequent page.
     *
     *   When paginating, all other parameters provided to
     *   {@link protos.google.cloud.aiplatform.v1.FeaturestoreService.SearchFeatures|FeaturestoreService.SearchFeatures},
     *   except `page_size`, must match the call that provided the page token.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is Array of {@link protos.google.cloud.aiplatform.v1.Feature|Feature}.
     *   The client library will perform auto-pagination by default: it will call the API as many
     *   times as needed and will merge results from all the pages into this array.
     *   Note that it can affect your quota.
     *   We recommend using `searchFeaturesAsync()`
     *   method described below for async iteration which you can stop as needed.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     */
    searchFeatures(request?: protos.google.cloud.aiplatform.v1.ISearchFeaturesRequest, options?: CallOptions): Promise<[
        protos.google.cloud.aiplatform.v1.IFeature[],
        protos.google.cloud.aiplatform.v1.ISearchFeaturesRequest | null,
        protos.google.cloud.aiplatform.v1.ISearchFeaturesResponse
    ]>;
    searchFeatures(request: protos.google.cloud.aiplatform.v1.ISearchFeaturesRequest, options: CallOptions, callback: PaginationCallback<protos.google.cloud.aiplatform.v1.ISearchFeaturesRequest, protos.google.cloud.aiplatform.v1.ISearchFeaturesResponse | null | undefined, protos.google.cloud.aiplatform.v1.IFeature>): void;
    searchFeatures(request: protos.google.cloud.aiplatform.v1.ISearchFeaturesRequest, callback: PaginationCallback<protos.google.cloud.aiplatform.v1.ISearchFeaturesRequest, protos.google.cloud.aiplatform.v1.ISearchFeaturesResponse | null | undefined, protos.google.cloud.aiplatform.v1.IFeature>): void;
    /**
     * Equivalent to `searchFeatures`, but returns a NodeJS Stream object.
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.location
     *   Required. The resource name of the Location to search Features.
     *   Format:
     *   `projects/{project}/locations/{location}`
     * @param {string} request.query
     *   Query string that is a conjunction of field-restricted queries and/or
     *   field-restricted filters.  Field-restricted queries and filters can be
     *   combined using `AND` to form a conjunction.
     *
     *   A field query is in the form FIELD:QUERY. This implicitly checks if QUERY
     *   exists as a substring within Feature's FIELD. The QUERY
     *   and the FIELD are converted to a sequence of words (i.e. tokens) for
     *   comparison. This is done by:
     *
     *     * Removing leading/trailing whitespace and tokenizing the search value.
     *     Characters that are not one of alphanumeric `[a-zA-Z0-9]`, underscore
     *     `_`, or asterisk `*` are treated as delimiters for tokens. `*` is treated
     *     as a wildcard that matches characters within a token.
     *     * Ignoring case.
     *     * Prepending an asterisk to the first and appending an asterisk to the
     *     last token in QUERY.
     *
     *   A QUERY must be either a singular token or a phrase. A phrase is one or
     *   multiple words enclosed in double quotation marks ("). With phrases, the
     *   order of the words is important. Words in the phrase must be matching in
     *   order and consecutively.
     *
     *   Supported FIELDs for field-restricted queries:
     *
     *   * `feature_id`
     *   * `description`
     *   * `entity_type_id`
     *
     *   Examples:
     *
     *   * `feature_id: foo` --> Matches a Feature with ID containing the substring
     *   `foo` (eg. `foo`, `foofeature`, `barfoo`).
     *   * `feature_id: foo*feature` --> Matches a Feature with ID containing the
     *   substring `foo*feature` (eg. `foobarfeature`).
     *   * `feature_id: foo AND description: bar` --> Matches a Feature with ID
     *   containing the substring `foo` and description containing the substring
     *   `bar`.
     *
     *
     *   Besides field queries, the following exact-match filters are
     *   supported. The exact-match filters do not support wildcards. Unlike
     *   field-restricted queries, exact-match filters are case-sensitive.
     *
     *   * `feature_id`: Supports = comparisons.
     *   * `description`: Supports = comparisons. Multi-token filters should be
     *   enclosed in quotes.
     *   * `entity_type_id`: Supports = comparisons.
     *   * `value_type`: Supports = and != comparisons.
     *   * `labels`: Supports key-value equality as well as key presence.
     *   * `featurestore_id`: Supports = comparisons.
     *
     *   Examples:
     *
     *   * `description = "foo bar"` --> Any Feature with description exactly equal
     *   to `foo bar`
     *   * `value_type = DOUBLE` --> Features whose type is DOUBLE.
     *   * `labels.active = yes AND labels.env = prod` --> Features having both
     *       (active: yes) and (env: prod) labels.
     *   * `labels.env: *` --> Any Feature which has a label with `env` as the
     *     key.
     * @param {number} request.pageSize
     *   The maximum number of Features to return. The service may return fewer
     *   than this value. If unspecified, at most 100 Features will be returned.
     *   The maximum value is 100; any value greater than 100 will be coerced to
     *   100.
     * @param {string} request.pageToken
     *   A page token, received from a previous
     *   {@link protos.google.cloud.aiplatform.v1.FeaturestoreService.SearchFeatures|FeaturestoreService.SearchFeatures}
     *   call. Provide this to retrieve the subsequent page.
     *
     *   When paginating, all other parameters provided to
     *   {@link protos.google.cloud.aiplatform.v1.FeaturestoreService.SearchFeatures|FeaturestoreService.SearchFeatures},
     *   except `page_size`, must match the call that provided the page token.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Stream}
     *   An object stream which emits an object representing {@link protos.google.cloud.aiplatform.v1.Feature|Feature} on 'data' event.
     *   The client library will perform auto-pagination by default: it will call the API as many
     *   times as needed. Note that it can affect your quota.
     *   We recommend using `searchFeaturesAsync()`
     *   method described below for async iteration which you can stop as needed.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     */
    searchFeaturesStream(request?: protos.google.cloud.aiplatform.v1.ISearchFeaturesRequest, options?: CallOptions): Transform;
    /**
     * Equivalent to `searchFeatures`, but returns an iterable object.
     *
     * `for`-`await`-`of` syntax is used with the iterable to get response elements on-demand.
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.location
     *   Required. The resource name of the Location to search Features.
     *   Format:
     *   `projects/{project}/locations/{location}`
     * @param {string} request.query
     *   Query string that is a conjunction of field-restricted queries and/or
     *   field-restricted filters.  Field-restricted queries and filters can be
     *   combined using `AND` to form a conjunction.
     *
     *   A field query is in the form FIELD:QUERY. This implicitly checks if QUERY
     *   exists as a substring within Feature's FIELD. The QUERY
     *   and the FIELD are converted to a sequence of words (i.e. tokens) for
     *   comparison. This is done by:
     *
     *     * Removing leading/trailing whitespace and tokenizing the search value.
     *     Characters that are not one of alphanumeric `[a-zA-Z0-9]`, underscore
     *     `_`, or asterisk `*` are treated as delimiters for tokens. `*` is treated
     *     as a wildcard that matches characters within a token.
     *     * Ignoring case.
     *     * Prepending an asterisk to the first and appending an asterisk to the
     *     last token in QUERY.
     *
     *   A QUERY must be either a singular token or a phrase. A phrase is one or
     *   multiple words enclosed in double quotation marks ("). With phrases, the
     *   order of the words is important. Words in the phrase must be matching in
     *   order and consecutively.
     *
     *   Supported FIELDs for field-restricted queries:
     *
     *   * `feature_id`
     *   * `description`
     *   * `entity_type_id`
     *
     *   Examples:
     *
     *   * `feature_id: foo` --> Matches a Feature with ID containing the substring
     *   `foo` (eg. `foo`, `foofeature`, `barfoo`).
     *   * `feature_id: foo*feature` --> Matches a Feature with ID containing the
     *   substring `foo*feature` (eg. `foobarfeature`).
     *   * `feature_id: foo AND description: bar` --> Matches a Feature with ID
     *   containing the substring `foo` and description containing the substring
     *   `bar`.
     *
     *
     *   Besides field queries, the following exact-match filters are
     *   supported. The exact-match filters do not support wildcards. Unlike
     *   field-restricted queries, exact-match filters are case-sensitive.
     *
     *   * `feature_id`: Supports = comparisons.
     *   * `description`: Supports = comparisons. Multi-token filters should be
     *   enclosed in quotes.
     *   * `entity_type_id`: Supports = comparisons.
     *   * `value_type`: Supports = and != comparisons.
     *   * `labels`: Supports key-value equality as well as key presence.
     *   * `featurestore_id`: Supports = comparisons.
     *
     *   Examples:
     *
     *   * `description = "foo bar"` --> Any Feature with description exactly equal
     *   to `foo bar`
     *   * `value_type = DOUBLE` --> Features whose type is DOUBLE.
     *   * `labels.active = yes AND labels.env = prod` --> Features having both
     *       (active: yes) and (env: prod) labels.
     *   * `labels.env: *` --> Any Feature which has a label with `env` as the
     *     key.
     * @param {number} request.pageSize
     *   The maximum number of Features to return. The service may return fewer
     *   than this value. If unspecified, at most 100 Features will be returned.
     *   The maximum value is 100; any value greater than 100 will be coerced to
     *   100.
     * @param {string} request.pageToken
     *   A page token, received from a previous
     *   {@link protos.google.cloud.aiplatform.v1.FeaturestoreService.SearchFeatures|FeaturestoreService.SearchFeatures}
     *   call. Provide this to retrieve the subsequent page.
     *
     *   When paginating, all other parameters provided to
     *   {@link protos.google.cloud.aiplatform.v1.FeaturestoreService.SearchFeatures|FeaturestoreService.SearchFeatures},
     *   except `page_size`, must match the call that provided the page token.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Object}
     *   An iterable Object that allows {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Iteration_protocols | async iteration }.
     *   When you iterate the returned iterable, each element will be an object representing
     *   {@link protos.google.cloud.aiplatform.v1.Feature|Feature}. The API will be called under the hood as needed, once per the page,
     *   so you can stop the iteration when you don't need more results.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1/featurestore_service.search_features.js</caption>
     * region_tag:aiplatform_v1_generated_FeaturestoreService_SearchFeatures_async
     */
    searchFeaturesAsync(request?: protos.google.cloud.aiplatform.v1.ISearchFeaturesRequest, options?: CallOptions): AsyncIterable<protos.google.cloud.aiplatform.v1.IFeature>;
    /**
     * Gets the access control policy for a resource. Returns an empty policy
     * if the resource exists and does not have a policy set.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.resource
     *   REQUIRED: The resource for which the policy is being requested.
     *   See the operation documentation for the appropriate value for this field.
     * @param {Object} [request.options]
     *   OPTIONAL: A `GetPolicyOptions` object for specifying options to
     *   `GetIamPolicy`. This field is only used by Cloud IAM.
     *
     *   This object should have the same structure as {@link google.iam.v1.GetPolicyOptions | GetPolicyOptions}.
     * @param {Object} [options]
     *   Optional parameters. You can override the default settings for this call, e.g, timeout,
     *   retries, paginations, etc. See {@link https://googleapis.github.io/gax-nodejs/interfaces/CallOptions.html | gax.CallOptions} for the details.
     * @param {function(?Error, ?Object)} [callback]
     *   The function which will be called with the result of the API call.
     *
     *   The second parameter to the callback is an object representing {@link google.iam.v1.Policy | Policy}.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing {@link google.iam.v1.Policy | Policy}.
     *   The promise has a method named "cancel" which cancels the ongoing API call.
     */
    getIamPolicy(request: IamProtos.google.iam.v1.GetIamPolicyRequest, options?: gax.CallOptions | Callback<IamProtos.google.iam.v1.Policy, IamProtos.google.iam.v1.GetIamPolicyRequest | null | undefined, {} | null | undefined>, callback?: Callback<IamProtos.google.iam.v1.Policy, IamProtos.google.iam.v1.GetIamPolicyRequest | null | undefined, {} | null | undefined>): Promise<[IamProtos.google.iam.v1.Policy]>;
    /**
     * Returns permissions that a caller has on the specified resource. If the
     * resource does not exist, this will return an empty set of
     * permissions, not a NOT_FOUND error.
     *
     * Note: This operation is designed to be used for building
     * permission-aware UIs and command-line tools, not for authorization
     * checking. This operation may "fail open" without warning.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.resource
     *   REQUIRED: The resource for which the policy detail is being requested.
     *   See the operation documentation for the appropriate value for this field.
     * @param {string[]} request.permissions
     *   The set of permissions to check for the `resource`. Permissions with
     *   wildcards (such as '*' or 'storage.*') are not allowed. For more
     *   information see {@link https://cloud.google.com/iam/docs/overview#permissions | IAM Overview }.
     * @param {Object} [options]
     *   Optional parameters. You can override the default settings for this call, e.g, timeout,
     *   retries, paginations, etc. See {@link https://googleapis.github.io/gax-nodejs/interfaces/CallOptions.html | gax.CallOptions} for the details.
     * @param {function(?Error, ?Object)} [callback]
     *   The function which will be called with the result of the API call.
     *
     *   The second parameter to the callback is an object representing {@link google.iam.v1.TestIamPermissionsResponse | TestIamPermissionsResponse}.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing {@link google.iam.v1.TestIamPermissionsResponse | TestIamPermissionsResponse}.
     *   The promise has a method named "cancel" which cancels the ongoing API call.
     */
    setIamPolicy(request: IamProtos.google.iam.v1.SetIamPolicyRequest, options?: gax.CallOptions | Callback<IamProtos.google.iam.v1.Policy, IamProtos.google.iam.v1.SetIamPolicyRequest | null | undefined, {} | null | undefined>, callback?: Callback<IamProtos.google.iam.v1.Policy, IamProtos.google.iam.v1.SetIamPolicyRequest | null | undefined, {} | null | undefined>): Promise<[IamProtos.google.iam.v1.Policy]>;
    /**
     * Returns permissions that a caller has on the specified resource. If the
     * resource does not exist, this will return an empty set of
     * permissions, not a NOT_FOUND error.
     *
     * Note: This operation is designed to be used for building
     * permission-aware UIs and command-line tools, not for authorization
     * checking. This operation may "fail open" without warning.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.resource
     *   REQUIRED: The resource for which the policy detail is being requested.
     *   See the operation documentation for the appropriate value for this field.
     * @param {string[]} request.permissions
     *   The set of permissions to check for the `resource`. Permissions with
     *   wildcards (such as '*' or 'storage.*') are not allowed. For more
     *   information see {@link https://cloud.google.com/iam/docs/overview#permissions | IAM Overview }.
     * @param {Object} [options]
     *   Optional parameters. You can override the default settings for this call, e.g, timeout,
     *   retries, paginations, etc. See {@link https://googleapis.github.io/gax-nodejs/interfaces/CallOptions.html | gax.CallOptions} for the details.
     * @param {function(?Error, ?Object)} [callback]
     *   The function which will be called with the result of the API call.
     *
     *   The second parameter to the callback is an object representing {@link google.iam.v1.TestIamPermissionsResponse | TestIamPermissionsResponse}.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing {@link google.iam.v1.TestIamPermissionsResponse | TestIamPermissionsResponse}.
     *   The promise has a method named "cancel" which cancels the ongoing API call.
     *
     */
    testIamPermissions(request: IamProtos.google.iam.v1.TestIamPermissionsRequest, options?: gax.CallOptions | Callback<IamProtos.google.iam.v1.TestIamPermissionsResponse, IamProtos.google.iam.v1.TestIamPermissionsRequest | null | undefined, {} | null | undefined>, callback?: Callback<IamProtos.google.iam.v1.TestIamPermissionsResponse, IamProtos.google.iam.v1.TestIamPermissionsRequest | null | undefined, {} | null | undefined>): Promise<[IamProtos.google.iam.v1.TestIamPermissionsResponse]>;
    /**
     * Gets information about a location.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.name
     *   Resource name for the location.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html | CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing {@link google.cloud.location.Location | Location}.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#regular-methods | documentation }
     *   for more details and examples.
     * @example
     * ```
     * const [response] = await client.getLocation(request);
     * ```
     */
    getLocation(request: LocationProtos.google.cloud.location.IGetLocationRequest, options?: gax.CallOptions | Callback<LocationProtos.google.cloud.location.ILocation, LocationProtos.google.cloud.location.IGetLocationRequest | null | undefined, {} | null | undefined>, callback?: Callback<LocationProtos.google.cloud.location.ILocation, LocationProtos.google.cloud.location.IGetLocationRequest | null | undefined, {} | null | undefined>): Promise<LocationProtos.google.cloud.location.ILocation>;
    /**
     * Lists information about the supported locations for this service. Returns an iterable object.
     *
     * `for`-`await`-`of` syntax is used with the iterable to get response elements on-demand.
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.name
     *   The resource that owns the locations collection, if applicable.
     * @param {string} request.filter
     *   The standard list filter.
     * @param {number} request.pageSize
     *   The standard list page size.
     * @param {string} request.pageToken
     *   The standard list page token.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Object}
     *   An iterable Object that allows {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Iteration_protocols | async iteration }.
     *   When you iterate the returned iterable, each element will be an object representing
     *   {@link google.cloud.location.Location | Location}. The API will be called under the hood as needed, once per the page,
     *   so you can stop the iteration when you don't need more results.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     * @example
     * ```
     * const iterable = client.listLocationsAsync(request);
     * for await (const response of iterable) {
     *   // process response
     * }
     * ```
     */
    listLocationsAsync(request: LocationProtos.google.cloud.location.IListLocationsRequest, options?: CallOptions): AsyncIterable<LocationProtos.google.cloud.location.ILocation>;
    /**
     * Gets the latest state of a long-running operation.  Clients can use this
     * method to poll the operation result at intervals as recommended by the API
     * service.
     *
     * @param {Object} request - The request object that will be sent.
     * @param {string} request.name - The name of the operation resource.
     * @param {Object=} options
     *   Optional parameters. You can override the default settings for this call,
     *   e.g, timeout, retries, paginations, etc. See {@link
     *   https://googleapis.github.io/gax-nodejs/global.html#CallOptions | gax.CallOptions}
     *   for the details.
     * @param {function(?Error, ?Object)=} callback
     *   The function which will be called with the result of the API call.
     *
     *   The second parameter to the callback is an object representing
     *   {@link google.longrunning.Operation | google.longrunning.Operation}.
     * @return {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing
     * {@link google.longrunning.Operation | google.longrunning.Operation}.
     * The promise has a method named "cancel" which cancels the ongoing API call.
     *
     * @example
     * ```
     * const client = longrunning.operationsClient();
     * const name = '';
     * const [response] = await client.getOperation({name});
     * // doThingsWith(response)
     * ```
     */
    getOperation(request: protos.google.longrunning.GetOperationRequest, optionsOrCallback?: gax.CallOptions | Callback<protos.google.longrunning.Operation, protos.google.longrunning.GetOperationRequest, {} | null | undefined>, callback?: Callback<protos.google.longrunning.Operation, protos.google.longrunning.GetOperationRequest, {} | null | undefined>): Promise<[protos.google.longrunning.Operation]>;
    /**
     * Lists operations that match the specified filter in the request. If the
     * server doesn't support this method, it returns `UNIMPLEMENTED`. Returns an iterable object.
     *
     * For-await-of syntax is used with the iterable to recursively get response element on-demand.
     *
     * @param {Object} request - The request object that will be sent.
     * @param {string} request.name - The name of the operation collection.
     * @param {string} request.filter - The standard list filter.
     * @param {number=} request.pageSize -
     *   The maximum number of resources contained in the underlying API
     *   response. If page streaming is performed per-resource, this
     *   parameter does not affect the return value. If page streaming is
     *   performed per-page, this determines the maximum number of
     *   resources in a page.
     * @param {Object=} options
     *   Optional parameters. You can override the default settings for this call,
     *   e.g, timeout, retries, paginations, etc. See {@link
     *   https://googleapis.github.io/gax-nodejs/global.html#CallOptions | gax.CallOptions} for the
     *   details.
     * @returns {Object}
     *   An iterable Object that conforms to {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Iteration_protocols | iteration protocols}.
     *
     * @example
     * ```
     * const client = longrunning.operationsClient();
     * for await (const response of client.listOperationsAsync(request));
     * // doThingsWith(response)
     * ```
     */
    listOperationsAsync(request: protos.google.longrunning.ListOperationsRequest, options?: gax.CallOptions): AsyncIterable<protos.google.longrunning.ListOperationsResponse>;
    /**
     * Starts asynchronous cancellation on a long-running operation.  The server
     * makes a best effort to cancel the operation, but success is not
     * guaranteed.  If the server doesn't support this method, it returns
     * `google.rpc.Code.UNIMPLEMENTED`.  Clients can use
     * {@link Operations.GetOperation} or
     * other methods to check whether the cancellation succeeded or whether the
     * operation completed despite cancellation. On successful cancellation,
     * the operation is not deleted; instead, it becomes an operation with
     * an {@link Operation.error} value with a {@link google.rpc.Status.code} of
     * 1, corresponding to `Code.CANCELLED`.
     *
     * @param {Object} request - The request object that will be sent.
     * @param {string} request.name - The name of the operation resource to be cancelled.
     * @param {Object=} options
     *   Optional parameters. You can override the default settings for this call,
     * e.g, timeout, retries, paginations, etc. See {@link
     * https://googleapis.github.io/gax-nodejs/global.html#CallOptions | gax.CallOptions} for the
     * details.
     * @param {function(?Error)=} callback
     *   The function which will be called with the result of the API call.
     * @return {Promise} - The promise which resolves when API call finishes.
     *   The promise has a method named "cancel" which cancels the ongoing API
     * call.
     *
     * @example
     * ```
     * const client = longrunning.operationsClient();
     * await client.cancelOperation({name: ''});
     * ```
     */
    cancelOperation(request: protos.google.longrunning.CancelOperationRequest, optionsOrCallback?: gax.CallOptions | Callback<protos.google.longrunning.CancelOperationRequest, protos.google.protobuf.Empty, {} | undefined | null>, callback?: Callback<protos.google.longrunning.CancelOperationRequest, protos.google.protobuf.Empty, {} | undefined | null>): Promise<protos.google.protobuf.Empty>;
    /**
     * Deletes a long-running operation. This method indicates that the client is
     * no longer interested in the operation result. It does not cancel the
     * operation. If the server doesn't support this method, it returns
     * `google.rpc.Code.UNIMPLEMENTED`.
     *
     * @param {Object} request - The request object that will be sent.
     * @param {string} request.name - The name of the operation resource to be deleted.
     * @param {Object=} options
     *   Optional parameters. You can override the default settings for this call,
     * e.g, timeout, retries, paginations, etc. See {@link
     * https://googleapis.github.io/gax-nodejs/global.html#CallOptions | gax.CallOptions}
     * for the details.
     * @param {function(?Error)=} callback
     *   The function which will be called with the result of the API call.
     * @return {Promise} - The promise which resolves when API call finishes.
     *   The promise has a method named "cancel" which cancels the ongoing API
     * call.
     *
     * @example
     * ```
     * const client = longrunning.operationsClient();
     * await client.deleteOperation({name: ''});
     * ```
     */
    deleteOperation(request: protos.google.longrunning.DeleteOperationRequest, optionsOrCallback?: gax.CallOptions | Callback<protos.google.protobuf.Empty, protos.google.longrunning.DeleteOperationRequest, {} | null | undefined>, callback?: Callback<protos.google.protobuf.Empty, protos.google.longrunning.DeleteOperationRequest, {} | null | undefined>): Promise<protos.google.protobuf.Empty>;
    /**
     * Return a fully-qualified annotation resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} dataset
     * @param {string} data_item
     * @param {string} annotation
     * @returns {string} Resource name string.
     */
    annotationPath(project: string, location: string, dataset: string, dataItem: string, annotation: string): string;
    /**
     * Parse the project from Annotation resource.
     *
     * @param {string} annotationName
     *   A fully-qualified path representing Annotation resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromAnnotationName(annotationName: string): string | number;
    /**
     * Parse the location from Annotation resource.
     *
     * @param {string} annotationName
     *   A fully-qualified path representing Annotation resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromAnnotationName(annotationName: string): string | number;
    /**
     * Parse the dataset from Annotation resource.
     *
     * @param {string} annotationName
     *   A fully-qualified path representing Annotation resource.
     * @returns {string} A string representing the dataset.
     */
    matchDatasetFromAnnotationName(annotationName: string): string | number;
    /**
     * Parse the data_item from Annotation resource.
     *
     * @param {string} annotationName
     *   A fully-qualified path representing Annotation resource.
     * @returns {string} A string representing the data_item.
     */
    matchDataItemFromAnnotationName(annotationName: string): string | number;
    /**
     * Parse the annotation from Annotation resource.
     *
     * @param {string} annotationName
     *   A fully-qualified path representing Annotation resource.
     * @returns {string} A string representing the annotation.
     */
    matchAnnotationFromAnnotationName(annotationName: string): string | number;
    /**
     * Return a fully-qualified annotationSpec resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} dataset
     * @param {string} annotation_spec
     * @returns {string} Resource name string.
     */
    annotationSpecPath(project: string, location: string, dataset: string, annotationSpec: string): string;
    /**
     * Parse the project from AnnotationSpec resource.
     *
     * @param {string} annotationSpecName
     *   A fully-qualified path representing AnnotationSpec resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromAnnotationSpecName(annotationSpecName: string): string | number;
    /**
     * Parse the location from AnnotationSpec resource.
     *
     * @param {string} annotationSpecName
     *   A fully-qualified path representing AnnotationSpec resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromAnnotationSpecName(annotationSpecName: string): string | number;
    /**
     * Parse the dataset from AnnotationSpec resource.
     *
     * @param {string} annotationSpecName
     *   A fully-qualified path representing AnnotationSpec resource.
     * @returns {string} A string representing the dataset.
     */
    matchDatasetFromAnnotationSpecName(annotationSpecName: string): string | number;
    /**
     * Parse the annotation_spec from AnnotationSpec resource.
     *
     * @param {string} annotationSpecName
     *   A fully-qualified path representing AnnotationSpec resource.
     * @returns {string} A string representing the annotation_spec.
     */
    matchAnnotationSpecFromAnnotationSpecName(annotationSpecName: string): string | number;
    /**
     * Return a fully-qualified artifact resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} metadata_store
     * @param {string} artifact
     * @returns {string} Resource name string.
     */
    artifactPath(project: string, location: string, metadataStore: string, artifact: string): string;
    /**
     * Parse the project from Artifact resource.
     *
     * @param {string} artifactName
     *   A fully-qualified path representing Artifact resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromArtifactName(artifactName: string): string | number;
    /**
     * Parse the location from Artifact resource.
     *
     * @param {string} artifactName
     *   A fully-qualified path representing Artifact resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromArtifactName(artifactName: string): string | number;
    /**
     * Parse the metadata_store from Artifact resource.
     *
     * @param {string} artifactName
     *   A fully-qualified path representing Artifact resource.
     * @returns {string} A string representing the metadata_store.
     */
    matchMetadataStoreFromArtifactName(artifactName: string): string | number;
    /**
     * Parse the artifact from Artifact resource.
     *
     * @param {string} artifactName
     *   A fully-qualified path representing Artifact resource.
     * @returns {string} A string representing the artifact.
     */
    matchArtifactFromArtifactName(artifactName: string): string | number;
    /**
     * Return a fully-qualified batchPredictionJob resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} batch_prediction_job
     * @returns {string} Resource name string.
     */
    batchPredictionJobPath(project: string, location: string, batchPredictionJob: string): string;
    /**
     * Parse the project from BatchPredictionJob resource.
     *
     * @param {string} batchPredictionJobName
     *   A fully-qualified path representing BatchPredictionJob resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromBatchPredictionJobName(batchPredictionJobName: string): string | number;
    /**
     * Parse the location from BatchPredictionJob resource.
     *
     * @param {string} batchPredictionJobName
     *   A fully-qualified path representing BatchPredictionJob resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromBatchPredictionJobName(batchPredictionJobName: string): string | number;
    /**
     * Parse the batch_prediction_job from BatchPredictionJob resource.
     *
     * @param {string} batchPredictionJobName
     *   A fully-qualified path representing BatchPredictionJob resource.
     * @returns {string} A string representing the batch_prediction_job.
     */
    matchBatchPredictionJobFromBatchPredictionJobName(batchPredictionJobName: string): string | number;
    /**
     * Return a fully-qualified cachedContent resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} cached_content
     * @returns {string} Resource name string.
     */
    cachedContentPath(project: string, location: string, cachedContent: string): string;
    /**
     * Parse the project from CachedContent resource.
     *
     * @param {string} cachedContentName
     *   A fully-qualified path representing CachedContent resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromCachedContentName(cachedContentName: string): string | number;
    /**
     * Parse the location from CachedContent resource.
     *
     * @param {string} cachedContentName
     *   A fully-qualified path representing CachedContent resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromCachedContentName(cachedContentName: string): string | number;
    /**
     * Parse the cached_content from CachedContent resource.
     *
     * @param {string} cachedContentName
     *   A fully-qualified path representing CachedContent resource.
     * @returns {string} A string representing the cached_content.
     */
    matchCachedContentFromCachedContentName(cachedContentName: string): string | number;
    /**
     * Return a fully-qualified context resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} metadata_store
     * @param {string} context
     * @returns {string} Resource name string.
     */
    contextPath(project: string, location: string, metadataStore: string, context: string): string;
    /**
     * Parse the project from Context resource.
     *
     * @param {string} contextName
     *   A fully-qualified path representing Context resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromContextName(contextName: string): string | number;
    /**
     * Parse the location from Context resource.
     *
     * @param {string} contextName
     *   A fully-qualified path representing Context resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromContextName(contextName: string): string | number;
    /**
     * Parse the metadata_store from Context resource.
     *
     * @param {string} contextName
     *   A fully-qualified path representing Context resource.
     * @returns {string} A string representing the metadata_store.
     */
    matchMetadataStoreFromContextName(contextName: string): string | number;
    /**
     * Parse the context from Context resource.
     *
     * @param {string} contextName
     *   A fully-qualified path representing Context resource.
     * @returns {string} A string representing the context.
     */
    matchContextFromContextName(contextName: string): string | number;
    /**
     * Return a fully-qualified customJob resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} custom_job
     * @returns {string} Resource name string.
     */
    customJobPath(project: string, location: string, customJob: string): string;
    /**
     * Parse the project from CustomJob resource.
     *
     * @param {string} customJobName
     *   A fully-qualified path representing CustomJob resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromCustomJobName(customJobName: string): string | number;
    /**
     * Parse the location from CustomJob resource.
     *
     * @param {string} customJobName
     *   A fully-qualified path representing CustomJob resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromCustomJobName(customJobName: string): string | number;
    /**
     * Parse the custom_job from CustomJob resource.
     *
     * @param {string} customJobName
     *   A fully-qualified path representing CustomJob resource.
     * @returns {string} A string representing the custom_job.
     */
    matchCustomJobFromCustomJobName(customJobName: string): string | number;
    /**
     * Return a fully-qualified dataItem resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} dataset
     * @param {string} data_item
     * @returns {string} Resource name string.
     */
    dataItemPath(project: string, location: string, dataset: string, dataItem: string): string;
    /**
     * Parse the project from DataItem resource.
     *
     * @param {string} dataItemName
     *   A fully-qualified path representing DataItem resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromDataItemName(dataItemName: string): string | number;
    /**
     * Parse the location from DataItem resource.
     *
     * @param {string} dataItemName
     *   A fully-qualified path representing DataItem resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromDataItemName(dataItemName: string): string | number;
    /**
     * Parse the dataset from DataItem resource.
     *
     * @param {string} dataItemName
     *   A fully-qualified path representing DataItem resource.
     * @returns {string} A string representing the dataset.
     */
    matchDatasetFromDataItemName(dataItemName: string): string | number;
    /**
     * Parse the data_item from DataItem resource.
     *
     * @param {string} dataItemName
     *   A fully-qualified path representing DataItem resource.
     * @returns {string} A string representing the data_item.
     */
    matchDataItemFromDataItemName(dataItemName: string): string | number;
    /**
     * Return a fully-qualified dataLabelingJob resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} data_labeling_job
     * @returns {string} Resource name string.
     */
    dataLabelingJobPath(project: string, location: string, dataLabelingJob: string): string;
    /**
     * Parse the project from DataLabelingJob resource.
     *
     * @param {string} dataLabelingJobName
     *   A fully-qualified path representing DataLabelingJob resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromDataLabelingJobName(dataLabelingJobName: string): string | number;
    /**
     * Parse the location from DataLabelingJob resource.
     *
     * @param {string} dataLabelingJobName
     *   A fully-qualified path representing DataLabelingJob resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromDataLabelingJobName(dataLabelingJobName: string): string | number;
    /**
     * Parse the data_labeling_job from DataLabelingJob resource.
     *
     * @param {string} dataLabelingJobName
     *   A fully-qualified path representing DataLabelingJob resource.
     * @returns {string} A string representing the data_labeling_job.
     */
    matchDataLabelingJobFromDataLabelingJobName(dataLabelingJobName: string): string | number;
    /**
     * Return a fully-qualified dataset resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} dataset
     * @returns {string} Resource name string.
     */
    datasetPath(project: string, location: string, dataset: string): string;
    /**
     * Parse the project from Dataset resource.
     *
     * @param {string} datasetName
     *   A fully-qualified path representing Dataset resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromDatasetName(datasetName: string): string | number;
    /**
     * Parse the location from Dataset resource.
     *
     * @param {string} datasetName
     *   A fully-qualified path representing Dataset resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromDatasetName(datasetName: string): string | number;
    /**
     * Parse the dataset from Dataset resource.
     *
     * @param {string} datasetName
     *   A fully-qualified path representing Dataset resource.
     * @returns {string} A string representing the dataset.
     */
    matchDatasetFromDatasetName(datasetName: string): string | number;
    /**
     * Return a fully-qualified datasetVersion resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} dataset
     * @param {string} dataset_version
     * @returns {string} Resource name string.
     */
    datasetVersionPath(project: string, location: string, dataset: string, datasetVersion: string): string;
    /**
     * Parse the project from DatasetVersion resource.
     *
     * @param {string} datasetVersionName
     *   A fully-qualified path representing DatasetVersion resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromDatasetVersionName(datasetVersionName: string): string | number;
    /**
     * Parse the location from DatasetVersion resource.
     *
     * @param {string} datasetVersionName
     *   A fully-qualified path representing DatasetVersion resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromDatasetVersionName(datasetVersionName: string): string | number;
    /**
     * Parse the dataset from DatasetVersion resource.
     *
     * @param {string} datasetVersionName
     *   A fully-qualified path representing DatasetVersion resource.
     * @returns {string} A string representing the dataset.
     */
    matchDatasetFromDatasetVersionName(datasetVersionName: string): string | number;
    /**
     * Parse the dataset_version from DatasetVersion resource.
     *
     * @param {string} datasetVersionName
     *   A fully-qualified path representing DatasetVersion resource.
     * @returns {string} A string representing the dataset_version.
     */
    matchDatasetVersionFromDatasetVersionName(datasetVersionName: string): string | number;
    /**
     * Return a fully-qualified deploymentResourcePool resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} deployment_resource_pool
     * @returns {string} Resource name string.
     */
    deploymentResourcePoolPath(project: string, location: string, deploymentResourcePool: string): string;
    /**
     * Parse the project from DeploymentResourcePool resource.
     *
     * @param {string} deploymentResourcePoolName
     *   A fully-qualified path representing DeploymentResourcePool resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromDeploymentResourcePoolName(deploymentResourcePoolName: string): string | number;
    /**
     * Parse the location from DeploymentResourcePool resource.
     *
     * @param {string} deploymentResourcePoolName
     *   A fully-qualified path representing DeploymentResourcePool resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromDeploymentResourcePoolName(deploymentResourcePoolName: string): string | number;
    /**
     * Parse the deployment_resource_pool from DeploymentResourcePool resource.
     *
     * @param {string} deploymentResourcePoolName
     *   A fully-qualified path representing DeploymentResourcePool resource.
     * @returns {string} A string representing the deployment_resource_pool.
     */
    matchDeploymentResourcePoolFromDeploymentResourcePoolName(deploymentResourcePoolName: string): string | number;
    /**
     * Return a fully-qualified entityType resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} featurestore
     * @param {string} entity_type
     * @returns {string} Resource name string.
     */
    entityTypePath(project: string, location: string, featurestore: string, entityType: string): string;
    /**
     * Parse the project from EntityType resource.
     *
     * @param {string} entityTypeName
     *   A fully-qualified path representing EntityType resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromEntityTypeName(entityTypeName: string): string | number;
    /**
     * Parse the location from EntityType resource.
     *
     * @param {string} entityTypeName
     *   A fully-qualified path representing EntityType resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromEntityTypeName(entityTypeName: string): string | number;
    /**
     * Parse the featurestore from EntityType resource.
     *
     * @param {string} entityTypeName
     *   A fully-qualified path representing EntityType resource.
     * @returns {string} A string representing the featurestore.
     */
    matchFeaturestoreFromEntityTypeName(entityTypeName: string): string | number;
    /**
     * Parse the entity_type from EntityType resource.
     *
     * @param {string} entityTypeName
     *   A fully-qualified path representing EntityType resource.
     * @returns {string} A string representing the entity_type.
     */
    matchEntityTypeFromEntityTypeName(entityTypeName: string): string | number;
    /**
     * Return a fully-qualified execution resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} metadata_store
     * @param {string} execution
     * @returns {string} Resource name string.
     */
    executionPath(project: string, location: string, metadataStore: string, execution: string): string;
    /**
     * Parse the project from Execution resource.
     *
     * @param {string} executionName
     *   A fully-qualified path representing Execution resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromExecutionName(executionName: string): string | number;
    /**
     * Parse the location from Execution resource.
     *
     * @param {string} executionName
     *   A fully-qualified path representing Execution resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromExecutionName(executionName: string): string | number;
    /**
     * Parse the metadata_store from Execution resource.
     *
     * @param {string} executionName
     *   A fully-qualified path representing Execution resource.
     * @returns {string} A string representing the metadata_store.
     */
    matchMetadataStoreFromExecutionName(executionName: string): string | number;
    /**
     * Parse the execution from Execution resource.
     *
     * @param {string} executionName
     *   A fully-qualified path representing Execution resource.
     * @returns {string} A string representing the execution.
     */
    matchExecutionFromExecutionName(executionName: string): string | number;
    /**
     * Return a fully-qualified featureGroup resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} feature_group
     * @returns {string} Resource name string.
     */
    featureGroupPath(project: string, location: string, featureGroup: string): string;
    /**
     * Parse the project from FeatureGroup resource.
     *
     * @param {string} featureGroupName
     *   A fully-qualified path representing FeatureGroup resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromFeatureGroupName(featureGroupName: string): string | number;
    /**
     * Parse the location from FeatureGroup resource.
     *
     * @param {string} featureGroupName
     *   A fully-qualified path representing FeatureGroup resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromFeatureGroupName(featureGroupName: string): string | number;
    /**
     * Parse the feature_group from FeatureGroup resource.
     *
     * @param {string} featureGroupName
     *   A fully-qualified path representing FeatureGroup resource.
     * @returns {string} A string representing the feature_group.
     */
    matchFeatureGroupFromFeatureGroupName(featureGroupName: string): string | number;
    /**
     * Return a fully-qualified featureOnlineStore resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} feature_online_store
     * @returns {string} Resource name string.
     */
    featureOnlineStorePath(project: string, location: string, featureOnlineStore: string): string;
    /**
     * Parse the project from FeatureOnlineStore resource.
     *
     * @param {string} featureOnlineStoreName
     *   A fully-qualified path representing FeatureOnlineStore resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromFeatureOnlineStoreName(featureOnlineStoreName: string): string | number;
    /**
     * Parse the location from FeatureOnlineStore resource.
     *
     * @param {string} featureOnlineStoreName
     *   A fully-qualified path representing FeatureOnlineStore resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromFeatureOnlineStoreName(featureOnlineStoreName: string): string | number;
    /**
     * Parse the feature_online_store from FeatureOnlineStore resource.
     *
     * @param {string} featureOnlineStoreName
     *   A fully-qualified path representing FeatureOnlineStore resource.
     * @returns {string} A string representing the feature_online_store.
     */
    matchFeatureOnlineStoreFromFeatureOnlineStoreName(featureOnlineStoreName: string): string | number;
    /**
     * Return a fully-qualified featureView resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} feature_online_store
     * @param {string} feature_view
     * @returns {string} Resource name string.
     */
    featureViewPath(project: string, location: string, featureOnlineStore: string, featureView: string): string;
    /**
     * Parse the project from FeatureView resource.
     *
     * @param {string} featureViewName
     *   A fully-qualified path representing FeatureView resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromFeatureViewName(featureViewName: string): string | number;
    /**
     * Parse the location from FeatureView resource.
     *
     * @param {string} featureViewName
     *   A fully-qualified path representing FeatureView resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromFeatureViewName(featureViewName: string): string | number;
    /**
     * Parse the feature_online_store from FeatureView resource.
     *
     * @param {string} featureViewName
     *   A fully-qualified path representing FeatureView resource.
     * @returns {string} A string representing the feature_online_store.
     */
    matchFeatureOnlineStoreFromFeatureViewName(featureViewName: string): string | number;
    /**
     * Parse the feature_view from FeatureView resource.
     *
     * @param {string} featureViewName
     *   A fully-qualified path representing FeatureView resource.
     * @returns {string} A string representing the feature_view.
     */
    matchFeatureViewFromFeatureViewName(featureViewName: string): string | number;
    /**
     * Return a fully-qualified featureViewSync resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} feature_online_store
     * @param {string} feature_view
     * @returns {string} Resource name string.
     */
    featureViewSyncPath(project: string, location: string, featureOnlineStore: string, featureView: string): string;
    /**
     * Parse the project from FeatureViewSync resource.
     *
     * @param {string} featureViewSyncName
     *   A fully-qualified path representing FeatureViewSync resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromFeatureViewSyncName(featureViewSyncName: string): string | number;
    /**
     * Parse the location from FeatureViewSync resource.
     *
     * @param {string} featureViewSyncName
     *   A fully-qualified path representing FeatureViewSync resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromFeatureViewSyncName(featureViewSyncName: string): string | number;
    /**
     * Parse the feature_online_store from FeatureViewSync resource.
     *
     * @param {string} featureViewSyncName
     *   A fully-qualified path representing FeatureViewSync resource.
     * @returns {string} A string representing the feature_online_store.
     */
    matchFeatureOnlineStoreFromFeatureViewSyncName(featureViewSyncName: string): string | number;
    /**
     * Parse the feature_view from FeatureViewSync resource.
     *
     * @param {string} featureViewSyncName
     *   A fully-qualified path representing FeatureViewSync resource.
     * @returns {string} A string representing the feature_view.
     */
    matchFeatureViewFromFeatureViewSyncName(featureViewSyncName: string): string | number;
    /**
     * Return a fully-qualified featurestore resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} featurestore
     * @returns {string} Resource name string.
     */
    featurestorePath(project: string, location: string, featurestore: string): string;
    /**
     * Parse the project from Featurestore resource.
     *
     * @param {string} featurestoreName
     *   A fully-qualified path representing Featurestore resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromFeaturestoreName(featurestoreName: string): string | number;
    /**
     * Parse the location from Featurestore resource.
     *
     * @param {string} featurestoreName
     *   A fully-qualified path representing Featurestore resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromFeaturestoreName(featurestoreName: string): string | number;
    /**
     * Parse the featurestore from Featurestore resource.
     *
     * @param {string} featurestoreName
     *   A fully-qualified path representing Featurestore resource.
     * @returns {string} A string representing the featurestore.
     */
    matchFeaturestoreFromFeaturestoreName(featurestoreName: string): string | number;
    /**
     * Return a fully-qualified hyperparameterTuningJob resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} hyperparameter_tuning_job
     * @returns {string} Resource name string.
     */
    hyperparameterTuningJobPath(project: string, location: string, hyperparameterTuningJob: string): string;
    /**
     * Parse the project from HyperparameterTuningJob resource.
     *
     * @param {string} hyperparameterTuningJobName
     *   A fully-qualified path representing HyperparameterTuningJob resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromHyperparameterTuningJobName(hyperparameterTuningJobName: string): string | number;
    /**
     * Parse the location from HyperparameterTuningJob resource.
     *
     * @param {string} hyperparameterTuningJobName
     *   A fully-qualified path representing HyperparameterTuningJob resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromHyperparameterTuningJobName(hyperparameterTuningJobName: string): string | number;
    /**
     * Parse the hyperparameter_tuning_job from HyperparameterTuningJob resource.
     *
     * @param {string} hyperparameterTuningJobName
     *   A fully-qualified path representing HyperparameterTuningJob resource.
     * @returns {string} A string representing the hyperparameter_tuning_job.
     */
    matchHyperparameterTuningJobFromHyperparameterTuningJobName(hyperparameterTuningJobName: string): string | number;
    /**
     * Return a fully-qualified index resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} index
     * @returns {string} Resource name string.
     */
    indexPath(project: string, location: string, index: string): string;
    /**
     * Parse the project from Index resource.
     *
     * @param {string} indexName
     *   A fully-qualified path representing Index resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromIndexName(indexName: string): string | number;
    /**
     * Parse the location from Index resource.
     *
     * @param {string} indexName
     *   A fully-qualified path representing Index resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromIndexName(indexName: string): string | number;
    /**
     * Parse the index from Index resource.
     *
     * @param {string} indexName
     *   A fully-qualified path representing Index resource.
     * @returns {string} A string representing the index.
     */
    matchIndexFromIndexName(indexName: string): string | number;
    /**
     * Return a fully-qualified indexEndpoint resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} index_endpoint
     * @returns {string} Resource name string.
     */
    indexEndpointPath(project: string, location: string, indexEndpoint: string): string;
    /**
     * Parse the project from IndexEndpoint resource.
     *
     * @param {string} indexEndpointName
     *   A fully-qualified path representing IndexEndpoint resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromIndexEndpointName(indexEndpointName: string): string | number;
    /**
     * Parse the location from IndexEndpoint resource.
     *
     * @param {string} indexEndpointName
     *   A fully-qualified path representing IndexEndpoint resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromIndexEndpointName(indexEndpointName: string): string | number;
    /**
     * Parse the index_endpoint from IndexEndpoint resource.
     *
     * @param {string} indexEndpointName
     *   A fully-qualified path representing IndexEndpoint resource.
     * @returns {string} A string representing the index_endpoint.
     */
    matchIndexEndpointFromIndexEndpointName(indexEndpointName: string): string | number;
    /**
     * Return a fully-qualified location resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @returns {string} Resource name string.
     */
    locationPath(project: string, location: string): string;
    /**
     * Parse the project from Location resource.
     *
     * @param {string} locationName
     *   A fully-qualified path representing Location resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromLocationName(locationName: string): string | number;
    /**
     * Parse the location from Location resource.
     *
     * @param {string} locationName
     *   A fully-qualified path representing Location resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromLocationName(locationName: string): string | number;
    /**
     * Return a fully-qualified metadataSchema resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} metadata_store
     * @param {string} metadata_schema
     * @returns {string} Resource name string.
     */
    metadataSchemaPath(project: string, location: string, metadataStore: string, metadataSchema: string): string;
    /**
     * Parse the project from MetadataSchema resource.
     *
     * @param {string} metadataSchemaName
     *   A fully-qualified path representing MetadataSchema resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromMetadataSchemaName(metadataSchemaName: string): string | number;
    /**
     * Parse the location from MetadataSchema resource.
     *
     * @param {string} metadataSchemaName
     *   A fully-qualified path representing MetadataSchema resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromMetadataSchemaName(metadataSchemaName: string): string | number;
    /**
     * Parse the metadata_store from MetadataSchema resource.
     *
     * @param {string} metadataSchemaName
     *   A fully-qualified path representing MetadataSchema resource.
     * @returns {string} A string representing the metadata_store.
     */
    matchMetadataStoreFromMetadataSchemaName(metadataSchemaName: string): string | number;
    /**
     * Parse the metadata_schema from MetadataSchema resource.
     *
     * @param {string} metadataSchemaName
     *   A fully-qualified path representing MetadataSchema resource.
     * @returns {string} A string representing the metadata_schema.
     */
    matchMetadataSchemaFromMetadataSchemaName(metadataSchemaName: string): string | number;
    /**
     * Return a fully-qualified metadataStore resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} metadata_store
     * @returns {string} Resource name string.
     */
    metadataStorePath(project: string, location: string, metadataStore: string): string;
    /**
     * Parse the project from MetadataStore resource.
     *
     * @param {string} metadataStoreName
     *   A fully-qualified path representing MetadataStore resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromMetadataStoreName(metadataStoreName: string): string | number;
    /**
     * Parse the location from MetadataStore resource.
     *
     * @param {string} metadataStoreName
     *   A fully-qualified path representing MetadataStore resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromMetadataStoreName(metadataStoreName: string): string | number;
    /**
     * Parse the metadata_store from MetadataStore resource.
     *
     * @param {string} metadataStoreName
     *   A fully-qualified path representing MetadataStore resource.
     * @returns {string} A string representing the metadata_store.
     */
    matchMetadataStoreFromMetadataStoreName(metadataStoreName: string): string | number;
    /**
     * Return a fully-qualified model resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} model
     * @returns {string} Resource name string.
     */
    modelPath(project: string, location: string, model: string): string;
    /**
     * Parse the project from Model resource.
     *
     * @param {string} modelName
     *   A fully-qualified path representing Model resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromModelName(modelName: string): string | number;
    /**
     * Parse the location from Model resource.
     *
     * @param {string} modelName
     *   A fully-qualified path representing Model resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromModelName(modelName: string): string | number;
    /**
     * Parse the model from Model resource.
     *
     * @param {string} modelName
     *   A fully-qualified path representing Model resource.
     * @returns {string} A string representing the model.
     */
    matchModelFromModelName(modelName: string): string | number;
    /**
     * Return a fully-qualified modelDeploymentMonitoringJob resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} model_deployment_monitoring_job
     * @returns {string} Resource name string.
     */
    modelDeploymentMonitoringJobPath(project: string, location: string, modelDeploymentMonitoringJob: string): string;
    /**
     * Parse the project from ModelDeploymentMonitoringJob resource.
     *
     * @param {string} modelDeploymentMonitoringJobName
     *   A fully-qualified path representing ModelDeploymentMonitoringJob resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromModelDeploymentMonitoringJobName(modelDeploymentMonitoringJobName: string): string | number;
    /**
     * Parse the location from ModelDeploymentMonitoringJob resource.
     *
     * @param {string} modelDeploymentMonitoringJobName
     *   A fully-qualified path representing ModelDeploymentMonitoringJob resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromModelDeploymentMonitoringJobName(modelDeploymentMonitoringJobName: string): string | number;
    /**
     * Parse the model_deployment_monitoring_job from ModelDeploymentMonitoringJob resource.
     *
     * @param {string} modelDeploymentMonitoringJobName
     *   A fully-qualified path representing ModelDeploymentMonitoringJob resource.
     * @returns {string} A string representing the model_deployment_monitoring_job.
     */
    matchModelDeploymentMonitoringJobFromModelDeploymentMonitoringJobName(modelDeploymentMonitoringJobName: string): string | number;
    /**
     * Return a fully-qualified modelEvaluation resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} model
     * @param {string} evaluation
     * @returns {string} Resource name string.
     */
    modelEvaluationPath(project: string, location: string, model: string, evaluation: string): string;
    /**
     * Parse the project from ModelEvaluation resource.
     *
     * @param {string} modelEvaluationName
     *   A fully-qualified path representing ModelEvaluation resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromModelEvaluationName(modelEvaluationName: string): string | number;
    /**
     * Parse the location from ModelEvaluation resource.
     *
     * @param {string} modelEvaluationName
     *   A fully-qualified path representing ModelEvaluation resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromModelEvaluationName(modelEvaluationName: string): string | number;
    /**
     * Parse the model from ModelEvaluation resource.
     *
     * @param {string} modelEvaluationName
     *   A fully-qualified path representing ModelEvaluation resource.
     * @returns {string} A string representing the model.
     */
    matchModelFromModelEvaluationName(modelEvaluationName: string): string | number;
    /**
     * Parse the evaluation from ModelEvaluation resource.
     *
     * @param {string} modelEvaluationName
     *   A fully-qualified path representing ModelEvaluation resource.
     * @returns {string} A string representing the evaluation.
     */
    matchEvaluationFromModelEvaluationName(modelEvaluationName: string): string | number;
    /**
     * Return a fully-qualified modelEvaluationSlice resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} model
     * @param {string} evaluation
     * @param {string} slice
     * @returns {string} Resource name string.
     */
    modelEvaluationSlicePath(project: string, location: string, model: string, evaluation: string, slice: string): string;
    /**
     * Parse the project from ModelEvaluationSlice resource.
     *
     * @param {string} modelEvaluationSliceName
     *   A fully-qualified path representing ModelEvaluationSlice resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromModelEvaluationSliceName(modelEvaluationSliceName: string): string | number;
    /**
     * Parse the location from ModelEvaluationSlice resource.
     *
     * @param {string} modelEvaluationSliceName
     *   A fully-qualified path representing ModelEvaluationSlice resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromModelEvaluationSliceName(modelEvaluationSliceName: string): string | number;
    /**
     * Parse the model from ModelEvaluationSlice resource.
     *
     * @param {string} modelEvaluationSliceName
     *   A fully-qualified path representing ModelEvaluationSlice resource.
     * @returns {string} A string representing the model.
     */
    matchModelFromModelEvaluationSliceName(modelEvaluationSliceName: string): string | number;
    /**
     * Parse the evaluation from ModelEvaluationSlice resource.
     *
     * @param {string} modelEvaluationSliceName
     *   A fully-qualified path representing ModelEvaluationSlice resource.
     * @returns {string} A string representing the evaluation.
     */
    matchEvaluationFromModelEvaluationSliceName(modelEvaluationSliceName: string): string | number;
    /**
     * Parse the slice from ModelEvaluationSlice resource.
     *
     * @param {string} modelEvaluationSliceName
     *   A fully-qualified path representing ModelEvaluationSlice resource.
     * @returns {string} A string representing the slice.
     */
    matchSliceFromModelEvaluationSliceName(modelEvaluationSliceName: string): string | number;
    /**
     * Return a fully-qualified nasJob resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} nas_job
     * @returns {string} Resource name string.
     */
    nasJobPath(project: string, location: string, nasJob: string): string;
    /**
     * Parse the project from NasJob resource.
     *
     * @param {string} nasJobName
     *   A fully-qualified path representing NasJob resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromNasJobName(nasJobName: string): string | number;
    /**
     * Parse the location from NasJob resource.
     *
     * @param {string} nasJobName
     *   A fully-qualified path representing NasJob resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromNasJobName(nasJobName: string): string | number;
    /**
     * Parse the nas_job from NasJob resource.
     *
     * @param {string} nasJobName
     *   A fully-qualified path representing NasJob resource.
     * @returns {string} A string representing the nas_job.
     */
    matchNasJobFromNasJobName(nasJobName: string): string | number;
    /**
     * Return a fully-qualified nasTrialDetail resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} nas_job
     * @param {string} nas_trial_detail
     * @returns {string} Resource name string.
     */
    nasTrialDetailPath(project: string, location: string, nasJob: string, nasTrialDetail: string): string;
    /**
     * Parse the project from NasTrialDetail resource.
     *
     * @param {string} nasTrialDetailName
     *   A fully-qualified path representing NasTrialDetail resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromNasTrialDetailName(nasTrialDetailName: string): string | number;
    /**
     * Parse the location from NasTrialDetail resource.
     *
     * @param {string} nasTrialDetailName
     *   A fully-qualified path representing NasTrialDetail resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromNasTrialDetailName(nasTrialDetailName: string): string | number;
    /**
     * Parse the nas_job from NasTrialDetail resource.
     *
     * @param {string} nasTrialDetailName
     *   A fully-qualified path representing NasTrialDetail resource.
     * @returns {string} A string representing the nas_job.
     */
    matchNasJobFromNasTrialDetailName(nasTrialDetailName: string): string | number;
    /**
     * Parse the nas_trial_detail from NasTrialDetail resource.
     *
     * @param {string} nasTrialDetailName
     *   A fully-qualified path representing NasTrialDetail resource.
     * @returns {string} A string representing the nas_trial_detail.
     */
    matchNasTrialDetailFromNasTrialDetailName(nasTrialDetailName: string): string | number;
    /**
     * Return a fully-qualified notebookExecutionJob resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} notebook_execution_job
     * @returns {string} Resource name string.
     */
    notebookExecutionJobPath(project: string, location: string, notebookExecutionJob: string): string;
    /**
     * Parse the project from NotebookExecutionJob resource.
     *
     * @param {string} notebookExecutionJobName
     *   A fully-qualified path representing NotebookExecutionJob resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromNotebookExecutionJobName(notebookExecutionJobName: string): string | number;
    /**
     * Parse the location from NotebookExecutionJob resource.
     *
     * @param {string} notebookExecutionJobName
     *   A fully-qualified path representing NotebookExecutionJob resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromNotebookExecutionJobName(notebookExecutionJobName: string): string | number;
    /**
     * Parse the notebook_execution_job from NotebookExecutionJob resource.
     *
     * @param {string} notebookExecutionJobName
     *   A fully-qualified path representing NotebookExecutionJob resource.
     * @returns {string} A string representing the notebook_execution_job.
     */
    matchNotebookExecutionJobFromNotebookExecutionJobName(notebookExecutionJobName: string): string | number;
    /**
     * Return a fully-qualified notebookRuntime resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} notebook_runtime
     * @returns {string} Resource name string.
     */
    notebookRuntimePath(project: string, location: string, notebookRuntime: string): string;
    /**
     * Parse the project from NotebookRuntime resource.
     *
     * @param {string} notebookRuntimeName
     *   A fully-qualified path representing NotebookRuntime resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromNotebookRuntimeName(notebookRuntimeName: string): string | number;
    /**
     * Parse the location from NotebookRuntime resource.
     *
     * @param {string} notebookRuntimeName
     *   A fully-qualified path representing NotebookRuntime resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromNotebookRuntimeName(notebookRuntimeName: string): string | number;
    /**
     * Parse the notebook_runtime from NotebookRuntime resource.
     *
     * @param {string} notebookRuntimeName
     *   A fully-qualified path representing NotebookRuntime resource.
     * @returns {string} A string representing the notebook_runtime.
     */
    matchNotebookRuntimeFromNotebookRuntimeName(notebookRuntimeName: string): string | number;
    /**
     * Return a fully-qualified notebookRuntimeTemplate resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} notebook_runtime_template
     * @returns {string} Resource name string.
     */
    notebookRuntimeTemplatePath(project: string, location: string, notebookRuntimeTemplate: string): string;
    /**
     * Parse the project from NotebookRuntimeTemplate resource.
     *
     * @param {string} notebookRuntimeTemplateName
     *   A fully-qualified path representing NotebookRuntimeTemplate resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromNotebookRuntimeTemplateName(notebookRuntimeTemplateName: string): string | number;
    /**
     * Parse the location from NotebookRuntimeTemplate resource.
     *
     * @param {string} notebookRuntimeTemplateName
     *   A fully-qualified path representing NotebookRuntimeTemplate resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromNotebookRuntimeTemplateName(notebookRuntimeTemplateName: string): string | number;
    /**
     * Parse the notebook_runtime_template from NotebookRuntimeTemplate resource.
     *
     * @param {string} notebookRuntimeTemplateName
     *   A fully-qualified path representing NotebookRuntimeTemplate resource.
     * @returns {string} A string representing the notebook_runtime_template.
     */
    matchNotebookRuntimeTemplateFromNotebookRuntimeTemplateName(notebookRuntimeTemplateName: string): string | number;
    /**
     * Return a fully-qualified persistentResource resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} persistent_resource
     * @returns {string} Resource name string.
     */
    persistentResourcePath(project: string, location: string, persistentResource: string): string;
    /**
     * Parse the project from PersistentResource resource.
     *
     * @param {string} persistentResourceName
     *   A fully-qualified path representing PersistentResource resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromPersistentResourceName(persistentResourceName: string): string | number;
    /**
     * Parse the location from PersistentResource resource.
     *
     * @param {string} persistentResourceName
     *   A fully-qualified path representing PersistentResource resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromPersistentResourceName(persistentResourceName: string): string | number;
    /**
     * Parse the persistent_resource from PersistentResource resource.
     *
     * @param {string} persistentResourceName
     *   A fully-qualified path representing PersistentResource resource.
     * @returns {string} A string representing the persistent_resource.
     */
    matchPersistentResourceFromPersistentResourceName(persistentResourceName: string): string | number;
    /**
     * Return a fully-qualified pipelineJob resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} pipeline_job
     * @returns {string} Resource name string.
     */
    pipelineJobPath(project: string, location: string, pipelineJob: string): string;
    /**
     * Parse the project from PipelineJob resource.
     *
     * @param {string} pipelineJobName
     *   A fully-qualified path representing PipelineJob resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromPipelineJobName(pipelineJobName: string): string | number;
    /**
     * Parse the location from PipelineJob resource.
     *
     * @param {string} pipelineJobName
     *   A fully-qualified path representing PipelineJob resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromPipelineJobName(pipelineJobName: string): string | number;
    /**
     * Parse the pipeline_job from PipelineJob resource.
     *
     * @param {string} pipelineJobName
     *   A fully-qualified path representing PipelineJob resource.
     * @returns {string} A string representing the pipeline_job.
     */
    matchPipelineJobFromPipelineJobName(pipelineJobName: string): string | number;
    /**
     * Return a fully-qualified project resource name string.
     *
     * @param {string} project
     * @returns {string} Resource name string.
     */
    projectPath(project: string): string;
    /**
     * Parse the project from Project resource.
     *
     * @param {string} projectName
     *   A fully-qualified path representing Project resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromProjectName(projectName: string): string | number;
    /**
     * Return a fully-qualified projectLocationEndpoint resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} endpoint
     * @returns {string} Resource name string.
     */
    projectLocationEndpointPath(project: string, location: string, endpoint: string): string;
    /**
     * Parse the project from ProjectLocationEndpoint resource.
     *
     * @param {string} projectLocationEndpointName
     *   A fully-qualified path representing project_location_endpoint resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromProjectLocationEndpointName(projectLocationEndpointName: string): string | number;
    /**
     * Parse the location from ProjectLocationEndpoint resource.
     *
     * @param {string} projectLocationEndpointName
     *   A fully-qualified path representing project_location_endpoint resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromProjectLocationEndpointName(projectLocationEndpointName: string): string | number;
    /**
     * Parse the endpoint from ProjectLocationEndpoint resource.
     *
     * @param {string} projectLocationEndpointName
     *   A fully-qualified path representing project_location_endpoint resource.
     * @returns {string} A string representing the endpoint.
     */
    matchEndpointFromProjectLocationEndpointName(projectLocationEndpointName: string): string | number;
    /**
     * Return a fully-qualified projectLocationFeatureGroupFeature resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} feature_group
     * @param {string} feature
     * @returns {string} Resource name string.
     */
    projectLocationFeatureGroupFeaturePath(project: string, location: string, featureGroup: string, feature: string): string;
    /**
     * Parse the project from ProjectLocationFeatureGroupFeature resource.
     *
     * @param {string} projectLocationFeatureGroupFeatureName
     *   A fully-qualified path representing project_location_feature_group_feature resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromProjectLocationFeatureGroupFeatureName(projectLocationFeatureGroupFeatureName: string): string | number;
    /**
     * Parse the location from ProjectLocationFeatureGroupFeature resource.
     *
     * @param {string} projectLocationFeatureGroupFeatureName
     *   A fully-qualified path representing project_location_feature_group_feature resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromProjectLocationFeatureGroupFeatureName(projectLocationFeatureGroupFeatureName: string): string | number;
    /**
     * Parse the feature_group from ProjectLocationFeatureGroupFeature resource.
     *
     * @param {string} projectLocationFeatureGroupFeatureName
     *   A fully-qualified path representing project_location_feature_group_feature resource.
     * @returns {string} A string representing the feature_group.
     */
    matchFeatureGroupFromProjectLocationFeatureGroupFeatureName(projectLocationFeatureGroupFeatureName: string): string | number;
    /**
     * Parse the feature from ProjectLocationFeatureGroupFeature resource.
     *
     * @param {string} projectLocationFeatureGroupFeatureName
     *   A fully-qualified path representing project_location_feature_group_feature resource.
     * @returns {string} A string representing the feature.
     */
    matchFeatureFromProjectLocationFeatureGroupFeatureName(projectLocationFeatureGroupFeatureName: string): string | number;
    /**
     * Return a fully-qualified projectLocationFeaturestoreEntityTypeFeature resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} featurestore
     * @param {string} entity_type
     * @param {string} feature
     * @returns {string} Resource name string.
     */
    projectLocationFeaturestoreEntityTypeFeaturePath(project: string, location: string, featurestore: string, entityType: string, feature: string): string;
    /**
     * Parse the project from ProjectLocationFeaturestoreEntityTypeFeature resource.
     *
     * @param {string} projectLocationFeaturestoreEntityTypeFeatureName
     *   A fully-qualified path representing project_location_featurestore_entity_type_feature resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromProjectLocationFeaturestoreEntityTypeFeatureName(projectLocationFeaturestoreEntityTypeFeatureName: string): string | number;
    /**
     * Parse the location from ProjectLocationFeaturestoreEntityTypeFeature resource.
     *
     * @param {string} projectLocationFeaturestoreEntityTypeFeatureName
     *   A fully-qualified path representing project_location_featurestore_entity_type_feature resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromProjectLocationFeaturestoreEntityTypeFeatureName(projectLocationFeaturestoreEntityTypeFeatureName: string): string | number;
    /**
     * Parse the featurestore from ProjectLocationFeaturestoreEntityTypeFeature resource.
     *
     * @param {string} projectLocationFeaturestoreEntityTypeFeatureName
     *   A fully-qualified path representing project_location_featurestore_entity_type_feature resource.
     * @returns {string} A string representing the featurestore.
     */
    matchFeaturestoreFromProjectLocationFeaturestoreEntityTypeFeatureName(projectLocationFeaturestoreEntityTypeFeatureName: string): string | number;
    /**
     * Parse the entity_type from ProjectLocationFeaturestoreEntityTypeFeature resource.
     *
     * @param {string} projectLocationFeaturestoreEntityTypeFeatureName
     *   A fully-qualified path representing project_location_featurestore_entity_type_feature resource.
     * @returns {string} A string representing the entity_type.
     */
    matchEntityTypeFromProjectLocationFeaturestoreEntityTypeFeatureName(projectLocationFeaturestoreEntityTypeFeatureName: string): string | number;
    /**
     * Parse the feature from ProjectLocationFeaturestoreEntityTypeFeature resource.
     *
     * @param {string} projectLocationFeaturestoreEntityTypeFeatureName
     *   A fully-qualified path representing project_location_featurestore_entity_type_feature resource.
     * @returns {string} A string representing the feature.
     */
    matchFeatureFromProjectLocationFeaturestoreEntityTypeFeatureName(projectLocationFeaturestoreEntityTypeFeatureName: string): string | number;
    /**
     * Return a fully-qualified projectLocationPublisherModel resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} publisher
     * @param {string} model
     * @returns {string} Resource name string.
     */
    projectLocationPublisherModelPath(project: string, location: string, publisher: string, model: string): string;
    /**
     * Parse the project from ProjectLocationPublisherModel resource.
     *
     * @param {string} projectLocationPublisherModelName
     *   A fully-qualified path representing project_location_publisher_model resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromProjectLocationPublisherModelName(projectLocationPublisherModelName: string): string | number;
    /**
     * Parse the location from ProjectLocationPublisherModel resource.
     *
     * @param {string} projectLocationPublisherModelName
     *   A fully-qualified path representing project_location_publisher_model resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromProjectLocationPublisherModelName(projectLocationPublisherModelName: string): string | number;
    /**
     * Parse the publisher from ProjectLocationPublisherModel resource.
     *
     * @param {string} projectLocationPublisherModelName
     *   A fully-qualified path representing project_location_publisher_model resource.
     * @returns {string} A string representing the publisher.
     */
    matchPublisherFromProjectLocationPublisherModelName(projectLocationPublisherModelName: string): string | number;
    /**
     * Parse the model from ProjectLocationPublisherModel resource.
     *
     * @param {string} projectLocationPublisherModelName
     *   A fully-qualified path representing project_location_publisher_model resource.
     * @returns {string} A string representing the model.
     */
    matchModelFromProjectLocationPublisherModelName(projectLocationPublisherModelName: string): string | number;
    /**
     * Return a fully-qualified publisherModel resource name string.
     *
     * @param {string} publisher
     * @param {string} model
     * @returns {string} Resource name string.
     */
    publisherModelPath(publisher: string, model: string): string;
    /**
     * Parse the publisher from PublisherModel resource.
     *
     * @param {string} publisherModelName
     *   A fully-qualified path representing PublisherModel resource.
     * @returns {string} A string representing the publisher.
     */
    matchPublisherFromPublisherModelName(publisherModelName: string): string | number;
    /**
     * Parse the model from PublisherModel resource.
     *
     * @param {string} publisherModelName
     *   A fully-qualified path representing PublisherModel resource.
     * @returns {string} A string representing the model.
     */
    matchModelFromPublisherModelName(publisherModelName: string): string | number;
    /**
     * Return a fully-qualified ragCorpus resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} rag_corpus
     * @returns {string} Resource name string.
     */
    ragCorpusPath(project: string, location: string, ragCorpus: string): string;
    /**
     * Parse the project from RagCorpus resource.
     *
     * @param {string} ragCorpusName
     *   A fully-qualified path representing RagCorpus resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromRagCorpusName(ragCorpusName: string): string | number;
    /**
     * Parse the location from RagCorpus resource.
     *
     * @param {string} ragCorpusName
     *   A fully-qualified path representing RagCorpus resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromRagCorpusName(ragCorpusName: string): string | number;
    /**
     * Parse the rag_corpus from RagCorpus resource.
     *
     * @param {string} ragCorpusName
     *   A fully-qualified path representing RagCorpus resource.
     * @returns {string} A string representing the rag_corpus.
     */
    matchRagCorpusFromRagCorpusName(ragCorpusName: string): string | number;
    /**
     * Return a fully-qualified ragFile resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} rag_corpus
     * @param {string} rag_file
     * @returns {string} Resource name string.
     */
    ragFilePath(project: string, location: string, ragCorpus: string, ragFile: string): string;
    /**
     * Parse the project from RagFile resource.
     *
     * @param {string} ragFileName
     *   A fully-qualified path representing RagFile resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromRagFileName(ragFileName: string): string | number;
    /**
     * Parse the location from RagFile resource.
     *
     * @param {string} ragFileName
     *   A fully-qualified path representing RagFile resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromRagFileName(ragFileName: string): string | number;
    /**
     * Parse the rag_corpus from RagFile resource.
     *
     * @param {string} ragFileName
     *   A fully-qualified path representing RagFile resource.
     * @returns {string} A string representing the rag_corpus.
     */
    matchRagCorpusFromRagFileName(ragFileName: string): string | number;
    /**
     * Parse the rag_file from RagFile resource.
     *
     * @param {string} ragFileName
     *   A fully-qualified path representing RagFile resource.
     * @returns {string} A string representing the rag_file.
     */
    matchRagFileFromRagFileName(ragFileName: string): string | number;
    /**
     * Return a fully-qualified reasoningEngine resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} reasoning_engine
     * @returns {string} Resource name string.
     */
    reasoningEnginePath(project: string, location: string, reasoningEngine: string): string;
    /**
     * Parse the project from ReasoningEngine resource.
     *
     * @param {string} reasoningEngineName
     *   A fully-qualified path representing ReasoningEngine resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromReasoningEngineName(reasoningEngineName: string): string | number;
    /**
     * Parse the location from ReasoningEngine resource.
     *
     * @param {string} reasoningEngineName
     *   A fully-qualified path representing ReasoningEngine resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromReasoningEngineName(reasoningEngineName: string): string | number;
    /**
     * Parse the reasoning_engine from ReasoningEngine resource.
     *
     * @param {string} reasoningEngineName
     *   A fully-qualified path representing ReasoningEngine resource.
     * @returns {string} A string representing the reasoning_engine.
     */
    matchReasoningEngineFromReasoningEngineName(reasoningEngineName: string): string | number;
    /**
     * Return a fully-qualified savedQuery resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} dataset
     * @param {string} saved_query
     * @returns {string} Resource name string.
     */
    savedQueryPath(project: string, location: string, dataset: string, savedQuery: string): string;
    /**
     * Parse the project from SavedQuery resource.
     *
     * @param {string} savedQueryName
     *   A fully-qualified path representing SavedQuery resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromSavedQueryName(savedQueryName: string): string | number;
    /**
     * Parse the location from SavedQuery resource.
     *
     * @param {string} savedQueryName
     *   A fully-qualified path representing SavedQuery resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromSavedQueryName(savedQueryName: string): string | number;
    /**
     * Parse the dataset from SavedQuery resource.
     *
     * @param {string} savedQueryName
     *   A fully-qualified path representing SavedQuery resource.
     * @returns {string} A string representing the dataset.
     */
    matchDatasetFromSavedQueryName(savedQueryName: string): string | number;
    /**
     * Parse the saved_query from SavedQuery resource.
     *
     * @param {string} savedQueryName
     *   A fully-qualified path representing SavedQuery resource.
     * @returns {string} A string representing the saved_query.
     */
    matchSavedQueryFromSavedQueryName(savedQueryName: string): string | number;
    /**
     * Return a fully-qualified schedule resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} schedule
     * @returns {string} Resource name string.
     */
    schedulePath(project: string, location: string, schedule: string): string;
    /**
     * Parse the project from Schedule resource.
     *
     * @param {string} scheduleName
     *   A fully-qualified path representing Schedule resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromScheduleName(scheduleName: string): string | number;
    /**
     * Parse the location from Schedule resource.
     *
     * @param {string} scheduleName
     *   A fully-qualified path representing Schedule resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromScheduleName(scheduleName: string): string | number;
    /**
     * Parse the schedule from Schedule resource.
     *
     * @param {string} scheduleName
     *   A fully-qualified path representing Schedule resource.
     * @returns {string} A string representing the schedule.
     */
    matchScheduleFromScheduleName(scheduleName: string): string | number;
    /**
     * Return a fully-qualified specialistPool resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} specialist_pool
     * @returns {string} Resource name string.
     */
    specialistPoolPath(project: string, location: string, specialistPool: string): string;
    /**
     * Parse the project from SpecialistPool resource.
     *
     * @param {string} specialistPoolName
     *   A fully-qualified path representing SpecialistPool resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromSpecialistPoolName(specialistPoolName: string): string | number;
    /**
     * Parse the location from SpecialistPool resource.
     *
     * @param {string} specialistPoolName
     *   A fully-qualified path representing SpecialistPool resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromSpecialistPoolName(specialistPoolName: string): string | number;
    /**
     * Parse the specialist_pool from SpecialistPool resource.
     *
     * @param {string} specialistPoolName
     *   A fully-qualified path representing SpecialistPool resource.
     * @returns {string} A string representing the specialist_pool.
     */
    matchSpecialistPoolFromSpecialistPoolName(specialistPoolName: string): string | number;
    /**
     * Return a fully-qualified study resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} study
     * @returns {string} Resource name string.
     */
    studyPath(project: string, location: string, study: string): string;
    /**
     * Parse the project from Study resource.
     *
     * @param {string} studyName
     *   A fully-qualified path representing Study resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromStudyName(studyName: string): string | number;
    /**
     * Parse the location from Study resource.
     *
     * @param {string} studyName
     *   A fully-qualified path representing Study resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromStudyName(studyName: string): string | number;
    /**
     * Parse the study from Study resource.
     *
     * @param {string} studyName
     *   A fully-qualified path representing Study resource.
     * @returns {string} A string representing the study.
     */
    matchStudyFromStudyName(studyName: string): string | number;
    /**
     * Return a fully-qualified tensorboard resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} tensorboard
     * @returns {string} Resource name string.
     */
    tensorboardPath(project: string, location: string, tensorboard: string): string;
    /**
     * Parse the project from Tensorboard resource.
     *
     * @param {string} tensorboardName
     *   A fully-qualified path representing Tensorboard resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromTensorboardName(tensorboardName: string): string | number;
    /**
     * Parse the location from Tensorboard resource.
     *
     * @param {string} tensorboardName
     *   A fully-qualified path representing Tensorboard resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromTensorboardName(tensorboardName: string): string | number;
    /**
     * Parse the tensorboard from Tensorboard resource.
     *
     * @param {string} tensorboardName
     *   A fully-qualified path representing Tensorboard resource.
     * @returns {string} A string representing the tensorboard.
     */
    matchTensorboardFromTensorboardName(tensorboardName: string): string | number;
    /**
     * Return a fully-qualified tensorboardExperiment resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} tensorboard
     * @param {string} experiment
     * @returns {string} Resource name string.
     */
    tensorboardExperimentPath(project: string, location: string, tensorboard: string, experiment: string): string;
    /**
     * Parse the project from TensorboardExperiment resource.
     *
     * @param {string} tensorboardExperimentName
     *   A fully-qualified path representing TensorboardExperiment resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromTensorboardExperimentName(tensorboardExperimentName: string): string | number;
    /**
     * Parse the location from TensorboardExperiment resource.
     *
     * @param {string} tensorboardExperimentName
     *   A fully-qualified path representing TensorboardExperiment resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromTensorboardExperimentName(tensorboardExperimentName: string): string | number;
    /**
     * Parse the tensorboard from TensorboardExperiment resource.
     *
     * @param {string} tensorboardExperimentName
     *   A fully-qualified path representing TensorboardExperiment resource.
     * @returns {string} A string representing the tensorboard.
     */
    matchTensorboardFromTensorboardExperimentName(tensorboardExperimentName: string): string | number;
    /**
     * Parse the experiment from TensorboardExperiment resource.
     *
     * @param {string} tensorboardExperimentName
     *   A fully-qualified path representing TensorboardExperiment resource.
     * @returns {string} A string representing the experiment.
     */
    matchExperimentFromTensorboardExperimentName(tensorboardExperimentName: string): string | number;
    /**
     * Return a fully-qualified tensorboardRun resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} tensorboard
     * @param {string} experiment
     * @param {string} run
     * @returns {string} Resource name string.
     */
    tensorboardRunPath(project: string, location: string, tensorboard: string, experiment: string, run: string): string;
    /**
     * Parse the project from TensorboardRun resource.
     *
     * @param {string} tensorboardRunName
     *   A fully-qualified path representing TensorboardRun resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromTensorboardRunName(tensorboardRunName: string): string | number;
    /**
     * Parse the location from TensorboardRun resource.
     *
     * @param {string} tensorboardRunName
     *   A fully-qualified path representing TensorboardRun resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromTensorboardRunName(tensorboardRunName: string): string | number;
    /**
     * Parse the tensorboard from TensorboardRun resource.
     *
     * @param {string} tensorboardRunName
     *   A fully-qualified path representing TensorboardRun resource.
     * @returns {string} A string representing the tensorboard.
     */
    matchTensorboardFromTensorboardRunName(tensorboardRunName: string): string | number;
    /**
     * Parse the experiment from TensorboardRun resource.
     *
     * @param {string} tensorboardRunName
     *   A fully-qualified path representing TensorboardRun resource.
     * @returns {string} A string representing the experiment.
     */
    matchExperimentFromTensorboardRunName(tensorboardRunName: string): string | number;
    /**
     * Parse the run from TensorboardRun resource.
     *
     * @param {string} tensorboardRunName
     *   A fully-qualified path representing TensorboardRun resource.
     * @returns {string} A string representing the run.
     */
    matchRunFromTensorboardRunName(tensorboardRunName: string): string | number;
    /**
     * Return a fully-qualified tensorboardTimeSeries resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} tensorboard
     * @param {string} experiment
     * @param {string} run
     * @param {string} time_series
     * @returns {string} Resource name string.
     */
    tensorboardTimeSeriesPath(project: string, location: string, tensorboard: string, experiment: string, run: string, timeSeries: string): string;
    /**
     * Parse the project from TensorboardTimeSeries resource.
     *
     * @param {string} tensorboardTimeSeriesName
     *   A fully-qualified path representing TensorboardTimeSeries resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromTensorboardTimeSeriesName(tensorboardTimeSeriesName: string): string | number;
    /**
     * Parse the location from TensorboardTimeSeries resource.
     *
     * @param {string} tensorboardTimeSeriesName
     *   A fully-qualified path representing TensorboardTimeSeries resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromTensorboardTimeSeriesName(tensorboardTimeSeriesName: string): string | number;
    /**
     * Parse the tensorboard from TensorboardTimeSeries resource.
     *
     * @param {string} tensorboardTimeSeriesName
     *   A fully-qualified path representing TensorboardTimeSeries resource.
     * @returns {string} A string representing the tensorboard.
     */
    matchTensorboardFromTensorboardTimeSeriesName(tensorboardTimeSeriesName: string): string | number;
    /**
     * Parse the experiment from TensorboardTimeSeries resource.
     *
     * @param {string} tensorboardTimeSeriesName
     *   A fully-qualified path representing TensorboardTimeSeries resource.
     * @returns {string} A string representing the experiment.
     */
    matchExperimentFromTensorboardTimeSeriesName(tensorboardTimeSeriesName: string): string | number;
    /**
     * Parse the run from TensorboardTimeSeries resource.
     *
     * @param {string} tensorboardTimeSeriesName
     *   A fully-qualified path representing TensorboardTimeSeries resource.
     * @returns {string} A string representing the run.
     */
    matchRunFromTensorboardTimeSeriesName(tensorboardTimeSeriesName: string): string | number;
    /**
     * Parse the time_series from TensorboardTimeSeries resource.
     *
     * @param {string} tensorboardTimeSeriesName
     *   A fully-qualified path representing TensorboardTimeSeries resource.
     * @returns {string} A string representing the time_series.
     */
    matchTimeSeriesFromTensorboardTimeSeriesName(tensorboardTimeSeriesName: string): string | number;
    /**
     * Return a fully-qualified trainingPipeline resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} training_pipeline
     * @returns {string} Resource name string.
     */
    trainingPipelinePath(project: string, location: string, trainingPipeline: string): string;
    /**
     * Parse the project from TrainingPipeline resource.
     *
     * @param {string} trainingPipelineName
     *   A fully-qualified path representing TrainingPipeline resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromTrainingPipelineName(trainingPipelineName: string): string | number;
    /**
     * Parse the location from TrainingPipeline resource.
     *
     * @param {string} trainingPipelineName
     *   A fully-qualified path representing TrainingPipeline resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromTrainingPipelineName(trainingPipelineName: string): string | number;
    /**
     * Parse the training_pipeline from TrainingPipeline resource.
     *
     * @param {string} trainingPipelineName
     *   A fully-qualified path representing TrainingPipeline resource.
     * @returns {string} A string representing the training_pipeline.
     */
    matchTrainingPipelineFromTrainingPipelineName(trainingPipelineName: string): string | number;
    /**
     * Return a fully-qualified trial resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} study
     * @param {string} trial
     * @returns {string} Resource name string.
     */
    trialPath(project: string, location: string, study: string, trial: string): string;
    /**
     * Parse the project from Trial resource.
     *
     * @param {string} trialName
     *   A fully-qualified path representing Trial resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromTrialName(trialName: string): string | number;
    /**
     * Parse the location from Trial resource.
     *
     * @param {string} trialName
     *   A fully-qualified path representing Trial resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromTrialName(trialName: string): string | number;
    /**
     * Parse the study from Trial resource.
     *
     * @param {string} trialName
     *   A fully-qualified path representing Trial resource.
     * @returns {string} A string representing the study.
     */
    matchStudyFromTrialName(trialName: string): string | number;
    /**
     * Parse the trial from Trial resource.
     *
     * @param {string} trialName
     *   A fully-qualified path representing Trial resource.
     * @returns {string} A string representing the trial.
     */
    matchTrialFromTrialName(trialName: string): string | number;
    /**
     * Return a fully-qualified tuningJob resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} tuning_job
     * @returns {string} Resource name string.
     */
    tuningJobPath(project: string, location: string, tuningJob: string): string;
    /**
     * Parse the project from TuningJob resource.
     *
     * @param {string} tuningJobName
     *   A fully-qualified path representing TuningJob resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromTuningJobName(tuningJobName: string): string | number;
    /**
     * Parse the location from TuningJob resource.
     *
     * @param {string} tuningJobName
     *   A fully-qualified path representing TuningJob resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromTuningJobName(tuningJobName: string): string | number;
    /**
     * Parse the tuning_job from TuningJob resource.
     *
     * @param {string} tuningJobName
     *   A fully-qualified path representing TuningJob resource.
     * @returns {string} A string representing the tuning_job.
     */
    matchTuningJobFromTuningJobName(tuningJobName: string): string | number;
    /**
     * Terminate the gRPC channel and close the client.
     *
     * The client will no longer be usable and all future behavior is undefined.
     * @returns {Promise} A promise that resolves when the client is closed.
     */
    close(): Promise<void>;
}
