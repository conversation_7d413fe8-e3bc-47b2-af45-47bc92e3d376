{"name": "@genkit-ai/googleai", "description": "Genkit AI framework plugin for Google AI APIs, including Gemini APIs.", "keywords": ["genkit", "genkit-plugin", "genkit-embedder", "genkit-model", "gemini", "google gemini", "google ai", "ai", "genai", "generative-ai"], "version": "1.10.0", "type": "commonjs", "repository": {"type": "git", "url": "https://github.com/firebase/genkit.git", "directory": "js/plugins/googleai"}, "author": "genkit", "license": "Apache-2.0", "dependencies": {"@google/generative-ai": "^0.24.0", "google-auth-library": "^9.6.3", "node-fetch": "^3.3.2"}, "peerDependencies": {"genkit": "^1.10.0"}, "devDependencies": {"@types/node": "^20.11.16", "npm-run-all": "^4.1.5", "rimraf": "^6.0.1", "tsup": "^8.3.5", "tsx": "^4.19.2", "typescript": "^4.9.0"}, "types": "./lib/index.d.ts", "exports": {".": {"require": "./lib/index.js", "import": "./lib/index.mjs", "types": "./lib/index.d.ts", "default": "./lib/index.js"}}, "scripts": {"check": "tsc", "compile": "tsup-node", "build:clean": "rimraf ./lib", "build": "npm-run-all build:clean check compile", "build:watch": "tsup-node --watch", "test": "tsx --test ./tests/*_test.ts ./tests/**/*_test.ts"}}