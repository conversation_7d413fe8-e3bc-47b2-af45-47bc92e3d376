{"version": 3, "file": "AlwaysSampleExemplarFilter.js", "sourceRoot": "", "sources": ["../../../src/exemplar/AlwaysSampleExemplarFilter.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAKH;IAAA;IASA,CAAC;IARC,iDAAY,GAAZ,UACE,MAAc,EACd,UAAkB,EAClB,WAA6B,EAC7B,IAAa;QAEb,OAAO,IAAI,CAAC;IACd,CAAC;IACH,iCAAC;AAAD,CAAC,AATD,IASC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Context, HrTime, MetricAttributes } from '@opentelemetry/api';\nimport { ExemplarFilter } from './ExemplarFilter';\n\nexport class AlwaysSampleExemplarFilter implements ExemplarFilter {\n  shouldSample(\n    _value: number,\n    _timestamp: HrTime,\n    _attributes: MetricAttributes,\n    _ctx: Context\n  ): boolean {\n    return true;\n  }\n}\n"]}