{"name": "@google-cloud/vertexai", "description": "Vertex Generative AI client for Node.js", "version": "1.10.0", "license": "Apache-2.0", "author": "Google LLC", "engines": {"node": ">=18.0.0"}, "homepage": "https://github.com/googleapis/nodejs-vertexai", "repository": "googleapis/nodejs-vertexai", "main": "build/src/index.js", "type": "commonjs", "scripts": {"clean": "gts clean", "compile": "tsc -p .", "docs": "jsdoc -c .jsdoc.js", "predocs-test": "npm run docs", "docs-test": "linkinator docs", "compile:oss": "tsc -p tsconfig.json.oss", "fix": "gts fix", "test": "npm run test:src && npm run test:test", "test:test": "jasmine build/test/*.js --reporter=test/spec/reporter.js", "test:src": "jasmine build/src/functions/test/*.js build/src/models/test/*.js --reporter=test/spec/reporter.js", "test:system": "jasmine build/system_test/*.js --reporter=test/spec/reporter.js", "lint": "gts lint", "clean-js-files": "find . -type f -name \"*.js\" -exec rm -f {} +", "clean-js-map-files": "find . -type f -name \"*.js.map\" -exec rm -f {} +", "postpack": "if [ \"${CLEAN}\" ]; then npm run clean-after-pack; fi", "cover": "npm run cover:unit && npm run cover:integration && npm run cover:report", "cover:unit": "nyc npm run test", "cover:integration": "nyc --no-clean npm run test:system", "cover:report": "nyc report --reporter=lcov", "prepare": "npm run compile", "pretest": "npm run compile", "posttest": "npm run lint"}, "dependencies": {"google-auth-library": "^9.1.0"}, "devDependencies": {"@types/jasmine": "^5.1.2", "@types/node": "^20.9.0", "gts": "^5.2.0", "jasmine": "^5.1.0", "jasmine-reporters": "^2.4.0", "jsdoc": "^4.0.0", "jsdoc-fresh": "^3.0.0", "jsdoc-region-tag": "^3.0.0", "linkinator": "^4.0.0", "typescript": "~5.2.0", "nyc": "^15.1.0"}}