import * as aiplatform from "@google-cloud/aiplatform";
import { z } from "genkit";
import { CommonRetrieverOptionsSchema } from "genkit/retriever";
class Datapoint extends aiplatform.protos.google.cloud.aiplatform.v1.IndexDatapoint {
  constructor(properties) {
    super(properties);
  }
}
const SparseEmbeddingSchema = z.object({
  values: z.array(z.number()).optional(),
  dimensions: z.array(z.union([z.number(), z.string()])).optional()
});
const RestrictionSchema = z.object({
  namespace: z.string(),
  allowList: z.array(z.string()),
  denyList: z.array(z.string())
});
const NumericRestrictionOperatorSchema = z.enum([
  "OPERATOR_UNSPECIFIED",
  "LESS",
  "LESS_EQUAL",
  "EQUAL",
  "GREATER_EQUAL",
  "GREATER",
  "NOT_EQUAL"
]);
const NumericRestrictionSchema = z.object({
  valueInt: z.union([z.number(), z.string()]).optional(),
  valueFloat: z.number().optional(),
  valueDouble: z.number().optional(),
  namespace: z.string(),
  op: z.union([NumericRestrictionOperatorSchema, z.null()]).optional()
});
const CrowdingTagSchema = z.object({
  crowdingAttribute: z.string().optional()
});
const IndexDatapointSchema = z.object({
  datapointId: z.string().optional(),
  featureVector: z.array(z.number()).optional(),
  sparseEmbedding: SparseEmbeddingSchema.optional(),
  restricts: z.array(RestrictionSchema).optional(),
  numericRestricts: z.array(NumericRestrictionSchema).optional(),
  crowdingTag: CrowdingTagSchema.optional()
});
const NeighborSchema = z.object({
  datapoint: IndexDatapointSchema.optional(),
  distance: z.number().optional(),
  sparseDistance: z.number().optional()
});
const NearestNeighborsSchema = z.object({
  id: z.string().optional(),
  neighbors: z.array(NeighborSchema).optional()
});
const FindNeighborsResponseSchema = z.object({
  nearestNeighbors: z.array(NearestNeighborsSchema).optional()
});
function assertTypeEquality(value) {
}
assertTypeEquality({});
assertTypeEquality({});
const VertexAIVectorRetrieverOptionsSchema = CommonRetrieverOptionsSchema.extend({}).optional();
const VertexAIVectorIndexerOptionsSchema = z.any();
export {
  CrowdingTagSchema,
  Datapoint,
  FindNeighborsResponseSchema,
  NeighborSchema,
  NumericRestrictionOperatorSchema,
  NumericRestrictionSchema,
  RestrictionSchema,
  SparseEmbeddingSchema,
  VertexAIVectorIndexerOptionsSchema,
  VertexAIVectorRetrieverOptionsSchema
};
//# sourceMappingURL=types.mjs.map