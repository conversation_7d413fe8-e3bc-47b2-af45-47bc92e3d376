"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
var evaluation_exports = {};
__export(evaluation_exports, {
  VertexAIEvaluationMetricType: () => import_types2.VertexAIEvaluationMetricType,
  vertexAIEvaluation: () => vertexAIEvaluation
});
module.exports = __toCommonJS(evaluation_exports);
var import_plugin = require("genkit/plugin");
var import_common = require("../common/index.js");
var import_evaluation = require("./evaluation.js");
var import_types2 = require("./types.js");
function vertexAIEvaluation(options) {
  return (0, import_plugin.genkitPlugin)("vertexAIEvaluation", async (ai) => {
    const { projectId, location, authClient } = await (0, import_common.getDerivedParams)(options);
    (0, import_evaluation.vertexEvaluators)(ai, authClient, options.metrics, projectId, location);
  });
}
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  VertexAIEvaluationMetricType,
  vertexAIEvaluation
});
//# sourceMappingURL=index.js.map