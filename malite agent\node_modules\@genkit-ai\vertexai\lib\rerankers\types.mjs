import { z } from "genkit";
const VertexAIRerankerOptionsSchema = z.object({
  k: z.number().optional().describe("Number of top documents to rerank"),
  // Optional: Number of documents to rerank
  model: z.string().optional().describe("Model name for reranking"),
  // Optional: Model name, defaults to a pre-defined model
  location: z.string().optional().describe('Google Cloud location, e.g., "us-central1"')
  // Optional: Location of the reranking model
});
export {
  VertexAIRerankerOptionsSchema
};
//# sourceMappingURL=types.mjs.map