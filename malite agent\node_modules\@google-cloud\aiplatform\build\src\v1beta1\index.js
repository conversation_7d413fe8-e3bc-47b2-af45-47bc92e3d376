"use strict";
// Copyright 2025 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     https://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//
// ** This file is automatically generated by gapic-generator-typescript. **
// ** https://github.com/googleapis/gapic-generator-typescript **
// ** All changes to this file may be overwritten. **
Object.defineProperty(exports, "__esModule", { value: true });
exports.VizierServiceClient = exports.VertexRagServiceClient = exports.VertexRagDataServiceClient = exports.TensorboardServiceClient = exports.SpecialistPoolServiceClient = exports.ScheduleServiceClient = exports.ReasoningEngineServiceClient = exports.ReasoningEngineExecutionServiceClient = exports.PredictionServiceClient = exports.PipelineServiceClient = exports.PersistentResourceServiceClient = exports.NotebookServiceClient = exports.ModelServiceClient = exports.ModelMonitoringServiceClient = exports.ModelGardenServiceClient = exports.MigrationServiceClient = exports.MetadataServiceClient = exports.MatchServiceClient = exports.LlmUtilityServiceClient = exports.JobServiceClient = exports.IndexServiceClient = exports.IndexEndpointServiceClient = exports.GenAiTuningServiceClient = exports.GenAiCacheServiceClient = exports.FeaturestoreServiceClient = exports.FeaturestoreOnlineServingServiceClient = exports.FeatureRegistryServiceClient = exports.FeatureOnlineStoreServiceClient = exports.FeatureOnlineStoreAdminServiceClient = exports.ExtensionRegistryServiceClient = exports.ExtensionExecutionServiceClient = exports.EvaluationServiceClient = exports.EndpointServiceClient = exports.DeploymentResourcePoolServiceClient = exports.DatasetServiceClient = void 0;
var dataset_service_client_1 = require("./dataset_service_client");
Object.defineProperty(exports, "DatasetServiceClient", { enumerable: true, get: function () { return dataset_service_client_1.DatasetServiceClient; } });
var deployment_resource_pool_service_client_1 = require("./deployment_resource_pool_service_client");
Object.defineProperty(exports, "DeploymentResourcePoolServiceClient", { enumerable: true, get: function () { return deployment_resource_pool_service_client_1.DeploymentResourcePoolServiceClient; } });
var endpoint_service_client_1 = require("./endpoint_service_client");
Object.defineProperty(exports, "EndpointServiceClient", { enumerable: true, get: function () { return endpoint_service_client_1.EndpointServiceClient; } });
var evaluation_service_client_1 = require("./evaluation_service_client");
Object.defineProperty(exports, "EvaluationServiceClient", { enumerable: true, get: function () { return evaluation_service_client_1.EvaluationServiceClient; } });
var extension_execution_service_client_1 = require("./extension_execution_service_client");
Object.defineProperty(exports, "ExtensionExecutionServiceClient", { enumerable: true, get: function () { return extension_execution_service_client_1.ExtensionExecutionServiceClient; } });
var extension_registry_service_client_1 = require("./extension_registry_service_client");
Object.defineProperty(exports, "ExtensionRegistryServiceClient", { enumerable: true, get: function () { return extension_registry_service_client_1.ExtensionRegistryServiceClient; } });
var feature_online_store_admin_service_client_1 = require("./feature_online_store_admin_service_client");
Object.defineProperty(exports, "FeatureOnlineStoreAdminServiceClient", { enumerable: true, get: function () { return feature_online_store_admin_service_client_1.FeatureOnlineStoreAdminServiceClient; } });
var feature_online_store_service_client_1 = require("./feature_online_store_service_client");
Object.defineProperty(exports, "FeatureOnlineStoreServiceClient", { enumerable: true, get: function () { return feature_online_store_service_client_1.FeatureOnlineStoreServiceClient; } });
var feature_registry_service_client_1 = require("./feature_registry_service_client");
Object.defineProperty(exports, "FeatureRegistryServiceClient", { enumerable: true, get: function () { return feature_registry_service_client_1.FeatureRegistryServiceClient; } });
var featurestore_online_serving_service_client_1 = require("./featurestore_online_serving_service_client");
Object.defineProperty(exports, "FeaturestoreOnlineServingServiceClient", { enumerable: true, get: function () { return featurestore_online_serving_service_client_1.FeaturestoreOnlineServingServiceClient; } });
var featurestore_service_client_1 = require("./featurestore_service_client");
Object.defineProperty(exports, "FeaturestoreServiceClient", { enumerable: true, get: function () { return featurestore_service_client_1.FeaturestoreServiceClient; } });
var gen_ai_cache_service_client_1 = require("./gen_ai_cache_service_client");
Object.defineProperty(exports, "GenAiCacheServiceClient", { enumerable: true, get: function () { return gen_ai_cache_service_client_1.GenAiCacheServiceClient; } });
var gen_ai_tuning_service_client_1 = require("./gen_ai_tuning_service_client");
Object.defineProperty(exports, "GenAiTuningServiceClient", { enumerable: true, get: function () { return gen_ai_tuning_service_client_1.GenAiTuningServiceClient; } });
var index_endpoint_service_client_1 = require("./index_endpoint_service_client");
Object.defineProperty(exports, "IndexEndpointServiceClient", { enumerable: true, get: function () { return index_endpoint_service_client_1.IndexEndpointServiceClient; } });
var index_service_client_1 = require("./index_service_client");
Object.defineProperty(exports, "IndexServiceClient", { enumerable: true, get: function () { return index_service_client_1.IndexServiceClient; } });
var job_service_client_1 = require("./job_service_client");
Object.defineProperty(exports, "JobServiceClient", { enumerable: true, get: function () { return job_service_client_1.JobServiceClient; } });
var llm_utility_service_client_1 = require("./llm_utility_service_client");
Object.defineProperty(exports, "LlmUtilityServiceClient", { enumerable: true, get: function () { return llm_utility_service_client_1.LlmUtilityServiceClient; } });
var match_service_client_1 = require("./match_service_client");
Object.defineProperty(exports, "MatchServiceClient", { enumerable: true, get: function () { return match_service_client_1.MatchServiceClient; } });
var metadata_service_client_1 = require("./metadata_service_client");
Object.defineProperty(exports, "MetadataServiceClient", { enumerable: true, get: function () { return metadata_service_client_1.MetadataServiceClient; } });
var migration_service_client_1 = require("./migration_service_client");
Object.defineProperty(exports, "MigrationServiceClient", { enumerable: true, get: function () { return migration_service_client_1.MigrationServiceClient; } });
var model_garden_service_client_1 = require("./model_garden_service_client");
Object.defineProperty(exports, "ModelGardenServiceClient", { enumerable: true, get: function () { return model_garden_service_client_1.ModelGardenServiceClient; } });
var model_monitoring_service_client_1 = require("./model_monitoring_service_client");
Object.defineProperty(exports, "ModelMonitoringServiceClient", { enumerable: true, get: function () { return model_monitoring_service_client_1.ModelMonitoringServiceClient; } });
var model_service_client_1 = require("./model_service_client");
Object.defineProperty(exports, "ModelServiceClient", { enumerable: true, get: function () { return model_service_client_1.ModelServiceClient; } });
var notebook_service_client_1 = require("./notebook_service_client");
Object.defineProperty(exports, "NotebookServiceClient", { enumerable: true, get: function () { return notebook_service_client_1.NotebookServiceClient; } });
var persistent_resource_service_client_1 = require("./persistent_resource_service_client");
Object.defineProperty(exports, "PersistentResourceServiceClient", { enumerable: true, get: function () { return persistent_resource_service_client_1.PersistentResourceServiceClient; } });
var pipeline_service_client_1 = require("./pipeline_service_client");
Object.defineProperty(exports, "PipelineServiceClient", { enumerable: true, get: function () { return pipeline_service_client_1.PipelineServiceClient; } });
var prediction_service_client_1 = require("./prediction_service_client");
Object.defineProperty(exports, "PredictionServiceClient", { enumerable: true, get: function () { return prediction_service_client_1.PredictionServiceClient; } });
var reasoning_engine_execution_service_client_1 = require("./reasoning_engine_execution_service_client");
Object.defineProperty(exports, "ReasoningEngineExecutionServiceClient", { enumerable: true, get: function () { return reasoning_engine_execution_service_client_1.ReasoningEngineExecutionServiceClient; } });
var reasoning_engine_service_client_1 = require("./reasoning_engine_service_client");
Object.defineProperty(exports, "ReasoningEngineServiceClient", { enumerable: true, get: function () { return reasoning_engine_service_client_1.ReasoningEngineServiceClient; } });
var schedule_service_client_1 = require("./schedule_service_client");
Object.defineProperty(exports, "ScheduleServiceClient", { enumerable: true, get: function () { return schedule_service_client_1.ScheduleServiceClient; } });
var specialist_pool_service_client_1 = require("./specialist_pool_service_client");
Object.defineProperty(exports, "SpecialistPoolServiceClient", { enumerable: true, get: function () { return specialist_pool_service_client_1.SpecialistPoolServiceClient; } });
var tensorboard_service_client_1 = require("./tensorboard_service_client");
Object.defineProperty(exports, "TensorboardServiceClient", { enumerable: true, get: function () { return tensorboard_service_client_1.TensorboardServiceClient; } });
var vertex_rag_data_service_client_1 = require("./vertex_rag_data_service_client");
Object.defineProperty(exports, "VertexRagDataServiceClient", { enumerable: true, get: function () { return vertex_rag_data_service_client_1.VertexRagDataServiceClient; } });
var vertex_rag_service_client_1 = require("./vertex_rag_service_client");
Object.defineProperty(exports, "VertexRagServiceClient", { enumerable: true, get: function () { return vertex_rag_service_client_1.VertexRagServiceClient; } });
var vizier_service_client_1 = require("./vizier_service_client");
Object.defineProperty(exports, "VizierServiceClient", { enumerable: true, get: function () { return vizier_service_client_1.VizierServiceClient; } });
//# sourceMappingURL=index.js.map