// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.aiplatform.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Cloud.AIPlatform.V1";
option go_package = "cloud.google.com/go/aiplatform/apiv1/aiplatformpb;aiplatformpb";
option java_multiple_files = true;
option java_outer_classname = "ApiAuthProto";
option java_package = "com.google.cloud.aiplatform.v1";
option php_namespace = "Google\\Cloud\\AIPlatform\\V1";
option ruby_package = "Google::Cloud::AIPlatform::V1";
option (google.api.resource_definition) = {
  type: "secretmanager.googleapis.com/SecretVersion"
  pattern: "projects/{project}/secrets/{secret}/versions/{secret_version}"
};

// The generic reusable api auth config.
message ApiAuth {
  // The API secret.
  message ApiKeyConfig {
    // Required. The SecretManager secret version resource name storing API key.
    // e.g. projects/{project}/secrets/{secret}/versions/{version}
    string api_key_secret_version = 1 [
      (google.api.field_behavior) = REQUIRED,
      (google.api.resource_reference) = {
        type: "secretmanager.googleapis.com/SecretVersion"
      }
    ];
  }

  // The auth config.
  oneof auth_config {
    // The API secret.
    ApiKeyConfig api_key_config = 1;
  }
}
