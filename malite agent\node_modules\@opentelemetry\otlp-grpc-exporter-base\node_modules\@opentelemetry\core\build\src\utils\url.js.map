{"version": 3, "file": "url.js", "sourceRoot": "", "sources": ["../../../src/utils/url.ts"], "names": [], "mappings": ";;;AAAA;;;;;;;;;;;;;;GAcG;AACH,SAAgB,UAAU,CAAC,GAAW,EAAE,UAA2B;IACjE,IAAI,OAAO,UAAU,KAAK,QAAQ,EAAE;QAClC,OAAO,GAAG,KAAK,UAAU,CAAC;KAC3B;SAAM;QACL,OAAO,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;KAChC;AACH,CAAC;AAND,gCAMC;AACD;;;;GAIG;AACH,SAAgB,YAAY,CAC1B,GAAW,EACX,WAAoC;IAEpC,IAAI,CAAC,WAAW,EAAE;QAChB,OAAO,KAAK,CAAC;KACd;IAED,KAAK,MAAM,SAAS,IAAI,WAAW,EAAE;QACnC,IAAI,UAAU,CAAC,GAAG,EAAE,SAAS,CAAC,EAAE;YAC9B,OAAO,IAAI,CAAC;SACb;KACF;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAdD,oCAcC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nexport function urlMatches(url: string, urlToMatch: string | RegExp): boolean {\n  if (typeof urlToMatch === 'string') {\n    return url === urlToMatch;\n  } else {\n    return !!url.match(urlToMatch);\n  }\n}\n/**\n * Check if {@param url} should be ignored when comparing against {@param ignoredUrls}\n * @param url\n * @param ignoredUrls\n */\nexport function isUrlIgnored(\n  url: string,\n  ignoredUrls?: Array<string | RegExp>\n): boolean {\n  if (!ignoredUrls) {\n    return false;\n  }\n\n  for (const ignoreUrl of ignoredUrls) {\n    if (urlMatches(url, ignoreUrl)) {\n      return true;\n    }\n  }\n  return false;\n}\n"]}