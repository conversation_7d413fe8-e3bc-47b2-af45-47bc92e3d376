{"traceId": "7054e43bdff9e01e052b415576994d58", "spans": {"93407dc188c790da": {"spanId": "93407dc188c790da", "traceId": "7054e43bdff9e01e052b415576994d58", "parentSpanId": "cd7a613ba0c25f4d", "startTime": 1748149447822, "endTime": 1748149450734.928, "attributes": {"genkit:type": "action", "genkit:metadata:subtype": "model", "genkit:name": "vertexai/gemini-1.0-pro", "genkit:path": "/{generate,t:util}/{vertexai/gemini-1.0-pro,t:action,s:model}", "genkit:input": "{\"messages\":[{\"role\":\"user\",\"content\":[{\"text\":\"مرحباً، قدم نفسك باللغة العربية وأخبرني كيف يمكنني البدء باستخدام Genkit.\"}]}],\"config\":{},\"tools\":[],\"output\":{}}", "genkit:state": "error"}, "displayName": "vertexai/gemini-1.0-pro", "links": [], "instrumentationLibrary": {"name": "genkit-tracer", "version": "v1"}, "spanKind": "INTERNAL", "sameProcessAsParentSpan": {"value": true}, "status": {"code": 2, "message": "[VertexAI.ClientError]: got status: 404 Not Found. {\"error\":{\"code\":404,\"message\":\"Publisher Model `projects/376309378697/locations/us-central1/publishers/google/models/gemini-1.0-pro` was not found or your project does not have access to it. Please ensure you are using a valid model version. For more information, see: https://cloud.google.com/vertex-ai/generative-ai/docs/learn/model-versions\",\"status\":\"NOT_FOUND\"}}"}, "timeEvents": {"timeEvent": [{"time": 1748149450734.4006, "annotation": {"attributes": {"exception.type": "ClientError", "exception.message": "[VertexAI.ClientError]: got status: 404 Not Found. {\"error\":{\"code\":404,\"message\":\"Publisher Model `projects/376309378697/locations/us-central1/publishers/google/models/gemini-1.0-pro` was not found or your project does not have access to it. Please ensure you are using a valid model version. For more information, see: https://cloud.google.com/vertex-ai/generative-ai/docs/learn/model-versions\",\"status\":\"NOT_FOUND\"}}", "exception.stacktrace": "ClientError: [VertexAI.ClientError]: got status: 404 Not Found. {\"error\":{\"code\":404,\"message\":\"Publisher Model `projects/376309378697/locations/us-central1/publishers/google/models/gemini-1.0-pro` was not found or your project does not have access to it. Please ensure you are using a valid model version. For more information, see: https://cloud.google.com/vertex-ai/generative-ai/docs/learn/model-versions\",\"status\":\"NOT_FOUND\"}}\n    at throwErrorIfNotOK (C:\\Users\\<USER>\\malite agent\\node_modules\\@google-cloud\\vertexai\\build\\src\\functions\\post_fetch_processing.js:32:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async generateContent (C:\\Users\\<USER>\\malite agent\\node_modules\\@google-cloud\\vertexai\\build\\src\\functions\\generate_content.js:59:5)\n    at async ChatSessionPreview.sendMessage (C:\\Users\\<USER>\\malite agent\\node_modules\\@google-cloud\\vertexai\\build\\src\\models\\chat_session.js:242:39)\n    at async callGemini (C:\\Users\\<USER>\\malite agent\\node_modules\\@genkit-ai\\vertexai\\lib\\gemini.js:908:26)\n    at async C:\\Users\\<USER>\\malite agent\\node_modules\\@genkit-ai\\vertexai\\lib\\gemini.js:952:11\n    at async C:\\Users\\<USER>\\malite agent\\node_modules\\@genkit-ai\\core\\lib\\action.js:210:14\n    at async C:\\Users\\<USER>\\malite agent\\node_modules\\@genkit-ai\\core\\lib\\action.js:131:27\n    at async C:\\Users\\<USER>\\malite agent\\node_modules\\@genkit-ai\\core\\lib\\tracing\\instrumentation.js:65:16\n    at async C:\\Users\\<USER>\\malite agent\\node_modules\\@genkit-ai\\core\\lib\\tracing\\instrumentation.js:88:24"}, "description": "exception"}}]}}, "cd7a613ba0c25f4d": {"spanId": "cd7a613ba0c25f4d", "traceId": "7054e43bdff9e01e052b415576994d58", "startTime": 1748149447701, "endTime": 1748149450738.0823, "attributes": {"genkit:type": "util", "genkit:name": "generate", "genkit:isRoot": true, "genkit:path": "/{generate,t:util}", "genkit:input": "{\"model\":\"vertexai/gemini-1.0-pro\",\"messages\":[{\"role\":\"user\",\"content\":[{\"text\":\"مرحباً، قدم نفسك باللغة العربية وأخبرني كيف يمكنني البدء باستخدام Genkit.\"}]}],\"config\":{}}", "genkit:state": "error"}, "displayName": "generate", "links": [], "instrumentationLibrary": {"name": "genkit-tracer", "version": "v1"}, "spanKind": "INTERNAL", "sameProcessAsParentSpan": {"value": true}, "status": {"code": 2, "message": "[VertexAI.ClientError]: got status: 404 Not Found. {\"error\":{\"code\":404,\"message\":\"Publisher Model `projects/376309378697/locations/us-central1/publishers/google/models/gemini-1.0-pro` was not found or your project does not have access to it. Please ensure you are using a valid model version. For more information, see: https://cloud.google.com/vertex-ai/generative-ai/docs/learn/model-versions\",\"status\":\"NOT_FOUND\"}}"}, "timeEvents": {"timeEvent": [{"time": 1748149450737.9375, "annotation": {"attributes": {"exception.type": "ClientError", "exception.message": "[VertexAI.ClientError]: got status: 404 Not Found. {\"error\":{\"code\":404,\"message\":\"Publisher Model `projects/376309378697/locations/us-central1/publishers/google/models/gemini-1.0-pro` was not found or your project does not have access to it. Please ensure you are using a valid model version. For more information, see: https://cloud.google.com/vertex-ai/generative-ai/docs/learn/model-versions\",\"status\":\"NOT_FOUND\"}}", "exception.stacktrace": "ClientError: [VertexAI.ClientError]: got status: 404 Not Found. {\"error\":{\"code\":404,\"message\":\"Publisher Model `projects/376309378697/locations/us-central1/publishers/google/models/gemini-1.0-pro` was not found or your project does not have access to it. Please ensure you are using a valid model version. For more information, see: https://cloud.google.com/vertex-ai/generative-ai/docs/learn/model-versions\",\"status\":\"NOT_FOUND\"}}\n    at throwErrorIfNotOK (C:\\Users\\<USER>\\malite agent\\node_modules\\@google-cloud\\vertexai\\build\\src\\functions\\post_fetch_processing.js:32:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async generateContent (C:\\Users\\<USER>\\malite agent\\node_modules\\@google-cloud\\vertexai\\build\\src\\functions\\generate_content.js:59:5)\n    at async ChatSessionPreview.sendMessage (C:\\Users\\<USER>\\malite agent\\node_modules\\@google-cloud\\vertexai\\build\\src\\models\\chat_session.js:242:39)\n    at async callGemini (C:\\Users\\<USER>\\malite agent\\node_modules\\@genkit-ai\\vertexai\\lib\\gemini.js:908:26)\n    at async C:\\Users\\<USER>\\malite agent\\node_modules\\@genkit-ai\\vertexai\\lib\\gemini.js:952:11\n    at async C:\\Users\\<USER>\\malite agent\\node_modules\\@genkit-ai\\core\\lib\\action.js:210:14\n    at async C:\\Users\\<USER>\\malite agent\\node_modules\\@genkit-ai\\core\\lib\\action.js:131:27\n    at async C:\\Users\\<USER>\\malite agent\\node_modules\\@genkit-ai\\core\\lib\\tracing\\instrumentation.js:65:16\n    at async C:\\Users\\<USER>\\malite agent\\node_modules\\@genkit-ai\\core\\lib\\tracing\\instrumentation.js:88:24"}, "description": "exception"}}]}}}, "displayName": "generate", "startTime": 1748149447701, "endTime": 1748149450738.0823}