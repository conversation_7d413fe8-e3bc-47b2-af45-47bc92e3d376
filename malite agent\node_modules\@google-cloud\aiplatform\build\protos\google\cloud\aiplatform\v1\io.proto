// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.aiplatform.v1;

import "google/api/field_behavior.proto";
import "google/cloud/aiplatform/v1/api_auth.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.AIPlatform.V1";
option go_package = "cloud.google.com/go/aiplatform/apiv1/aiplatformpb;aiplatformpb";
option java_multiple_files = true;
option java_outer_classname = "IoProto";
option java_package = "com.google.cloud.aiplatform.v1";
option php_namespace = "Google\\Cloud\\AIPlatform\\V1";
option ruby_package = "Google::Cloud::AIPlatform::V1";

// The storage details for Avro input content.
message AvroSource {
  // Required. Google Cloud Storage location.
  GcsSource gcs_source = 1 [(google.api.field_behavior) = REQUIRED];
}

// The storage details for CSV input content.
message CsvSource {
  // Required. Google Cloud Storage location.
  GcsSource gcs_source = 1 [(google.api.field_behavior) = REQUIRED];
}

// The Google Cloud Storage location for the input content.
message GcsSource {
  // Required. Google Cloud Storage URI(-s) to the input file(s). May contain
  // wildcards. For more information on wildcards, see
  // https://cloud.google.com/storage/docs/gsutil/addlhelp/WildcardNames.
  repeated string uris = 1 [(google.api.field_behavior) = REQUIRED];
}

// The Google Cloud Storage location where the output is to be written to.
message GcsDestination {
  // Required. Google Cloud Storage URI to output directory. If the uri doesn't
  // end with
  // '/', a '/' will be automatically appended. The directory is created if it
  // doesn't exist.
  string output_uri_prefix = 1 [(google.api.field_behavior) = REQUIRED];
}

// The BigQuery location for the input content.
message BigQuerySource {
  // Required. BigQuery URI to a table, up to 2000 characters long.
  // Accepted forms:
  //
  // *  BigQuery path. For example: `bq://projectId.bqDatasetId.bqTableId`.
  string input_uri = 1 [(google.api.field_behavior) = REQUIRED];
}

// The BigQuery location for the output content.
message BigQueryDestination {
  // Required. BigQuery URI to a project or table, up to 2000 characters long.
  //
  // When only the project is specified, the Dataset and Table is created.
  // When the full table reference is specified, the Dataset must exist and
  // table must not exist.
  //
  // Accepted forms:
  //
  // *  BigQuery path. For example:
  // `bq://projectId` or `bq://projectId.bqDatasetId` or
  // `bq://projectId.bqDatasetId.bqTableId`.
  string output_uri = 1 [(google.api.field_behavior) = REQUIRED];
}

// The storage details for CSV output content.
message CsvDestination {
  // Required. Google Cloud Storage location.
  GcsDestination gcs_destination = 1 [(google.api.field_behavior) = REQUIRED];
}

// The storage details for TFRecord output content.
message TFRecordDestination {
  // Required. Google Cloud Storage location.
  GcsDestination gcs_destination = 1 [(google.api.field_behavior) = REQUIRED];
}

// The Container Registry location for the container image.
message ContainerRegistryDestination {
  // Required. Container Registry URI of a container image.
  // Only Google Container Registry and Artifact Registry are supported now.
  // Accepted forms:
  //
  // *  Google Container Registry path. For example:
  //    `gcr.io/projectId/imageName:tag`.
  //
  // *  Artifact Registry path. For example:
  //    `us-central1-docker.pkg.dev/projectId/repoName/imageName:tag`.
  //
  // If a tag is not specified, "latest" will be used as the default tag.
  string output_uri = 1 [(google.api.field_behavior) = REQUIRED];
}

// The Google Drive location for the input content.
message GoogleDriveSource {
  // The type and ID of the Google Drive resource.
  message ResourceId {
    // The type of the Google Drive resource.
    enum ResourceType {
      // Unspecified resource type.
      RESOURCE_TYPE_UNSPECIFIED = 0;

      // File resource type.
      RESOURCE_TYPE_FILE = 1;

      // Folder resource type.
      RESOURCE_TYPE_FOLDER = 2;
    }

    // Required. The type of the Google Drive resource.
    ResourceType resource_type = 1 [(google.api.field_behavior) = REQUIRED];

    // Required. The ID of the Google Drive resource.
    string resource_id = 2 [(google.api.field_behavior) = REQUIRED];
  }

  // Required. Google Drive resource IDs.
  repeated ResourceId resource_ids = 1 [(google.api.field_behavior) = REQUIRED];
}

// The input content is encapsulated and uploaded in the request.
message DirectUploadSource {}

// The Slack source for the ImportRagFilesRequest.
message SlackSource {
  // SlackChannels contains the Slack channels and corresponding access token.
  message SlackChannels {
    // SlackChannel contains the Slack channel ID and the time range to import.
    message SlackChannel {
      // Required. The Slack channel ID.
      string channel_id = 1 [(google.api.field_behavior) = REQUIRED];

      // Optional. The starting timestamp for messages to import.
      google.protobuf.Timestamp start_time = 2
          [(google.api.field_behavior) = OPTIONAL];

      // Optional. The ending timestamp for messages to import.
      google.protobuf.Timestamp end_time = 3
          [(google.api.field_behavior) = OPTIONAL];
    }

    // Required. The Slack channel IDs.
    repeated SlackChannel channels = 1 [(google.api.field_behavior) = REQUIRED];

    // Required. The SecretManager secret version resource name (e.g.
    // projects/{project}/secrets/{secret}/versions/{version}) storing the
    // Slack channel access token that has access to the slack channel IDs.
    // See: https://api.slack.com/tutorials/tracks/getting-a-token.
    ApiAuth.ApiKeyConfig api_key_config = 3
        [(google.api.field_behavior) = REQUIRED];
  }

  // Required. The Slack channels.
  repeated SlackChannels channels = 1 [(google.api.field_behavior) = REQUIRED];
}

// The Jira source for the ImportRagFilesRequest.
message JiraSource {
  // JiraQueries contains the Jira queries and corresponding authentication.
  message JiraQueries {
    // A list of Jira projects to import in their entirety.
    repeated string projects = 3;

    // A list of custom Jira queries to import. For information about JQL (Jira
    // Query Language), see
    // https://support.atlassian.com/************************cloud/docs/use-advanced-search-with-jira-query-language-jql/
    repeated string custom_queries = 4;

    // Required. The Jira email address.
    string email = 5 [(google.api.field_behavior) = REQUIRED];

    // Required. The Jira server URI.
    string server_uri = 6 [(google.api.field_behavior) = REQUIRED];

    // Required. The SecretManager secret version resource name (e.g.
    // projects/{project}/secrets/{secret}/versions/{version}) storing the
    // Jira API key. See [Manage API tokens for your Atlassian
    // account](https://support.atlassian.com/atlassian-account/docs/manage-api-tokens-for-your-atlassian-account/).
    ApiAuth.ApiKeyConfig api_key_config = 7
        [(google.api.field_behavior) = REQUIRED];
  }

  // Required. The Jira queries.
  repeated JiraQueries jira_queries = 1
      [(google.api.field_behavior) = REQUIRED];
}

// The SharePointSources to pass to ImportRagFiles.
message SharePointSources {
  // An individual SharePointSource.
  message SharePointSource {
    // The SharePoint folder source. If not provided, uses "root".
    oneof folder_source {
      // The path of the SharePoint folder to download from.
      string sharepoint_folder_path = 5;

      // The ID of the SharePoint folder to download from.
      string sharepoint_folder_id = 6;
    }

    // The SharePoint drive source.
    oneof drive_source {
      // The name of the drive to download from.
      string drive_name = 7;

      // The ID of the drive to download from.
      string drive_id = 8;
    }

    // The Application ID for the app registered in Microsoft Azure Portal.
    // The application must also be configured with MS Graph permissions
    // "Files.ReadAll", "Sites.ReadAll" and BrowserSiteLists.Read.All.
    string client_id = 1;

    // The application secret for the app registered in Azure.
    ApiAuth.ApiKeyConfig client_secret = 2;

    // Unique identifier of the Azure Active Directory Instance.
    string tenant_id = 3;

    // The name of the SharePoint site to download from. This can be the site
    // name or the site id.
    string sharepoint_site_name = 4;

    // Output only. The SharePoint file id. Output only.
    string file_id = 9 [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // The SharePoint sources.
  repeated SharePointSource share_point_sources = 1;
}
