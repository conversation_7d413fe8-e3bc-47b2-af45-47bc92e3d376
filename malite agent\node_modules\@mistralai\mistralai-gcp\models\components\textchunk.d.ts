import * as z from "zod";
import { ClosedEnum } from "../../types/enums.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
export declare const Type: {
    readonly Text: "text";
};
export type Type = ClosedEnum<typeof Type>;
export type TextChunk = {
    text: string;
    type?: Type | undefined;
};
/** @internal */
export declare const Type$inboundSchema: z.ZodNativeEnum<typeof Type>;
/** @internal */
export declare const Type$outboundSchema: z.Zod<PERSON>ative<PERSON>num<typeof Type>;
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export declare namespace Type$ {
    /** @deprecated use `Type$inboundSchema` instead. */
    const inboundSchema: z.ZodNativeEnum<{
        readonly Text: "text";
    }>;
    /** @deprecated use `Type$outboundSchema` instead. */
    const outboundSchema: z.<PERSON>od<PERSON><{
        readonly Text: "text";
    }>;
}
/** @internal */
export declare const TextChunk$inboundSchema: z.ZodType<TextChunk, z.ZodTypeDef, unknown>;
/** @internal */
export type TextChunk$Outbound = {
    text: string;
    type: string;
};
/** @internal */
export declare const TextChunk$outboundSchema: z.ZodType<TextChunk$Outbound, z.ZodTypeDef, TextChunk>;
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export declare namespace TextChunk$ {
    /** @deprecated use `TextChunk$inboundSchema` instead. */
    const inboundSchema: z.ZodType<TextChunk, z.ZodTypeDef, unknown>;
    /** @deprecated use `TextChunk$outboundSchema` instead. */
    const outboundSchema: z.ZodType<TextChunk$Outbound, z.ZodTypeDef, TextChunk>;
    /** @deprecated use `TextChunk$Outbound` instead. */
    type Outbound = TextChunk$Outbound;
}
export declare function textChunkToJSON(textChunk: TextChunk): string;
export declare function textChunkFromJSON(jsonString: string): SafeParseResult<TextChunk, SDKValidationError>;
//# sourceMappingURL=textchunk.d.ts.map