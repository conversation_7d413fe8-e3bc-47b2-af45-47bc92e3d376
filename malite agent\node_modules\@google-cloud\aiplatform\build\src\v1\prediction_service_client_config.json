{"interfaces": {"google.cloud.aiplatform.v1.PredictionService": {"retry_codes": {"non_idempotent": [], "idempotent": ["DEADLINE_EXCEEDED", "UNAVAILABLE"]}, "retry_params": {"default": {"initial_retry_delay_millis": 100, "retry_delay_multiplier": 1.3, "max_retry_delay_millis": 60000, "initial_rpc_timeout_millis": 60000, "rpc_timeout_multiplier": 1, "max_rpc_timeout_millis": 60000, "total_timeout_millis": 600000}}, "methods": {"Predict": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "RawPredict": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "StreamRawPredict": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "DirectPredict": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "DirectRawPredict": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "StreamDirectPredict": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "StreamDirectRawPredict": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "StreamingPredict": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "ServerStreamingPredict": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "StreamingRawPredict": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "Explain": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "GenerateContent": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "StreamGenerateContent": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}}}}}