// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.aiplatform.v1beta1;

import "google/api/field_behavior.proto";
import "google/cloud/aiplatform/v1beta1/env_var.proto";

option csharp_namespace = "Google.Cloud.AIPlatform.V1Beta1";
option go_package = "cloud.google.com/go/aiplatform/apiv1beta1/aiplatformpb;aiplatformpb";
option java_multiple_files = true;
option java_outer_classname = "NotebookSoftwareConfigProto";
option java_package = "com.google.cloud.aiplatform.v1beta1";
option php_namespace = "Google\\Cloud\\AIPlatform\\V1beta1";
option ruby_package = "Google::Cloud::AIPlatform::V1beta1";

// Post startup script config.
message PostStartupScriptConfig {
  // Represents a notebook runtime post startup script behavior.
  enum PostStartupScriptBehavior {
    // Unspecified post startup script behavior.
    POST_STARTUP_SCRIPT_BEHAVIOR_UNSPECIFIED = 0;

    // Run post startup script after runtime is started.
    RUN_ONCE = 1;

    // Run post startup script after runtime is stopped.
    RUN_EVERY_START = 2;

    // Download and run post startup script every time runtime is started.
    DOWNLOAD_AND_RUN_EVERY_START = 3;
  }

  // Optional. Post startup script to run after runtime is started.
  string post_startup_script = 1 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Post startup script url to download. Example:
  // https://bucket/script.sh
  string post_startup_script_url = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Post startup script behavior that defines download and execution
  // behavior.
  PostStartupScriptBehavior post_startup_script_behavior = 3
      [(google.api.field_behavior) = OPTIONAL];
}

// Notebook Software Config.
message NotebookSoftwareConfig {
  // Optional. Environment variables to be passed to the container.
  // Maximum limit is 100.
  repeated EnvVar env = 1 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Post startup script config.
  PostStartupScriptConfig post_startup_script_config = 2
      [(google.api.field_behavior) = OPTIONAL];
}
