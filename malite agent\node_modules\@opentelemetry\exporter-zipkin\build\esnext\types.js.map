{"version": 3, "file": "types.js", "sourceRoot": "", "sources": ["../../src/types.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAyIH;;;;;;;;;;;;;;;;;;;;;GAqBG;AACH,MAAM,CAAN,IAAY,QAKX;AALD,WAAY,QAAQ;IAClB,6BAAiB,CAAA;IACjB,6BAAiB,CAAA;IACjB,iCAAqB,CAAA;IACrB,iCAAqB,CAAA;AACvB,CAAC,EALW,QAAQ,KAAR,QAAQ,QAKnB", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ExportResult } from '@opentelemetry/core';\n\n/**\n * Exporter config\n */\nexport interface ExporterConfig {\n  headers?: Record<string, string>;\n  serviceName?: string;\n  url?: string;\n  // Optional mapping overrides for OpenTelemetry status code and description.\n  statusCodeTagName?: string;\n  statusDescriptionTagName?: string;\n  getExportRequestHeaders?: () => Record<string, string> | undefined;\n}\n\n/**\n * Zipkin Span\n * @see https://github.com/openzipkin/zipkin-api/blob/master/zipkin2-api.yaml\n */\nexport interface Span {\n  /**\n   * Trace identifier, set on all spans within it.\n   */\n  traceId: string;\n  /**\n   * The logical operation this span represents in lowercase (e.g. rpc method).\n   * Leave absent if unknown.\n   */\n  name: string;\n  /**\n   * The parent span ID or absent if this the root span in a trace.\n   */\n  parentId?: string;\n  /**\n   * Unique 64bit identifier for this operation within the trace.\n   */\n  id: string;\n  /**\n   * When present, kind clarifies timestamp, duration and remoteEndpoint.\n   * When absent, the span is local or incomplete.\n   */\n  kind?: SpanKind;\n  /**\n   * Epoch microseconds of the start of this span, possibly absent if\n   * incomplete.\n   */\n  timestamp: number;\n  /**\n   * Duration in microseconds of the critical path, if known.\n   */\n  duration: number;\n  /**\n   * True is a request to store this span even if it overrides sampling policy.\n   * This is true when the `X-B3-Flags` header has a value of 1.\n   */\n  debug?: boolean;\n  /**\n   * True if we are contributing to a span started by another tracer (ex on a\n   * different host).\n   */\n  shared?: boolean;\n  /**\n   * The host that recorded this span, primarily for query by service name.\n   */\n  localEndpoint: Endpoint;\n  /**\n   * Associates events that explain latency with the time they happened.\n   */\n  annotations?: Annotation[];\n  /**\n   * Tags give your span context for search, viewing and analysis.\n   */\n  tags: Tags;\n  /**\n   * TODO: `remoteEndpoint`, do we need to support it?\n   * When an RPC (or messaging) span, indicates the other side of the\n   * connection.\n   */\n}\n\n/**\n * Associates an event that explains latency with a timestamp.\n * Unlike log statements, annotations are often codes. Ex. \"ws\" for WireSend\n * Zipkin v1 core annotations such as \"cs\" and \"sr\" have been replaced with\n * Span.Kind, which interprets timestamp and duration.\n */\nexport interface Annotation {\n  /**\n   * Epoch microseconds of this event.\n   * For example, 1502787600000000 corresponds to 2017-08-15 09:00 UTC\n   */\n  timestamp: number;\n  /**\n   * Usually a short tag indicating an event, like \"error\"\n   * While possible to add larger data, such as garbage collection details, low\n   * cardinality event names both keep the size of spans down and also are easy\n   * to search against.\n   */\n  value: string;\n}\n\n/**\n * The network context of a node in the service graph.\n */\nexport interface Endpoint {\n  /**\n   * Lower-case label of this node in the service graph, such as \"favstar\".\n   * Leave absent if unknown.\n   * This is a primary label for trace lookup and aggregation, so it should be\n   * intuitive and consistent. Many use a name from service discovery.\n   */\n  serviceName?: string;\n  /**\n   * The text representation of the primary IPv4 address associated with this\n   * connection. Ex. ************** Absent if unknown.\n   */\n  ipv4?: string;\n  /**\n   * The text representation of the primary IPv6 address associated with a\n   * connection. Ex. 2001:db8::c001 Absent if unknown.\n   * Prefer using the ipv4 field for mapped addresses.\n   */\n  port?: number;\n}\n\n/**\n * Adds context to a span, for search, viewing and analysis.\n * For example, a key \"your_app.version\" would let you lookup traces by version.\n * A tag \"sql.query\" isn't searchable, but it can help in debugging when viewing\n * a trace.\n */\nexport interface Tags {\n  [tagKey: string]: unknown;\n}\n\n/**\n * When present, kind clarifies timestamp, duration and remoteEndpoint. When\n * absent, the span is local or incomplete. Unlike client and server, there\n * is no direct critical path latency relationship between producer and\n * consumer spans.\n * `CLIENT`\n *   timestamp is the moment a request was sent to the server.\n *   duration is the delay until a response or an error was received.\n *   remoteEndpoint is the server.\n * `SERVER`\n *   timestamp is the moment a client request was received.\n *   duration is the delay until a response was sent or an error.\n *   remoteEndpoint is the client.\n * `PRODUCER`\n *   timestamp is the moment a message was sent to a destination.\n *   duration is the delay sending the message, such as batching.\n *   remoteEndpoint is the broker.\n * `CONSUMER`\n *   timestamp is the moment a message was received from an origin.\n *   duration is the delay consuming the message, such as from backlog.\n *   remoteEndpoint - Represents the broker. Leave serviceName absent if unknown.\n */\nexport enum SpanKind {\n  CLIENT = 'CLIENT',\n  SERVER = 'SERVER',\n  CONSUMER = 'CONSUMER',\n  PRODUCER = 'PRODUCER',\n}\n\n/**\n * interface for function that will send zipkin spans\n */\nexport type SendFunction = (\n  zipkinSpans: Span[],\n  done: (result: ExportResult) => void\n) => void;\n\nexport type GetHeaders = () => Record<string, string> | undefined;\n\nexport type SendFn = (\n  zipkinSpans: Span[],\n  done: (result: ExportResult) => void\n) => void;\n"]}