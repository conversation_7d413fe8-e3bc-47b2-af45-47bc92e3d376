{"version": 3, "file": "W3CBaggagePropagator.js", "sourceRoot": "", "sources": ["../../../../src/baggage/propagation/W3CBaggagePropagator.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,4CAO4B;AAE5B,mEAAmE;AACnE,4CAKsB;AACtB,oCAA6E;AAE7E;;;;;GAKG;AACH,MAAa,oBAAoB;IAC/B,MAAM,CAAC,OAAgB,EAAE,OAAgB,EAAE,MAAqB;QAC9D,MAAM,OAAO,GAAG,iBAAW,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;QAChD,IAAI,CAAC,OAAO,IAAI,IAAA,sCAAmB,EAAC,OAAO,CAAC;YAAE,OAAO;QACrD,MAAM,QAAQ,GAAG,IAAA,mBAAW,EAAC,OAAO,CAAC;aAClC,MAAM,CAAC,CAAC,IAAY,EAAE,EAAE;YACvB,OAAO,IAAI,CAAC,MAAM,IAAI,4CAAgC,CAAC;QACzD,CAAC,CAAC;aACD,KAAK,CAAC,CAAC,EAAE,wCAA4B,CAAC,CAAC;QAC1C,MAAM,WAAW,GAAG,IAAA,yBAAiB,EAAC,QAAQ,CAAC,CAAC;QAChD,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE;YAC1B,MAAM,CAAC,GAAG,CAAC,OAAO,EAAE,0BAAc,EAAE,WAAW,CAAC,CAAC;SAClD;IACH,CAAC;IAED,OAAO,CAAC,OAAgB,EAAE,OAAgB,EAAE,MAAqB;QAC/D,MAAM,WAAW,GAAG,MAAM,CAAC,GAAG,CAAC,OAAO,EAAE,0BAAc,CAAC,CAAC;QACxD,MAAM,aAAa,GAAG,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC;YAC9C,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC,mCAAuB,CAAC;YAC3C,CAAC,CAAC,WAAW,CAAC;QAChB,IAAI,CAAC,aAAa;YAAE,OAAO,OAAO,CAAC;QACnC,MAAM,OAAO,GAAiC,EAAE,CAAC;QACjD,IAAI,aAAa,CAAC,MAAM,KAAK,CAAC,EAAE;YAC9B,OAAO,OAAO,CAAC;SAChB;QACD,MAAM,KAAK,GAAG,aAAa,CAAC,KAAK,CAAC,mCAAuB,CAAC,CAAC;QAC3D,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;YACpB,MAAM,OAAO,GAAG,IAAA,yBAAiB,EAAC,KAAK,CAAC,CAAC;YACzC,IAAI,OAAO,EAAE;gBACX,MAAM,YAAY,GAAiB,EAAE,KAAK,EAAE,OAAO,CAAC,KAAK,EAAE,CAAC;gBAC5D,IAAI,OAAO,CAAC,QAAQ,EAAE;oBACpB,YAAY,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;iBAC1C;gBACD,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,YAAY,CAAC;aACrC;QACH,CAAC,CAAC,CAAC;QACH,IAAI,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,KAAK,CAAC,EAAE;YACxC,OAAO,OAAO,CAAC;SAChB;QACD,OAAO,iBAAW,CAAC,UAAU,CAAC,OAAO,EAAE,iBAAW,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,CAAC;IAC7E,CAAC;IAED,MAAM;QACJ,OAAO,CAAC,0BAAc,CAAC,CAAC;IAC1B,CAAC;CACF;AA7CD,oDA6CC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  BaggageEntry,\n  Context,\n  propagation,\n  TextMapGetter,\n  TextMapPropagator,\n  TextMapSetter,\n} from '@opentelemetry/api';\n\nimport { isTracingSuppressed } from '../../trace/suppress-tracing';\nimport {\n  BAGGAGE_HEADER,\n  BAGGAGE_ITEMS_SEPARATOR,\n  BAGGAGE_MAX_NAME_VALUE_PAIRS,\n  BAGGAGE_MAX_PER_NAME_VALUE_PAIRS,\n} from '../constants';\nimport { getKeyPairs, parsePairKeyValue, serializeKeyPairs } from '../utils';\n\n/**\n * Propagates {@link Baggage} through Context format propagation.\n *\n * Based on the Baggage specification:\n * https://w3c.github.io/baggage/\n */\nexport class W3CBaggagePropagator implements TextMapPropagator {\n  inject(context: Context, carrier: unknown, setter: TextMapSetter): void {\n    const baggage = propagation.getBaggage(context);\n    if (!baggage || isTracingSuppressed(context)) return;\n    const keyPairs = getKeyPairs(baggage)\n      .filter((pair: string) => {\n        return pair.length <= BAGGAGE_MAX_PER_NAME_VALUE_PAIRS;\n      })\n      .slice(0, BAGGAGE_MAX_NAME_VALUE_PAIRS);\n    const headerValue = serializeKeyPairs(keyPairs);\n    if (headerValue.length > 0) {\n      setter.set(carrier, BAGGAGE_HEADER, headerValue);\n    }\n  }\n\n  extract(context: Context, carrier: unknown, getter: TextMapGetter): Context {\n    const headerValue = getter.get(carrier, BAGGAGE_HEADER);\n    const baggageString = Array.isArray(headerValue)\n      ? headerValue.join(BAGGAGE_ITEMS_SEPARATOR)\n      : headerValue;\n    if (!baggageString) return context;\n    const baggage: Record<string, BaggageEntry> = {};\n    if (baggageString.length === 0) {\n      return context;\n    }\n    const pairs = baggageString.split(BAGGAGE_ITEMS_SEPARATOR);\n    pairs.forEach(entry => {\n      const keyPair = parsePairKeyValue(entry);\n      if (keyPair) {\n        const baggageEntry: BaggageEntry = { value: keyPair.value };\n        if (keyPair.metadata) {\n          baggageEntry.metadata = keyPair.metadata;\n        }\n        baggage[keyPair.key] = baggageEntry;\n      }\n    });\n    if (Object.entries(baggage).length === 0) {\n      return context;\n    }\n    return propagation.setBaggage(context, propagation.createBaggage(baggage));\n  }\n\n  fields(): string[] {\n    return [BAGGAGE_HEADER];\n  }\n}\n"]}