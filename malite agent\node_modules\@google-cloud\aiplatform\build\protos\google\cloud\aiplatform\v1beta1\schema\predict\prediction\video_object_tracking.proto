// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.aiplatform.v1beta1.schema.predict.prediction;

import "google/protobuf/duration.proto";
import "google/protobuf/wrappers.proto";

option csharp_namespace = "Google.Cloud.AIPlatform.V1Beta1.Schema.Predict.Prediction";
option go_package = "cloud.google.com/go/aiplatform/apiv1beta1/schema/predict/prediction/predictionpb;predictionpb";
option java_multiple_files = true;
option java_outer_classname = "VideoObjectTrackingPredictionResultProto";
option java_package = "com.google.cloud.aiplatform.v1beta1.schema.predict.prediction";
option php_namespace = "Google\\Cloud\\AIPlatform\\V1beta1\\Schema\\Predict\\Prediction";
option ruby_package = "Google::Cloud::AIPlatform::V1beta1::Schema::Predict::Prediction";

// Prediction output format for Video Object Tracking.
message VideoObjectTrackingPredictionResult {
  // The fields `xMin`, `xMax`, `yMin`, and `yMax` refer to a bounding box,
  // i.e. the rectangle over the video frame pinpointing the found
  // AnnotationSpec. The coordinates are relative to the frame size, and the
  // point 0,0 is in the top left of the frame.
  message Frame {
    // A time (frame) of a video in which the object has been detected.
    // Expressed as a number of seconds as measured from the
    // start of the video, with fractions up to a microsecond precision, and
    // with "s" appended at the end.
    google.protobuf.Duration time_offset = 1;

    // The leftmost coordinate of the bounding box.
    google.protobuf.FloatValue x_min = 2;

    // The rightmost coordinate of the bounding box.
    google.protobuf.FloatValue x_max = 3;

    // The topmost coordinate of the bounding box.
    google.protobuf.FloatValue y_min = 4;

    // The bottommost coordinate of the bounding box.
    google.protobuf.FloatValue y_max = 5;
  }

  // The resource ID of the AnnotationSpec that had been identified.
  string id = 1;

  // The display name of the AnnotationSpec that had been identified.
  string display_name = 2;

  // The beginning, inclusive, of the video's time segment in which the
  // object instance has been detected. Expressed as a number of seconds as
  // measured from the start of the video, with fractions up to a microsecond
  // precision, and with "s" appended at the end.
  google.protobuf.Duration time_segment_start = 3;

  // The end, inclusive, of the video's time segment in which the
  // object instance has been detected. Expressed as a number of seconds as
  // measured from the start of the video, with fractions up to a microsecond
  // precision, and with "s" appended at the end.
  google.protobuf.Duration time_segment_end = 4;

  // The Model's confidence in correction of this prediction, higher
  // value means higher confidence.
  google.protobuf.FloatValue confidence = 5;

  // All of the frames of the video in which a single object instance has been
  // detected. The bounding boxes in the frames identify the same object.
  repeated Frame frames = 6;
}
