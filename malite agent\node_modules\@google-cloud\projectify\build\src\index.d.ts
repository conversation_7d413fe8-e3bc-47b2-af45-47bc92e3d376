/**
 * Populate the `{{projectId}}` placeholder.
 *
 * @throws {Error} If a projectId is required, but one is not provided.
 *
 * @param {*} - Any input value that may contain a placeholder. Arrays and objects will be looped.
 * @param {string} projectId - A projectId. If not provided
 * @return {*} - The original argument with all placeholders populated.
 */
export declare function replaceProjectIdToken(value: any, projectId: string): any;
/**
 * Custom error type for missing project ID errors.
 */
export declare class MissingProjectIdError extends Error {
    message: string;
}
