# Docker Compose للإنتاج - تطبيق Malite Agent
version: '3.8'

services:
  # الخدمة الرئيسية - تطبيق Malite Agent
  malite-agent:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: malite-agent-prod
    ports:
      - "8080:8080"
    environment:
      - NODE_ENV=production
      - GENKIT_ENV=production
      - PORT=8080
      - HOST=0.0.0.0
      - VERTEX_AI_LOCATION=us-central1
      - LOG_LEVEL=INFO
      - ENABLE_CONTENT_FILTERING=true
      - ENABLE_CACHING=true
    env_file:
      - .env.production
    volumes:
      - ./logs:/app/logs:rw
    networks:
      - malite-network
    restart: always
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 1G
          cpus: '0.5'
    healthcheck:
      test: ["CMD", "./docker-healthcheck.sh"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "5"

  # خدمة Redis للتخزين المؤقت
  redis:
    image: redis:7-alpine
    container_name: malite-redis-prod
    volumes:
      - redis_data:/data
    networks:
      - malite-network
    restart: always
    command: redis-server --appendonly yes --maxmemory 256mb --maxmemory-policy allkeys-lru
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 5s
      retries: 3
    logging:
      driver: "json-file"
      options:
        max-size: "5m"
        max-file: "3"

  # خدمة Nginx كـ Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: malite-nginx-prod
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.prod.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - ./logs/nginx:/var/log/nginx:rw
    networks:
      - malite-network
    restart: always
    depends_on:
      - malite-agent
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.25'
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost/health"]
      interval: 30s
      timeout: 5s
      retries: 3
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

# الشبكات
networks:
  malite-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# الأحجام المستمرة
volumes:
  redis_data:
    driver: local
