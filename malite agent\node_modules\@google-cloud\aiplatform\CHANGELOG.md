# Changelog

## [3.35.0](https://github.com/googleapis/google-cloud-node/compare/aiplatform-v3.34.0...aiplatform-v3.35.0) (2025-02-28)


### Features

* A new field `create_time` is added to message `.google.cloud.aiplatform.v1.GenerateContentResponse` ([fdef07b](https://github.com/googleapis/google-cloud-node/commit/fdef07b2005791ed8a3bd600b526784ce24a78d8))
* A new field `create_time` is added to message `.google.cloud.aiplatform.v1.GenerateContentResponse` ([fdef07b](https://github.com/googleapis/google-cloud-node/commit/fdef07b2005791ed8a3bd600b526784ce24a78d8))
* A new field `list_all_versions` to `ListPublisherModelsRequest` ([fdef07b](https://github.com/googleapis/google-cloud-node/commit/fdef07b2005791ed8a3bd600b526784ce24a78d8))
* A new field `response_id` is added to message `.google.cloud.aiplatform.v1.GenerateContentResponse` ([fdef07b](https://github.com/googleapis/google-cloud-node/commit/fdef07b2005791ed8a3bd600b526784ce24a78d8))
* A new value `NVIDIA_H100_MEGA_80GB` is added to enum `AcceleratorType` ([fdef07b](https://github.com/googleapis/google-cloud-node/commit/fdef07b2005791ed8a3bd600b526784ce24a78d8))
* A new value `NVIDIA_H100_MEGA_80GB` is added to enum `AcceleratorType` ([fdef07b](https://github.com/googleapis/google-cloud-node/commit/fdef07b2005791ed8a3bd600b526784ce24a78d8))
* Add a new thought field in content proto ([fdef07b](https://github.com/googleapis/google-cloud-node/commit/fdef07b2005791ed8a3bd600b526784ce24a78d8))
* Add additional Probe options to v1 model.proto ([fdef07b](https://github.com/googleapis/google-cloud-node/commit/fdef07b2005791ed8a3bd600b526784ce24a78d8))
* Add Context Cache to v1 ([fdef07b](https://github.com/googleapis/google-cloud-node/commit/fdef07b2005791ed8a3bd600b526784ce24a78d8))
* Add GenerationConfig.MediaResolution ([fdef07b](https://github.com/googleapis/google-cloud-node/commit/fdef07b2005791ed8a3bd600b526784ce24a78d8))
* Add GenerationConfig.Modality ([fdef07b](https://github.com/googleapis/google-cloud-node/commit/fdef07b2005791ed8a3bd600b526784ce24a78d8))
* Add GenerationConfig.SpeechConfig ([fdef07b](https://github.com/googleapis/google-cloud-node/commit/fdef07b2005791ed8a3bd600b526784ce24a78d8))
* Add LLM parser proto to API ([fdef07b](https://github.com/googleapis/google-cloud-node/commit/fdef07b2005791ed8a3bd600b526784ce24a78d8))
* Add machine_spec, data_persistent_disk_spec, network_spec, euc_config, shielded_vm_config to `.google.cloud.aiplatform.v1beta1.NotebookRuntime` ([fdef07b](https://github.com/googleapis/google-cloud-node/commit/fdef07b2005791ed8a3bd600b526784ce24a78d8))
* Add machine_spec, data_persistent_disk_spec, network_spec, euc_config, shielded_vm_config to message `.google.cloud.aiplatform.v1.NotebookRuntime` ([fdef07b](https://github.com/googleapis/google-cloud-node/commit/fdef07b2005791ed8a3bd600b526784ce24a78d8))
* Add Model Garden deploy API ([fdef07b](https://github.com/googleapis/google-cloud-node/commit/fdef07b2005791ed8a3bd600b526784ce24a78d8))
* Add Model Garden deploy API ([#5836](https://github.com/googleapis/google-cloud-node/issues/5836)) ([fdef07b](https://github.com/googleapis/google-cloud-node/commit/fdef07b2005791ed8a3bd600b526784ce24a78d8))
* Add new `RequiredReplicaCount` field to DedicatedResources in MachineResources ([fdef07b](https://github.com/googleapis/google-cloud-node/commit/fdef07b2005791ed8a3bd600b526784ce24a78d8))
* Add new `RequiredReplicaCount` field to DedicatedResources in MachineResources ([fdef07b](https://github.com/googleapis/google-cloud-node/commit/fdef07b2005791ed8a3bd600b526784ce24a78d8))
* Add new `Status` field to DeployedModel in Endpoint ([fdef07b](https://github.com/googleapis/google-cloud-node/commit/fdef07b2005791ed8a3bd600b526784ce24a78d8))
* Add new `Status` field to DeployedModel in Endpoint ([fdef07b](https://github.com/googleapis/google-cloud-node/commit/fdef07b2005791ed8a3bd600b526784ce24a78d8))
* Add Notebooks Runtime Software Configuration ([fdef07b](https://github.com/googleapis/google-cloud-node/commit/fdef07b2005791ed8a3bd600b526784ce24a78d8))
* Add Notebooks Runtime Software Configuration ([fdef07b](https://github.com/googleapis/google-cloud-node/commit/fdef07b2005791ed8a3bd600b526784ce24a78d8))
* Add optimized config in v1 API ([fdef07b](https://github.com/googleapis/google-cloud-node/commit/fdef07b2005791ed8a3bd600b526784ce24a78d8))
* Add per-modality token count break downs for GenAI APIs ([fdef07b](https://github.com/googleapis/google-cloud-node/commit/fdef07b2005791ed8a3bd600b526784ce24a78d8))
* Add per-modality token count break downs for GenAI APIs ([fdef07b](https://github.com/googleapis/google-cloud-node/commit/fdef07b2005791ed8a3bd600b526784ce24a78d8))
* Add rag_files_count to RagCorpus to count number of associated files ([fdef07b](https://github.com/googleapis/google-cloud-node/commit/fdef07b2005791ed8a3bd600b526784ce24a78d8))
* Add retrieval_config to ToolConfig v1 ([fdef07b](https://github.com/googleapis/google-cloud-node/commit/fdef07b2005791ed8a3bd600b526784ce24a78d8))
* Add retrieval_config to ToolConfig v1beta1 ([fdef07b](https://github.com/googleapis/google-cloud-node/commit/fdef07b2005791ed8a3bd600b526784ce24a78d8))
* Add RolloutOptions to DeployedModel in v1beta1 endpoint.proto, add additional Probe options in v1beta1 model.proto ([fdef07b](https://github.com/googleapis/google-cloud-node/commit/fdef07b2005791ed8a3bd600b526784ce24a78d8))
* Add speculative decoding spec to DeployedModel proto ([fdef07b](https://github.com/googleapis/google-cloud-node/commit/fdef07b2005791ed8a3bd600b526784ce24a78d8))
* Add Tool.GoogleSearch ([fdef07b](https://github.com/googleapis/google-cloud-node/commit/fdef07b2005791ed8a3bd600b526784ce24a78d8))
* Add Vertex RAG service proto to v1 ([fdef07b](https://github.com/googleapis/google-cloud-node/commit/fdef07b2005791ed8a3bd600b526784ce24a78d8))
* Add workbench_runtime and kernel_name to NotebookExecutionJob ([fdef07b](https://github.com/googleapis/google-cloud-node/commit/fdef07b2005791ed8a3bd600b526784ce24a78d8))
* Add workbench_runtime and kernel_name to NotebookExecutionJob ([fdef07b](https://github.com/googleapis/google-cloud-node/commit/fdef07b2005791ed8a3bd600b526784ce24a78d8))
* Enable FeatureGroup IAM Methods in v1beta1 API version ([fdef07b](https://github.com/googleapis/google-cloud-node/commit/fdef07b2005791ed8a3bd600b526784ce24a78d8))
* Enable FeatureGroup Service Account and IAM methods ([fdef07b](https://github.com/googleapis/google-cloud-node/commit/fdef07b2005791ed8a3bd600b526784ce24a78d8))
* Enable FeatureView Service Account in v1 API version ([fdef07b](https://github.com/googleapis/google-cloud-node/commit/fdef07b2005791ed8a3bd600b526784ce24a78d8))
* Enable UpdateFeatureMonitor in v1beta1 API version ([fdef07b](https://github.com/googleapis/google-cloud-node/commit/fdef07b2005791ed8a3bd600b526784ce24a78d8))
* EvaluateDataset API v1beta1 initial release ([fdef07b](https://github.com/googleapis/google-cloud-node/commit/fdef07b2005791ed8a3bd600b526784ce24a78d8))
* Expose code execution tool API to v1 ([fdef07b](https://github.com/googleapis/google-cloud-node/commit/fdef07b2005791ed8a3bd600b526784ce24a78d8))
* Introduce HybridSearch and Ranking configuration for RAG ([fdef07b](https://github.com/googleapis/google-cloud-node/commit/fdef07b2005791ed8a3bd600b526784ce24a78d8))
* Introduce VertexAiSearch integration for RAG ([fdef07b](https://github.com/googleapis/google-cloud-node/commit/fdef07b2005791ed8a3bd600b526784ce24a78d8))
* Model Registry Checkpoint API ([fdef07b](https://github.com/googleapis/google-cloud-node/commit/fdef07b2005791ed8a3bd600b526784ce24a78d8))
* Model Registry Checkpoint API ([fdef07b](https://github.com/googleapis/google-cloud-node/commit/fdef07b2005791ed8a3bd600b526784ce24a78d8))
* Paging changes for bigquery ([fdef07b](https://github.com/googleapis/google-cloud-node/commit/fdef07b2005791ed8a3bd600b526784ce24a78d8))
* Reasoning Engine v1 GAPIC release ([fdef07b](https://github.com/googleapis/google-cloud-node/commit/fdef07b2005791ed8a3bd600b526784ce24a78d8))
* Remove autorater config related visibility v1beta1 ([fdef07b](https://github.com/googleapis/google-cloud-node/commit/fdef07b2005791ed8a3bd600b526784ce24a78d8))
* Support streaming and multi class methods in Reasoning Engine v1beta1 API ([fdef07b](https://github.com/googleapis/google-cloud-node/commit/fdef07b2005791ed8a3bd600b526784ce24a78d8))


### Bug Fixes

* Add x-goog-request params to headers for LRO-polling methods ([fdef07b](https://github.com/googleapis/google-cloud-node/commit/fdef07b2005791ed8a3bd600b526784ce24a78d8))
* Finalize fixing typings for headers in generator ([fdef07b](https://github.com/googleapis/google-cloud-node/commit/fdef07b2005791ed8a3bd600b526784ce24a78d8))
* Fix typings for headers in generator ([fdef07b](https://github.com/googleapis/google-cloud-node/commit/fdef07b2005791ed8a3bd600b526784ce24a78d8))
* Remove extra protos in ESM & capture ESM in headers ([fdef07b](https://github.com/googleapis/google-cloud-node/commit/fdef07b2005791ed8a3bd600b526784ce24a78d8))

## [3.34.0](https://github.com/googleapis/google-cloud-node/compare/aiplatform-v3.33.0...aiplatform-v3.34.0) (2024-11-21)


### Features

* Add a v1 UpdateEndpointLongRunning API ([#5812](https://github.com/googleapis/google-cloud-node/issues/5812)) ([39dc74e](https://github.com/googleapis/google-cloud-node/commit/39dc74ec30a573169d282a6e1b172c48e23b00d4))

## [3.33.0](https://github.com/googleapis/google-cloud-node/compare/aiplatform-v3.32.0...aiplatform-v3.33.0) (2024-11-14)


### Features

* Add fast_tryout_enabled to FasterDeploymentConfig v1 proto ([#5781](https://github.com/googleapis/google-cloud-node/issues/5781)) ([3cc221d](https://github.com/googleapis/google-cloud-node/commit/3cc221d8c6018eab6e7a5ba14c38be2218dcee36))

## [3.32.0](https://github.com/googleapis/google-cloud-node/compare/aiplatform-v3.31.0...aiplatform-v3.32.0) (2024-10-30)


### Features

* Add code execution tool API ([#5761](https://github.com/googleapis/google-cloud-node/issues/5761)) ([f032b83](https://github.com/googleapis/google-cloud-node/commit/f032b8303a412828252e822edb607be47471013b))
* Introduce DefaultRuntime to PipelineJob ([#5756](https://github.com/googleapis/google-cloud-node/issues/5756)) ([c4badb0](https://github.com/googleapis/google-cloud-node/commit/c4badb0999c66dc075d5630dc8920a2528417c87))

## [3.31.0](https://github.com/googleapis/google-cloud-node/compare/aiplatform-v3.30.1...aiplatform-v3.31.0) (2024-10-10)


### Features

* Add partner_model_tuning_spec to TuningJob ([#5721](https://github.com/googleapis/google-cloud-node/issues/5721)) ([cec22ab](https://github.com/googleapis/google-cloud-node/commit/cec22abd4f27d8acf3c7c1d87b2bd6873e505841))

## [3.30.1](https://github.com/googleapis/google-cloud-node/compare/aiplatform-v3.30.0...aiplatform-v3.30.1) (2024-10-10)


### Bug Fixes

* Annotate PipelineJob and PipelineTaskRerunConfig fields as optional ([#5698](https://github.com/googleapis/google-cloud-node/issues/5698)) ([183c038](https://github.com/googleapis/google-cloud-node/commit/183c038dde5d49727ebc066990c3217629d3fa61))

## [3.30.0](https://github.com/googleapis/google-cloud-node/compare/aiplatform-v3.29.0...aiplatform-v3.30.0) (2024-09-24)


### Features

* A new field `response_logprbs` is added to message `.google.cloud.aiplatform.v1.GenerationConfig` ([#5664](https://github.com/googleapis/google-cloud-node/issues/5664)) ([100e106](https://github.com/googleapis/google-cloud-node/commit/100e106f93010cf648295a6df58c1a2f82c01340))

## [3.29.0](https://github.com/googleapis/google-cloud-node/compare/aiplatform-v3.28.0...aiplatform-v3.29.0) (2024-09-10)


### Features

* Add more configurability to feature_group.proto ([#5656](https://github.com/googleapis/google-cloud-node/issues/5656)) ([9760c20](https://github.com/googleapis/google-cloud-node/commit/9760c20edaed2aaba62f4278b9afff01804d5c34))
* Add Vector DB config for Vertex RAG (Weaviate + FeatureStore) ([#5648](https://github.com/googleapis/google-cloud-node/issues/5648)) ([d62f18a](https://github.com/googleapis/google-cloud-node/commit/d62f18a7e7b0800e44604216f3c9a7477dc8551e))

## [3.28.0](https://github.com/googleapis/google-cloud-node/compare/aiplatform-v3.27.0...aiplatform-v3.28.0) (2024-08-29)


### Features

* Add max_wait_duration to Scheduling ([#5642](https://github.com/googleapis/google-cloud-node/issues/5642)) ([39e65cb](https://github.com/googleapis/google-cloud-node/commit/39e65cbbe8c09020d6d33a2b6864de17ec58ef51))

## [3.27.0](https://github.com/googleapis/google-cloud-node/compare/aiplatform-v3.26.0...aiplatform-v3.27.0) (2024-08-19)


### Features

* A new field `satisfies_pzs` is added to message `.google.cloud.aiplatform.v1.BatchPredictionJob` ([#5605](https://github.com/googleapis/google-cloud-node/issues/5605)) ([e4574ad](https://github.com/googleapis/google-cloud-node/commit/e4574ad769864684f633fa38c4ebf819143ccabb))

## [3.26.0](https://github.com/googleapis/google-cloud-node/compare/aiplatform-v3.25.0...aiplatform-v3.26.0) (2024-08-09)


### Features

* A new field `score` is added to message `.google.cloud.aiplatform.v1.Candidate` ([#5577](https://github.com/googleapis/google-cloud-node/issues/5577)) ([1abd9a7](https://github.com/googleapis/google-cloud-node/commit/1abd9a70dfddde93dfd21122dd1e041f9e6a3fb8))

## [3.25.0](https://github.com/googleapis/google-cloud-node/compare/aiplatform-v3.24.0...aiplatform-v3.25.0) (2024-07-10)


### Features

* Add model and contents fields to ComputeTokensRequest v1 ([#5517](https://github.com/googleapis/google-cloud-node/issues/5517)) ([8983209](https://github.com/googleapis/google-cloud-node/commit/89832091d214555b274df86d817866a33b559e05))
* Enable rest_numeric_enums for aiplatform v1 and v1beta1 ([#5531](https://github.com/googleapis/google-cloud-node/issues/5531)) ([2359c34](https://github.com/googleapis/google-cloud-node/commit/2359c347ca89df4aee706b0c6798560d686a15cc))

## [3.24.0](https://github.com/googleapis/google-cloud-node/compare/aiplatform-v3.23.0...aiplatform-v3.24.0) (2024-06-26)


### Features

* Add MALFORMED_FUNCTION_CALL to FinishReason ([#5481](https://github.com/googleapis/google-cloud-node/issues/5481)) ([83498e9](https://github.com/googleapis/google-cloud-node/commit/83498e9a326d0640bf3654491814dc19cc479def))

## [3.23.0](https://github.com/googleapis/google-cloud-node/compare/aiplatform-v3.22.0...aiplatform-v3.23.0) (2024-06-05)


### Features

* Add rag_embedding_model_config to RagCorpus ([#5430](https://github.com/googleapis/google-cloud-node/issues/5430)) ([a1aba11](https://github.com/googleapis/google-cloud-node/commit/a1aba11b9fb796f7c8abfa9930e8636bd7106d01))

## [3.22.0](https://github.com/googleapis/google-cloud-node/compare/aiplatform-v3.21.0...aiplatform-v3.22.0) (2024-06-03)


### Features

* Add ValueType.STRUCT to Feature  ([#5381](https://github.com/googleapis/google-cloud-node/issues/5381)) ([6d60ea8](https://github.com/googleapis/google-cloud-node/commit/6d60ea883cd5e3759296a14057b5ee3a86962464))

## [3.21.0](https://github.com/googleapis/google-cloud-node/compare/aiplatform-v3.20.0...aiplatform-v3.21.0) (2024-05-21)


### Features

* [Many APIs] update Nodejs generator to send API versions in headers for GAPICs ([#5351](https://github.com/googleapis/google-cloud-node/issues/5351)) ([01f48fc](https://github.com/googleapis/google-cloud-node/commit/01f48fce63ec4ddf801d59ee2b8c0db9f6fb8372))
* [Many APIs] update Nodejs generator to send API versions in headers for GAPICs ([#5354](https://github.com/googleapis/google-cloud-node/issues/5354)) ([a9784ed](https://github.com/googleapis/google-cloud-node/commit/a9784ed3db6ee96d171762308bbbcd57390b6866))
* Update Nodejs generator to send API versions in headers for GAPICs ([#5343](https://github.com/googleapis/google-cloud-node/issues/5343)) ([d129760](https://github.com/googleapis/google-cloud-node/commit/d129760b6ce09bf5d7037aa04df60f0d2e064bea))

## [3.20.0](https://github.com/googleapis/google-cloud-node/compare/aiplatform-v3.19.1...aiplatform-v3.20.0) (2024-05-02)


### Features

* A new field `search_entry_point` is added to message `.google.cloud.aiplatform.v1beta1.GroundingMetadata` ([#5285](https://github.com/googleapis/google-cloud-node/issues/5285)) ([fe2a4e3](https://github.com/googleapis/google-cloud-node/commit/fe2a4e3cb6f8714048b1e0c97e07c7afbfb25602))

## [3.19.1](https://github.com/googleapis/google-cloud-node/compare/aiplatform-v3.19.0...aiplatform-v3.19.1) (2024-04-23)


### Bug Fixes

* Delete the deprecated field for model monitor ([#5274](https://github.com/googleapis/google-cloud-node/issues/5274)) ([ae521e6](https://github.com/googleapis/google-cloud-node/commit/ae521e642cf612621edabaeacc81abbd66aec5d2))

## [3.19.0](https://github.com/googleapis/google-cloud-node/compare/aiplatform-v3.18.0...aiplatform-v3.19.0) (2024-04-18)


### Features

* Add model_monitor resource and APIs to public v1beta1 client library ([#5234](https://github.com/googleapis/google-cloud-node/issues/5234)) ([cecd67c](https://github.com/googleapis/google-cloud-node/commit/cecd67c9f25e687c0a0081e4f424b70525604d72))

## [3.18.0](https://github.com/googleapis/google-cloud-node/compare/aiplatform-v3.17.0...aiplatform-v3.18.0) (2024-04-10)


### Features

* Add Persistent Resource reboot api call to v1beta1 ([#5182](https://github.com/googleapis/google-cloud-node/issues/5182)) ([c271d54](https://github.com/googleapis/google-cloud-node/commit/c271d54547cf657eabd674ace50842d22005fb67))

## [3.17.0](https://github.com/googleapis/google-cloud-node/compare/aiplatform-v3.16.0...aiplatform-v3.17.0) (2024-03-26)


### Features

* Add Optimized feature store proto ([#5153](https://github.com/googleapis/google-cloud-node/issues/5153)) ([1f5d99c](https://github.com/googleapis/google-cloud-node/commit/1f5d99c3fec4d7c0c84da22fc780e4e76af57548))
* Evaluation Service aiplatform v1beta1 initial release ([#5172](https://github.com/googleapis/google-cloud-node/issues/5172)) ([6827acc](https://github.com/googleapis/google-cloud-node/commit/6827accaabc06bffa6a154189c18cd8e3bc7ed82))

## [3.16.0](https://github.com/googleapis/google-cloud-node/compare/aiplatform-v3.15.0...aiplatform-v3.16.0) (2024-03-23)


### Features

* Add v1beta1 StreamingFetchFeatureValues API ([#5128](https://github.com/googleapis/google-cloud-node/issues/5128)) ([4849a58](https://github.com/googleapis/google-cloud-node/commit/4849a58a9761a0123f3232f33651bd6972e6fd3a))

## [3.15.0](https://github.com/googleapis/google-cloud-node/compare/aiplatform-v3.14.0...aiplatform-v3.15.0) (2024-03-12)


### Features

* A new value `NVIDIA_H100_80GB` is added to enum `AcceleratorType` ([#5114](https://github.com/googleapis/google-cloud-node/issues/5114)) ([729d778](https://github.com/googleapis/google-cloud-node/commit/729d778f67e4e0869379b32d2b2b19c758ebc955))

## [3.14.0](https://github.com/googleapis/google-cloud-node/compare/aiplatform-v3.13.0...aiplatform-v3.14.0) (2024-02-27)


### Features

* Add CompositeKey message and composite_key field to FeatureViewDataKey ([#5072](https://github.com/googleapis/google-cloud-node/issues/5072)) ([b643f04](https://github.com/googleapis/google-cloud-node/commit/b643f040b35dd180ad0264935373e44056283249))

## [3.13.0](https://github.com/googleapis/google-cloud-node/compare/aiplatform-v3.12.0...aiplatform-v3.13.0) (2024-02-22)


### Features

* Add `point_of_contact` to `Feature` message ([#5064](https://github.com/googleapis/google-cloud-node/issues/5064)) ([d158bfb](https://github.com/googleapis/google-cloud-node/commit/d158bfb432f1c54ae9cb121607c33508e79accf0))
* Add Grounding feature to PredictionService.GenerateContent ([#5036](https://github.com/googleapis/google-cloud-node/issues/5036)) ([6b73cce](https://github.com/googleapis/google-cloud-node/commit/6b73cce2fc4b29290b51878ba07aa4b9bca4e573))

## [3.12.0](https://github.com/googleapis/google-cloud-node/compare/aiplatform-v3.11.0...aiplatform-v3.12.0) (2024-02-09)


### Features

* Trusted Private Cloud support, use the universeDomain parameter  ([#5021](https://github.com/googleapis/google-cloud-node/issues/5021)) ([ca9af37](https://github.com/googleapis/google-cloud-node/commit/ca9af375a2a62ce3e3f5ad09db43028591a0b571))

## [3.11.0](https://github.com/googleapis/google-cloud-node/compare/aiplatform-v3.10.1...aiplatform-v3.11.0) (2024-02-07)


### Features

* Add generateContent Unary API for aiplatform_v1 ([#5003](https://github.com/googleapis/google-cloud-node/issues/5003)) ([8722b31](https://github.com/googleapis/google-cloud-node/commit/8722b3190d0a20ebd19308c9fd8c426c417f5e4e))

## [3.10.1](https://github.com/googleapis/google-cloud-node/compare/aiplatform-v3.10.0...aiplatform-v3.10.1) (2024-01-23)


### Bug Fixes

* Improve retry logic for streaming API calls ([#4942](https://github.com/googleapis/google-cloud-node/issues/4942)) ([0bca99d](https://github.com/googleapis/google-cloud-node/commit/0bca99dd38384b0cdeb2d418a001985d58297b3c))

## [3.10.0](https://github.com/googleapis/google-cloud-node/compare/aiplatform-v3.9.0...aiplatform-v3.10.0) (2024-01-05)


### Features

* Add Content ([#4880](https://github.com/googleapis/google-cloud-node/issues/4880)) ([d74d48d](https://github.com/googleapis/google-cloud-node/commit/d74d48dbdd24391c9777d06d306a64b15bead998))

## [3.9.0](https://github.com/googleapis/google-cloud-node/compare/aiplatform-v3.8.0...aiplatform-v3.9.0) (2023-12-11)


### Features

* Add data_stats to Model ([#4866](https://github.com/googleapis/google-cloud-node/issues/4866)) ([e7614ad](https://github.com/googleapis/google-cloud-node/commit/e7614ad5cb26d4828ac3cbc81d94bc7a9ea0a26c))

## [3.8.0](https://github.com/googleapis/google-cloud-node/compare/aiplatform-v3.7.0...aiplatform-v3.8.0) (2023-11-30)


### Features

* Add grpc_ports to UploadModel ModelContainerSpec ([#4836](https://github.com/googleapis/google-cloud-node/issues/4836)) ([736ff8b](https://github.com/googleapis/google-cloud-node/commit/736ff8bf73959cbc6e0caa0125bf4dd2ba0776dc))

## [3.7.0](https://github.com/googleapis/google-cloud-node/compare/aiplatform-v3.6.0...aiplatform-v3.7.0) (2023-11-16)


### Features

* [aiplatform] add protected_artifact_location_id to CustomJob ([#4809](https://github.com/googleapis/google-cloud-node/issues/4809)) ([9da9501](https://github.com/googleapis/google-cloud-node/commit/9da950110296e4fed58de27f35284091616ea1b7))

## [3.6.0](https://github.com/googleapis/google-cloud-node/compare/aiplatform-v3.5.0...aiplatform-v3.6.0) (2023-11-09)


### Features

* Add Optimized to FeatureOnlineStore ([#4790](https://github.com/googleapis/google-cloud-node/issues/4790)) ([61cb5c9](https://github.com/googleapis/google-cloud-node/commit/61cb5c91e054639da95268ab13ae99f909d261c8))

## [3.5.0](https://github.com/googleapis/google-cloud-node/compare/aiplatform-v3.4.0...aiplatform-v3.5.0) (2023-11-03)


### Features

* Adding new fields for concurrent explanations ([489e188](https://github.com/googleapis/google-cloud-node/commit/489e188d97af302f96299a5b40eb5e07020972fc))


### Bug Fixes

* **aiplatform/v1beta1:** Change CreateFeature metadata ([#4782](https://github.com/googleapis/google-cloud-node/issues/4782)) ([489e188](https://github.com/googleapis/google-cloud-node/commit/489e188d97af302f96299a5b40eb5e07020972fc))

## [3.4.0](https://github.com/googleapis/google-cloud-node/compare/aiplatform-v3.3.0...aiplatform-v3.4.0) (2023-10-24)


### Features

* Add DatasetVersion and dataset version RPCs to DatasetService ([#4726](https://github.com/googleapis/google-cloud-node/issues/4726)) ([a515212](https://github.com/googleapis/google-cloud-node/commit/a515212494f629c171a6a038136a20aaefe3d1cc))

## [3.3.0](https://github.com/googleapis/google-cloud-node/compare/aiplatform-v3.2.0...aiplatform-v3.3.0) (2023-10-18)


### Features

* Add dedicated_serving_endpoint ([#4722](https://github.com/googleapis/google-cloud-node/issues/4722)) ([b01c920](https://github.com/googleapis/google-cloud-node/commit/b01c920a996dfc8a3f21d0433f756985edf46b34))
* Add feature.proto ([#4713](https://github.com/googleapis/google-cloud-node/issues/4713)) ([0490f66](https://github.com/googleapis/google-cloud-node/commit/0490f6644994992176c4d960ea1d29402cfae305))

## [3.2.0](https://github.com/googleapis/google-cloud-node/compare/aiplatform-v3.1.0...aiplatform-v3.2.0) (2023-09-11)


### Features

* Add encryption_spec to index.proto and index_endpoint.proto ([#4596](https://github.com/googleapis/google-cloud-node/issues/4596)) ([32d823a](https://github.com/googleapis/google-cloud-node/commit/32d823a8ea1613ebb653978a7cd0686f9af2724b))

## [3.1.0](https://github.com/googleapis/google-cloud-node/compare/aiplatform-v3.0.0...aiplatform-v3.1.0) (2023-09-06)


### Features

* Add NVIDIA_H100_80GB and TPU_V5_LITEPOD to AcceleratorType ([#4557](https://github.com/googleapis/google-cloud-node/issues/4557)) ([7253ef1](https://github.com/googleapis/google-cloud-node/commit/7253ef13b805900f4e686c18bd55b7f0b299e8bb))


### Bug Fixes

* [Many APIs] simplify logic for HTTP/1.1 REST fallback option ([#4583](https://github.com/googleapis/google-cloud-node/issues/4583)) ([c3ddba8](https://github.com/googleapis/google-cloud-node/commit/c3ddba8df9fee6185e36a4e99f7c67b0319f1242))

## [3.0.0](https://github.com/googleapis/google-cloud-node/compare/aiplatform-v2.17.0...aiplatform-v3.0.0) (2023-08-06)


### ⚠ BREAKING CHANGES

* migrate to Node 14 ([#4443](https://github.com/googleapis/google-cloud-node/issues/4443))

### Features

* Add `PredictionService.ServerStreamingPredict` method ([#4439](https://github.com/googleapis/google-cloud-node/issues/4439)) ([44d81b3](https://github.com/googleapis/google-cloud-node/commit/44d81b3c16b3884aed141a16bc6094f5c54ab6d7))
* Add RaySepc to ResourceRuntimeSpec, and add ResourceRuntime to PersistentResource ([#4470](https://github.com/googleapis/google-cloud-node/issues/4470)) ([d58a096](https://github.com/googleapis/google-cloud-node/commit/d58a0965ed45659ddab161972da43a64f0b4d36b))


### Miscellaneous Chores

* Migrate to Node 14 ([#4443](https://github.com/googleapis/google-cloud-node/issues/4443)) ([2260f12](https://github.com/googleapis/google-cloud-node/commit/2260f12543d171bda95345e53475f5f0fdc45770))

## [2.17.0](https://github.com/googleapis/google-cloud-node/compare/aiplatform-v2.16.0...aiplatform-v2.17.0) (2023-07-24)


### Features

* Add data_item_count to Dataset ([#4411](https://github.com/googleapis/google-cloud-node/issues/4411)) ([3f99064](https://github.com/googleapis/google-cloud-node/commit/3f99064cfc29afce6cd7f8b72b881ae7ceb8a394))

## [2.16.0](https://github.com/googleapis/google-cloud-node/compare/aiplatform-v2.15.0...aiplatform-v2.16.0) (2023-06-28)


### Features

* Add bias_configs to ModelEvaluation ([#4349](https://github.com/googleapis/google-cloud-node/issues/4349)) ([4c047d4](https://github.com/googleapis/google-cloud-node/commit/4c047d4a9cdbc08dfb70e3843a54ca73ad1f4e79))
* Add UpdateExplanationDataset to aiplatform ([#4359](https://github.com/googleapis/google-cloud-node/issues/4359)) ([c0a3d8a](https://github.com/googleapis/google-cloud-node/commit/c0a3d8a1df6b319e555955dfef8a56ceef54a6e2))

## [2.15.0](https://github.com/googleapis/google-cloud-node/compare/aiplatform-v2.14.0...aiplatform-v2.15.0) (2023-06-06)


### Features

* [ai-platform] add blocking_operation_ids to ImportFeatureValuesOperationMetadata ([#4284](https://github.com/googleapis/google-cloud-node/issues/4284)) ([99736a9](https://github.com/googleapis/google-cloud-node/commit/99736a9b9ac01b9673280eddc2f57a8722646ecd))


### Bug Fixes

* [ai-platform] update typings for helpers, updated docstrings; removed stale comment ([#4312](https://github.com/googleapis/google-cloud-node/issues/4312)) ([b2c4f46](https://github.com/googleapis/google-cloud-node/commit/b2c4f4670643d8e5a727c80fb668d5b8ab0cf2b1))

## [2.14.0](https://github.com/googleapis/google-cloud-node/compare/aiplatform-v2.13.0...aiplatform-v2.14.0) (2023-05-20)


### Features

* Add updateSchedule method to ScheduleService ([#4280](https://github.com/googleapis/google-cloud-node/issues/4280)) ([d1e67be](https://github.com/googleapis/google-cloud-node/commit/d1e67beadf1910378989cd1bd3c19cbe000973d7))

## [2.13.0](https://github.com/googleapis/google-cloud-node/compare/aiplatform-v2.12.0...aiplatform-v2.13.0) (2023-05-09)


### Features

* Add example_gcs_source to Examples in aiplatform v1beta1 explanation.proto ([#4249](https://github.com/googleapis/google-cloud-node/issues/4249)) ([4af8ae2](https://github.com/googleapis/google-cloud-node/commit/4af8ae27af952194885939a3e8014089bf7fc01e))

## [2.12.0](https://github.com/googleapis/google-cloud-node/compare/aiplatform-v2.11.0...aiplatform-v2.12.0) (2023-05-04)


### Features

* Add model_garden_service.proto and publisher_model.proto to BUILD.bazel ([#4225](https://github.com/googleapis/google-cloud-node/issues/4225)) ([5367d25](https://github.com/googleapis/google-cloud-node/commit/5367d25cdaa4e935e5c1e345622bf4cbd7131e1a))

## [2.11.0](https://github.com/googleapis/google-cloud-node/compare/aiplatform-v2.10.0...aiplatform-v2.11.0) (2023-05-02)


### Features

* Add experiment and experiment_run to CustomJobSpec ([20eafa1](https://github.com/googleapis/google-cloud-node/commit/20eafa1b79ca8f255400d4eb00fb6d398d3a3980))
* Add GENIE to ModelSourceType ([20eafa1](https://github.com/googleapis/google-cloud-node/commit/20eafa1b79ca8f255400d4eb00fb6d398d3a3980))
* Add MutateDeployedModel RPC to endpoint_service ([20eafa1](https://github.com/googleapis/google-cloud-node/commit/20eafa1b79ca8f255400d4eb00fb6d398d3a3980))
* Add NVIDIA_L4 to AcceleratorType ([20eafa1](https://github.com/googleapis/google-cloud-node/commit/20eafa1b79ca8f255400d4eb00fb6d398d3a3980))
* Add NVIDIA_L4 to AcceleratorType ([#4216](https://github.com/googleapis/google-cloud-node/issues/4216)) ([20eafa1](https://github.com/googleapis/google-cloud-node/commit/20eafa1b79ca8f255400d4eb00fb6d398d3a3980))
* Add offline_storage_ttl_days to EntityType ([20eafa1](https://github.com/googleapis/google-cloud-node/commit/20eafa1b79ca8f255400d4eb00fb6d398d3a3980))
* Add online_storage_ttl_days to FeatureStore ([20eafa1](https://github.com/googleapis/google-cloud-node/commit/20eafa1b79ca8f255400d4eb00fb6d398d3a3980))

## [2.10.0](https://github.com/googleapis/google-cloud-node/compare/aiplatform-v2.9.0...aiplatform-v2.10.0) (2023-04-24)


### Features

* Add is_default to Tensorboard in aiplatform v1 tensorboard.proto and v1beta1 tensorboard.proto ([#4179](https://github.com/googleapis/google-cloud-node/issues/4179)) ([3c46888](https://github.com/googleapis/google-cloud-node/commit/3c46888d8b5b7568eaeac913fed55829a63f58df))

## [2.9.0](https://github.com/googleapis/google-cloud-node/compare/aiplatform-v2.8.1...aiplatform-v2.9.0) (2023-04-13)


### Features

* [ai-platform] add public_endpoint_enabled and publid_endpoint_domain_name to IndexEndpoint ([#4136](https://github.com/googleapis/google-cloud-node/issues/4136)) ([94ea195](https://github.com/googleapis/google-cloud-node/commit/94ea195acc92d947f27374a5a8c753c2f0cc1d0e))
* Add notification_channels in aiplatform v1beta1 model_monitoring.proto ([#4160](https://github.com/googleapis/google-cloud-node/issues/4160)) ([c56a74d](https://github.com/googleapis/google-cloud-node/commit/c56a74d070a826e35b3582060c7ff5aa51a72f17))


### Bug Fixes

* **deps:** Bump `google-gax` to ^3.5.8 ([#4117](https://github.com/googleapis/google-cloud-node/issues/4117)) ([0b67d88](https://github.com/googleapis/google-cloud-node/commit/0b67d883963643ce1b4f6d2ccd3e8d37adf6e029))
* Minify JSON and JS files, and remove .map files ([#4143](https://github.com/googleapis/google-cloud-node/issues/4143)) ([170f7d5](https://github.com/googleapis/google-cloud-node/commit/170f7d57b8fd344d182a8e758867b8124722eebc))

## [2.8.1](https://github.com/googleapis/google-cloud-node/compare/aiplatform-v2.8.0...aiplatform-v2.8.1) (2023-03-28)


### Bug Fixes

* Remove large_model_reference from Model in aiplatform v1beta1 model.proto ([#4098](https://github.com/googleapis/google-cloud-node/issues/4098)) ([3c1a0ee](https://github.com/googleapis/google-cloud-node/commit/3c1a0eec71a426a8b6bc31ad7b4b729ccafd500a))

## [2.8.0](https://github.com/googleapis/google-cloud-node/compare/aiplatform-v2.7.0...aiplatform-v2.8.0) (2023-03-06)


### Features

* Add disable_container_logging to BatchPredictionJob in aiplatform v1,v1beta1 batch_prediction_job.proto ([#4052](https://github.com/googleapis/google-cloud-node/issues/4052)) ([735af33](https://github.com/googleapis/google-cloud-node/commit/735af33f8c5548118e0df774f08f89cc657445ef))

## [2.7.0](https://github.com/googleapis/google-cloud-node/compare/aiplatform-v2.6.1...aiplatform-v2.7.0) (2023-02-22)


### Features

* Add match service in aiplatform v1beta1 match_service.proto ([#4002](https://github.com/googleapis/google-cloud-node/issues/4002)) ([e2944f5](https://github.com/googleapis/google-cloud-node/commit/e2944f565b39ae151e11f938fb9dcbefd337880e))

## [2.6.1](https://github.com/googleapis/google-cloud-node/compare/aiplatform-v2.6.0...aiplatform-v2.6.1) (2023-02-15)


### Bug Fixes

* [Many APIs] changing format of the jsdoc links ([#3985](https://github.com/googleapis/google-cloud-node/issues/3985)) ([fb2a6fd](https://github.com/googleapis/google-cloud-node/commit/fb2a6fdbd9dcf2ae91b3767629d71f0970d0712c))

## [2.6.0](https://github.com/googleapis/google-cloud-node/compare/aiplatform-v2.5.0...aiplatform-v2.6.0) (2023-02-09)


### Features

* [ai-platform] add service_networking.proto to aiplatform v1 ([#3937](https://github.com/googleapis/google-cloud-node/issues/3937)) ([fcaacbc](https://github.com/googleapis/google-cloud-node/commit/fcaacbc8060ec8eb750d283dc17febe9fb0f2cd8))

## [2.5.0](https://github.com/googleapis/google-cloud-node/compare/aiplatform-v2.4.0...aiplatform-v2.5.0) (2023-01-28)


### Features

* Add enable_dashboard_access in aiplatform v1 and v1beta1 ([#460](https://github.com/googleapis/google-cloud-node/issues/460)) ([58d9da5](https://github.com/googleapis/google-cloud-node/commit/58d9da5b38285ea729c7dcbfb28940265751ac21))


### Bug Fixes

* Removing migrated samples (to nodejs-docs-samples) from repo ([#555](https://github.com/googleapis/google-cloud-node/issues/555)) ([c97bb20](https://github.com/googleapis/google-cloud-node/commit/c97bb20334af2f1d469e8fcdd2cc01fe7182d64f))

## [2.4.0](https://github.com/googleapis/nodejs-ai-platform/compare/v2.3.0...v2.4.0) (2023-01-25)


### Features

* Add enable_dashboard_access in aiplatform v1 and v1beta1 ([#460](https://github.com/googleapis/nodejs-ai-platform/issues/460)) ([685cfed](https://github.com/googleapis/nodejs-ai-platform/commit/685cfedc6bb69fe8b118ce613f13eea6dc5d2b0b))


### Bug Fixes

* **deps:** Use google-gax v3.5.2 ([#458](https://github.com/googleapis/nodejs-ai-platform/issues/458)) ([9345b15](https://github.com/googleapis/nodejs-ai-platform/commit/9345b15fcc22f60f00cd50442403fc1af4fabe71))
* Removing migrated samples (to nodejs-docs-samples) from repo ([#555](https://github.com/googleapis/nodejs-ai-platform/issues/555)) ([0864901](https://github.com/googleapis/nodejs-ai-platform/commit/08649013609e9ba39de734f8a496dd082d3cf76d))

## [2.3.0](https://github.com/googleapis/nodejs-ai-platform/compare/v2.2.0...v2.3.0) (2022-09-21)


### Features

* Add deleteFeatureValues in aiplatform v1beta1 featurestore_service.proto ([#371](https://github.com/googleapis/nodejs-ai-platform/issues/371)) ([e1c5cd6](https://github.com/googleapis/nodejs-ai-platform/commit/e1c5cd6b5d03afb03911ba9aa685457aa359a602))
* Add timestamp_outside_retention_rows_count, RemoveContextChildren, order_by, InputArtifact, read_mask, TransferLearningConfig ([#450](https://github.com/googleapis/nodejs-ai-platform/issues/450)) ([3a3f71f](https://github.com/googleapis/nodejs-ai-platform/commit/3a3f71faf9add8925a9ca9fa6427a6f6f50e0990))


### Bug Fixes

* Allow passing gax instance to client constructor ([#365](https://github.com/googleapis/nodejs-ai-platform/issues/365)) ([6200e38](https://github.com/googleapis/nodejs-ai-platform/commit/6200e38d052636102ee3d8a817682f2d8c388213))
* Preserve default values in x-goog-request-params header ([#370](https://github.com/googleapis/nodejs-ai-platform/issues/370)) ([6860cfd](https://github.com/googleapis/nodejs-ai-platform/commit/6860cfded924648ef79706278b66d31736dbfe23))

## [2.2.0](https://github.com/googleapis/nodejs-ai-platform/compare/v2.1.0...v2.2.0) (2022-08-27)


### Features

* add a DeploymentResourcePool API resource_definition ([fa3c209](https://github.com/googleapis/nodejs-ai-platform/commit/fa3c209a4b9c1dbe55f55ba6fa4589f14866156b))
* add DeploymentResourcePool in aiplatform v1beta1 deployment_resource_pool.proto ([fa3c209](https://github.com/googleapis/nodejs-ai-platform/commit/fa3c209a4b9c1dbe55f55ba6fa4589f14866156b))
* add DeploymentResourcePoolService in aiplatform v1beta1 deployment_resource_pool_service.proto ([fa3c209](https://github.com/googleapis/nodejs-ai-platform/commit/fa3c209a4b9c1dbe55f55ba6fa4589f14866156b))
* add shared_resources for supported prediction_resources ([fa3c209](https://github.com/googleapis/nodejs-ai-platform/commit/fa3c209a4b9c1dbe55f55ba6fa4589f14866156b))
* add SHARED_RESOURCES to DeploymentResourcesType in aiplatform v1beta1 model.proto ([fa3c209](https://github.com/googleapis/nodejs-ai-platform/commit/fa3c209a4b9c1dbe55f55ba6fa4589f14866156b))
* add WriteFeatureValues in aiplatform v1beta1 featurestore_online_service.proto ([fa3c209](https://github.com/googleapis/nodejs-ai-platform/commit/fa3c209a4b9c1dbe55f55ba6fa4589f14866156b))
* added SHARED_RESOURCES enum to aiplatform v1 model.proto ([fa3c209](https://github.com/googleapis/nodejs-ai-platform/commit/fa3c209a4b9c1dbe55f55ba6fa4589f14866156b))
* DeploymentResourcePool and DeployementResourcePoolService added to aiplatform v1beta1 model.proto (cl/463147866) ([fa3c209](https://github.com/googleapis/nodejs-ai-platform/commit/fa3c209a4b9c1dbe55f55ba6fa4589f14866156b))
* making network arg optional in aiplatform v1 custom_job.proto ([fa3c209](https://github.com/googleapis/nodejs-ai-platform/commit/fa3c209a4b9c1dbe55f55ba6fa4589f14866156b))
* making network arg optional in aiplatform v1beta1 custom_job.proto ([fa3c209](https://github.com/googleapis/nodejs-ai-platform/commit/fa3c209a4b9c1dbe55f55ba6fa4589f14866156b))
* **samples:** add entity type apis samples ([#339](https://github.com/googleapis/nodejs-ai-platform/issues/339)) ([5bb18f3](https://github.com/googleapis/nodejs-ai-platform/commit/5bb18f3cc9b5b565612cc44dec3f5b503a89bb88))
* **samples:** add feature apis samples ([#340](https://github.com/googleapis/nodejs-ai-platform/issues/340)) ([90dcec7](https://github.com/googleapis/nodejs-ai-platform/commit/90dcec729659b811dcb2eddb6425e48d19e5dddd))
* **samples:** add feature values apis samples ([#341](https://github.com/googleapis/nodejs-ai-platform/issues/341)) ([4cef015](https://github.com/googleapis/nodejs-ai-platform/commit/4cef015867dd80af195236efc43b309d82c7f8af))
* **samples:** add remaining featurestore api samples ([#338](https://github.com/googleapis/nodejs-ai-platform/issues/338)) ([f136458](https://github.com/googleapis/nodejs-ai-platform/commit/f1364583a58793c5f04cf180e7b2e0bbda100f53))


### Bug Fixes

* better support for fallback mode ([#362](https://github.com/googleapis/nodejs-ai-platform/issues/362)) ([3786e75](https://github.com/googleapis/nodejs-ai-platform/commit/3786e7563eb294dbb22bec89f4ec3b012c6437b1))
* change import long to require ([#363](https://github.com/googleapis/nodejs-ai-platform/issues/363)) ([b26381c](https://github.com/googleapis/nodejs-ai-platform/commit/b26381cd314dcdda56f5cc0fc7d0f62f2ebd44ba))
* do not import the whole google-gax from proto JS ([#1553](https://github.com/googleapis/nodejs-ai-platform/issues/1553)) ([#366](https://github.com/googleapis/nodejs-ai-platform/issues/366)) ([1ccaa8d](https://github.com/googleapis/nodejs-ai-platform/commit/1ccaa8dff34cf684ab372bbc79b8a25ffa2a4b26))
* remove pip install statements ([#1546](https://github.com/googleapis/nodejs-ai-platform/issues/1546)) ([#364](https://github.com/googleapis/nodejs-ai-platform/issues/364)) ([275a048](https://github.com/googleapis/nodejs-ai-platform/commit/275a0481ea783ba14505a5c296e1e3c3ccf48556))
* use google-gax v3.3.0 ([1ccaa8d](https://github.com/googleapis/nodejs-ai-platform/commit/1ccaa8dff34cf684ab372bbc79b8a25ffa2a4b26))

## [2.1.0](https://github.com/googleapis/nodejs-ai-platform/compare/v2.0.0...v2.1.0) (2022-07-04)


### Features

* add BatchImportModelEvaluationSlices API in aiplatform v1 model_service.proto ([0a7e609](https://github.com/googleapis/nodejs-ai-platform/commit/0a7e60980bce5850d016d2b6e3df35f0d5b55360))
* add ListSavedQueries rpc to aiplatform v1 dataset_service.proto ([0a7e609](https://github.com/googleapis/nodejs-ai-platform/commit/0a7e60980bce5850d016d2b6e3df35f0d5b55360))
* add ListSavedQueries rpc to aiplatform v1beta1 dataset_service.proto ([0a7e609](https://github.com/googleapis/nodejs-ai-platform/commit/0a7e60980bce5850d016d2b6e3df35f0d5b55360))
* add monitor_window to ModelDeploymentMonitoringScheduleConfig proto in aiplatform v1/v1beta1 model_deployment_monitoring_job.proto ([0a7e609](https://github.com/googleapis/nodejs-ai-platform/commit/0a7e60980bce5850d016d2b6e3df35f0d5b55360))
* add saved_query_id to InputDataConfig in aiplatform v1 training_pipeline.proto ([0a7e609](https://github.com/googleapis/nodejs-ai-platform/commit/0a7e60980bce5850d016d2b6e3df35f0d5b55360))
* add saved_query_id to InputDataConfig in aiplatform v1beta1 training_pipeline.proto ([0a7e609](https://github.com/googleapis/nodejs-ai-platform/commit/0a7e60980bce5850d016d2b6e3df35f0d5b55360))
* add saved_query.proto to aiplatform v1 ([0a7e609](https://github.com/googleapis/nodejs-ai-platform/commit/0a7e60980bce5850d016d2b6e3df35f0d5b55360))
* add saved_query.proto to aiplatform v1beta1 ([0a7e609](https://github.com/googleapis/nodejs-ai-platform/commit/0a7e60980bce5850d016d2b6e3df35f0d5b55360))

## [2.0.0](https://github.com/googleapis/nodejs-ai-platform/compare/v1.19.0...v2.0.0) (2022-06-23)


### ⚠ BREAKING CHANGES

* update library to use Node 12 (#304)

### Features

* add ConvexAutomatedStoppingSpec to StudySpec in aiplatform v1beta1 study.proto ([863748a](https://github.com/googleapis/nodejs-ai-platform/commit/863748aa5ca44064a8affafda5e5ae050b948a03))
* add display_name and metadata to ModelEvaluation in aiplatform model_evaluation.proto ([#297](https://github.com/googleapis/nodejs-ai-platform/issues/297)) ([1e6dcb6](https://github.com/googleapis/nodejs-ai-platform/commit/1e6dcb65e39c0c9e9ec211a743c38ee8385a3423))
* add Examples to Explanation related messages in aiplatform v1beta1 explanation.proto ([#307](https://github.com/googleapis/nodejs-ai-platform/issues/307)) ([c69ac2b](https://github.com/googleapis/nodejs-ai-platform/commit/c69ac2bb8d64e443fa4296204a770f5120cb9682))
* add IAM policy to aiplatform_v1beta1.yaml ([#308](https://github.com/googleapis/nodejs-ai-platform/issues/308)) ([6557767](https://github.com/googleapis/nodejs-ai-platform/commit/6557767c67d2b4461aa8e7c44d10b1572eceefeb))
* add JOB_STATE_UPDATING to JobState in aiplatform v1beta1 job_state.proto ([863748a](https://github.com/googleapis/nodejs-ai-platform/commit/863748aa5ca44064a8affafda5e5ae050b948a03))
* add LatestMonitoringPipelineMetadata to ModelDeploymentMonitoringJob in aiplatform v1beta1 model_deployment_monitoring_job.proto ([863748a](https://github.com/googleapis/nodejs-ai-platform/commit/863748aa5ca44064a8affafda5e5ae050b948a03))
* add ListModelVersion, DeleteModelVersion, and MergeVersionAliases rpcs to aiplatform v1beta1 model_service.proto ([863748a](https://github.com/googleapis/nodejs-ai-platform/commit/863748aa5ca44064a8affafda5e5ae050b948a03))
* add MfsMount in aiplatform v1beta1 machine_resources.proto ([863748a](https://github.com/googleapis/nodejs-ai-platform/commit/863748aa5ca44064a8affafda5e5ae050b948a03))
* add model_id and parent_model to TrainingPipeline in aiplatform v1beta1 training_pipeline.proto ([863748a](https://github.com/googleapis/nodejs-ai-platform/commit/863748aa5ca44064a8affafda5e5ae050b948a03))
* add model_version_id to DeployedModel in aiplatform v1beta1 endpoint.proto ([863748a](https://github.com/googleapis/nodejs-ai-platform/commit/863748aa5ca44064a8affafda5e5ae050b948a03))
* add model_version_id to PredictResponse in aiplatform v1beta1 prediction_service.proto ([863748a](https://github.com/googleapis/nodejs-ai-platform/commit/863748aa5ca44064a8affafda5e5ae050b948a03))
* add model_version_id to UploadModelRequest and UploadModelResponse in aiplatform v1beta1 model_service.proto ([863748a](https://github.com/googleapis/nodejs-ai-platform/commit/863748aa5ca44064a8affafda5e5ae050b948a03))
* add nfs_mounts to WorkPoolSpec in aiplatform v1beta1 custom_job.proto ([863748a](https://github.com/googleapis/nodejs-ai-platform/commit/863748aa5ca44064a8affafda5e5ae050b948a03))
* add PredictRequestResponseLoggingConfig to aiplatform v1beta1 endpoint.proto ([863748a](https://github.com/googleapis/nodejs-ai-platform/commit/863748aa5ca44064a8affafda5e5ae050b948a03))
* add reserved_ip_ranges to CustomJobSpec in aiplatform v1 custom_job.proto ([#286](https://github.com/googleapis/nodejs-ai-platform/issues/286)) ([863748a](https://github.com/googleapis/nodejs-ai-platform/commit/863748aa5ca44064a8affafda5e5ae050b948a03))
* add reserved_ip_ranges to CustomJobSpec in aiplatform v1beta1 custom_job.proto ([863748a](https://github.com/googleapis/nodejs-ai-platform/commit/863748aa5ca44064a8affafda5e5ae050b948a03))
* add version_id to Model in aiplatform v1beta1 model.proto ([863748a](https://github.com/googleapis/nodejs-ai-platform/commit/863748aa5ca44064a8affafda5e5ae050b948a03))
* rename Similarity to Examples, and similarity to examples in ExplanationParameters in aiplatform v1beta1 explanation.proto ([863748a](https://github.com/googleapis/nodejs-ai-platform/commit/863748aa5ca44064a8affafda5e5ae050b948a03))
* **samples:** add create-featurestore samples ([#317](https://github.com/googleapis/nodejs-ai-platform/issues/317)) ([5876d81](https://github.com/googleapis/nodejs-ai-platform/commit/5876d81482b7bd247ef02a6b9f7f062bfae9d4b0))


### Bug Fixes

* added retries to flaky test ([#299](https://github.com/googleapis/nodejs-ai-platform/issues/299)) ([ffc9a3f](https://github.com/googleapis/nodejs-ai-platform/commit/ffc9a3f972802d8483d1f0c02a685ea8f1500998))


### Build System

* update library to use Node 12 ([#304](https://github.com/googleapis/nodejs-ai-platform/issues/304)) ([0679cda](https://github.com/googleapis/nodejs-ai-platform/commit/0679cda64a937bd68f27705b607a406ed01d02c4))

## [1.19.0](https://github.com/googleapis/nodejs-ai-platform/compare/v1.18.0...v1.19.0) (2022-03-24)


### Features

* add data_item_schema_uri, annotation_schema_uri, explanation_specs to ModelEvaluationExplanationSpec in aiplatform v1 model_evaluation.proto ([b769264](https://github.com/googleapis/nodejs-ai-platform/commit/b769264263afb2e8e26e3b841c7ae0be7c67e0be))
* add ImportModelEvaluation in aiplatform v1 model_service.proto ([#280](https://github.com/googleapis/nodejs-ai-platform/issues/280)) ([b769264](https://github.com/googleapis/nodejs-ai-platform/commit/b769264263afb2e8e26e3b841c7ae0be7c67e0be))
* add ImportModelEvaluation in aiplatform v1beta1 model_service.proto ([b769264](https://github.com/googleapis/nodejs-ai-platform/commit/b769264263afb2e8e26e3b841c7ae0be7c67e0be))

## [1.18.0](https://github.com/googleapis/nodejs-ai-platform/compare/v1.17.0...v1.18.0) (2022-03-17)


### Features

* add `service_account` to `BatchPredictionJob` in aiplatform `v1beta1` `batch_prediction_job.proto` ([#278](https://github.com/googleapis/nodejs-ai-platform/issues/278)) ([1f05428](https://github.com/googleapis/nodejs-ai-platform/commit/1f05428689df1408ea1979976dc93bfbb5cfaaf6))

## [1.17.0](https://github.com/googleapis/nodejs-ai-platform/compare/v1.16.0...v1.17.0) (2022-03-14)


### Features

* add monitoring_config to EntityType in aiplatform v1 entity_type.proto ([#277](https://github.com/googleapis/nodejs-ai-platform/issues/277)) ([d9b8b06](https://github.com/googleapis/nodejs-ai-platform/commit/d9b8b06644dccf50a8d796ecd175d87a27466350))
* add PredictRequestResponseLoggingConfig to Endpoint in aiplatform v1 endpoint.proto ([#275](https://github.com/googleapis/nodejs-ai-platform/issues/275)) ([3e5dc67](https://github.com/googleapis/nodejs-ai-platform/commit/3e5dc673ce93c4f7323f86a4f303d10dd1dd0655))

## [1.16.0](https://github.com/googleapis/nodejs-ai-platform/compare/v1.15.0...v1.16.0) (2022-02-22)


### Features

* add TPU_V2 & TPU_V3 values to AcceleratorType in aiplatform v1/v1beta1 accelerator_type.proto ([#269](https://github.com/googleapis/nodejs-ai-platform/issues/269)) ([6e9290f](https://github.com/googleapis/nodejs-ai-platform/commit/6e9290f8ce1c85dacae6d23a1bedb46d37a0d3cd))

## [1.15.0](https://github.com/googleapis/nodejs-ai-platform/compare/v1.14.1...v1.15.0) (2022-02-08)


### Features

* add dedicated_resources to DeployedIndex in aiplatform v1beta1 index_endpoint.proto feat: add Scaling to OnlineServingConfig in aiplatform v1beta1 featurestore.proto chore: sort imports ([#262](https://github.com/googleapis/nodejs-ai-platform/issues/262)) ([f3e31ec](https://github.com/googleapis/nodejs-ai-platform/commit/f3e31ecc11282ce5280b5149873bc65df4bf788b))
* add dedicated_resources to DeployedIndex message in aiplatform v1 index_endpoint.proto chore: sort imports ([#261](https://github.com/googleapis/nodejs-ai-platform/issues/261)) ([8f7e61e](https://github.com/googleapis/nodejs-ai-platform/commit/8f7e61e32396e1c1dda6290a33b8edaf83a9b2a0))

### [1.14.1](https://github.com/googleapis/nodejs-ai-platform/compare/v1.14.0...v1.14.1) (2022-01-13)


### Bug Fixes

* remove duplicate sample ([#243](https://github.com/googleapis/nodejs-ai-platform/issues/243)) ([827edd9](https://github.com/googleapis/nodejs-ai-platform/commit/827edd96ecd9e04b7de8dbda3e4a535b9853628b))

## [1.14.0](https://www.github.com/googleapis/nodejs-ai-platform/compare/v1.13.0...v1.14.0) (2021-12-11)


### Features

* add enable_private_service_connect field to Endpoint ([#238](https://www.github.com/googleapis/nodejs-ai-platform/issues/238)) ([bac788d](https://www.github.com/googleapis/nodejs-ai-platform/commit/bac788d9e229927d3c9b30178523084ce45db14f))
* add enable_private_service_connect field to Endpoint ([#239](https://www.github.com/googleapis/nodejs-ai-platform/issues/239)) ([e9cbc46](https://www.github.com/googleapis/nodejs-ai-platform/commit/e9cbc460a7af44b8dbbe038b37a33dbd1050928a))
* add endpoint_id to CreateEndpointRequest and method signature to CreateEndpoint ([e9cbc46](https://www.github.com/googleapis/nodejs-ai-platform/commit/e9cbc460a7af44b8dbbe038b37a33dbd1050928a))
* add id field to DeployedModel ([e9cbc46](https://www.github.com/googleapis/nodejs-ai-platform/commit/e9cbc460a7af44b8dbbe038b37a33dbd1050928a))
* add method signature to CreateFeatureStore, CreateEntityType, CreateFeature ([e9cbc46](https://www.github.com/googleapis/nodejs-ai-platform/commit/e9cbc460a7af44b8dbbe038b37a33dbd1050928a))
* add network and enable_private_service_connect to IndexEndpoint ([e9cbc46](https://www.github.com/googleapis/nodejs-ai-platform/commit/e9cbc460a7af44b8dbbe038b37a33dbd1050928a))
* add service_attachment field to PrivateEndpoints ([e9cbc46](https://www.github.com/googleapis/nodejs-ai-platform/commit/e9cbc460a7af44b8dbbe038b37a33dbd1050928a))
* add service_attachment to IndexPrivateEndpoints ([e9cbc46](https://www.github.com/googleapis/nodejs-ai-platform/commit/e9cbc460a7af44b8dbbe038b37a33dbd1050928a))
* add stratified_split field to training_pipeline InputDataConfig ([e9cbc46](https://www.github.com/googleapis/nodejs-ai-platform/commit/e9cbc460a7af44b8dbbe038b37a33dbd1050928a))
* Exposing a field for v1 CustomJob-Tensorboard integration. ([03881ff](https://www.github.com/googleapis/nodejs-ai-platform/commit/03881ff26fbbd72a81cbf0d162178e8d6aadeaac))
* Tensorboard v1 protos release ([#234](https://www.github.com/googleapis/nodejs-ai-platform/issues/234)) ([03881ff](https://www.github.com/googleapis/nodejs-ai-platform/commit/03881ff26fbbd72a81cbf0d162178e8d6aadeaac))

## [1.13.0](https://www.github.com/googleapis/nodejs-ai-platform/compare/v1.12.0...v1.13.0) (2021-11-11)


### Features

* Adds support for `google.protobuf.Value` pipeline parameters in the `parameter_values` field ([#218](https://www.github.com/googleapis/nodejs-ai-platform/issues/218)) ([d05a598](https://www.github.com/googleapis/nodejs-ai-platform/commit/d05a598d095ea89cb6a0f385c2f82e9a8224b21f))

## [1.12.0](https://www.github.com/googleapis/nodejs-ai-platform/compare/v1.11.0...v1.12.0) (2021-10-27)


### Features

* add featurestore service to aiplatform v1 feat: add metadata service to aiplatform v1 ([#211](https://www.github.com/googleapis/nodejs-ai-platform/issues/211)) ([b8a92f2](https://www.github.com/googleapis/nodejs-ai-platform/commit/b8a92f2db9ed21ace33beaae3560e8c48ff2dbf9))
* add Similarity to explanation in aiplatform v1beta1 feat: add EncryptionSpec to featurestore in aiplatform v1beta1 feat: add PipelineTaskStatus to pipeline_job in aiplatform v1beta1 feat: add BatchReadTensorboardTimeSeriesData to tensorboard_serv... ([#210](https://www.github.com/googleapis/nodejs-ai-platform/issues/210)) ([ae37fb8](https://www.github.com/googleapis/nodejs-ai-platform/commit/ae37fb8bc56c4b4c9eb668cd87e85b307b331798))
* add vizier service to aiplatform v1 ([#205](https://www.github.com/googleapis/nodejs-ai-platform/issues/205)) ([4d37e14](https://www.github.com/googleapis/nodejs-ai-platform/commit/4d37e14eb75e5e5b8c5a0d34a7901484edf64fce))


### Bug Fixes

* Remove invalid resource annotations ([#213](https://www.github.com/googleapis/nodejs-ai-platform/issues/213)) ([2f32395](https://www.github.com/googleapis/nodejs-ai-platform/commit/2f3239515bc4e8203c76f38f0830e80ea0f57507))

## [1.11.0](https://www.github.com/googleapis/nodejs-ai-platform/compare/v1.10.1...v1.11.0) (2021-09-22)


### Features

* add deployment_group to DeployedIndex in aiplatform_v1beta1 ([35b45f1](https://www.github.com/googleapis/nodejs-ai-platform/commit/35b45f196743df8f27439cfc1b3bb0bdb13f4a7c))
* add model_deployment_monitoring_job to Endpoint in aiplatform_v1beta1 ([35b45f1](https://www.github.com/googleapis/nodejs-ai-platform/commit/35b45f196743df8f27439cfc1b3bb0bdb13f4a7c))
* add ModelEvaluationExplanationSpec in aiplatform_v1beta1 ([35b45f1](https://www.github.com/googleapis/nodejs-ai-platform/commit/35b45f196743df8f27439cfc1b3bb0bdb13f4a7c))
* add prediction service RPC RawPredict to aiplatform_v1beta1 feat: add tensorboard service RPCs to aiplatform_v1beta1: BatchCreateTensorboardRuns, BatchCreateTensorboardTimeSeries, WriteTensorboardExperimentData feat: add model_deployment_monitori... ([#196](https://www.github.com/googleapis/nodejs-ai-platform/issues/196)) ([35b45f1](https://www.github.com/googleapis/nodejs-ai-platform/commit/35b45f196743df8f27439cfc1b3bb0bdb13f4a7c))
* add tensorboard service RPCs to aiplatform_v1beta1: BatchCreateTensorboardRuns, BatchCreateTensorboardTimeSeries, WriteTensorboardExperimentData ([35b45f1](https://www.github.com/googleapis/nodejs-ai-platform/commit/35b45f196743df8f27439cfc1b3bb0bdb13f4a7c))
* add Vizier service to aiplatform v1 ([#197](https://www.github.com/googleapis/nodejs-ai-platform/issues/197)) ([7da1062](https://www.github.com/googleapis/nodejs-ai-platform/commit/7da106232ff10fa538a96939db888d6143497773))
* turns on self-signed JWT feature flag ([#191](https://www.github.com/googleapis/nodejs-ai-platform/issues/191)) ([7e9b76f](https://www.github.com/googleapis/nodejs-ai-platform/commit/7e9b76fbba979235afc9dbfd1396c4e411ccb530))


### Bug Fixes

* **deps:** require google-gax v2.24.1 ([#190](https://www.github.com/googleapis/nodejs-ai-platform/issues/190)) ([ce5177f](https://www.github.com/googleapis/nodejs-ai-platform/commit/ce5177f34896aa8fdd94e6aae169c09a33e34939))
* promote library to GA ([#201](https://www.github.com/googleapis/nodejs-ai-platform/issues/201)) ([7a9bb5e](https://www.github.com/googleapis/nodejs-ai-platform/commit/7a9bb5e2c1af0b0466cdf26c30c3206910a15e94))

### [1.10.1](https://www.github.com/googleapis/nodejs-ai-platform/compare/v1.10.0...v1.10.1) (2021-08-09)


### Bug Fixes

* **build:** migrate to using main branch ([#184](https://www.github.com/googleapis/nodejs-ai-platform/issues/184)) ([5d835fb](https://www.github.com/googleapis/nodejs-ai-platform/commit/5d835fba1163b6cd8b72cf5274e0b0417db8c19e))

## [1.10.0](https://www.github.com/googleapis/nodejs-ai-platform/compare/v1.9.1...v1.10.0) (2021-07-23)


### Features

* Adds BigQuery output table field to batch prediction job output config ([22f1f67](https://www.github.com/googleapis/nodejs-ai-platform/commit/22f1f673b9feeb3230e07a03253912261238b956))
* Adds CustomJob.web_access_uris, CustomJob.enable_web_access fields ([22f1f67](https://www.github.com/googleapis/nodejs-ai-platform/commit/22f1f673b9feeb3230e07a03253912261238b956))
* Adds Endpoint.network, Endpoint.private_endpoints fields and PrivateEndpoints message ([22f1f67](https://www.github.com/googleapis/nodejs-ai-platform/commit/22f1f673b9feeb3230e07a03253912261238b956))
* Adds Execution.State constants: CACHED and CANCELLED ([22f1f67](https://www.github.com/googleapis/nodejs-ai-platform/commit/22f1f673b9feeb3230e07a03253912261238b956))
* Adds Feature Store features ([22f1f67](https://www.github.com/googleapis/nodejs-ai-platform/commit/22f1f673b9feeb3230e07a03253912261238b956))
* Adds fields to Study message ([22f1f67](https://www.github.com/googleapis/nodejs-ai-platform/commit/22f1f673b9feeb3230e07a03253912261238b956))
* Adds IndexEndpoint.private_ip_ranges field ([22f1f67](https://www.github.com/googleapis/nodejs-ai-platform/commit/22f1f673b9feeb3230e07a03253912261238b956))
* Adds IndexEndpointService.deployed_index_id field ([22f1f67](https://www.github.com/googleapis/nodejs-ai-platform/commit/22f1f673b9feeb3230e07a03253912261238b956))
* Adds MetadataService.DeleteArtifact and DeleteExecution methods ([22f1f67](https://www.github.com/googleapis/nodejs-ai-platform/commit/22f1f673b9feeb3230e07a03253912261238b956))
* Adds ModelMonitoringObjectConfig.explanation_config field and ModelMonitoringObjectConfig.ExplanationConfig message ([22f1f67](https://www.github.com/googleapis/nodejs-ai-platform/commit/22f1f673b9feeb3230e07a03253912261238b956))
* Adds ModelMonitoringObjectiveConfig.TrainingPredictionSkewDetectionConfig.attribution_score_skew_thresholds and ModelMonitoringObjectiveConfig.PredictionDriftDetectionConfig.attribution_score_drift_threshold fields ([22f1f67](https://www.github.com/googleapis/nodejs-ai-platform/commit/22f1f673b9feeb3230e07a03253912261238b956))
* Removes breaking change from v1 version of AI Platform protos ([22f1f67](https://www.github.com/googleapis/nodejs-ai-platform/commit/22f1f673b9feeb3230e07a03253912261238b956))

### [1.9.1](https://www.github.com/googleapis/nodejs-ai-platform/compare/v1.9.0...v1.9.1) (2021-07-21)


### Bug Fixes

* Updating WORKSPACE files to use the newest version of the Typescript generator. ([#172](https://www.github.com/googleapis/nodejs-ai-platform/issues/172)) ([d22eceb](https://www.github.com/googleapis/nodejs-ai-platform/commit/d22ecebc4693b63516a69753c19fa7a7a400464f))

## [1.9.0](https://www.github.com/googleapis/nodejs-ai-platform/compare/v1.8.0...v1.9.0) (2021-07-15)


### Features

* Adds additional_experiments field to AutoMlTablesInputs ([#170](https://www.github.com/googleapis/nodejs-ai-platform/issues/170)) ([4a0a502](https://www.github.com/googleapis/nodejs-ai-platform/commit/4a0a502bb1a01efb701202098524f0020d1faad4))

## [1.8.0](https://www.github.com/googleapis/nodejs-ai-platform/compare/v1.7.4...v1.8.0) (2021-07-14)


### Features

* Removes AcceleratorType.TPU_V2 and TPU_V3 constants ([#167](https://www.github.com/googleapis/nodejs-ai-platform/issues/167)) ([72d153e](https://www.github.com/googleapis/nodejs-ai-platform/commit/72d153ea7732a0d4b73a4ab8d727679523adf6df))

### [1.7.4](https://www.github.com/googleapis/nodejs-ai-platform/compare/v1.7.3...v1.7.4) (2021-07-12)


### Bug Fixes

* **deps:** google-gax v2.17.1 ([#164](https://www.github.com/googleapis/nodejs-ai-platform/issues/164)) ([1ec11fc](https://www.github.com/googleapis/nodejs-ai-platform/commit/1ec11fc4fac373ee1a28ac10676fbfe9fc5535d1))

### [1.7.3](https://www.github.com/googleapis/nodejs-ai-platform/compare/v1.7.2...v1.7.3) (2021-06-29)


### Bug Fixes

* add C#, PHP and Ruby options for all AI Platform protos ([#158](https://www.github.com/googleapis/nodejs-ai-platform/issues/158)) ([da0babf](https://www.github.com/googleapis/nodejs-ai-platform/commit/da0babf54c6b074b8dc3d0354f8a57b9bc0cefc1))
* **deps:** google-gax v2.17.0 with mTLS ([#161](https://www.github.com/googleapis/nodejs-ai-platform/issues/161)) ([8e7d46f](https://www.github.com/googleapis/nodejs-ai-platform/commit/8e7d46f1252c4e89a663dd28b70b49bf269f091a))

### [1.7.2](https://www.github.com/googleapis/nodejs-ai-platform/compare/v1.7.1...v1.7.2) (2021-06-22)


### Bug Fixes

* make request optional in all cases ([#152](https://www.github.com/googleapis/nodejs-ai-platform/issues/152)) ([96b9fba](https://www.github.com/googleapis/nodejs-ai-platform/commit/96b9fba9b66807ac5ea2c5826d5fd3f4e895dc9e))

### [1.7.1](https://www.github.com/googleapis/nodejs-ai-platform/compare/v1.7.0...v1.7.1) (2021-06-02)


### Bug Fixes

* GoogleAdsError missing using generator version after 1.3.0 ([#141](https://www.github.com/googleapis/nodejs-ai-platform/issues/141)) ([743c71b](https://www.github.com/googleapis/nodejs-ai-platform/commit/743c71b3cbfd1a1ce62e7bb1395fc599e3690a41))

## [1.7.0](https://www.github.com/googleapis/nodejs-ai-platform/compare/v1.6.0...v1.7.0) (2021-05-11)


### ⚠ BREAKING CHANGES

* BREAKING_CHANGE: remove display_name from FeatureStore feat: add invalid_row_count to ImportFeatureValuesResponse and ImportFeatureValuesOperationMetadata (#124)
* BREAKING CHANGE: remove unsupported accelerator types feat: add aiplatform API Vizier service (#92)

### Features

* add featurestore, index, metadata, monitoring, pipeline, and tensorboard services to aiplatform v1beta1. ([#118](https://www.github.com/googleapis/nodejs-ai-platform/issues/118)) ([c3ce821](https://www.github.com/googleapis/nodejs-ai-platform/commit/c3ce8215218dff6d6d42536c9307a05c00bf458b))
* BREAKING CHANGE: remove unsupported accelerator types feat: add aiplatform API Vizier service ([#92](https://www.github.com/googleapis/nodejs-ai-platform/issues/92)) ([23f184d](https://www.github.com/googleapis/nodejs-ai-platform/commit/23f184dffb8482fc032234ae652e75999eb93203))


### Bug Fixes

* BREAKING_CHANGE: remove display_name from FeatureStore feat: add invalid_row_count to ImportFeatureValuesResponse and ImportFeatureValuesOperationMetadata ([#124](https://www.github.com/googleapis/nodejs-ai-platform/issues/124)) ([34b8548](https://www.github.com/googleapis/nodejs-ai-platform/commit/34b85485bc7ead9d86daebfd736a0d938d431566))
* **deps:** require google-gax v2.12.0 ([#120](https://www.github.com/googleapis/nodejs-ai-platform/issues/120)) ([2b58ecb](https://www.github.com/googleapis/nodejs-ai-platform/commit/2b58ecbaad65f8718bfaaf125664ce86cb1ae1d8))
* use require() to load JSON protos ([#122](https://www.github.com/googleapis/nodejs-ai-platform/issues/122)) ([3f26575](https://www.github.com/googleapis/nodejs-ai-platform/commit/3f265753f9c9e9a52966e549c9f74c0fe31002b1))

## [1.6.0](https://www.github.com/googleapis/nodejs-ai-platform/compare/v1.5.0...v1.6.0) (2021-03-11)


### Features

* removes forecasting (time_series_forecasting.proto) from public v1beta1 protos ([#86](https://www.github.com/googleapis/nodejs-ai-platform/issues/86)) ([ccc1953](https://www.github.com/googleapis/nodejs-ai-platform/commit/ccc19537477a57ab36b13072ff6d569eba29d0a7))


### Bug Fixes

* adds index.ts to excludes ([#81](https://www.github.com/googleapis/nodejs-ai-platform/issues/81)) ([d8c6bbb](https://www.github.com/googleapis/nodejs-ai-platform/commit/d8c6bbb67ca1ea81268674e9666536688f0cd720))

## [1.5.0](https://www.github.com/googleapis/nodejs-ai-platform/compare/v1.4.0...v1.5.0) (2021-02-17)


### Features

* ImageSegmentationPredictionResult.category_mask field changed to string data type ([#76](https://www.github.com/googleapis/nodejs-ai-platform/issues/76)) ([ec47129](https://www.github.com/googleapis/nodejs-ai-platform/commit/ec471298fe3135165745175720173203fe8ff016))

## [1.4.0](https://www.github.com/googleapis/nodejs-ai-platform/compare/v1.3.0...v1.4.0) (2021-02-12)


### Features

* add encryption_spec to aiplatform v1beta1 ([#63](https://www.github.com/googleapis/nodejs-ai-platform/issues/63)) ([c3256aa](https://www.github.com/googleapis/nodejs-ai-platform/commit/c3256aae40e39c113dfdf6be9f28f0cb09a7b600))
* adds v1 version of library ([#70](https://www.github.com/googleapis/nodejs-ai-platform/issues/70)) ([5a1b765](https://www.github.com/googleapis/nodejs-ai-platform/commit/5a1b765c03efdb5cbe82b1a34ccf70b86ebd8137))
* **samples:** add additional samples to library ([#25](https://www.github.com/googleapis/nodejs-ai-platform/issues/25)) ([5a7f5db](https://www.github.com/googleapis/nodejs-ai-platform/commit/5a7f5db965a232a98f1bcf18f4f769db5ba3644c))
* updates enhanced client library veneer and samples ([#73](https://www.github.com/googleapis/nodejs-ai-platform/issues/73)) ([59e293f](https://www.github.com/googleapis/nodejs-ai-platform/commit/59e293fa90ea2894b8c135d2ef18662564ceae2c))


### Bug Fixes

* reduces image object detection test file size ([#69](https://www.github.com/googleapis/nodejs-ai-platform/issues/69)) ([fb4308c](https://www.github.com/googleapis/nodejs-ai-platform/commit/fb4308ce09a9fc029996c5d9cd29ae02dc45adea))

## [1.3.0](https://www.github.com/googleapis/nodejs-ai-platform/compare/v1.2.0...v1.3.0) (2021-01-14)


### Features

* **samples:** adds samples for enhanced version of library ([#16](https://www.github.com/googleapis/nodejs-ai-platform/issues/16)) ([aef443c](https://www.github.com/googleapis/nodejs-ai-platform/commit/aef443c41b8a9a2199e0c5b100a5ab91444b0dfe))

## [1.2.0](https://www.github.com/googleapis/nodejs-ai-platform/compare/v1.1.0...v1.2.0) (2021-01-09)


### Features

* adds cleaner utility for orphaned resources ([#34](https://www.github.com/googleapis/nodejs-ai-platform/issues/34)) ([7c09258](https://www.github.com/googleapis/nodejs-ai-platform/commit/7c09258eb99d9c40ba7bf28b6d84434d6bb8a5b9))
* adds style enumeration ([#37](https://www.github.com/googleapis/nodejs-ai-platform/issues/37)) ([81c1515](https://www.github.com/googleapis/nodejs-ai-platform/commit/81c15150f55c4ef20359c1e48530f2c9bcd0f64b))

## [1.1.0](https://www.github.com/googleapis/nodejs-ai-platform/compare/v1.0.0...v1.1.0) (2021-01-07)


### Features

* adds enhancements to library ([#22](https://www.github.com/googleapis/nodejs-ai-platform/issues/22)) ([b697a38](https://www.github.com/googleapis/nodejs-ai-platform/commit/b697a38f696ab14b3a7ce9563f2ed5449eeeab4f))

## 1.0.0 (2020-11-10)


### ⚠ BREAKING CHANGES

* initial generation of library (#4)

### Features

* initial generation ([182128b](https://www.github.com/googleapis/nodejs-ai-platform/commit/182128bfa593c787e2f0970ba224a68595b45971))
* initial generation of library ([#4](https://www.github.com/googleapis/nodejs-ai-platform/issues/4)) ([1b8db30](https://www.github.com/googleapis/nodejs-ai-platform/commit/1b8db30e243aac5d9dab74b00431c81dbf412a66))
