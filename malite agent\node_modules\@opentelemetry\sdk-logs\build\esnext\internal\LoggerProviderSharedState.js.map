{"version": 3, "file": "LoggerProviderSharedState.js", "sourceRoot": "", "sources": ["../../../src/internal/LoggerProviderSharedState.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAMH,OAAO,EAAE,sBAAsB,EAAE,MAAM,kCAAkC,CAAC;AAE1E,MAAM,OAAO,yBAAyB;IAKpC,YACW,QAAmB,EACnB,uBAA+B,EAC/B,eAA0C;QAF1C,aAAQ,GAAR,QAAQ,CAAW;QACnB,4BAAuB,GAAvB,uBAAuB,CAAQ;QAC/B,oBAAe,GAAf,eAAe,CAA2B;QAP5C,YAAO,GAAwB,IAAI,GAAG,EAAE,CAAC;QAEzC,kCAA6B,GAAyB,EAAE,CAAC;QAOhE,IAAI,CAAC,eAAe,GAAG,IAAI,sBAAsB,EAAE,CAAC;IACtD,CAAC;CACF", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Logger } from '@opentelemetry/api-logs';\nimport { IResource } from '@opentelemetry/resources';\nimport { LogRecordProcessor } from '../LogRecordProcessor';\nimport { LogRecordLimits } from '../types';\nimport { NoopLogRecordProcessor } from '../export/NoopLogRecordProcessor';\n\nexport class LoggerProviderSharedState {\n  readonly loggers: Map<string, Logger> = new Map();\n  activeProcessor: LogRecordProcessor;\n  readonly registeredLogRecordProcessors: LogRecordProcessor[] = [];\n\n  constructor(\n    readonly resource: IResource,\n    readonly forceFlushTimeoutMillis: number,\n    readonly logRecordLimits: Required<LogRecordLimits>\n  ) {\n    this.activeProcessor = new NoopLogRecordProcessor();\n  }\n}\n"]}