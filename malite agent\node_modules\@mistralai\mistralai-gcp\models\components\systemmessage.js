"use strict";
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SystemMessage$ = exports.SystemMessage$outboundSchema = exports.SystemMessage$inboundSchema = exports.Role$ = exports.Role$outboundSchema = exports.Role$inboundSchema = exports.SystemMessageContent$ = exports.SystemMessageContent$outboundSchema = exports.SystemMessageContent$inboundSchema = exports.Role = void 0;
exports.systemMessageContentToJSON = systemMessageContentToJSON;
exports.systemMessageContentFromJSON = systemMessageContentFromJSON;
exports.systemMessageToJSON = systemMessageToJSON;
exports.systemMessageFromJSON = systemMessageFromJSON;
const z = __importStar(require("zod"));
const schemas_js_1 = require("../../lib/schemas.js");
const textchunk_js_1 = require("./textchunk.js");
exports.Role = {
    System: "system",
};
/** @internal */
exports.SystemMessageContent$inboundSchema = z.union([z.string(), z.array(textchunk_js_1.TextChunk$inboundSchema)]);
/** @internal */
exports.SystemMessageContent$outboundSchema = z.union([z.string(), z.array(textchunk_js_1.TextChunk$outboundSchema)]);
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var SystemMessageContent$;
(function (SystemMessageContent$) {
    /** @deprecated use `SystemMessageContent$inboundSchema` instead. */
    SystemMessageContent$.inboundSchema = exports.SystemMessageContent$inboundSchema;
    /** @deprecated use `SystemMessageContent$outboundSchema` instead. */
    SystemMessageContent$.outboundSchema = exports.SystemMessageContent$outboundSchema;
})(SystemMessageContent$ || (exports.SystemMessageContent$ = SystemMessageContent$ = {}));
function systemMessageContentToJSON(systemMessageContent) {
    return JSON.stringify(exports.SystemMessageContent$outboundSchema.parse(systemMessageContent));
}
function systemMessageContentFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.SystemMessageContent$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'SystemMessageContent' from JSON`);
}
/** @internal */
exports.Role$inboundSchema = z.nativeEnum(exports.Role);
/** @internal */
exports.Role$outboundSchema = exports.Role$inboundSchema;
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var Role$;
(function (Role$) {
    /** @deprecated use `Role$inboundSchema` instead. */
    Role$.inboundSchema = exports.Role$inboundSchema;
    /** @deprecated use `Role$outboundSchema` instead. */
    Role$.outboundSchema = exports.Role$outboundSchema;
})(Role$ || (exports.Role$ = Role$ = {}));
/** @internal */
exports.SystemMessage$inboundSchema = z.object({
    content: z.union([z.string(), z.array(textchunk_js_1.TextChunk$inboundSchema)]),
    role: exports.Role$inboundSchema.default("system"),
});
/** @internal */
exports.SystemMessage$outboundSchema = z.object({
    content: z.union([z.string(), z.array(textchunk_js_1.TextChunk$outboundSchema)]),
    role: exports.Role$outboundSchema.default("system"),
});
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var SystemMessage$;
(function (SystemMessage$) {
    /** @deprecated use `SystemMessage$inboundSchema` instead. */
    SystemMessage$.inboundSchema = exports.SystemMessage$inboundSchema;
    /** @deprecated use `SystemMessage$outboundSchema` instead. */
    SystemMessage$.outboundSchema = exports.SystemMessage$outboundSchema;
})(SystemMessage$ || (exports.SystemMessage$ = SystemMessage$ = {}));
function systemMessageToJSON(systemMessage) {
    return JSON.stringify(exports.SystemMessage$outboundSchema.parse(systemMessage));
}
function systemMessageFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.SystemMessage$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'SystemMessage' from JSON`);
}
//# sourceMappingURL=systemmessage.js.map