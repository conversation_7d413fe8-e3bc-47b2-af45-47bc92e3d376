{"version": 3, "file": "SpanProcessor.js", "sourceRoot": "", "sources": ["../../src/SpanProcessor.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Context } from '@opentelemetry/api';\nimport { ReadableSpan } from './export/ReadableSpan';\nimport { Span } from './Span';\n\n/**\n * SpanProcessor is the interface Tracer SDK uses to allow synchronous hooks\n * for when a {@link Span} is started or when a {@link Span} is ended.\n */\nexport interface SpanProcessor {\n  /**\n   * Forces to export all finished spans\n   */\n  forceFlush(): Promise<void>;\n\n  /**\n   * Called when a {@link Span} is started, if the `span.isRecording()`\n   * returns true.\n   * @param span the Span that just started.\n   */\n  onStart(span: Span, parentContext: Context): void;\n\n  /**\n   * Called when a {@link ReadableSpan} is ended, if the `span.isRecording()`\n   * returns true.\n   * @param span the Span that just ended.\n   */\n  onEnd(span: ReadableSpan): void;\n\n  /**\n   * Shuts down the processor. Called when SDK is shut down. This is an\n   * opportunity for processor to do any cleanup required.\n   */\n  shutdown(): Promise<void>;\n}\n"]}