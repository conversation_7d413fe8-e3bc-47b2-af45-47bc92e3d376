// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.aiplatform.v1;

import "google/api/field_behavior.proto";

option csharp_namespace = "Google.Cloud.AIPlatform.V1";
option go_package = "cloud.google.com/go/aiplatform/apiv1/aiplatformpb;aiplatformpb";
option java_multiple_files = true;
option java_outer_classname = "FeatureSelectorProto";
option java_package = "com.google.cloud.aiplatform.v1";
option php_namespace = "Google\\Cloud\\AIPlatform\\V1";
option ruby_package = "Google::Cloud::AIPlatform::V1";

// Matcher for Features of an EntityType by Feature ID.
message IdMatcher {
  // Required. The following are accepted as `ids`:
  //
  //  * A single-element list containing only `*`, which selects all Features
  //  in the target EntityType, or
  //  * A list containing only Feature IDs, which selects only Features with
  //  those IDs in the target EntityType.
  repeated string ids = 1 [(google.api.field_behavior) = REQUIRED];
}

// Selector for Features of an EntityType.
message FeatureSelector {
  // Required. Matches Features based on ID.
  IdMatcher id_matcher = 1 [(google.api.field_behavior) = REQUIRED];
}
