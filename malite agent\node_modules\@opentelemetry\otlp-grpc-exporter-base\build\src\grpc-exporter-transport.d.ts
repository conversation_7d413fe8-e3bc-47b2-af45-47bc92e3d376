/// <reference types="node" />
import type { Metada<PERSON>, ChannelCredentials } from '@grpc/grpc-js';
import { ExportResponse } from './export-response';
import { IExporterTransport } from './exporter-transport';
export declare function createInsecureCredentials(): ChannelCredentials;
export declare function createSslCredentials(rootCert?: Buffer, privateKey?: B<PERSON><PERSON>, certChain?: Buffer): ChannelCredentials;
export declare function createEmptyMetadata(): Metadata;
export interface GrpcExporterTransportParameters {
    grpcPath: string;
    grpcName: string;
    address: string;
    /**
     * NOTE: Ensure that you're only importing/requiring gRPC inside the function providing the channel credentials,
     * otherwise, gRPC and http/https instrumentations may break.
     *
     * For common cases, you can avoid to import/require gRPC your function by using
     *   - {@link createSslCredentials}
     *   - {@link createInsecureCredentials}
     */
    credentials: () => ChannelCredentials;
    /**
     * NOTE: Ensure that you're only importing/requiring gRPC inside the function providing the metadata,
     * otherwise, gRPC and http/https instrumentations may break.
     *
     * To avoid having to import/require gRPC from your function to create a new Metadata object,
     * use {@link createEmptyMetadata}
     */
    metadata: () => Metadata;
    compression: 'gzip' | 'none';
    timeoutMillis: number;
}
export declare class GrpcExporterTransport implements IExporterTransport {
    private _parameters;
    private _client?;
    private _metadata?;
    constructor(_parameters: GrpcExporterTransportParameters);
    shutdown(): void;
    send(data: Uint8Array): Promise<ExportResponse>;
}
//# sourceMappingURL=grpc-exporter-transport.d.ts.map