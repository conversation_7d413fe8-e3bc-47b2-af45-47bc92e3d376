/**
 * فحص صحة التطبيق للتأكد من أنه يعمل بشكل صحيح
 * يستخدم في Docker و Cloud Run للتحقق من حالة التطبيق
 */

import http from 'http';
import { fileURLToPath } from 'url';
import path from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// إعدادات فحص الصحة
const HEALTH_CHECK_PORT = process.env.PORT || 8080;
const HEALTH_CHECK_HOST = process.env.HOST || 'localhost';
const HEALTH_CHECK_TIMEOUT = 5000; // 5 ثوانٍ

/**
 * فحص الاتصال بالخدمة
 */
function checkService() {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: HEALTH_CHECK_HOST,
      port: HEALTH_CHECK_PORT,
      path: '/health',
      method: 'GET',
      timeout: HEALTH_CHECK_TIMEOUT,
    };

    const req = http.request(options, (res) => {
      if (res.statusCode === 200) {
        resolve('Service is healthy');
      } else {
        reject(new Error(`Service returned status code: ${res.statusCode}`));
      }
    });

    req.on('error', (error) => {
      reject(new Error(`Health check failed: ${error.message}`));
    });

    req.on('timeout', () => {
      req.destroy();
      reject(new Error('Health check timed out'));
    });

    req.end();
  });
}

/**
 * فحص الذاكرة
 */
function checkMemory() {
  const memUsage = process.memoryUsage();
  const maxMemory = 2 * 1024 * 1024 * 1024; // 2GB
  
  if (memUsage.heapUsed > maxMemory * 0.9) {
    throw new Error('Memory usage too high');
  }
  
  return 'Memory usage is normal';
}

/**
 * فحص متغيرات البيئة المطلوبة
 */
function checkEnvironment() {
  const requiredEnvVars = [
    'NODE_ENV',
    'GENKIT_ENV'
  ];
  
  const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);
  
  if (missingVars.length > 0) {
    throw new Error(`Missing environment variables: ${missingVars.join(', ')}`);
  }
  
  return 'Environment variables are set';
}

/**
 * تشغيل جميع فحوصات الصحة
 */
async function runHealthChecks() {
  const checks = [
    { name: 'Service', check: checkService },
    { name: 'Memory', check: checkMemory },
    { name: 'Environment', check: checkEnvironment },
  ];
  
  const results = [];
  
  for (const { name, check } of checks) {
    try {
      const result = await check();
      results.push({ name, status: 'OK', message: result });
    } catch (error) {
      results.push({ name, status: 'FAIL', message: error.message });
    }
  }
  
  return results;
}

/**
 * تشغيل فحص الصحة الرئيسي
 */
async function main() {
  try {
    console.log('🏥 بدء فحص صحة التطبيق...');
    
    const results = await runHealthChecks();
    const failedChecks = results.filter(result => result.status === 'FAIL');
    
    // طباعة النتائج
    results.forEach(result => {
      const icon = result.status === 'OK' ? '✅' : '❌';
      console.log(`${icon} ${result.name}: ${result.message}`);
    });
    
    if (failedChecks.length > 0) {
      console.error(`❌ فشل ${failedChecks.length} من ${results.length} فحوصات`);
      process.exit(1);
    } else {
      console.log(`✅ نجح جميع الفحوصات (${results.length}/${results.length})`);
      process.exit(0);
    }
    
  } catch (error) {
    console.error('❌ خطأ في فحص الصحة:', error.message);
    process.exit(1);
  }
}

// تشغيل فحص الصحة إذا تم استدعاء الملف مباشرة
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export { runHealthChecks, checkService, checkMemory, checkEnvironment };
