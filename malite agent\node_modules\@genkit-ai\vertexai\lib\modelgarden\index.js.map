{"version": 3, "sources": ["../../src/modelgarden/index.ts"], "sourcesContent": ["/**\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Genkit } from 'genkit';\nimport { GenkitPlugin, genkitPlugin } from 'genkit/plugin';\nimport { getDerivedParams } from '../common/index.js';\nimport { SUPPORTED_ANTHROPIC_MODELS, anthropicModel } from './anthropic.js';\nimport { SUPPORTED_MISTRAL_MODELS, mistralModel } from './mistral.js';\nimport {\n  SUPPORTED_OPENAI_FORMAT_MODELS,\n  modelGardenOpenaiCompatibleModel,\n} from './model_garden.js';\nimport { PluginOptions } from './types.js';\n\n/**\n * Add Google Cloud Vertex AI Rerankers API to Genkit.\n */\nexport function vertexAIModelGarden(options: PluginOptions): GenkitPlugin {\n  return genkitPlugin('vertexAIModelGarden', async (ai: Genkit) => {\n    const { projectId, location, authClient } = await getDerivedParams(options);\n\n    options.models.forEach((m) => {\n      const anthropicEntry = Object.entries(SUPPORTED_ANTHROPIC_MODELS).find(\n        ([_, value]) => value.name === m.name\n      );\n      if (anthropicEntry) {\n        anthropicModel(ai, anthropicEntry[0], projectId, location);\n        return;\n      }\n      const mistralEntry = Object.entries(SUPPORTED_MISTRAL_MODELS).find(\n        ([_, value]) => value.name === m.name\n      );\n      if (mistralEntry) {\n        mistralModel(ai, mistralEntry[0], projectId, location);\n        return;\n      }\n      const openaiModel = Object.entries(SUPPORTED_OPENAI_FORMAT_MODELS).find(\n        ([_, value]) => value.name === m.name\n      );\n      if (openaiModel) {\n        modelGardenOpenaiCompatibleModel(\n          ai,\n          openaiModel[0],\n          projectId,\n          location,\n          authClient,\n          options.openAiBaseUrlTemplate\n        );\n        return;\n      }\n      throw new Error(`Unsupported model garden model: ${m.name}`);\n    });\n  });\n}\n\nexport {\n  claude35Sonnet,\n  claude35SonnetV2,\n  claude3Haiku,\n  claude3Opus,\n  claude3Sonnet,\n} from './anthropic.js';\nexport { codestral, mistralLarge, mistralNemo } from './mistral.js';\nexport { llama3, llama31, llama32 } from './model_garden.js';\nexport type { PluginOptions };\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAiBA,oBAA2C;AAC3C,oBAAiC;AACjC,uBAA2D;AAC3D,qBAAuD;AACvD,0BAGO;AA4CP,IAAAA,oBAMO;AACP,IAAAC,kBAAqD;AACrD,IAAAC,uBAAyC;AA9ClC,SAAS,oBAAoB,SAAsC;AACxE,aAAO,4BAAa,uBAAuB,OAAO,OAAe;AAC/D,UAAM,EAAE,WAAW,UAAU,WAAW,IAAI,UAAM,gCAAiB,OAAO;AAE1E,YAAQ,OAAO,QAAQ,CAAC,MAAM;AAC5B,YAAM,iBAAiB,OAAO,QAAQ,2CAA0B,EAAE;AAAA,QAChE,CAAC,CAAC,GAAG,KAAK,MAAM,MAAM,SAAS,EAAE;AAAA,MACnC;AACA,UAAI,gBAAgB;AAClB,6CAAe,IAAI,eAAe,CAAC,GAAG,WAAW,QAAQ;AACzD;AAAA,MACF;AACA,YAAM,eAAe,OAAO,QAAQ,uCAAwB,EAAE;AAAA,QAC5D,CAAC,CAAC,GAAG,KAAK,MAAM,MAAM,SAAS,EAAE;AAAA,MACnC;AACA,UAAI,cAAc;AAChB,yCAAa,IAAI,aAAa,CAAC,GAAG,WAAW,QAAQ;AACrD;AAAA,MACF;AACA,YAAM,cAAc,OAAO,QAAQ,kDAA8B,EAAE;AAAA,QACjE,CAAC,CAAC,GAAG,KAAK,MAAM,MAAM,SAAS,EAAE;AAAA,MACnC;AACA,UAAI,aAAa;AACf;AAAA,UACE;AAAA,UACA,YAAY,CAAC;AAAA,UACb;AAAA,UACA;AAAA,UACA;AAAA,UACA,QAAQ;AAAA,QACV;AACA;AAAA,MACF;AACA,YAAM,IAAI,MAAM,mCAAmC,EAAE,IAAI,EAAE;AAAA,IAC7D,CAAC;AAAA,EACH,CAAC;AACH;", "names": ["import_anthropic", "import_mistral", "import_model_garden"]}