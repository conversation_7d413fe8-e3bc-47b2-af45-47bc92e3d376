// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.aiplatform.v1;

import "google/api/field_behavior.proto";

option csharp_namespace = "Google.Cloud.AIPlatform.V1";
option go_package = "cloud.google.com/go/aiplatform/apiv1/aiplatformpb;aiplatformpb";
option java_multiple_files = true;
option java_outer_classname = "EncryptionSpecProto";
option java_package = "com.google.cloud.aiplatform.v1";
option php_namespace = "Google\\Cloud\\AIPlatform\\V1";
option ruby_package = "Google::Cloud::AIPlatform::V1";

// Represents a customer-managed encryption key spec that can be applied to
// a top-level resource.
message EncryptionSpec {
  // Required. The Cloud KMS resource identifier of the customer managed
  // encryption key used to protect a resource. Has the form:
  // `projects/my-project/locations/my-region/keyRings/my-kr/cryptoKeys/my-key`.
  // The key needs to be in the same region as where the compute resource is
  // created.
  string kms_key_name = 1 [(google.api.field_behavior) = REQUIRED];
}
