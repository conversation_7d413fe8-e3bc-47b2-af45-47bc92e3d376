# 🐳 دليل Docker لتطبيق Malite Agent

هذا الدليل يوضح كيفية بناء وتشغيل تطبيق Malite Agent باستخدام Docker.

## 📋 المتطلبات

- [Docker Desktop](https://docs.docker.com/get-docker/) مثبت ويعمل
- [Docker Compose](https://docs.docker.com/compose/install/) (مدمج مع Docker Desktop)
- ملف `.env` مُعد بشكل صحيح

## 🚀 البدء السريع

### 1. بناء الحاوية
```bash
# بناء أساسي
npm run docker:build

# أو باستخدام Docker مباشرة
docker build -t malite-agent .
```

### 2. تشغيل الحاوية
```bash
# تشغيل أساسي
npm run docker:run

# تشغيل في الخلفية
npm run docker:run:detached

# أو باستخدام Docker مباشرة
docker run -p 8080:8080 malite-agent
```

### 3. التحقق من التشغيل
```bash
# فحص الصحة
curl http://localhost:8080/health

# اختبار API
curl -X POST http://localhost:8080/api/chat \
  -H "Content-Type: application/json" \
  -d '{"message": "مرحباً"}'
```

## 🛠️ أوامر npm المتاحة

### بناء الحاويات
```bash
npm run docker:build              # بناء للتطوير
npm run docker:build:prod         # بناء للإنتاج
```

### تشغيل الحاويات
```bash
npm run docker:run               # تشغيل تفاعلي
npm run docker:run:detached      # تشغيل في الخلفية
```

### إدارة الحاويات
```bash
npm run docker:stop              # إيقاف وحذف الحاوية
npm run docker:logs              # عرض السجلات
```

### Docker Compose
```bash
npm run docker:compose:up        # تشغيل جميع الخدمات
npm run docker:compose:up:detached  # تشغيل في الخلفية
npm run docker:compose:down      # إيقاف جميع الخدمات
npm run docker:compose:prod      # تشغيل الإنتاج
```

## 🔧 Scripts PowerShell (للويندوز)

### بناء الحاوية
```powershell
# بناء أساسي
.\scripts\docker-build.ps1

# بناء للإنتاج
.\scripts\docker-build.ps1 -Environment prod

# بناء مع اسم مخصص
.\scripts\docker-build.ps1 -ImageName my-malite -Tag v1.0.0

# بناء ورفع إلى Registry
.\scripts\docker-build.ps1 -Registry gcr.io/my-project
```

### تشغيل الحاوية
```powershell
# تشغيل أساسي
.\scripts\docker-run.ps1

# تشغيل في الخلفية
.\scripts\docker-run.ps1 -Detached

# تشغيل على منفذ مختلف
.\scripts\docker-run.ps1 -Port 3000

# تشغيل في بيئة الإنتاج
.\scripts\docker-run.ps1 -Environment production
```

## 🐳 Docker Compose

### للتطوير
```bash
# تشغيل جميع الخدمات (التطبيق + Redis)
docker-compose up

# تشغيل في الخلفية
docker-compose up -d

# إعادة البناء والتشغيل
docker-compose up --build
```

### للإنتاج
```bash
# تشغيل الإنتاج مع Nginx و Redis
docker-compose -f docker-compose.prod.yml up -d

# عرض السجلات
docker-compose -f docker-compose.prod.yml logs -f

# إيقاف الخدمات
docker-compose -f docker-compose.prod.yml down
```

## 📊 مراقبة الحاويات

### عرض الحاويات العاملة
```bash
docker ps
```

### عرض السجلات
```bash
# سجلات الحاوية الرئيسية
docker logs -f malite-app

# سجلات جميع خدمات Compose
docker-compose logs -f
```

### دخول الحاوية
```bash
# دخول الحاوية للتشخيص
docker exec -it malite-app sh

# تشغيل أمر داخل الحاوية
docker exec malite-app npm run test:models
```

### مراقبة الأداء
```bash
# عرض استخدام الموارد
docker stats

# فحص صحة الحاوية
docker exec malite-app ./docker-healthcheck.sh
```

## 🔍 استكشاف الأخطاء

### مشاكل شائعة

#### 1. فشل في بناء الصورة
```bash
# تنظيف وإعادة البناء
docker system prune -a
docker build --no-cache -t malite-agent .
```

#### 2. مشكلة في المنافذ
```bash
# التحقق من المنافذ المستخدمة
netstat -tulpn | grep 8080

# تشغيل على منفذ مختلف
docker run -p 3000:8080 malite-agent
```

#### 3. مشاكل في متغيرات البيئة
```bash
# التحقق من متغيرات البيئة
docker exec malite-app env

# تشغيل مع ملف .env
docker run --env-file .env -p 8080:8080 malite-agent
```

#### 4. مشاكل في الذاكرة
```bash
# زيادة الذاكرة المخصصة
docker run -m 2g -p 8080:8080 malite-agent
```

### أوامر التشخيص
```bash
# فحص العمليات داخل الحاوية
docker exec malite-app ps aux

# فحص مساحة القرص
docker exec malite-app df -h

# فحص الشبكة
docker network ls
docker network inspect malite_malite-network
```

## 🧹 التنظيف

### تنظيف الحاويات
```bash
# إيقاف وحذف جميع الحاويات
docker stop $(docker ps -aq)
docker rm $(docker ps -aq)

# حذف الصور غير المستخدمة
docker image prune -a

# تنظيف شامل
docker system prune -a --volumes
```

### تنظيف Docker Compose
```bash
# إيقاف وحذف جميع الخدمات والأحجام
docker-compose down -v

# تنظيف الشبكات
docker network prune
```

## 📁 هيكل الملفات

```
malite agent/
├── Dockerfile                    # ملف بناء الحاوية
├── docker-compose.yml           # للتطوير
├── docker-compose.prod.yml      # للإنتاج
├── .dockerignore                # ملفات مستبعدة من البناء
├── docker-healthcheck.sh        # script فحص الصحة
├── scripts/
│   ├── docker-build.ps1         # script بناء للويندوز
│   ├── docker-run.ps1           # script تشغيل للويندوز
│   └── docker-commands.md       # دليل الأوامر
└── logs/                        # مجلد السجلات
```

## 🌐 النشر

### Google Container Registry
```bash
# تسمية الصورة
docker tag malite-agent gcr.io/YOUR_PROJECT_ID/malite-agent

# رفع الصورة
docker push gcr.io/YOUR_PROJECT_ID/malite-agent
```

### Docker Hub
```bash
# تسمية الصورة
docker tag malite-agent username/malite-agent

# رفع الصورة
docker push username/malite-agent
```

## 📝 ملاحظات مهمة

1. **متغيرات البيئة**: تأكد من إعداد ملف `.env` قبل التشغيل
2. **المنافذ**: المنفذ الافتراضي هو 8080
3. **الذاكرة**: الحد الأدنى المطلوب 1GB
4. **التخزين**: تأكد من وجود مساحة كافية للسجلات
5. **الأمان**: لا تضع أسرار في Dockerfile أو الصور

## 🔗 روابط مفيدة

- [وثائق Docker](https://docs.docker.com/)
- [وثائق Docker Compose](https://docs.docker.com/compose/)
- [أفضل الممارسات](https://docs.docker.com/develop/dev-best-practices/)
- [دليل النشر الكامل](docs/deployment-guide.md)
