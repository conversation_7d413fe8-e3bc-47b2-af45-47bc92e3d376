{"version": 3, "file": "getMachineId.js", "sourceRoot": "", "sources": ["../../../../../../src/detectors/platform/node/machine-id/getMachineId.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AACH,OAAO,KAAK,OAAO,MAAM,SAAS,CAAC;AAEnC,IAAI,YAAmC,CAAC;AAExC,QAAQ,OAAO,CAAC,QAAQ,EAAE;IACxB,KAAK,QAAQ;QACX,CAAG,YAAY,GAAK,OAAO,CAAC,uBAAuB,CAAC,aAArC,CAAsC,CAAC;QACtD,MAAM;IACR,KAAK,OAAO;QACV,CAAG,YAAY,GAAK,OAAO,CAAC,sBAAsB,CAAC,aAApC,CAAqC,CAAC;QACrD,MAAM;IACR,KAAK,SAAS;QACZ,CAAG,YAAY,GAAK,OAAO,CAAC,oBAAoB,CAAC,aAAlC,CAAmC,CAAC;QACnD,MAAM;IACR,KAAK,OAAO;QACV,CAAG,YAAY,GAAK,OAAO,CAAC,oBAAoB,CAAC,aAAlC,CAAmC,CAAC;QACnD,MAAM;IACR;QACE,CAAG,YAAY,GAAK,OAAO,CAAC,4BAA4B,CAAC,aAA1C,CAA2C,CAAC;CAC9D;AAED,OAAO,EAAE,YAAY,EAAE,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport * as process from 'process';\n\nlet getMachineId: () => Promise<string>;\n\nswitch (process.platform) {\n  case 'darwin':\n    ({ getMachineId } = require('./getMachineId-darwin'));\n    break;\n  case 'linux':\n    ({ getMachineId } = require('./getMachineId-linux'));\n    break;\n  case 'freebsd':\n    ({ getMachineId } = require('./getMachineId-bsd'));\n    break;\n  case 'win32':\n    ({ getMachineId } = require('./getMachineId-win'));\n    break;\n  default:\n    ({ getMachineId } = require('./getMachineId-unsupported'));\n}\n\nexport { getMachineId };\n"]}