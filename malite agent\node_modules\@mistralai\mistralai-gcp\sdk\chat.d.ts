import { EventStream } from "../lib/event-streams.js";
import { <PERSON>lientSDK, RequestOptions } from "../lib/sdks.js";
import * as components from "../models/components/index.js";
export declare class Chat extends ClientSDK {
    /**
     * Stream chat completion
     *
     * @remarks
     * Mistral AI provides the ability to stream responses back to a client in order to allow partial results for certain requests. Tokens will be sent as data-only server-sent events as they become available, with the stream terminated by a data: [DONE] message. Otherwise, the server will hold the request open until the timeout or until completion, with the response containing the full result as JSON.
     */
    stream(request: components.ChatCompletionStreamRequest, options?: RequestOptions): Promise<EventStream<components.CompletionEvent>>;
    /**
     * Chat Completion
     */
    complete(request: components.ChatCompletionRequest, options?: RequestOptions): Promise<components.ChatCompletionResponse>;
}
//# sourceMappingURL=chat.d.ts.map