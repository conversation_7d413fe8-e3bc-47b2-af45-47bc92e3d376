{"version": 3, "file": "ObservableRegistry.js", "sourceRoot": "", "sources": ["../../../src/state/ObservableRegistry.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,4CAM4B;AAC5B,gDAA8E;AAC9E,0DAG6B;AAC7B,oCAKkB;AAkBlB;;;;;GAKG;AACH,MAAa,kBAAkB;IAA/B;QACU,eAAU,GAA+B,EAAE,CAAC;QAC5C,oBAAe,GAAoC,EAAE,CAAC;IAqJhE,CAAC;IAnJC,WAAW,CAAC,QAA4B,EAAE,UAAgC;QACxE,MAAM,GAAG,GAAG,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;QACrD,IAAI,GAAG,IAAI,CAAC,EAAE;YACZ,OAAO;SACR;QACD,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,QAAQ,EAAE,UAAU,EAAE,CAAC,CAAC;IACjD,CAAC;IAED,cAAc,CACZ,QAA4B,EAC5B,UAAgC;QAEhC,MAAM,GAAG,GAAG,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;QACrD,IAAI,GAAG,GAAG,CAAC,EAAE;YACX,OAAO;SACR;QACD,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;IACjC,CAAC;IAED,gBAAgB,CACd,QAAiC,EACjC,WAAyB;QAEzB,sCAAsC;QACtC,MAAM,qBAAqB,GAAG,IAAI,GAAG,CACnC,WAAW,CAAC,MAAM,CAAC,oCAAsB,CAAC,CAC3C,CAAC;QACF,IAAI,qBAAqB,CAAC,IAAI,KAAK,CAAC,EAAE;YACpC,UAAI,CAAC,KAAK,CACR,kEAAkE,EAClE,WAAW,CACZ,CAAC;YACF,OAAO;SACR;QACD,MAAM,GAAG,GAAG,IAAI,CAAC,kBAAkB,CAAC,QAAQ,EAAE,qBAAqB,CAAC,CAAC;QACrE,IAAI,GAAG,IAAI,CAAC,EAAE;YACZ,OAAO;SACR;QACD,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,EAAE,QAAQ,EAAE,WAAW,EAAE,qBAAqB,EAAE,CAAC,CAAC;IAC9E,CAAC;IAED,mBAAmB,CACjB,QAAiC,EACjC,WAAyB;QAEzB,sCAAsC;QACtC,MAAM,qBAAqB,GAAG,IAAI,GAAG,CACnC,WAAW,CAAC,MAAM,CAAC,oCAAsB,CAAC,CAC3C,CAAC;QACF,MAAM,GAAG,GAAG,IAAI,CAAC,kBAAkB,CAAC,QAAQ,EAAE,qBAAqB,CAAC,CAAC;QACrE,IAAI,GAAG,GAAG,CAAC,EAAE;YACX,OAAO;SACR;QACD,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;IACtC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,OAAO,CACX,cAAsB,EACtB,aAAsB;QAEtB,MAAM,eAAe,GAAG,IAAI,CAAC,iBAAiB,CAC5C,cAAc,EACd,aAAa,CACd,CAAC;QACF,MAAM,oBAAoB,GAAG,IAAI,CAAC,sBAAsB,CACtD,cAAc,EACd,aAAa,CACd,CAAC;QAEF,MAAM,OAAO,GAAG,MAAM,IAAA,yBAAiB,EAAC;YACtC,GAAG,eAAe;YAClB,GAAG,oBAAoB;SACxB,CAAC,CAAC;QAEH,MAAM,UAAU,GAAG,OAAO;aACvB,MAAM,CAAC,0CAAkC,CAAC;aAC1C,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;QACxB,OAAO,UAAU,CAAC;IACpB,CAAC;IAEO,iBAAiB,CAAC,eAAuB,EAAE,aAAsB;QACvE,OAAO,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,KAAK,EAAE,EAAE,QAAQ,EAAE,UAAU,EAAE,EAAE,EAAE;YAC5D,MAAM,gBAAgB,GAAG,IAAI,uCAAoB,CAC/C,UAAU,CAAC,WAAW,CAAC,IAAI,EAC3B,UAAU,CAAC,WAAW,CAAC,SAAS,CACjC,CAAC;YACF,IAAI,WAAW,GAAkB,OAAO,CAAC,OAAO,CAC9C,QAAQ,CAAC,gBAAgB,CAAC,CAC3B,CAAC;YACF,IAAI,aAAa,IAAI,IAAI,EAAE;gBACzB,WAAW,GAAG,IAAA,uBAAe,EAAC,WAAW,EAAE,aAAa,CAAC,CAAC;aAC3D;YACD,MAAM,WAAW,CAAC;YAClB,UAAU,CAAC,eAAe,CAAC,OAAO,CAAC,aAAa,CAAC,EAAE;gBACjD,aAAa,CAAC,MAAM,CAAC,gBAAgB,CAAC,OAAO,EAAE,eAAe,CAAC,CAAC;YAClE,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,sBAAsB,CAC5B,eAAuB,EACvB,aAAsB;QAEtB,OAAO,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,KAAK,EAAE,EAAE,QAAQ,EAAE,WAAW,EAAE,EAAE,EAAE;YAClE,MAAM,gBAAgB,GAAG,IAAI,4CAAyB,EAAE,CAAC;YACzD,IAAI,WAAW,GAAkB,OAAO,CAAC,OAAO,CAC9C,QAAQ,CAAC,gBAAgB,CAAC,CAC3B,CAAC;YACF,IAAI,aAAa,IAAI,IAAI,EAAE;gBACzB,WAAW,GAAG,IAAA,uBAAe,EAAC,WAAW,EAAE,aAAa,CAAC,CAAC;aAC3D;YACD,MAAM,WAAW,CAAC;YAClB,WAAW,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE;gBAC/B,MAAM,MAAM,GAAG,gBAAgB,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;gBACxD,IAAI,MAAM,IAAI,IAAI,EAAE;oBAClB,OAAO;iBACR;gBACD,UAAU,CAAC,eAAe,CAAC,OAAO,CAAC,aAAa,CAAC,EAAE;oBACjD,aAAa,CAAC,MAAM,CAAC,MAAM,EAAE,eAAe,CAAC,CAAC;gBAChD,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,aAAa,CACnB,QAA4B,EAC5B,UAAgC;QAEhC,OAAO,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,MAAM,CAAC,EAAE;YACxC,OAAO,MAAM,CAAC,QAAQ,KAAK,QAAQ,IAAI,MAAM,CAAC,UAAU,KAAK,UAAU,CAAC;QAC1E,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,kBAAkB,CACxB,QAAiC,EACjC,WAAsC;QAEtC,OAAO,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,MAAM,CAAC,EAAE;YAC7C,OAAO,CACL,MAAM,CAAC,QAAQ,KAAK,QAAQ;gBAC5B,IAAA,iBAAS,EAAC,MAAM,CAAC,WAAW,EAAE,WAAW,CAAC,CAC3C,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC;CACF;AAvJD,gDAuJC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  diag,\n  HrTime,\n  BatchObservableCallback,\n  Observable,\n  ObservableCallback,\n} from '@opentelemetry/api';\nimport { isObservableInstrument, ObservableInstrument } from '../Instruments';\nimport {\n  BatchObservableResultImpl,\n  ObservableResultImpl,\n} from '../ObservableResult';\nimport {\n  callWithTimeout,\n  PromiseAllSettled,\n  isPromiseAllSettledRejectionResult,\n  setEquals,\n} from '../utils';\n\n/**\n * Records for single instrument observable callback.\n */\ninterface ObservableCallbackRecord {\n  callback: ObservableCallback;\n  instrument: ObservableInstrument;\n}\n\n/**\n * Records for multiple instruments observable callback.\n */\ninterface BatchObservableCallbackRecord {\n  callback: BatchObservableCallback;\n  instruments: Set<ObservableInstrument>;\n}\n\n/**\n * An internal interface for managing ObservableCallbacks.\n *\n * Every registered callback associated with a set of instruments are be evaluated\n * exactly once during collection prior to reading data for that instrument.\n */\nexport class ObservableRegistry {\n  private _callbacks: ObservableCallbackRecord[] = [];\n  private _batchCallbacks: BatchObservableCallbackRecord[] = [];\n\n  addCallback(callback: ObservableCallback, instrument: ObservableInstrument) {\n    const idx = this._findCallback(callback, instrument);\n    if (idx >= 0) {\n      return;\n    }\n    this._callbacks.push({ callback, instrument });\n  }\n\n  removeCallback(\n    callback: ObservableCallback,\n    instrument: ObservableInstrument\n  ) {\n    const idx = this._findCallback(callback, instrument);\n    if (idx < 0) {\n      return;\n    }\n    this._callbacks.splice(idx, 1);\n  }\n\n  addBatchCallback(\n    callback: BatchObservableCallback,\n    instruments: Observable[]\n  ) {\n    // Create a set of unique instruments.\n    const observableInstruments = new Set(\n      instruments.filter(isObservableInstrument)\n    );\n    if (observableInstruments.size === 0) {\n      diag.error(\n        'BatchObservableCallback is not associated with valid instruments',\n        instruments\n      );\n      return;\n    }\n    const idx = this._findBatchCallback(callback, observableInstruments);\n    if (idx >= 0) {\n      return;\n    }\n    this._batchCallbacks.push({ callback, instruments: observableInstruments });\n  }\n\n  removeBatchCallback(\n    callback: BatchObservableCallback,\n    instruments: Observable[]\n  ) {\n    // Create a set of unique instruments.\n    const observableInstruments = new Set(\n      instruments.filter(isObservableInstrument)\n    );\n    const idx = this._findBatchCallback(callback, observableInstruments);\n    if (idx < 0) {\n      return;\n    }\n    this._batchCallbacks.splice(idx, 1);\n  }\n\n  /**\n   * @returns a promise of rejected reasons for invoking callbacks.\n   */\n  async observe(\n    collectionTime: HrTime,\n    timeoutMillis?: number\n  ): Promise<unknown[]> {\n    const callbackFutures = this._observeCallbacks(\n      collectionTime,\n      timeoutMillis\n    );\n    const batchCallbackFutures = this._observeBatchCallbacks(\n      collectionTime,\n      timeoutMillis\n    );\n\n    const results = await PromiseAllSettled([\n      ...callbackFutures,\n      ...batchCallbackFutures,\n    ]);\n\n    const rejections = results\n      .filter(isPromiseAllSettledRejectionResult)\n      .map(it => it.reason);\n    return rejections;\n  }\n\n  private _observeCallbacks(observationTime: HrTime, timeoutMillis?: number) {\n    return this._callbacks.map(async ({ callback, instrument }) => {\n      const observableResult = new ObservableResultImpl(\n        instrument._descriptor.name,\n        instrument._descriptor.valueType\n      );\n      let callPromise: Promise<void> = Promise.resolve(\n        callback(observableResult)\n      );\n      if (timeoutMillis != null) {\n        callPromise = callWithTimeout(callPromise, timeoutMillis);\n      }\n      await callPromise;\n      instrument._metricStorages.forEach(metricStorage => {\n        metricStorage.record(observableResult._buffer, observationTime);\n      });\n    });\n  }\n\n  private _observeBatchCallbacks(\n    observationTime: HrTime,\n    timeoutMillis?: number\n  ) {\n    return this._batchCallbacks.map(async ({ callback, instruments }) => {\n      const observableResult = new BatchObservableResultImpl();\n      let callPromise: Promise<void> = Promise.resolve(\n        callback(observableResult)\n      );\n      if (timeoutMillis != null) {\n        callPromise = callWithTimeout(callPromise, timeoutMillis);\n      }\n      await callPromise;\n      instruments.forEach(instrument => {\n        const buffer = observableResult._buffer.get(instrument);\n        if (buffer == null) {\n          return;\n        }\n        instrument._metricStorages.forEach(metricStorage => {\n          metricStorage.record(buffer, observationTime);\n        });\n      });\n    });\n  }\n\n  private _findCallback(\n    callback: ObservableCallback,\n    instrument: ObservableInstrument\n  ) {\n    return this._callbacks.findIndex(record => {\n      return record.callback === callback && record.instrument === instrument;\n    });\n  }\n\n  private _findBatchCallback(\n    callback: BatchObservableCallback,\n    instruments: Set<ObservableInstrument>\n  ) {\n    return this._batchCallbacks.findIndex(record => {\n      return (\n        record.callback === callback &&\n        setEquals(record.instruments, instruments)\n      );\n    });\n  }\n}\n"]}