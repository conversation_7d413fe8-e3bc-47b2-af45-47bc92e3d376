import { Attributes } from '@opentelemetry/api';
export interface LoggerOptions {
    /**
     * The schemaUrl of the tracer or instrumentation library
     * @default ''
     */
    schemaUrl?: string;
    /**
     * The instrumentation scope attributes to associate with emitted telemetry
     */
    scopeAttributes?: Attributes;
    /**
     * Specifies whether the Trace Context should automatically be passed on to the LogRecords emitted by the Logger.
     * @default true
     */
    includeTraceContext?: boolean;
}
//# sourceMappingURL=LoggerOptions.d.ts.map