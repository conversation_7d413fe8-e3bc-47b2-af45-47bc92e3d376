{"version": 3, "file": "NoopSpanProcessor.js", "sourceRoot": "", "sources": ["../../../src/export/NoopSpanProcessor.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAOH,4CAA4C;AAC5C,MAAM,OAAO,iBAAiB;IAC5B,OAAO,CAAC,KAAW,EAAE,QAAiB,IAAS,CAAC;IAChD,KAAK,CAAC,KAAmB,IAAS,CAAC;IACnC,QAAQ;QACN,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;IAC3B,CAAC;IACD,UAAU;QACR,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;IAC3B,CAAC;CACF", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Context } from '@opentelemetry/api';\nimport { ReadableSpan } from './ReadableSpan';\nimport { Span } from '../Span';\nimport { SpanProcessor } from '../SpanProcessor';\n\n/** No-op implementation of SpanProcessor */\nexport class NoopSpanProcessor implements SpanProcessor {\n  onStart(_span: Span, _context: Context): void {}\n  onEnd(_span: ReadableSpan): void {}\n  shutdown(): Promise<void> {\n    return Promise.resolve();\n  }\n  forceFlush(): Promise<void> {\n    return Promise.resolve();\n  }\n}\n"]}