{"version": 3, "file": "MetricStorageRegistry.js", "sourceRoot": "", "sources": ["../../../src/state/MetricStorageRegistry.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAGH,OAAO,EAEL,0BAA0B,GAC3B,MAAM,yBAAyB,CAAC;AACjC,OAAO,KAAK,GAAG,MAAM,oBAAoB,CAAC;AAC1C,OAAO,EACL,2BAA2B,EAC3B,yBAAyB,GAC1B,MAAM,+BAA+B,CAAC;AAKvC;;GAEG;AACH,MAAM,OAAO,qBAAqB;IAAlC;QACmB,oBAAe,GAAe,IAAI,GAAG,EAAE,CAAC;QACxC,0BAAqB,GAAG,IAAI,GAAG,EAG7C,CAAC;IAkIN,CAAC;IAhIC,MAAM,CAAC,MAAM;QACX,OAAO,IAAI,qBAAqB,EAAE,CAAC;IACrC,CAAC;IAED,WAAW,CAAC,SAAgC;QAC1C,IAAI,QAAQ,GAAoB,EAAE,CAAC;QACnC,KAAK,MAAM,cAAc,IAAI,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,EAAE;YAC1D,QAAQ,GAAG,QAAQ,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;SAC5C;QAED,MAAM,oBAAoB,GAAG,IAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QACvE,IAAI,oBAAoB,IAAI,IAAI,EAAE;YAChC,KAAK,MAAM,cAAc,IAAI,oBAAoB,CAAC,MAAM,EAAE,EAAE;gBAC1D,QAAQ,GAAG,QAAQ,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;aAC5C;SACF;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED,QAAQ,CAAC,OAAsB;QAC7B,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;IACvD,CAAC;IAED,oBAAoB,CAClB,SAAgC,EAChC,OAAsB;QAEtB,IAAI,UAAU,GAAG,IAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QAC3D,IAAI,UAAU,IAAI,IAAI,EAAE;YACtB,UAAU,GAAG,IAAI,GAAG,EAAE,CAAC;YACvB,IAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC;SACvD;QACD,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;IAC7C,CAAC;IAED,6BAA6B,CAC3B,kBAAwC;QAExC,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;QACnE,IAAI,QAAQ,KAAK,SAAS,EAAE;YAC1B,OAAO,IAAI,CAAC;SACb;QAED,oEAAoE;QACpE,uEAAuE;QACvE,OAAO,IAAI,CAAC,8BAA8B,CAAI,kBAAkB,EAAE,QAAQ,CAAC,CAAC;IAC9E,CAAC;IAED,sCAAsC,CACpC,SAAgC,EAChC,kBAAwC;QAExC,MAAM,UAAU,GAAG,IAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QAC7D,IAAI,UAAU,KAAK,SAAS,EAAE;YAC5B,OAAO,IAAI,CAAC;SACb;QAED,MAAM,QAAQ,GAAG,UAAU,CAAC,GAAG,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;QACzD,IAAI,QAAQ,KAAK,SAAS,EAAE;YAC1B,OAAO,IAAI,CAAC;SACb;QAED,oEAAoE;QACpE,uEAAuE;QACvE,OAAO,IAAI,CAAC,8BAA8B,CAAI,kBAAkB,EAAE,QAAQ,CAAC,CAAC;IAC9E,CAAC;IAEO,gBAAgB,CAAC,OAAsB,EAAE,UAAsB;QACrE,MAAM,UAAU,GAAG,OAAO,CAAC,uBAAuB,EAAE,CAAC;QACrD,MAAM,QAAQ,GAAG,UAAU,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;QAEjD,IAAI,QAAQ,KAAK,SAAS,EAAE;YAC1B,UAAU,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC;YAC3C,OAAO;SACR;QAED,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IACzB,CAAC;IAEO,8BAA8B,CACpC,kBAAwC,EACxC,gBAAiC;QAEjC,IAAI,iBAAiB,GAAG,IAAI,CAAC;QAE7B,KAAK,MAAM,eAAe,IAAI,gBAAgB,EAAE;YAC9C,MAAM,kBAAkB,GAAG,eAAe,CAAC,uBAAuB,EAAE,CAAC;YAErE,IAAI,0BAA0B,CAAC,kBAAkB,EAAE,kBAAkB,CAAC,EAAE;gBACtE,mDAAmD;gBACnD,IAAI,kBAAkB,CAAC,WAAW,KAAK,kBAAkB,CAAC,WAAW,EAAE;oBACrE,IACE,kBAAkB,CAAC,WAAW,CAAC,MAAM;wBACrC,kBAAkB,CAAC,WAAW,CAAC,MAAM,EACrC;wBACA,eAAe,CAAC,iBAAiB,CAAC,kBAAkB,CAAC,WAAW,CAAC,CAAC;qBACnE;oBAED,GAAG,CAAC,IAAI,CAAC,IAAI,CACX,qCAAqC,EACrC,kBAAkB,CAAC,IAAI,EACvB,mHAAmH,EACnH,YAAY,EACZ,yBAAyB,CAAC,kBAAkB,EAAE,kBAAkB,CAAC,EACjE,gEAAgE,EAChE,2BAA2B,CAAC,kBAAkB,EAAE,kBAAkB,CAAC,CACpE,CAAC;iBACH;gBACD,wGAAwG;gBACxG,iBAAiB,GAAG,eAAoB,CAAC;aAC1C;iBAAM;gBACL,yEAAyE;gBACzE,+CAA+C;gBAC/C,GAAG,CAAC,IAAI,CAAC,IAAI,CACX,qCAAqC,EACrC,kBAAkB,CAAC,IAAI,EACvB,kFAAkF,EAClF,YAAY,EACZ,yBAAyB,CAAC,kBAAkB,EAAE,kBAAkB,CAAC,EACjE,4BAA4B,EAC5B,2BAA2B,CAAC,kBAAkB,EAAE,kBAAkB,CAAC,CACpE,CAAC;aACH;SACF;QAED,OAAO,iBAAiB,CAAC;IAC3B,CAAC;CACF", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { MetricStorage } from './MetricStorage';\nimport {\n  InstrumentDescriptor,\n  isDescriptorCompatibleWith,\n} from '../InstrumentDescriptor';\nimport * as api from '@opentelemetry/api';\nimport {\n  getConflictResolutionRecipe,\n  getIncompatibilityDetails,\n} from '../view/RegistrationConflicts';\nimport { MetricCollectorHandle } from './MetricCollector';\n\ntype StorageMap = Map<string, MetricStorage[]>;\n\n/**\n * Internal class for storing {@link MetricStorage}\n */\nexport class MetricStorageRegistry {\n  private readonly _sharedRegistry: StorageMap = new Map();\n  private readonly _perCollectorRegistry = new Map<\n    MetricCollectorHandle,\n    StorageMap\n  >();\n\n  static create() {\n    return new MetricStorageRegistry();\n  }\n\n  getStorages(collector: MetricCollectorHandle): MetricStorage[] {\n    let storages: MetricStorage[] = [];\n    for (const metricStorages of this._sharedRegistry.values()) {\n      storages = storages.concat(metricStorages);\n    }\n\n    const perCollectorStorages = this._perCollectorRegistry.get(collector);\n    if (perCollectorStorages != null) {\n      for (const metricStorages of perCollectorStorages.values()) {\n        storages = storages.concat(metricStorages);\n      }\n    }\n\n    return storages;\n  }\n\n  register(storage: MetricStorage) {\n    this._registerStorage(storage, this._sharedRegistry);\n  }\n\n  registerForCollector(\n    collector: MetricCollectorHandle,\n    storage: MetricStorage\n  ) {\n    let storageMap = this._perCollectorRegistry.get(collector);\n    if (storageMap == null) {\n      storageMap = new Map();\n      this._perCollectorRegistry.set(collector, storageMap);\n    }\n    this._registerStorage(storage, storageMap);\n  }\n\n  findOrUpdateCompatibleStorage<T extends MetricStorage>(\n    expectedDescriptor: InstrumentDescriptor\n  ): T | null {\n    const storages = this._sharedRegistry.get(expectedDescriptor.name);\n    if (storages === undefined) {\n      return null;\n    }\n\n    // If the descriptor is compatible, the type of their metric storage\n    // (either SyncMetricStorage or AsyncMetricStorage) must be compatible.\n    return this._findOrUpdateCompatibleStorage<T>(expectedDescriptor, storages);\n  }\n\n  findOrUpdateCompatibleCollectorStorage<T extends MetricStorage>(\n    collector: MetricCollectorHandle,\n    expectedDescriptor: InstrumentDescriptor\n  ): T | null {\n    const storageMap = this._perCollectorRegistry.get(collector);\n    if (storageMap === undefined) {\n      return null;\n    }\n\n    const storages = storageMap.get(expectedDescriptor.name);\n    if (storages === undefined) {\n      return null;\n    }\n\n    // If the descriptor is compatible, the type of their metric storage\n    // (either SyncMetricStorage or AsyncMetricStorage) must be compatible.\n    return this._findOrUpdateCompatibleStorage<T>(expectedDescriptor, storages);\n  }\n\n  private _registerStorage(storage: MetricStorage, storageMap: StorageMap) {\n    const descriptor = storage.getInstrumentDescriptor();\n    const storages = storageMap.get(descriptor.name);\n\n    if (storages === undefined) {\n      storageMap.set(descriptor.name, [storage]);\n      return;\n    }\n\n    storages.push(storage);\n  }\n\n  private _findOrUpdateCompatibleStorage<T extends MetricStorage>(\n    expectedDescriptor: InstrumentDescriptor,\n    existingStorages: MetricStorage[]\n  ): T | null {\n    let compatibleStorage = null;\n\n    for (const existingStorage of existingStorages) {\n      const existingDescriptor = existingStorage.getInstrumentDescriptor();\n\n      if (isDescriptorCompatibleWith(existingDescriptor, expectedDescriptor)) {\n        // Use the longer description if it does not match.\n        if (existingDescriptor.description !== expectedDescriptor.description) {\n          if (\n            expectedDescriptor.description.length >\n            existingDescriptor.description.length\n          ) {\n            existingStorage.updateDescription(expectedDescriptor.description);\n          }\n\n          api.diag.warn(\n            'A view or instrument with the name ',\n            expectedDescriptor.name,\n            ' has already been registered, but has a different description and is incompatible with another registered view.\\n',\n            'Details:\\n',\n            getIncompatibilityDetails(existingDescriptor, expectedDescriptor),\n            'The longer description will be used.\\nTo resolve the conflict:',\n            getConflictResolutionRecipe(existingDescriptor, expectedDescriptor)\n          );\n        }\n        // Storage is fully compatible. There will never be more than one pre-existing fully compatible storage.\n        compatibleStorage = existingStorage as T;\n      } else {\n        // The implementation SHOULD warn about duplicate instrument registration\n        // conflicts after applying View configuration.\n        api.diag.warn(\n          'A view or instrument with the name ',\n          expectedDescriptor.name,\n          ' has already been registered and is incompatible with another registered view.\\n',\n          'Details:\\n',\n          getIncompatibilityDetails(existingDescriptor, expectedDescriptor),\n          'To resolve the conflict:\\n',\n          getConflictResolutionRecipe(existingDescriptor, expectedDescriptor)\n        );\n      }\n    }\n\n    return compatibleStorage;\n  }\n}\n"]}