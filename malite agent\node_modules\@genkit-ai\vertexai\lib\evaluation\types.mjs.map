{"version": 3, "sources": ["../../src/evaluation/types.ts"], "sourcesContent": ["/**\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { CommonPluginOptions } from '../common/types.js';\n\nexport enum VertexAIEvaluationMetricType {\n  // Update genkit/docs/plugins/vertex-ai.md when modifying the list of enums\n  BLEU = 'BLEU',\n  ROUGE = 'ROUGE',\n  FLUENCY = 'FLEUNCY',\n  SAFETY = 'SAFETY',\n  GROUNDEDNESS = 'GROUNDEDNESS',\n  SUMMARIZATION_QUALITY = 'SUMMARIZATION_QUALITY',\n  SUMMARIZATION_HELPFULNESS = 'SUMMARIZATION_HELPFULNESS',\n  SUMMARIZATION_VERBOSITY = 'SUMMARIZATION_VERBOSITY',\n}\n\n/**\n * Evaluation metric config. Use `metricSpec` to define the behavior of the metric.\n * The value of `metricSpec` will be included in the request to the API. See the API documentation\n * for details on the possible values of `metricSpec` for each metric.\n * https://cloud.google.com/vertex-ai/generative-ai/docs/model-reference/evaluation#parameter-list\n */\nexport type VertexAIEvaluationMetricConfig = {\n  type: VertexAIEvaluationMetricType;\n  metricSpec: any;\n};\n\nexport type VertexAIEvaluationMetric =\n  | VertexAIEvaluationMetricType\n  | VertexAIEvaluationMetricConfig;\n\n/** Options specific to evaluation configuration */\nexport interface EvaluationOptions {\n  metrics: VertexAIEvaluationMetric[];\n}\n\nexport interface PluginOptions extends CommonPluginOptions, EvaluationOptions {}\n"], "mappings": "AAkBO,IAAK,+BA<PERSON>,kBAAKA,kCAAL;AAEL,EAAAA,8BAAA,UAAO;AACP,EAAAA,8BAAA,WAAQ;AACR,EAAAA,8BAAA,aAAU;AACV,EAAAA,8BAAA,YAAS;AACT,EAAAA,8BAAA,kBAAe;AACf,EAAAA,8BAAA,2BAAwB;AACxB,EAAAA,8BAAA,+BAA4B;AAC5B,EAAAA,8BAAA,6BAA0B;AAThB,SAAAA;AAAA,GAAA;", "names": ["VertexAIEvaluationMetricType"]}