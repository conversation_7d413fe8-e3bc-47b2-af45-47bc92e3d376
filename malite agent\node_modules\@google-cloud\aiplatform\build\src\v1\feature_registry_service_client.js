"use strict";
// Copyright 2025 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     https://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//
// ** This file is automatically generated by gapic-generator-typescript. **
// ** https://github.com/googleapis/gapic-generator-typescript **
// ** All changes to this file may be overwritten. **
Object.defineProperty(exports, "__esModule", { value: true });
exports.FeatureRegistryServiceClient = void 0;
const jsonProtos = require("../../protos/protos.json");
/**
 * Client JSON configuration object, loaded from
 * `src/v1/feature_registry_service_client_config.json`.
 * This file defines retry strategy and timeouts for all API methods in this library.
 */
const gapicConfig = require("./feature_registry_service_client_config.json");
const version = require('../../../package.json').version;
/**
 *  The service that handles CRUD and List for resources for
 *  FeatureRegistry.
 * @class
 * @memberof v1
 */
class FeatureRegistryServiceClient {
    /**
     * Construct an instance of FeatureRegistryServiceClient.
     *
     * @param {object} [options] - The configuration object.
     * The options accepted by the constructor are described in detail
     * in [this document](https://github.com/googleapis/gax-nodejs/blob/main/client-libraries.md#creating-the-client-instance).
     * The common options are:
     * @param {object} [options.credentials] - Credentials object.
     * @param {string} [options.credentials.client_email]
     * @param {string} [options.credentials.private_key]
     * @param {string} [options.email] - Account email address. Required when
     *     using a .pem or .p12 keyFilename.
     * @param {string} [options.keyFilename] - Full path to the a .json, .pem, or
     *     .p12 key downloaded from the Google Developers Console. If you provide
     *     a path to a JSON file, the projectId option below is not necessary.
     *     NOTE: .pem and .p12 require you to specify options.email as well.
     * @param {number} [options.port] - The port on which to connect to
     *     the remote host.
     * @param {string} [options.projectId] - The project ID from the Google
     *     Developer's Console, e.g. 'grape-spaceship-123'. We will also check
     *     the environment variable GCLOUD_PROJECT for your project ID. If your
     *     app is running in an environment which supports
     *     {@link https://developers.google.com/identity/protocols/application-default-credentials Application Default Credentials},
     *     your project ID will be detected automatically.
     * @param {string} [options.apiEndpoint] - The domain name of the
     *     API remote host.
     * @param {gax.ClientConfig} [options.clientConfig] - Client configuration override.
     *     Follows the structure of {@link gapicConfig}.
     * @param {boolean} [options.fallback] - Use HTTP/1.1 REST mode.
     *     For more information, please check the
     *     {@link https://github.com/googleapis/gax-nodejs/blob/main/client-libraries.md#http11-rest-api-mode documentation}.
     * @param {gax} [gaxInstance]: loaded instance of `google-gax`. Useful if you
     *     need to avoid loading the default gRPC version and want to use the fallback
     *     HTTP implementation. Load only fallback version and pass it to the constructor:
     *     ```
     *     const gax = require('google-gax/build/src/fallback'); // avoids loading google-gax with gRPC
     *     const client = new FeatureRegistryServiceClient({fallback: true}, gax);
     *     ```
     */
    constructor(opts, gaxInstance) {
        var _a, _b, _c, _d, _e;
        this._terminated = false;
        this.descriptors = {
            page: {},
            stream: {},
            longrunning: {},
            batching: {},
        };
        // Ensure that options include all the required fields.
        const staticMembers = this
            .constructor;
        if ((opts === null || opts === void 0 ? void 0 : opts.universe_domain) &&
            (opts === null || opts === void 0 ? void 0 : opts.universeDomain) &&
            (opts === null || opts === void 0 ? void 0 : opts.universe_domain) !== (opts === null || opts === void 0 ? void 0 : opts.universeDomain)) {
            throw new Error('Please set either universe_domain or universeDomain, but not both.');
        }
        const universeDomainEnvVar = typeof process === 'object' && typeof process.env === 'object'
            ? process.env['GOOGLE_CLOUD_UNIVERSE_DOMAIN']
            : undefined;
        this._universeDomain =
            (_c = (_b = (_a = opts === null || opts === void 0 ? void 0 : opts.universeDomain) !== null && _a !== void 0 ? _a : opts === null || opts === void 0 ? void 0 : opts.universe_domain) !== null && _b !== void 0 ? _b : universeDomainEnvVar) !== null && _c !== void 0 ? _c : 'googleapis.com';
        this._servicePath = 'aiplatform.' + this._universeDomain;
        const servicePath = (opts === null || opts === void 0 ? void 0 : opts.servicePath) || (opts === null || opts === void 0 ? void 0 : opts.apiEndpoint) || this._servicePath;
        this._providedCustomServicePath = !!((opts === null || opts === void 0 ? void 0 : opts.servicePath) || (opts === null || opts === void 0 ? void 0 : opts.apiEndpoint));
        const port = (opts === null || opts === void 0 ? void 0 : opts.port) || staticMembers.port;
        const clientConfig = (_d = opts === null || opts === void 0 ? void 0 : opts.clientConfig) !== null && _d !== void 0 ? _d : {};
        const fallback = (_e = opts === null || opts === void 0 ? void 0 : opts.fallback) !== null && _e !== void 0 ? _e : (typeof window !== 'undefined' && typeof (window === null || window === void 0 ? void 0 : window.fetch) === 'function');
        opts = Object.assign({ servicePath, port, clientConfig, fallback }, opts);
        // Request numeric enum values if REST transport is used.
        opts.numericEnums = true;
        // If scopes are unset in options and we're connecting to a non-default endpoint, set scopes just in case.
        if (servicePath !== this._servicePath && !('scopes' in opts)) {
            opts['scopes'] = staticMembers.scopes;
        }
        // Load google-gax module synchronously if needed
        if (!gaxInstance) {
            gaxInstance = require('google-gax');
        }
        // Choose either gRPC or proto-over-HTTP implementation of google-gax.
        this._gaxModule = opts.fallback ? gaxInstance.fallback : gaxInstance;
        // Create a `gaxGrpc` object, with any grpc-specific options sent to the client.
        this._gaxGrpc = new this._gaxModule.GrpcClient(opts);
        // Save options to use in initialize() method.
        this._opts = opts;
        // Save the auth object to the client, for use by other methods.
        this.auth = this._gaxGrpc.auth;
        // Set useJWTAccessWithScope on the auth object.
        this.auth.useJWTAccessWithScope = true;
        // Set defaultServicePath on the auth object.
        this.auth.defaultServicePath = this._servicePath;
        // Set the default scopes in auth client if needed.
        if (servicePath === this._servicePath) {
            this.auth.defaultScopes = staticMembers.scopes;
        }
        this.iamClient = new this._gaxModule.IamClient(this._gaxGrpc, opts);
        this.locationsClient = new this._gaxModule.LocationsClient(this._gaxGrpc, opts);
        // Determine the client header string.
        const clientHeader = [`gax/${this._gaxModule.version}`, `gapic/${version}`];
        if (typeof process === 'object' && 'versions' in process) {
            clientHeader.push(`gl-node/${process.versions.node}`);
        }
        else {
            clientHeader.push(`gl-web/${this._gaxModule.version}`);
        }
        if (!opts.fallback) {
            clientHeader.push(`grpc/${this._gaxGrpc.grpcVersion}`);
        }
        else {
            clientHeader.push(`rest/${this._gaxGrpc.grpcVersion}`);
        }
        if (opts.libName && opts.libVersion) {
            clientHeader.push(`${opts.libName}/${opts.libVersion}`);
        }
        // Load the applicable protos.
        this._protos = this._gaxGrpc.loadProtoJSON(jsonProtos);
        // This API contains "path templates"; forward-slash-separated
        // identifiers to uniquely identify resources within the API.
        // Create useful helper objects for these.
        this.pathTemplates = {
            annotationPathTemplate: new this._gaxModule.PathTemplate('projects/{project}/locations/{location}/datasets/{dataset}/dataItems/{data_item}/annotations/{annotation}'),
            annotationSpecPathTemplate: new this._gaxModule.PathTemplate('projects/{project}/locations/{location}/datasets/{dataset}/annotationSpecs/{annotation_spec}'),
            artifactPathTemplate: new this._gaxModule.PathTemplate('projects/{project}/locations/{location}/metadataStores/{metadata_store}/artifacts/{artifact}'),
            batchPredictionJobPathTemplate: new this._gaxModule.PathTemplate('projects/{project}/locations/{location}/batchPredictionJobs/{batch_prediction_job}'),
            cachedContentPathTemplate: new this._gaxModule.PathTemplate('projects/{project}/locations/{location}/cachedContents/{cached_content}'),
            contextPathTemplate: new this._gaxModule.PathTemplate('projects/{project}/locations/{location}/metadataStores/{metadata_store}/contexts/{context}'),
            customJobPathTemplate: new this._gaxModule.PathTemplate('projects/{project}/locations/{location}/customJobs/{custom_job}'),
            dataItemPathTemplate: new this._gaxModule.PathTemplate('projects/{project}/locations/{location}/datasets/{dataset}/dataItems/{data_item}'),
            dataLabelingJobPathTemplate: new this._gaxModule.PathTemplate('projects/{project}/locations/{location}/dataLabelingJobs/{data_labeling_job}'),
            datasetPathTemplate: new this._gaxModule.PathTemplate('projects/{project}/locations/{location}/datasets/{dataset}'),
            datasetVersionPathTemplate: new this._gaxModule.PathTemplate('projects/{project}/locations/{location}/datasets/{dataset}/datasetVersions/{dataset_version}'),
            deploymentResourcePoolPathTemplate: new this._gaxModule.PathTemplate('projects/{project}/locations/{location}/deploymentResourcePools/{deployment_resource_pool}'),
            entityTypePathTemplate: new this._gaxModule.PathTemplate('projects/{project}/locations/{location}/featurestores/{featurestore}/entityTypes/{entity_type}'),
            executionPathTemplate: new this._gaxModule.PathTemplate('projects/{project}/locations/{location}/metadataStores/{metadata_store}/executions/{execution}'),
            featureGroupPathTemplate: new this._gaxModule.PathTemplate('projects/{project}/locations/{location}/featureGroups/{feature_group}'),
            featureOnlineStorePathTemplate: new this._gaxModule.PathTemplate('projects/{project}/locations/{location}/featureOnlineStores/{feature_online_store}'),
            featureViewPathTemplate: new this._gaxModule.PathTemplate('projects/{project}/locations/{location}/featureOnlineStores/{feature_online_store}/featureViews/{feature_view}'),
            featureViewSyncPathTemplate: new this._gaxModule.PathTemplate('projects/{project}/locations/{location}/featureOnlineStores/{feature_online_store}/featureViews/{feature_view}/featureViewSyncs/feature_view_sync'),
            featurestorePathTemplate: new this._gaxModule.PathTemplate('projects/{project}/locations/{location}/featurestores/{featurestore}'),
            hyperparameterTuningJobPathTemplate: new this._gaxModule.PathTemplate('projects/{project}/locations/{location}/hyperparameterTuningJobs/{hyperparameter_tuning_job}'),
            indexPathTemplate: new this._gaxModule.PathTemplate('projects/{project}/locations/{location}/indexes/{index}'),
            indexEndpointPathTemplate: new this._gaxModule.PathTemplate('projects/{project}/locations/{location}/indexEndpoints/{index_endpoint}'),
            locationPathTemplate: new this._gaxModule.PathTemplate('projects/{project}/locations/{location}'),
            metadataSchemaPathTemplate: new this._gaxModule.PathTemplate('projects/{project}/locations/{location}/metadataStores/{metadata_store}/metadataSchemas/{metadata_schema}'),
            metadataStorePathTemplate: new this._gaxModule.PathTemplate('projects/{project}/locations/{location}/metadataStores/{metadata_store}'),
            modelPathTemplate: new this._gaxModule.PathTemplate('projects/{project}/locations/{location}/models/{model}'),
            modelDeploymentMonitoringJobPathTemplate: new this._gaxModule.PathTemplate('projects/{project}/locations/{location}/modelDeploymentMonitoringJobs/{model_deployment_monitoring_job}'),
            modelEvaluationPathTemplate: new this._gaxModule.PathTemplate('projects/{project}/locations/{location}/models/{model}/evaluations/{evaluation}'),
            modelEvaluationSlicePathTemplate: new this._gaxModule.PathTemplate('projects/{project}/locations/{location}/models/{model}/evaluations/{evaluation}/slices/{slice}'),
            nasJobPathTemplate: new this._gaxModule.PathTemplate('projects/{project}/locations/{location}/nasJobs/{nas_job}'),
            nasTrialDetailPathTemplate: new this._gaxModule.PathTemplate('projects/{project}/locations/{location}/nasJobs/{nas_job}/nasTrialDetails/{nas_trial_detail}'),
            notebookExecutionJobPathTemplate: new this._gaxModule.PathTemplate('projects/{project}/locations/{location}/notebookExecutionJobs/{notebook_execution_job}'),
            notebookRuntimePathTemplate: new this._gaxModule.PathTemplate('projects/{project}/locations/{location}/notebookRuntimes/{notebook_runtime}'),
            notebookRuntimeTemplatePathTemplate: new this._gaxModule.PathTemplate('projects/{project}/locations/{location}/notebookRuntimeTemplates/{notebook_runtime_template}'),
            persistentResourcePathTemplate: new this._gaxModule.PathTemplate('projects/{project}/locations/{location}/persistentResources/{persistent_resource}'),
            pipelineJobPathTemplate: new this._gaxModule.PathTemplate('projects/{project}/locations/{location}/pipelineJobs/{pipeline_job}'),
            projectPathTemplate: new this._gaxModule.PathTemplate('projects/{project}'),
            projectLocationEndpointPathTemplate: new this._gaxModule.PathTemplate('projects/{project}/locations/{location}/endpoints/{endpoint}'),
            projectLocationFeatureGroupFeaturePathTemplate: new this._gaxModule.PathTemplate('projects/{project}/locations/{location}/featureGroups/{feature_group}/features/{feature}'),
            projectLocationFeaturestoreEntityTypeFeaturePathTemplate: new this._gaxModule.PathTemplate('projects/{project}/locations/{location}/featurestores/{featurestore}/entityTypes/{entity_type}/features/{feature}'),
            projectLocationPublisherModelPathTemplate: new this._gaxModule.PathTemplate('projects/{project}/locations/{location}/publishers/{publisher}/models/{model}'),
            publisherModelPathTemplate: new this._gaxModule.PathTemplate('publishers/{publisher}/models/{model}'),
            ragCorpusPathTemplate: new this._gaxModule.PathTemplate('projects/{project}/locations/{location}/ragCorpora/{rag_corpus}'),
            ragFilePathTemplate: new this._gaxModule.PathTemplate('projects/{project}/locations/{location}/ragCorpora/{rag_corpus}/ragFiles/{rag_file}'),
            reasoningEnginePathTemplate: new this._gaxModule.PathTemplate('projects/{project}/locations/{location}/reasoningEngines/{reasoning_engine}'),
            savedQueryPathTemplate: new this._gaxModule.PathTemplate('projects/{project}/locations/{location}/datasets/{dataset}/savedQueries/{saved_query}'),
            schedulePathTemplate: new this._gaxModule.PathTemplate('projects/{project}/locations/{location}/schedules/{schedule}'),
            specialistPoolPathTemplate: new this._gaxModule.PathTemplate('projects/{project}/locations/{location}/specialistPools/{specialist_pool}'),
            studyPathTemplate: new this._gaxModule.PathTemplate('projects/{project}/locations/{location}/studies/{study}'),
            tensorboardPathTemplate: new this._gaxModule.PathTemplate('projects/{project}/locations/{location}/tensorboards/{tensorboard}'),
            tensorboardExperimentPathTemplate: new this._gaxModule.PathTemplate('projects/{project}/locations/{location}/tensorboards/{tensorboard}/experiments/{experiment}'),
            tensorboardRunPathTemplate: new this._gaxModule.PathTemplate('projects/{project}/locations/{location}/tensorboards/{tensorboard}/experiments/{experiment}/runs/{run}'),
            tensorboardTimeSeriesPathTemplate: new this._gaxModule.PathTemplate('projects/{project}/locations/{location}/tensorboards/{tensorboard}/experiments/{experiment}/runs/{run}/timeSeries/{time_series}'),
            trainingPipelinePathTemplate: new this._gaxModule.PathTemplate('projects/{project}/locations/{location}/trainingPipelines/{training_pipeline}'),
            trialPathTemplate: new this._gaxModule.PathTemplate('projects/{project}/locations/{location}/studies/{study}/trials/{trial}'),
            tuningJobPathTemplate: new this._gaxModule.PathTemplate('projects/{project}/locations/{location}/tuningJobs/{tuning_job}'),
        };
        // Some of the methods on this service return "paged" results,
        // (e.g. 50 results at a time, with tokens to get subsequent
        // pages). Denote the keys used for pagination and results.
        this.descriptors.page = {
            listFeatureGroups: new this._gaxModule.PageDescriptor('pageToken', 'nextPageToken', 'featureGroups'),
            listFeatures: new this._gaxModule.PageDescriptor('pageToken', 'nextPageToken', 'features'),
        };
        const protoFilesRoot = this._gaxModule.protobuf.Root.fromJSON(jsonProtos);
        // This API contains "long-running operations", which return a
        // an Operation object that allows for tracking of the operation,
        // rather than holding a request open.
        const lroOptions = {
            auth: this.auth,
            grpc: 'grpc' in this._gaxGrpc ? this._gaxGrpc.grpc : undefined,
        };
        if (opts.fallback) {
            lroOptions.protoJson = protoFilesRoot;
            lroOptions.httpRules = [
                {
                    selector: 'google.cloud.location.Locations.GetLocation',
                    get: '/ui/{name=projects/*/locations/*}',
                    additional_bindings: [{ get: '/v1/{name=projects/*/locations/*}' }],
                },
                {
                    selector: 'google.cloud.location.Locations.ListLocations',
                    get: '/ui/{name=projects/*}/locations',
                    additional_bindings: [{ get: '/v1/{name=projects/*}/locations' }],
                },
                {
                    selector: 'google.iam.v1.IAMPolicy.GetIamPolicy',
                    post: '/v1/{resource=projects/*/locations/*/featurestores/*}:getIamPolicy',
                    additional_bindings: [
                        {
                            post: '/v1/{resource=projects/*/locations/*/featurestores/*/entityTypes/*}:getIamPolicy',
                        },
                        {
                            post: '/v1/{resource=projects/*/locations/*/models/*}:getIamPolicy',
                        },
                        {
                            post: '/v1/{resource=projects/*/locations/*/notebookRuntimeTemplates/*}:getIamPolicy',
                        },
                        {
                            post: '/v1/{resource=projects/*/locations/*/featureOnlineStores/*}:getIamPolicy',
                        },
                        {
                            post: '/v1/{resource=projects/*/locations/*/featureOnlineStores/*/featureViews/*}:getIamPolicy',
                        },
                        {
                            post: '/ui/{resource=projects/*/locations/*/featurestores/*}:getIamPolicy',
                        },
                        {
                            post: '/ui/{resource=projects/*/locations/*/featurestores/*/entityTypes/*}:getIamPolicy',
                        },
                        {
                            post: '/ui/{resource=projects/*/locations/*/models/*}:getIamPolicy',
                        },
                        {
                            post: '/ui/{resource=projects/*/locations/*/endpoints/*}:getIamPolicy',
                        },
                        {
                            post: '/ui/{resource=projects/*/locations/*/notebookRuntimeTemplates/*}:getIamPolicy',
                        },
                        {
                            post: '/ui/{resource=projects/*/locations/*/publishers/*/models/*}:getIamPolicy',
                        },
                        {
                            post: '/ui/{resource=projects/*/locations/*/featureOnlineStores/*}:getIamPolicy',
                        },
                        {
                            post: '/ui/{resource=projects/*/locations/*/featureOnlineStores/*/featureViews/*}:getIamPolicy',
                        },
                        {
                            post: '/ui/{resource=projects/*/locations/*/featureGroups/*}:getIamPolicy',
                        },
                    ],
                },
                {
                    selector: 'google.iam.v1.IAMPolicy.SetIamPolicy',
                    post: '/v1/{resource=projects/*/locations/*/featurestores/*}:setIamPolicy',
                    body: '*',
                    additional_bindings: [
                        {
                            post: '/v1/{resource=projects/*/locations/*/featurestores/*/entityTypes/*}:setIamPolicy',
                            body: '*',
                        },
                        {
                            post: '/v1/{resource=projects/*/locations/*/models/*}:setIamPolicy',
                            body: '*',
                        },
                        {
                            post: '/v1/{resource=projects/*/locations/*/notebookRuntimeTemplates/*}:setIamPolicy',
                            body: '*',
                        },
                        {
                            post: '/v1/{resource=projects/*/locations/*/featureOnlineStores/*}:setIamPolicy',
                            body: '*',
                        },
                        {
                            post: '/v1/{resource=projects/*/locations/*/featureOnlineStores/*/featureViews/*}:setIamPolicy',
                            body: '*',
                        },
                        {
                            post: '/ui/{resource=projects/*/locations/*/featurestores/*}:setIamPolicy',
                            body: '*',
                        },
                        {
                            post: '/ui/{resource=projects/*/locations/*/featurestores/*/entityTypes/*}:setIamPolicy',
                            body: '*',
                        },
                        {
                            post: '/ui/{resource=projects/*/locations/*/models/*}:setIamPolicy',
                            body: '*',
                        },
                        {
                            post: '/ui/{resource=projects/*/locations/*/endpoints/*}:setIamPolicy',
                            body: '*',
                        },
                        {
                            post: '/ui/{resource=projects/*/locations/*/notebookRuntimeTemplates/*}:setIamPolicy',
                            body: '*',
                        },
                        {
                            post: '/ui/{resource=projects/*/locations/*/featureOnlineStores/*}:setIamPolicy',
                            body: '*',
                        },
                        {
                            post: '/ui/{resource=projects/*/locations/*/featureOnlineStores/*/featureViews/*}:setIamPolicy',
                            body: '*',
                        },
                        {
                            post: '/ui/{resource=projects/*/locations/*/featureGroups/*}:setIamPolicy',
                            body: '*',
                        },
                    ],
                },
                {
                    selector: 'google.iam.v1.IAMPolicy.TestIamPermissions',
                    post: '/v1/{resource=projects/*/locations/*/featurestores/*}:testIamPermissions',
                    additional_bindings: [
                        {
                            post: '/v1/{resource=projects/*/locations/*/featurestores/*/entityTypes/*}:testIamPermissions',
                        },
                        {
                            post: '/v1/{resource=projects/*/locations/*/models/*}:testIamPermissions',
                        },
                        {
                            post: '/v1/{resource=projects/*/locations/*/notebookRuntimeTemplates/*}:testIamPermissions',
                        },
                        {
                            post: '/v1/{resource=projects/*/locations/*/featureOnlineStores/*}:testIamPermissions',
                        },
                        {
                            post: '/v1/{resource=projects/*/locations/*/featureOnlineStores/*/featureViews/*}:testIamPermissions',
                        },
                        {
                            post: '/ui/{resource=projects/*/locations/*/featurestores/*}:testIamPermissions',
                        },
                        {
                            post: '/ui/{resource=projects/*/locations/*/featurestores/*/entityTypes/*}:testIamPermissions',
                        },
                        {
                            post: '/ui/{resource=projects/*/locations/*/models/*}:testIamPermissions',
                        },
                        {
                            post: '/ui/{resource=projects/*/locations/*/endpoints/*}:testIamPermissions',
                        },
                        {
                            post: '/ui/{resource=projects/*/locations/*/notebookRuntimeTemplates/*}:testIamPermissions',
                        },
                        {
                            post: '/ui/{resource=projects/*/locations/*/featureOnlineStores/*}:testIamPermissions',
                        },
                        {
                            post: '/ui/{resource=projects/*/locations/*/featureOnlineStores/*/featureViews/*}:testIamPermissions',
                        },
                        {
                            post: '/ui/{resource=projects/*/locations/*/featureGroups/*}:testIamPermissions',
                        },
                    ],
                },
                {
                    selector: 'google.longrunning.Operations.CancelOperation',
                    post: '/ui/{name=projects/*/locations/*/operations/*}:cancel',
                    additional_bindings: [
                        {
                            post: '/ui/{name=projects/*/locations/*/agents/*/operations/*}:cancel',
                        },
                        {
                            post: '/ui/{name=projects/*/locations/*/apps/*/operations/*}:cancel',
                        },
                        {
                            post: '/ui/{name=projects/*/locations/*/datasets/*/operations/*}:cancel',
                        },
                        {
                            post: '/ui/{name=projects/*/locations/*/datasets/*/dataItems/*/operations/*}:cancel',
                        },
                        {
                            post: '/ui/{name=projects/*/locations/*/datasets/*/savedQueries/*/operations/*}:cancel',
                        },
                        {
                            post: '/ui/{name=projects/*/locations/*/datasets/*/annotationSpecs/*/operations/*}:cancel',
                        },
                        {
                            post: '/ui/{name=projects/*/locations/*/datasets/*/dataItems/*/annotations/*/operations/*}:cancel',
                        },
                        {
                            post: '/ui/{name=projects/*/locations/*/deploymentResourcePools/*/operations/*}:cancel',
                        },
                        {
                            post: '/ui/{name=projects/*/locations/*/edgeDevices/*/operations/*}:cancel',
                        },
                        {
                            post: '/ui/{name=projects/*/locations/*/endpoints/*/operations/*}:cancel',
                        },
                        {
                            post: '/ui/{name=projects/*/locations/*/extensionControllers/*/operations/*}:cancel',
                        },
                        {
                            post: '/ui/{name=projects/*/locations/*/extensions/*/operations/*}:cancel',
                        },
                        {
                            post: '/ui/{name=projects/*/locations/*/featurestores/*/operations/*}:cancel',
                        },
                        {
                            post: '/ui/{name=projects/*/locations/*/featurestores/*/entityTypes/*/operations/*}:cancel',
                        },
                        {
                            post: '/ui/{name=projects/*/locations/*/featurestores/*/entityTypes/*/features/*/operations/*}:cancel',
                        },
                        {
                            post: '/ui/{name=projects/*/locations/*/customJobs/*/operations/*}:cancel',
                        },
                        {
                            post: '/ui/{name=projects/*/locations/*/dataLabelingJobs/*/operations/*}:cancel',
                        },
                        {
                            post: '/ui/{name=projects/*/locations/*/hyperparameterTuningJobs/*/operations/*}:cancel',
                        },
                        {
                            post: '/ui/{name=projects/*/locations/*/tuningJobs/*/operations/*}:cancel',
                        },
                        {
                            post: '/ui/{name=projects/*/locations/*/indexes/*/operations/*}:cancel',
                        },
                        {
                            post: '/ui/{name=projects/*/locations/*/indexEndpoints/*/operations/*}:cancel',
                        },
                        {
                            post: '/ui/{name=projects/*/locations/*/metadataStores/*/operations/*}:cancel',
                        },
                        {
                            post: '/ui/{name=projects/*/locations/*/metadataStores/*/artifacts/*/operations/*}:cancel',
                        },
                        {
                            post: '/ui/{name=projects/*/locations/*/metadataStores/*/contexts/*/operations/*}:cancel',
                        },
                        {
                            post: '/ui/{name=projects/*/locations/*/metadataStores/*/executions/*/operations/*}:cancel',
                        },
                        {
                            post: '/ui/{name=projects/*/locations/*/modelDeploymentMonitoringJobs/*/operations/*}:cancel',
                        },
                        {
                            post: '/ui/{name=projects/*/locations/*/modelMonitors/*/operations/*}:cancel',
                        },
                        {
                            post: '/ui/{name=projects/*/locations/*/migratableResources/*/operations/*}:cancel',
                        },
                        {
                            post: '/ui/{name=projects/*/locations/*/models/*/operations/*}:cancel',
                        },
                        {
                            post: '/ui/{name=projects/*/locations/*/models/*/evaluations/*/operations/*}:cancel',
                        },
                        {
                            post: '/ui/{name=projects/*/locations/*/notebookExecutionJobs/*/operations/*}:cancel',
                        },
                        {
                            post: '/ui/{name=projects/*/locations/*/notebookRuntimes/*/operations/*}:cancel',
                        },
                        {
                            post: '/ui/{name=projects/*/locations/*/notebookRuntimeTemplates/*/operations/*}:cancel',
                        },
                        {
                            post: '/ui/{name=projects/*/locations/*/persistentResources/*/operations/*}:cancel',
                        },
                        {
                            post: '/ui/{name=projects/*/locations/*/studies/*/operations/*}:cancel',
                        },
                        {
                            post: '/ui/{name=projects/*/locations/*/studies/*/trials/*/operations/*}:cancel',
                        },
                        {
                            post: '/ui/{name=projects/*/locations/*/trainingPipelines/*/operations/*}:cancel',
                        },
                        {
                            post: '/ui/{name=projects/*/locations/*/pipelineJobs/*/operations/*}:cancel',
                        },
                        {
                            post: '/ui/{name=projects/*/locations/*/schedules/*/operations/*}:cancel',
                        },
                        {
                            post: '/ui/{name=projects/*/locations/*/specialistPools/*/operations/*}:cancel',
                        },
                        {
                            post: '/ui/{name=projects/*/locations/*/tensorboards/*/operations/*}:cancel',
                        },
                        {
                            post: '/ui/{name=projects/*/locations/*/tensorboards/*/experiments/*/operations/*}:cancel',
                        },
                        {
                            post: '/ui/{name=projects/*/locations/*/tensorboards/*/experiments/*/runs/*/operations/*}:cancel',
                        },
                        {
                            post: '/ui/{name=projects/*/locations/*/tensorboards/*/experiments/*/runs/*/timeSeries/*/operations/*}:cancel',
                        },
                        { post: '/v1/{name=projects/*/locations/*/operations/*}:cancel' },
                        {
                            post: '/v1/{name=projects/*/locations/*/datasets/*/operations/*}:cancel',
                        },
                        {
                            post: '/v1/{name=projects/*/locations/*/datasets/*/dataItems/*/operations/*}:cancel',
                        },
                        {
                            post: '/v1/{name=projects/*/locations/*/datasets/*/savedQueries/*/operations/*}:cancel',
                        },
                        {
                            post: '/v1/{name=projects/*/locations/*/datasets/*/annotationSpecs/*/operations/*}:cancel',
                        },
                        {
                            post: '/v1/{name=projects/*/locations/*/datasets/*/dataItems/*/annotations/*/operations/*}:cancel',
                        },
                        {
                            post: '/v1/{name=projects/*/locations/*/deploymentResourcePools/*/operations/*}:cancel',
                        },
                        {
                            post: '/v1/{name=projects/*/locations/*/endpoints/*/operations/*}:cancel',
                        },
                        {
                            post: '/v1/{name=projects/*/locations/*/featurestores/*/operations/*}:cancel',
                        },
                        {
                            post: '/v1/{name=projects/*/locations/*/featurestores/*/entityTypes/*/operations/*}:cancel',
                        },
                        {
                            post: '/v1/{name=projects/*/locations/*/featurestores/*/entityTypes/*/features/*/operations/*}:cancel',
                        },
                        {
                            post: '/v1/{name=projects/*/locations/*/customJobs/*/operations/*}:cancel',
                        },
                        {
                            post: '/v1/{name=projects/*/locations/*/dataLabelingJobs/*/operations/*}:cancel',
                        },
                        {
                            post: '/v1/{name=projects/*/locations/*/hyperparameterTuningJobs/*/operations/*}:cancel',
                        },
                        {
                            post: '/v1/{name=projects/*/locations/*/tuningJobs/*/operations/*}:cancel',
                        },
                        {
                            post: '/v1/{name=projects/*/locations/*/indexes/*/operations/*}:cancel',
                        },
                        {
                            post: '/v1/{name=projects/*/locations/*/indexEndpoints/*/operations/*}:cancel',
                        },
                        {
                            post: '/v1/{name=projects/*/locations/*/metadataStores/*/operations/*}:cancel',
                        },
                        {
                            post: '/v1/{name=projects/*/locations/*/metadataStores/*/artifacts/*/operations/*}:cancel',
                        },
                        {
                            post: '/v1/{name=projects/*/locations/*/metadataStores/*/contexts/*/operations/*}:cancel',
                        },
                        {
                            post: '/v1/{name=projects/*/locations/*/metadataStores/*/executions/*/operations/*}:cancel',
                        },
                        {
                            post: '/v1/{name=projects/*/locations/*/modelDeploymentMonitoringJobs/*/operations/*}:cancel',
                        },
                        {
                            post: '/v1/{name=projects/*/locations/*/migratableResources/*/operations/*}:cancel',
                        },
                        {
                            post: '/v1/{name=projects/*/locations/*/models/*/operations/*}:cancel',
                        },
                        {
                            post: '/v1/{name=projects/*/locations/*/models/*/evaluations/*/operations/*}:cancel',
                        },
                        {
                            post: '/v1/{name=projects/*/locations/*/notebookExecutionJobs/*/operations/*}:cancel',
                        },
                        {
                            post: '/v1/{name=projects/*/locations/*/notebookRuntimes/*/operations/*}:cancel',
                        },
                        {
                            post: '/v1/{name=projects/*/locations/*/notebookRuntimeTemplates/*/operations/*}:cancel',
                        },
                        {
                            post: '/v1/{name=projects/*/locations/*/persistentResources/*/operations/*}:cancel',
                        },
                        {
                            post: '/v1/{name=projects/*/locations/*/ragCorpora/*/operations/*}:cancel',
                        },
                        {
                            post: '/v1/{name=projects/*/locations/*/ragCorpora/*/ragFiles/*/operations/*}:cancel',
                        },
                        {
                            post: '/v1/{name=projects/*/locations/*/reasoningEngines/*/operations/*}:cancel',
                        },
                        {
                            post: '/v1/{name=projects/*/locations/*/studies/*/operations/*}:cancel',
                        },
                        {
                            post: '/v1/{name=projects/*/locations/*/studies/*/trials/*/operations/*}:cancel',
                        },
                        {
                            post: '/v1/{name=projects/*/locations/*/trainingPipelines/*/operations/*}:cancel',
                        },
                        {
                            post: '/v1/{name=projects/*/locations/*/pipelineJobs/*/operations/*}:cancel',
                        },
                        {
                            post: '/v1/{name=projects/*/locations/*/schedules/*/operations/*}:cancel',
                        },
                        {
                            post: '/v1/{name=projects/*/locations/*/specialistPools/*/operations/*}:cancel',
                        },
                        {
                            post: '/v1/{name=projects/*/locations/*/tensorboards/*/operations/*}:cancel',
                        },
                        {
                            post: '/v1/{name=projects/*/locations/*/tensorboards/*/experiments/*/operations/*}:cancel',
                        },
                        {
                            post: '/v1/{name=projects/*/locations/*/tensorboards/*/experiments/*/runs/*/operations/*}:cancel',
                        },
                        {
                            post: '/v1/{name=projects/*/locations/*/tensorboards/*/experiments/*/runs/*/timeSeries/*/operations/*}:cancel',
                        },
                    ],
                },
                {
                    selector: 'google.longrunning.Operations.DeleteOperation',
                    delete: '/ui/{name=projects/*/locations/*/operations/*}',
                    additional_bindings: [
                        { delete: '/ui/{name=projects/*/locations/*/agents/*/operations/*}' },
                        { delete: '/ui/{name=projects/*/locations/*/apps/*/operations/*}' },
                        {
                            delete: '/ui/{name=projects/*/locations/*/datasets/*/operations/*}',
                        },
                        {
                            delete: '/ui/{name=projects/*/locations/*/datasets/*/dataItems/*/operations/*}',
                        },
                        {
                            delete: '/ui/{name=projects/*/locations/*/datasets/*/savedQueries/*/operations/*}',
                        },
                        {
                            delete: '/ui/{name=projects/*/locations/*/datasets/*/annotationSpecs/*/operations/*}',
                        },
                        {
                            delete: '/ui/{name=projects/*/locations/*/datasets/*/dataItems/*/annotations/*/operations/*}',
                        },
                        {
                            delete: '/ui/{name=projects/*/locations/*/deploymentResourcePools/*/operations/*}',
                        },
                        {
                            delete: '/ui/{name=projects/*/locations/*/edgeDevices/*/operations/*}',
                        },
                        {
                            delete: '/ui/{name=projects/*/locations/*/endpoints/*/operations/*}',
                        },
                        {
                            delete: '/ui/{name=projects/*/locations/*/extensionControllers/*}/operations',
                        },
                        {
                            delete: '/ui/{name=projects/*/locations/*/extensions/*}/operations',
                        },
                        {
                            delete: '/ui/{name=projects/*/locations/*/featurestores/*/operations/*}',
                        },
                        {
                            delete: '/ui/{name=projects/*/locations/*/featurestores/*/entityTypes/*/operations/*}',
                        },
                        {
                            delete: '/ui/{name=projects/*/locations/*/featurestores/*/entityTypes/*/features/*/operations/*}',
                        },
                        {
                            delete: '/ui/{name=projects/*/locations/*/customJobs/*/operations/*}',
                        },
                        {
                            delete: '/ui/{name=projects/*/locations/*/dataLabelingJobs/*/operations/*}',
                        },
                        {
                            delete: '/ui/{name=projects/*/locations/*/hyperparameterTuningJobs/*/operations/*}',
                        },
                        {
                            delete: '/ui/{name=projects/*/locations/*/indexes/*/operations/*}',
                        },
                        {
                            delete: '/ui/{name=projects/*/locations/*/indexEndpoints/*/operations/*}',
                        },
                        {
                            delete: '/ui/{name=projects/*/locations/*/metadataStores/*/operations/*}',
                        },
                        {
                            delete: '/ui/{name=projects/*/locations/*/metadataStores/*/artifacts/*/operations/*}',
                        },
                        {
                            delete: '/ui/{name=projects/*/locations/*/metadataStores/*/contexts/*/operations/*}',
                        },
                        {
                            delete: '/ui/{name=projects/*/locations/*/metadataStores/*/executions/*/operations/*}',
                        },
                        {
                            delete: '/ui/{name=projects/*/locations/*/modelDeploymentMonitoringJobs/*/operations/*}',
                        },
                        {
                            delete: '/ui/{name=projects/*/locations/*/modelMonitors/*/operations/*}',
                        },
                        {
                            delete: '/ui/{name=projects/*/locations/*/migratableResources/*/operations/*}',
                        },
                        { delete: '/ui/{name=projects/*/locations/*/models/*/operations/*}' },
                        {
                            delete: '/ui/{name=projects/*/locations/*/models/*/evaluations/*/operations/*}',
                        },
                        {
                            delete: '/ui/{name=projects/*/locations/*/notebookExecutionJobs/*/operations/*}',
                        },
                        {
                            delete: '/ui/{name=projects/*/locations/*/notebookRuntimes/*/operations/*}',
                        },
                        {
                            delete: '/ui/{name=projects/*/locations/*/notebookRuntimeTemplates/*/operations/*}',
                        },
                        {
                            delete: '/ui/{name=projects/*/locations/*/persistentResources/*/operations/*}',
                        },
                        {
                            delete: '/ui/{name=projects/*/locations/*/studies/*/operations/*}',
                        },
                        {
                            delete: '/ui/{name=projects/*/locations/*/studies/*/trials/*/operations/*}',
                        },
                        {
                            delete: '/ui/{name=projects/*/locations/*/trainingPipelines/*/operations/*}',
                        },
                        {
                            delete: '/ui/{name=projects/*/locations/*/pipelineJobs/*/operations/*}',
                        },
                        {
                            delete: '/ui/{name=projects/*/locations/*/schedules/*/operations/*}',
                        },
                        {
                            delete: '/ui/{name=projects/*/locations/*/specialistPools/*/operations/*}',
                        },
                        {
                            delete: '/ui/{name=projects/*/locations/*/tensorboards/*/operations/*}',
                        },
                        {
                            delete: '/ui/{name=projects/*/locations/*/tensorboards/*/experiments/*/operations/*}',
                        },
                        {
                            delete: '/ui/{name=projects/*/locations/*/tensorboards/*/experiments/*/runs/*/operations/*}',
                        },
                        {
                            delete: '/ui/{name=projects/*/locations/*/tensorboards/*/experiments/*/runs/*/timeSeries/*/operations/*}',
                        },
                        {
                            delete: '/ui/{name=projects/*/locations/*/featureOnlineStores/*/operations/*}',
                        },
                        {
                            delete: '/ui/{name=projects/*/locations/*/featureGroups/*/operations/*}',
                        },
                        {
                            delete: '/ui/{name=projects/*/locations/*/featureGroups/*/features/*/operations/*}',
                        },
                        {
                            delete: '/ui/{name=projects/*/locations/*/featureGroups/*/featureMonitors/*/operations/*}',
                        },
                        {
                            delete: '/ui/{name=projects/*/locations/*/featureOnlineStores/*/featureViews/*/operations/*}',
                        },
                        { delete: '/v1/{name=projects/*/locations/*/operations/*}' },
                        {
                            delete: '/v1/{name=projects/*/locations/*/datasets/*/operations/*}',
                        },
                        {
                            delete: '/v1/{name=projects/*/locations/*/datasets/*/dataItems/*/operations/*}',
                        },
                        {
                            delete: '/v1/{name=projects/*/locations/*/datasets/*/savedQueries/*/operations/*}',
                        },
                        {
                            delete: '/v1/{name=projects/*/locations/*/datasets/*/annotationSpecs/*/operations/*}',
                        },
                        {
                            delete: '/v1/{name=projects/*/locations/*/datasets/*/dataItems/*/annotations/*/operations/*}',
                        },
                        {
                            delete: '/v1/{name=projects/*/locations/*/deploymentResourcePools/*/operations/*}',
                        },
                        {
                            delete: '/v1/{name=projects/*/locations/*/endpoints/*/operations/*}',
                        },
                        {
                            delete: '/v1/{name=projects/*/locations/*/featurestores/*/operations/*}',
                        },
                        {
                            delete: '/v1/{name=projects/*/locations/*/featurestores/*/entityTypes/*/operations/*}',
                        },
                        {
                            delete: '/v1/{name=projects/*/locations/*/featurestores/*/entityTypes/*/features/*/operations/*}',
                        },
                        {
                            delete: '/v1/{name=projects/*/locations/*/customJobs/*/operations/*}',
                        },
                        {
                            delete: '/v1/{name=projects/*/locations/*/dataLabelingJobs/*/operations/*}',
                        },
                        {
                            delete: '/v1/{name=projects/*/locations/*/hyperparameterTuningJobs/*/operations/*}',
                        },
                        {
                            delete: '/v1/{name=projects/*/locations/*/indexes/*/operations/*}',
                        },
                        {
                            delete: '/v1/{name=projects/*/locations/*/indexEndpoints/*/operations/*}',
                        },
                        {
                            delete: '/v1/{name=projects/*/locations/*/metadataStores/*/operations/*}',
                        },
                        {
                            delete: '/v1/{name=projects/*/locations/*/metadataStores/*/artifacts/*/operations/*}',
                        },
                        {
                            delete: '/v1/{name=projects/*/locations/*/metadataStores/*/contexts/*/operations/*}',
                        },
                        {
                            delete: '/v1/{name=projects/*/locations/*/metadataStores/*/executions/*/operations/*}',
                        },
                        {
                            delete: '/v1/{name=projects/*/locations/*/modelDeploymentMonitoringJobs/*/operations/*}',
                        },
                        {
                            delete: '/v1/{name=projects/*/locations/*/migratableResources/*/operations/*}',
                        },
                        { delete: '/v1/{name=projects/*/locations/*/models/*/operations/*}' },
                        {
                            delete: '/v1/{name=projects/*/locations/*/models/*/evaluations/*/operations/*}',
                        },
                        {
                            delete: '/v1/{name=projects/*/locations/*/notebookExecutionJobs/*/operations/*}',
                        },
                        {
                            delete: '/v1/{name=projects/*/locations/*/notebookRuntimes/*/operations/*}',
                        },
                        {
                            delete: '/v1/{name=projects/*/locations/*/notebookRuntimeTemplates/*/operations/*}',
                        },
                        {
                            delete: '/v1/{name=projects/*/locations/*/ragCorpora/*/operations/*}',
                        },
                        {
                            delete: '/v1/{name=projects/*/locations/*/ragCorpora/*/ragFiles/*/operations/*}',
                        },
                        {
                            delete: '/v1/{name=projects/*/locations/*/reasoningEngines/*/operations/*}',
                        },
                        {
                            delete: '/v1/{name=projects/*/locations/*/studies/*/operations/*}',
                        },
                        {
                            delete: '/v1/{name=projects/*/locations/*/studies/*/trials/*/operations/*}',
                        },
                        {
                            delete: '/v1/{name=projects/*/locations/*/trainingPipelines/*/operations/*}',
                        },
                        {
                            delete: '/v1/{name=projects/*/locations/*/persistentResources/*/operations/*}',
                        },
                        {
                            delete: '/v1/{name=projects/*/locations/*/pipelineJobs/*/operations/*}',
                        },
                        {
                            delete: '/v1/{name=projects/*/locations/*/schedules/*/operations/*}',
                        },
                        {
                            delete: '/v1/{name=projects/*/locations/*/specialistPools/*/operations/*}',
                        },
                        {
                            delete: '/v1/{name=projects/*/locations/*/tensorboards/*/operations/*}',
                        },
                        {
                            delete: '/v1/{name=projects/*/locations/*/tensorboards/*/experiments/*/operations/*}',
                        },
                        {
                            delete: '/v1/{name=projects/*/locations/*/tensorboards/*/experiments/*/runs/*/operations/*}',
                        },
                        {
                            delete: '/v1/{name=projects/*/locations/*/tensorboards/*/experiments/*/runs/*/timeSeries/*/operations/*}',
                        },
                        {
                            delete: '/v1/{name=projects/*/locations/*/featureOnlineStores/*/operations/*}',
                        },
                        {
                            delete: '/v1/{name=projects/*/locations/*/featureGroups/*/operations/*}',
                        },
                        {
                            delete: '/v1/{name=projects/*/locations/*/featureGroups/*/features/*/operations/*}',
                        },
                        {
                            delete: '/v1/{name=projects/*/locations/*/featureOnlineStores/*/featureViews/*/operations/*}',
                        },
                    ],
                },
                {
                    selector: 'google.longrunning.Operations.GetOperation',
                    get: '/ui/{name=projects/*/locations/*/operations/*}',
                    additional_bindings: [
                        { get: '/ui/{name=projects/*/locations/*/agents/*/operations/*}' },
                        { get: '/ui/{name=projects/*/locations/*/apps/*/operations/*}' },
                        { get: '/ui/{name=projects/*/locations/*/datasets/*/operations/*}' },
                        {
                            get: '/ui/{name=projects/*/locations/*/datasets/*/dataItems/*/operations/*}',
                        },
                        {
                            get: '/ui/{name=projects/*/locations/*/datasets/*/savedQueries/*/operations/*}',
                        },
                        {
                            get: '/ui/{name=projects/*/locations/*/datasets/*/annotationSpecs/*/operations/*}',
                        },
                        {
                            get: '/ui/{name=projects/*/locations/*/datasets/*/dataItems/*/annotations/*/operations/*}',
                        },
                        {
                            get: '/ui/{name=projects/*/locations/*/deploymentResourcePools/*/operations/*}',
                        },
                        {
                            get: '/ui/{name=projects/*/locations/*/edgeDeploymentJobs/*/operations/*}',
                        },
                        {
                            get: '/ui/{name=projects/*/locations/*/edgeDevices/*/operations/*}',
                        },
                        { get: '/ui/{name=projects/*/locations/*/endpoints/*/operations/*}' },
                        {
                            get: '/ui/{name=projects/*/locations/*/extensionControllers/*/operations/*}',
                        },
                        {
                            get: '/ui/{name=projects/*/locations/*/extensions/*/operations/*}',
                        },
                        {
                            get: '/ui/{name=projects/*/locations/*/featurestores/*/operations/*}',
                        },
                        {
                            get: '/ui/{name=projects/*/locations/*/featurestores/*/entityTypes/*/operations/*}',
                        },
                        {
                            get: '/ui/{name=projects/*/locations/*/featurestores/*/entityTypes/*/features/*/operations/*}',
                        },
                        {
                            get: '/ui/{name=projects/*/locations/*/customJobs/*/operations/*}',
                        },
                        {
                            get: '/ui/{name=projects/*/locations/*/dataLabelingJobs/*/operations/*}',
                        },
                        {
                            get: '/ui/{name=projects/*/locations/*/hyperparameterTuningJobs/*/operations/*}',
                        },
                        {
                            get: '/ui/{name=projects/*/locations/*/tuningJobs/*/operations/*}',
                        },
                        { get: '/ui/{name=projects/*/locations/*/indexes/*/operations/*}' },
                        {
                            get: '/ui/{name=projects/*/locations/*/indexEndpoints/*/operations/*}',
                        },
                        {
                            get: '/ui/{name=projects/*/locations/*/metadataStores/*/operations/*}',
                        },
                        {
                            get: '/ui/{name=projects/*/locations/*/metadataStores/*/artifacts/*/operations/*}',
                        },
                        {
                            get: '/ui/{name=projects/*/locations/*/metadataStores/*/contexts/*/operations/*}',
                        },
                        {
                            get: '/ui/{name=projects/*/locations/*/metadataStores/*/executions/*/operations/*}',
                        },
                        {
                            get: '/ui/{name=projects/*/locations/*/modelDeploymentMonitoringJobs/*/operations/*}',
                        },
                        {
                            get: '/ui/{name=projects/*/locations/*/modelMonitors/*/operations/*}',
                        },
                        {
                            get: '/ui/{name=projects/*/locations/*/migratableResources/*/operations/*}',
                        },
                        { get: '/ui/{name=projects/*/locations/*/models/*/operations/*}' },
                        {
                            get: '/ui/{name=projects/*/locations/*/models/*/evaluations/*/operations/*}',
                        },
                        {
                            get: '/ui/{name=projects/*/locations/*/notebookExecutionJobs/*/operations/*}',
                        },
                        {
                            get: '/ui/{name=projects/*/locations/*/notebookRuntimes/*/operations/*}',
                        },
                        {
                            get: '/ui/{name=projects/*/locations/*/notebookRuntimeTemplates/*/operations/*}',
                        },
                        {
                            get: '/ui/{name=projects/*/locations/*/persistentResources/*/operations/*}',
                        },
                        { get: '/ui/{name=projects/*/locations/*/studies/*/operations/*}' },
                        {
                            get: '/ui/{name=projects/*/locations/*/studies/*/trials/*/operations/*}',
                        },
                        {
                            get: '/ui/{name=projects/*/locations/*/trainingPipelines/*/operations/*}',
                        },
                        {
                            get: '/ui/{name=projects/*/locations/*/pipelineJobs/*/operations/*}',
                        },
                        { get: '/ui/{name=projects/*/locations/*/schedules/*/operations/*}' },
                        {
                            get: '/ui/{name=projects/*/locations/*/specialistPools/*/operations/*}',
                        },
                        {
                            get: '/ui/{name=projects/*/locations/*/tensorboards/*/operations/*}',
                        },
                        {
                            get: '/ui/{name=projects/*/locations/*/tensorboards/*/experiments/*/operations/*}',
                        },
                        {
                            get: '/ui/{name=projects/*/locations/*/tensorboards/*/experiments/*/runs/*/operations/*}',
                        },
                        {
                            get: '/ui/{name=projects/*/locations/*/tensorboards/*/experiments/*/runs/*/timeSeries/*/operations/*}',
                        },
                        {
                            get: '/ui/{name=projects/*/locations/*/featureOnlineStores/*/operations/*}',
                        },
                        {
                            get: '/ui/{name=projects/*/locations/*/featureOnlineStores/*/featureViews/*/operations/*}',
                        },
                        {
                            get: '/ui/{name=projects/*/locations/*/featureGroups/*/operations/*}',
                        },
                        {
                            get: '/ui/{name=projects/*/locations/*/featureGroups/*/features/*/operations/*}',
                        },
                        {
                            get: '/ui/{name=projects/*/locations/*/featureGroups/*/featureMonitors/*/operations/*}',
                        },
                        { get: '/v1/{name=projects/*/locations/*/operations/*}' },
                        { get: '/v1/{name=projects/*/locations/*/datasets/*/operations/*}' },
                        {
                            get: '/v1/{name=projects/*/locations/*/datasets/*/dataItems/*/operations/*}',
                        },
                        {
                            get: '/v1/{name=projects/*/locations/*/datasets/*/savedQueries/*/operations/*}',
                        },
                        {
                            get: '/v1/{name=projects/*/locations/*/datasets/*/annotationSpecs/*/operations/*}',
                        },
                        {
                            get: '/v1/{name=projects/*/locations/*/datasets/*/dataItems/*/annotations/*/operations/*}',
                        },
                        {
                            get: '/v1/{name=projects/*/locations/*/deploymentResourcePools/*/operations/*}',
                        },
                        { get: '/v1/{name=projects/*/locations/*/endpoints/*/operations/*}' },
                        {
                            get: '/v1/{name=projects/*/locations/*/featurestores/*/operations/*}',
                        },
                        {
                            get: '/v1/{name=projects/*/locations/*/featurestores/*/entityTypes/*/operations/*}',
                        },
                        {
                            get: '/v1/{name=projects/*/locations/*/featurestores/*/entityTypes/*/features/*/operations/*}',
                        },
                        {
                            get: '/v1/{name=projects/*/locations/*/customJobs/*/operations/*}',
                        },
                        {
                            get: '/v1/{name=projects/*/locations/*/dataLabelingJobs/*/operations/*}',
                        },
                        {
                            get: '/v1/{name=projects/*/locations/*/hyperparameterTuningJobs/*/operations/*}',
                        },
                        {
                            get: '/v1/{name=projects/*/locations/*/tuningJobs/*/operations/*}',
                        },
                        { get: '/v1/{name=projects/*/locations/*/indexes/*/operations/*}' },
                        {
                            get: '/v1/{name=projects/*/locations/*/indexEndpoints/*/operations/*}',
                        },
                        {
                            get: '/v1/{name=projects/*/locations/*/metadataStores/*/operations/*}',
                        },
                        {
                            get: '/v1/{name=projects/*/locations/*/metadataStores/*/artifacts/*/operations/*}',
                        },
                        {
                            get: '/v1/{name=projects/*/locations/*/metadataStores/*/contexts/*/operations/*}',
                        },
                        {
                            get: '/v1/{name=projects/*/locations/*/metadataStores/*/executions/*/operations/*}',
                        },
                        {
                            get: '/v1/{name=projects/*/locations/*/modelDeploymentMonitoringJobs/*/operations/*}',
                        },
                        {
                            get: '/v1/{name=projects/*/locations/*/migratableResources/*/operations/*}',
                        },
                        { get: '/v1/{name=projects/*/locations/*/models/*/operations/*}' },
                        {
                            get: '/v1/{name=projects/*/locations/*/models/*/evaluations/*/operations/*}',
                        },
                        {
                            get: '/v1/{name=projects/*/locations/*/notebookExecutionJobs/*/operations/*}',
                        },
                        {
                            get: '/v1/{name=projects/*/locations/*/notebookRuntimes/*/operations/*}',
                        },
                        {
                            get: '/v1/{name=projects/*/locations/*/notebookRuntimeTemplates/*/operations/*}',
                        },
                        {
                            get: '/v1/{name=projects/*/locations/*/ragCorpora/*/operations/*}',
                        },
                        {
                            get: '/v1/{name=projects/*/locations/*/ragCorpora/*/ragFiles/*/operations/*}',
                        },
                        {
                            get: '/v1/{name=projects/*/locations/*/reasoningEngines/*/operations/*}',
                        },
                        { get: '/v1/{name=projects/*/locations/*/studies/*/operations/*}' },
                        {
                            get: '/v1/{name=projects/*/locations/*/studies/*/trials/*/operations/*}',
                        },
                        {
                            get: '/v1/{name=projects/*/locations/*/trainingPipelines/*/operations/*}',
                        },
                        {
                            get: '/v1/{name=projects/*/locations/*/persistentResources/*/operations/*}',
                        },
                        {
                            get: '/v1/{name=projects/*/locations/*/pipelineJobs/*/operations/*}',
                        },
                        { get: '/v1/{name=projects/*/locations/*/schedules/*/operations/*}' },
                        {
                            get: '/v1/{name=projects/*/locations/*/specialistPools/*/operations/*}',
                        },
                        {
                            get: '/v1/{name=projects/*/locations/*/tensorboards/*/operations/*}',
                        },
                        {
                            get: '/v1/{name=projects/*/locations/*/tensorboards/*/experiments/*/operations/*}',
                        },
                        {
                            get: '/v1/{name=projects/*/locations/*/tensorboards/*/experiments/*/runs/*/operations/*}',
                        },
                        {
                            get: '/v1/{name=projects/*/locations/*/tensorboards/*/experiments/*/runs/*/timeSeries/*/operations/*}',
                        },
                        {
                            get: '/v1/{name=projects/*/locations/*/featureOnlineStores/*/operations/*}',
                        },
                        {
                            get: '/v1/{name=projects/*/locations/*/featureOnlineStores/*/featureViews/*/operations/*}',
                        },
                        {
                            get: '/v1/{name=projects/*/locations/*/featureGroups/*/operations/*}',
                        },
                        {
                            get: '/v1/{name=projects/*/locations/*/featureGroups/*/features/*/operations/*}',
                        },
                    ],
                },
                {
                    selector: 'google.longrunning.Operations.ListOperations',
                    get: '/ui/{name=projects/*/locations/*}/operations',
                    additional_bindings: [
                        { get: '/ui/{name=projects/*/locations/*/agents/*}/operations' },
                        { get: '/ui/{name=projects/*/locations/*/apps/*}/operations' },
                        { get: '/ui/{name=projects/*/locations/*/datasets/*}/operations' },
                        {
                            get: '/ui/{name=projects/*/locations/*/datasets/*/dataItems/*}/operations',
                        },
                        {
                            get: '/ui/{name=projects/*/locations/*/datasets/*/savedQueries/*}/operations',
                        },
                        {
                            get: '/ui/{name=projects/*/locations/*/datasets/*/annotationSpecs/*}/operations',
                        },
                        {
                            get: '/ui/{name=projects/*/locations/*/datasets/*/dataItems/*/annotations/*}/operations',
                        },
                        {
                            get: '/ui/{name=projects/*/locations/*/deploymentResourcePools/*}/operations',
                        },
                        { get: '/ui/{name=projects/*/locations/*/edgeDevices/*}/operations' },
                        { get: '/ui/{name=projects/*/locations/*/endpoints/*}/operations' },
                        {
                            get: '/ui/{name=projects/*/locations/*/extensionControllers/*}/operations',
                        },
                        { get: '/ui/{name=projects/*/locations/*/extensions/*}/operations' },
                        {
                            get: '/ui/{name=projects/*/locations/*/featurestores/*}/operations',
                        },
                        {
                            get: '/ui/{name=projects/*/locations/*/featurestores/*/entityTypes/*}/operations',
                        },
                        {
                            get: '/ui/{name=projects/*/locations/*/featurestores/*/entityTypes/*/features/*}/operations',
                        },
                        { get: '/ui/{name=projects/*/locations/*/customJobs/*}/operations' },
                        {
                            get: '/ui/{name=projects/*/locations/*/dataLabelingJobs/*}/operations',
                        },
                        {
                            get: '/ui/{name=projects/*/locations/*/hyperparameterTuningJobs/*}/operations',
                        },
                        { get: '/ui/{name=projects/*/locations/*/tuningJobs/*}/operations' },
                        { get: '/ui/{name=projects/*/locations/*/indexes/*}/operations' },
                        {
                            get: '/ui/{name=projects/*/locations/*/indexEndpoints/*}/operations',
                        },
                        {
                            get: '/ui/{name=projects/*/locations/*/metadataStores/*}/operations',
                        },
                        {
                            get: '/ui/{name=projects/*/locations/*/metadataStores/*/artifacts/*}/operations',
                        },
                        {
                            get: '/ui/{name=projects/*/locations/*/metadataStores/*/contexts/*}/operations',
                        },
                        {
                            get: '/ui/{name=projects/*/locations/*/metadataStores/*/executions/*}/operations',
                        },
                        {
                            get: '/ui/{name=projects/*/locations/*/modelDeploymentMonitoringJobs/*}/operations',
                        },
                        {
                            get: '/ui/{name=projects/*/locations/*/modelMonitors/*}/operations',
                        },
                        {
                            get: '/ui/{name=projects/*/locations/*/migratableResources/*}/operations',
                        },
                        { get: '/ui/{name=projects/*/locations/*/models/*}/operations' },
                        {
                            get: '/ui/{name=projects/*/locations/*/models/*/evaluations/*}/operations',
                        },
                        {
                            get: '/ui/{name=projects/*/locations/*/notebookExecutionJobs/*}/operations',
                        },
                        {
                            get: '/ui/{name=projects/*/locations/*/notebookRuntimes/*}/operations',
                        },
                        {
                            get: '/ui/{name=projects/*/locations/*/notebookRuntimeTemplates/*}/operations',
                        },
                        { get: '/ui/{name=projects/*/locations/*/studies/*}/operations' },
                        {
                            get: '/ui/{name=projects/*/locations/*/studies/*/trials/*}/operations',
                        },
                        {
                            get: '/ui/{name=projects/*/locations/*/trainingPipelines/*}/operations',
                        },
                        {
                            get: '/ui/{name=projects/*/locations/*/persistentResources/*}/operations',
                        },
                        {
                            get: '/ui/{name=projects/*/locations/*/pipelineJobs/*}/operations',
                        },
                        { get: '/ui/{name=projects/*/locations/*/schedules/*}/operations' },
                        {
                            get: '/ui/{name=projects/*/locations/*/specialistPools/*}/operations',
                        },
                        {
                            get: '/ui/{name=projects/*/locations/*/tensorboards/*}/operations',
                        },
                        {
                            get: '/ui/{name=projects/*/locations/*/tensorboards/*/experiments/*}/operations',
                        },
                        {
                            get: '/ui/{name=projects/*/locations/*/tensorboards/*/experiments/*/runs/*}/operations',
                        },
                        {
                            get: '/ui/{name=projects/*/locations/*/tensorboards/*/experiments/*/runs/*/timeSeries/*}/operations',
                        },
                        {
                            get: '/ui/{name=projects/*/locations/*/featureOnlineStores/*/operations/*}:wait',
                        },
                        {
                            get: '/ui/{name=projects/*/locations/*/featureOnlineStores/*/featureViews/*/operations/*}:wait',
                        },
                        {
                            get: '/ui/{name=projects/*/locations/*/featureGroups/*/operations/*}:wait',
                        },
                        {
                            get: '/ui/{name=projects/*/locations/*/featureGroups/*/features/*/operations/*}:wait',
                        },
                        {
                            get: '/ui/{name=projects/*/locations/*/featureGroups/*/featureMonitors/*/operations/*}:wait',
                        },
                        { get: '/v1/{name=projects/*/locations/*}/operations' },
                        { get: '/v1/{name=projects/*/locations/*/datasets/*}/operations' },
                        {
                            get: '/v1/{name=projects/*/locations/*/datasets/*/dataItems/*}/operations',
                        },
                        {
                            get: '/v1/{name=projects/*/locations/*/datasets/*/savedQueries/*}/operations',
                        },
                        {
                            get: '/v1/{name=projects/*/locations/*/datasets/*/annotationSpecs/*}/operations',
                        },
                        {
                            get: '/v1/{name=projects/*/locations/*/datasets/*/dataItems/*/annotations/*}/operations',
                        },
                        {
                            get: '/v1/{name=projects/*/locations/*/deploymentResourcePools/*}/operations',
                        },
                        { get: '/v1/{name=projects/*/locations/*/endpoints/*}/operations' },
                        {
                            get: '/v1/{name=projects/*/locations/*/featurestores/*}/operations',
                        },
                        {
                            get: '/v1/{name=projects/*/locations/*/featurestores/*/entityTypes/*}/operations',
                        },
                        {
                            get: '/v1/{name=projects/*/locations/*/featurestores/*/entityTypes/*/features/*}/operations',
                        },
                        { get: '/v1/{name=projects/*/locations/*/customJobs/*}/operations' },
                        {
                            get: '/v1/{name=projects/*/locations/*/dataLabelingJobs/*}/operations',
                        },
                        {
                            get: '/v1/{name=projects/*/locations/*/hyperparameterTuningJobs/*}/operations',
                        },
                        { get: '/v1/{name=projects/*/locations/*/tuningJobs/*}/operations' },
                        { get: '/v1/{name=projects/*/locations/*/indexes/*}/operations' },
                        {
                            get: '/v1/{name=projects/*/locations/*/indexEndpoints/*}/operations',
                        },
                        {
                            get: '/v1/{name=projects/*/locations/*/metadataStores/*}/operations',
                        },
                        {
                            get: '/v1/{name=projects/*/locations/*/metadataStores/*/artifacts/*}/operations',
                        },
                        {
                            get: '/v1/{name=projects/*/locations/*/metadataStores/*/contexts/*}/operations',
                        },
                        {
                            get: '/v1/{name=projects/*/locations/*/metadataStores/*/executions/*}/operations',
                        },
                        {
                            get: '/v1/{name=projects/*/locations/*/modelDeploymentMonitoringJobs/*}/operations',
                        },
                        {
                            get: '/v1/{name=projects/*/locations/*/migratableResources/*}/operations',
                        },
                        { get: '/v1/{name=projects/*/locations/*/models/*}/operations' },
                        {
                            get: '/v1/{name=projects/*/locations/*/models/*/evaluations/*}/operations',
                        },
                        {
                            get: '/v1/{name=projects/*/locations/*/notebookExecutionJobs/*}/operations',
                        },
                        {
                            get: '/v1/{name=projects/*/locations/*/notebookRuntimes/*}/operations',
                        },
                        {
                            get: '/v1/{name=projects/*/locations/*/notebookRuntimeTemplates/*}/operations',
                        },
                        {
                            get: '/v1/{name=projects/*/locations/*/reasoningEngines/*}/operations',
                        },
                        { get: '/v1/{name=projects/*/locations/*/studies/*}/operations' },
                        {
                            get: '/v1/{name=projects/*/locations/*/studies/*/trials/*}/operations',
                        },
                        {
                            get: '/v1/{name=projects/*/locations/*/trainingPipelines/*}/operations',
                        },
                        {
                            get: '/v1/{name=projects/*/locations/*/persistentResources/*}/operations',
                        },
                        {
                            get: '/v1/{name=projects/*/locations/*/pipelineJobs/*}/operations',
                        },
                        { get: '/v1/{name=projects/*/locations/*/ragCorpora/*}/operations' },
                        {
                            get: '/v1/{name=projects/*/locations/*/ragCorpora/*/ragFiles/*}/operations',
                        },
                        { get: '/v1/{name=projects/*/locations/*/schedules/*}/operations' },
                        {
                            get: '/v1/{name=projects/*/locations/*/specialistPools/*}/operations',
                        },
                        {
                            get: '/v1/{name=projects/*/locations/*/tensorboards/*}/operations',
                        },
                        {
                            get: '/v1/{name=projects/*/locations/*/tensorboards/*/experiments/*}/operations',
                        },
                        {
                            get: '/v1/{name=projects/*/locations/*/tensorboards/*/experiments/*/runs/*}/operations',
                        },
                        {
                            get: '/v1/{name=projects/*/locations/*/tensorboards/*/experiments/*/runs/*/timeSeries/*}/operations',
                        },
                        {
                            get: '/v1/{name=projects/*/locations/*/featureOnlineStores/*/operations/*}:wait',
                        },
                        {
                            get: '/v1/{name=projects/*/locations/*/featureOnlineStores/*/featureViews/*/operations/*}:wait',
                        },
                        {
                            get: '/v1/{name=projects/*/locations/*/featureGroups/*/operations/*}:wait',
                        },
                        {
                            get: '/v1/{name=projects/*/locations/*/featureGroups/*/features/*/operations/*}:wait',
                        },
                    ],
                },
                {
                    selector: 'google.longrunning.Operations.WaitOperation',
                    post: '/ui/{name=projects/*/locations/*/operations/*}:wait',
                    additional_bindings: [
                        {
                            post: '/ui/{name=projects/*/locations/*/agents/*/operations/*}:wait',
                        },
                        {
                            post: '/ui/{name=projects/*/locations/*/apps/*/operations/*}:wait',
                        },
                        {
                            post: '/ui/{name=projects/*/locations/*/datasets/*/operations/*}:wait',
                        },
                        {
                            post: '/ui/{name=projects/*/locations/*/datasets/*/dataItems/*/operations/*}:wait',
                        },
                        {
                            post: '/ui/{name=projects/*/locations/*/datasets/*/savedQueries/*/operations/*}:wait',
                        },
                        {
                            post: '/ui/{name=projects/*/locations/*/datasets/*/annotationSpecs/*/operations/*}:wait',
                        },
                        {
                            post: '/ui/{name=projects/*/locations/*/datasets/*/dataItems/*/annotations/*/operations/*}:wait',
                        },
                        {
                            post: '/ui/{name=projects/*/locations/*/deploymentResourcePools/*/operations/*}:wait',
                        },
                        {
                            post: '/ui/{name=projects/*/locations/*/edgeDevices/*/operations/*}:wait',
                        },
                        {
                            post: '/ui/{name=projects/*/locations/*/endpoints/*/operations/*}:wait',
                        },
                        {
                            post: '/ui/{name=projects/*/locations/*/extensionControllers/*/operations/*}:wait',
                        },
                        {
                            post: '/ui/{name=projects/*/locations/*/extensions/*/operations/*}:wait',
                        },
                        {
                            post: '/ui/{name=projects/*/locations/*/featurestores/*/operations/*}:wait',
                        },
                        {
                            post: '/ui/{name=projects/*/locations/*/featurestores/*/entityTypes/*/operations/*}:wait',
                        },
                        {
                            post: '/ui/{name=projects/*/locations/*/featurestores/*/entityTypes/*/features/*/operations/*}:wait',
                        },
                        {
                            post: '/ui/{name=projects/*/locations/*/customJobs/*/operations/*}:wait',
                        },
                        {
                            post: '/ui/{name=projects/*/locations/*/dataLabelingJobs/*/operations/*}:wait',
                        },
                        {
                            post: '/ui/{name=projects/*/locations/*/hyperparameterTuningJobs/*/operations/*}:wait',
                        },
                        {
                            post: '/ui/{name=projects/*/locations/*/tuningJobs/*/operations/*}:wait',
                        },
                        {
                            post: '/ui/{name=projects/*/locations/*/indexes/*/operations/*}:wait',
                        },
                        {
                            post: '/ui/{name=projects/*/locations/*/indexEndpoints/*/operations/*}:wait',
                        },
                        {
                            post: '/ui/{name=projects/*/locations/*/metadataStores/*/operations/*}:wait',
                        },
                        {
                            post: '/ui/{name=projects/*/locations/*/metadataStores/*/artifacts/*/operations/*}:wait',
                        },
                        {
                            post: '/ui/{name=projects/*/locations/*/metadataStores/*/contexts/*/operations/*}:wait',
                        },
                        {
                            post: '/ui/{name=projects/*/locations/*/metadataStores/*/executions/*/operations/*}:wait',
                        },
                        {
                            post: '/ui/{name=projects/*/locations/*/modelDeploymentMonitoringJobs/*/operations/*}:wait',
                        },
                        {
                            post: '/ui/{name=projects/*/locations/*/modelMonitors/*/operations/*}:wait',
                        },
                        {
                            post: '/ui/{name=projects/*/locations/*/migratableResources/*/operations/*}:wait',
                        },
                        {
                            post: '/ui/{name=projects/*/locations/*/models/*/operations/*}:wait',
                        },
                        {
                            post: '/ui/{name=projects/*/locations/*/models/*/evaluations/*/operations/*}:wait',
                        },
                        {
                            post: '/ui/{name=projects/*/locations/*/notebookExecutionJobs/*/operations/*}:wait',
                        },
                        {
                            post: '/ui/{name=projects/*/locations/*/notebookRuntimes/*/operations/*}:wait',
                        },
                        {
                            post: '/ui/{name=projects/*/locations/*/notebookRuntimeTemplates/*/operations/*}:wait',
                        },
                        {
                            post: '/ui/{name=projects/*/locations/*/studies/*/operations/*}:wait',
                        },
                        {
                            post: '/ui/{name=projects/*/locations/*/studies/*/trials/*/operations/*}:wait',
                        },
                        {
                            post: '/ui/{name=projects/*/locations/*/trainingPipelines/*/operations/*}:wait',
                        },
                        {
                            post: '/ui/{name=projects/*/locations/*/persistentResources/*/operations/*}:wait',
                        },
                        {
                            post: '/ui/{name=projects/*/locations/*/pipelineJobs/*/operations/*}:wait',
                        },
                        {
                            post: '/ui/{name=projects/*/locations/*/schedules/*/operations/*}:wait',
                        },
                        {
                            post: '/ui/{name=projects/*/locations/*/specialistPools/*/operations/*}:wait',
                        },
                        {
                            post: '/ui/{name=projects/*/locations/*/tensorboards/*/operations/*}:wait',
                        },
                        {
                            post: '/ui/{name=projects/*/locations/*/tensorboards/*/experiments/*/operations/*}:wait',
                        },
                        {
                            post: '/ui/{name=projects/*/locations/*/tensorboards/*/experiments/*/runs/*/operations/*}:wait',
                        },
                        {
                            post: '/ui/{name=projects/*/locations/*/tensorboards/*/experiments/*/runs/*/timeSeries/*/operations/*}:wait',
                        },
                        {
                            post: '/ui/{name=projects/*/locations/*/featureOnlineStores/*/operations/*}:wait',
                        },
                        {
                            post: '/ui/{name=projects/*/locations/*/featureOnlineStores/*/featureViews/*/operations/*}:wait',
                        },
                        {
                            post: '/ui/{name=projects/*/locations/*/featureGroups/*/operations/*}:wait',
                        },
                        {
                            post: '/ui/{name=projects/*/locations/*/featureGroups/*/features/*/operations/*}:wait',
                        },
                        {
                            post: '/ui/{name=projects/*/locations/*/featureGroups/*/featureMonitors/*/operations/*}:wait',
                        },
                        { post: '/v1/{name=projects/*/locations/*/operations/*}:wait' },
                        {
                            post: '/v1/{name=projects/*/locations/*/datasets/*/operations/*}:wait',
                        },
                        {
                            post: '/v1/{name=projects/*/locations/*/datasets/*/dataItems/*/operations/*}:wait',
                        },
                        {
                            post: '/v1/{name=projects/*/locations/*/datasets/*/savedQueries/*/operations/*}:wait',
                        },
                        {
                            post: '/v1/{name=projects/*/locations/*/datasets/*/annotationSpecs/*/operations/*}:wait',
                        },
                        {
                            post: '/v1/{name=projects/*/locations/*/datasets/*/dataItems/*/annotations/*/operations/*}:wait',
                        },
                        {
                            post: '/v1/{name=projects/*/locations/*/deploymentResourcePools/*/operations/*}:wait',
                        },
                        {
                            post: '/v1/{name=projects/*/locations/*/endpoints/*/operations/*}:wait',
                        },
                        {
                            post: '/v1/{name=projects/*/locations/*/featurestores/*/operations/*}:wait',
                        },
                        {
                            post: '/v1/{name=projects/*/locations/*/featurestores/*/entityTypes/*/operations/*}:wait',
                        },
                        {
                            post: '/v1/{name=projects/*/locations/*/featurestores/*/entityTypes/*/features/*/operations/*}:wait',
                        },
                        {
                            post: '/v1/{name=projects/*/locations/*/customJobs/*/operations/*}:wait',
                        },
                        {
                            post: '/v1/{name=projects/*/locations/*/dataLabelingJobs/*/operations/*}:wait',
                        },
                        {
                            post: '/v1/{name=projects/*/locations/*/hyperparameterTuningJobs/*/operations/*}:wait',
                        },
                        {
                            post: '/v1/{name=projects/*/locations/*/indexes/*/operations/*}:wait',
                        },
                        {
                            post: '/v1/{name=projects/*/locations/*/indexEndpoints/*/operations/*}:wait',
                        },
                        {
                            post: '/v1/{name=projects/*/locations/*/metadataStores/*/operations/*}:wait',
                        },
                        {
                            post: '/v1/{name=projects/*/locations/*/metadataStores/*/artifacts/*/operations/*}:wait',
                        },
                        {
                            post: '/v1/{name=projects/*/locations/*/metadataStores/*/contexts/*/operations/*}:wait',
                        },
                        {
                            post: '/v1/{name=projects/*/locations/*/metadataStores/*/executions/*/operations/*}:wait',
                        },
                        {
                            post: '/v1/{name=projects/*/locations/*/modelDeploymentMonitoringJobs/*/operations/*}:wait',
                        },
                        {
                            post: '/v1/{name=projects/*/locations/*/migratableResources/*/operations/*}:wait',
                        },
                        {
                            post: '/v1/{name=projects/*/locations/*/models/*/operations/*}:wait',
                        },
                        {
                            post: '/v1/{name=projects/*/locations/*/models/*/evaluations/*/operations/*}:wait',
                        },
                        {
                            post: '/v1/{name=projects/*/locations/*/notebookExecutionJobs/*/operations/*}:wait',
                        },
                        {
                            post: '/v1/{name=projects/*/locations/*/notebookRuntimes/*/operations/*}:wait',
                        },
                        {
                            post: '/v1/{name=projects/*/locations/*/notebookRuntimeTemplates/*/operations/*}:wait',
                        },
                        {
                            post: '/v1/{name=projects/*/locations/*/ragCorpora/*/operations/*}:wait',
                        },
                        {
                            post: '/v1/{name=projects/*/locations/*/ragCorpora/*/ragFiles/*/operations/*}:wait',
                        },
                        {
                            post: '/v1/{name=projects/*/locations/*/reasoningEngines/*/operations/*}:wait',
                        },
                        {
                            post: '/v1/{name=projects/*/locations/*/studies/*/operations/*}:wait',
                        },
                        {
                            post: '/v1/{name=projects/*/locations/*/studies/*/trials/*/operations/*}:wait',
                        },
                        {
                            post: '/v1/{name=projects/*/locations/*/trainingPipelines/*/operations/*}:wait',
                        },
                        {
                            post: '/v1/{name=projects/*/locations/*/persistentResources/*/operations/*}:wait',
                        },
                        {
                            post: '/v1/{name=projects/*/locations/*/pipelineJobs/*/operations/*}:wait',
                        },
                        {
                            post: '/v1/{name=projects/*/locations/*/schedules/*/operations/*}:wait',
                        },
                        {
                            post: '/v1/{name=projects/*/locations/*/specialistPools/*/operations/*}:wait',
                        },
                        {
                            post: '/v1/{name=projects/*/locations/*/tensorboards/*/operations/*}:wait',
                        },
                        {
                            post: '/v1/{name=projects/*/locations/*/tensorboards/*/experiments/*/operations/*}:wait',
                        },
                        {
                            post: '/v1/{name=projects/*/locations/*/tensorboards/*/experiments/*/runs/*/operations/*}:wait',
                        },
                        {
                            post: '/v1/{name=projects/*/locations/*/tensorboards/*/experiments/*/runs/*/timeSeries/*/operations/*}:wait',
                        },
                        {
                            post: '/v1/{name=projects/*/locations/*/featureOnlineStores/*/operations/*}:wait',
                        },
                        {
                            post: '/v1/{name=projects/*/locations/*/featureOnlineStores/*/featureViews/*/operations/*}:wait',
                        },
                        {
                            post: '/v1/{name=projects/*/locations/*/featureGroups/*/operations/*}:wait',
                        },
                        {
                            post: '/v1/{name=projects/*/locations/*/featureGroups/*/features/*/operations/*}:wait',
                        },
                    ],
                },
            ];
        }
        this.operationsClient = this._gaxModule
            .lro(lroOptions)
            .operationsClient(opts);
        const createFeatureGroupResponse = protoFilesRoot.lookup('.google.cloud.aiplatform.v1.FeatureGroup');
        const createFeatureGroupMetadata = protoFilesRoot.lookup('.google.cloud.aiplatform.v1.CreateFeatureGroupOperationMetadata');
        const updateFeatureGroupResponse = protoFilesRoot.lookup('.google.cloud.aiplatform.v1.FeatureGroup');
        const updateFeatureGroupMetadata = protoFilesRoot.lookup('.google.cloud.aiplatform.v1.UpdateFeatureGroupOperationMetadata');
        const deleteFeatureGroupResponse = protoFilesRoot.lookup('.google.protobuf.Empty');
        const deleteFeatureGroupMetadata = protoFilesRoot.lookup('.google.cloud.aiplatform.v1.DeleteOperationMetadata');
        const createFeatureResponse = protoFilesRoot.lookup('.google.cloud.aiplatform.v1.Feature');
        const createFeatureMetadata = protoFilesRoot.lookup('.google.cloud.aiplatform.v1.CreateFeatureOperationMetadata');
        const batchCreateFeaturesResponse = protoFilesRoot.lookup('.google.cloud.aiplatform.v1.BatchCreateFeaturesResponse');
        const batchCreateFeaturesMetadata = protoFilesRoot.lookup('.google.cloud.aiplatform.v1.BatchCreateFeaturesOperationMetadata');
        const updateFeatureResponse = protoFilesRoot.lookup('.google.cloud.aiplatform.v1.Feature');
        const updateFeatureMetadata = protoFilesRoot.lookup('.google.cloud.aiplatform.v1.UpdateFeatureOperationMetadata');
        const deleteFeatureResponse = protoFilesRoot.lookup('.google.protobuf.Empty');
        const deleteFeatureMetadata = protoFilesRoot.lookup('.google.cloud.aiplatform.v1.DeleteOperationMetadata');
        this.descriptors.longrunning = {
            createFeatureGroup: new this._gaxModule.LongrunningDescriptor(this.operationsClient, createFeatureGroupResponse.decode.bind(createFeatureGroupResponse), createFeatureGroupMetadata.decode.bind(createFeatureGroupMetadata)),
            updateFeatureGroup: new this._gaxModule.LongrunningDescriptor(this.operationsClient, updateFeatureGroupResponse.decode.bind(updateFeatureGroupResponse), updateFeatureGroupMetadata.decode.bind(updateFeatureGroupMetadata)),
            deleteFeatureGroup: new this._gaxModule.LongrunningDescriptor(this.operationsClient, deleteFeatureGroupResponse.decode.bind(deleteFeatureGroupResponse), deleteFeatureGroupMetadata.decode.bind(deleteFeatureGroupMetadata)),
            createFeature: new this._gaxModule.LongrunningDescriptor(this.operationsClient, createFeatureResponse.decode.bind(createFeatureResponse), createFeatureMetadata.decode.bind(createFeatureMetadata)),
            batchCreateFeatures: new this._gaxModule.LongrunningDescriptor(this.operationsClient, batchCreateFeaturesResponse.decode.bind(batchCreateFeaturesResponse), batchCreateFeaturesMetadata.decode.bind(batchCreateFeaturesMetadata)),
            updateFeature: new this._gaxModule.LongrunningDescriptor(this.operationsClient, updateFeatureResponse.decode.bind(updateFeatureResponse), updateFeatureMetadata.decode.bind(updateFeatureMetadata)),
            deleteFeature: new this._gaxModule.LongrunningDescriptor(this.operationsClient, deleteFeatureResponse.decode.bind(deleteFeatureResponse), deleteFeatureMetadata.decode.bind(deleteFeatureMetadata)),
        };
        // Put together the default options sent with requests.
        this._defaults = this._gaxGrpc.constructSettings('google.cloud.aiplatform.v1.FeatureRegistryService', gapicConfig, opts.clientConfig || {}, { 'x-goog-api-client': clientHeader.join(' ') });
        // Set up a dictionary of "inner API calls"; the core implementation
        // of calling the API is handled in `google-gax`, with this code
        // merely providing the destination and request information.
        this.innerApiCalls = {};
        // Add a warn function to the client constructor so it can be easily tested.
        this.warn = this._gaxModule.warn;
    }
    /**
     * Initialize the client.
     * Performs asynchronous operations (such as authentication) and prepares the client.
     * This function will be called automatically when any class method is called for the
     * first time, but if you need to initialize it before calling an actual method,
     * feel free to call initialize() directly.
     *
     * You can await on this method if you want to make sure the client is initialized.
     *
     * @returns {Promise} A promise that resolves to an authenticated service stub.
     */
    initialize() {
        // If the client stub promise is already initialized, return immediately.
        if (this.featureRegistryServiceStub) {
            return this.featureRegistryServiceStub;
        }
        // Put together the "service stub" for
        // google.cloud.aiplatform.v1.FeatureRegistryService.
        this.featureRegistryServiceStub = this._gaxGrpc.createStub(this._opts.fallback
            ? this._protos.lookupService('google.cloud.aiplatform.v1.FeatureRegistryService')
            : // eslint-disable-next-line @typescript-eslint/no-explicit-any
                this._protos.google.cloud.aiplatform.v1
                    .FeatureRegistryService, this._opts, this._providedCustomServicePath);
        // Iterate over each of the methods that the service provides
        // and create an API call method for each.
        const featureRegistryServiceStubMethods = [
            'createFeatureGroup',
            'getFeatureGroup',
            'listFeatureGroups',
            'updateFeatureGroup',
            'deleteFeatureGroup',
            'createFeature',
            'batchCreateFeatures',
            'getFeature',
            'listFeatures',
            'updateFeature',
            'deleteFeature',
        ];
        for (const methodName of featureRegistryServiceStubMethods) {
            const callPromise = this.featureRegistryServiceStub.then(stub => (...args) => {
                if (this._terminated) {
                    return Promise.reject('The client has already been closed.');
                }
                const func = stub[methodName];
                return func.apply(stub, args);
            }, (err) => () => {
                throw err;
            });
            const descriptor = this.descriptors.page[methodName] ||
                this.descriptors.longrunning[methodName] ||
                undefined;
            const apiCall = this._gaxModule.createApiCall(callPromise, this._defaults[methodName], descriptor, this._opts.fallback);
            this.innerApiCalls[methodName] = apiCall;
        }
        return this.featureRegistryServiceStub;
    }
    /**
     * The DNS address for this API service.
     * @deprecated Use the apiEndpoint method of the client instance.
     * @returns {string} The DNS address for this service.
     */
    static get servicePath() {
        if (typeof process === 'object' &&
            typeof process.emitWarning === 'function') {
            process.emitWarning('Static servicePath is deprecated, please use the instance method instead.', 'DeprecationWarning');
        }
        return 'aiplatform.googleapis.com';
    }
    /**
     * The DNS address for this API service - same as servicePath.
     * @deprecated Use the apiEndpoint method of the client instance.
     * @returns {string} The DNS address for this service.
     */
    static get apiEndpoint() {
        if (typeof process === 'object' &&
            typeof process.emitWarning === 'function') {
            process.emitWarning('Static apiEndpoint is deprecated, please use the instance method instead.', 'DeprecationWarning');
        }
        return 'aiplatform.googleapis.com';
    }
    /**
     * The DNS address for this API service.
     * @returns {string} The DNS address for this service.
     */
    get apiEndpoint() {
        return this._servicePath;
    }
    get universeDomain() {
        return this._universeDomain;
    }
    /**
     * The port for this API service.
     * @returns {number} The default port for this service.
     */
    static get port() {
        return 443;
    }
    /**
     * The scopes needed to make gRPC calls for every method defined
     * in this service.
     * @returns {string[]} List of default scopes.
     */
    static get scopes() {
        return ['https://www.googleapis.com/auth/cloud-platform'];
    }
    /**
     * Return the project ID used by this class.
     * @returns {Promise} A promise that resolves to string containing the project ID.
     */
    getProjectId(callback) {
        if (callback) {
            this.auth.getProjectId(callback);
            return;
        }
        return this.auth.getProjectId();
    }
    getFeatureGroup(request, optionsOrCallback, callback) {
        var _a;
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                name: (_a = request.name) !== null && _a !== void 0 ? _a : '',
            });
        this.initialize();
        return this.innerApiCalls.getFeatureGroup(request, options, callback);
    }
    getFeature(request, optionsOrCallback, callback) {
        var _a;
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                name: (_a = request.name) !== null && _a !== void 0 ? _a : '',
            });
        this.initialize();
        return this.innerApiCalls.getFeature(request, options, callback);
    }
    createFeatureGroup(request, optionsOrCallback, callback) {
        var _a;
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                parent: (_a = request.parent) !== null && _a !== void 0 ? _a : '',
            });
        this.initialize();
        return this.innerApiCalls.createFeatureGroup(request, options, callback);
    }
    /**
     * Check the status of the long running operation returned by `createFeatureGroup()`.
     * @param {String} name
     *   The operation name that will be passed.
     * @returns {Promise} - The promise which resolves to an object.
     *   The decoded operation object has result and metadata field to get information from.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#long-running-operations | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1/feature_registry_service.create_feature_group.js</caption>
     * region_tag:aiplatform_v1_generated_FeatureRegistryService_CreateFeatureGroup_async
     */
    async checkCreateFeatureGroupProgress(name) {
        const request = new this._gaxModule.operationsProtos.google.longrunning.GetOperationRequest({ name });
        const [operation] = await this.operationsClient.getOperation(request);
        const decodeOperation = new this._gaxModule.Operation(operation, this.descriptors.longrunning.createFeatureGroup, this._gaxModule.createDefaultBackoffSettings());
        return decodeOperation;
    }
    updateFeatureGroup(request, optionsOrCallback, callback) {
        var _a;
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                'feature_group.name': (_a = request.featureGroup.name) !== null && _a !== void 0 ? _a : '',
            });
        this.initialize();
        return this.innerApiCalls.updateFeatureGroup(request, options, callback);
    }
    /**
     * Check the status of the long running operation returned by `updateFeatureGroup()`.
     * @param {String} name
     *   The operation name that will be passed.
     * @returns {Promise} - The promise which resolves to an object.
     *   The decoded operation object has result and metadata field to get information from.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#long-running-operations | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1/feature_registry_service.update_feature_group.js</caption>
     * region_tag:aiplatform_v1_generated_FeatureRegistryService_UpdateFeatureGroup_async
     */
    async checkUpdateFeatureGroupProgress(name) {
        const request = new this._gaxModule.operationsProtos.google.longrunning.GetOperationRequest({ name });
        const [operation] = await this.operationsClient.getOperation(request);
        const decodeOperation = new this._gaxModule.Operation(operation, this.descriptors.longrunning.updateFeatureGroup, this._gaxModule.createDefaultBackoffSettings());
        return decodeOperation;
    }
    deleteFeatureGroup(request, optionsOrCallback, callback) {
        var _a;
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                name: (_a = request.name) !== null && _a !== void 0 ? _a : '',
            });
        this.initialize();
        return this.innerApiCalls.deleteFeatureGroup(request, options, callback);
    }
    /**
     * Check the status of the long running operation returned by `deleteFeatureGroup()`.
     * @param {String} name
     *   The operation name that will be passed.
     * @returns {Promise} - The promise which resolves to an object.
     *   The decoded operation object has result and metadata field to get information from.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#long-running-operations | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1/feature_registry_service.delete_feature_group.js</caption>
     * region_tag:aiplatform_v1_generated_FeatureRegistryService_DeleteFeatureGroup_async
     */
    async checkDeleteFeatureGroupProgress(name) {
        const request = new this._gaxModule.operationsProtos.google.longrunning.GetOperationRequest({ name });
        const [operation] = await this.operationsClient.getOperation(request);
        const decodeOperation = new this._gaxModule.Operation(operation, this.descriptors.longrunning.deleteFeatureGroup, this._gaxModule.createDefaultBackoffSettings());
        return decodeOperation;
    }
    createFeature(request, optionsOrCallback, callback) {
        var _a;
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                parent: (_a = request.parent) !== null && _a !== void 0 ? _a : '',
            });
        this.initialize();
        return this.innerApiCalls.createFeature(request, options, callback);
    }
    /**
     * Check the status of the long running operation returned by `createFeature()`.
     * @param {String} name
     *   The operation name that will be passed.
     * @returns {Promise} - The promise which resolves to an object.
     *   The decoded operation object has result and metadata field to get information from.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#long-running-operations | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1/feature_registry_service.create_feature.js</caption>
     * region_tag:aiplatform_v1_generated_FeatureRegistryService_CreateFeature_async
     */
    async checkCreateFeatureProgress(name) {
        const request = new this._gaxModule.operationsProtos.google.longrunning.GetOperationRequest({ name });
        const [operation] = await this.operationsClient.getOperation(request);
        const decodeOperation = new this._gaxModule.Operation(operation, this.descriptors.longrunning.createFeature, this._gaxModule.createDefaultBackoffSettings());
        return decodeOperation;
    }
    batchCreateFeatures(request, optionsOrCallback, callback) {
        var _a;
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                parent: (_a = request.parent) !== null && _a !== void 0 ? _a : '',
            });
        this.initialize();
        return this.innerApiCalls.batchCreateFeatures(request, options, callback);
    }
    /**
     * Check the status of the long running operation returned by `batchCreateFeatures()`.
     * @param {String} name
     *   The operation name that will be passed.
     * @returns {Promise} - The promise which resolves to an object.
     *   The decoded operation object has result and metadata field to get information from.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#long-running-operations | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1/feature_registry_service.batch_create_features.js</caption>
     * region_tag:aiplatform_v1_generated_FeatureRegistryService_BatchCreateFeatures_async
     */
    async checkBatchCreateFeaturesProgress(name) {
        const request = new this._gaxModule.operationsProtos.google.longrunning.GetOperationRequest({ name });
        const [operation] = await this.operationsClient.getOperation(request);
        const decodeOperation = new this._gaxModule.Operation(operation, this.descriptors.longrunning.batchCreateFeatures, this._gaxModule.createDefaultBackoffSettings());
        return decodeOperation;
    }
    updateFeature(request, optionsOrCallback, callback) {
        var _a;
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                'feature.name': (_a = request.feature.name) !== null && _a !== void 0 ? _a : '',
            });
        this.initialize();
        return this.innerApiCalls.updateFeature(request, options, callback);
    }
    /**
     * Check the status of the long running operation returned by `updateFeature()`.
     * @param {String} name
     *   The operation name that will be passed.
     * @returns {Promise} - The promise which resolves to an object.
     *   The decoded operation object has result and metadata field to get information from.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#long-running-operations | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1/feature_registry_service.update_feature.js</caption>
     * region_tag:aiplatform_v1_generated_FeatureRegistryService_UpdateFeature_async
     */
    async checkUpdateFeatureProgress(name) {
        const request = new this._gaxModule.operationsProtos.google.longrunning.GetOperationRequest({ name });
        const [operation] = await this.operationsClient.getOperation(request);
        const decodeOperation = new this._gaxModule.Operation(operation, this.descriptors.longrunning.updateFeature, this._gaxModule.createDefaultBackoffSettings());
        return decodeOperation;
    }
    deleteFeature(request, optionsOrCallback, callback) {
        var _a;
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                name: (_a = request.name) !== null && _a !== void 0 ? _a : '',
            });
        this.initialize();
        return this.innerApiCalls.deleteFeature(request, options, callback);
    }
    /**
     * Check the status of the long running operation returned by `deleteFeature()`.
     * @param {String} name
     *   The operation name that will be passed.
     * @returns {Promise} - The promise which resolves to an object.
     *   The decoded operation object has result and metadata field to get information from.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#long-running-operations | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1/feature_registry_service.delete_feature.js</caption>
     * region_tag:aiplatform_v1_generated_FeatureRegistryService_DeleteFeature_async
     */
    async checkDeleteFeatureProgress(name) {
        const request = new this._gaxModule.operationsProtos.google.longrunning.GetOperationRequest({ name });
        const [operation] = await this.operationsClient.getOperation(request);
        const decodeOperation = new this._gaxModule.Operation(operation, this.descriptors.longrunning.deleteFeature, this._gaxModule.createDefaultBackoffSettings());
        return decodeOperation;
    }
    listFeatureGroups(request, optionsOrCallback, callback) {
        var _a;
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                parent: (_a = request.parent) !== null && _a !== void 0 ? _a : '',
            });
        this.initialize();
        return this.innerApiCalls.listFeatureGroups(request, options, callback);
    }
    /**
     * Equivalent to `listFeatureGroups`, but returns a NodeJS Stream object.
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. The resource name of the Location to list FeatureGroups.
     *   Format:
     *   `projects/{project}/locations/{location}`
     * @param {string} request.filter
     *   Lists the FeatureGroups that match the filter expression. The
     *   following fields are supported:
     *
     *   * `create_time`: Supports `=`, `!=`, `<`, `>`, `<=`, and `>=` comparisons.
     *   Values must be
     *     in RFC 3339 format.
     *   * `update_time`: Supports `=`, `!=`, `<`, `>`, `<=`, and `>=` comparisons.
     *   Values must be
     *     in RFC 3339 format.
     *   * `labels`: Supports key-value equality and key presence.
     *
     *   Examples:
     *
     *   * `create_time > "2020-01-01" OR update_time > "2020-01-01"`
     *      FeatureGroups created or updated after 2020-01-01.
     *   * `labels.env = "prod"`
     *      FeatureGroups with label "env" set to "prod".
     * @param {number} request.pageSize
     *   The maximum number of FeatureGroups to return. The service may return
     *   fewer than this value. If unspecified, at most 100 FeatureGroups will
     *   be returned. The maximum value is 100; any value greater than 100 will be
     *   coerced to 100.
     * @param {string} request.pageToken
     *   A page token, received from a previous
     *   {@link protos.google.cloud.aiplatform.v1.FeatureRegistryService.ListFeatureGroups|FeatureRegistryService.ListFeatureGroups}
     *   call. Provide this to retrieve the subsequent page.
     *
     *   When paginating, all other parameters provided to
     *   {@link protos.google.cloud.aiplatform.v1.FeatureRegistryService.ListFeatureGroups|FeatureRegistryService.ListFeatureGroups}
     *   must match the call that provided the page token.
     * @param {string} request.orderBy
     *   A comma-separated list of fields to order by, sorted in ascending order.
     *   Use "desc" after a field name for descending.
     *   Supported Fields:
     *
     *     * `create_time`
     *     * `update_time`
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Stream}
     *   An object stream which emits an object representing {@link protos.google.cloud.aiplatform.v1.FeatureGroup|FeatureGroup} on 'data' event.
     *   The client library will perform auto-pagination by default: it will call the API as many
     *   times as needed. Note that it can affect your quota.
     *   We recommend using `listFeatureGroupsAsync()`
     *   method described below for async iteration which you can stop as needed.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     */
    listFeatureGroupsStream(request, options) {
        var _a;
        request = request || {};
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                parent: (_a = request.parent) !== null && _a !== void 0 ? _a : '',
            });
        const defaultCallSettings = this._defaults['listFeatureGroups'];
        const callSettings = defaultCallSettings.merge(options);
        this.initialize();
        return this.descriptors.page.listFeatureGroups.createStream(this.innerApiCalls.listFeatureGroups, request, callSettings);
    }
    /**
     * Equivalent to `listFeatureGroups`, but returns an iterable object.
     *
     * `for`-`await`-`of` syntax is used with the iterable to get response elements on-demand.
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. The resource name of the Location to list FeatureGroups.
     *   Format:
     *   `projects/{project}/locations/{location}`
     * @param {string} request.filter
     *   Lists the FeatureGroups that match the filter expression. The
     *   following fields are supported:
     *
     *   * `create_time`: Supports `=`, `!=`, `<`, `>`, `<=`, and `>=` comparisons.
     *   Values must be
     *     in RFC 3339 format.
     *   * `update_time`: Supports `=`, `!=`, `<`, `>`, `<=`, and `>=` comparisons.
     *   Values must be
     *     in RFC 3339 format.
     *   * `labels`: Supports key-value equality and key presence.
     *
     *   Examples:
     *
     *   * `create_time > "2020-01-01" OR update_time > "2020-01-01"`
     *      FeatureGroups created or updated after 2020-01-01.
     *   * `labels.env = "prod"`
     *      FeatureGroups with label "env" set to "prod".
     * @param {number} request.pageSize
     *   The maximum number of FeatureGroups to return. The service may return
     *   fewer than this value. If unspecified, at most 100 FeatureGroups will
     *   be returned. The maximum value is 100; any value greater than 100 will be
     *   coerced to 100.
     * @param {string} request.pageToken
     *   A page token, received from a previous
     *   {@link protos.google.cloud.aiplatform.v1.FeatureRegistryService.ListFeatureGroups|FeatureRegistryService.ListFeatureGroups}
     *   call. Provide this to retrieve the subsequent page.
     *
     *   When paginating, all other parameters provided to
     *   {@link protos.google.cloud.aiplatform.v1.FeatureRegistryService.ListFeatureGroups|FeatureRegistryService.ListFeatureGroups}
     *   must match the call that provided the page token.
     * @param {string} request.orderBy
     *   A comma-separated list of fields to order by, sorted in ascending order.
     *   Use "desc" after a field name for descending.
     *   Supported Fields:
     *
     *     * `create_time`
     *     * `update_time`
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Object}
     *   An iterable Object that allows {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Iteration_protocols | async iteration }.
     *   When you iterate the returned iterable, each element will be an object representing
     *   {@link protos.google.cloud.aiplatform.v1.FeatureGroup|FeatureGroup}. The API will be called under the hood as needed, once per the page,
     *   so you can stop the iteration when you don't need more results.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1/feature_registry_service.list_feature_groups.js</caption>
     * region_tag:aiplatform_v1_generated_FeatureRegistryService_ListFeatureGroups_async
     */
    listFeatureGroupsAsync(request, options) {
        var _a;
        request = request || {};
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                parent: (_a = request.parent) !== null && _a !== void 0 ? _a : '',
            });
        const defaultCallSettings = this._defaults['listFeatureGroups'];
        const callSettings = defaultCallSettings.merge(options);
        this.initialize();
        return this.descriptors.page.listFeatureGroups.asyncIterate(this.innerApiCalls['listFeatureGroups'], request, callSettings);
    }
    listFeatures(request, optionsOrCallback, callback) {
        var _a;
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                parent: (_a = request.parent) !== null && _a !== void 0 ? _a : '',
            });
        this.initialize();
        return this.innerApiCalls.listFeatures(request, options, callback);
    }
    /**
     * Equivalent to `listFeatures`, but returns a NodeJS Stream object.
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. The resource name of the Location to list Features.
     *   Format for entity_type as parent:
     *   `projects/{project}/locations/{location}/featurestores/{featurestore}/entityTypes/{entity_type}`
     *   Format for feature_group as parent:
     *   `projects/{project}/locations/{location}/featureGroups/{feature_group}`
     * @param {string} request.filter
     *   Lists the Features that match the filter expression. The following
     *   filters are supported:
     *
     *   * `value_type`: Supports = and != comparisons.
     *   * `create_time`: Supports =, !=, <, >, >=, and <= comparisons. Values must
     *   be in RFC 3339 format.
     *   * `update_time`: Supports =, !=, <, >, >=, and <= comparisons. Values must
     *   be in RFC 3339 format.
     *   * `labels`: Supports key-value equality as well as key presence.
     *
     *   Examples:
     *
     *   * `value_type = DOUBLE` --> Features whose type is DOUBLE.
     *   * `create_time > \"2020-01-31T15:30:00.000000Z\" OR
     *        update_time > \"2020-01-31T15:30:00.000000Z\"` --> EntityTypes created
     *        or updated after 2020-01-31T15:30:00.000000Z.
     *   * `labels.active = yes AND labels.env = prod` --> Features having both
     *       (active: yes) and (env: prod) labels.
     *   * `labels.env: *` --> Any Feature which has a label with 'env' as the
     *     key.
     * @param {number} request.pageSize
     *   The maximum number of Features to return. The service may return fewer
     *   than this value. If unspecified, at most 1000 Features will be returned.
     *   The maximum value is 1000; any value greater than 1000 will be coerced to
     *   1000.
     * @param {string} request.pageToken
     *   A page token, received from a previous
     *   {@link protos.google.cloud.aiplatform.v1.FeaturestoreService.ListFeatures|FeaturestoreService.ListFeatures}
     *   call or
     *   {@link protos.google.cloud.aiplatform.v1.FeatureRegistryService.ListFeatures|FeatureRegistryService.ListFeatures}
     *   call. Provide this to retrieve the subsequent page.
     *
     *   When paginating, all other parameters provided to
     *   {@link protos.google.cloud.aiplatform.v1.FeaturestoreService.ListFeatures|FeaturestoreService.ListFeatures}
     *   or
     *   {@link protos.google.cloud.aiplatform.v1.FeatureRegistryService.ListFeatures|FeatureRegistryService.ListFeatures}
     *   must match the call that provided the page token.
     * @param {string} request.orderBy
     *   A comma-separated list of fields to order by, sorted in ascending order.
     *   Use "desc" after a field name for descending.
     *   Supported fields:
     *
     *     * `feature_id`
     *     * `value_type` (Not supported for FeatureRegistry Feature)
     *     * `create_time`
     *     * `update_time`
     * @param {google.protobuf.FieldMask} request.readMask
     *   Mask specifying which fields to read.
     * @param {number} request.latestStatsCount
     *   Only applicable for Vertex AI Feature Store (Legacy).
     *   If set, return the most recent
     *   {@link protos.google.cloud.aiplatform.v1.ListFeaturesRequest.latest_stats_count|ListFeaturesRequest.latest_stats_count}
     *   of stats for each Feature in response. Valid value is [0, 10]. If number of
     *   stats exists <
     *   {@link protos.google.cloud.aiplatform.v1.ListFeaturesRequest.latest_stats_count|ListFeaturesRequest.latest_stats_count},
     *   return all existing stats.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Stream}
     *   An object stream which emits an object representing {@link protos.google.cloud.aiplatform.v1.Feature|Feature} on 'data' event.
     *   The client library will perform auto-pagination by default: it will call the API as many
     *   times as needed. Note that it can affect your quota.
     *   We recommend using `listFeaturesAsync()`
     *   method described below for async iteration which you can stop as needed.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     */
    listFeaturesStream(request, options) {
        var _a;
        request = request || {};
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                parent: (_a = request.parent) !== null && _a !== void 0 ? _a : '',
            });
        const defaultCallSettings = this._defaults['listFeatures'];
        const callSettings = defaultCallSettings.merge(options);
        this.initialize();
        return this.descriptors.page.listFeatures.createStream(this.innerApiCalls.listFeatures, request, callSettings);
    }
    /**
     * Equivalent to `listFeatures`, but returns an iterable object.
     *
     * `for`-`await`-`of` syntax is used with the iterable to get response elements on-demand.
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. The resource name of the Location to list Features.
     *   Format for entity_type as parent:
     *   `projects/{project}/locations/{location}/featurestores/{featurestore}/entityTypes/{entity_type}`
     *   Format for feature_group as parent:
     *   `projects/{project}/locations/{location}/featureGroups/{feature_group}`
     * @param {string} request.filter
     *   Lists the Features that match the filter expression. The following
     *   filters are supported:
     *
     *   * `value_type`: Supports = and != comparisons.
     *   * `create_time`: Supports =, !=, <, >, >=, and <= comparisons. Values must
     *   be in RFC 3339 format.
     *   * `update_time`: Supports =, !=, <, >, >=, and <= comparisons. Values must
     *   be in RFC 3339 format.
     *   * `labels`: Supports key-value equality as well as key presence.
     *
     *   Examples:
     *
     *   * `value_type = DOUBLE` --> Features whose type is DOUBLE.
     *   * `create_time > \"2020-01-31T15:30:00.000000Z\" OR
     *        update_time > \"2020-01-31T15:30:00.000000Z\"` --> EntityTypes created
     *        or updated after 2020-01-31T15:30:00.000000Z.
     *   * `labels.active = yes AND labels.env = prod` --> Features having both
     *       (active: yes) and (env: prod) labels.
     *   * `labels.env: *` --> Any Feature which has a label with 'env' as the
     *     key.
     * @param {number} request.pageSize
     *   The maximum number of Features to return. The service may return fewer
     *   than this value. If unspecified, at most 1000 Features will be returned.
     *   The maximum value is 1000; any value greater than 1000 will be coerced to
     *   1000.
     * @param {string} request.pageToken
     *   A page token, received from a previous
     *   {@link protos.google.cloud.aiplatform.v1.FeaturestoreService.ListFeatures|FeaturestoreService.ListFeatures}
     *   call or
     *   {@link protos.google.cloud.aiplatform.v1.FeatureRegistryService.ListFeatures|FeatureRegistryService.ListFeatures}
     *   call. Provide this to retrieve the subsequent page.
     *
     *   When paginating, all other parameters provided to
     *   {@link protos.google.cloud.aiplatform.v1.FeaturestoreService.ListFeatures|FeaturestoreService.ListFeatures}
     *   or
     *   {@link protos.google.cloud.aiplatform.v1.FeatureRegistryService.ListFeatures|FeatureRegistryService.ListFeatures}
     *   must match the call that provided the page token.
     * @param {string} request.orderBy
     *   A comma-separated list of fields to order by, sorted in ascending order.
     *   Use "desc" after a field name for descending.
     *   Supported fields:
     *
     *     * `feature_id`
     *     * `value_type` (Not supported for FeatureRegistry Feature)
     *     * `create_time`
     *     * `update_time`
     * @param {google.protobuf.FieldMask} request.readMask
     *   Mask specifying which fields to read.
     * @param {number} request.latestStatsCount
     *   Only applicable for Vertex AI Feature Store (Legacy).
     *   If set, return the most recent
     *   {@link protos.google.cloud.aiplatform.v1.ListFeaturesRequest.latest_stats_count|ListFeaturesRequest.latest_stats_count}
     *   of stats for each Feature in response. Valid value is [0, 10]. If number of
     *   stats exists <
     *   {@link protos.google.cloud.aiplatform.v1.ListFeaturesRequest.latest_stats_count|ListFeaturesRequest.latest_stats_count},
     *   return all existing stats.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Object}
     *   An iterable Object that allows {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Iteration_protocols | async iteration }.
     *   When you iterate the returned iterable, each element will be an object representing
     *   {@link protos.google.cloud.aiplatform.v1.Feature|Feature}. The API will be called under the hood as needed, once per the page,
     *   so you can stop the iteration when you don't need more results.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1/feature_registry_service.list_features.js</caption>
     * region_tag:aiplatform_v1_generated_FeatureRegistryService_ListFeatures_async
     */
    listFeaturesAsync(request, options) {
        var _a;
        request = request || {};
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                parent: (_a = request.parent) !== null && _a !== void 0 ? _a : '',
            });
        const defaultCallSettings = this._defaults['listFeatures'];
        const callSettings = defaultCallSettings.merge(options);
        this.initialize();
        return this.descriptors.page.listFeatures.asyncIterate(this.innerApiCalls['listFeatures'], request, callSettings);
    }
    /**
     * Gets the access control policy for a resource. Returns an empty policy
     * if the resource exists and does not have a policy set.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.resource
     *   REQUIRED: The resource for which the policy is being requested.
     *   See the operation documentation for the appropriate value for this field.
     * @param {Object} [request.options]
     *   OPTIONAL: A `GetPolicyOptions` object for specifying options to
     *   `GetIamPolicy`. This field is only used by Cloud IAM.
     *
     *   This object should have the same structure as {@link google.iam.v1.GetPolicyOptions | GetPolicyOptions}.
     * @param {Object} [options]
     *   Optional parameters. You can override the default settings for this call, e.g, timeout,
     *   retries, paginations, etc. See {@link https://googleapis.github.io/gax-nodejs/interfaces/CallOptions.html | gax.CallOptions} for the details.
     * @param {function(?Error, ?Object)} [callback]
     *   The function which will be called with the result of the API call.
     *
     *   The second parameter to the callback is an object representing {@link google.iam.v1.Policy | Policy}.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing {@link google.iam.v1.Policy | Policy}.
     *   The promise has a method named "cancel" which cancels the ongoing API call.
     */
    getIamPolicy(request, options, callback) {
        return this.iamClient.getIamPolicy(request, options, callback);
    }
    /**
     * Returns permissions that a caller has on the specified resource. If the
     * resource does not exist, this will return an empty set of
     * permissions, not a NOT_FOUND error.
     *
     * Note: This operation is designed to be used for building
     * permission-aware UIs and command-line tools, not for authorization
     * checking. This operation may "fail open" without warning.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.resource
     *   REQUIRED: The resource for which the policy detail is being requested.
     *   See the operation documentation for the appropriate value for this field.
     * @param {string[]} request.permissions
     *   The set of permissions to check for the `resource`. Permissions with
     *   wildcards (such as '*' or 'storage.*') are not allowed. For more
     *   information see {@link https://cloud.google.com/iam/docs/overview#permissions | IAM Overview }.
     * @param {Object} [options]
     *   Optional parameters. You can override the default settings for this call, e.g, timeout,
     *   retries, paginations, etc. See {@link https://googleapis.github.io/gax-nodejs/interfaces/CallOptions.html | gax.CallOptions} for the details.
     * @param {function(?Error, ?Object)} [callback]
     *   The function which will be called with the result of the API call.
     *
     *   The second parameter to the callback is an object representing {@link google.iam.v1.TestIamPermissionsResponse | TestIamPermissionsResponse}.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing {@link google.iam.v1.TestIamPermissionsResponse | TestIamPermissionsResponse}.
     *   The promise has a method named "cancel" which cancels the ongoing API call.
     */
    setIamPolicy(request, options, callback) {
        return this.iamClient.setIamPolicy(request, options, callback);
    }
    /**
     * Returns permissions that a caller has on the specified resource. If the
     * resource does not exist, this will return an empty set of
     * permissions, not a NOT_FOUND error.
     *
     * Note: This operation is designed to be used for building
     * permission-aware UIs and command-line tools, not for authorization
     * checking. This operation may "fail open" without warning.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.resource
     *   REQUIRED: The resource for which the policy detail is being requested.
     *   See the operation documentation for the appropriate value for this field.
     * @param {string[]} request.permissions
     *   The set of permissions to check for the `resource`. Permissions with
     *   wildcards (such as '*' or 'storage.*') are not allowed. For more
     *   information see {@link https://cloud.google.com/iam/docs/overview#permissions | IAM Overview }.
     * @param {Object} [options]
     *   Optional parameters. You can override the default settings for this call, e.g, timeout,
     *   retries, paginations, etc. See {@link https://googleapis.github.io/gax-nodejs/interfaces/CallOptions.html | gax.CallOptions} for the details.
     * @param {function(?Error, ?Object)} [callback]
     *   The function which will be called with the result of the API call.
     *
     *   The second parameter to the callback is an object representing {@link google.iam.v1.TestIamPermissionsResponse | TestIamPermissionsResponse}.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing {@link google.iam.v1.TestIamPermissionsResponse | TestIamPermissionsResponse}.
     *   The promise has a method named "cancel" which cancels the ongoing API call.
     *
     */
    testIamPermissions(request, options, callback) {
        return this.iamClient.testIamPermissions(request, options, callback);
    }
    /**
     * Gets information about a location.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.name
     *   Resource name for the location.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html | CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing {@link google.cloud.location.Location | Location}.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#regular-methods | documentation }
     *   for more details and examples.
     * @example
     * ```
     * const [response] = await client.getLocation(request);
     * ```
     */
    getLocation(request, options, callback) {
        return this.locationsClient.getLocation(request, options, callback);
    }
    /**
     * Lists information about the supported locations for this service. Returns an iterable object.
     *
     * `for`-`await`-`of` syntax is used with the iterable to get response elements on-demand.
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.name
     *   The resource that owns the locations collection, if applicable.
     * @param {string} request.filter
     *   The standard list filter.
     * @param {number} request.pageSize
     *   The standard list page size.
     * @param {string} request.pageToken
     *   The standard list page token.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Object}
     *   An iterable Object that allows {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Iteration_protocols | async iteration }.
     *   When you iterate the returned iterable, each element will be an object representing
     *   {@link google.cloud.location.Location | Location}. The API will be called under the hood as needed, once per the page,
     *   so you can stop the iteration when you don't need more results.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     * @example
     * ```
     * const iterable = client.listLocationsAsync(request);
     * for await (const response of iterable) {
     *   // process response
     * }
     * ```
     */
    listLocationsAsync(request, options) {
        return this.locationsClient.listLocationsAsync(request, options);
    }
    /**
     * Gets the latest state of a long-running operation.  Clients can use this
     * method to poll the operation result at intervals as recommended by the API
     * service.
     *
     * @param {Object} request - The request object that will be sent.
     * @param {string} request.name - The name of the operation resource.
     * @param {Object=} options
     *   Optional parameters. You can override the default settings for this call,
     *   e.g, timeout, retries, paginations, etc. See {@link
     *   https://googleapis.github.io/gax-nodejs/global.html#CallOptions | gax.CallOptions}
     *   for the details.
     * @param {function(?Error, ?Object)=} callback
     *   The function which will be called with the result of the API call.
     *
     *   The second parameter to the callback is an object representing
     *   {@link google.longrunning.Operation | google.longrunning.Operation}.
     * @return {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing
     * {@link google.longrunning.Operation | google.longrunning.Operation}.
     * The promise has a method named "cancel" which cancels the ongoing API call.
     *
     * @example
     * ```
     * const client = longrunning.operationsClient();
     * const name = '';
     * const [response] = await client.getOperation({name});
     * // doThingsWith(response)
     * ```
     */
    getOperation(request, optionsOrCallback, callback) {
        var _a;
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                name: (_a = request.name) !== null && _a !== void 0 ? _a : '',
            });
        return this.operationsClient.getOperation(request, options, callback);
    }
    /**
     * Lists operations that match the specified filter in the request. If the
     * server doesn't support this method, it returns `UNIMPLEMENTED`. Returns an iterable object.
     *
     * For-await-of syntax is used with the iterable to recursively get response element on-demand.
     *
     * @param {Object} request - The request object that will be sent.
     * @param {string} request.name - The name of the operation collection.
     * @param {string} request.filter - The standard list filter.
     * @param {number=} request.pageSize -
     *   The maximum number of resources contained in the underlying API
     *   response. If page streaming is performed per-resource, this
     *   parameter does not affect the return value. If page streaming is
     *   performed per-page, this determines the maximum number of
     *   resources in a page.
     * @param {Object=} options
     *   Optional parameters. You can override the default settings for this call,
     *   e.g, timeout, retries, paginations, etc. See {@link
     *   https://googleapis.github.io/gax-nodejs/global.html#CallOptions | gax.CallOptions} for the
     *   details.
     * @returns {Object}
     *   An iterable Object that conforms to {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Iteration_protocols | iteration protocols}.
     *
     * @example
     * ```
     * const client = longrunning.operationsClient();
     * for await (const response of client.listOperationsAsync(request));
     * // doThingsWith(response)
     * ```
     */
    listOperationsAsync(request, options) {
        var _a;
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                name: (_a = request.name) !== null && _a !== void 0 ? _a : '',
            });
        return this.operationsClient.listOperationsAsync(request, options);
    }
    /**
     * Starts asynchronous cancellation on a long-running operation.  The server
     * makes a best effort to cancel the operation, but success is not
     * guaranteed.  If the server doesn't support this method, it returns
     * `google.rpc.Code.UNIMPLEMENTED`.  Clients can use
     * {@link Operations.GetOperation} or
     * other methods to check whether the cancellation succeeded or whether the
     * operation completed despite cancellation. On successful cancellation,
     * the operation is not deleted; instead, it becomes an operation with
     * an {@link Operation.error} value with a {@link google.rpc.Status.code} of
     * 1, corresponding to `Code.CANCELLED`.
     *
     * @param {Object} request - The request object that will be sent.
     * @param {string} request.name - The name of the operation resource to be cancelled.
     * @param {Object=} options
     *   Optional parameters. You can override the default settings for this call,
     * e.g, timeout, retries, paginations, etc. See {@link
     * https://googleapis.github.io/gax-nodejs/global.html#CallOptions | gax.CallOptions} for the
     * details.
     * @param {function(?Error)=} callback
     *   The function which will be called with the result of the API call.
     * @return {Promise} - The promise which resolves when API call finishes.
     *   The promise has a method named "cancel" which cancels the ongoing API
     * call.
     *
     * @example
     * ```
     * const client = longrunning.operationsClient();
     * await client.cancelOperation({name: ''});
     * ```
     */
    cancelOperation(request, optionsOrCallback, callback) {
        var _a;
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                name: (_a = request.name) !== null && _a !== void 0 ? _a : '',
            });
        return this.operationsClient.cancelOperation(request, options, callback);
    }
    /**
     * Deletes a long-running operation. This method indicates that the client is
     * no longer interested in the operation result. It does not cancel the
     * operation. If the server doesn't support this method, it returns
     * `google.rpc.Code.UNIMPLEMENTED`.
     *
     * @param {Object} request - The request object that will be sent.
     * @param {string} request.name - The name of the operation resource to be deleted.
     * @param {Object=} options
     *   Optional parameters. You can override the default settings for this call,
     * e.g, timeout, retries, paginations, etc. See {@link
     * https://googleapis.github.io/gax-nodejs/global.html#CallOptions | gax.CallOptions}
     * for the details.
     * @param {function(?Error)=} callback
     *   The function which will be called with the result of the API call.
     * @return {Promise} - The promise which resolves when API call finishes.
     *   The promise has a method named "cancel" which cancels the ongoing API
     * call.
     *
     * @example
     * ```
     * const client = longrunning.operationsClient();
     * await client.deleteOperation({name: ''});
     * ```
     */
    deleteOperation(request, optionsOrCallback, callback) {
        var _a;
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                name: (_a = request.name) !== null && _a !== void 0 ? _a : '',
            });
        return this.operationsClient.deleteOperation(request, options, callback);
    }
    // --------------------
    // -- Path templates --
    // --------------------
    /**
     * Return a fully-qualified annotation resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} dataset
     * @param {string} data_item
     * @param {string} annotation
     * @returns {string} Resource name string.
     */
    annotationPath(project, location, dataset, dataItem, annotation) {
        return this.pathTemplates.annotationPathTemplate.render({
            project: project,
            location: location,
            dataset: dataset,
            data_item: dataItem,
            annotation: annotation,
        });
    }
    /**
     * Parse the project from Annotation resource.
     *
     * @param {string} annotationName
     *   A fully-qualified path representing Annotation resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromAnnotationName(annotationName) {
        return this.pathTemplates.annotationPathTemplate.match(annotationName)
            .project;
    }
    /**
     * Parse the location from Annotation resource.
     *
     * @param {string} annotationName
     *   A fully-qualified path representing Annotation resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromAnnotationName(annotationName) {
        return this.pathTemplates.annotationPathTemplate.match(annotationName)
            .location;
    }
    /**
     * Parse the dataset from Annotation resource.
     *
     * @param {string} annotationName
     *   A fully-qualified path representing Annotation resource.
     * @returns {string} A string representing the dataset.
     */
    matchDatasetFromAnnotationName(annotationName) {
        return this.pathTemplates.annotationPathTemplate.match(annotationName)
            .dataset;
    }
    /**
     * Parse the data_item from Annotation resource.
     *
     * @param {string} annotationName
     *   A fully-qualified path representing Annotation resource.
     * @returns {string} A string representing the data_item.
     */
    matchDataItemFromAnnotationName(annotationName) {
        return this.pathTemplates.annotationPathTemplate.match(annotationName)
            .data_item;
    }
    /**
     * Parse the annotation from Annotation resource.
     *
     * @param {string} annotationName
     *   A fully-qualified path representing Annotation resource.
     * @returns {string} A string representing the annotation.
     */
    matchAnnotationFromAnnotationName(annotationName) {
        return this.pathTemplates.annotationPathTemplate.match(annotationName)
            .annotation;
    }
    /**
     * Return a fully-qualified annotationSpec resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} dataset
     * @param {string} annotation_spec
     * @returns {string} Resource name string.
     */
    annotationSpecPath(project, location, dataset, annotationSpec) {
        return this.pathTemplates.annotationSpecPathTemplate.render({
            project: project,
            location: location,
            dataset: dataset,
            annotation_spec: annotationSpec,
        });
    }
    /**
     * Parse the project from AnnotationSpec resource.
     *
     * @param {string} annotationSpecName
     *   A fully-qualified path representing AnnotationSpec resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromAnnotationSpecName(annotationSpecName) {
        return this.pathTemplates.annotationSpecPathTemplate.match(annotationSpecName).project;
    }
    /**
     * Parse the location from AnnotationSpec resource.
     *
     * @param {string} annotationSpecName
     *   A fully-qualified path representing AnnotationSpec resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromAnnotationSpecName(annotationSpecName) {
        return this.pathTemplates.annotationSpecPathTemplate.match(annotationSpecName).location;
    }
    /**
     * Parse the dataset from AnnotationSpec resource.
     *
     * @param {string} annotationSpecName
     *   A fully-qualified path representing AnnotationSpec resource.
     * @returns {string} A string representing the dataset.
     */
    matchDatasetFromAnnotationSpecName(annotationSpecName) {
        return this.pathTemplates.annotationSpecPathTemplate.match(annotationSpecName).dataset;
    }
    /**
     * Parse the annotation_spec from AnnotationSpec resource.
     *
     * @param {string} annotationSpecName
     *   A fully-qualified path representing AnnotationSpec resource.
     * @returns {string} A string representing the annotation_spec.
     */
    matchAnnotationSpecFromAnnotationSpecName(annotationSpecName) {
        return this.pathTemplates.annotationSpecPathTemplate.match(annotationSpecName).annotation_spec;
    }
    /**
     * Return a fully-qualified artifact resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} metadata_store
     * @param {string} artifact
     * @returns {string} Resource name string.
     */
    artifactPath(project, location, metadataStore, artifact) {
        return this.pathTemplates.artifactPathTemplate.render({
            project: project,
            location: location,
            metadata_store: metadataStore,
            artifact: artifact,
        });
    }
    /**
     * Parse the project from Artifact resource.
     *
     * @param {string} artifactName
     *   A fully-qualified path representing Artifact resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromArtifactName(artifactName) {
        return this.pathTemplates.artifactPathTemplate.match(artifactName).project;
    }
    /**
     * Parse the location from Artifact resource.
     *
     * @param {string} artifactName
     *   A fully-qualified path representing Artifact resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromArtifactName(artifactName) {
        return this.pathTemplates.artifactPathTemplate.match(artifactName).location;
    }
    /**
     * Parse the metadata_store from Artifact resource.
     *
     * @param {string} artifactName
     *   A fully-qualified path representing Artifact resource.
     * @returns {string} A string representing the metadata_store.
     */
    matchMetadataStoreFromArtifactName(artifactName) {
        return this.pathTemplates.artifactPathTemplate.match(artifactName)
            .metadata_store;
    }
    /**
     * Parse the artifact from Artifact resource.
     *
     * @param {string} artifactName
     *   A fully-qualified path representing Artifact resource.
     * @returns {string} A string representing the artifact.
     */
    matchArtifactFromArtifactName(artifactName) {
        return this.pathTemplates.artifactPathTemplate.match(artifactName).artifact;
    }
    /**
     * Return a fully-qualified batchPredictionJob resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} batch_prediction_job
     * @returns {string} Resource name string.
     */
    batchPredictionJobPath(project, location, batchPredictionJob) {
        return this.pathTemplates.batchPredictionJobPathTemplate.render({
            project: project,
            location: location,
            batch_prediction_job: batchPredictionJob,
        });
    }
    /**
     * Parse the project from BatchPredictionJob resource.
     *
     * @param {string} batchPredictionJobName
     *   A fully-qualified path representing BatchPredictionJob resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromBatchPredictionJobName(batchPredictionJobName) {
        return this.pathTemplates.batchPredictionJobPathTemplate.match(batchPredictionJobName).project;
    }
    /**
     * Parse the location from BatchPredictionJob resource.
     *
     * @param {string} batchPredictionJobName
     *   A fully-qualified path representing BatchPredictionJob resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromBatchPredictionJobName(batchPredictionJobName) {
        return this.pathTemplates.batchPredictionJobPathTemplate.match(batchPredictionJobName).location;
    }
    /**
     * Parse the batch_prediction_job from BatchPredictionJob resource.
     *
     * @param {string} batchPredictionJobName
     *   A fully-qualified path representing BatchPredictionJob resource.
     * @returns {string} A string representing the batch_prediction_job.
     */
    matchBatchPredictionJobFromBatchPredictionJobName(batchPredictionJobName) {
        return this.pathTemplates.batchPredictionJobPathTemplate.match(batchPredictionJobName).batch_prediction_job;
    }
    /**
     * Return a fully-qualified cachedContent resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} cached_content
     * @returns {string} Resource name string.
     */
    cachedContentPath(project, location, cachedContent) {
        return this.pathTemplates.cachedContentPathTemplate.render({
            project: project,
            location: location,
            cached_content: cachedContent,
        });
    }
    /**
     * Parse the project from CachedContent resource.
     *
     * @param {string} cachedContentName
     *   A fully-qualified path representing CachedContent resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromCachedContentName(cachedContentName) {
        return this.pathTemplates.cachedContentPathTemplate.match(cachedContentName)
            .project;
    }
    /**
     * Parse the location from CachedContent resource.
     *
     * @param {string} cachedContentName
     *   A fully-qualified path representing CachedContent resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromCachedContentName(cachedContentName) {
        return this.pathTemplates.cachedContentPathTemplate.match(cachedContentName)
            .location;
    }
    /**
     * Parse the cached_content from CachedContent resource.
     *
     * @param {string} cachedContentName
     *   A fully-qualified path representing CachedContent resource.
     * @returns {string} A string representing the cached_content.
     */
    matchCachedContentFromCachedContentName(cachedContentName) {
        return this.pathTemplates.cachedContentPathTemplate.match(cachedContentName)
            .cached_content;
    }
    /**
     * Return a fully-qualified context resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} metadata_store
     * @param {string} context
     * @returns {string} Resource name string.
     */
    contextPath(project, location, metadataStore, context) {
        return this.pathTemplates.contextPathTemplate.render({
            project: project,
            location: location,
            metadata_store: metadataStore,
            context: context,
        });
    }
    /**
     * Parse the project from Context resource.
     *
     * @param {string} contextName
     *   A fully-qualified path representing Context resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromContextName(contextName) {
        return this.pathTemplates.contextPathTemplate.match(contextName).project;
    }
    /**
     * Parse the location from Context resource.
     *
     * @param {string} contextName
     *   A fully-qualified path representing Context resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromContextName(contextName) {
        return this.pathTemplates.contextPathTemplate.match(contextName).location;
    }
    /**
     * Parse the metadata_store from Context resource.
     *
     * @param {string} contextName
     *   A fully-qualified path representing Context resource.
     * @returns {string} A string representing the metadata_store.
     */
    matchMetadataStoreFromContextName(contextName) {
        return this.pathTemplates.contextPathTemplate.match(contextName)
            .metadata_store;
    }
    /**
     * Parse the context from Context resource.
     *
     * @param {string} contextName
     *   A fully-qualified path representing Context resource.
     * @returns {string} A string representing the context.
     */
    matchContextFromContextName(contextName) {
        return this.pathTemplates.contextPathTemplate.match(contextName).context;
    }
    /**
     * Return a fully-qualified customJob resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} custom_job
     * @returns {string} Resource name string.
     */
    customJobPath(project, location, customJob) {
        return this.pathTemplates.customJobPathTemplate.render({
            project: project,
            location: location,
            custom_job: customJob,
        });
    }
    /**
     * Parse the project from CustomJob resource.
     *
     * @param {string} customJobName
     *   A fully-qualified path representing CustomJob resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromCustomJobName(customJobName) {
        return this.pathTemplates.customJobPathTemplate.match(customJobName)
            .project;
    }
    /**
     * Parse the location from CustomJob resource.
     *
     * @param {string} customJobName
     *   A fully-qualified path representing CustomJob resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromCustomJobName(customJobName) {
        return this.pathTemplates.customJobPathTemplate.match(customJobName)
            .location;
    }
    /**
     * Parse the custom_job from CustomJob resource.
     *
     * @param {string} customJobName
     *   A fully-qualified path representing CustomJob resource.
     * @returns {string} A string representing the custom_job.
     */
    matchCustomJobFromCustomJobName(customJobName) {
        return this.pathTemplates.customJobPathTemplate.match(customJobName)
            .custom_job;
    }
    /**
     * Return a fully-qualified dataItem resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} dataset
     * @param {string} data_item
     * @returns {string} Resource name string.
     */
    dataItemPath(project, location, dataset, dataItem) {
        return this.pathTemplates.dataItemPathTemplate.render({
            project: project,
            location: location,
            dataset: dataset,
            data_item: dataItem,
        });
    }
    /**
     * Parse the project from DataItem resource.
     *
     * @param {string} dataItemName
     *   A fully-qualified path representing DataItem resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromDataItemName(dataItemName) {
        return this.pathTemplates.dataItemPathTemplate.match(dataItemName).project;
    }
    /**
     * Parse the location from DataItem resource.
     *
     * @param {string} dataItemName
     *   A fully-qualified path representing DataItem resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromDataItemName(dataItemName) {
        return this.pathTemplates.dataItemPathTemplate.match(dataItemName).location;
    }
    /**
     * Parse the dataset from DataItem resource.
     *
     * @param {string} dataItemName
     *   A fully-qualified path representing DataItem resource.
     * @returns {string} A string representing the dataset.
     */
    matchDatasetFromDataItemName(dataItemName) {
        return this.pathTemplates.dataItemPathTemplate.match(dataItemName).dataset;
    }
    /**
     * Parse the data_item from DataItem resource.
     *
     * @param {string} dataItemName
     *   A fully-qualified path representing DataItem resource.
     * @returns {string} A string representing the data_item.
     */
    matchDataItemFromDataItemName(dataItemName) {
        return this.pathTemplates.dataItemPathTemplate.match(dataItemName)
            .data_item;
    }
    /**
     * Return a fully-qualified dataLabelingJob resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} data_labeling_job
     * @returns {string} Resource name string.
     */
    dataLabelingJobPath(project, location, dataLabelingJob) {
        return this.pathTemplates.dataLabelingJobPathTemplate.render({
            project: project,
            location: location,
            data_labeling_job: dataLabelingJob,
        });
    }
    /**
     * Parse the project from DataLabelingJob resource.
     *
     * @param {string} dataLabelingJobName
     *   A fully-qualified path representing DataLabelingJob resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromDataLabelingJobName(dataLabelingJobName) {
        return this.pathTemplates.dataLabelingJobPathTemplate.match(dataLabelingJobName).project;
    }
    /**
     * Parse the location from DataLabelingJob resource.
     *
     * @param {string} dataLabelingJobName
     *   A fully-qualified path representing DataLabelingJob resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromDataLabelingJobName(dataLabelingJobName) {
        return this.pathTemplates.dataLabelingJobPathTemplate.match(dataLabelingJobName).location;
    }
    /**
     * Parse the data_labeling_job from DataLabelingJob resource.
     *
     * @param {string} dataLabelingJobName
     *   A fully-qualified path representing DataLabelingJob resource.
     * @returns {string} A string representing the data_labeling_job.
     */
    matchDataLabelingJobFromDataLabelingJobName(dataLabelingJobName) {
        return this.pathTemplates.dataLabelingJobPathTemplate.match(dataLabelingJobName).data_labeling_job;
    }
    /**
     * Return a fully-qualified dataset resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} dataset
     * @returns {string} Resource name string.
     */
    datasetPath(project, location, dataset) {
        return this.pathTemplates.datasetPathTemplate.render({
            project: project,
            location: location,
            dataset: dataset,
        });
    }
    /**
     * Parse the project from Dataset resource.
     *
     * @param {string} datasetName
     *   A fully-qualified path representing Dataset resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromDatasetName(datasetName) {
        return this.pathTemplates.datasetPathTemplate.match(datasetName).project;
    }
    /**
     * Parse the location from Dataset resource.
     *
     * @param {string} datasetName
     *   A fully-qualified path representing Dataset resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromDatasetName(datasetName) {
        return this.pathTemplates.datasetPathTemplate.match(datasetName).location;
    }
    /**
     * Parse the dataset from Dataset resource.
     *
     * @param {string} datasetName
     *   A fully-qualified path representing Dataset resource.
     * @returns {string} A string representing the dataset.
     */
    matchDatasetFromDatasetName(datasetName) {
        return this.pathTemplates.datasetPathTemplate.match(datasetName).dataset;
    }
    /**
     * Return a fully-qualified datasetVersion resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} dataset
     * @param {string} dataset_version
     * @returns {string} Resource name string.
     */
    datasetVersionPath(project, location, dataset, datasetVersion) {
        return this.pathTemplates.datasetVersionPathTemplate.render({
            project: project,
            location: location,
            dataset: dataset,
            dataset_version: datasetVersion,
        });
    }
    /**
     * Parse the project from DatasetVersion resource.
     *
     * @param {string} datasetVersionName
     *   A fully-qualified path representing DatasetVersion resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromDatasetVersionName(datasetVersionName) {
        return this.pathTemplates.datasetVersionPathTemplate.match(datasetVersionName).project;
    }
    /**
     * Parse the location from DatasetVersion resource.
     *
     * @param {string} datasetVersionName
     *   A fully-qualified path representing DatasetVersion resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromDatasetVersionName(datasetVersionName) {
        return this.pathTemplates.datasetVersionPathTemplate.match(datasetVersionName).location;
    }
    /**
     * Parse the dataset from DatasetVersion resource.
     *
     * @param {string} datasetVersionName
     *   A fully-qualified path representing DatasetVersion resource.
     * @returns {string} A string representing the dataset.
     */
    matchDatasetFromDatasetVersionName(datasetVersionName) {
        return this.pathTemplates.datasetVersionPathTemplate.match(datasetVersionName).dataset;
    }
    /**
     * Parse the dataset_version from DatasetVersion resource.
     *
     * @param {string} datasetVersionName
     *   A fully-qualified path representing DatasetVersion resource.
     * @returns {string} A string representing the dataset_version.
     */
    matchDatasetVersionFromDatasetVersionName(datasetVersionName) {
        return this.pathTemplates.datasetVersionPathTemplate.match(datasetVersionName).dataset_version;
    }
    /**
     * Return a fully-qualified deploymentResourcePool resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} deployment_resource_pool
     * @returns {string} Resource name string.
     */
    deploymentResourcePoolPath(project, location, deploymentResourcePool) {
        return this.pathTemplates.deploymentResourcePoolPathTemplate.render({
            project: project,
            location: location,
            deployment_resource_pool: deploymentResourcePool,
        });
    }
    /**
     * Parse the project from DeploymentResourcePool resource.
     *
     * @param {string} deploymentResourcePoolName
     *   A fully-qualified path representing DeploymentResourcePool resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromDeploymentResourcePoolName(deploymentResourcePoolName) {
        return this.pathTemplates.deploymentResourcePoolPathTemplate.match(deploymentResourcePoolName).project;
    }
    /**
     * Parse the location from DeploymentResourcePool resource.
     *
     * @param {string} deploymentResourcePoolName
     *   A fully-qualified path representing DeploymentResourcePool resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromDeploymentResourcePoolName(deploymentResourcePoolName) {
        return this.pathTemplates.deploymentResourcePoolPathTemplate.match(deploymentResourcePoolName).location;
    }
    /**
     * Parse the deployment_resource_pool from DeploymentResourcePool resource.
     *
     * @param {string} deploymentResourcePoolName
     *   A fully-qualified path representing DeploymentResourcePool resource.
     * @returns {string} A string representing the deployment_resource_pool.
     */
    matchDeploymentResourcePoolFromDeploymentResourcePoolName(deploymentResourcePoolName) {
        return this.pathTemplates.deploymentResourcePoolPathTemplate.match(deploymentResourcePoolName).deployment_resource_pool;
    }
    /**
     * Return a fully-qualified entityType resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} featurestore
     * @param {string} entity_type
     * @returns {string} Resource name string.
     */
    entityTypePath(project, location, featurestore, entityType) {
        return this.pathTemplates.entityTypePathTemplate.render({
            project: project,
            location: location,
            featurestore: featurestore,
            entity_type: entityType,
        });
    }
    /**
     * Parse the project from EntityType resource.
     *
     * @param {string} entityTypeName
     *   A fully-qualified path representing EntityType resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromEntityTypeName(entityTypeName) {
        return this.pathTemplates.entityTypePathTemplate.match(entityTypeName)
            .project;
    }
    /**
     * Parse the location from EntityType resource.
     *
     * @param {string} entityTypeName
     *   A fully-qualified path representing EntityType resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromEntityTypeName(entityTypeName) {
        return this.pathTemplates.entityTypePathTemplate.match(entityTypeName)
            .location;
    }
    /**
     * Parse the featurestore from EntityType resource.
     *
     * @param {string} entityTypeName
     *   A fully-qualified path representing EntityType resource.
     * @returns {string} A string representing the featurestore.
     */
    matchFeaturestoreFromEntityTypeName(entityTypeName) {
        return this.pathTemplates.entityTypePathTemplate.match(entityTypeName)
            .featurestore;
    }
    /**
     * Parse the entity_type from EntityType resource.
     *
     * @param {string} entityTypeName
     *   A fully-qualified path representing EntityType resource.
     * @returns {string} A string representing the entity_type.
     */
    matchEntityTypeFromEntityTypeName(entityTypeName) {
        return this.pathTemplates.entityTypePathTemplate.match(entityTypeName)
            .entity_type;
    }
    /**
     * Return a fully-qualified execution resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} metadata_store
     * @param {string} execution
     * @returns {string} Resource name string.
     */
    executionPath(project, location, metadataStore, execution) {
        return this.pathTemplates.executionPathTemplate.render({
            project: project,
            location: location,
            metadata_store: metadataStore,
            execution: execution,
        });
    }
    /**
     * Parse the project from Execution resource.
     *
     * @param {string} executionName
     *   A fully-qualified path representing Execution resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromExecutionName(executionName) {
        return this.pathTemplates.executionPathTemplate.match(executionName)
            .project;
    }
    /**
     * Parse the location from Execution resource.
     *
     * @param {string} executionName
     *   A fully-qualified path representing Execution resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromExecutionName(executionName) {
        return this.pathTemplates.executionPathTemplate.match(executionName)
            .location;
    }
    /**
     * Parse the metadata_store from Execution resource.
     *
     * @param {string} executionName
     *   A fully-qualified path representing Execution resource.
     * @returns {string} A string representing the metadata_store.
     */
    matchMetadataStoreFromExecutionName(executionName) {
        return this.pathTemplates.executionPathTemplate.match(executionName)
            .metadata_store;
    }
    /**
     * Parse the execution from Execution resource.
     *
     * @param {string} executionName
     *   A fully-qualified path representing Execution resource.
     * @returns {string} A string representing the execution.
     */
    matchExecutionFromExecutionName(executionName) {
        return this.pathTemplates.executionPathTemplate.match(executionName)
            .execution;
    }
    /**
     * Return a fully-qualified featureGroup resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} feature_group
     * @returns {string} Resource name string.
     */
    featureGroupPath(project, location, featureGroup) {
        return this.pathTemplates.featureGroupPathTemplate.render({
            project: project,
            location: location,
            feature_group: featureGroup,
        });
    }
    /**
     * Parse the project from FeatureGroup resource.
     *
     * @param {string} featureGroupName
     *   A fully-qualified path representing FeatureGroup resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromFeatureGroupName(featureGroupName) {
        return this.pathTemplates.featureGroupPathTemplate.match(featureGroupName)
            .project;
    }
    /**
     * Parse the location from FeatureGroup resource.
     *
     * @param {string} featureGroupName
     *   A fully-qualified path representing FeatureGroup resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromFeatureGroupName(featureGroupName) {
        return this.pathTemplates.featureGroupPathTemplate.match(featureGroupName)
            .location;
    }
    /**
     * Parse the feature_group from FeatureGroup resource.
     *
     * @param {string} featureGroupName
     *   A fully-qualified path representing FeatureGroup resource.
     * @returns {string} A string representing the feature_group.
     */
    matchFeatureGroupFromFeatureGroupName(featureGroupName) {
        return this.pathTemplates.featureGroupPathTemplate.match(featureGroupName)
            .feature_group;
    }
    /**
     * Return a fully-qualified featureOnlineStore resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} feature_online_store
     * @returns {string} Resource name string.
     */
    featureOnlineStorePath(project, location, featureOnlineStore) {
        return this.pathTemplates.featureOnlineStorePathTemplate.render({
            project: project,
            location: location,
            feature_online_store: featureOnlineStore,
        });
    }
    /**
     * Parse the project from FeatureOnlineStore resource.
     *
     * @param {string} featureOnlineStoreName
     *   A fully-qualified path representing FeatureOnlineStore resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromFeatureOnlineStoreName(featureOnlineStoreName) {
        return this.pathTemplates.featureOnlineStorePathTemplate.match(featureOnlineStoreName).project;
    }
    /**
     * Parse the location from FeatureOnlineStore resource.
     *
     * @param {string} featureOnlineStoreName
     *   A fully-qualified path representing FeatureOnlineStore resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromFeatureOnlineStoreName(featureOnlineStoreName) {
        return this.pathTemplates.featureOnlineStorePathTemplate.match(featureOnlineStoreName).location;
    }
    /**
     * Parse the feature_online_store from FeatureOnlineStore resource.
     *
     * @param {string} featureOnlineStoreName
     *   A fully-qualified path representing FeatureOnlineStore resource.
     * @returns {string} A string representing the feature_online_store.
     */
    matchFeatureOnlineStoreFromFeatureOnlineStoreName(featureOnlineStoreName) {
        return this.pathTemplates.featureOnlineStorePathTemplate.match(featureOnlineStoreName).feature_online_store;
    }
    /**
     * Return a fully-qualified featureView resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} feature_online_store
     * @param {string} feature_view
     * @returns {string} Resource name string.
     */
    featureViewPath(project, location, featureOnlineStore, featureView) {
        return this.pathTemplates.featureViewPathTemplate.render({
            project: project,
            location: location,
            feature_online_store: featureOnlineStore,
            feature_view: featureView,
        });
    }
    /**
     * Parse the project from FeatureView resource.
     *
     * @param {string} featureViewName
     *   A fully-qualified path representing FeatureView resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromFeatureViewName(featureViewName) {
        return this.pathTemplates.featureViewPathTemplate.match(featureViewName)
            .project;
    }
    /**
     * Parse the location from FeatureView resource.
     *
     * @param {string} featureViewName
     *   A fully-qualified path representing FeatureView resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromFeatureViewName(featureViewName) {
        return this.pathTemplates.featureViewPathTemplate.match(featureViewName)
            .location;
    }
    /**
     * Parse the feature_online_store from FeatureView resource.
     *
     * @param {string} featureViewName
     *   A fully-qualified path representing FeatureView resource.
     * @returns {string} A string representing the feature_online_store.
     */
    matchFeatureOnlineStoreFromFeatureViewName(featureViewName) {
        return this.pathTemplates.featureViewPathTemplate.match(featureViewName)
            .feature_online_store;
    }
    /**
     * Parse the feature_view from FeatureView resource.
     *
     * @param {string} featureViewName
     *   A fully-qualified path representing FeatureView resource.
     * @returns {string} A string representing the feature_view.
     */
    matchFeatureViewFromFeatureViewName(featureViewName) {
        return this.pathTemplates.featureViewPathTemplate.match(featureViewName)
            .feature_view;
    }
    /**
     * Return a fully-qualified featureViewSync resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} feature_online_store
     * @param {string} feature_view
     * @returns {string} Resource name string.
     */
    featureViewSyncPath(project, location, featureOnlineStore, featureView) {
        return this.pathTemplates.featureViewSyncPathTemplate.render({
            project: project,
            location: location,
            feature_online_store: featureOnlineStore,
            feature_view: featureView,
        });
    }
    /**
     * Parse the project from FeatureViewSync resource.
     *
     * @param {string} featureViewSyncName
     *   A fully-qualified path representing FeatureViewSync resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromFeatureViewSyncName(featureViewSyncName) {
        return this.pathTemplates.featureViewSyncPathTemplate.match(featureViewSyncName).project;
    }
    /**
     * Parse the location from FeatureViewSync resource.
     *
     * @param {string} featureViewSyncName
     *   A fully-qualified path representing FeatureViewSync resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromFeatureViewSyncName(featureViewSyncName) {
        return this.pathTemplates.featureViewSyncPathTemplate.match(featureViewSyncName).location;
    }
    /**
     * Parse the feature_online_store from FeatureViewSync resource.
     *
     * @param {string} featureViewSyncName
     *   A fully-qualified path representing FeatureViewSync resource.
     * @returns {string} A string representing the feature_online_store.
     */
    matchFeatureOnlineStoreFromFeatureViewSyncName(featureViewSyncName) {
        return this.pathTemplates.featureViewSyncPathTemplate.match(featureViewSyncName).feature_online_store;
    }
    /**
     * Parse the feature_view from FeatureViewSync resource.
     *
     * @param {string} featureViewSyncName
     *   A fully-qualified path representing FeatureViewSync resource.
     * @returns {string} A string representing the feature_view.
     */
    matchFeatureViewFromFeatureViewSyncName(featureViewSyncName) {
        return this.pathTemplates.featureViewSyncPathTemplate.match(featureViewSyncName).feature_view;
    }
    /**
     * Return a fully-qualified featurestore resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} featurestore
     * @returns {string} Resource name string.
     */
    featurestorePath(project, location, featurestore) {
        return this.pathTemplates.featurestorePathTemplate.render({
            project: project,
            location: location,
            featurestore: featurestore,
        });
    }
    /**
     * Parse the project from Featurestore resource.
     *
     * @param {string} featurestoreName
     *   A fully-qualified path representing Featurestore resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromFeaturestoreName(featurestoreName) {
        return this.pathTemplates.featurestorePathTemplate.match(featurestoreName)
            .project;
    }
    /**
     * Parse the location from Featurestore resource.
     *
     * @param {string} featurestoreName
     *   A fully-qualified path representing Featurestore resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromFeaturestoreName(featurestoreName) {
        return this.pathTemplates.featurestorePathTemplate.match(featurestoreName)
            .location;
    }
    /**
     * Parse the featurestore from Featurestore resource.
     *
     * @param {string} featurestoreName
     *   A fully-qualified path representing Featurestore resource.
     * @returns {string} A string representing the featurestore.
     */
    matchFeaturestoreFromFeaturestoreName(featurestoreName) {
        return this.pathTemplates.featurestorePathTemplate.match(featurestoreName)
            .featurestore;
    }
    /**
     * Return a fully-qualified hyperparameterTuningJob resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} hyperparameter_tuning_job
     * @returns {string} Resource name string.
     */
    hyperparameterTuningJobPath(project, location, hyperparameterTuningJob) {
        return this.pathTemplates.hyperparameterTuningJobPathTemplate.render({
            project: project,
            location: location,
            hyperparameter_tuning_job: hyperparameterTuningJob,
        });
    }
    /**
     * Parse the project from HyperparameterTuningJob resource.
     *
     * @param {string} hyperparameterTuningJobName
     *   A fully-qualified path representing HyperparameterTuningJob resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromHyperparameterTuningJobName(hyperparameterTuningJobName) {
        return this.pathTemplates.hyperparameterTuningJobPathTemplate.match(hyperparameterTuningJobName).project;
    }
    /**
     * Parse the location from HyperparameterTuningJob resource.
     *
     * @param {string} hyperparameterTuningJobName
     *   A fully-qualified path representing HyperparameterTuningJob resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromHyperparameterTuningJobName(hyperparameterTuningJobName) {
        return this.pathTemplates.hyperparameterTuningJobPathTemplate.match(hyperparameterTuningJobName).location;
    }
    /**
     * Parse the hyperparameter_tuning_job from HyperparameterTuningJob resource.
     *
     * @param {string} hyperparameterTuningJobName
     *   A fully-qualified path representing HyperparameterTuningJob resource.
     * @returns {string} A string representing the hyperparameter_tuning_job.
     */
    matchHyperparameterTuningJobFromHyperparameterTuningJobName(hyperparameterTuningJobName) {
        return this.pathTemplates.hyperparameterTuningJobPathTemplate.match(hyperparameterTuningJobName).hyperparameter_tuning_job;
    }
    /**
     * Return a fully-qualified index resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} index
     * @returns {string} Resource name string.
     */
    indexPath(project, location, index) {
        return this.pathTemplates.indexPathTemplate.render({
            project: project,
            location: location,
            index: index,
        });
    }
    /**
     * Parse the project from Index resource.
     *
     * @param {string} indexName
     *   A fully-qualified path representing Index resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromIndexName(indexName) {
        return this.pathTemplates.indexPathTemplate.match(indexName).project;
    }
    /**
     * Parse the location from Index resource.
     *
     * @param {string} indexName
     *   A fully-qualified path representing Index resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromIndexName(indexName) {
        return this.pathTemplates.indexPathTemplate.match(indexName).location;
    }
    /**
     * Parse the index from Index resource.
     *
     * @param {string} indexName
     *   A fully-qualified path representing Index resource.
     * @returns {string} A string representing the index.
     */
    matchIndexFromIndexName(indexName) {
        return this.pathTemplates.indexPathTemplate.match(indexName).index;
    }
    /**
     * Return a fully-qualified indexEndpoint resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} index_endpoint
     * @returns {string} Resource name string.
     */
    indexEndpointPath(project, location, indexEndpoint) {
        return this.pathTemplates.indexEndpointPathTemplate.render({
            project: project,
            location: location,
            index_endpoint: indexEndpoint,
        });
    }
    /**
     * Parse the project from IndexEndpoint resource.
     *
     * @param {string} indexEndpointName
     *   A fully-qualified path representing IndexEndpoint resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromIndexEndpointName(indexEndpointName) {
        return this.pathTemplates.indexEndpointPathTemplate.match(indexEndpointName)
            .project;
    }
    /**
     * Parse the location from IndexEndpoint resource.
     *
     * @param {string} indexEndpointName
     *   A fully-qualified path representing IndexEndpoint resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromIndexEndpointName(indexEndpointName) {
        return this.pathTemplates.indexEndpointPathTemplate.match(indexEndpointName)
            .location;
    }
    /**
     * Parse the index_endpoint from IndexEndpoint resource.
     *
     * @param {string} indexEndpointName
     *   A fully-qualified path representing IndexEndpoint resource.
     * @returns {string} A string representing the index_endpoint.
     */
    matchIndexEndpointFromIndexEndpointName(indexEndpointName) {
        return this.pathTemplates.indexEndpointPathTemplate.match(indexEndpointName)
            .index_endpoint;
    }
    /**
     * Return a fully-qualified location resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @returns {string} Resource name string.
     */
    locationPath(project, location) {
        return this.pathTemplates.locationPathTemplate.render({
            project: project,
            location: location,
        });
    }
    /**
     * Parse the project from Location resource.
     *
     * @param {string} locationName
     *   A fully-qualified path representing Location resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromLocationName(locationName) {
        return this.pathTemplates.locationPathTemplate.match(locationName).project;
    }
    /**
     * Parse the location from Location resource.
     *
     * @param {string} locationName
     *   A fully-qualified path representing Location resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromLocationName(locationName) {
        return this.pathTemplates.locationPathTemplate.match(locationName).location;
    }
    /**
     * Return a fully-qualified metadataSchema resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} metadata_store
     * @param {string} metadata_schema
     * @returns {string} Resource name string.
     */
    metadataSchemaPath(project, location, metadataStore, metadataSchema) {
        return this.pathTemplates.metadataSchemaPathTemplate.render({
            project: project,
            location: location,
            metadata_store: metadataStore,
            metadata_schema: metadataSchema,
        });
    }
    /**
     * Parse the project from MetadataSchema resource.
     *
     * @param {string} metadataSchemaName
     *   A fully-qualified path representing MetadataSchema resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromMetadataSchemaName(metadataSchemaName) {
        return this.pathTemplates.metadataSchemaPathTemplate.match(metadataSchemaName).project;
    }
    /**
     * Parse the location from MetadataSchema resource.
     *
     * @param {string} metadataSchemaName
     *   A fully-qualified path representing MetadataSchema resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromMetadataSchemaName(metadataSchemaName) {
        return this.pathTemplates.metadataSchemaPathTemplate.match(metadataSchemaName).location;
    }
    /**
     * Parse the metadata_store from MetadataSchema resource.
     *
     * @param {string} metadataSchemaName
     *   A fully-qualified path representing MetadataSchema resource.
     * @returns {string} A string representing the metadata_store.
     */
    matchMetadataStoreFromMetadataSchemaName(metadataSchemaName) {
        return this.pathTemplates.metadataSchemaPathTemplate.match(metadataSchemaName).metadata_store;
    }
    /**
     * Parse the metadata_schema from MetadataSchema resource.
     *
     * @param {string} metadataSchemaName
     *   A fully-qualified path representing MetadataSchema resource.
     * @returns {string} A string representing the metadata_schema.
     */
    matchMetadataSchemaFromMetadataSchemaName(metadataSchemaName) {
        return this.pathTemplates.metadataSchemaPathTemplate.match(metadataSchemaName).metadata_schema;
    }
    /**
     * Return a fully-qualified metadataStore resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} metadata_store
     * @returns {string} Resource name string.
     */
    metadataStorePath(project, location, metadataStore) {
        return this.pathTemplates.metadataStorePathTemplate.render({
            project: project,
            location: location,
            metadata_store: metadataStore,
        });
    }
    /**
     * Parse the project from MetadataStore resource.
     *
     * @param {string} metadataStoreName
     *   A fully-qualified path representing MetadataStore resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromMetadataStoreName(metadataStoreName) {
        return this.pathTemplates.metadataStorePathTemplate.match(metadataStoreName)
            .project;
    }
    /**
     * Parse the location from MetadataStore resource.
     *
     * @param {string} metadataStoreName
     *   A fully-qualified path representing MetadataStore resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromMetadataStoreName(metadataStoreName) {
        return this.pathTemplates.metadataStorePathTemplate.match(metadataStoreName)
            .location;
    }
    /**
     * Parse the metadata_store from MetadataStore resource.
     *
     * @param {string} metadataStoreName
     *   A fully-qualified path representing MetadataStore resource.
     * @returns {string} A string representing the metadata_store.
     */
    matchMetadataStoreFromMetadataStoreName(metadataStoreName) {
        return this.pathTemplates.metadataStorePathTemplate.match(metadataStoreName)
            .metadata_store;
    }
    /**
     * Return a fully-qualified model resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} model
     * @returns {string} Resource name string.
     */
    modelPath(project, location, model) {
        return this.pathTemplates.modelPathTemplate.render({
            project: project,
            location: location,
            model: model,
        });
    }
    /**
     * Parse the project from Model resource.
     *
     * @param {string} modelName
     *   A fully-qualified path representing Model resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromModelName(modelName) {
        return this.pathTemplates.modelPathTemplate.match(modelName).project;
    }
    /**
     * Parse the location from Model resource.
     *
     * @param {string} modelName
     *   A fully-qualified path representing Model resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromModelName(modelName) {
        return this.pathTemplates.modelPathTemplate.match(modelName).location;
    }
    /**
     * Parse the model from Model resource.
     *
     * @param {string} modelName
     *   A fully-qualified path representing Model resource.
     * @returns {string} A string representing the model.
     */
    matchModelFromModelName(modelName) {
        return this.pathTemplates.modelPathTemplate.match(modelName).model;
    }
    /**
     * Return a fully-qualified modelDeploymentMonitoringJob resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} model_deployment_monitoring_job
     * @returns {string} Resource name string.
     */
    modelDeploymentMonitoringJobPath(project, location, modelDeploymentMonitoringJob) {
        return this.pathTemplates.modelDeploymentMonitoringJobPathTemplate.render({
            project: project,
            location: location,
            model_deployment_monitoring_job: modelDeploymentMonitoringJob,
        });
    }
    /**
     * Parse the project from ModelDeploymentMonitoringJob resource.
     *
     * @param {string} modelDeploymentMonitoringJobName
     *   A fully-qualified path representing ModelDeploymentMonitoringJob resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromModelDeploymentMonitoringJobName(modelDeploymentMonitoringJobName) {
        return this.pathTemplates.modelDeploymentMonitoringJobPathTemplate.match(modelDeploymentMonitoringJobName).project;
    }
    /**
     * Parse the location from ModelDeploymentMonitoringJob resource.
     *
     * @param {string} modelDeploymentMonitoringJobName
     *   A fully-qualified path representing ModelDeploymentMonitoringJob resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromModelDeploymentMonitoringJobName(modelDeploymentMonitoringJobName) {
        return this.pathTemplates.modelDeploymentMonitoringJobPathTemplate.match(modelDeploymentMonitoringJobName).location;
    }
    /**
     * Parse the model_deployment_monitoring_job from ModelDeploymentMonitoringJob resource.
     *
     * @param {string} modelDeploymentMonitoringJobName
     *   A fully-qualified path representing ModelDeploymentMonitoringJob resource.
     * @returns {string} A string representing the model_deployment_monitoring_job.
     */
    matchModelDeploymentMonitoringJobFromModelDeploymentMonitoringJobName(modelDeploymentMonitoringJobName) {
        return this.pathTemplates.modelDeploymentMonitoringJobPathTemplate.match(modelDeploymentMonitoringJobName).model_deployment_monitoring_job;
    }
    /**
     * Return a fully-qualified modelEvaluation resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} model
     * @param {string} evaluation
     * @returns {string} Resource name string.
     */
    modelEvaluationPath(project, location, model, evaluation) {
        return this.pathTemplates.modelEvaluationPathTemplate.render({
            project: project,
            location: location,
            model: model,
            evaluation: evaluation,
        });
    }
    /**
     * Parse the project from ModelEvaluation resource.
     *
     * @param {string} modelEvaluationName
     *   A fully-qualified path representing ModelEvaluation resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromModelEvaluationName(modelEvaluationName) {
        return this.pathTemplates.modelEvaluationPathTemplate.match(modelEvaluationName).project;
    }
    /**
     * Parse the location from ModelEvaluation resource.
     *
     * @param {string} modelEvaluationName
     *   A fully-qualified path representing ModelEvaluation resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromModelEvaluationName(modelEvaluationName) {
        return this.pathTemplates.modelEvaluationPathTemplate.match(modelEvaluationName).location;
    }
    /**
     * Parse the model from ModelEvaluation resource.
     *
     * @param {string} modelEvaluationName
     *   A fully-qualified path representing ModelEvaluation resource.
     * @returns {string} A string representing the model.
     */
    matchModelFromModelEvaluationName(modelEvaluationName) {
        return this.pathTemplates.modelEvaluationPathTemplate.match(modelEvaluationName).model;
    }
    /**
     * Parse the evaluation from ModelEvaluation resource.
     *
     * @param {string} modelEvaluationName
     *   A fully-qualified path representing ModelEvaluation resource.
     * @returns {string} A string representing the evaluation.
     */
    matchEvaluationFromModelEvaluationName(modelEvaluationName) {
        return this.pathTemplates.modelEvaluationPathTemplate.match(modelEvaluationName).evaluation;
    }
    /**
     * Return a fully-qualified modelEvaluationSlice resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} model
     * @param {string} evaluation
     * @param {string} slice
     * @returns {string} Resource name string.
     */
    modelEvaluationSlicePath(project, location, model, evaluation, slice) {
        return this.pathTemplates.modelEvaluationSlicePathTemplate.render({
            project: project,
            location: location,
            model: model,
            evaluation: evaluation,
            slice: slice,
        });
    }
    /**
     * Parse the project from ModelEvaluationSlice resource.
     *
     * @param {string} modelEvaluationSliceName
     *   A fully-qualified path representing ModelEvaluationSlice resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromModelEvaluationSliceName(modelEvaluationSliceName) {
        return this.pathTemplates.modelEvaluationSlicePathTemplate.match(modelEvaluationSliceName).project;
    }
    /**
     * Parse the location from ModelEvaluationSlice resource.
     *
     * @param {string} modelEvaluationSliceName
     *   A fully-qualified path representing ModelEvaluationSlice resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromModelEvaluationSliceName(modelEvaluationSliceName) {
        return this.pathTemplates.modelEvaluationSlicePathTemplate.match(modelEvaluationSliceName).location;
    }
    /**
     * Parse the model from ModelEvaluationSlice resource.
     *
     * @param {string} modelEvaluationSliceName
     *   A fully-qualified path representing ModelEvaluationSlice resource.
     * @returns {string} A string representing the model.
     */
    matchModelFromModelEvaluationSliceName(modelEvaluationSliceName) {
        return this.pathTemplates.modelEvaluationSlicePathTemplate.match(modelEvaluationSliceName).model;
    }
    /**
     * Parse the evaluation from ModelEvaluationSlice resource.
     *
     * @param {string} modelEvaluationSliceName
     *   A fully-qualified path representing ModelEvaluationSlice resource.
     * @returns {string} A string representing the evaluation.
     */
    matchEvaluationFromModelEvaluationSliceName(modelEvaluationSliceName) {
        return this.pathTemplates.modelEvaluationSlicePathTemplate.match(modelEvaluationSliceName).evaluation;
    }
    /**
     * Parse the slice from ModelEvaluationSlice resource.
     *
     * @param {string} modelEvaluationSliceName
     *   A fully-qualified path representing ModelEvaluationSlice resource.
     * @returns {string} A string representing the slice.
     */
    matchSliceFromModelEvaluationSliceName(modelEvaluationSliceName) {
        return this.pathTemplates.modelEvaluationSlicePathTemplate.match(modelEvaluationSliceName).slice;
    }
    /**
     * Return a fully-qualified nasJob resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} nas_job
     * @returns {string} Resource name string.
     */
    nasJobPath(project, location, nasJob) {
        return this.pathTemplates.nasJobPathTemplate.render({
            project: project,
            location: location,
            nas_job: nasJob,
        });
    }
    /**
     * Parse the project from NasJob resource.
     *
     * @param {string} nasJobName
     *   A fully-qualified path representing NasJob resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromNasJobName(nasJobName) {
        return this.pathTemplates.nasJobPathTemplate.match(nasJobName).project;
    }
    /**
     * Parse the location from NasJob resource.
     *
     * @param {string} nasJobName
     *   A fully-qualified path representing NasJob resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromNasJobName(nasJobName) {
        return this.pathTemplates.nasJobPathTemplate.match(nasJobName).location;
    }
    /**
     * Parse the nas_job from NasJob resource.
     *
     * @param {string} nasJobName
     *   A fully-qualified path representing NasJob resource.
     * @returns {string} A string representing the nas_job.
     */
    matchNasJobFromNasJobName(nasJobName) {
        return this.pathTemplates.nasJobPathTemplate.match(nasJobName).nas_job;
    }
    /**
     * Return a fully-qualified nasTrialDetail resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} nas_job
     * @param {string} nas_trial_detail
     * @returns {string} Resource name string.
     */
    nasTrialDetailPath(project, location, nasJob, nasTrialDetail) {
        return this.pathTemplates.nasTrialDetailPathTemplate.render({
            project: project,
            location: location,
            nas_job: nasJob,
            nas_trial_detail: nasTrialDetail,
        });
    }
    /**
     * Parse the project from NasTrialDetail resource.
     *
     * @param {string} nasTrialDetailName
     *   A fully-qualified path representing NasTrialDetail resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromNasTrialDetailName(nasTrialDetailName) {
        return this.pathTemplates.nasTrialDetailPathTemplate.match(nasTrialDetailName).project;
    }
    /**
     * Parse the location from NasTrialDetail resource.
     *
     * @param {string} nasTrialDetailName
     *   A fully-qualified path representing NasTrialDetail resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromNasTrialDetailName(nasTrialDetailName) {
        return this.pathTemplates.nasTrialDetailPathTemplate.match(nasTrialDetailName).location;
    }
    /**
     * Parse the nas_job from NasTrialDetail resource.
     *
     * @param {string} nasTrialDetailName
     *   A fully-qualified path representing NasTrialDetail resource.
     * @returns {string} A string representing the nas_job.
     */
    matchNasJobFromNasTrialDetailName(nasTrialDetailName) {
        return this.pathTemplates.nasTrialDetailPathTemplate.match(nasTrialDetailName).nas_job;
    }
    /**
     * Parse the nas_trial_detail from NasTrialDetail resource.
     *
     * @param {string} nasTrialDetailName
     *   A fully-qualified path representing NasTrialDetail resource.
     * @returns {string} A string representing the nas_trial_detail.
     */
    matchNasTrialDetailFromNasTrialDetailName(nasTrialDetailName) {
        return this.pathTemplates.nasTrialDetailPathTemplate.match(nasTrialDetailName).nas_trial_detail;
    }
    /**
     * Return a fully-qualified notebookExecutionJob resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} notebook_execution_job
     * @returns {string} Resource name string.
     */
    notebookExecutionJobPath(project, location, notebookExecutionJob) {
        return this.pathTemplates.notebookExecutionJobPathTemplate.render({
            project: project,
            location: location,
            notebook_execution_job: notebookExecutionJob,
        });
    }
    /**
     * Parse the project from NotebookExecutionJob resource.
     *
     * @param {string} notebookExecutionJobName
     *   A fully-qualified path representing NotebookExecutionJob resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromNotebookExecutionJobName(notebookExecutionJobName) {
        return this.pathTemplates.notebookExecutionJobPathTemplate.match(notebookExecutionJobName).project;
    }
    /**
     * Parse the location from NotebookExecutionJob resource.
     *
     * @param {string} notebookExecutionJobName
     *   A fully-qualified path representing NotebookExecutionJob resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromNotebookExecutionJobName(notebookExecutionJobName) {
        return this.pathTemplates.notebookExecutionJobPathTemplate.match(notebookExecutionJobName).location;
    }
    /**
     * Parse the notebook_execution_job from NotebookExecutionJob resource.
     *
     * @param {string} notebookExecutionJobName
     *   A fully-qualified path representing NotebookExecutionJob resource.
     * @returns {string} A string representing the notebook_execution_job.
     */
    matchNotebookExecutionJobFromNotebookExecutionJobName(notebookExecutionJobName) {
        return this.pathTemplates.notebookExecutionJobPathTemplate.match(notebookExecutionJobName).notebook_execution_job;
    }
    /**
     * Return a fully-qualified notebookRuntime resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} notebook_runtime
     * @returns {string} Resource name string.
     */
    notebookRuntimePath(project, location, notebookRuntime) {
        return this.pathTemplates.notebookRuntimePathTemplate.render({
            project: project,
            location: location,
            notebook_runtime: notebookRuntime,
        });
    }
    /**
     * Parse the project from NotebookRuntime resource.
     *
     * @param {string} notebookRuntimeName
     *   A fully-qualified path representing NotebookRuntime resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromNotebookRuntimeName(notebookRuntimeName) {
        return this.pathTemplates.notebookRuntimePathTemplate.match(notebookRuntimeName).project;
    }
    /**
     * Parse the location from NotebookRuntime resource.
     *
     * @param {string} notebookRuntimeName
     *   A fully-qualified path representing NotebookRuntime resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromNotebookRuntimeName(notebookRuntimeName) {
        return this.pathTemplates.notebookRuntimePathTemplate.match(notebookRuntimeName).location;
    }
    /**
     * Parse the notebook_runtime from NotebookRuntime resource.
     *
     * @param {string} notebookRuntimeName
     *   A fully-qualified path representing NotebookRuntime resource.
     * @returns {string} A string representing the notebook_runtime.
     */
    matchNotebookRuntimeFromNotebookRuntimeName(notebookRuntimeName) {
        return this.pathTemplates.notebookRuntimePathTemplate.match(notebookRuntimeName).notebook_runtime;
    }
    /**
     * Return a fully-qualified notebookRuntimeTemplate resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} notebook_runtime_template
     * @returns {string} Resource name string.
     */
    notebookRuntimeTemplatePath(project, location, notebookRuntimeTemplate) {
        return this.pathTemplates.notebookRuntimeTemplatePathTemplate.render({
            project: project,
            location: location,
            notebook_runtime_template: notebookRuntimeTemplate,
        });
    }
    /**
     * Parse the project from NotebookRuntimeTemplate resource.
     *
     * @param {string} notebookRuntimeTemplateName
     *   A fully-qualified path representing NotebookRuntimeTemplate resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromNotebookRuntimeTemplateName(notebookRuntimeTemplateName) {
        return this.pathTemplates.notebookRuntimeTemplatePathTemplate.match(notebookRuntimeTemplateName).project;
    }
    /**
     * Parse the location from NotebookRuntimeTemplate resource.
     *
     * @param {string} notebookRuntimeTemplateName
     *   A fully-qualified path representing NotebookRuntimeTemplate resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromNotebookRuntimeTemplateName(notebookRuntimeTemplateName) {
        return this.pathTemplates.notebookRuntimeTemplatePathTemplate.match(notebookRuntimeTemplateName).location;
    }
    /**
     * Parse the notebook_runtime_template from NotebookRuntimeTemplate resource.
     *
     * @param {string} notebookRuntimeTemplateName
     *   A fully-qualified path representing NotebookRuntimeTemplate resource.
     * @returns {string} A string representing the notebook_runtime_template.
     */
    matchNotebookRuntimeTemplateFromNotebookRuntimeTemplateName(notebookRuntimeTemplateName) {
        return this.pathTemplates.notebookRuntimeTemplatePathTemplate.match(notebookRuntimeTemplateName).notebook_runtime_template;
    }
    /**
     * Return a fully-qualified persistentResource resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} persistent_resource
     * @returns {string} Resource name string.
     */
    persistentResourcePath(project, location, persistentResource) {
        return this.pathTemplates.persistentResourcePathTemplate.render({
            project: project,
            location: location,
            persistent_resource: persistentResource,
        });
    }
    /**
     * Parse the project from PersistentResource resource.
     *
     * @param {string} persistentResourceName
     *   A fully-qualified path representing PersistentResource resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromPersistentResourceName(persistentResourceName) {
        return this.pathTemplates.persistentResourcePathTemplate.match(persistentResourceName).project;
    }
    /**
     * Parse the location from PersistentResource resource.
     *
     * @param {string} persistentResourceName
     *   A fully-qualified path representing PersistentResource resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromPersistentResourceName(persistentResourceName) {
        return this.pathTemplates.persistentResourcePathTemplate.match(persistentResourceName).location;
    }
    /**
     * Parse the persistent_resource from PersistentResource resource.
     *
     * @param {string} persistentResourceName
     *   A fully-qualified path representing PersistentResource resource.
     * @returns {string} A string representing the persistent_resource.
     */
    matchPersistentResourceFromPersistentResourceName(persistentResourceName) {
        return this.pathTemplates.persistentResourcePathTemplate.match(persistentResourceName).persistent_resource;
    }
    /**
     * Return a fully-qualified pipelineJob resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} pipeline_job
     * @returns {string} Resource name string.
     */
    pipelineJobPath(project, location, pipelineJob) {
        return this.pathTemplates.pipelineJobPathTemplate.render({
            project: project,
            location: location,
            pipeline_job: pipelineJob,
        });
    }
    /**
     * Parse the project from PipelineJob resource.
     *
     * @param {string} pipelineJobName
     *   A fully-qualified path representing PipelineJob resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromPipelineJobName(pipelineJobName) {
        return this.pathTemplates.pipelineJobPathTemplate.match(pipelineJobName)
            .project;
    }
    /**
     * Parse the location from PipelineJob resource.
     *
     * @param {string} pipelineJobName
     *   A fully-qualified path representing PipelineJob resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromPipelineJobName(pipelineJobName) {
        return this.pathTemplates.pipelineJobPathTemplate.match(pipelineJobName)
            .location;
    }
    /**
     * Parse the pipeline_job from PipelineJob resource.
     *
     * @param {string} pipelineJobName
     *   A fully-qualified path representing PipelineJob resource.
     * @returns {string} A string representing the pipeline_job.
     */
    matchPipelineJobFromPipelineJobName(pipelineJobName) {
        return this.pathTemplates.pipelineJobPathTemplate.match(pipelineJobName)
            .pipeline_job;
    }
    /**
     * Return a fully-qualified project resource name string.
     *
     * @param {string} project
     * @returns {string} Resource name string.
     */
    projectPath(project) {
        return this.pathTemplates.projectPathTemplate.render({
            project: project,
        });
    }
    /**
     * Parse the project from Project resource.
     *
     * @param {string} projectName
     *   A fully-qualified path representing Project resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromProjectName(projectName) {
        return this.pathTemplates.projectPathTemplate.match(projectName).project;
    }
    /**
     * Return a fully-qualified projectLocationEndpoint resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} endpoint
     * @returns {string} Resource name string.
     */
    projectLocationEndpointPath(project, location, endpoint) {
        return this.pathTemplates.projectLocationEndpointPathTemplate.render({
            project: project,
            location: location,
            endpoint: endpoint,
        });
    }
    /**
     * Parse the project from ProjectLocationEndpoint resource.
     *
     * @param {string} projectLocationEndpointName
     *   A fully-qualified path representing project_location_endpoint resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromProjectLocationEndpointName(projectLocationEndpointName) {
        return this.pathTemplates.projectLocationEndpointPathTemplate.match(projectLocationEndpointName).project;
    }
    /**
     * Parse the location from ProjectLocationEndpoint resource.
     *
     * @param {string} projectLocationEndpointName
     *   A fully-qualified path representing project_location_endpoint resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromProjectLocationEndpointName(projectLocationEndpointName) {
        return this.pathTemplates.projectLocationEndpointPathTemplate.match(projectLocationEndpointName).location;
    }
    /**
     * Parse the endpoint from ProjectLocationEndpoint resource.
     *
     * @param {string} projectLocationEndpointName
     *   A fully-qualified path representing project_location_endpoint resource.
     * @returns {string} A string representing the endpoint.
     */
    matchEndpointFromProjectLocationEndpointName(projectLocationEndpointName) {
        return this.pathTemplates.projectLocationEndpointPathTemplate.match(projectLocationEndpointName).endpoint;
    }
    /**
     * Return a fully-qualified projectLocationFeatureGroupFeature resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} feature_group
     * @param {string} feature
     * @returns {string} Resource name string.
     */
    projectLocationFeatureGroupFeaturePath(project, location, featureGroup, feature) {
        return this.pathTemplates.projectLocationFeatureGroupFeaturePathTemplate.render({
            project: project,
            location: location,
            feature_group: featureGroup,
            feature: feature,
        });
    }
    /**
     * Parse the project from ProjectLocationFeatureGroupFeature resource.
     *
     * @param {string} projectLocationFeatureGroupFeatureName
     *   A fully-qualified path representing project_location_feature_group_feature resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromProjectLocationFeatureGroupFeatureName(projectLocationFeatureGroupFeatureName) {
        return this.pathTemplates.projectLocationFeatureGroupFeaturePathTemplate.match(projectLocationFeatureGroupFeatureName).project;
    }
    /**
     * Parse the location from ProjectLocationFeatureGroupFeature resource.
     *
     * @param {string} projectLocationFeatureGroupFeatureName
     *   A fully-qualified path representing project_location_feature_group_feature resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromProjectLocationFeatureGroupFeatureName(projectLocationFeatureGroupFeatureName) {
        return this.pathTemplates.projectLocationFeatureGroupFeaturePathTemplate.match(projectLocationFeatureGroupFeatureName).location;
    }
    /**
     * Parse the feature_group from ProjectLocationFeatureGroupFeature resource.
     *
     * @param {string} projectLocationFeatureGroupFeatureName
     *   A fully-qualified path representing project_location_feature_group_feature resource.
     * @returns {string} A string representing the feature_group.
     */
    matchFeatureGroupFromProjectLocationFeatureGroupFeatureName(projectLocationFeatureGroupFeatureName) {
        return this.pathTemplates.projectLocationFeatureGroupFeaturePathTemplate.match(projectLocationFeatureGroupFeatureName).feature_group;
    }
    /**
     * Parse the feature from ProjectLocationFeatureGroupFeature resource.
     *
     * @param {string} projectLocationFeatureGroupFeatureName
     *   A fully-qualified path representing project_location_feature_group_feature resource.
     * @returns {string} A string representing the feature.
     */
    matchFeatureFromProjectLocationFeatureGroupFeatureName(projectLocationFeatureGroupFeatureName) {
        return this.pathTemplates.projectLocationFeatureGroupFeaturePathTemplate.match(projectLocationFeatureGroupFeatureName).feature;
    }
    /**
     * Return a fully-qualified projectLocationFeaturestoreEntityTypeFeature resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} featurestore
     * @param {string} entity_type
     * @param {string} feature
     * @returns {string} Resource name string.
     */
    projectLocationFeaturestoreEntityTypeFeaturePath(project, location, featurestore, entityType, feature) {
        return this.pathTemplates.projectLocationFeaturestoreEntityTypeFeaturePathTemplate.render({
            project: project,
            location: location,
            featurestore: featurestore,
            entity_type: entityType,
            feature: feature,
        });
    }
    /**
     * Parse the project from ProjectLocationFeaturestoreEntityTypeFeature resource.
     *
     * @param {string} projectLocationFeaturestoreEntityTypeFeatureName
     *   A fully-qualified path representing project_location_featurestore_entity_type_feature resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromProjectLocationFeaturestoreEntityTypeFeatureName(projectLocationFeaturestoreEntityTypeFeatureName) {
        return this.pathTemplates.projectLocationFeaturestoreEntityTypeFeaturePathTemplate.match(projectLocationFeaturestoreEntityTypeFeatureName).project;
    }
    /**
     * Parse the location from ProjectLocationFeaturestoreEntityTypeFeature resource.
     *
     * @param {string} projectLocationFeaturestoreEntityTypeFeatureName
     *   A fully-qualified path representing project_location_featurestore_entity_type_feature resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromProjectLocationFeaturestoreEntityTypeFeatureName(projectLocationFeaturestoreEntityTypeFeatureName) {
        return this.pathTemplates.projectLocationFeaturestoreEntityTypeFeaturePathTemplate.match(projectLocationFeaturestoreEntityTypeFeatureName).location;
    }
    /**
     * Parse the featurestore from ProjectLocationFeaturestoreEntityTypeFeature resource.
     *
     * @param {string} projectLocationFeaturestoreEntityTypeFeatureName
     *   A fully-qualified path representing project_location_featurestore_entity_type_feature resource.
     * @returns {string} A string representing the featurestore.
     */
    matchFeaturestoreFromProjectLocationFeaturestoreEntityTypeFeatureName(projectLocationFeaturestoreEntityTypeFeatureName) {
        return this.pathTemplates.projectLocationFeaturestoreEntityTypeFeaturePathTemplate.match(projectLocationFeaturestoreEntityTypeFeatureName).featurestore;
    }
    /**
     * Parse the entity_type from ProjectLocationFeaturestoreEntityTypeFeature resource.
     *
     * @param {string} projectLocationFeaturestoreEntityTypeFeatureName
     *   A fully-qualified path representing project_location_featurestore_entity_type_feature resource.
     * @returns {string} A string representing the entity_type.
     */
    matchEntityTypeFromProjectLocationFeaturestoreEntityTypeFeatureName(projectLocationFeaturestoreEntityTypeFeatureName) {
        return this.pathTemplates.projectLocationFeaturestoreEntityTypeFeaturePathTemplate.match(projectLocationFeaturestoreEntityTypeFeatureName).entity_type;
    }
    /**
     * Parse the feature from ProjectLocationFeaturestoreEntityTypeFeature resource.
     *
     * @param {string} projectLocationFeaturestoreEntityTypeFeatureName
     *   A fully-qualified path representing project_location_featurestore_entity_type_feature resource.
     * @returns {string} A string representing the feature.
     */
    matchFeatureFromProjectLocationFeaturestoreEntityTypeFeatureName(projectLocationFeaturestoreEntityTypeFeatureName) {
        return this.pathTemplates.projectLocationFeaturestoreEntityTypeFeaturePathTemplate.match(projectLocationFeaturestoreEntityTypeFeatureName).feature;
    }
    /**
     * Return a fully-qualified projectLocationPublisherModel resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} publisher
     * @param {string} model
     * @returns {string} Resource name string.
     */
    projectLocationPublisherModelPath(project, location, publisher, model) {
        return this.pathTemplates.projectLocationPublisherModelPathTemplate.render({
            project: project,
            location: location,
            publisher: publisher,
            model: model,
        });
    }
    /**
     * Parse the project from ProjectLocationPublisherModel resource.
     *
     * @param {string} projectLocationPublisherModelName
     *   A fully-qualified path representing project_location_publisher_model resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromProjectLocationPublisherModelName(projectLocationPublisherModelName) {
        return this.pathTemplates.projectLocationPublisherModelPathTemplate.match(projectLocationPublisherModelName).project;
    }
    /**
     * Parse the location from ProjectLocationPublisherModel resource.
     *
     * @param {string} projectLocationPublisherModelName
     *   A fully-qualified path representing project_location_publisher_model resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromProjectLocationPublisherModelName(projectLocationPublisherModelName) {
        return this.pathTemplates.projectLocationPublisherModelPathTemplate.match(projectLocationPublisherModelName).location;
    }
    /**
     * Parse the publisher from ProjectLocationPublisherModel resource.
     *
     * @param {string} projectLocationPublisherModelName
     *   A fully-qualified path representing project_location_publisher_model resource.
     * @returns {string} A string representing the publisher.
     */
    matchPublisherFromProjectLocationPublisherModelName(projectLocationPublisherModelName) {
        return this.pathTemplates.projectLocationPublisherModelPathTemplate.match(projectLocationPublisherModelName).publisher;
    }
    /**
     * Parse the model from ProjectLocationPublisherModel resource.
     *
     * @param {string} projectLocationPublisherModelName
     *   A fully-qualified path representing project_location_publisher_model resource.
     * @returns {string} A string representing the model.
     */
    matchModelFromProjectLocationPublisherModelName(projectLocationPublisherModelName) {
        return this.pathTemplates.projectLocationPublisherModelPathTemplate.match(projectLocationPublisherModelName).model;
    }
    /**
     * Return a fully-qualified publisherModel resource name string.
     *
     * @param {string} publisher
     * @param {string} model
     * @returns {string} Resource name string.
     */
    publisherModelPath(publisher, model) {
        return this.pathTemplates.publisherModelPathTemplate.render({
            publisher: publisher,
            model: model,
        });
    }
    /**
     * Parse the publisher from PublisherModel resource.
     *
     * @param {string} publisherModelName
     *   A fully-qualified path representing PublisherModel resource.
     * @returns {string} A string representing the publisher.
     */
    matchPublisherFromPublisherModelName(publisherModelName) {
        return this.pathTemplates.publisherModelPathTemplate.match(publisherModelName).publisher;
    }
    /**
     * Parse the model from PublisherModel resource.
     *
     * @param {string} publisherModelName
     *   A fully-qualified path representing PublisherModel resource.
     * @returns {string} A string representing the model.
     */
    matchModelFromPublisherModelName(publisherModelName) {
        return this.pathTemplates.publisherModelPathTemplate.match(publisherModelName).model;
    }
    /**
     * Return a fully-qualified ragCorpus resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} rag_corpus
     * @returns {string} Resource name string.
     */
    ragCorpusPath(project, location, ragCorpus) {
        return this.pathTemplates.ragCorpusPathTemplate.render({
            project: project,
            location: location,
            rag_corpus: ragCorpus,
        });
    }
    /**
     * Parse the project from RagCorpus resource.
     *
     * @param {string} ragCorpusName
     *   A fully-qualified path representing RagCorpus resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromRagCorpusName(ragCorpusName) {
        return this.pathTemplates.ragCorpusPathTemplate.match(ragCorpusName)
            .project;
    }
    /**
     * Parse the location from RagCorpus resource.
     *
     * @param {string} ragCorpusName
     *   A fully-qualified path representing RagCorpus resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromRagCorpusName(ragCorpusName) {
        return this.pathTemplates.ragCorpusPathTemplate.match(ragCorpusName)
            .location;
    }
    /**
     * Parse the rag_corpus from RagCorpus resource.
     *
     * @param {string} ragCorpusName
     *   A fully-qualified path representing RagCorpus resource.
     * @returns {string} A string representing the rag_corpus.
     */
    matchRagCorpusFromRagCorpusName(ragCorpusName) {
        return this.pathTemplates.ragCorpusPathTemplate.match(ragCorpusName)
            .rag_corpus;
    }
    /**
     * Return a fully-qualified ragFile resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} rag_corpus
     * @param {string} rag_file
     * @returns {string} Resource name string.
     */
    ragFilePath(project, location, ragCorpus, ragFile) {
        return this.pathTemplates.ragFilePathTemplate.render({
            project: project,
            location: location,
            rag_corpus: ragCorpus,
            rag_file: ragFile,
        });
    }
    /**
     * Parse the project from RagFile resource.
     *
     * @param {string} ragFileName
     *   A fully-qualified path representing RagFile resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromRagFileName(ragFileName) {
        return this.pathTemplates.ragFilePathTemplate.match(ragFileName).project;
    }
    /**
     * Parse the location from RagFile resource.
     *
     * @param {string} ragFileName
     *   A fully-qualified path representing RagFile resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromRagFileName(ragFileName) {
        return this.pathTemplates.ragFilePathTemplate.match(ragFileName).location;
    }
    /**
     * Parse the rag_corpus from RagFile resource.
     *
     * @param {string} ragFileName
     *   A fully-qualified path representing RagFile resource.
     * @returns {string} A string representing the rag_corpus.
     */
    matchRagCorpusFromRagFileName(ragFileName) {
        return this.pathTemplates.ragFilePathTemplate.match(ragFileName).rag_corpus;
    }
    /**
     * Parse the rag_file from RagFile resource.
     *
     * @param {string} ragFileName
     *   A fully-qualified path representing RagFile resource.
     * @returns {string} A string representing the rag_file.
     */
    matchRagFileFromRagFileName(ragFileName) {
        return this.pathTemplates.ragFilePathTemplate.match(ragFileName).rag_file;
    }
    /**
     * Return a fully-qualified reasoningEngine resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} reasoning_engine
     * @returns {string} Resource name string.
     */
    reasoningEnginePath(project, location, reasoningEngine) {
        return this.pathTemplates.reasoningEnginePathTemplate.render({
            project: project,
            location: location,
            reasoning_engine: reasoningEngine,
        });
    }
    /**
     * Parse the project from ReasoningEngine resource.
     *
     * @param {string} reasoningEngineName
     *   A fully-qualified path representing ReasoningEngine resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromReasoningEngineName(reasoningEngineName) {
        return this.pathTemplates.reasoningEnginePathTemplate.match(reasoningEngineName).project;
    }
    /**
     * Parse the location from ReasoningEngine resource.
     *
     * @param {string} reasoningEngineName
     *   A fully-qualified path representing ReasoningEngine resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromReasoningEngineName(reasoningEngineName) {
        return this.pathTemplates.reasoningEnginePathTemplate.match(reasoningEngineName).location;
    }
    /**
     * Parse the reasoning_engine from ReasoningEngine resource.
     *
     * @param {string} reasoningEngineName
     *   A fully-qualified path representing ReasoningEngine resource.
     * @returns {string} A string representing the reasoning_engine.
     */
    matchReasoningEngineFromReasoningEngineName(reasoningEngineName) {
        return this.pathTemplates.reasoningEnginePathTemplate.match(reasoningEngineName).reasoning_engine;
    }
    /**
     * Return a fully-qualified savedQuery resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} dataset
     * @param {string} saved_query
     * @returns {string} Resource name string.
     */
    savedQueryPath(project, location, dataset, savedQuery) {
        return this.pathTemplates.savedQueryPathTemplate.render({
            project: project,
            location: location,
            dataset: dataset,
            saved_query: savedQuery,
        });
    }
    /**
     * Parse the project from SavedQuery resource.
     *
     * @param {string} savedQueryName
     *   A fully-qualified path representing SavedQuery resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromSavedQueryName(savedQueryName) {
        return this.pathTemplates.savedQueryPathTemplate.match(savedQueryName)
            .project;
    }
    /**
     * Parse the location from SavedQuery resource.
     *
     * @param {string} savedQueryName
     *   A fully-qualified path representing SavedQuery resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromSavedQueryName(savedQueryName) {
        return this.pathTemplates.savedQueryPathTemplate.match(savedQueryName)
            .location;
    }
    /**
     * Parse the dataset from SavedQuery resource.
     *
     * @param {string} savedQueryName
     *   A fully-qualified path representing SavedQuery resource.
     * @returns {string} A string representing the dataset.
     */
    matchDatasetFromSavedQueryName(savedQueryName) {
        return this.pathTemplates.savedQueryPathTemplate.match(savedQueryName)
            .dataset;
    }
    /**
     * Parse the saved_query from SavedQuery resource.
     *
     * @param {string} savedQueryName
     *   A fully-qualified path representing SavedQuery resource.
     * @returns {string} A string representing the saved_query.
     */
    matchSavedQueryFromSavedQueryName(savedQueryName) {
        return this.pathTemplates.savedQueryPathTemplate.match(savedQueryName)
            .saved_query;
    }
    /**
     * Return a fully-qualified schedule resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} schedule
     * @returns {string} Resource name string.
     */
    schedulePath(project, location, schedule) {
        return this.pathTemplates.schedulePathTemplate.render({
            project: project,
            location: location,
            schedule: schedule,
        });
    }
    /**
     * Parse the project from Schedule resource.
     *
     * @param {string} scheduleName
     *   A fully-qualified path representing Schedule resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromScheduleName(scheduleName) {
        return this.pathTemplates.schedulePathTemplate.match(scheduleName).project;
    }
    /**
     * Parse the location from Schedule resource.
     *
     * @param {string} scheduleName
     *   A fully-qualified path representing Schedule resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromScheduleName(scheduleName) {
        return this.pathTemplates.schedulePathTemplate.match(scheduleName).location;
    }
    /**
     * Parse the schedule from Schedule resource.
     *
     * @param {string} scheduleName
     *   A fully-qualified path representing Schedule resource.
     * @returns {string} A string representing the schedule.
     */
    matchScheduleFromScheduleName(scheduleName) {
        return this.pathTemplates.schedulePathTemplate.match(scheduleName).schedule;
    }
    /**
     * Return a fully-qualified specialistPool resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} specialist_pool
     * @returns {string} Resource name string.
     */
    specialistPoolPath(project, location, specialistPool) {
        return this.pathTemplates.specialistPoolPathTemplate.render({
            project: project,
            location: location,
            specialist_pool: specialistPool,
        });
    }
    /**
     * Parse the project from SpecialistPool resource.
     *
     * @param {string} specialistPoolName
     *   A fully-qualified path representing SpecialistPool resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromSpecialistPoolName(specialistPoolName) {
        return this.pathTemplates.specialistPoolPathTemplate.match(specialistPoolName).project;
    }
    /**
     * Parse the location from SpecialistPool resource.
     *
     * @param {string} specialistPoolName
     *   A fully-qualified path representing SpecialistPool resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromSpecialistPoolName(specialistPoolName) {
        return this.pathTemplates.specialistPoolPathTemplate.match(specialistPoolName).location;
    }
    /**
     * Parse the specialist_pool from SpecialistPool resource.
     *
     * @param {string} specialistPoolName
     *   A fully-qualified path representing SpecialistPool resource.
     * @returns {string} A string representing the specialist_pool.
     */
    matchSpecialistPoolFromSpecialistPoolName(specialistPoolName) {
        return this.pathTemplates.specialistPoolPathTemplate.match(specialistPoolName).specialist_pool;
    }
    /**
     * Return a fully-qualified study resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} study
     * @returns {string} Resource name string.
     */
    studyPath(project, location, study) {
        return this.pathTemplates.studyPathTemplate.render({
            project: project,
            location: location,
            study: study,
        });
    }
    /**
     * Parse the project from Study resource.
     *
     * @param {string} studyName
     *   A fully-qualified path representing Study resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromStudyName(studyName) {
        return this.pathTemplates.studyPathTemplate.match(studyName).project;
    }
    /**
     * Parse the location from Study resource.
     *
     * @param {string} studyName
     *   A fully-qualified path representing Study resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromStudyName(studyName) {
        return this.pathTemplates.studyPathTemplate.match(studyName).location;
    }
    /**
     * Parse the study from Study resource.
     *
     * @param {string} studyName
     *   A fully-qualified path representing Study resource.
     * @returns {string} A string representing the study.
     */
    matchStudyFromStudyName(studyName) {
        return this.pathTemplates.studyPathTemplate.match(studyName).study;
    }
    /**
     * Return a fully-qualified tensorboard resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} tensorboard
     * @returns {string} Resource name string.
     */
    tensorboardPath(project, location, tensorboard) {
        return this.pathTemplates.tensorboardPathTemplate.render({
            project: project,
            location: location,
            tensorboard: tensorboard,
        });
    }
    /**
     * Parse the project from Tensorboard resource.
     *
     * @param {string} tensorboardName
     *   A fully-qualified path representing Tensorboard resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromTensorboardName(tensorboardName) {
        return this.pathTemplates.tensorboardPathTemplate.match(tensorboardName)
            .project;
    }
    /**
     * Parse the location from Tensorboard resource.
     *
     * @param {string} tensorboardName
     *   A fully-qualified path representing Tensorboard resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromTensorboardName(tensorboardName) {
        return this.pathTemplates.tensorboardPathTemplate.match(tensorboardName)
            .location;
    }
    /**
     * Parse the tensorboard from Tensorboard resource.
     *
     * @param {string} tensorboardName
     *   A fully-qualified path representing Tensorboard resource.
     * @returns {string} A string representing the tensorboard.
     */
    matchTensorboardFromTensorboardName(tensorboardName) {
        return this.pathTemplates.tensorboardPathTemplate.match(tensorboardName)
            .tensorboard;
    }
    /**
     * Return a fully-qualified tensorboardExperiment resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} tensorboard
     * @param {string} experiment
     * @returns {string} Resource name string.
     */
    tensorboardExperimentPath(project, location, tensorboard, experiment) {
        return this.pathTemplates.tensorboardExperimentPathTemplate.render({
            project: project,
            location: location,
            tensorboard: tensorboard,
            experiment: experiment,
        });
    }
    /**
     * Parse the project from TensorboardExperiment resource.
     *
     * @param {string} tensorboardExperimentName
     *   A fully-qualified path representing TensorboardExperiment resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromTensorboardExperimentName(tensorboardExperimentName) {
        return this.pathTemplates.tensorboardExperimentPathTemplate.match(tensorboardExperimentName).project;
    }
    /**
     * Parse the location from TensorboardExperiment resource.
     *
     * @param {string} tensorboardExperimentName
     *   A fully-qualified path representing TensorboardExperiment resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromTensorboardExperimentName(tensorboardExperimentName) {
        return this.pathTemplates.tensorboardExperimentPathTemplate.match(tensorboardExperimentName).location;
    }
    /**
     * Parse the tensorboard from TensorboardExperiment resource.
     *
     * @param {string} tensorboardExperimentName
     *   A fully-qualified path representing TensorboardExperiment resource.
     * @returns {string} A string representing the tensorboard.
     */
    matchTensorboardFromTensorboardExperimentName(tensorboardExperimentName) {
        return this.pathTemplates.tensorboardExperimentPathTemplate.match(tensorboardExperimentName).tensorboard;
    }
    /**
     * Parse the experiment from TensorboardExperiment resource.
     *
     * @param {string} tensorboardExperimentName
     *   A fully-qualified path representing TensorboardExperiment resource.
     * @returns {string} A string representing the experiment.
     */
    matchExperimentFromTensorboardExperimentName(tensorboardExperimentName) {
        return this.pathTemplates.tensorboardExperimentPathTemplate.match(tensorboardExperimentName).experiment;
    }
    /**
     * Return a fully-qualified tensorboardRun resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} tensorboard
     * @param {string} experiment
     * @param {string} run
     * @returns {string} Resource name string.
     */
    tensorboardRunPath(project, location, tensorboard, experiment, run) {
        return this.pathTemplates.tensorboardRunPathTemplate.render({
            project: project,
            location: location,
            tensorboard: tensorboard,
            experiment: experiment,
            run: run,
        });
    }
    /**
     * Parse the project from TensorboardRun resource.
     *
     * @param {string} tensorboardRunName
     *   A fully-qualified path representing TensorboardRun resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromTensorboardRunName(tensorboardRunName) {
        return this.pathTemplates.tensorboardRunPathTemplate.match(tensorboardRunName).project;
    }
    /**
     * Parse the location from TensorboardRun resource.
     *
     * @param {string} tensorboardRunName
     *   A fully-qualified path representing TensorboardRun resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromTensorboardRunName(tensorboardRunName) {
        return this.pathTemplates.tensorboardRunPathTemplate.match(tensorboardRunName).location;
    }
    /**
     * Parse the tensorboard from TensorboardRun resource.
     *
     * @param {string} tensorboardRunName
     *   A fully-qualified path representing TensorboardRun resource.
     * @returns {string} A string representing the tensorboard.
     */
    matchTensorboardFromTensorboardRunName(tensorboardRunName) {
        return this.pathTemplates.tensorboardRunPathTemplate.match(tensorboardRunName).tensorboard;
    }
    /**
     * Parse the experiment from TensorboardRun resource.
     *
     * @param {string} tensorboardRunName
     *   A fully-qualified path representing TensorboardRun resource.
     * @returns {string} A string representing the experiment.
     */
    matchExperimentFromTensorboardRunName(tensorboardRunName) {
        return this.pathTemplates.tensorboardRunPathTemplate.match(tensorboardRunName).experiment;
    }
    /**
     * Parse the run from TensorboardRun resource.
     *
     * @param {string} tensorboardRunName
     *   A fully-qualified path representing TensorboardRun resource.
     * @returns {string} A string representing the run.
     */
    matchRunFromTensorboardRunName(tensorboardRunName) {
        return this.pathTemplates.tensorboardRunPathTemplate.match(tensorboardRunName).run;
    }
    /**
     * Return a fully-qualified tensorboardTimeSeries resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} tensorboard
     * @param {string} experiment
     * @param {string} run
     * @param {string} time_series
     * @returns {string} Resource name string.
     */
    tensorboardTimeSeriesPath(project, location, tensorboard, experiment, run, timeSeries) {
        return this.pathTemplates.tensorboardTimeSeriesPathTemplate.render({
            project: project,
            location: location,
            tensorboard: tensorboard,
            experiment: experiment,
            run: run,
            time_series: timeSeries,
        });
    }
    /**
     * Parse the project from TensorboardTimeSeries resource.
     *
     * @param {string} tensorboardTimeSeriesName
     *   A fully-qualified path representing TensorboardTimeSeries resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromTensorboardTimeSeriesName(tensorboardTimeSeriesName) {
        return this.pathTemplates.tensorboardTimeSeriesPathTemplate.match(tensorboardTimeSeriesName).project;
    }
    /**
     * Parse the location from TensorboardTimeSeries resource.
     *
     * @param {string} tensorboardTimeSeriesName
     *   A fully-qualified path representing TensorboardTimeSeries resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromTensorboardTimeSeriesName(tensorboardTimeSeriesName) {
        return this.pathTemplates.tensorboardTimeSeriesPathTemplate.match(tensorboardTimeSeriesName).location;
    }
    /**
     * Parse the tensorboard from TensorboardTimeSeries resource.
     *
     * @param {string} tensorboardTimeSeriesName
     *   A fully-qualified path representing TensorboardTimeSeries resource.
     * @returns {string} A string representing the tensorboard.
     */
    matchTensorboardFromTensorboardTimeSeriesName(tensorboardTimeSeriesName) {
        return this.pathTemplates.tensorboardTimeSeriesPathTemplate.match(tensorboardTimeSeriesName).tensorboard;
    }
    /**
     * Parse the experiment from TensorboardTimeSeries resource.
     *
     * @param {string} tensorboardTimeSeriesName
     *   A fully-qualified path representing TensorboardTimeSeries resource.
     * @returns {string} A string representing the experiment.
     */
    matchExperimentFromTensorboardTimeSeriesName(tensorboardTimeSeriesName) {
        return this.pathTemplates.tensorboardTimeSeriesPathTemplate.match(tensorboardTimeSeriesName).experiment;
    }
    /**
     * Parse the run from TensorboardTimeSeries resource.
     *
     * @param {string} tensorboardTimeSeriesName
     *   A fully-qualified path representing TensorboardTimeSeries resource.
     * @returns {string} A string representing the run.
     */
    matchRunFromTensorboardTimeSeriesName(tensorboardTimeSeriesName) {
        return this.pathTemplates.tensorboardTimeSeriesPathTemplate.match(tensorboardTimeSeriesName).run;
    }
    /**
     * Parse the time_series from TensorboardTimeSeries resource.
     *
     * @param {string} tensorboardTimeSeriesName
     *   A fully-qualified path representing TensorboardTimeSeries resource.
     * @returns {string} A string representing the time_series.
     */
    matchTimeSeriesFromTensorboardTimeSeriesName(tensorboardTimeSeriesName) {
        return this.pathTemplates.tensorboardTimeSeriesPathTemplate.match(tensorboardTimeSeriesName).time_series;
    }
    /**
     * Return a fully-qualified trainingPipeline resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} training_pipeline
     * @returns {string} Resource name string.
     */
    trainingPipelinePath(project, location, trainingPipeline) {
        return this.pathTemplates.trainingPipelinePathTemplate.render({
            project: project,
            location: location,
            training_pipeline: trainingPipeline,
        });
    }
    /**
     * Parse the project from TrainingPipeline resource.
     *
     * @param {string} trainingPipelineName
     *   A fully-qualified path representing TrainingPipeline resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromTrainingPipelineName(trainingPipelineName) {
        return this.pathTemplates.trainingPipelinePathTemplate.match(trainingPipelineName).project;
    }
    /**
     * Parse the location from TrainingPipeline resource.
     *
     * @param {string} trainingPipelineName
     *   A fully-qualified path representing TrainingPipeline resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromTrainingPipelineName(trainingPipelineName) {
        return this.pathTemplates.trainingPipelinePathTemplate.match(trainingPipelineName).location;
    }
    /**
     * Parse the training_pipeline from TrainingPipeline resource.
     *
     * @param {string} trainingPipelineName
     *   A fully-qualified path representing TrainingPipeline resource.
     * @returns {string} A string representing the training_pipeline.
     */
    matchTrainingPipelineFromTrainingPipelineName(trainingPipelineName) {
        return this.pathTemplates.trainingPipelinePathTemplate.match(trainingPipelineName).training_pipeline;
    }
    /**
     * Return a fully-qualified trial resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} study
     * @param {string} trial
     * @returns {string} Resource name string.
     */
    trialPath(project, location, study, trial) {
        return this.pathTemplates.trialPathTemplate.render({
            project: project,
            location: location,
            study: study,
            trial: trial,
        });
    }
    /**
     * Parse the project from Trial resource.
     *
     * @param {string} trialName
     *   A fully-qualified path representing Trial resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromTrialName(trialName) {
        return this.pathTemplates.trialPathTemplate.match(trialName).project;
    }
    /**
     * Parse the location from Trial resource.
     *
     * @param {string} trialName
     *   A fully-qualified path representing Trial resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromTrialName(trialName) {
        return this.pathTemplates.trialPathTemplate.match(trialName).location;
    }
    /**
     * Parse the study from Trial resource.
     *
     * @param {string} trialName
     *   A fully-qualified path representing Trial resource.
     * @returns {string} A string representing the study.
     */
    matchStudyFromTrialName(trialName) {
        return this.pathTemplates.trialPathTemplate.match(trialName).study;
    }
    /**
     * Parse the trial from Trial resource.
     *
     * @param {string} trialName
     *   A fully-qualified path representing Trial resource.
     * @returns {string} A string representing the trial.
     */
    matchTrialFromTrialName(trialName) {
        return this.pathTemplates.trialPathTemplate.match(trialName).trial;
    }
    /**
     * Return a fully-qualified tuningJob resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} tuning_job
     * @returns {string} Resource name string.
     */
    tuningJobPath(project, location, tuningJob) {
        return this.pathTemplates.tuningJobPathTemplate.render({
            project: project,
            location: location,
            tuning_job: tuningJob,
        });
    }
    /**
     * Parse the project from TuningJob resource.
     *
     * @param {string} tuningJobName
     *   A fully-qualified path representing TuningJob resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromTuningJobName(tuningJobName) {
        return this.pathTemplates.tuningJobPathTemplate.match(tuningJobName)
            .project;
    }
    /**
     * Parse the location from TuningJob resource.
     *
     * @param {string} tuningJobName
     *   A fully-qualified path representing TuningJob resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromTuningJobName(tuningJobName) {
        return this.pathTemplates.tuningJobPathTemplate.match(tuningJobName)
            .location;
    }
    /**
     * Parse the tuning_job from TuningJob resource.
     *
     * @param {string} tuningJobName
     *   A fully-qualified path representing TuningJob resource.
     * @returns {string} A string representing the tuning_job.
     */
    matchTuningJobFromTuningJobName(tuningJobName) {
        return this.pathTemplates.tuningJobPathTemplate.match(tuningJobName)
            .tuning_job;
    }
    /**
     * Terminate the gRPC channel and close the client.
     *
     * The client will no longer be usable and all future behavior is undefined.
     * @returns {Promise} A promise that resolves when the client is closed.
     */
    close() {
        if (this.featureRegistryServiceStub && !this._terminated) {
            return this.featureRegistryServiceStub.then(stub => {
                this._terminated = true;
                stub.close();
                this.iamClient.close();
                this.locationsClient.close();
                this.operationsClient.close();
            });
        }
        return Promise.resolve();
    }
}
exports.FeatureRegistryServiceClient = FeatureRegistryServiceClient;
//# sourceMappingURL=feature_registry_service_client.js.map