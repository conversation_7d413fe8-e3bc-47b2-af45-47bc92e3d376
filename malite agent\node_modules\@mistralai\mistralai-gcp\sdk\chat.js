"use strict";
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.Chat = void 0;
const chatComplete_js_1 = require("../funcs/chatComplete.js");
const chatStream_js_1 = require("../funcs/chatStream.js");
const sdks_js_1 = require("../lib/sdks.js");
const fp_js_1 = require("../types/fp.js");
class Chat extends sdks_js_1.ClientSDK {
    /**
     * Stream chat completion
     *
     * @remarks
     * Mistral AI provides the ability to stream responses back to a client in order to allow partial results for certain requests. Tokens will be sent as data-only server-sent events as they become available, with the stream terminated by a data: [DONE] message. Otherwise, the server will hold the request open until the timeout or until completion, with the response containing the full result as JSON.
     */
    async stream(request, options) {
        return (0, fp_js_1.unwrapAsync)((0, chatStream_js_1.chatStream)(this, request, options));
    }
    /**
     * Chat Completion
     */
    async complete(request, options) {
        return (0, fp_js_1.unwrapAsync)((0, chatComplete_js_1.chatComplete)(this, request, options));
    }
}
exports.Chat = Chat;
//# sourceMappingURL=chat.js.map