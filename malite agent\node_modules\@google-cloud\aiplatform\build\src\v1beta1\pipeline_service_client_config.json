{"interfaces": {"google.cloud.aiplatform.v1beta1.PipelineService": {"retry_codes": {"non_idempotent": [], "idempotent": ["DEADLINE_EXCEEDED", "UNAVAILABLE"]}, "retry_params": {"default": {"initial_retry_delay_millis": 100, "retry_delay_multiplier": 1.3, "max_retry_delay_millis": 60000, "initial_rpc_timeout_millis": 60000, "rpc_timeout_multiplier": 1, "max_rpc_timeout_millis": 60000, "total_timeout_millis": 600000}}, "methods": {"CreateTrainingPipeline": {"timeout_millis": 5000, "retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "GetTrainingPipeline": {"timeout_millis": 5000, "retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "ListTrainingPipelines": {"timeout_millis": 5000, "retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "DeleteTrainingPipeline": {"timeout_millis": 5000, "retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "CancelTrainingPipeline": {"timeout_millis": 5000, "retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "CreatePipelineJob": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "GetPipelineJob": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "ListPipelineJobs": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "DeletePipelineJob": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "BatchDeletePipelineJobs": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "CancelPipelineJob": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "BatchCancelPipelineJobs": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}}}}}