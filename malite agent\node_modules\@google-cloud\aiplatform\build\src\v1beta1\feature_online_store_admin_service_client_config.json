{"interfaces": {"google.cloud.aiplatform.v1beta1.FeatureOnlineStoreAdminService": {"retry_codes": {"non_idempotent": [], "idempotent": ["DEADLINE_EXCEEDED", "UNAVAILABLE"]}, "retry_params": {"default": {"initial_retry_delay_millis": 100, "retry_delay_multiplier": 1.3, "max_retry_delay_millis": 60000, "initial_rpc_timeout_millis": 60000, "rpc_timeout_multiplier": 1, "max_rpc_timeout_millis": 60000, "total_timeout_millis": 600000}}, "methods": {"CreateFeatureOnlineStore": {"timeout_millis": 5000, "retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "GetFeatureOnlineStore": {"timeout_millis": 5000, "retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "ListFeatureOnlineStores": {"timeout_millis": 5000, "retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "UpdateFeatureOnlineStore": {"timeout_millis": 5000, "retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "DeleteFeatureOnlineStore": {"timeout_millis": 5000, "retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "CreateFeatureView": {"timeout_millis": 5000, "retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "GetFeatureView": {"timeout_millis": 5000, "retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "ListFeatureViews": {"timeout_millis": 5000, "retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "UpdateFeatureView": {"timeout_millis": 5000, "retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "DeleteFeatureView": {"timeout_millis": 5000, "retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "SyncFeatureView": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "GetFeatureViewSync": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "ListFeatureViewSyncs": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}}}}}