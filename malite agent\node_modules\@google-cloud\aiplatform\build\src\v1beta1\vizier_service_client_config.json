{"interfaces": {"google.cloud.aiplatform.v1beta1.VizierService": {"retry_codes": {"non_idempotent": [], "idempotent": ["DEADLINE_EXCEEDED", "UNAVAILABLE"]}, "retry_params": {"default": {"initial_retry_delay_millis": 100, "retry_delay_multiplier": 1.3, "max_retry_delay_millis": 60000, "initial_rpc_timeout_millis": 60000, "rpc_timeout_multiplier": 1, "max_rpc_timeout_millis": 60000, "total_timeout_millis": 600000}}, "methods": {"CreateStudy": {"timeout_millis": 5000, "retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "GetStudy": {"timeout_millis": 5000, "retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "ListStudies": {"timeout_millis": 5000, "retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "DeleteStudy": {"timeout_millis": 5000, "retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "LookupStudy": {"timeout_millis": 5000, "retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "SuggestTrials": {"timeout_millis": 5000, "retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "CreateTrial": {"timeout_millis": 5000, "retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "GetTrial": {"timeout_millis": 5000, "retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "ListTrials": {"timeout_millis": 5000, "retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "AddTrialMeasurement": {"timeout_millis": 5000, "retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "CompleteTrial": {"timeout_millis": 5000, "retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "DeleteTrial": {"timeout_millis": 5000, "retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "CheckTrialEarlyStoppingState": {"timeout_millis": 5000, "retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "StopTrial": {"timeout_millis": 5000, "retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "ListOptimalTrials": {"timeout_millis": 5000, "retry_codes_name": "non_idempotent", "retry_params_name": "default"}}}}}