{"version": 3, "sources": ["../src/registry.ts"], "sourcesContent": ["/**\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Dotprompt } from 'dotprompt';\nimport { AsyncLocalStorage } from 'node:async_hooks';\nimport * as z from 'zod';\nimport {\n  Action,\n  ActionMetadata,\n  runOutsideActionRuntimeContext,\n} from './action.js';\nimport { GenkitError } from './error.js';\nimport { logger } from './logging.js';\nimport { PluginProvider } from './plugin.js';\nimport { JSONSchema, toJsonSchema } from './schema.js';\n\nexport type AsyncProvider<T> = () => Promise<T>;\n\n/**\n * Type of a runnable action.\n */\nexport type ActionType =\n  | 'custom'\n  | 'embedder'\n  | 'evaluator'\n  | 'executable-prompt'\n  | 'flow'\n  | 'indexer'\n  | 'model'\n  | 'prompt'\n  | 'reranker'\n  | 'retriever'\n  | 'tool'\n  | 'util';\n\n/**\n * A schema is either a Zod schema or a JSON schema.\n */\nexport interface Schema {\n  schema?: z.ZodTypeAny;\n  jsonSchema?: JSONSchema;\n}\n\nfunction parsePluginName(registryKey: string) {\n  const tokens = registryKey.split('/');\n  if (tokens.length >= 4) {\n    return tokens[2];\n  }\n  return undefined;\n}\n\ninterface ParsedRegistryKey {\n  actionType: ActionType;\n  pluginName?: string;\n  actionName: string;\n}\n\n/**\n * Parses the registry key into key parts as per the key format convention. Ex:\n *  - /model/googleai/gemini-2.0-flash\n *  - /prompt/my-plugin/folder/my-prompt\n *  - /util/generate\n */\nexport function parseRegistryKey(\n  registryKey: string\n): ParsedRegistryKey | undefined {\n  const tokens = registryKey.split('/');\n  if (tokens.length < 3) {\n    // Invalid key format\n    return undefined;\n  }\n  // ex: /model/googleai/gemini-2.0-flash or /prompt/my-plugin/folder/my-prompt\n  if (tokens.length >= 4) {\n    return {\n      actionType: tokens[1] as ActionType,\n      pluginName: tokens[2],\n      actionName: tokens.slice(3).join('/'),\n    };\n  }\n  // ex: /util/generate\n  return {\n    actionType: tokens[1] as ActionType,\n    actionName: tokens[2],\n  };\n}\n\nexport type ActionsRecord = Record<string, Action<z.ZodTypeAny, z.ZodTypeAny>>;\nexport type ActionMetadataRecord = Record<string, ActionMetadata>;\n\n/**\n * The registry is used to store and lookup actions, trace stores, flow state stores, plugins, and schemas.\n */\nexport class Registry {\n  private actionsById: Record<\n    string,\n    | Action<z.ZodTypeAny, z.ZodTypeAny>\n    | PromiseLike<Action<z.ZodTypeAny, z.ZodTypeAny>>\n  > = {};\n  private pluginsByName: Record<string, PluginProvider> = {};\n  private schemasByName: Record<string, Schema> = {};\n  private valueByTypeAndName: Record<string, Record<string, any>> = {};\n  private allPluginsInitialized = false;\n  public apiStability: 'stable' | 'beta' = 'stable';\n\n  readonly asyncStore: AsyncStore;\n  readonly dotprompt: Dotprompt;\n  readonly parent?: Registry;\n\n  constructor(parent?: Registry) {\n    if (parent) {\n      this.parent = parent;\n      this.apiStability = parent?.apiStability;\n      this.asyncStore = parent.asyncStore;\n      this.dotprompt = parent.dotprompt;\n    } else {\n      this.asyncStore = new AsyncStore();\n      this.dotprompt = new Dotprompt({\n        schemaResolver: async (name) => {\n          const resolvedSchema = await this.lookupSchema(name);\n          if (!resolvedSchema) {\n            throw new GenkitError({\n              message: `Schema '${name}' not found`,\n              status: 'NOT_FOUND',\n            });\n          }\n          return toJsonSchema(resolvedSchema);\n        },\n      });\n    }\n  }\n\n  /**\n   * Creates a new registry overlaid onto the provided registry.\n   * @param parent The parent registry.\n   * @returns The new overlaid registry.\n   */\n  static withParent(parent: Registry) {\n    return new Registry(parent);\n  }\n\n  /**\n   * Looks up an action in the registry.\n   * @param key The key of the action to lookup.\n   * @returns The action.\n   */\n  async lookupAction<\n    I extends z.ZodTypeAny,\n    O extends z.ZodTypeAny,\n    R extends Action<I, O>,\n  >(key: string): Promise<R> {\n    // We always try to initialize the plugin first.\n    const parsedKey = parseRegistryKey(key);\n    if (parsedKey?.pluginName && this.pluginsByName[parsedKey.pluginName]) {\n      await this.initializePlugin(parsedKey.pluginName);\n\n      // If we don't see the key in the registry, we try to resolve\n      // the action with the dynamic resolver. If it exists, it will\n      // register the action in the registry.\n      if (!this.actionsById[key]) {\n        await this.resolvePluginAction(\n          parsedKey.pluginName,\n          parsedKey.actionType,\n          parsedKey.actionName\n        );\n      }\n    }\n    return (\n      ((await this.actionsById[key]) as R) || this.parent?.lookupAction(key)\n    );\n  }\n\n  /**\n   * Registers an action in the registry.\n   * @param type The type of the action to register.\n   * @param action The action to register.\n   */\n  registerAction<I extends z.ZodTypeAny, O extends z.ZodTypeAny>(\n    type: ActionType,\n    action: Action<I, O>\n  ) {\n    if (type !== action.__action.actionType) {\n      throw new GenkitError({\n        status: 'INVALID_ARGUMENT',\n        message: `action type (${type}) does not match type on action (${action.__action.actionType})`,\n      });\n    }\n    const key = `/${type}/${action.__action.name}`;\n    logger.debug(`registering ${key}`);\n    if (this.actionsById.hasOwnProperty(key)) {\n      // TODO: Make this an error!\n      logger.warn(\n        `WARNING: ${key} already has an entry in the registry. Overwriting.`\n      );\n    }\n    this.actionsById[key] = action;\n  }\n\n  /**\n   * Registers an action promise in the registry.\n   */\n  registerActionAsync<I extends z.ZodTypeAny, O extends z.ZodTypeAny>(\n    type: ActionType,\n    name: string,\n    action: PromiseLike<Action<I, O>>\n  ) {\n    const key = `/${type}/${name}`;\n    logger.debug(`registering ${key} (async)`);\n    if (this.actionsById.hasOwnProperty(key)) {\n      // TODO: Make this an error!\n      logger.warn(\n        `WARNING: ${key} already has an entry in the registry. Overwriting.`\n      );\n    }\n    this.actionsById[key] = action;\n  }\n\n  /**\n   * Returns all actions that have been registered in the registry.\n   * @returns All actions in the registry as a map of <key, action>.\n   */\n  async listActions(): Promise<ActionsRecord> {\n    await this.initializeAllPlugins();\n    const actions: Record<string, Action<z.ZodTypeAny, z.ZodTypeAny>> = {};\n    await Promise.all(\n      Object.entries(this.actionsById).map(async ([key, action]) => {\n        actions[key] = await action;\n      })\n    );\n    return {\n      ...(await this.parent?.listActions()),\n      ...actions,\n    };\n  }\n\n  /**\n   * Returns all actions that are resolvable by plugins as well as those that are already\n   * in the registry.\n   *\n   * NOTE: this method should not be used in latency sensitive code paths.\n   * It may rely on \"admin\" API calls such as \"list models\", which may cause increased cold start latency.\n   *\n   * @returns All resolvable action metadata as a map of <key, action metadata>.\n   */\n  async listResolvableActions(): Promise<ActionMetadataRecord> {\n    const resolvableActions = {} as ActionMetadataRecord;\n    // We listActions for all plugins in parallel.\n    await Promise.all(\n      Object.entries(this.pluginsByName).map(async ([pluginName, plugin]) => {\n        if (plugin.listActions) {\n          try {\n            (await plugin.listActions()).forEach((meta) => {\n              if (!meta.name) {\n                throw new GenkitError({\n                  status: 'INVALID_ARGUMENT',\n                  message: `Invalid metadata when listing actions from ${pluginName} - name required`,\n                });\n              }\n              if (!meta.actionType) {\n                throw new GenkitError({\n                  status: 'INVALID_ARGUMENT',\n                  message: `Invalid metadata when listing actions from ${pluginName} - actionType required`,\n                });\n              }\n              resolvableActions[`/${meta.actionType}/${meta.name}`] = meta;\n            });\n          } catch (e) {\n            logger.error(`Error listing actions for ${pluginName}\\n`, e);\n          }\n        }\n      })\n    );\n    // Also add actions that are already registered.\n    for (const [key, action] of Object.entries(await this.listActions())) {\n      resolvableActions[key] = action.__action;\n    }\n    return {\n      ...(await this.parent?.listResolvableActions()),\n      ...resolvableActions,\n    };\n  }\n\n  /**\n   * Initializes all plugins in the registry.\n   */\n  async initializeAllPlugins() {\n    if (this.allPluginsInitialized) {\n      return;\n    }\n    for (const pluginName of Object.keys(this.pluginsByName)) {\n      await this.initializePlugin(pluginName);\n    }\n    this.allPluginsInitialized = true;\n  }\n\n  /**\n   * Registers a plugin provider. This plugin must be initialized before it can be used by calling {@link initializePlugin} or {@link initializeAllPlugins}.\n   * @param name The name of the plugin to register.\n   * @param provider The plugin provider.\n   */\n  registerPluginProvider(name: string, provider: PluginProvider) {\n    if (this.pluginsByName[name]) {\n      throw new Error(`Plugin ${name} already registered`);\n    }\n    this.allPluginsInitialized = false;\n    let cached;\n    let isInitialized = false;\n    this.pluginsByName[name] = {\n      name: provider.name,\n      initializer: () => {\n        if (!isInitialized) {\n          cached = provider.initializer();\n          isInitialized = true;\n        }\n        return cached;\n      },\n      resolver: async (actionType: ActionType, actionName: string) => {\n        if (provider.resolver) {\n          await provider.resolver(actionType, actionName);\n        }\n      },\n      listActions: async () => {\n        if (provider.listActions) {\n          return await provider.listActions();\n        }\n        return [];\n      },\n    };\n  }\n\n  /**\n   * Looks up a plugin.\n   * @param name The name of the plugin to lookup.\n   * @returns The plugin provider.\n   */\n  lookupPlugin(name: string): PluginProvider | undefined {\n    return this.pluginsByName[name] || this.parent?.lookupPlugin(name);\n  }\n\n  /**\n   * Resolves a new Action dynamically by registering it.\n   * @param pluginName The name of the plugin\n   * @param actionType The type of the action\n   * @param actionName The name of the action\n   * @returns\n   */\n  async resolvePluginAction(\n    pluginName: string,\n    actionType: ActionType,\n    actionName: string\n  ) {\n    const plugin = this.pluginsByName[pluginName];\n    if (plugin) {\n      return await runOutsideActionRuntimeContext(this, async () => {\n        if (plugin.resolver) {\n          await plugin.resolver(actionType, actionName);\n        }\n      });\n    }\n  }\n\n  /**\n   * Initializes a plugin already registered with {@link registerPluginProvider}.\n   * @param name The name of the plugin to initialize.\n   * @returns The plugin.\n   */\n  async initializePlugin(name: string) {\n    if (this.pluginsByName[name]) {\n      return await runOutsideActionRuntimeContext(this, () =>\n        this.pluginsByName[name].initializer()\n      );\n    }\n  }\n\n  /**\n   * Registers a schema.\n   * @param name The name of the schema to register.\n   * @param data The schema to register (either a Zod schema or a JSON schema).\n   */\n  registerSchema(name: string, data: Schema) {\n    if (this.schemasByName[name]) {\n      throw new Error(`Schema ${name} already registered`);\n    }\n    this.schemasByName[name] = data;\n  }\n\n  registerValue(type: string, name: string, value: any) {\n    if (!this.valueByTypeAndName[type]) {\n      this.valueByTypeAndName[type] = {};\n    }\n    this.valueByTypeAndName[type][name] = value;\n  }\n\n  async lookupValue<T = unknown>(\n    type: string,\n    key: string\n  ): Promise<T | undefined> {\n    const pluginName = parsePluginName(key);\n    if (!this.valueByTypeAndName[type]?.[key] && pluginName) {\n      await this.initializePlugin(pluginName);\n    }\n    return (\n      (this.valueByTypeAndName[type]?.[key] as T) ||\n      this.parent?.lookupValue(type, key)\n    );\n  }\n\n  async listValues<T>(type: string): Promise<Record<string, T>> {\n    await this.initializeAllPlugins();\n    return {\n      ...((await this.parent?.listValues(type)) || {}),\n      ...(this.valueByTypeAndName[type] || {}),\n    } as Record<string, T>;\n  }\n\n  /**\n   * Looks up a schema.\n   * @param name The name of the schema to lookup.\n   * @returns The schema.\n   */\n  lookupSchema(name: string): Schema | undefined {\n    return this.schemasByName[name] || this.parent?.lookupSchema(name);\n  }\n}\n\n/**\n * Manages AsyncLocalStorage instances in a single place.\n */\nexport class AsyncStore {\n  private asls: Record<string, AsyncLocalStorage<any>> = {};\n\n  getStore<T>(key: string): T | undefined {\n    return this.asls[key]?.getStore();\n  }\n\n  run<T, R>(key: string, store: T, callback: () => R): R {\n    if (!this.asls[key]) {\n      this.asls[key] = new AsyncLocalStorage<T>();\n    }\n    return this.asls[key].run(store, callback);\n  }\n}\n\n/**\n * An object that has a reference to Genkit Registry.\n */\nexport interface HasRegistry {\n  get registry(): Registry;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAgBA,uBAA0B;AAC1B,8BAAkC;AAElC,oBAIO;AACP,mBAA4B;AAC5B,qBAAuB;AAEvB,oBAAyC;AA6BzC,SAAS,gBAAgB,aAAqB;AAC5C,QAAM,SAAS,YAAY,MAAM,GAAG;AACpC,MAAI,OAAO,UAAU,GAAG;AACtB,WAAO,OAAO,CAAC;AAAA,EACjB;AACA,SAAO;AACT;AAcO,SAAS,iBACd,aAC+B;AAC/B,QAAM,SAAS,YAAY,MAAM,GAAG;AACpC,MAAI,OAAO,SAAS,GAAG;AAErB,WAAO;AAAA,EACT;AAEA,MAAI,OAAO,UAAU,GAAG;AACtB,WAAO;AAAA,MACL,YAAY,OAAO,CAAC;AAAA,MACpB,YAAY,OAAO,CAAC;AAAA,MACpB,YAAY,OAAO,MAAM,CAAC,EAAE,KAAK,GAAG;AAAA,IACtC;AAAA,EACF;AAEA,SAAO;AAAA,IACL,YAAY,OAAO,CAAC;AAAA,IACpB,YAAY,OAAO,CAAC;AAAA,EACtB;AACF;AAQO,MAAM,SAAS;AAAA,EACZ,cAIJ,CAAC;AAAA,EACG,gBAAgD,CAAC;AAAA,EACjD,gBAAwC,CAAC;AAAA,EACzC,qBAA0D,CAAC;AAAA,EAC3D,wBAAwB;AAAA,EACzB,eAAkC;AAAA,EAEhC;AAAA,EACA;AAAA,EACA;AAAA,EAET,YAAY,QAAmB;AAC7B,QAAI,QAAQ;AACV,WAAK,SAAS;AACd,WAAK,eAAe,QAAQ;AAC5B,WAAK,aAAa,OAAO;AACzB,WAAK,YAAY,OAAO;AAAA,IAC1B,OAAO;AACL,WAAK,aAAa,IAAI,WAAW;AACjC,WAAK,YAAY,IAAI,2BAAU;AAAA,QAC7B,gBAAgB,OAAO,SAAS;AAC9B,gBAAM,iBAAiB,MAAM,KAAK,aAAa,IAAI;AACnD,cAAI,CAAC,gBAAgB;AACnB,kBAAM,IAAI,yBAAY;AAAA,cACpB,SAAS,WAAW,IAAI;AAAA,cACxB,QAAQ;AAAA,YACV,CAAC;AAAA,UACH;AACA,qBAAO,4BAAa,cAAc;AAAA,QACpC;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,OAAO,WAAW,QAAkB;AAClC,WAAO,IAAI,SAAS,MAAM;AAAA,EAC5B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,aAIJ,KAAyB;AAEzB,UAAM,YAAY,iBAAiB,GAAG;AACtC,QAAI,WAAW,cAAc,KAAK,cAAc,UAAU,UAAU,GAAG;AACrE,YAAM,KAAK,iBAAiB,UAAU,UAAU;AAKhD,UAAI,CAAC,KAAK,YAAY,GAAG,GAAG;AAC1B,cAAM,KAAK;AAAA,UACT,UAAU;AAAA,UACV,UAAU;AAAA,UACV,UAAU;AAAA,QACZ;AAAA,MACF;AAAA,IACF;AACA,WACI,MAAM,KAAK,YAAY,GAAG,KAAY,KAAK,QAAQ,aAAa,GAAG;AAAA,EAEzE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,eACE,MACA,QACA;AACA,QAAI,SAAS,OAAO,SAAS,YAAY;AACvC,YAAM,IAAI,yBAAY;AAAA,QACpB,QAAQ;AAAA,QACR,SAAS,gBAAgB,IAAI,oCAAoC,OAAO,SAAS,UAAU;AAAA,MAC7F,CAAC;AAAA,IACH;AACA,UAAM,MAAM,IAAI,IAAI,IAAI,OAAO,SAAS,IAAI;AAC5C,0BAAO,MAAM,eAAe,GAAG,EAAE;AACjC,QAAI,KAAK,YAAY,eAAe,GAAG,GAAG;AAExC,4BAAO;AAAA,QACL,YAAY,GAAG;AAAA,MACjB;AAAA,IACF;AACA,SAAK,YAAY,GAAG,IAAI;AAAA,EAC1B;AAAA;AAAA;AAAA;AAAA,EAKA,oBACE,MACA,MACA,QACA;AACA,UAAM,MAAM,IAAI,IAAI,IAAI,IAAI;AAC5B,0BAAO,MAAM,eAAe,GAAG,UAAU;AACzC,QAAI,KAAK,YAAY,eAAe,GAAG,GAAG;AAExC,4BAAO;AAAA,QACL,YAAY,GAAG;AAAA,MACjB;AAAA,IACF;AACA,SAAK,YAAY,GAAG,IAAI;AAAA,EAC1B;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,cAAsC;AAC1C,UAAM,KAAK,qBAAqB;AAChC,UAAM,UAA8D,CAAC;AACrE,UAAM,QAAQ;AAAA,MACZ,OAAO,QAAQ,KAAK,WAAW,EAAE,IAAI,OAAO,CAAC,KAAK,MAAM,MAAM;AAC5D,gBAAQ,GAAG,IAAI,MAAM;AAAA,MACvB,CAAC;AAAA,IACH;AACA,WAAO;AAAA,MACL,GAAI,MAAM,KAAK,QAAQ,YAAY;AAAA,MACnC,GAAG;AAAA,IACL;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,MAAM,wBAAuD;AAC3D,UAAM,oBAAoB,CAAC;AAE3B,UAAM,QAAQ;AAAA,MACZ,OAAO,QAAQ,KAAK,aAAa,EAAE,IAAI,OAAO,CAAC,YAAY,MAAM,MAAM;AACrE,YAAI,OAAO,aAAa;AACtB,cAAI;AACF,aAAC,MAAM,OAAO,YAAY,GAAG,QAAQ,CAAC,SAAS;AAC7C,kBAAI,CAAC,KAAK,MAAM;AACd,sBAAM,IAAI,yBAAY;AAAA,kBACpB,QAAQ;AAAA,kBACR,SAAS,8CAA8C,UAAU;AAAA,gBACnE,CAAC;AAAA,cACH;AACA,kBAAI,CAAC,KAAK,YAAY;AACpB,sBAAM,IAAI,yBAAY;AAAA,kBACpB,QAAQ;AAAA,kBACR,SAAS,8CAA8C,UAAU;AAAA,gBACnE,CAAC;AAAA,cACH;AACA,gCAAkB,IAAI,KAAK,UAAU,IAAI,KAAK,IAAI,EAAE,IAAI;AAAA,YAC1D,CAAC;AAAA,UACH,SAAS,GAAG;AACV,kCAAO,MAAM,6BAA6B,UAAU;AAAA,GAAM,CAAC;AAAA,UAC7D;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AAEA,eAAW,CAAC,KAAK,MAAM,KAAK,OAAO,QAAQ,MAAM,KAAK,YAAY,CAAC,GAAG;AACpE,wBAAkB,GAAG,IAAI,OAAO;AAAA,IAClC;AACA,WAAO;AAAA,MACL,GAAI,MAAM,KAAK,QAAQ,sBAAsB;AAAA,MAC7C,GAAG;AAAA,IACL;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,uBAAuB;AAC3B,QAAI,KAAK,uBAAuB;AAC9B;AAAA,IACF;AACA,eAAW,cAAc,OAAO,KAAK,KAAK,aAAa,GAAG;AACxD,YAAM,KAAK,iBAAiB,UAAU;AAAA,IACxC;AACA,SAAK,wBAAwB;AAAA,EAC/B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,uBAAuB,MAAc,UAA0B;AAC7D,QAAI,KAAK,cAAc,IAAI,GAAG;AAC5B,YAAM,IAAI,MAAM,UAAU,IAAI,qBAAqB;AAAA,IACrD;AACA,SAAK,wBAAwB;AAC7B,QAAI;AACJ,QAAI,gBAAgB;AACpB,SAAK,cAAc,IAAI,IAAI;AAAA,MACzB,MAAM,SAAS;AAAA,MACf,aAAa,MAAM;AACjB,YAAI,CAAC,eAAe;AAClB,mBAAS,SAAS,YAAY;AAC9B,0BAAgB;AAAA,QAClB;AACA,eAAO;AAAA,MACT;AAAA,MACA,UAAU,OAAO,YAAwB,eAAuB;AAC9D,YAAI,SAAS,UAAU;AACrB,gBAAM,SAAS,SAAS,YAAY,UAAU;AAAA,QAChD;AAAA,MACF;AAAA,MACA,aAAa,YAAY;AACvB,YAAI,SAAS,aAAa;AACxB,iBAAO,MAAM,SAAS,YAAY;AAAA,QACpC;AACA,eAAO,CAAC;AAAA,MACV;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,aAAa,MAA0C;AACrD,WAAO,KAAK,cAAc,IAAI,KAAK,KAAK,QAAQ,aAAa,IAAI;AAAA,EACnE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,MAAM,oBACJ,YACA,YACA,YACA;AACA,UAAM,SAAS,KAAK,cAAc,UAAU;AAC5C,QAAI,QAAQ;AACV,aAAO,UAAM,8CAA+B,MAAM,YAAY;AAC5D,YAAI,OAAO,UAAU;AACnB,gBAAM,OAAO,SAAS,YAAY,UAAU;AAAA,QAC9C;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,iBAAiB,MAAc;AACnC,QAAI,KAAK,cAAc,IAAI,GAAG;AAC5B,aAAO,UAAM;AAAA,QAA+B;AAAA,QAAM,MAChD,KAAK,cAAc,IAAI,EAAE,YAAY;AAAA,MACvC;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,eAAe,MAAc,MAAc;AACzC,QAAI,KAAK,cAAc,IAAI,GAAG;AAC5B,YAAM,IAAI,MAAM,UAAU,IAAI,qBAAqB;AAAA,IACrD;AACA,SAAK,cAAc,IAAI,IAAI;AAAA,EAC7B;AAAA,EAEA,cAAc,MAAc,MAAc,OAAY;AACpD,QAAI,CAAC,KAAK,mBAAmB,IAAI,GAAG;AAClC,WAAK,mBAAmB,IAAI,IAAI,CAAC;AAAA,IACnC;AACA,SAAK,mBAAmB,IAAI,EAAE,IAAI,IAAI;AAAA,EACxC;AAAA,EAEA,MAAM,YACJ,MACA,KACwB;AACxB,UAAM,aAAa,gBAAgB,GAAG;AACtC,QAAI,CAAC,KAAK,mBAAmB,IAAI,IAAI,GAAG,KAAK,YAAY;AACvD,YAAM,KAAK,iBAAiB,UAAU;AAAA,IACxC;AACA,WACG,KAAK,mBAAmB,IAAI,IAAI,GAAG,KACpC,KAAK,QAAQ,YAAY,MAAM,GAAG;AAAA,EAEtC;AAAA,EAEA,MAAM,WAAc,MAA0C;AAC5D,UAAM,KAAK,qBAAqB;AAChC,WAAO;AAAA,MACL,GAAK,MAAM,KAAK,QAAQ,WAAW,IAAI,KAAM,CAAC;AAAA,MAC9C,GAAI,KAAK,mBAAmB,IAAI,KAAK,CAAC;AAAA,IACxC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,aAAa,MAAkC;AAC7C,WAAO,KAAK,cAAc,IAAI,KAAK,KAAK,QAAQ,aAAa,IAAI;AAAA,EACnE;AACF;AAKO,MAAM,WAAW;AAAA,EACd,OAA+C,CAAC;AAAA,EAExD,SAAY,KAA4B;AACtC,WAAO,KAAK,KAAK,GAAG,GAAG,SAAS;AAAA,EAClC;AAAA,EAEA,IAAU,KAAa,OAAU,UAAsB;AACrD,QAAI,CAAC,KAAK,KAAK,GAAG,GAAG;AACnB,WAAK,KAAK,GAAG,IAAI,IAAI,0CAAqB;AAAA,IAC5C;AACA,WAAO,KAAK,KAAK,GAAG,EAAE,IAAI,OAAO,QAAQ;AAAA,EAC3C;AACF;", "names": []}