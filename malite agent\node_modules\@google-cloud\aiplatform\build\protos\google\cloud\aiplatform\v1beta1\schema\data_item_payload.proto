// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.aiplatform.v1beta1.schema;

import "google/api/field_behavior.proto";

option csharp_namespace = "Google.Cloud.AIPlatform.V1Beta1.Schema";
option go_package = "cloud.google.com/go/aiplatform/apiv1beta1/schema/schemapb;schemapb";
option java_multiple_files = true;
option java_outer_classname = "DataItemPayloadProto";
option java_package = "com.google.cloud.aiplatform.v1beta1.schema";
option php_namespace = "Google\\Cloud\\AIPlatform\\V1beta1\\Schema";
option ruby_package = "Google::Cloud::AIPlatform::V1beta1::Schema";

// Payload of Image DataItem.
message ImageDataItem {
  // Required. Google Cloud Storage URI points to the original image in user's bucket.
  // The image is up to 30MB in size.
  string gcs_uri = 1 [(google.api.field_behavior) = REQUIRED];

  // Output only. The mime type of the content of the image. Only the images in below listed
  // mime types are supported.
  // - image/jpeg
  // - image/gif
  // - image/png
  // - image/webp
  // - image/bmp
  // - image/tiff
  // - image/vnd.microsoft.icon
  string mime_type = 2 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Payload of Video DataItem.
message VideoDataItem {
  // Required. Google Cloud Storage URI points to the original video in user's bucket.
  // The video is up to 50 GB in size and up to 3 hour in duration.
  string gcs_uri = 1 [(google.api.field_behavior) = REQUIRED];

  // Output only. The mime type of the content of the video. Only the videos in below listed
  // mime types are supported.
  // Supported mime_type:
  // - video/mp4
  // - video/avi
  // - video/quicktime
  string mime_type = 2 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Payload of Text DataItem.
message TextDataItem {
  // Output only. Google Cloud Storage URI points to the original text in user's bucket.
  // The text file is up to 10MB in size.
  string gcs_uri = 1 [(google.api.field_behavior) = OUTPUT_ONLY];
}
