{"version": 3, "file": "MeterSelector.js", "sourceRoot": "", "sources": ["../../../src/view/MeterSelector.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAEH,OAAO,EAAE,cAAc,EAAa,MAAM,aAAa,CAAC;AAQxD,MAAM,OAAO,aAAa;IAKxB,YAAY,QAAgC;QAC1C,IAAI,CAAC,WAAW,GAAG,IAAI,cAAc,CAAC,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAE,IAAI,CAAC,CAAC;QACtD,IAAI,CAAC,cAAc,GAAG,IAAI,cAAc,CAAC,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAE,OAAO,CAAC,CAAC;QAC5D,IAAI,CAAC,gBAAgB,GAAG,IAAI,cAAc,CAAC,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAE,SAAS,CAAC,CAAC;IAClE,CAAC;IAED,aAAa;QACX,OAAO,IAAI,CAAC,WAAW,CAAC;IAC1B,CAAC;IAED;;OAEG;IACH,gBAAgB;QACd,OAAO,IAAI,CAAC,cAAc,CAAC;IAC7B,CAAC;IAED,kBAAkB;QAChB,OAAO,IAAI,CAAC,gBAAgB,CAAC;IAC/B,CAAC;CACF", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ExactPredicate, Predicate } from './Predicate';\n\nexport interface MeterSelectorCriteria {\n  name?: string;\n  version?: string;\n  schemaUrl?: string;\n}\n\nexport class MeterSelector {\n  private _nameFilter: Predicate;\n  private _versionFilter: Predicate;\n  private _schemaUrlFilter: Predicate;\n\n  constructor(criteria?: MeterSelectorCriteria) {\n    this._nameFilter = new ExactPredicate(criteria?.name);\n    this._versionFilter = new ExactPredicate(criteria?.version);\n    this._schemaUrlFilter = new ExactPredicate(criteria?.schemaUrl);\n  }\n\n  getNameFilter() {\n    return this._nameFilter;\n  }\n\n  /**\n   * TODO: semver filter? no spec yet.\n   */\n  getVersionFilter() {\n    return this._versionFilter;\n  }\n\n  getSchemaUrlFilter() {\n    return this._schemaUrlFilter;\n  }\n}\n"]}