{"version": 3, "file": "hex-to-base64.js", "sourceRoot": "", "sources": ["../../../../src/platform/browser/hex-to-base64.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;;;;;;;;;;;;;;;;;;;;;;;AAEH,OAAO,EAAE,WAAW,EAAE,MAAM,4BAA4B,CAAC;AAEzD,MAAM,UAAU,WAAW,CAAC,MAAc;IACxC,OAAO,IAAI,CAAC,MAAM,CAAC,YAAY,OAAnB,MAAM,2BAAiB,WAAW,CAAC,MAAM,CAAC,WAAE,CAAC;AAC3D,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { hexToBinary } from '../../common/hex-to-binary';\n\nexport function hexToBase64(hexStr: string): string {\n  return btoa(String.fromCharCode(...hexToBinary(hexStr)));\n}\n"]}