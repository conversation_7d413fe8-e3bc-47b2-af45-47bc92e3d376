{"version": 3, "sources": ["../../src/modelgarden/types.ts"], "sourcesContent": ["/**\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ModelReference } from 'genkit';\nimport { CommonPluginOptions } from '../common/types.js';\n\n/**\n * Evaluation metric config. Use `metricSpec` to define the behavior of the metric.\n * The value of `metricSpec` will be included in the request to the API. See the API documentation\n * for details on the possible values of `metricSpec` for each metric.\n * https://cloud.google.com/vertex-ai/generative-ai/docs/model-reference/evaluation#parameter-list\n */\n\n/** Options specific to Model Garden configuration */\nexport interface ModelGardenOptions {\n  models: ModelReference<any>[];\n  openAiBaseUrlTemplate?: string;\n}\n\nexport interface PluginOptions\n  extends CommonPluginOptions,\n    ModelGardenOptions {}\n"], "mappings": ";;;;;;;;;;;;;;AAAA;AAAA;", "names": []}