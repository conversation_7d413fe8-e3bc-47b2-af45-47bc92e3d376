{"version": 3, "file": "Logger.js", "sourceRoot": "", "sources": ["../../src/Logger.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAIH,4CAA6C;AAE7C,2CAAwC;AAGxC,MAAa,MAAM;IACjB,YACkB,oBAA0C,EAClD,YAAuC;QAD/B,yBAAoB,GAApB,oBAAoB,CAAsB;QAClD,iBAAY,GAAZ,YAAY,CAA2B;IAC9C,CAAC;IAEG,IAAI,CAAC,SAA4B;QACtC,MAAM,cAAc,GAAG,SAAS,CAAC,OAAO,IAAI,aAAO,CAAC,MAAM,EAAE,CAAC;QAC7D;;;;WAIG;QACH,MAAM,iBAAiB,GAAG,IAAI,qBAAS,CACrC,IAAI,CAAC,YAAY,EACjB,IAAI,CAAC,oBAAoB,kBAEvB,OAAO,EAAE,cAAc,IACpB,SAAS,EAEf,CAAC;QACF;;;WAGG;QACH,IAAI,CAAC,YAAY,CAAC,eAAe,CAAC,MAAM,CAAC,iBAAiB,EAAE,cAAc,CAAC,CAAC;QAC5E;;;WAGG;QACH,iBAAiB,CAAC,aAAa,EAAE,CAAC;IACpC,CAAC;CACF;AAhCD,wBAgCC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport type * as logsAPI from '@opentelemetry/api-logs';\nimport type { InstrumentationScope } from '@opentelemetry/core';\nimport { context } from '@opentelemetry/api';\n\nimport { LogRecord } from './LogRecord';\nimport { LoggerProviderSharedState } from './internal/LoggerProviderSharedState';\n\nexport class Logger implements logsAPI.Logger {\n  constructor(\n    public readonly instrumentationScope: InstrumentationScope,\n    private _sharedState: LoggerProviderSharedState\n  ) {}\n\n  public emit(logRecord: logsAPI.LogRecord): void {\n    const currentContext = logRecord.context || context.active();\n    /**\n     * If a Logger was obtained with include_trace_context=true,\n     * the LogRecords it emits MUST automatically include the Trace Context from the active Context,\n     * if Context has not been explicitly set.\n     */\n    const logRecordInstance = new LogRecord(\n      this._sharedState,\n      this.instrumentationScope,\n      {\n        context: currentContext,\n        ...logRecord,\n      }\n    );\n    /**\n     * the explicitly passed Context,\n     * the current Context, or an empty Context if the Logger was obtained with include_trace_context=false\n     */\n    this._sharedState.activeProcessor.onEmit(logRecordInstance, currentContext);\n    /**\n     * A LogRecordProcessor may freely modify logRecord for the duration of the OnEmit call.\n     * If logRecord is needed after OnEmit returns (i.e. for asynchronous processing) only reads are permitted.\n     */\n    logRecordInstance._makeReadonly();\n  }\n}\n"]}