{"version": 3, "sources": ["../../src/common/types.ts"], "sourcesContent": ["/**\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ModelReference } from 'genkit';\nimport { GoogleAuthOptions } from 'google-auth-library';\nimport { GeminiConfigSchema } from '../gemini';\n\n/** Common options for Vertex AI plugin configuration */\nexport interface CommonPluginOptions {\n  /** The Google Cloud project id to call. */\n  projectId?: string;\n  /** The Google Cloud region to call. */\n  location: string;\n  /** Provide custom authentication configuration for connecting to Vertex AI. */\n  googleAuth?: GoogleAuthOptions;\n  /** Enables additional debug traces (e.g. raw model API call details). */\n  experimental_debugTraces?: boolean;\n}\n\n/** Combined plugin options, extending common options with subplugin-specific options */\nexport interface PluginOptions extends CommonPluginOptions {\n  models?: (\n    | ModelReference</** @ignore */ typeof GeminiConfigSchema>\n    | string\n  )[];\n}\n"], "mappings": ";;;;;;;;;;;;;;AAAA;AAAA;", "names": []}