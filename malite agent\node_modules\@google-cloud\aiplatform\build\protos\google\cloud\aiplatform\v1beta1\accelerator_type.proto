// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.aiplatform.v1beta1;

option csharp_namespace = "Google.Cloud.AIPlatform.V1Beta1";
option go_package = "cloud.google.com/go/aiplatform/apiv1beta1/aiplatformpb;aiplatformpb";
option java_multiple_files = true;
option java_outer_classname = "AcceleratorTypeProto";
option java_package = "com.google.cloud.aiplatform.v1beta1";
option php_namespace = "Google\\Cloud\\AIPlatform\\V1beta1";
option ruby_package = "Google::Cloud::AIPlatform::V1beta1";

// Represents a hardware accelerator type.
enum AcceleratorType {
  // Unspecified accelerator type, which means no accelerator.
  ACCELERATOR_TYPE_UNSPECIFIED = 0;

  // Deprecated: Nvidia Tesla K80 GPU has reached end of support,
  // see https://cloud.google.com/compute/docs/eol/k80-eol.
  NVIDIA_TESLA_K80 = 1 [deprecated = true];

  // Nvidia Tesla P100 GPU.
  NVIDIA_TESLA_P100 = 2;

  // Nvidia Tesla V100 GPU.
  NVIDIA_TESLA_V100 = 3;

  // Nvidia Tesla P4 GPU.
  NVIDIA_TESLA_P4 = 4;

  // Nvidia Tesla T4 GPU.
  NVIDIA_TESLA_T4 = 5;

  // Nvidia Tesla A100 GPU.
  NVIDIA_TESLA_A100 = 8;

  // Nvidia A100 80GB GPU.
  NVIDIA_A100_80GB = 9;

  // Nvidia L4 GPU.
  NVIDIA_L4 = 11;

  // Nvidia H100 80Gb GPU.
  NVIDIA_H100_80GB = 13;

  // Nvidia H100 Mega 80Gb GPU.
  NVIDIA_H100_MEGA_80GB = 14;

  // TPU v2.
  TPU_V2 = 6;

  // TPU v3.
  TPU_V3 = 7;

  // TPU v4.
  TPU_V4_POD = 10;

  // TPU v5.
  TPU_V5_LITEPOD = 12;
}
