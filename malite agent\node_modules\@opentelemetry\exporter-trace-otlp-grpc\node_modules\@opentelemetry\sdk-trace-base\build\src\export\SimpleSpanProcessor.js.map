{"version": 3, "file": "SimpleSpanProcessor.js", "sourceRoot": "", "sources": ["../../../src/export/SimpleSpanProcessor.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,4CAAyD;AACzD,8CAM6B;AAO7B;;;;;;;GAOG;AACH,MAAa,mBAAmB;IAI9B,YAA6B,SAAuB;QAAvB,cAAS,GAAT,SAAS,CAAc;QAClD,IAAI,CAAC,aAAa,GAAG,IAAI,qBAAc,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;QAC9D,IAAI,CAAC,kBAAkB,GAAG,IAAI,GAAG,EAAiB,CAAC;IACrD,CAAC;IAED,KAAK,CAAC,UAAU;QACd,8CAA8C;QAC9C,MAAM,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC;QACvD,IAAI,IAAI,CAAC,SAAS,CAAC,UAAU,EAAE;YAC7B,MAAM,IAAI,CAAC,SAAS,CAAC,UAAU,EAAE,CAAC;SACnC;IACH,CAAC;IAED,OAAO,CAAC,KAAW,EAAE,cAAuB,IAAS,CAAC;IAEtD,KAAK,CAAC,IAAkB;;QACtB,IAAI,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE;YAC/B,OAAO;SACR;QAED,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,UAAU,GAAG,gBAAU,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;YAC9D,OAAO;SACR;QAED,MAAM,QAAQ,GAAG,GAAG,EAAE,CACpB,eAAQ;aACL,OAAO,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,IAAI,CAAC,CAAC;aAC/B,IAAI,CAAC,CAAC,MAAoB,EAAE,EAAE;;YAC7B,IAAI,MAAM,CAAC,IAAI,KAAK,uBAAgB,CAAC,OAAO,EAAE;gBAC5C,IAAA,yBAAkB,EAChB,MAAA,MAAM,CAAC,KAAK,mCACV,IAAI,KAAK,CACP,mDAAmD,MAAM,GAAG,CAC7D,CACJ,CAAC;aACH;QACH,CAAC,CAAC;aACD,KAAK,CAAC,KAAK,CAAC,EAAE;YACb,IAAA,yBAAkB,EAAC,KAAK,CAAC,CAAC;QAC5B,CAAC,CAAC,CAAC;QAEP,sFAAsF;QACtF,IAAI,IAAI,CAAC,QAAQ,CAAC,sBAAsB,EAAE;YACxC,MAAM,aAAa,GAAG,MAAA,MAAC,IAAI,CAAC,QAAqB,EAC9C,sBAAsB,mDACtB,IAAI,CACH,GAAG,EAAE;gBACH,IAAI,aAAa,IAAI,IAAI,EAAE;oBACzB,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;iBAC/C;gBACD,OAAO,QAAQ,EAAE,CAAC;YACpB,CAAC,EACD,GAAG,CAAC,EAAE,CAAC,IAAA,yBAAkB,EAAC,GAAG,CAAC,CAC/B,CAAC;YAEJ,+BAA+B;YAC/B,IAAI,aAAa,IAAI,IAAI,EAAE;gBACzB,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;aAC5C;SACF;aAAM;YACL,KAAK,QAAQ,EAAE,CAAC;SACjB;IACH,CAAC;IAED,QAAQ;QACN,OAAO,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC;IACnC,CAAC;IAEO,SAAS;QACf,OAAO,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC;IACnC,CAAC;CACF;AA3ED,kDA2EC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Context, TraceFlags } from '@opentelemetry/api';\nimport {\n  internal,\n  ExportResultCode,\n  globalErrorHandler,\n  BindOnceFuture,\n  ExportResult,\n} from '@opentelemetry/core';\nimport { Span } from '../Span';\nimport { SpanProcessor } from '../SpanProcessor';\nimport { ReadableSpan } from './ReadableSpan';\nimport { SpanExporter } from './SpanExporter';\nimport { Resource } from '@opentelemetry/resources';\n\n/**\n * An implementation of the {@link SpanProcessor} that converts the {@link Span}\n * to {@link ReadableSpan} and passes it to the configured exporter.\n *\n * Only spans that are sampled are converted.\n *\n * NOTE: This {@link SpanProcessor} exports every ended span individually instead of batching spans together, which causes significant performance overhead with most exporters. For production use, please consider using the {@link BatchSpanProcessor} instead.\n */\nexport class SimpleSpanProcessor implements SpanProcessor {\n  private _shutdownOnce: BindOnceFuture<void>;\n  private _unresolvedExports: Set<Promise<void>>;\n\n  constructor(private readonly _exporter: SpanExporter) {\n    this._shutdownOnce = new BindOnceFuture(this._shutdown, this);\n    this._unresolvedExports = new Set<Promise<void>>();\n  }\n\n  async forceFlush(): Promise<void> {\n    // await unresolved resources before resolving\n    await Promise.all(Array.from(this._unresolvedExports));\n    if (this._exporter.forceFlush) {\n      await this._exporter.forceFlush();\n    }\n  }\n\n  onStart(_span: Span, _parentContext: Context): void {}\n\n  onEnd(span: ReadableSpan): void {\n    if (this._shutdownOnce.isCalled) {\n      return;\n    }\n\n    if ((span.spanContext().traceFlags & TraceFlags.SAMPLED) === 0) {\n      return;\n    }\n\n    const doExport = () =>\n      internal\n        ._export(this._exporter, [span])\n        .then((result: ExportResult) => {\n          if (result.code !== ExportResultCode.SUCCESS) {\n            globalErrorHandler(\n              result.error ??\n                new Error(\n                  `SimpleSpanProcessor: span export failed (status ${result})`\n                )\n            );\n          }\n        })\n        .catch(error => {\n          globalErrorHandler(error);\n        });\n\n    // Avoid scheduling a promise to make the behavior more predictable and easier to test\n    if (span.resource.asyncAttributesPending) {\n      const exportPromise = (span.resource as Resource)\n        .waitForAsyncAttributes?.()\n        .then(\n          () => {\n            if (exportPromise != null) {\n              this._unresolvedExports.delete(exportPromise);\n            }\n            return doExport();\n          },\n          err => globalErrorHandler(err)\n        );\n\n      // store the unresolved exports\n      if (exportPromise != null) {\n        this._unresolvedExports.add(exportPromise);\n      }\n    } else {\n      void doExport();\n    }\n  }\n\n  shutdown(): Promise<void> {\n    return this._shutdownOnce.call();\n  }\n\n  private _shutdown(): Promise<void> {\n    return this._exporter.shutdown();\n  }\n}\n"]}