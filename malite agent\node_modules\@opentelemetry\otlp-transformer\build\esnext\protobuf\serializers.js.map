{"version": 3, "file": "serializers.js", "sourceRoot": "", "sources": ["../../../src/protobuf/serializers.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAEH,OAAO,KAAK,IAAI,MAAM,mBAAmB,CAAC;AAgB1C,OAAO,EAAE,+BAA+B,EAAE,MAAM,UAAU,CAAC;AAC3D,OAAO,EAAE,iCAAiC,EAAE,MAAM,YAAY,CAAC;AAE/D,OAAO,EAAE,8BAA8B,EAAE,MAAM,SAAS,CAAC;AAGzD,MAAM,gBAAgB,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE;KAChE,yBAAmE,CAAC;AAEvE,MAAM,eAAe,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE;KAC/D,wBAAiE,CAAC;AAErE,MAAM,mBAAmB,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,SAAS,CAAC,OAAO,CAAC,EAAE;KACtE,4BAAyE,CAAC;AAE7E,MAAM,kBAAkB,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,SAAS,CAAC,OAAO,CAAC,EAAE;KACrE,2BAAuE,CAAC;AAE3E,MAAM,iBAAiB,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE;KAClE,0BAAqE,CAAC;AAEzE,MAAM,gBAAgB,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE;KACjE,yBAAmE,CAAC;AAEvE,MAAM,CAAC,MAAM,sBAAsB,GAG/B;IACF,gBAAgB,EAAE,CAAC,GAAwB,EAAE,EAAE;QAC7C,MAAM,OAAO,GAAG,8BAA8B,CAAC,GAAG,CAAC,CAAC;QACpD,OAAO,eAAe,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,CAAC;IAClD,CAAC;IACD,mBAAmB,EAAE,CAAC,GAAe,EAAE,EAAE;QACvC,OAAO,gBAAgB,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;IACtC,CAAC;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,yBAAyB,GAGlC;IACF,gBAAgB,EAAE,CAAC,GAAsB,EAAE,EAAE;QAC3C,MAAM,OAAO,GAAG,iCAAiC,CAAC,GAAG,CAAC,CAAC;QACvD,OAAO,kBAAkB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,CAAC;IACrD,CAAC;IACD,mBAAmB,EAAE,CAAC,GAAe,EAAE,EAAE;QACvC,OAAO,mBAAmB,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;IACzC,CAAC;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,uBAAuB,GAGhC;IACF,gBAAgB,EAAE,CAAC,GAAmB,EAAE,EAAE;QACxC,MAAM,OAAO,GAAG,+BAA+B,CAAC,GAAG,CAAC,CAAC;QACrD,OAAO,gBAAgB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,CAAC;IACnD,CAAC;IACD,mBAAmB,EAAE,CAAC,GAAe,EAAE,EAAE;QACvC,OAAO,iBAAiB,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;IACvC,CAAC;CACF,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport * as root from '../generated/root';\nimport { ISerializer } from '../common/i-serializer';\nimport {\n  IExportMetricsServiceRequest,\n  IExportMetricsServiceResponse,\n} from '../metrics/types';\nimport { ExportType } from './protobuf-export-type';\nimport {\n  IExportTraceServiceRequest,\n  IExportTraceServiceResponse,\n} from '../trace/types';\nimport {\n  IExportLogsServiceRequest,\n  IExportLogsServiceResponse,\n} from '../logs/types';\nimport { ReadableSpan } from '@opentelemetry/sdk-trace-base';\nimport { createExportTraceServiceRequest } from '../trace';\nimport { createExportMetricsServiceRequest } from '../metrics';\nimport { ResourceMetrics } from '@opentelemetry/sdk-metrics';\nimport { createExportLogsServiceRequest } from '../logs';\nimport { ReadableLogRecord } from '@opentelemetry/sdk-logs';\n\nconst logsResponseType = root.opentelemetry.proto.collector.logs.v1\n  .ExportLogsServiceResponse as ExportType<IExportLogsServiceResponse>;\n\nconst logsRequestType = root.opentelemetry.proto.collector.logs.v1\n  .ExportLogsServiceRequest as ExportType<IExportLogsServiceRequest>;\n\nconst metricsResponseType = root.opentelemetry.proto.collector.metrics.v1\n  .ExportMetricsServiceResponse as ExportType<IExportMetricsServiceResponse>;\n\nconst metricsRequestType = root.opentelemetry.proto.collector.metrics.v1\n  .ExportMetricsServiceRequest as ExportType<IExportMetricsServiceRequest>;\n\nconst traceResponseType = root.opentelemetry.proto.collector.trace.v1\n  .ExportTraceServiceResponse as ExportType<IExportTraceServiceResponse>;\n\nconst traceRequestType = root.opentelemetry.proto.collector.trace.v1\n  .ExportTraceServiceRequest as ExportType<IExportTraceServiceRequest>;\n\nexport const ProtobufLogsSerializer: ISerializer<\n  ReadableLogRecord[],\n  IExportLogsServiceResponse\n> = {\n  serializeRequest: (arg: ReadableLogRecord[]) => {\n    const request = createExportLogsServiceRequest(arg);\n    return logsRequestType.encode(request).finish();\n  },\n  deserializeResponse: (arg: Uint8Array) => {\n    return logsResponseType.decode(arg);\n  },\n};\n\nexport const ProtobufMetricsSerializer: ISerializer<\n  ResourceMetrics[],\n  IExportMetricsServiceResponse\n> = {\n  serializeRequest: (arg: ResourceMetrics[]) => {\n    const request = createExportMetricsServiceRequest(arg);\n    return metricsRequestType.encode(request).finish();\n  },\n  deserializeResponse: (arg: Uint8Array) => {\n    return metricsResponseType.decode(arg);\n  },\n};\n\nexport const ProtobufTraceSerializer: ISerializer<\n  ReadableSpan[],\n  IExportTraceServiceResponse\n> = {\n  serializeRequest: (arg: ReadableSpan[]) => {\n    const request = createExportTraceServiceRequest(arg);\n    return traceRequestType.encode(request).finish();\n  },\n  deserializeResponse: (arg: Uint8Array) => {\n    return traceResponseType.decode(arg);\n  },\n};\n"]}