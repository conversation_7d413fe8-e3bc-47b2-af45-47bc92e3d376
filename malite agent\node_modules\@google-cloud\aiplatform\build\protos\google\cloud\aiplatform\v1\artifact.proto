// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.aiplatform.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/struct.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.AIPlatform.V1";
option go_package = "cloud.google.com/go/aiplatform/apiv1/aiplatformpb;aiplatformpb";
option java_multiple_files = true;
option java_outer_classname = "ArtifactProto";
option java_package = "com.google.cloud.aiplatform.v1";
option php_namespace = "Google\\Cloud\\AIPlatform\\V1";
option ruby_package = "Google::Cloud::AIPlatform::V1";

// Instance of a general artifact.
message Artifact {
  option (google.api.resource) = {
    type: "aiplatform.googleapis.com/Artifact"
    pattern: "projects/{project}/locations/{location}/metadataStores/{metadata_store}/artifacts/{artifact}"
  };

  // Describes the state of the Artifact.
  enum State {
    // Unspecified state for the Artifact.
    STATE_UNSPECIFIED = 0;

    // A state used by systems like Vertex AI Pipelines to indicate that the
    // underlying data item represented by this Artifact is being created.
    PENDING = 1;

    // A state indicating that the Artifact should exist, unless something
    // external to the system deletes it.
    LIVE = 2;
  }

  // Output only. The resource name of the Artifact.
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // User provided display name of the Artifact.
  // May be up to 128 Unicode characters.
  string display_name = 2;

  // The uniform resource identifier of the artifact file.
  // May be empty if there is no actual artifact file.
  string uri = 6;

  // An eTag used to perform consistent read-modify-write updates. If not set, a
  // blind "overwrite" update happens.
  string etag = 9;

  // The labels with user-defined metadata to organize your Artifacts.
  //
  // Label keys and values can be no longer than 64 characters
  // (Unicode codepoints), can only contain lowercase letters, numeric
  // characters, underscores and dashes. International characters are allowed.
  // No more than 64 user labels can be associated with one Artifact (System
  // labels are excluded).
  map<string, string> labels = 10;

  // Output only. Timestamp when this Artifact was created.
  google.protobuf.Timestamp create_time = 11
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Timestamp when this Artifact was last updated.
  google.protobuf.Timestamp update_time = 12
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // The state of this Artifact. This is a property of the Artifact, and does
  // not imply or capture any ongoing process. This property is managed by
  // clients (such as Vertex AI Pipelines), and the system does not prescribe
  // or check the validity of state transitions.
  State state = 13;

  // The title of the schema describing the metadata.
  //
  // Schema title and version is expected to be registered in earlier Create
  // Schema calls. And both are used together as unique identifiers to identify
  // schemas within the local metadata store.
  string schema_title = 14;

  // The version of the schema in schema_name to use.
  //
  // Schema title and version is expected to be registered in earlier Create
  // Schema calls. And both are used together as unique identifiers to identify
  // schemas within the local metadata store.
  string schema_version = 15;

  // Properties of the Artifact.
  // Top level metadata keys' heading and trailing spaces will be trimmed.
  // The size of this field should not exceed 200KB.
  google.protobuf.Struct metadata = 16;

  // Description of the Artifact
  string description = 17;
}
