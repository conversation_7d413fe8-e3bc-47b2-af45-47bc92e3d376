{"version": 3, "file": "environment.js", "sourceRoot": "", "sources": ["../../../../src/platform/node/environment.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,yDAKiC;AAEjC;;GAEG;AACH,SAAgB,MAAM;IACpB,MAAM,UAAU,GAAG,IAAA,8BAAgB,EAAC,OAAO,CAAC,GAAsB,CAAC,CAAC;IACpE,OAAO,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,iCAAmB,EAAE,UAAU,CAAC,CAAC;AAC5D,CAAC;AAHD,wBAGC;AAED,SAAgB,qBAAqB;IACnC,OAAO,IAAA,8BAAgB,EAAC,OAAO,CAAC,GAAsB,CAAC,CAAC;AAC1D,CAAC;AAFD,sDAEC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  DEFAULT_ENVIRONMENT,\n  ENVIRONMENT,\n  RAW_ENVIRONMENT,\n  parseEnvironment,\n} from '../../utils/environment';\n\n/**\n * Gets the environment variables\n */\nexport function getEnv(): Required<ENVIRONMENT> {\n  const processEnv = parseEnvironment(process.env as RAW_ENVIRONMENT);\n  return Object.assign({}, DEFAULT_ENVIRONMENT, processEnv);\n}\n\nexport function getEnvWithoutDefaults(): ENVIRONMENT {\n  return parseEnvironment(process.env as RAW_ENVIRONMENT);\n}\n"]}