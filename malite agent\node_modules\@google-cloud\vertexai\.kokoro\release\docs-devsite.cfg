# service account used to publish up-to-date docs.
before_action {
  fetch_keystore {
    keystore_resource {
      keystore_config_id: 73713
      keyname: "docuploader_service_account"
    }
  }
}

# doc publications use a Python image.
env_vars: {
    key: "TRAMPOLINE_IMAGE"
    value: "us-central1-docker.pkg.dev/cloud-sdk-release-custom-pool/release-images/node18"
}

# Download trampoline resources.
gfile_resources: "/bigstore/cloud-devrel-kokoro-resources/trampoline"

# Use the trampoline script to run in docker.
build_file: "nodejs-vertexai/.kokoro/trampoline_v2.sh"

env_vars: {
    key: "TRAMPOLINE_BUILD_FILE"
    value: "github/nodejs-vertexai/.kokoro/release/docs-devsite.sh"
}