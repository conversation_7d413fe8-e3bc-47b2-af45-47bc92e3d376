import { Document, DocumentDataSchema } from "genkit";
const getFirestoreDocumentRetriever = (db, collectionName) => {
  const firestoreRetriever = async (neighbors) => {
    const docs = [];
    for (const neighbor of neighbors) {
      const docRef = db.collection(collectionName).doc(neighbor.datapoint?.datapointId);
      const docSnapshot = await docRef.get();
      if (docSnapshot.exists) {
        const docData = { ...docSnapshot.data() };
        docData.metadata = { ...docData.metadata, ...neighbor };
        const parsedDocData = DocumentDataSchema.safeParse(docData);
        if (parsedDocData.success) {
          docs.push(new Document(parsedDocData.data));
        }
      }
    }
    return docs;
  };
  return firestoreRetriever;
};
const getFirestoreDocumentIndexer = (db, collectionName) => {
  const firestoreIndexer = async (docs) => {
    const batch = db.batch();
    const ids = [];
    docs.forEach((doc) => {
      const docRef = db.collection(collectionName).doc();
      batch.set(docRef, {
        content: doc.content,
        metadata: doc.metadata || null
      });
      ids.push(docRef.id);
    });
    await batch.commit();
    return ids;
  };
  return firestoreIndexer;
};
export {
  getFirestoreDocumentIndexer,
  getFirestoreDocumentRetriever
};
//# sourceMappingURL=firestore.mjs.map