# Changelog

[npm history][1]

[1]: https://www.npmjs.com/package/@google-cloud/projectify?activeTab=versions
## [4.0.0](https://github.com/googleapis/nodejs-projectify/compare/v3.0.0...v4.0.0) (2023-08-09)


### ⚠ BREAKING CHANGES

* upgrade to Node 14 ([#318](https://github.com/googleapis/nodejs-projectify/issues/318))

### Bug Fixes

* Remove pip install statements ([#1546](https://github.com/googleapis/nodejs-projectify/issues/1546)) ([#304](https://github.com/googleapis/nodejs-projectify/issues/304)) ([94cfff6](https://github.com/googleapis/nodejs-projectify/commit/94cfff665b7c6b8916b5c59e1c7a3cca7ff29303))


### Miscellaneous Chores

* Upgrade to Node 14 ([#318](https://github.com/googleapis/nodejs-projectify/issues/318)) ([6e9da4d](https://github.com/googleapis/nodejs-projectify/commit/6e9da4db77fab7ed6876e755a72156960b376d57))

## [3.0.0](https://github.com/googleapis/nodejs-projectify/compare/v2.1.1...v3.0.0) (2022-05-20)


### ⚠ BREAKING CHANGES

* update library to use Node 12 (#299)

### Build System

* update library to use Node 12 ([#299](https://github.com/googleapis/nodejs-projectify/issues/299)) ([83b63ca](https://github.com/googleapis/nodejs-projectify/commit/83b63ca8cb89086a8535a9fc8abd39e95f0cecd4))

### [2.1.1](https://www.github.com/googleapis/nodejs-projectify/compare/v2.1.0...v2.1.1) (2021-09-09)


### Bug Fixes

* **build:** switch primary branch to main ([#267](https://www.github.com/googleapis/nodejs-projectify/issues/267)) ([9e8d6e4](https://www.github.com/googleapis/nodejs-projectify/commit/9e8d6e48c080806b42164d7be0bd11197996f245))

## [2.1.0](https://www.github.com/googleapis/nodejs-projectify/compare/v2.0.1...v2.1.0) (2021-06-10)


### Features

* add `gcf-owl-bot[bot]` to `ignoreAuthors` ([#245](https://www.github.com/googleapis/nodejs-projectify/issues/245)) ([30f0499](https://www.github.com/googleapis/nodejs-projectify/commit/30f0499ade5f140774c3aa672b44fd3538e72309))

### [2.0.1](https://www.github.com/googleapis/nodejs-projectify/compare/v2.0.0...v2.0.1) (2020-07-06)


### Bug Fixes

* update node issue template ([#197](https://www.github.com/googleapis/nodejs-projectify/issues/197)) ([3406f2a](https://www.github.com/googleapis/nodejs-projectify/commit/3406f2aa431ed04541585b63c330c04270c602aa))

## [2.0.0](https://www.github.com/googleapis/nodejs-projectify/compare/v1.0.4...v2.0.0) (2020-03-24)


### ⚠ BREAKING CHANGES

* typescript@3.7 introduced some breaking changes
* drop Node 8 from engines field (#172)

### Features

* drop Node 8 from engines field ([#172](https://www.github.com/googleapis/nodejs-projectify/issues/172)) ([3eac424](https://www.github.com/googleapis/nodejs-projectify/commit/3eac424bfb1ee47144a77888dc68db687988945e))


### Build System

* update to latest version of gts/typescript ([#171](https://www.github.com/googleapis/nodejs-projectify/issues/171)) ([30f90cc](https://www.github.com/googleapis/nodejs-projectify/commit/30f90cc172da6ed9394da91869556bf5eef42434))

### [1.0.4](https://www.github.com/googleapis/nodejs-projectify/compare/v1.0.3...v1.0.4) (2019-12-05)


### Bug Fixes

* **publish:** publication failed to reach npm ([#141](https://www.github.com/googleapis/nodejs-projectify/issues/141)) ([5406ba5](https://www.github.com/googleapis/nodejs-projectify/commit/5406ba5e1d43a228a19072023c1baebce34190af))

### [1.0.3](https://www.github.com/googleapis/nodejs-projectify/compare/v1.0.2...v1.0.3) (2019-12-05)


### Bug Fixes

* **deps:** pin TypeScript below 3.7.0 ([6c95307](https://www.github.com/googleapis/nodejs-projectify/commit/6c953070139a77d30c4ce5b7dee1443874046906))

### [1.0.2](https://www.github.com/googleapis/nodejs-projectify/compare/v1.0.1...v1.0.2) (2019-11-14)


### Bug Fixes

* **docs:** add jsdoc-region-tag plugin ([#135](https://www.github.com/googleapis/nodejs-projectify/issues/135)) ([59301e7](https://www.github.com/googleapis/nodejs-projectify/commit/59301e7cfa855add4894dd9c46870e61fffa7413))

### [1.0.1](https://www.github.com/googleapis/nodejs-projectify/compare/v1.0.0...v1.0.1) (2019-06-26)


### Bug Fixes

* **docs:** link to reference docs section on googleapis.dev ([#119](https://www.github.com/googleapis/nodejs-projectify/issues/119)) ([90a009f](https://www.github.com/googleapis/nodejs-projectify/commit/90a009f))

## [1.0.0](https://www.github.com/googleapis/nodejs-projectify/compare/v0.3.3...v1.0.0) (2019-05-02)


### Build System

* upgrade engines field to >=8.10.0 ([#103](https://www.github.com/googleapis/nodejs-projectify/issues/103)) ([0149650](https://www.github.com/googleapis/nodejs-projectify/commit/0149650))


### BREAKING CHANGES

* upgrade engines field to >=8.10.0 (#103)

## v0.3.3

03-12-2019 12:27 PDT

This patch release contains a few updates to the docs.  That's all!

### Documentation
- docs: update links in contrib guide ([#86](https://github.com/googleapis/nodejs-projectify/pull/86))
- docs: update contributing path in README ([#82](https://github.com/googleapis/nodejs-projectify/pull/82))
- docs: move CONTRIBUTING.md to root ([#81](https://github.com/googleapis/nodejs-projectify/pull/81))
- docs: add lint/fix example to contributing guide ([#79](https://github.com/googleapis/nodejs-projectify/pull/79))

### Internal / Testing Changes
- build: Add docuploader credentials to node publish jobs ([#90](https://github.com/googleapis/nodejs-projectify/pull/90))
- build: use node10 to run samples-test, system-test etc ([#89](https://github.com/googleapis/nodejs-projectify/pull/89))
- build: update release configuration
- chore(deps): update dependency mocha to v6
- build: use linkinator for docs test ([#85](https://github.com/googleapis/nodejs-projectify/pull/85))
- build: create docs test npm scripts ([#84](https://github.com/googleapis/nodejs-projectify/pull/84))
- build: test using @grpc/grpc-js in CI ([#83](https://github.com/googleapis/nodejs-projectify/pull/83))
- build: ignore googleapis.com in doc link check ([#78](https://github.com/googleapis/nodejs-projectify/pull/78))
- build: check for 404s in the docs ([#77](https://github.com/googleapis/nodejs-projectify/pull/77))
- chore(build): inject yoshi automation key ([#75](https://github.com/googleapis/nodejs-projectify/pull/75))
- chore: update nyc and eslint configs ([#74](https://github.com/googleapis/nodejs-projectify/pull/74))
- chore: fix publish.sh permission +x ([#72](https://github.com/googleapis/nodejs-projectify/pull/72))
- fix(build): fix Kokoro release script ([#71](https://github.com/googleapis/nodejs-projectify/pull/71))
- build: add Kokoro configs for autorelease ([#70](https://github.com/googleapis/nodejs-projectify/pull/70))
- chore: always nyc report before calling codecov ([#67](https://github.com/googleapis/nodejs-projectify/pull/67))
- chore: nyc ignore build/test by default ([#66](https://github.com/googleapis/nodejs-projectify/pull/66))
- chore(build): update prettier config ([#64](https://github.com/googleapis/nodejs-projectify/pull/64))
- chore: update license file ([#63](https://github.com/googleapis/nodejs-projectify/pull/63))
- fix(build): fix system key decryption ([#59](https://github.com/googleapis/nodejs-projectify/pull/59))
- chore: add synth.metadata

## v0.3.2

### Bug fixes
- fix: do not replace projectId on stream objects ([#53](https://github.com/googleapis/nodejs-projectify/pull/53))

### Dependencies
- chore(deps): update dependency gts to ^0.9.0 ([#52](https://github.com/googleapis/nodejs-projectify/pull/52))

### Internal / Testing Changes
- chore: update eslintignore config ([#51](https://github.com/googleapis/nodejs-projectify/pull/51))
- chore: use latest npm on Windows ([#50](https://github.com/googleapis/nodejs-projectify/pull/50))
- chore: update CircleCI config ([#49](https://github.com/googleapis/nodejs-projectify/pull/49))
- chore: include build in eslintignore ([#46](https://github.com/googleapis/nodejs-projectify/pull/46))

## v0.3.1

### Implementation Changes
- fix: replaceProjectId should not fail when passed a Buffer ([#43](https://github.com/googleapis/nodejs-projectify/pull/43))

### Dependencies
- chore(deps): update dependency nyc to v13 ([#13](https://github.com/googleapis/nodejs-projectify/pull/13))
- chore(deps): lock file maintenance ([#11](https://github.com/googleapis/nodejs-projectify/pull/11))
- chore(deps): lock file maintenance ([#8](https://github.com/googleapis/nodejs-projectify/pull/8))
- chore(deps): update dependency typescript to v3 ([#7](https://github.com/googleapis/nodejs-projectify/pull/7))
- chore(deps): update dependency gts to ^0.8.0 ([#2](https://github.com/googleapis/nodejs-projectify/pull/2))
- chore(deps): lock file maintenance ([#4](https://github.com/googleapis/nodejs-projectify/pull/4))
- chore(deps): lock file maintenance ([#3](https://github.com/googleapis/nodejs-projectify/pull/3))

### Internal / Testing Changes
- chore: update issue templates ([#40](https://github.com/googleapis/nodejs-projectify/pull/40))
- chore: remove old issue template ([#38](https://github.com/googleapis/nodejs-projectify/pull/38))
- build: run tests on node11 ([#37](https://github.com/googleapis/nodejs-projectify/pull/37))
- chores(build): run codecov on continuous builds ([#34](https://github.com/googleapis/nodejs-projectify/pull/34))
- chores(build): do not collect sponge.xml from windows builds ([#35](https://github.com/googleapis/nodejs-projectify/pull/35))
- chore: update new issue template ([#33](https://github.com/googleapis/nodejs-projectify/pull/33))
- build: fix codecov uploading on Kokoro ([#30](https://github.com/googleapis/nodejs-projectify/pull/30))
- Update kokoro config ([#28](https://github.com/googleapis/nodejs-projectify/pull/28))
- Update CI config ([#26](https://github.com/googleapis/nodejs-projectify/pull/26))
- Don't publish sourcemaps ([#24](https://github.com/googleapis/nodejs-projectify/pull/24))
- build: prevent system/sample-test from leaking credentials
- Update kokoro config ([#22](https://github.com/googleapis/nodejs-projectify/pull/22))
- test: remove appveyor config ([#21](https://github.com/googleapis/nodejs-projectify/pull/21))
- Update CI config ([#20](https://github.com/googleapis/nodejs-projectify/pull/20))
- Enable prefer-const in the eslint config ([#19](https://github.com/googleapis/nodejs-projectify/pull/19))
- Enable no-var in eslint ([#18](https://github.com/googleapis/nodejs-projectify/pull/18))
- Update CI config ([#17](https://github.com/googleapis/nodejs-projectify/pull/17))
- Add synth and update CI config ([#15](https://github.com/googleapis/nodejs-projectify/pull/15))
- chore: ignore package-lock.json ([#12](https://github.com/googleapis/nodejs-projectify/pull/12))
- chore: update renovate config ([#10](https://github.com/googleapis/nodejs-projectify/pull/10))
- remove that whitespace ([#9](https://github.com/googleapis/nodejs-projectify/pull/9))
- chore: assert.deelEqual => assert.deepStrictEqual ([#6](https://github.com/googleapis/nodejs-projectify/pull/6))
- chore: move mocha options to mocha.opts ([#5](https://github.com/googleapis/nodejs-projectify/pull/5))
