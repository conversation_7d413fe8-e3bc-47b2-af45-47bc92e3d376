import { VertexAI } from '@google-cloud/vertexai';
import { GenerateRequest } from 'genkit/model';
import { GoogleAuth } from 'google-auth-library';
import { P as PluginOptions, G as GeminiConfigSchema } from '../types-Bc0LKM8D.mjs';
import 'genkit';

/**
 * Copyright 2024 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

interface DerivedParams {
    location: string;
    projectId: string;
    vertexClientFactory: (request: GenerateRequest<typeof GeminiConfigSchema>) => VertexAI;
    authClient: GoogleAuth;
}
/** @hidden */
declare function __setFakeDerivedParams(params: any): void;
declare function getDerivedParams(options?: PluginOptions): Promise<DerivedParams>;

export { PluginOptions, __setFakeDerivedParams, getDerivedParams };
