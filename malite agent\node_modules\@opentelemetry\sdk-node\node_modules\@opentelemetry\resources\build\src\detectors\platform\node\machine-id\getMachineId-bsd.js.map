{"version": 3, "file": "getMachineId-bsd.js", "sourceRoot": "", "sources": ["../../../../../../src/detectors/platform/node/machine-id/getMachineId-bsd.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,2BAAoC;AACpC,2CAAwC;AACxC,4CAA0C;AAEnC,KAAK,UAAU,YAAY;IAChC,IAAI;QACF,MAAM,MAAM,GAAG,MAAM,aAAE,CAAC,QAAQ,CAAC,aAAa,EAAE,EAAE,QAAQ,EAAE,MAAM,EAAE,CAAC,CAAC;QACtE,OAAO,MAAM,CAAC,IAAI,EAAE,CAAC;KACtB;IAAC,OAAO,CAAC,EAAE;QACV,UAAI,CAAC,KAAK,CAAC,6BAA6B,CAAC,EAAE,CAAC,CAAC;KAC9C;IAED,IAAI;QACF,MAAM,MAAM,GAAG,MAAM,IAAA,qBAAS,EAAC,4BAA4B,CAAC,CAAC;QAC7D,OAAO,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;KAC7B;IAAC,OAAO,CAAC,EAAE;QACV,UAAI,CAAC,KAAK,CAAC,6BAA6B,CAAC,EAAE,CAAC,CAAC;KAC9C;IAED,OAAO,EAAE,CAAC;AACZ,CAAC;AAhBD,oCAgBC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { promises as fs } from 'fs';\nimport { execAsync } from './execAsync';\nimport { diag } from '@opentelemetry/api';\n\nexport async function getMachineId(): Promise<string> {\n  try {\n    const result = await fs.readFile('/etc/hostid', { encoding: 'utf8' });\n    return result.trim();\n  } catch (e) {\n    diag.debug(`error reading machine id: ${e}`);\n  }\n\n  try {\n    const result = await execAsync('kenv -q smbios.system.uuid');\n    return result.stdout.trim();\n  } catch (e) {\n    diag.debug(`error reading machine id: ${e}`);\n  }\n\n  return '';\n}\n"]}