import * as _genkit_ai_ai_reranker from '@genkit-ai/ai/reranker';
import * as zod from 'zod';
import { Genkit } from 'genkit';
import { VertexRerankOptions } from './types.js';
import 'google-auth-library';
import '../types-Bc0LKM8D.js';
import '@google-cloud/vertexai';
import 'genkit/model';

/**
 * Creates Vertex AI rerankers.
 *
 * This function creates and registers rerankers for the specified models.
 *
 * @param {VertexRerankOptions<EmbedderCustomOptions>} options - The parameters for creating the rerankers.
 * @returns {Promise<void>}
 */
declare function vertexAiRerankers(ai: Genkit, options: VertexRerankOptions): Promise<void>;
/**
 * Creates a reference to a Vertex AI reranker.
 *
 * @param {Object} params - The parameters for the reranker reference.
 * @param {string} [params.displayName] - An optional display name for the reranker.
 * @returns {Object} - The reranker reference object.
 */
declare const vertexAiRerankerRef: (params: {
    rerankerName: string;
    displayName?: string;
}) => _genkit_ai_ai_reranker.RerankerReference<zod.ZodOptional<zod.ZodObject<{
    k: zod.ZodOptional<zod.ZodNumber>;
    model: zod.ZodOptional<zod.ZodString>;
    location: zod.ZodOptional<zod.ZodString>;
}, "strip", zod.ZodTypeAny, {
    model?: string | undefined;
    location?: string | undefined;
    k?: number | undefined;
}, {
    model?: string | undefined;
    location?: string | undefined;
    k?: number | undefined;
}>>>;

export { vertexAiRerankerRef, vertexAiRerankers };
