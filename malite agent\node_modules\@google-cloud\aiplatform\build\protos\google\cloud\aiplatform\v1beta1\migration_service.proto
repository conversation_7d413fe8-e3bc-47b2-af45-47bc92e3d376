// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.aiplatform.v1beta1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/aiplatform/v1beta1/migratable_resource.proto";
import "google/cloud/aiplatform/v1beta1/operation.proto";
import "google/longrunning/operations.proto";
import "google/rpc/status.proto";

option csharp_namespace = "Google.Cloud.AIPlatform.V1Beta1";
option go_package = "cloud.google.com/go/aiplatform/apiv1beta1/aiplatformpb;aiplatformpb";
option java_multiple_files = true;
option java_outer_classname = "MigrationServiceProto";
option java_package = "com.google.cloud.aiplatform.v1beta1";
option php_namespace = "Google\\Cloud\\AIPlatform\\V1beta1";
option ruby_package = "Google::Cloud::AIPlatform::V1beta1";

// A service that migrates resources from automl.googleapis.com,
// datalabeling.googleapis.com and ml.googleapis.com to Vertex AI.
service MigrationService {
  option (google.api.default_host) = "aiplatform.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Searches all of the resources in automl.googleapis.com,
  // datalabeling.googleapis.com and ml.googleapis.com that can be migrated to
  // Vertex AI's given location.
  rpc SearchMigratableResources(SearchMigratableResourcesRequest)
      returns (SearchMigratableResourcesResponse) {
    option (google.api.http) = {
      post: "/v1beta1/{parent=projects/*/locations/*}/migratableResources:search"
      body: "*"
    };
    option (google.api.method_signature) = "parent";
  }

  // Batch migrates resources from ml.googleapis.com, automl.googleapis.com,
  // and datalabeling.googleapis.com to Vertex AI.
  rpc BatchMigrateResources(BatchMigrateResourcesRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1beta1/{parent=projects/*/locations/*}/migratableResources:batchMigrate"
      body: "*"
    };
    option (google.api.method_signature) = "parent,migrate_resource_requests";
    option (google.longrunning.operation_info) = {
      response_type: "BatchMigrateResourcesResponse"
      metadata_type: "BatchMigrateResourcesOperationMetadata"
    };
  }
}

// Request message for
// [MigrationService.SearchMigratableResources][google.cloud.aiplatform.v1beta1.MigrationService.SearchMigratableResources].
message SearchMigratableResourcesRequest {
  // Required. The location that the migratable resources should be searched
  // from. It's the Vertex AI location that the resources can be migrated to,
  // not the resources' original location. Format:
  // `projects/{project}/locations/{location}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // The standard page size.
  // The default and maximum value is 100.
  int32 page_size = 2;

  // The standard page token.
  string page_token = 3;

  // A filter for your search. You can use the following types of filters:
  //
  // *   Resource type filters. The following strings filter for a specific type
  //     of
  //     [MigratableResource][google.cloud.aiplatform.v1beta1.MigratableResource]:
  //     *   `ml_engine_model_version:*`
  //     *   `automl_model:*`
  //     *   `automl_dataset:*`
  //     *   `data_labeling_dataset:*`
  // *   "Migrated or not" filters. The following strings filter for resources
  //     that either have or have not already been migrated:
  //     *   `last_migrate_time:*` filters for migrated resources.
  //     *   `NOT last_migrate_time:*` filters for not yet migrated resources.
  string filter = 4;
}

// Response message for
// [MigrationService.SearchMigratableResources][google.cloud.aiplatform.v1beta1.MigrationService.SearchMigratableResources].
message SearchMigratableResourcesResponse {
  // All migratable resources that can be migrated to the
  // location specified in the request.
  repeated MigratableResource migratable_resources = 1;

  // The standard next-page token.
  // The migratable_resources may not fill page_size in
  // SearchMigratableResourcesRequest even when there are subsequent pages.
  string next_page_token = 2;
}

// Request message for
// [MigrationService.BatchMigrateResources][google.cloud.aiplatform.v1beta1.MigrationService.BatchMigrateResources].
message BatchMigrateResourcesRequest {
  // Required. The location of the migrated resource will live in.
  // Format: `projects/{project}/locations/{location}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Required. The request messages specifying the resources to migrate.
  // They must be in the same location as the destination.
  // Up to 50 resources can be migrated in one batch.
  repeated MigrateResourceRequest migrate_resource_requests = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Config of migrating one resource from automl.googleapis.com,
// datalabeling.googleapis.com and ml.googleapis.com to Vertex AI.
message MigrateResourceRequest {
  // Config for migrating version in ml.googleapis.com to Vertex AI's Model.
  message MigrateMlEngineModelVersionConfig {
    // Required. The ml.googleapis.com endpoint that this model version should
    // be migrated from. Example values:
    //
    // * ml.googleapis.com
    //
    // * us-centrall-ml.googleapis.com
    //
    // * europe-west4-ml.googleapis.com
    //
    // * asia-east1-ml.googleapis.com
    string endpoint = 1 [(google.api.field_behavior) = REQUIRED];

    // Required. Full resource name of ml engine model version.
    // Format: `projects/{project}/models/{model}/versions/{version}`.
    string model_version = 2 [
      (google.api.field_behavior) = REQUIRED,
      (google.api.resource_reference) = { type: "ml.googleapis.com/Version" }
    ];

    // Required. Display name of the model in Vertex AI.
    // System will pick a display name if unspecified.
    string model_display_name = 3 [(google.api.field_behavior) = REQUIRED];
  }

  // Config for migrating Model in automl.googleapis.com to Vertex AI's Model.
  message MigrateAutomlModelConfig {
    // Required. Full resource name of automl Model.
    // Format:
    // `projects/{project}/locations/{location}/models/{model}`.
    string model = 1 [
      (google.api.field_behavior) = REQUIRED,
      (google.api.resource_reference) = { type: "automl.googleapis.com/Model" }
    ];

    // Optional. Display name of the model in Vertex AI.
    // System will pick a display name if unspecified.
    string model_display_name = 2 [(google.api.field_behavior) = OPTIONAL];
  }

  // Config for migrating Dataset in automl.googleapis.com to Vertex AI's
  // Dataset.
  message MigrateAutomlDatasetConfig {
    // Required. Full resource name of automl Dataset.
    // Format:
    // `projects/{project}/locations/{location}/datasets/{dataset}`.
    string dataset = 1 [
      (google.api.field_behavior) = REQUIRED,
      (google.api.resource_reference) = {
        type: "automl.googleapis.com/Dataset"
      }
    ];

    // Required. Display name of the Dataset in Vertex AI.
    // System will pick a display name if unspecified.
    string dataset_display_name = 2 [(google.api.field_behavior) = REQUIRED];
  }

  // Config for migrating Dataset in datalabeling.googleapis.com to Vertex
  // AI's Dataset.
  message MigrateDataLabelingDatasetConfig {
    // Config for migrating AnnotatedDataset in datalabeling.googleapis.com to
    // Vertex AI's SavedQuery.
    message MigrateDataLabelingAnnotatedDatasetConfig {
      // Required. Full resource name of data labeling AnnotatedDataset.
      // Format:
      // `projects/{project}/datasets/{dataset}/annotatedDatasets/{annotated_dataset}`.
      string annotated_dataset = 1 [
        (google.api.field_behavior) = REQUIRED,
        (google.api.resource_reference) = {
          type: "datalabeling.googleapis.com/AnnotatedDataset"
        }
      ];
    }

    // Required. Full resource name of data labeling Dataset.
    // Format:
    // `projects/{project}/datasets/{dataset}`.
    string dataset = 1 [
      (google.api.field_behavior) = REQUIRED,
      (google.api.resource_reference) = {
        type: "datalabeling.googleapis.com/Dataset"
      }
    ];

    // Optional. Display name of the Dataset in Vertex AI.
    // System will pick a display name if unspecified.
    string dataset_display_name = 2 [(google.api.field_behavior) = OPTIONAL];

    // Optional. Configs for migrating AnnotatedDataset in
    // datalabeling.googleapis.com to Vertex AI's SavedQuery. The specified
    // AnnotatedDatasets have to belong to the datalabeling Dataset.
    repeated MigrateDataLabelingAnnotatedDatasetConfig
        migrate_data_labeling_annotated_dataset_configs = 3
        [(google.api.field_behavior) = OPTIONAL];
  }

  oneof request {
    // Config for migrating Version in ml.googleapis.com to Vertex AI's Model.
    MigrateMlEngineModelVersionConfig migrate_ml_engine_model_version_config =
        1;

    // Config for migrating Model in automl.googleapis.com to Vertex AI's
    // Model.
    MigrateAutomlModelConfig migrate_automl_model_config = 2;

    // Config for migrating Dataset in automl.googleapis.com to Vertex AI's
    // Dataset.
    MigrateAutomlDatasetConfig migrate_automl_dataset_config = 3;

    // Config for migrating Dataset in datalabeling.googleapis.com to
    // Vertex AI's Dataset.
    MigrateDataLabelingDatasetConfig migrate_data_labeling_dataset_config = 4;
  }
}

// Response message for
// [MigrationService.BatchMigrateResources][google.cloud.aiplatform.v1beta1.MigrationService.BatchMigrateResources].
message BatchMigrateResourcesResponse {
  // Successfully migrated resources.
  repeated MigrateResourceResponse migrate_resource_responses = 1;
}

// Describes a successfully migrated resource.
message MigrateResourceResponse {
  // After migration, the resource name in Vertex AI.
  oneof migrated_resource {
    // Migrated Dataset's resource name.
    string dataset = 1 [(google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/Dataset"
    }];

    // Migrated Model's resource name.
    string model = 2 [(google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/Model"
    }];
  }

  // Before migration, the identifier in ml.googleapis.com,
  // automl.googleapis.com or datalabeling.googleapis.com.
  MigratableResource migratable_resource = 3;
}

// Runtime operation information for
// [MigrationService.BatchMigrateResources][google.cloud.aiplatform.v1beta1.MigrationService.BatchMigrateResources].
message BatchMigrateResourcesOperationMetadata {
  // Represents a partial result in batch migration operation for one
  // [MigrateResourceRequest][google.cloud.aiplatform.v1beta1.MigrateResourceRequest].
  message PartialResult {
    // If the resource's migration is ongoing, none of the result will be set.
    // If the resource's migration is finished, either error or one of the
    // migrated resource name will be filled.
    oneof result {
      // The error result of the migration request in case of failure.
      google.rpc.Status error = 2;

      // Migrated model resource name.
      string model = 3 [(google.api.resource_reference) = {
        type: "aiplatform.googleapis.com/Model"
      }];

      // Migrated dataset resource name.
      string dataset = 4 [(google.api.resource_reference) = {
        type: "aiplatform.googleapis.com/Dataset"
      }];
    }

    // It's the same as the value in
    // [MigrateResourceRequest.migrate_resource_requests][].
    MigrateResourceRequest request = 1;
  }

  // The common part of the operation metadata.
  GenericOperationMetadata generic_metadata = 1;

  // Partial results that reflect the latest migration operation progress.
  repeated PartialResult partial_results = 2;
}
