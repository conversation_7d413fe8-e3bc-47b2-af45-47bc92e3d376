"use strict";
/*!
 * Copyright 2019 Google Inc. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.Model = void 0;
const common_1 = require("@google-cloud/common");
const promisify_1 = require("@google-cloud/promisify");
const arrify = require("arrify");
const extend = require("extend");
/**
 * The model export formats accepted by BigQuery.
 *
 * @type {array}
 * @private
 */
const FORMATS = ['ML_TF_SAVED_MODEL', 'ML_XGBOOST_BOOSTER'];
/**
 * Model objects are returned by methods such as {@link Dataset#model} and
 * {@link Dataset#getModels}.
 *
 * @class
 * @param {Dataset} dataset {@link Dataset} instance.
 * @param {string} id The ID of the model.
 *
 * @example
 * ```
 * const {BigQuery} = require('@google-cloud/bigquery');
 * const bigquery = new BigQuery();
 * const dataset = bigquery.dataset('my-dataset');
 *
 * const model = dataset.model('my-model');
 * ```
 */
class Model extends common_1.ServiceObject {
    constructor(dataset, id) {
        const methods = {
            /**
             * @callback DeleteModelCallback
             * @param {?Error} err Request error, if any.
             * @param {object} apiResponse The full API response.
             */
            /**
             * Delete the model.
             *
             * See {@link https://cloud.google.com/bigquery/docs/reference/rest/v2/models/delete| Models: delete API Documentation}
             *
             * @method Model#delete
             * @param {DeleteModelCallback} [callback] The callback function.
             * @param {?error} callback.err An error returned while making this
             *     request.
             * @param {object} callback.apiResponse The full API response.
             * @returns {Promise}
             *
             * @example
             * ```
             * const {BigQuery} = require('@google-cloud/bigquery');
             * const bigquery = new BigQuery();
             * const dataset = bigquery.dataset('my-dataset');
             * const model = dataset.model('my-model');
             *
             * model.delete((err, apiResponse) => {});
             *
             * ```
             * @example If the callback is omitted we'll return a Promise.
             * ```
             * const [apiResponse] = await model.delete();
             * ```
             * @example If successful, the response body is empty.
             * ```
             * ```
             */
            delete: true,
            /**
             * @callback ModelExistsCallback
             * @param {?Error} err Request error, if any.
             * @param {boolean} exists Indicates if the model exists.
             */
            /**
             * @typedef {array} ModelExistsResponse
             * @property {boolean} 0 Indicates if the model exists.
             */
            /**
             * Check if the model exists.
             *
             * @method Model#exists
             * @param {ModelExistsCallback} [callback] The callback function.
             * @param {?error} callback.err An error returned while making this
             *     request.
             * @param {boolean} callback.exists Whether the model exists or not.
             * @returns {Promise<ModelExistsResponse>}
             *
             * @example
             * ```
             * const {BigQuery} = require('@google-cloud/bigquery');
             * const bigquery = new BigQuery();
             * const dataset = bigquery.dataset('my-dataset');
             * const model = dataset.model('my-model');
             *
             * model.exists((err, exists) => {});
             *
             * ```
             * @example If the callback is omitted we'll return a Promise.
             * ```
             * const [exists] = await model.exists();
             * ```
             */
            exists: true,
            /**
             * @callback GetModelCallback
             * @param {?Error} err Request error, if any.
             * @param {Model} model The model.
             * @param {object} apiResponse The full API response body.
             */
            /**
             * @typedef {array} GetModelResponse
             * @property {Model} 0 The model.
             * @property {object} 1 The full API response body.
             */
            /**
             * Get a model if it exists.
             *
             * See {@link https://cloud.google.com/bigquery/docs/reference/rest/v2/models/get| Models: get API Documentation}
             *
             * @method Model#get:
             * @param {GetModelCallback} [callback] The callback function.
             * @param {?error} callback.err An error returned while making this
             *     request.
             * @param {Model} callback.model The {@link Model}.
             * @param {object} callback.apiResponse The full API response.
             * @returns {Promise<GetModelResponse>}
             *
             * @example
             * ```
             * const {BigQuery} = require('@google-cloud/bigquery');
             * const bigquery = new BigQuery();
             * const dataset = bigquery.dataset('my-dataset');
             * const model = dataset.model('my-model');
             *
             * model.get(err => {
             *   if (!err) {
             *     // `model.metadata` has been populated.
             *   }
             * });
             *
             * ```
             * @example If the callback is omitted we'll return a Promise.
             * ```
             * await model.get();
             * ```
             */
            get: true,
            /**
             * @callback GetModelMetadataCallback
             * @param {?Error} err Request error, if any.
             * @param {object} metadata The model metadata.
             * @param {object} apiResponse The full API response.
             */
            /**
             * @typedef {array} GetModelMetadataResponse
             * @property {object} 0 The model metadata.
             * @property {object} 1 The full API response.
             */
            /**
             * Return the metadata associated with the model.
             *
             * See {@link https://cloud.google.com/bigquery/docs/reference/rest/v2/models/get| Models: get API Documentation}
             *
             * @method Model#getMetadata
             * @param {GetModelMetadataCallback} [callback] The callback function.
             * @param {?error} callback.err An error returned while making this
             *     request.
             * @param {object} callback.metadata The metadata of the model.
             * @param {object} callback.apiResponse The full API response.
             * @returns {Promise<GetModelMetadataResponse>}
             *
             * @example
             * ```
             * const {BigQuery} = require('@google-cloud/bigquery');
             * const bigquery = new BigQuery();
             * const dataset = bigquery.dataset('my-dataset');
             * const model = dataset.model('my-model');
             *
             * model.getMetadata((err, metadata, apiResponse) => {});
             *
             * ```
             * @example If the callback is omitted we'll return a Promise.
             * ```
             * const [metadata, apiResponse] = await model.getMetadata();
             * ```
             */
            getMetadata: true,
            /**
             * @callback SetModelMetadataCallback
             * @param {?Error} err Request error, if any.
             * @param {object} metadata The model metadata.
             * @param {object} apiResponse The full API response.
             */
            /**
             * @typedef {array} SetModelMetadataResponse
             * @property {object} 0 The model metadata.
             * @property {object} 1 The full API response.
             */
            /**
             * See {@link https://cloud.google.com/bigquery/docs/reference/rest/v2/models/patch| Models: patch API Documentation}
             *
             * @method Model#setMetadata
             * @param {object} metadata The metadata key/value object to set.
             * @param {SetModelMetadataCallback} [callback] The callback function.
             * @param {?error} callback.err An error returned while making this
             *     request.
             * @param {object} callback.metadata The updated metadata of the model.
             * @param {object} callback.apiResponse The full API response.
             * @returns {Promise<SetModelMetadataResponse>}
             *
             * @example
             * ```
             * const {BigQuery} = require('@google-cloud/bigquery');
             * const bigquery = new BigQuery();
             * const dataset = bigquery.dataset('my-dataset');
             * const model = dataset.model('my-model');
             *
             * const metadata = {
             *   friendlyName: 'TheBestModelEver'
             * };
             *
             * model.setMetadata(metadata, (err, metadata, apiResponse) => {});
             *
             * ```
             * @example If the callback is omitted we'll return a Promise.
             * ```
             * const [metadata, apiResponse] = await model.setMetadata(metadata);
             * ```
             */
            setMetadata: true,
        };
        super({
            parent: dataset,
            baseUrl: '/models',
            id,
            methods,
        });
        this.dataset = dataset;
        this.bigQuery = dataset.bigQuery;
    }
    createExtractJob(destination, optionsOrCallback, cb) {
        let options = typeof optionsOrCallback === 'object' ? optionsOrCallback : {};
        const callback = typeof optionsOrCallback === 'function' ? optionsOrCallback : cb;
        options = extend(true, options, {
            destinationUris: arrify(destination).map(dest => {
                if (common_1.util.isCustomType(dest, 'storage/file')) {
                    return ('gs://' + dest.bucket.name + '/' + dest.name);
                }
                if (typeof dest === 'string') {
                    return dest;
                }
                throw new Error('Destination must be a string or a File object.');
            }),
        });
        if (options.format) {
            options.format = options.format.toUpperCase();
            if (FORMATS.includes(options.format)) {
                options.destinationFormat = options.format;
                delete options.format;
            }
            else {
                throw new Error('Destination format not recognized: ' + options.format);
            }
        }
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const body = {
            configuration: {
                extract: extend(true, options, {
                    sourceModel: {
                        datasetId: this.dataset.id,
                        projectId: this.dataset.projectId,
                        modelId: this.id,
                    },
                }),
            },
        };
        if (options.jobPrefix) {
            body.jobPrefix = options.jobPrefix;
            delete options.jobPrefix;
        }
        if (options.jobId) {
            body.jobId = options.jobId;
            delete options.jobId;
        }
        this.bigQuery.createJob(body, callback);
    }
    extract(destination, optionsOrCallback, cb) {
        const options = typeof optionsOrCallback === 'object' ? optionsOrCallback : {};
        const callback = typeof optionsOrCallback === 'function' ? optionsOrCallback : cb;
        this.createExtractJob(destination, options, (err, job, resp) => {
            if (err) {
                callback(err, resp);
                return;
            }
            job.on('error', callback).on('complete', metadata => {
                callback(null, metadata);
            });
        });
    }
}
exports.Model = Model;
/*! Developer Documentation
 *
 * All async methods (except for streams) will return a Promise in the event
 * that a callback is omitted.
 */
(0, promisify_1.promisifyAll)(Model);
//# sourceMappingURL=model.js.map