import { GENKIT_CLIENT_HEADER } from "genkit";
function endpoint(options) {
  return `https://${options.location}-aiplatform.googleapis.com/v1/projects/${options.projectId}/locations/${options.location}/publishers/google/models/${options.model}:predict`;
}
function predictModel(auth, { location, projectId }, model) {
  return async (instances, parameters) => {
    const fetch = (await import("node-fetch")).default;
    const accessToken = await auth.getAccessToken();
    const req = {
      instances,
      parameters
    };
    const response = await fetch(
      endpoint({
        projectId,
        location,
        model
      }),
      {
        method: "POST",
        body: JSON.stringify(req),
        headers: {
          Authorization: `Bearer ${accessToken}`,
          "Content-Type": "application/json",
          "X-Goog-Api-Client": GENKIT_CLIENT_HEADER
        }
      }
    );
    if (!response.ok) {
      throw new Error(
        `Error from Vertex AI predict: HTTP ${response.status}: ${await response.text()}`
      );
    }
    return await response.json();
  };
}
export {
  predictModel
};
//# sourceMappingURL=predict.mjs.map