{"version": 3, "file": "types.js", "sourceRoot": "", "sources": ["../../src/types.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ContextManager, TextMapPropagator } from '@opentelemetry/api';\nimport { IResource } from '@opentelemetry/resources';\nimport { IdGenerator } from './IdGenerator';\nimport { Sam<PERSON> } from './Sampler';\n\n/**\n * TracerConfig provides an interface for configuring a Basic Tracer.\n */\nexport interface TracerConfig {\n  /**\n   * Sampler determines if a span should be recorded or should be a NoopSpan.\n   */\n  sampler?: Sam<PERSON>;\n\n  /** General Limits */\n  generalLimits?: GeneralLimits;\n\n  /** Span Limits */\n  spanLimits?: SpanLimits;\n\n  /** Resource associated with trace telemetry  */\n  resource?: IResource;\n\n  /**\n   * Generator of trace and span IDs\n   * The default idGenerator generates random ids\n   */\n  idGenerator?: IdGenerator;\n\n  /**\n   * How long the forceFlush can run before it is cancelled.\n   * The default value is 30000ms\n   */\n  forceFlushTimeoutMillis?: number;\n}\n\n/**\n * Configuration options for registering the API with the SDK.\n * Undefined values may be substituted for defaults, and null\n * values will not be registered.\n */\nexport interface SDKRegistrationConfig {\n  /** Propagator to register as the global propagator */\n  propagator?: TextMapPropagator | null;\n\n  /** Context manager to register as the global context manager */\n  contextManager?: ContextManager | null;\n}\n\n/** Global configuration limits of trace service */\nexport interface GeneralLimits {\n  /** attributeValueLengthLimit is maximum allowed attribute value size */\n  attributeValueLengthLimit?: number;\n  /** attributeCountLimit is number of attributes per trace */\n  attributeCountLimit?: number;\n}\n\n/** Global configuration of trace service */\nexport interface SpanLimits {\n  /** attributeValueLengthLimit is maximum allowed attribute value size */\n  attributeValueLengthLimit?: number;\n  /** attributeCountLimit is number of attributes per span */\n  attributeCountLimit?: number;\n  /** linkCountLimit is number of links per span */\n  linkCountLimit?: number;\n  /** eventCountLimit is number of message events per span */\n  eventCountLimit?: number;\n  /** attributePerEventCountLimit is the maximum number of attributes allowed per span event */\n  attributePerEventCountLimit?: number;\n  /** attributePerLinkCountLimit is the maximum number of attributes allowed per span link */\n  attributePerLinkCountLimit?: number;\n}\n\n/** Interface configuration for a buffer. */\nexport interface BufferConfig {\n  /** The maximum batch size of every export. It must be smaller or equal to\n   * maxQueueSize. The default value is 512. */\n  maxExportBatchSize?: number;\n\n  /** The delay interval in milliseconds between two consecutive exports.\n   *  The default value is 5000ms. */\n  scheduledDelayMillis?: number;\n\n  /** How long the export can run before it is cancelled.\n   * The default value is 30000ms */\n  exportTimeoutMillis?: number;\n\n  /** The maximum queue size. After the size is reached spans are dropped.\n   * The default value is 2048. */\n  maxQueueSize?: number;\n}\n\n/** Interface configuration for BatchSpanProcessor on browser */\nexport interface BatchSpanProcessorBrowserConfig extends BufferConfig {\n  /** Disable flush when a user navigates to a new page, closes the tab or the browser, or,\n   * on mobile, switches to a different app. Auto flush is enabled by default. */\n  disableAutoFlushOnDocumentHide?: boolean;\n}\n"]}