{"version": 3, "file": "hex-to-base64.js", "sourceRoot": "", "sources": ["../../../../src/platform/node/hex-to-base64.ts"], "names": [], "mappings": ";;;AAAA;;;;;;;;;;;;;;GAcG;AACH,8DAAyD;AAEzD,SAAgB,WAAW,CAAC,MAAc;IACxC,OAAO,MAAM,CAAC,IAAI,CAAC,IAAA,2BAAW,EAAC,MAAM,CAAC,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;AAC7D,CAAC;AAFD,kCAEC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { hexToBinary } from '../../common/hex-to-binary';\n\nexport function hexToBase64(hexStr: string): string {\n  return Buffer.from(hexToBinary(hexStr)).toString('base64');\n}\n"]}