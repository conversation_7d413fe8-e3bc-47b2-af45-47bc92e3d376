{"traceId": "9378815b7352b48e83a266c7030171e7", "spans": {"638f9dc6e5aaa10a": {"spanId": "638f9dc6e5aaa10a", "traceId": "9378815b7352b48e83a266c7030171e7", "parentSpanId": "99f87361a7489f86", "startTime": 1748242263293, "endTime": 1748242279387.6736, "attributes": {"genkit:type": "action", "genkit:metadata:subtype": "model", "genkit:name": "googleai/gemini-1.5-flash", "genkit:path": "/{generate,t:util}/{googleai/gemini-1.5-flash,t:action,s:model}", "genkit:input": "{\"messages\":[{\"role\":\"user\",\"content\":[{\"text\":\"مرحباً، قدم نفسك باللغة العربية في جملتين فقط.\"}]}],\"config\":{},\"tools\":[],\"output\":{}}", "genkit:state": "error"}, "displayName": "googleai/gemini-1.5-flash", "links": [], "instrumentationLibrary": {"name": "genkit-tracer", "version": "v1"}, "spanKind": "INTERNAL", "sameProcessAsParentSpan": {"value": true}, "status": {"code": 2, "message": "[GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: fetch failed"}, "timeEvents": {"timeEvent": [{"time": 1748242279386.5913, "annotation": {"attributes": {"exception.type": "Error", "exception.message": "[GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: fetch failed", "exception.stacktrace": "TypeError: fetch failed\n    at node:internal/deps/undici/undici:13502:13\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async makeRequest (C:\\Users\\<USER>\\malite agent\\node_modules\\@google\\generative-ai\\dist\\index.js:397:20)\n    at async generateContent (C:\\Users\\<USER>\\malite agent\\node_modules\\@google\\generative-ai\\dist\\index.js:867:22)\n    at async ChatSession.sendMessage (C:\\Users\\<USER>\\malite agent\\node_modules\\@google\\generative-ai\\dist\\index.js:1210:9)\n    at async callGemini (C:\\Users\\<USER>\\malite agent\\node_modules\\@genkit-ai\\googleai\\lib\\gemini.js:845:26)\n    at async C:\\Users\\<USER>\\malite agent\\node_modules\\@genkit-ai\\googleai\\lib\\gemini.js:891:11\n    at async C:\\Users\\<USER>\\malite agent\\node_modules\\@genkit-ai\\core\\lib\\action.js:210:14\n    at async C:\\Users\\<USER>\\malite agent\\node_modules\\@genkit-ai\\core\\lib\\action.js:131:27\n    at async C:\\Users\\<USER>\\malite agent\\node_modules\\@genkit-ai\\core\\lib\\tracing\\instrumentation.js:65:16"}, "description": "exception"}}]}}, "99f87361a7489f86": {"spanId": "99f87361a7489f86", "traceId": "9378815b7352b48e83a266c7030171e7", "startTime": 1748242263062, "endTime": 1748242279392.4502, "attributes": {"genkit:type": "util", "genkit:name": "generate", "genkit:isRoot": true, "genkit:path": "/{generate,t:util}", "genkit:input": "{\"model\":\"googleai/gemini-1.5-flash\",\"messages\":[{\"role\":\"user\",\"content\":[{\"text\":\"مرحباً، قدم نفسك باللغة العربية في جملتين فقط.\"}]}],\"config\":{}}", "genkit:state": "error"}, "displayName": "generate", "links": [], "instrumentationLibrary": {"name": "genkit-tracer", "version": "v1"}, "spanKind": "INTERNAL", "sameProcessAsParentSpan": {"value": true}, "status": {"code": 2, "message": "[GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: fetch failed"}, "timeEvents": {"timeEvent": [{"time": 1748242279392.3406, "annotation": {"attributes": {"exception.type": "Error", "exception.message": "[GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: fetch failed", "exception.stacktrace": "TypeError: fetch failed\n    at node:internal/deps/undici/undici:13502:13\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async makeRequest (C:\\Users\\<USER>\\malite agent\\node_modules\\@google\\generative-ai\\dist\\index.js:397:20)\n    at async generateContent (C:\\Users\\<USER>\\malite agent\\node_modules\\@google\\generative-ai\\dist\\index.js:867:22)\n    at async ChatSession.sendMessage (C:\\Users\\<USER>\\malite agent\\node_modules\\@google\\generative-ai\\dist\\index.js:1210:9)\n    at async callGemini (C:\\Users\\<USER>\\malite agent\\node_modules\\@genkit-ai\\googleai\\lib\\gemini.js:845:26)\n    at async C:\\Users\\<USER>\\malite agent\\node_modules\\@genkit-ai\\googleai\\lib\\gemini.js:891:11\n    at async C:\\Users\\<USER>\\malite agent\\node_modules\\@genkit-ai\\core\\lib\\action.js:210:14\n    at async C:\\Users\\<USER>\\malite agent\\node_modules\\@genkit-ai\\core\\lib\\action.js:131:27\n    at async C:\\Users\\<USER>\\malite agent\\node_modules\\@genkit-ai\\core\\lib\\tracing\\instrumentation.js:65:16"}, "description": "exception"}}]}}}, "displayName": "generate", "startTime": 1748242263062, "endTime": 1748242279392.4502}