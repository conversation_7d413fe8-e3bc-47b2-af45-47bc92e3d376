# ملف .dockerignore لتجاهل الملفات غير المطلوبة في صورة Docker

# ملفات النظام
.DS_Store
Thumbs.db

# ملفات Git
.git
.gitignore
.gitattributes

# ملفات IDE
.vscode/
.idea/
*.swp
*.swo
*~

# ملفات Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# ملفات البيئة (سيتم إنشاؤها في وقت التشغيل)
.env
.env.local
.env.development
.env.test
.env.production

# ملفات التطوير
tests/
test/
docs/
*.test.js
*.spec.js

# ملفات التوثيق
README.md
CHANGELOG.md
LICENSE
*.md

# ملفات التكوين المحلية
.genkit/
coverage/
.nyc_output/

# ملفات مؤقتة
tmp/
temp/
*.tmp
*.temp

# ملفات السجلات
logs/
*.log

# ملفات النشر
.gcloudignore
cloudbuild.yaml
app.yaml
Dockerfile
.dockerignore

# ملفات النسخ الاحتياطية
*.backup
*.bak

# ملفات الضغط
*.zip
*.tar.gz
*.rar

# ملفات الصور (إذا لم تكن مطلوبة)
*.png
*.jpg
*.jpeg
*.gif
*.svg
*.ico

# ملفات أخرى
.eslintrc*
.prettierrc*
jest.config.js
babel.config.js
