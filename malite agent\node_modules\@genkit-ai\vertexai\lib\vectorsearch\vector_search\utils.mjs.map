{"version": 3, "sources": ["../../../src/vectorsearch/vector_search/utils.ts"], "sourcesContent": ["/**\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { GoogleAuth } from 'google-auth-library';\nimport { google } from 'googleapis';\n\n/**\n * Retrieves an access token using the provided GoogleAuth client.\n *\n * @param {GoogleAuth} auth - The GoogleAuth client.\n * @returns {Promise<string | null>} - A promise that resolves to the access token.\n */\nexport async function getAccessToken(auth: GoogleAuth): Promise<string | null> {\n  const client = await auth.getClient();\n  const _accessToken = await client.getAccessToken();\n  return _accessToken.token || null;\n}\n\n/**\n * Retrieves the project number for a given project ID.\n *\n * This function sends a request to the Google Cloud Resource Manager API to\n * fetch the project number for the specified project ID.\n *\n * @param {string} projectId - The ID of the Google Cloud project.\n * @returns {Promise<string>} - A promise that resolves to the project number.\n * @throws {Error} - Throws an error if the project number cannot be fetched.\n */\nexport async function getProjectNumber(projectId: string): Promise<string> {\n  const client = google.cloudresourcemanager('v1');\n  const authClient = await google.auth.getClient({\n    scopes: ['https://www.googleapis.com/auth/cloud-platform'],\n  });\n\n  try {\n    const response = await client.projects.get({\n      projectId: projectId,\n      auth: authClient,\n    });\n\n    if (!response.data.projectNumber) {\n      throw new Error(\n        `Error fetching project number for Vertex AI plugin for project ${projectId}`\n      );\n    }\n    return response.data['projectNumber'];\n  } catch (error) {\n    throw new Error(\n      `Error fetching project number for Vertex AI plugin for project ${projectId}`\n    );\n  }\n}\n"], "mappings": "AAiBA,SAAS,cAAc;AAQvB,eAAsB,eAAe,MAA0C;AAC7E,QAAM,SAAS,MAAM,KAAK,UAAU;AACpC,QAAM,eAAe,MAAM,OAAO,eAAe;AACjD,SAAO,aAAa,SAAS;AAC/B;AAYA,eAAsB,iBAAiB,WAAoC;AACzE,QAAM,SAAS,OAAO,qBAAqB,IAAI;AAC/C,QAAM,aAAa,MAAM,OAAO,KAAK,UAAU;AAAA,IAC7C,QAAQ,CAAC,gDAAgD;AAAA,EAC3D,CAAC;AAED,MAAI;AACF,UAAM,WAAW,MAAM,OAAO,SAAS,IAAI;AAAA,MACzC;AAAA,MACA,MAAM;AAAA,IACR,CAAC;AAED,QAAI,CAAC,SAAS,KAAK,eAAe;AAChC,YAAM,IAAI;AAAA,QACR,kEAAkE,SAAS;AAAA,MAC7E;AAAA,IACF;AACA,WAAO,SAAS,KAAK,eAAe;AAAA,EACtC,SAAS,OAAO;AACd,UAAM,IAAI;AAAA,MACR,kEAAkE,SAAS;AAAA,IAC7E;AAAA,EACF;AACF;", "names": []}