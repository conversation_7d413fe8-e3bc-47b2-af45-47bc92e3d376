{"version": 3, "file": "IResource.js", "sourceRoot": "", "sources": ["../../src/IResource.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ResourceAttributes } from './types';\n\n/**\n * An interface that represents a resource. A Resource describes the entity for which signals (metrics or trace) are\n * collected.\n *\n */\nexport interface IResource {\n  /**\n   * Check if async attributes have resolved. This is useful to avoid awaiting\n   * waitForAsyncAttributes (which will introduce asynchronous behavior) when not necessary.\n   *\n   * @returns true if the resource \"attributes\" property is not yet settled to its final value\n   */\n  asyncAttributesPending?: boolean;\n\n  /**\n   * @returns the Resource's attributes.\n   */\n  readonly attributes: ResourceAttributes;\n\n  /**\n   * Returns a promise that will never be rejected. Resolves when all async attributes have finished being added to\n   * this Resource's attributes. This is useful in exporters to block until resource detection\n   * has finished.\n   */\n  waitForAsyncAttributes?(): Promise<void>;\n\n  /**\n   * Returns a new, merged {@link Resource} by merging the current Resource\n   * with the other Resource. In case of a collision, other Resource takes\n   * precedence.\n   *\n   * @param other the Resource that will be merged with this.\n   * @returns the newly merged Resource.\n   */\n  merge(other: IResource | null): IResource;\n}\n"]}