// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.aiplatform.v1;

option csharp_namespace = "Google.Cloud.AIPlatform.V1";
option go_package = "cloud.google.com/go/aiplatform/apiv1/aiplatformpb;aiplatformpb";
option java_multiple_files = true;
option java_outer_classname = "TypesProto";
option java_package = "com.google.cloud.aiplatform.v1";
option php_namespace = "Google\\Cloud\\AIPlatform\\V1";
option ruby_package = "Google::Cloud::AIPlatform::V1";

// A list of boolean values.
message BoolArray {
  // A list of bool values.
  repeated bool values = 1;
}

// A list of double values.
message DoubleArray {
  // A list of double values.
  repeated double values = 1;
}

// A list of int64 values.
message Int64Array {
  // A list of int64 values.
  repeated int64 values = 1;
}

// A list of string values.
message StringArray {
  // A list of string values.
  repeated string values = 1;
}

// A tensor value type.
message Tensor {
  // Data type of the tensor.
  enum DataType {
    // Not a legal value for DataType. Used to indicate a DataType field has not
    // been set.
    DATA_TYPE_UNSPECIFIED = 0;

    // Data types that all computation devices are expected to be
    // capable to support.
    BOOL = 1;

    STRING = 2;

    FLOAT = 3;

    DOUBLE = 4;

    INT8 = 5;

    INT16 = 6;

    INT32 = 7;

    INT64 = 8;

    UINT8 = 9;

    UINT16 = 10;

    UINT32 = 11;

    UINT64 = 12;
  }

  // The data type of tensor.
  DataType dtype = 1;

  // Shape of the tensor.
  repeated int64 shape = 2;

  // Type specific representations that make it easy to create tensor protos in
  // all languages.  Only the representation corresponding to "dtype" can
  // be set.  The values hold the flattened representation of the tensor in
  // row major order.
  //
  // [BOOL][google.cloud.aiplatform.v1.Tensor.DataType.BOOL]
  repeated bool bool_val = 3;

  // [STRING][google.cloud.aiplatform.v1.Tensor.DataType.STRING]
  repeated string string_val = 14;

  // [STRING][google.cloud.aiplatform.v1.Tensor.DataType.STRING]
  repeated bytes bytes_val = 15;

  // [FLOAT][google.cloud.aiplatform.v1.Tensor.DataType.FLOAT]
  repeated float float_val = 5;

  // [DOUBLE][google.cloud.aiplatform.v1.Tensor.DataType.DOUBLE]
  repeated double double_val = 6;

  // [INT_8][google.cloud.aiplatform.v1.Tensor.DataType.INT8]
  // [INT_16][google.cloud.aiplatform.v1.Tensor.DataType.INT16]
  // [INT_32][google.cloud.aiplatform.v1.Tensor.DataType.INT32]
  repeated int32 int_val = 7;

  // [INT64][google.cloud.aiplatform.v1.Tensor.DataType.INT64]
  repeated int64 int64_val = 8;

  // [UINT8][google.cloud.aiplatform.v1.Tensor.DataType.UINT8]
  // [UINT16][google.cloud.aiplatform.v1.Tensor.DataType.UINT16]
  // [UINT32][google.cloud.aiplatform.v1.Tensor.DataType.UINT32]
  repeated uint32 uint_val = 9;

  // [UINT64][google.cloud.aiplatform.v1.Tensor.DataType.UINT64]
  repeated uint64 uint64_val = 10;

  // A list of tensor values.
  repeated Tensor list_val = 11;

  // A map of string to tensor.
  map<string, Tensor> struct_val = 12;

  // Serialized raw tensor content.
  bytes tensor_val = 13;
}
