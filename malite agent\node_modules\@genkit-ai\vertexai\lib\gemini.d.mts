import '@google-cloud/vertexai';
import 'genkit';
import 'genkit/model';
export { p as GENERIC_GEMINI_MODEL, n as GeminiConfig, G as GeminiConfigSchema, o as GeminiVersionString, S as SUPPORTED_GEMINI_MODELS, q as SUPPORTED_V15_MODELS, m as SafetySettingsSchema, v as cleanSchema, w as defineGeminiKnownModel, x as defineGeminiModel, u as fromGeminiCandidate, g as gemini, a as gemini10Pro, b as gemini15Flash, c as gemini15Pro, d as gemini20Flash, e as gemini20Flash001, f as gemini20FlashLite, h as gemini20FlashLitePreview0205, i as gemini20ProExp0205, j as gemini25FlashPreview0417, k as gemini25ProExp0325, l as gemini25ProPreview0325, s as toGeminiMessage, r as toGeminiSystemInstruction, t as toGeminiTool } from './types-Bc0LKM8D.mjs';
import 'google-auth-library';
