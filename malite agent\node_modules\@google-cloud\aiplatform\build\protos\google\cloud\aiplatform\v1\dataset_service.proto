// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.aiplatform.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/aiplatform/v1/annotation.proto";
import "google/cloud/aiplatform/v1/annotation_spec.proto";
import "google/cloud/aiplatform/v1/data_item.proto";
import "google/cloud/aiplatform/v1/dataset.proto";
import "google/cloud/aiplatform/v1/dataset_version.proto";
import "google/cloud/aiplatform/v1/model.proto";
import "google/cloud/aiplatform/v1/operation.proto";
import "google/cloud/aiplatform/v1/saved_query.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";

option csharp_namespace = "Google.Cloud.AIPlatform.V1";
option go_package = "cloud.google.com/go/aiplatform/apiv1/aiplatformpb;aiplatformpb";
option java_multiple_files = true;
option java_outer_classname = "DatasetServiceProto";
option java_package = "com.google.cloud.aiplatform.v1";
option php_namespace = "Google\\Cloud\\AIPlatform\\V1";
option ruby_package = "Google::Cloud::AIPlatform::V1";

// The service that manages Vertex AI Dataset and its child resources.
service DatasetService {
  option (google.api.default_host) = "aiplatform.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Creates a Dataset.
  rpc CreateDataset(CreateDatasetRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/datasets"
      body: "dataset"
      additional_bindings { post: "/v1/datasets" body: "dataset" }
    };
    option (google.api.method_signature) = "parent,dataset";
    option (google.longrunning.operation_info) = {
      response_type: "Dataset"
      metadata_type: "CreateDatasetOperationMetadata"
    };
  }

  // Gets a Dataset.
  rpc GetDataset(GetDatasetRequest) returns (Dataset) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/datasets/*}"
      additional_bindings { get: "/v1/{name=datasets/*}" }
    };
    option (google.api.method_signature) = "name";
  }

  // Updates a Dataset.
  rpc UpdateDataset(UpdateDatasetRequest) returns (Dataset) {
    option (google.api.http) = {
      patch: "/v1/{dataset.name=projects/*/locations/*/datasets/*}"
      body: "dataset"
      additional_bindings {
        patch: "/v1/{dataset.name=datasets/*}"
        body: "dataset"
      }
    };
    option (google.api.method_signature) = "dataset,update_mask";
  }

  // Lists Datasets in a Location.
  rpc ListDatasets(ListDatasetsRequest) returns (ListDatasetsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/datasets"
      additional_bindings { get: "/v1/datasets" }
    };
    option (google.api.method_signature) = "parent";
  }

  // Deletes a Dataset.
  rpc DeleteDataset(DeleteDatasetRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/datasets/*}"
      additional_bindings { delete: "/v1/{name=datasets/*}" }
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "DeleteOperationMetadata"
    };
  }

  // Imports data into a Dataset.
  rpc ImportData(ImportDataRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/datasets/*}:import"
      body: "*"
    };
    option (google.api.method_signature) = "name,import_configs";
    option (google.longrunning.operation_info) = {
      response_type: "ImportDataResponse"
      metadata_type: "ImportDataOperationMetadata"
    };
  }

  // Exports data from a Dataset.
  rpc ExportData(ExportDataRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/datasets/*}:export"
      body: "*"
    };
    option (google.api.method_signature) = "name,export_config";
    option (google.longrunning.operation_info) = {
      response_type: "ExportDataResponse"
      metadata_type: "ExportDataOperationMetadata"
    };
  }

  // Create a version from a Dataset.
  rpc CreateDatasetVersion(CreateDatasetVersionRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*/datasets/*}/datasetVersions"
      body: "dataset_version"
      additional_bindings {
        post: "/v1/{parent=datasets/*}/datasetVersions"
        body: "dataset_version"
      }
    };
    option (google.api.method_signature) = "parent,dataset_version";
    option (google.longrunning.operation_info) = {
      response_type: "DatasetVersion"
      metadata_type: "CreateDatasetVersionOperationMetadata"
    };
  }

  // Updates a DatasetVersion.
  rpc UpdateDatasetVersion(UpdateDatasetVersionRequest)
      returns (DatasetVersion) {
    option (google.api.http) = {
      patch: "/v1/{dataset_version.name=projects/*/locations/*/datasets/*/datasetVersions/*}"
      body: "dataset_version"
      additional_bindings {
        patch: "/v1/{dataset_version.name=datasets/*/datasetVersions/*}"
        body: "dataset_version"
      }
    };
    option (google.api.method_signature) = "dataset_version,update_mask";
  }

  // Deletes a Dataset version.
  rpc DeleteDatasetVersion(DeleteDatasetVersionRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/datasets/*/datasetVersions/*}"
      additional_bindings { delete: "/v1/{name=datasets/*/datasetVersions/*}" }
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "DeleteOperationMetadata"
    };
  }

  // Gets a Dataset version.
  rpc GetDatasetVersion(GetDatasetVersionRequest) returns (DatasetVersion) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/datasets/*/datasetVersions/*}"
      additional_bindings { get: "/v1/{name=datasets/*/datasetVersions/*}" }
    };
    option (google.api.method_signature) = "name";
  }

  // Lists DatasetVersions in a Dataset.
  rpc ListDatasetVersions(ListDatasetVersionsRequest)
      returns (ListDatasetVersionsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*/datasets/*}/datasetVersions"
      additional_bindings { get: "/v1/{parent=datasets/*}/datasetVersions" }
    };
    option (google.api.method_signature) = "parent";
  }

  // Restores a dataset version.
  rpc RestoreDatasetVersion(RestoreDatasetVersionRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/datasets/*/datasetVersions/*}:restore"
      additional_bindings {
        get: "/v1/{name=datasets/*/datasetVersions/*}:restore"
      }
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "DatasetVersion"
      metadata_type: "RestoreDatasetVersionOperationMetadata"
    };
  }

  // Lists DataItems in a Dataset.
  rpc ListDataItems(ListDataItemsRequest) returns (ListDataItemsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*/datasets/*}/dataItems"
    };
    option (google.api.method_signature) = "parent";
  }

  // Searches DataItems in a Dataset.
  rpc SearchDataItems(SearchDataItemsRequest)
      returns (SearchDataItemsResponse) {
    option (google.api.http) = {
      get: "/v1/{dataset=projects/*/locations/*/datasets/*}:searchDataItems"
    };
  }

  // Lists SavedQueries in a Dataset.
  rpc ListSavedQueries(ListSavedQueriesRequest)
      returns (ListSavedQueriesResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*/datasets/*}/savedQueries"
    };
    option (google.api.method_signature) = "parent";
  }

  // Deletes a SavedQuery.
  rpc DeleteSavedQuery(DeleteSavedQueryRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/datasets/*/savedQueries/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "DeleteOperationMetadata"
    };
  }

  // Gets an AnnotationSpec.
  rpc GetAnnotationSpec(GetAnnotationSpecRequest) returns (AnnotationSpec) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/datasets/*/annotationSpecs/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists Annotations belongs to a dataitem
  // This RPC is only available in InternalDatasetService. It is only used for
  // exporting conversation data to CCAI Insights.
  rpc ListAnnotations(ListAnnotationsRequest)
      returns (ListAnnotationsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*/datasets/*/dataItems/*}/annotations"
    };
    option (google.api.method_signature) = "parent";
  }
}

// Request message for
// [DatasetService.CreateDataset][google.cloud.aiplatform.v1.DatasetService.CreateDataset].
message CreateDatasetRequest {
  // Required. The resource name of the Location to create the Dataset in.
  // Format: `projects/{project}/locations/{location}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Required. The Dataset to create.
  Dataset dataset = 2 [(google.api.field_behavior) = REQUIRED];
}

// Runtime operation information for
// [DatasetService.CreateDataset][google.cloud.aiplatform.v1.DatasetService.CreateDataset].
message CreateDatasetOperationMetadata {
  // The operation generic information.
  GenericOperationMetadata generic_metadata = 1;
}

// Request message for
// [DatasetService.GetDataset][google.cloud.aiplatform.v1.DatasetService.GetDataset].
// Next ID: 4
message GetDatasetRequest {
  // Required. The name of the Dataset resource.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/Dataset"
    }
  ];

  // Mask specifying which fields to read.
  google.protobuf.FieldMask read_mask = 2;
}

// Request message for
// [DatasetService.UpdateDataset][google.cloud.aiplatform.v1.DatasetService.UpdateDataset].
message UpdateDatasetRequest {
  // Required. The Dataset which replaces the resource on the server.
  Dataset dataset = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The update mask applies to the resource.
  // For the `FieldMask` definition, see
  // [google.protobuf.FieldMask][google.protobuf.FieldMask]. Updatable fields:
  //
  //   * `display_name`
  //   * `description`
  //   * `labels`
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Request message for
// [DatasetService.UpdateDatasetVersion][google.cloud.aiplatform.v1.DatasetService.UpdateDatasetVersion].
message UpdateDatasetVersionRequest {
  // Required. The DatasetVersion which replaces the resource on the server.
  DatasetVersion dataset_version = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The update mask applies to the resource.
  // For the `FieldMask` definition, see
  // [google.protobuf.FieldMask][google.protobuf.FieldMask]. Updatable fields:
  //
  //   * `display_name`
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Request message for
// [DatasetService.ListDatasets][google.cloud.aiplatform.v1.DatasetService.ListDatasets].
message ListDatasetsRequest {
  // Required. The name of the Dataset's parent resource.
  // Format: `projects/{project}/locations/{location}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // An expression for filtering the results of the request. For field names
  // both snake_case and camelCase are supported.
  //
  //   * `display_name`: supports = and !=
  //   * `metadata_schema_uri`: supports = and !=
  //   * `labels` supports general map functions that is:
  //     * `labels.key=value` - key:value equality
  //     * `labels.key:* or labels:key - key existence
  //     * A key including a space must be quoted. `labels."a key"`.
  //
  // Some examples:
  //
  //   * `displayName="myDisplayName"`
  //   * `labels.myKey="myValue"`
  string filter = 2;

  // The standard list page size.
  int32 page_size = 3;

  // The standard list page token.
  string page_token = 4;

  // Mask specifying which fields to read.
  google.protobuf.FieldMask read_mask = 5;

  // A comma-separated list of fields to order by, sorted in ascending order.
  // Use "desc" after a field name for descending.
  // Supported fields:
  //
  //   * `display_name`
  //   * `create_time`
  //   * `update_time`
  string order_by = 6;
}

// Response message for
// [DatasetService.ListDatasets][google.cloud.aiplatform.v1.DatasetService.ListDatasets].
message ListDatasetsResponse {
  // A list of Datasets that matches the specified filter in the request.
  repeated Dataset datasets = 1;

  // The standard List next-page token.
  string next_page_token = 2;
}

// Request message for
// [DatasetService.DeleteDataset][google.cloud.aiplatform.v1.DatasetService.DeleteDataset].
message DeleteDatasetRequest {
  // Required. The resource name of the Dataset to delete.
  // Format:
  // `projects/{project}/locations/{location}/datasets/{dataset}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/Dataset"
    }
  ];
}

// Request message for
// [DatasetService.ImportData][google.cloud.aiplatform.v1.DatasetService.ImportData].
message ImportDataRequest {
  // Required. The name of the Dataset resource.
  // Format:
  // `projects/{project}/locations/{location}/datasets/{dataset}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/Dataset"
    }
  ];

  // Required. The desired input locations. The contents of all input locations
  // will be imported in one batch.
  repeated ImportDataConfig import_configs = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Response message for
// [DatasetService.ImportData][google.cloud.aiplatform.v1.DatasetService.ImportData].
message ImportDataResponse {}

// Runtime operation information for
// [DatasetService.ImportData][google.cloud.aiplatform.v1.DatasetService.ImportData].
message ImportDataOperationMetadata {
  // The common part of the operation metadata.
  GenericOperationMetadata generic_metadata = 1;
}

// Request message for
// [DatasetService.ExportData][google.cloud.aiplatform.v1.DatasetService.ExportData].
message ExportDataRequest {
  // Required. The name of the Dataset resource.
  // Format:
  // `projects/{project}/locations/{location}/datasets/{dataset}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/Dataset"
    }
  ];

  // Required. The desired output location.
  ExportDataConfig export_config = 2 [(google.api.field_behavior) = REQUIRED];
}

// Response message for
// [DatasetService.ExportData][google.cloud.aiplatform.v1.DatasetService.ExportData].
message ExportDataResponse {
  // All of the files that are exported in this export operation. For custom
  // code training export, only three (training, validation and test)
  // Cloud Storage paths in wildcard format are populated
  // (for example, gs://.../training-*).
  repeated string exported_files = 1;

  // Only present for custom code training export use case. Records data stats,
  // i.e., train/validation/test item/annotation counts calculated during
  // the export operation.
  Model.DataStats data_stats = 2;
}

// Runtime operation information for
// [DatasetService.ExportData][google.cloud.aiplatform.v1.DatasetService.ExportData].
message ExportDataOperationMetadata {
  // The common part of the operation metadata.
  GenericOperationMetadata generic_metadata = 1;

  // A Google Cloud Storage directory which path ends with '/'. The exported
  // data is stored in the directory.
  string gcs_output_directory = 2;
}

// Request message for
// [DatasetService.CreateDatasetVersion][google.cloud.aiplatform.v1.DatasetService.CreateDatasetVersion].
message CreateDatasetVersionRequest {
  // Required. The name of the Dataset resource.
  // Format:
  // `projects/{project}/locations/{location}/datasets/{dataset}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/Dataset"
    }
  ];

  // Required. The version to be created. The same CMEK policies with the
  // original Dataset will be applied the dataset version. So here we don't need
  // to specify the EncryptionSpecType here.
  DatasetVersion dataset_version = 2 [(google.api.field_behavior) = REQUIRED];
}

// Runtime operation information for
// [DatasetService.CreateDatasetVersion][google.cloud.aiplatform.v1.DatasetService.CreateDatasetVersion].
message CreateDatasetVersionOperationMetadata {
  // The common part of the operation metadata.
  GenericOperationMetadata generic_metadata = 1;
}

// Request message for
// [DatasetService.DeleteDatasetVersion][google.cloud.aiplatform.v1.DatasetService.DeleteDatasetVersion].
message DeleteDatasetVersionRequest {
  // Required. The resource name of the Dataset version to delete.
  // Format:
  // `projects/{project}/locations/{location}/datasets/{dataset}/datasetVersions/{dataset_version}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/DatasetVersion"
    }
  ];
}

// Request message for
// [DatasetService.GetDatasetVersion][google.cloud.aiplatform.v1.DatasetService.GetDatasetVersion].
// Next ID: 4
message GetDatasetVersionRequest {
  // Required. The resource name of the Dataset version to delete.
  // Format:
  // `projects/{project}/locations/{location}/datasets/{dataset}/datasetVersions/{dataset_version}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/DatasetVersion"
    }
  ];

  // Mask specifying which fields to read.
  google.protobuf.FieldMask read_mask = 2;
}

// Request message for
// [DatasetService.ListDatasetVersions][google.cloud.aiplatform.v1.DatasetService.ListDatasetVersions].
message ListDatasetVersionsRequest {
  // Required. The resource name of the Dataset to list DatasetVersions from.
  // Format:
  // `projects/{project}/locations/{location}/datasets/{dataset}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/Dataset"
    }
  ];

  // Optional. The standard list filter.
  string filter = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The standard list page size.
  int32 page_size = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The standard list page token.
  string page_token = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Mask specifying which fields to read.
  google.protobuf.FieldMask read_mask = 5
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. A comma-separated list of fields to order by, sorted in ascending
  // order. Use "desc" after a field name for descending.
  string order_by = 6 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for
// [DatasetService.ListDatasetVersions][google.cloud.aiplatform.v1.DatasetService.ListDatasetVersions].
message ListDatasetVersionsResponse {
  // A list of DatasetVersions that matches the specified filter in the request.
  repeated DatasetVersion dataset_versions = 1;

  // The standard List next-page token.
  string next_page_token = 2;
}

// Request message for
// [DatasetService.RestoreDatasetVersion][google.cloud.aiplatform.v1.DatasetService.RestoreDatasetVersion].
message RestoreDatasetVersionRequest {
  // Required. The name of the DatasetVersion resource.
  // Format:
  // `projects/{project}/locations/{location}/datasets/{dataset}/datasetVersions/{dataset_version}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/DatasetVersion"
    }
  ];
}

// Runtime operation information for
// [DatasetService.RestoreDatasetVersion][google.cloud.aiplatform.v1.DatasetService.RestoreDatasetVersion].
message RestoreDatasetVersionOperationMetadata {
  // The common part of the operation metadata.
  GenericOperationMetadata generic_metadata = 1;
}

// Request message for
// [DatasetService.ListDataItems][google.cloud.aiplatform.v1.DatasetService.ListDataItems].
message ListDataItemsRequest {
  // Required. The resource name of the Dataset to list DataItems from.
  // Format:
  // `projects/{project}/locations/{location}/datasets/{dataset}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/Dataset"
    }
  ];

  // The standard list filter.
  string filter = 2;

  // The standard list page size.
  int32 page_size = 3;

  // The standard list page token.
  string page_token = 4;

  // Mask specifying which fields to read.
  google.protobuf.FieldMask read_mask = 5;

  // A comma-separated list of fields to order by, sorted in ascending order.
  // Use "desc" after a field name for descending.
  string order_by = 6;
}

// Response message for
// [DatasetService.ListDataItems][google.cloud.aiplatform.v1.DatasetService.ListDataItems].
message ListDataItemsResponse {
  // A list of DataItems that matches the specified filter in the request.
  repeated DataItem data_items = 1;

  // The standard List next-page token.
  string next_page_token = 2;
}

// Request message for
// [DatasetService.SearchDataItems][google.cloud.aiplatform.v1.DatasetService.SearchDataItems].
message SearchDataItemsRequest {
  // Expression that allows ranking results based on annotation's property.
  message OrderByAnnotation {
    // Required. Saved query of the Annotation. Only Annotations belong to this
    // saved query will be considered for ordering.
    string saved_query = 1 [(google.api.field_behavior) = REQUIRED];

    // A comma-separated list of annotation fields to order by, sorted in
    // ascending order. Use "desc" after a field name for descending. Must also
    // specify saved_query.
    string order_by = 2;
  }

  oneof order {
    // A comma-separated list of data item fields to order by, sorted in
    // ascending order. Use "desc" after a field name for descending.
    string order_by_data_item = 12;

    // Expression that allows ranking results based on annotation's property.
    OrderByAnnotation order_by_annotation = 13;
  }

  // Required. The resource name of the Dataset from which to search DataItems.
  // Format:
  // `projects/{project}/locations/{location}/datasets/{dataset}`
  string dataset = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/Dataset"
    }
  ];

  // The resource name of a SavedQuery(annotation set in UI).
  // Format:
  // `projects/{project}/locations/{location}/datasets/{dataset}/savedQueries/{saved_query}`
  // All of the search will be done in the context of this SavedQuery.
  string saved_query = 2 [
    deprecated = true,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/SavedQuery"
    }
  ];

  // The resource name of a DataLabelingJob.
  // Format:
  // `projects/{project}/locations/{location}/dataLabelingJobs/{data_labeling_job}`
  // If this field is set, all of the search will be done in the context of
  // this DataLabelingJob.
  string data_labeling_job = 3;

  // An expression for filtering the DataItem that will be returned.
  //
  //   * `data_item_id` - for = or !=.
  //   * `labeled` - for = or !=.
  //   * `has_annotation(ANNOTATION_SPEC_ID)` - true only for DataItem that
  //     have at least one annotation with annotation_spec_id =
  //     `ANNOTATION_SPEC_ID` in the context of SavedQuery or DataLabelingJob.
  //
  // For example:
  //
  // * `data_item=1`
  // * `has_annotation(5)`
  string data_item_filter = 4;

  // An expression for filtering the Annotations that will be returned per
  // DataItem.
  //   * `annotation_spec_id` - for = or !=.
  string annotations_filter = 5 [deprecated = true];

  // An expression that specifies what Annotations will be returned per
  // DataItem. Annotations satisfied either of the conditions will be returned.
  //   * `annotation_spec_id` - for = or !=.
  // Must specify `saved_query_id=` - saved query id that annotations should
  // belong to.
  repeated string annotation_filters = 11;

  // Mask specifying which fields of
  // [DataItemView][google.cloud.aiplatform.v1.DataItemView] to read.
  google.protobuf.FieldMask field_mask = 6;

  // If set, only up to this many of Annotations will be returned per
  // DataItemView. The maximum value is 1000. If not set, the maximum value will
  // be used.
  int32 annotations_limit = 7;

  // Requested page size. Server may return fewer results than requested.
  // Default and maximum page size is 100.
  int32 page_size = 8;

  // A comma-separated list of fields to order by, sorted in ascending order.
  // Use "desc" after a field name for descending.
  string order_by = 9 [deprecated = true];

  // A token identifying a page of results for the server to return
  // Typically obtained via
  // [SearchDataItemsResponse.next_page_token][google.cloud.aiplatform.v1.SearchDataItemsResponse.next_page_token]
  // of the previous
  // [DatasetService.SearchDataItems][google.cloud.aiplatform.v1.DatasetService.SearchDataItems]
  // call.
  string page_token = 10;
}

// Response message for
// [DatasetService.SearchDataItems][google.cloud.aiplatform.v1.DatasetService.SearchDataItems].
message SearchDataItemsResponse {
  // The DataItemViews read.
  repeated DataItemView data_item_views = 1;

  // A token to retrieve next page of results.
  // Pass to
  // [SearchDataItemsRequest.page_token][google.cloud.aiplatform.v1.SearchDataItemsRequest.page_token]
  // to obtain that page.
  string next_page_token = 2;
}

// A container for a single DataItem and Annotations on it.
message DataItemView {
  // The DataItem.
  DataItem data_item = 1;

  // The Annotations on the DataItem. If too many Annotations should be returned
  // for the DataItem, this field will be truncated per annotations_limit in
  // request. If it was, then the has_truncated_annotations will be set to true.
  repeated Annotation annotations = 2;

  // True if and only if the Annotations field has been truncated. It happens if
  // more Annotations for this DataItem met the request's annotation_filter than
  // are allowed to be returned by annotations_limit.
  // Note that if Annotations field is not being returned due to field mask,
  // then this field will not be set to true no matter how many Annotations are
  // there.
  bool has_truncated_annotations = 3;
}

// Request message for
// [DatasetService.ListSavedQueries][google.cloud.aiplatform.v1.DatasetService.ListSavedQueries].
message ListSavedQueriesRequest {
  // Required. The resource name of the Dataset to list SavedQueries from.
  // Format:
  // `projects/{project}/locations/{location}/datasets/{dataset}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/Dataset"
    }
  ];

  // The standard list filter.
  string filter = 2;

  // The standard list page size.
  int32 page_size = 3;

  // The standard list page token.
  string page_token = 4;

  // Mask specifying which fields to read.
  google.protobuf.FieldMask read_mask = 5;

  // A comma-separated list of fields to order by, sorted in ascending order.
  // Use "desc" after a field name for descending.
  string order_by = 6;
}

// Response message for
// [DatasetService.ListSavedQueries][google.cloud.aiplatform.v1.DatasetService.ListSavedQueries].
message ListSavedQueriesResponse {
  // A list of SavedQueries that match the specified filter in the request.
  repeated SavedQuery saved_queries = 1;

  // The standard List next-page token.
  string next_page_token = 2;
}

// Request message for
// [DatasetService.DeleteSavedQuery][google.cloud.aiplatform.v1.DatasetService.DeleteSavedQuery].
message DeleteSavedQueryRequest {
  // Required. The resource name of the SavedQuery to delete.
  // Format:
  // `projects/{project}/locations/{location}/datasets/{dataset}/savedQueries/{saved_query}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/SavedQuery"
    }
  ];
}

// Request message for
// [DatasetService.GetAnnotationSpec][google.cloud.aiplatform.v1.DatasetService.GetAnnotationSpec].
message GetAnnotationSpecRequest {
  // Required. The name of the AnnotationSpec resource.
  // Format:
  // `projects/{project}/locations/{location}/datasets/{dataset}/annotationSpecs/{annotation_spec}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/AnnotationSpec"
    }
  ];

  // Mask specifying which fields to read.
  google.protobuf.FieldMask read_mask = 2;
}

// Request message for
// [DatasetService.ListAnnotations][google.cloud.aiplatform.v1.DatasetService.ListAnnotations].
message ListAnnotationsRequest {
  // Required. The resource name of the DataItem to list Annotations from.
  // Format:
  // `projects/{project}/locations/{location}/datasets/{dataset}/dataItems/{data_item}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/DataItem"
    }
  ];

  // The standard list filter.
  string filter = 2;

  // The standard list page size.
  int32 page_size = 3;

  // The standard list page token.
  string page_token = 4;

  // Mask specifying which fields to read.
  google.protobuf.FieldMask read_mask = 5;

  // A comma-separated list of fields to order by, sorted in ascending order.
  // Use "desc" after a field name for descending.
  string order_by = 6;
}

// Response message for
// [DatasetService.ListAnnotations][google.cloud.aiplatform.v1.DatasetService.ListAnnotations].
message ListAnnotationsResponse {
  // A list of Annotations that matches the specified filter in the request.
  repeated Annotation annotations = 1;

  // The standard List next-page token.
  string next_page_token = 2;
}
