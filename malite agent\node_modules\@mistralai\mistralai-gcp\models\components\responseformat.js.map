{"version": 3, "file": "responseformat.js", "sourceRoot": "", "sources": ["../../src/models/components/responseformat.ts"], "names": [], "mappings": ";AAAA;;GAEG;;;;;;;;;;;;;;;;;;;;;;;;;;AA0EH,oDAEC;AAED,wDAQC;AApFD,uCAAyB;AACzB,2DAA0D;AAC1D,qDAAiD;AAGjD,mDAKyB;AACzB,6DAI8B;AAU9B,gBAAgB;AACH,QAAA,4BAA4B,GAIrC,CAAC,CAAC,MAAM,CAAC;IACX,IAAI,EAAE,kDAA6B,CAAC,QAAQ,EAAE;IAC9C,WAAW,EAAE,CAAC,CAAC,QAAQ,CAAC,wCAAwB,CAAC,CAAC,QAAQ,EAAE;CAC7D,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE;IACjB,OAAO,IAAA,qBAAM,EAAC,CAAC,EAAE;QACf,aAAa,EAAE,YAAY;KAC5B,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AAQH,gBAAgB;AACH,QAAA,6BAA6B,GAItC,CAAC,CAAC,MAAM,CAAC;IACX,IAAI,EAAE,mDAA8B,CAAC,QAAQ,EAAE;IAC/C,UAAU,EAAE,CAAC,CAAC,QAAQ,CAAC,yCAAyB,CAAC,CAAC,QAAQ,EAAE;CAC7D,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE;IACjB,OAAO,IAAA,qBAAM,EAAC,CAAC,EAAE;QACf,UAAU,EAAE,aAAa;KAC1B,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AAEH;;;GAGG;AACH,IAAiB,eAAe,CAO/B;AAPD,WAAiB,eAAe;IAC9B,8DAA8D;IACjD,6BAAa,GAAG,oCAA4B,CAAC;IAC1D,+DAA+D;IAClD,8BAAc,GAAG,qCAA6B,CAAC;AAG9D,CAAC,EAPgB,eAAe,+BAAf,eAAe,QAO/B;AAED,SAAgB,oBAAoB,CAAC,cAA8B;IACjE,OAAO,IAAI,CAAC,SAAS,CAAC,qCAA6B,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC,CAAC;AAC7E,CAAC;AAED,SAAgB,sBAAsB,CACpC,UAAkB;IAElB,OAAO,IAAA,sBAAS,EACd,UAAU,EACV,CAAC,CAAC,EAAE,EAAE,CAAC,oCAA4B,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EACxD,4CAA4C,CAC7C,CAAC;AACJ,CAAC"}