{"interfaces": {"google.cloud.aiplatform.v1.DatasetService": {"retry_codes": {"non_idempotent": [], "idempotent": ["DEADLINE_EXCEEDED", "UNAVAILABLE"]}, "retry_params": {"default": {"initial_retry_delay_millis": 100, "retry_delay_multiplier": 1.3, "max_retry_delay_millis": 60000, "initial_rpc_timeout_millis": 60000, "rpc_timeout_multiplier": 1, "max_rpc_timeout_millis": 60000, "total_timeout_millis": 600000}}, "methods": {"CreateDataset": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "GetDataset": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "UpdateDataset": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "ListDatasets": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "DeleteDataset": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "ImportData": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "ExportData": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "CreateDatasetVersion": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "UpdateDatasetVersion": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "DeleteDatasetVersion": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "GetDatasetVersion": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "ListDatasetVersions": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "RestoreDatasetVersion": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "ListDataItems": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "SearchDataItems": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "ListSavedQueries": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "DeleteSavedQuery": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "GetAnnotationSpec": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "ListAnnotations": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}}}}}