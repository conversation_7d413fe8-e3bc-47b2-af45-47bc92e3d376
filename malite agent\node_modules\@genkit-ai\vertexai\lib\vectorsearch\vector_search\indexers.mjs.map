{"version": 3, "sources": ["../../../src/vectorsearch/vector_search/indexers.ts"], "sourcesContent": ["/**\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Genkit, z } from 'genkit';\nimport { IndexerAction, indexerRef } from 'genkit/retriever';\nimport {\n  Datapoint,\n  VertexAIVectorIndexerOptionsSchema,\n  VertexVectorSearchOptions,\n} from './types';\nimport { upsertDatapoints } from './upsert_datapoints';\n\n/**\n * Creates a reference to a Vertex AI indexer.\n *\n * @param {Object} params - The parameters for the indexer reference.\n * @param {string} params.indexId - The ID of the Vertex AI index.\n * @param {string} [params.displayName] - An optional display name for the indexer.\n * @returns {Object} - The indexer reference object.\n */\nexport const vertexAiIndexerRef = (params: {\n  indexId: string;\n  displayName?: string;\n}) => {\n  return indexerRef({\n    name: `vertexai/${params.indexId}`,\n    info: {\n      label: params.displayName ?? `Vertex AI - ${params.indexId}`,\n    },\n    configSchema: VertexAIVectorIndexerOptionsSchema.optional(),\n  });\n};\n\n/**\n * Creates Vertex AI indexers.\n *\n * This function returns a list of indexer actions for Vertex AI based on the provided\n * vector search options and embedder configurations.\n *\n * @param {VertexVectorSearchOptions<EmbedderCustomOptions>} params - The parameters for creating the indexers.\n * @returns {IndexerAction<z.ZodTypeAny>[]} - An array of indexer actions.\n */\nexport function vertexAiIndexers<EmbedderCustomOptions extends z.ZodTypeAny>(\n  ai: Genkit,\n  params: VertexVectorSearchOptions<EmbedderCustomOptions>\n): IndexerAction<z.ZodTypeAny>[] {\n  const vectorSearchOptions = params.pluginOptions.vectorSearchOptions;\n  const indexers: IndexerAction<z.ZodTypeAny>[] = [];\n\n  if (!vectorSearchOptions || vectorSearchOptions.length === 0) {\n    return indexers;\n  }\n\n  for (const vectorSearchOption of vectorSearchOptions) {\n    const { documentIndexer, indexId } = vectorSearchOption;\n    const embedderReference =\n      vectorSearchOption.embedder ?? params.defaultEmbedder;\n\n    if (!embedderReference) {\n      throw new Error(\n        'Embedder reference is required to define Vertex AI retriever'\n      );\n    }\n    const embedderOptions = vectorSearchOption.embedderOptions;\n\n    const indexer = ai.defineIndexer(\n      {\n        name: `vertexai/${indexId}`,\n        configSchema: VertexAIVectorIndexerOptionsSchema.optional(),\n      },\n      async (docs, options) => {\n        let docIds: string[] = [];\n        try {\n          docIds = await documentIndexer(docs, options);\n        } catch (error) {\n          throw new Error(\n            `Error storing your document content/metadata: ${error}`\n          );\n        }\n\n        const embeddings = await ai.embedMany({\n          embedder: embedderReference,\n          content: docs,\n          options: embedderOptions,\n        });\n\n        const datapoints = embeddings.map(({ embedding }, i) => {\n          const dp = new Datapoint({\n            datapointId: docIds[i],\n            featureVector: embedding,\n          });\n          if (docs[i].metadata?.restricts) {\n            dp.restricts = docs[i].metadata?.restricts;\n          }\n          if (docs[i].metadata?.numericRestricts) {\n            dp.numericRestricts = docs[i].metadata?.numericRestricts;\n          }\n          if (docs[i].metadata?.crowdingTag) {\n            dp.crowdingTag = docs[i].metadata?.crowdingTag;\n          }\n          return dp;\n        });\n\n        try {\n          await upsertDatapoints({\n            datapoints,\n            authClient: params.authClient,\n            projectId: params.pluginOptions.projectId!,\n            location: params.pluginOptions.location!,\n            indexId: indexId,\n          });\n        } catch (error) {\n          throw error;\n        }\n      }\n    );\n\n    indexers.push(indexer);\n  }\n  return indexers;\n}\n"], "mappings": "AAiBA,SAAwB,kBAAkB;AAC1C;AAAA,EACE;AAAA,EACA;AAAA,OAEK;AACP,SAAS,wBAAwB;AAU1B,MAAM,qBAAqB,CAAC,WAG7B;AACJ,SAAO,WAAW;AAAA,IAChB,MAAM,YAAY,OAAO,OAAO;AAAA,IAChC,MAAM;AAAA,MACJ,OAAO,OAAO,eAAe,eAAe,OAAO,OAAO;AAAA,IAC5D;AAAA,IACA,cAAc,mCAAmC,SAAS;AAAA,EAC5D,CAAC;AACH;AAWO,SAAS,iBACd,IACA,QAC+B;AAC/B,QAAM,sBAAsB,OAAO,cAAc;AACjD,QAAM,WAA0C,CAAC;AAEjD,MAAI,CAAC,uBAAuB,oBAAoB,WAAW,GAAG;AAC5D,WAAO;AAAA,EACT;AAEA,aAAW,sBAAsB,qBAAqB;AACpD,UAAM,EAAE,iBAAiB,QAAQ,IAAI;AACrC,UAAM,oBACJ,mBAAmB,YAAY,OAAO;AAExC,QAAI,CAAC,mBAAmB;AACtB,YAAM,IAAI;AAAA,QACR;AAAA,MACF;AAAA,IACF;AACA,UAAM,kBAAkB,mBAAmB;AAE3C,UAAM,UAAU,GAAG;AAAA,MACjB;AAAA,QACE,MAAM,YAAY,OAAO;AAAA,QACzB,cAAc,mCAAmC,SAAS;AAAA,MAC5D;AAAA,MACA,OAAO,MAAM,YAAY;AACvB,YAAI,SAAmB,CAAC;AACxB,YAAI;AACF,mBAAS,MAAM,gBAAgB,MAAM,OAAO;AAAA,QAC9C,SAAS,OAAO;AACd,gBAAM,IAAI;AAAA,YACR,iDAAiD,KAAK;AAAA,UACxD;AAAA,QACF;AAEA,cAAM,aAAa,MAAM,GAAG,UAAU;AAAA,UACpC,UAAU;AAAA,UACV,SAAS;AAAA,UACT,SAAS;AAAA,QACX,CAAC;AAED,cAAM,aAAa,WAAW,IAAI,CAAC,EAAE,UAAU,GAAG,MAAM;AACtD,gBAAM,KAAK,IAAI,UAAU;AAAA,YACvB,aAAa,OAAO,CAAC;AAAA,YACrB,eAAe;AAAA,UACjB,CAAC;AACD,cAAI,KAAK,CAAC,EAAE,UAAU,WAAW;AAC/B,eAAG,YAAY,KAAK,CAAC,EAAE,UAAU;AAAA,UACnC;AACA,cAAI,KAAK,CAAC,EAAE,UAAU,kBAAkB;AACtC,eAAG,mBAAmB,KAAK,CAAC,EAAE,UAAU;AAAA,UAC1C;AACA,cAAI,KAAK,CAAC,EAAE,UAAU,aAAa;AACjC,eAAG,cAAc,KAAK,CAAC,EAAE,UAAU;AAAA,UACrC;AACA,iBAAO;AAAA,QACT,CAAC;AAED,YAAI;AACF,gBAAM,iBAAiB;AAAA,YACrB;AAAA,YACA,YAAY,OAAO;AAAA,YACnB,WAAW,OAAO,cAAc;AAAA,YAChC,UAAU,OAAO,cAAc;AAAA,YAC/B;AAAA,UACF,CAAC;AAAA,QACH,SAAS,OAAO;AACd,gBAAM;AAAA,QACR;AAAA,MACF;AAAA,IACF;AAEA,aAAS,KAAK,OAAO;AAAA,EACvB;AACA,SAAO;AACT;", "names": []}