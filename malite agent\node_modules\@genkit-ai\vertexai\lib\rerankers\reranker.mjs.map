{"version": 3, "sources": ["../../src/rerankers/reranker.ts"], "sourcesContent": ["/**\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Genkit } from 'genkit';\nimport { RankedDocument, rerankerRef } from 'genkit/reranker';\nimport { DEFAULT_MODEL, getRerankEndpoint } from './constants.js';\nimport { VertexAIRerankerOptionsSchema, VertexRerankOptions } from './types.js';\n\n/**\n * Creates Vertex AI rerankers.\n *\n * This function creates and registers rerankers for the specified models.\n *\n * @param {VertexRerankOptions<EmbedderCustomOptions>} options - The parameters for creating the rerankers.\n * @returns {Promise<void>}\n */\nexport async function vertexAiRerankers(\n  ai: Genkit,\n  options: VertexRerankOptions\n): Promise<void> {\n  const rerankOptions = options.rerankOptions;\n\n  if (rerankOptions.length === 0) {\n    throw new Error('Provide at least one reranker configuration.');\n  }\n\n  const auth = options.authClient;\n  const client = await auth.getClient();\n  const projectId = options.projectId;\n\n  for (const rerankOption of rerankOptions) {\n    if (!rerankOption.name && !rerankOption.model) {\n      throw new Error('At least one of name or model must be provided.');\n    }\n    ai.defineReranker(\n      {\n        name: `vertexai/${rerankOption.name || rerankOption.model}`,\n        configSchema: VertexAIRerankerOptionsSchema.optional(),\n      },\n      async (query, documents, _options) => {\n        const response = await client.request({\n          method: 'POST',\n          url: getRerankEndpoint(projectId, options.location ?? 'us-central1'),\n          data: {\n            model: rerankOption.model || DEFAULT_MODEL, // Use model from config or default\n            query: query.text,\n            records: documents.map((doc, idx) => ({\n              id: `${idx}`,\n              content: doc.text,\n            })),\n          },\n        });\n\n        const rankedDocuments: RankedDocument[] = (\n          response.data as any\n        ).records.map((record: any) => {\n          const doc = documents[record.id];\n          return new RankedDocument({\n            content: doc.content,\n            metadata: {\n              ...doc.metadata,\n              score: record.score,\n            },\n          });\n        });\n\n        return { documents: rankedDocuments };\n      }\n    );\n  }\n}\n\n/**\n * Creates a reference to a Vertex AI reranker.\n *\n * @param {Object} params - The parameters for the reranker reference.\n * @param {string} [params.displayName] - An optional display name for the reranker.\n * @returns {Object} - The reranker reference object.\n */\nexport const vertexAiRerankerRef = (params: {\n  rerankerName: string;\n  displayName?: string;\n}) => {\n  return rerankerRef({\n    name: `vertexai/${params.rerankerName}`,\n    info: {\n      label: params.displayName ?? `Vertex AI Reranker`,\n    },\n    configSchema: VertexAIRerankerOptionsSchema.optional(),\n  });\n};\n"], "mappings": "AAiBA,SAAS,gBAAgB,mBAAmB;AAC5C,SAAS,eAAe,yBAAyB;AACjD,SAAS,qCAA0D;AAUnE,eAAsB,kBACpB,IACA,SACe;AACf,QAAM,gBAAgB,QAAQ;AAE9B,MAAI,cAAc,WAAW,GAAG;AAC9B,UAAM,IAAI,MAAM,8CAA8C;AAAA,EAChE;AAEA,QAAM,OAAO,QAAQ;AACrB,QAAM,SAAS,MAAM,KAAK,UAAU;AACpC,QAAM,YAAY,QAAQ;AAE1B,aAAW,gBAAgB,eAAe;AACxC,QAAI,CAAC,aAAa,QAAQ,CAAC,aAAa,OAAO;AAC7C,YAAM,IAAI,MAAM,iDAAiD;AAAA,IACnE;AACA,OAAG;AAAA,MACD;AAAA,QACE,MAAM,YAAY,aAAa,QAAQ,aAAa,KAAK;AAAA,QACzD,cAAc,8BAA8B,SAAS;AAAA,MACvD;AAAA,MACA,OAAO,OAAO,WAAW,aAAa;AACpC,cAAM,WAAW,MAAM,OAAO,QAAQ;AAAA,UACpC,QAAQ;AAAA,UACR,KAAK,kBAAkB,WAAW,QAAQ,YAAY,aAAa;AAAA,UACnE,MAAM;AAAA,YACJ,OAAO,aAAa,SAAS;AAAA;AAAA,YAC7B,OAAO,MAAM;AAAA,YACb,SAAS,UAAU,IAAI,CAAC,KAAK,SAAS;AAAA,cACpC,IAAI,GAAG,GAAG;AAAA,cACV,SAAS,IAAI;AAAA,YACf,EAAE;AAAA,UACJ;AAAA,QACF,CAAC;AAED,cAAM,kBACJ,SAAS,KACT,QAAQ,IAAI,CAAC,WAAgB;AAC7B,gBAAM,MAAM,UAAU,OAAO,EAAE;AAC/B,iBAAO,IAAI,eAAe;AAAA,YACxB,SAAS,IAAI;AAAA,YACb,UAAU;AAAA,cACR,GAAG,IAAI;AAAA,cACP,OAAO,OAAO;AAAA,YAChB;AAAA,UACF,CAAC;AAAA,QACH,CAAC;AAED,eAAO,EAAE,WAAW,gBAAgB;AAAA,MACtC;AAAA,IACF;AAAA,EACF;AACF;AASO,MAAM,sBAAsB,CAAC,WAG9B;AACJ,SAAO,YAAY;AAAA,IACjB,MAAM,YAAY,OAAO,YAAY;AAAA,IACrC,MAAM;AAAA,MACJ,OAAO,OAAO,eAAe;AAAA,IAC/B;AAAA,IACA,cAAc,8BAA8B,SAAS;AAAA,EACvD,CAAC;AACH;", "names": []}