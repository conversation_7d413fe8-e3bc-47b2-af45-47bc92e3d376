import { GoogleAuth } from 'google-auth-library';
import { P as PluginOptions } from './types-Bc0LKM8D.mjs';
import 'genkit';
import '@google-cloud/vertexai';
import 'genkit/model';

/**
 * Copyright 2024 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

interface PredictionResponse<R> {
    predictions: R[];
}
type PredictClient<I = unknown, R = unknown, P = unknown> = (instances: I[], parameters: P) => Promise<PredictionResponse<R>>;
declare function predictModel<I = unknown, R = unknown, P = unknown>(auth: GoogleAuth, { location, projectId }: PluginOptions, model: string): PredictClient<I, R, P>;

export { type PredictClient, predictModel };
