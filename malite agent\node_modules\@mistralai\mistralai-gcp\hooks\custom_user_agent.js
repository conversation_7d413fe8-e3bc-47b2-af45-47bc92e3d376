"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CustomUserAgentHook = void 0;
class CustomUserAgentHook {
    beforeRequest(_, request) {
        const userAgent = request.headers.get('user-agent');
        const version = userAgent.split(' ')[1];
        request.headers.set('user-agent', `mistral-gcp-client-typescript/${version}`);
        return request;
    }
}
exports.CustomUserAgentHook = CustomUserAgentHook;
//# sourceMappingURL=custom_user_agent.js.map