{"version": 3, "file": "errors.js", "sourceRoot": "", "sources": ["../../../src/types/errors.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;GAeG;;;AAEH;;GAEG;AACH,MAAM,eAAgB,SAAQ,KAAK;IAEjC,YAAY,OAAe,EAAE,UAAkB;QAC7C,KAAK,CAAC,OAAO,EAAE,EAAC,KAAK,EAAE,UAAU,EAAC,CAAC,CAAC;QACpC,IAAI,CAAC,OAAO,GAAG,qBAAqB,CAAC,iBAAiB,EAAE,OAAO,CAAC,CAAC;QACjE,IAAI,CAAC,IAAI,GAAG,iBAAiB,CAAC;QAC9B,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;IAC/B,CAAC;CACF;AAiFC,0CAAe;AA/EjB;;;GAGG;AACH,MAAM,WAAY,SAAQ,KAAK;IAE7B,YAAY,OAAe,EAAE,UAAkB;QAC7C,KAAK,CAAC,OAAO,EAAE,EAAC,KAAK,EAAE,UAAU,EAAC,CAAC,CAAC;QACpC,IAAI,CAAC,OAAO,GAAG,qBAAqB,CAAC,aAAa,EAAE,OAAO,CAAC,CAAC;QAC7D,IAAI,CAAC,IAAI,GAAG,aAAa,CAAC;QAC1B,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;IAC/B,CAAC;CACF;AAiEC,kCAAW;AAlDb;;;GAGG;AACH,MAAM,cAAe,SAAQ,KAAK;IAChC,YACE,OAAe,EACR,IAAa,EACb,MAAe,EACf,YAA6B;QAEpC,KAAK,CAAC,OAAO,CAAC,CAAC;QAJR,SAAI,GAAJ,IAAI,CAAS;QACb,WAAM,GAAN,MAAM,CAAS;QACf,iBAAY,GAAZ,YAAY,CAAiB;IAGtC,CAAC;CACF;AAsCC,wCAAc;AApChB;;;GAGG;AACH,MAAM,uBAAwB,SAAQ,KAAK;IAEzC,YAAY,OAAe,EAAE,UAAkB;QAC7C,KAAK,CAAC,OAAO,EAAE,EAAC,KAAK,EAAE,UAAU,EAAC,CAAC,CAAC;QACpC,IAAI,CAAC,OAAO,GAAG,qBAAqB,CAAC,yBAAyB,EAAE,OAAO,CAAC,CAAC;QACzE,IAAI,CAAC,IAAI,GAAG,yBAAyB,CAAC;QACtC,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;IAC/B,CAAC;CACF;AA0BC,0DAAuB;AAxBzB;;GAEG;AACH,MAAM,oBAAqB,SAAQ,KAAK;IAEtC,YAAY,OAAe,EAAE,UAAkB;QAC7C,KAAK,CAAC,OAAO,EAAE,EAAC,KAAK,EAAE,UAAU,EAAC,CAAC,CAAC;QACpC,IAAI,CAAC,OAAO,GAAG,qBAAqB,CAAC,sBAAsB,EAAE,OAAO,CAAC,CAAC;QACtE,IAAI,CAAC,IAAI,GAAG,sBAAsB,CAAC;QACnC,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;IAC/B,CAAC;CACF;AAcC,oDAAoB;AAZtB,SAAS,qBAAqB,CAC5B,cAAsB,EACtB,OAAe;IAEf,OAAO,aAAa,cAAc,MAAM,OAAO,EAAE,CAAC;AACpD,CAAC"}