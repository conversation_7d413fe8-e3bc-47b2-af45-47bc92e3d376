{"version": 3, "file": "vertex_ai.js", "sourceRoot": "", "sources": ["../../src/vertex_ai.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;GAeG;;;AAEH,oBAAoB;AACpB,6DAAkE;AAElE,qCAAiE;AAQjE,2CAIwB;AACxB,yCAAyC;AACzC,iEAAkE;AAElE;;;;;GAKG;AACH,MAAa,QAAQ;IAOnB;;;;;;;OAOG;IACH,YAAY,IAAgB;QAC1B,MAAM,IAAI,GAAG,yBAAyB,CACpC,IAAI,CAAC,OAAO,EACZ,IAAI,CAAC,iBAAiB,CACvB,CAAC;QACF,IAAI,CAAC,QAAQ,GAAG,eAAe,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC/C,IAAI,CAAC,OAAO,GAAG,cAAc,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAC5C,IAAI,CAAC,UAAU,GAAG,IAAI,gCAAU,CAAC,IAAI,CAAC,CAAC;QACvC,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;QACpC,IAAI,CAAC,OAAO,GAAG,IAAI,eAAe,CAChC,IAAI,CAAC,OAAO,EACZ,IAAI,CAAC,QAAQ,EACb,IAAI,CAAC,UAAU,EACf,IAAI,CAAC,WAAW,CACjB,CAAC;IACJ,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OA4CG;IACH,kBAAkB,CAChB,WAAwB,EACxB,cAA+B;QAE/B,MAAM,wBAAwB,GAA6B;YACzD,KAAK,EAAE,WAAW,CAAC,KAAK;YACxB,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,cAAc,EAAE,WAAW,CAAC,cAAc;YAC1C,gBAAgB,EAAE,WAAW,CAAC,gBAAgB;YAC9C,KAAK,EAAE,WAAW,CAAC,KAAK;YACxB,UAAU,EAAE,WAAW,CAAC,UAAU;YAClC,cAAc,EAAE,cAAc;YAC9B,iBAAiB,EAAE,WAAW,CAAC,iBAAiB;SACjD,CAAC;QACF,OAAO,IAAI,wBAAe,CAAC,wBAAwB,CAAC,CAAC;IACvD,CAAC;IAES,UAAU;QAClB,OAAO,IAAI,CAAC,OAAO,CAAC;IACtB,CAAC;IAES,WAAW;QACnB,OAAO,IAAI,CAAC,QAAQ,CAAC;IACvB,CAAC;CACF;AAxGD,4BAwGC;AAED;;;GAGG;AACH,MAAM,eAAe;IASnB;;;;;;;;;;;;;;OAcG;IACH,YACE,OAAe,EACf,QAAgB,EAChB,UAAsB,EACtB,WAAoB;QAEpB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;QAE/B,IAAI,CAAC,SAAS,GAAG,IAAI,SAAS,CAAC,SAAS,CACtC,IAAI,CAAC,OAAO,EACZ,IAAI,CAAC,QAAQ,EACb,SAAS,EACT,IAAI,CAAC,UAAU,CAChB,CAAC;QACF,IAAI,CAAC,cAAc,GAAG,IAAI,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IACrE,CAAC;IAED;;;;OAIG;IACH,kBAAkB,CAChB,WAAwB,EACxB,cAA+B;QAE/B,MAAM,wBAAwB,GAA6B;YACzD,KAAK,EAAE,WAAW,CAAC,KAAK;YACxB,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,cAAc,EAAE,WAAW,CAAC,cAAc;YAC1C,gBAAgB,EAAE,WAAW,CAAC,gBAAgB;YAC9C,KAAK,EAAE,WAAW,CAAC,KAAK;YACxB,UAAU,EAAE,WAAW,CAAC,UAAU;YAClC,cAAc,EAAE,cAAc;YAC9B,iBAAiB,EAAE,WAAW,CAAC,iBAAiB;SACjD,CAAC;QACF,OAAO,IAAI,+BAAsB,CAAC,wBAAwB,CAAC,CAAC;IAC9D,CAAC;IAED,mCAAmC,CACjC,aAA4B,EAC5B,WAAkC,EAClC,cAA+B;QAE/B,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE;YACvB,MAAM,IAAI,oBAAW,CAAC,6CAA6C,CAAC,CAAC;SACtE;QACD,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE;YACxB,MAAM,IAAI,oBAAW,CAAC,8CAA8C,CAAC,CAAC;SACvE;QACD,0BAA0B,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QAChD;;;WAGG;QACH,MAAM,oBAAoB,GACxB,CAAC,OAAO,EAAE,mBAAmB,CAAC,CAAC;QAEjC,KAAK,MAAM,GAAG,IAAI,oBAAoB,EAAE;YACtC,IACE,CAAA,WAAW,aAAX,WAAW,uBAAX,WAAW,CAAG,GAAG,CAAC;gBAClB,aAAa,CAAC,GAAG,CAAC;gBAClB,CAAA,WAAW,aAAX,WAAW,uBAAX,WAAW,CAAG,GAAG,CAAC,MAAK,aAAa,CAAC,GAAG,CAAC,EACzC;gBACA,IAAI,GAAG,KAAK,OAAO,EAAE;oBACnB,MAAM,eAAe,GAAG,cAAc,CAAC,WAAW,CAAC,GAAG,CAAE,CAAC,CAAC;oBAC1D,MAAM,iBAAiB,GAAG,cAAc,CAAC,aAAa,CAAC,GAAG,CAAE,CAAC,CAAC;oBAC9D,IAAI,eAAe,KAAK,iBAAiB,EAAE;wBACzC,SAAS;qBACV;iBACF;gBACD,MAAM,IAAI,oBAAW,CACnB,wBAAwB,GAAG,4BAA4B;oBACrD,KAAK,WAAW,CAAC,GAAG,CAAC,wBAAwB,aAAa,CAAC,GAAG,CAAC,GAAG,CACrE,CAAC;aACH;SACF;QAED,aAAa,CAAC,IAAI,GAAG,IAAA,uCAAqB,EACxC,IAAI,CAAC,OAAO,EACZ,IAAI,CAAC,QAAQ,EACb,aAAa,CAAC,IAAI,CACnB,CAAC;QACF,MAAM,oBAAoB,GAA6B;YACrD,KAAK,EAAE,aAAa,CAAC,KAAK;YAC1B,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,cAAc,EAAE,WAAW,aAAX,WAAW,uBAAX,WAAW,CAAE,cAAc;YAC3C,gBAAgB,EAAE,WAAW,aAAX,WAAW,uBAAX,WAAW,CAAE,gBAAgB;YAC/C,KAAK,EAAE,aAAa,CAAC,KAAK;YAC1B,UAAU,EAAE,aAAa,CAAC,UAAU;YACpC,cAAc,EAAE,cAAc;YAC9B,iBAAiB,EAAE,aAAa,CAAC,iBAAiB;YAClD,aAAa;SACd,CAAC;QACF,OAAO,IAAI,+BAAsB,CAAC,oBAAoB,CAAC,CAAC;IAC1D,CAAC;CACF;AAED,SAAS,0BAA0B,CAAC,SAAiB;IACnD,IACE,SAAS,CAAC,UAAU,CAAC,SAAS,CAAC;QAC/B,CAAC,SAAS,CAAC,UAAU,CAAC,WAAW,CAAC;YAChC,SAAS,CAAC,QAAQ,CAAC,4BAA4B,CAAC,CAAC;QACnD,CAAC,SAAS,CAAC,QAAQ,CAAC,GAAG,CAAC,EACxB;QACA,OAAO;KACR;IACD,MAAM,IAAI,oBAAW,CACnB,6NAA6N,SAAS,EAAE,CACzO,CAAC;AACJ,CAAC;AAED,SAAS,cAAc,CAAC,SAAiB;IACvC,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;QAC5B,OAAO,SAAS,CAAC;KAClB;IACD,OAAO,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAG,CAAC;AACrC,CAAC;AAED,SAAS,yBAAyB,CAChC,OAAgB,EAChB,iBAAqC;IAErC,IAAI,IAAuB,CAAC;IAC5B,MAAM,aAAa,GAAG,gDAAgD,CAAC;IACvE,IAAI,CAAC,iBAAiB,EAAE;QACtB,IAAI,GAAG;YACL,MAAM,EAAE,aAAa;SACtB,CAAC;QACF,OAAO,IAAI,CAAC;KACb;IACD,IAAI,iBAAiB,CAAC,SAAS,IAAI,iBAAiB,CAAC,SAAS,KAAK,OAAO,EAAE;QAC1E,MAAM,IAAI,KAAK,CACb,8DAA8D,OAAO,8CAA8C,iBAAiB,CAAC,SAAS,EAAE,CACjJ,CAAC;KACH;IACD,IAAI,GAAG,iBAAiB,CAAC;IACzB,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;QAChB,IAAI,CAAC,MAAM,GAAG,aAAa,CAAC;QAC5B,OAAO,IAAI,CAAC;KACb;IACD,IACE,CAAC,OAAO,IAAI,CAAC,MAAM,KAAK,QAAQ,IAAI,IAAI,CAAC,MAAM,KAAK,aAAa,CAAC;QAClE,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,EACtE;QACA,MAAM,IAAI,wBAAe,CACvB,kCAAkC,IAAI,CAAC,MAAM,mCAAmC,aAAa,oBAAoB,aAAa,4EAA4E,CAC3M,CAAC;KACH;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AAED,SAAS,cAAc,CAAC,gBAAyB;IAC/C,MAAM,2BAA2B,GAC/B,+BAA+B;QAC/B,sDAAsD;QACtD,iFAAiF;QACjF,kEAAkE,CAAC;IACrE,IAAI,gBAAgB,EAAE;QACpB,OAAO,gBAAgB,CAAC;KACzB;IACD,MAAM,sBAAsB,GAAG,OAAO,CAAC,GAAG,CAAC,sBAAsB,CAAC,CAAC;IACnE,IAAI,sBAAsB,EAAE;QAC1B,OAAO,sBAAsB,CAAC;KAC/B;IACD,MAAM,IAAI,6BAAoB,CAAC,2BAA2B,CAAC,CAAC;AAC9D,CAAC;AAED,SAAS,eAAe,CAAC,iBAA0B;IACjD,IAAI,iBAAiB,EAAE;QACrB,OAAO,iBAAiB,CAAC;KAC1B;IACD,MAAM,gBAAgB,GACpB,OAAO,CAAC,GAAG,CAAC,qBAAqB,CAAC,IAAI,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;IACvE,IAAI,gBAAgB,EAAE;QACpB,OAAO,gBAAgB,CAAC;KACzB;IACD,OAAO,aAAa,CAAC;AACvB,CAAC"}