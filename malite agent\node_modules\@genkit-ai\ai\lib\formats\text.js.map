{"version": 3, "sources": ["../../src/formats/text.ts"], "sourcesContent": ["/**\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport type { Formatter } from './types';\n\nexport const textFormatter: Formatter<string, string> = {\n  name: 'text',\n  config: {\n    contentType: 'text/plain',\n  },\n  handler: () => {\n    return {\n      parseChunk: (chunk) => {\n        return chunk.text;\n      },\n\n      parseMessage: (message) => {\n        return message.text;\n      },\n    };\n  },\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAkBO,MAAM,gBAA2C;AAAA,EACtD,MAAM;AAAA,EACN,QAAQ;AAAA,IACN,aAAa;AAAA,EACf;AAAA,EACA,SAAS,MAAM;AACb,WAAO;AAAA,MACL,YAAY,CAAC,UAAU;AACrB,eAAO,MAAM;AAAA,MACf;AAAA,MAEA,cAAc,CAAC,YAAY;AACzB,eAAO,QAAQ;AAAA,MACjB;AAAA,IACF;AAAA,EACF;AACF;", "names": []}