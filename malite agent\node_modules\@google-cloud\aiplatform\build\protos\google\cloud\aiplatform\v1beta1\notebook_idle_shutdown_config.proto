// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.aiplatform.v1beta1;

import "google/api/field_behavior.proto";
import "google/protobuf/duration.proto";

option csharp_namespace = "Google.Cloud.AIPlatform.V1Beta1";
option go_package = "cloud.google.com/go/aiplatform/apiv1beta1/aiplatformpb;aiplatformpb";
option java_multiple_files = true;
option java_outer_classname = "NotebookIdleShutdownConfigProto";
option java_package = "com.google.cloud.aiplatform.v1beta1";
option php_namespace = "Google\\Cloud\\AIPlatform\\V1beta1";
option ruby_package = "Google::Cloud::AIPlatform::V1beta1";

// The idle shutdown configuration of NotebookRuntimeTemplate, which contains
// the idle_timeout as required field.
message NotebookIdleShutdownConfig {
  // Required. Duration is accurate to the second. In Notebook, Idle Timeout is
  // accurate to minute so the range of idle_timeout (second) is: 10 * 60 ~ 1440
  // * 60.
  google.protobuf.Duration idle_timeout = 1
      [(google.api.field_behavior) = REQUIRED];

  // Whether Idle Shutdown is disabled in this NotebookRuntimeTemplate.
  bool idle_shutdown_disabled = 2;
}
