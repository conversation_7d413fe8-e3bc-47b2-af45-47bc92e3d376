{"version": 3, "file": "MultiLogRecordProcessor.js", "sourceRoot": "", "sources": ["../../src/MultiLogRecordProcessor.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAEH,OAAO,EAAE,eAAe,EAAE,MAAM,qBAAqB,CAAC;AAKtD;;;GAGG;AACH,MAAM,OAAO,uBAAuB;IAClC,YACkB,UAAgC,EAChC,uBAA+B;QAD/B,eAAU,GAAV,UAAU,CAAsB;QAChC,4BAAuB,GAAvB,uBAAuB,CAAQ;IAC9C,CAAC;IAEG,KAAK,CAAC,UAAU;QACrB,MAAM,OAAO,GAAG,IAAI,CAAC,uBAAuB,CAAC;QAC7C,MAAM,OAAO,CAAC,GAAG,CACf,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,CAC9B,eAAe,CAAC,SAAS,CAAC,UAAU,EAAE,EAAE,OAAO,CAAC,CACjD,CACF,CAAC;IACJ,CAAC;IAEM,MAAM,CAAC,SAAoB,EAAE,OAAiB;QACnD,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE,CACnC,UAAU,CAAC,MAAM,CAAC,SAAS,EAAE,OAAO,CAAC,CACtC,CAAC;IACJ,CAAC;IAEM,KAAK,CAAC,QAAQ;QACnB,MAAM,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;IAC5E,CAAC;CACF", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { callWithTimeout } from '@opentelemetry/core';\nimport type { Context } from '@opentelemetry/api';\nimport type { LogRecordProcessor } from './LogRecordProcessor';\nimport type { LogRecord } from './LogRecord';\n\n/**\n * Implementation of the {@link LogRecordProcessor} that simply forwards all\n * received events to a list of {@link LogRecordProcessor}s.\n */\nexport class MultiLogRecordProcessor implements LogRecordProcessor {\n  constructor(\n    public readonly processors: LogRecordProcessor[],\n    public readonly forceFlushTimeoutMillis: number\n  ) {}\n\n  public async forceFlush(): Promise<void> {\n    const timeout = this.forceFlushTimeoutMillis;\n    await Promise.all(\n      this.processors.map(processor =>\n        callWithTimeout(processor.forceFlush(), timeout)\n      )\n    );\n  }\n\n  public onEmit(logRecord: LogRecord, context?: Context): void {\n    this.processors.forEach(processors =>\n      processors.onEmit(logRecord, context)\n    );\n  }\n\n  public async shutdown(): Promise<void> {\n    await Promise.all(this.processors.map(processor => processor.shutdown()));\n  }\n}\n"]}