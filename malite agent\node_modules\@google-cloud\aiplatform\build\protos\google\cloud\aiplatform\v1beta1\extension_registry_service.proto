// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.aiplatform.v1beta1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/aiplatform/v1beta1/extension.proto";
import "google/cloud/aiplatform/v1beta1/operation.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";

option csharp_namespace = "Google.Cloud.AIPlatform.V1Beta1";
option go_package = "cloud.google.com/go/aiplatform/apiv1beta1/aiplatformpb;aiplatformpb";
option java_multiple_files = true;
option java_outer_classname = "ExtensionRegistryServiceProto";
option java_package = "com.google.cloud.aiplatform.v1beta1";
option php_namespace = "Google\\Cloud\\AIPlatform\\V1beta1";
option ruby_package = "Google::Cloud::AIPlatform::V1beta1";

// A service for managing Vertex AI's Extension registry.
service ExtensionRegistryService {
  option (google.api.default_host) = "aiplatform.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Imports an Extension.
  rpc ImportExtension(ImportExtensionRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1beta1/{parent=projects/*/locations/*}/extensions:import"
      body: "extension"
    };
    option (google.api.method_signature) = "parent,extension";
    option (google.longrunning.operation_info) = {
      response_type: "Extension"
      metadata_type: "ImportExtensionOperationMetadata"
    };
  }

  // Gets an Extension.
  rpc GetExtension(GetExtensionRequest) returns (Extension) {
    option (google.api.http) = {
      get: "/v1beta1/{name=projects/*/locations/*/extensions/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists Extensions in a location.
  rpc ListExtensions(ListExtensionsRequest) returns (ListExtensionsResponse) {
    option (google.api.http) = {
      get: "/v1beta1/{parent=projects/*/locations/*}/extensions"
    };
    option (google.api.method_signature) = "parent";
  }

  // Updates an Extension.
  rpc UpdateExtension(UpdateExtensionRequest) returns (Extension) {
    option (google.api.http) = {
      patch: "/v1beta1/{extension.name=projects/*/locations/*/extensions/*}"
      body: "extension"
    };
    option (google.api.method_signature) = "extension,update_mask";
  }

  // Deletes an Extension.
  rpc DeleteExtension(DeleteExtensionRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1beta1/{name=projects/*/locations/*/extensions/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "DeleteOperationMetadata"
    };
  }
}

// Request message for
// [ExtensionRegistryService.ImportExtension][google.cloud.aiplatform.v1beta1.ExtensionRegistryService.ImportExtension].
message ImportExtensionRequest {
  // Required. The resource name of the Location to import the Extension in.
  // Format: `projects/{project}/locations/{location}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Required. The Extension to import.
  Extension extension = 2 [(google.api.field_behavior) = REQUIRED];
}

// Details of
// [ExtensionRegistryService.ImportExtension][google.cloud.aiplatform.v1beta1.ExtensionRegistryService.ImportExtension]
// operation.
message ImportExtensionOperationMetadata {
  // The common part of the operation metadata.
  GenericOperationMetadata generic_metadata = 1;
}

// Request message for
// [ExtensionRegistryService.GetExtension][google.cloud.aiplatform.v1beta1.ExtensionRegistryService.GetExtension].
message GetExtensionRequest {
  // Required. The name of the Extension resource.
  // Format:
  // `projects/{project}/locations/{location}/extensions/{extension}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/Extension"
    }
  ];
}

// Request message for
// [ExtensionRegistryService.UpdateExtension][google.cloud.aiplatform.v1beta1.ExtensionRegistryService.UpdateExtension].
message UpdateExtensionRequest {
  // Required. The Extension which replaces the resource on the server.
  Extension extension = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. Mask specifying which fields to update.
  // Supported fields:
  //
  //    * `display_name`
  //    * `description`
  //    * `runtime_config`
  //    * `tool_use_examples`
  //    * `manifest.description`
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Request message for
// [ExtensionRegistryService.ListExtensions][google.cloud.aiplatform.v1beta1.ExtensionRegistryService.ListExtensions].
message ListExtensionsRequest {
  // Required. The resource name of the Location to list the Extensions from.
  // Format: `projects/{project}/locations/{location}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Optional. The standard list filter.
  // Supported fields:
  //    * `display_name`
  //    * `create_time`
  //    * `update_time`
  //
  // More detail in [AIP-160](https://google.aip.dev/160).
  string filter = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The standard list page size.
  int32 page_size = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The standard list page token.
  string page_token = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A comma-separated list of fields to order by, sorted in ascending
  // order. Use "desc" after a field name for descending. Supported fields:
  //   * `display_name`
  //   * `create_time`
  //   * `update_time`
  //
  // Example: `display_name, create_time desc`.
  string order_by = 6 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for
// [ExtensionRegistryService.ListExtensions][google.cloud.aiplatform.v1beta1.ExtensionRegistryService.ListExtensions]
message ListExtensionsResponse {
  // List of Extension in the requested page.
  repeated Extension extensions = 1;

  // A token to retrieve the next page of results.
  // Pass to
  // [ListExtensionsRequest.page_token][google.cloud.aiplatform.v1beta1.ListExtensionsRequest.page_token]
  // to obtain that page.
  string next_page_token = 2;
}

// Request message for
// [ExtensionRegistryService.DeleteExtension][google.cloud.aiplatform.v1beta1.ExtensionRegistryService.DeleteExtension].
message DeleteExtensionRequest {
  // Required. The name of the Extension resource to be deleted.
  // Format:
  // `projects/{project}/locations/{location}/extensions/{extension}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/Extension"
    }
  ];
}
