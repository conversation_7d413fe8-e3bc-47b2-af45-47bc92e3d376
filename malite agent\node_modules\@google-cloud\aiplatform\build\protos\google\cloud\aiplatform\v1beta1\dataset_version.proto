// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.aiplatform.v1beta1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/struct.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.AIPlatform.V1Beta1";
option go_package = "cloud.google.com/go/aiplatform/apiv1beta1/aiplatformpb;aiplatformpb";
option java_multiple_files = true;
option java_outer_classname = "DatasetVersionProto";
option java_package = "com.google.cloud.aiplatform.v1beta1";
option php_namespace = "Google\\Cloud\\AIPlatform\\V1beta1";
option ruby_package = "Google::Cloud::AIPlatform::V1beta1";

// Describes the dataset version.
message DatasetVersion {
  option (google.api.resource) = {
    type: "aiplatform.googleapis.com/DatasetVersion"
    pattern: "projects/{project}/locations/{location}/datasets/{dataset}/datasetVersions/{dataset_version}"
  };

  // Output only. Identifier. The resource name of the DatasetVersion.
  string name = 1 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.field_behavior) = IDENTIFIER
  ];

  // Output only. Timestamp when this DatasetVersion was created.
  google.protobuf.Timestamp create_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Timestamp when this DatasetVersion was last updated.
  google.protobuf.Timestamp update_time = 6
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Used to perform consistent read-modify-write updates. If not set, a blind
  // "overwrite" update happens.
  string etag = 3;

  // Output only. Name of the associated BigQuery dataset.
  string big_query_dataset_name = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // The user-defined name of the DatasetVersion.
  // The name can be up to 128 characters long and can consist of any UTF-8
  // characters.
  string display_name = 7;

  // Required. Output only. Additional information about the DatasetVersion.
  google.protobuf.Value metadata = 8 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.field_behavior) = REQUIRED
  ];

  // Output only. Reference to the public base model last used by the dataset
  // version. Only set for prompt dataset versions.
  string model_reference = 9 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Reserved for future use.
  bool satisfies_pzs = 10 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Reserved for future use.
  bool satisfies_pzi = 11 [(google.api.field_behavior) = OUTPUT_ONLY];
}
