// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.aiplatform.v1beta1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/aiplatform/v1beta1/feature_online_store.proto";
import "google/cloud/aiplatform/v1beta1/feature_view.proto";
import "google/cloud/aiplatform/v1beta1/feature_view_sync.proto";
import "google/cloud/aiplatform/v1beta1/operation.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";

option csharp_namespace = "Google.Cloud.AIPlatform.V1Beta1";
option go_package = "cloud.google.com/go/aiplatform/apiv1beta1/aiplatformpb;aiplatformpb";
option java_multiple_files = true;
option java_outer_classname = "FeatureOnlineStoreAdminServiceProto";
option java_package = "com.google.cloud.aiplatform.v1beta1";
option php_namespace = "Google\\Cloud\\AIPlatform\\V1beta1";
option ruby_package = "Google::Cloud::AIPlatform::V1beta1";

// The service that handles CRUD and List for resources for
// FeatureOnlineStore.
service FeatureOnlineStoreAdminService {
  option (google.api.default_host) = "aiplatform.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Creates a new FeatureOnlineStore in a given project and location.
  rpc CreateFeatureOnlineStore(CreateFeatureOnlineStoreRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1beta1/{parent=projects/*/locations/*}/featureOnlineStores"
      body: "feature_online_store"
    };
    option (google.api.method_signature) =
        "parent,feature_online_store,feature_online_store_id";
    option (google.longrunning.operation_info) = {
      response_type: "FeatureOnlineStore"
      metadata_type: "CreateFeatureOnlineStoreOperationMetadata"
    };
  }

  // Gets details of a single FeatureOnlineStore.
  rpc GetFeatureOnlineStore(GetFeatureOnlineStoreRequest)
      returns (FeatureOnlineStore) {
    option (google.api.http) = {
      get: "/v1beta1/{name=projects/*/locations/*/featureOnlineStores/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists FeatureOnlineStores in a given project and location.
  rpc ListFeatureOnlineStores(ListFeatureOnlineStoresRequest)
      returns (ListFeatureOnlineStoresResponse) {
    option (google.api.http) = {
      get: "/v1beta1/{parent=projects/*/locations/*}/featureOnlineStores"
    };
    option (google.api.method_signature) = "parent";
  }

  // Updates the parameters of a single FeatureOnlineStore.
  rpc UpdateFeatureOnlineStore(UpdateFeatureOnlineStoreRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1beta1/{feature_online_store.name=projects/*/locations/*/featureOnlineStores/*}"
      body: "feature_online_store"
    };
    option (google.api.method_signature) = "feature_online_store,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "FeatureOnlineStore"
      metadata_type: "UpdateFeatureOnlineStoreOperationMetadata"
    };
  }

  // Deletes a single FeatureOnlineStore. The FeatureOnlineStore must not
  // contain any FeatureViews.
  rpc DeleteFeatureOnlineStore(DeleteFeatureOnlineStoreRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1beta1/{name=projects/*/locations/*/featureOnlineStores/*}"
    };
    option (google.api.method_signature) = "name,force";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "DeleteOperationMetadata"
    };
  }

  // Creates a new FeatureView in a given FeatureOnlineStore.
  rpc CreateFeatureView(CreateFeatureViewRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1beta1/{parent=projects/*/locations/*/featureOnlineStores/*}/featureViews"
      body: "feature_view"
    };
    option (google.api.method_signature) =
        "parent,feature_view,feature_view_id";
    option (google.longrunning.operation_info) = {
      response_type: "FeatureView"
      metadata_type: "CreateFeatureViewOperationMetadata"
    };
  }

  // Gets details of a single FeatureView.
  rpc GetFeatureView(GetFeatureViewRequest) returns (FeatureView) {
    option (google.api.http) = {
      get: "/v1beta1/{name=projects/*/locations/*/featureOnlineStores/*/featureViews/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists FeatureViews in a given FeatureOnlineStore.
  rpc ListFeatureViews(ListFeatureViewsRequest)
      returns (ListFeatureViewsResponse) {
    option (google.api.http) = {
      get: "/v1beta1/{parent=projects/*/locations/*/featureOnlineStores/*}/featureViews"
    };
    option (google.api.method_signature) = "parent";
  }

  // Updates the parameters of a single FeatureView.
  rpc UpdateFeatureView(UpdateFeatureViewRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1beta1/{feature_view.name=projects/*/locations/*/featureOnlineStores/*/featureViews/*}"
      body: "feature_view"
    };
    option (google.api.method_signature) = "feature_view,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "FeatureView"
      metadata_type: "UpdateFeatureViewOperationMetadata"
    };
  }

  // Deletes a single FeatureView.
  rpc DeleteFeatureView(DeleteFeatureViewRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1beta1/{name=projects/*/locations/*/featureOnlineStores/*/featureViews/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "DeleteOperationMetadata"
    };
  }

  // Triggers on-demand sync for the FeatureView.
  rpc SyncFeatureView(SyncFeatureViewRequest)
      returns (SyncFeatureViewResponse) {
    option (google.api.http) = {
      post: "/v1beta1/{feature_view=projects/*/locations/*/featureOnlineStores/*/featureViews/*}:sync"
      body: "*"
    };
    option (google.api.method_signature) = "feature_view";
  }

  // Gets details of a single FeatureViewSync.
  rpc GetFeatureViewSync(GetFeatureViewSyncRequest) returns (FeatureViewSync) {
    option (google.api.http) = {
      get: "/v1beta1/{name=projects/*/locations/*/featureOnlineStores/*/featureViews/*/featureViewSyncs/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists FeatureViewSyncs in a given FeatureView.
  rpc ListFeatureViewSyncs(ListFeatureViewSyncsRequest)
      returns (ListFeatureViewSyncsResponse) {
    option (google.api.http) = {
      get: "/v1beta1/{parent=projects/*/locations/*/featureOnlineStores/*/featureViews/*}/featureViewSyncs"
    };
    option (google.api.method_signature) = "parent";
  }
}

// Request message for
// [FeatureOnlineStoreAdminService.CreateFeatureOnlineStore][google.cloud.aiplatform.v1beta1.FeatureOnlineStoreAdminService.CreateFeatureOnlineStore].
message CreateFeatureOnlineStoreRequest {
  // Required. The resource name of the Location to create FeatureOnlineStores.
  // Format:
  // `projects/{project}/locations/{location}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "aiplatform.googleapis.com/FeatureOnlineStore"
    }
  ];

  // Required. The FeatureOnlineStore to create.
  FeatureOnlineStore feature_online_store = 2
      [(google.api.field_behavior) = REQUIRED];

  // Required. The ID to use for this FeatureOnlineStore, which will become the
  // final component of the FeatureOnlineStore's resource name.
  //
  // This value may be up to 60 characters, and valid characters are
  // `[a-z0-9_]`. The first character cannot be a number.
  //
  // The value must be unique within the project and location.
  string feature_online_store_id = 3 [(google.api.field_behavior) = REQUIRED];
}

// Request message for
// [FeatureOnlineStoreAdminService.GetFeatureOnlineStore][google.cloud.aiplatform.v1beta1.FeatureOnlineStoreAdminService.GetFeatureOnlineStore].
message GetFeatureOnlineStoreRequest {
  // Required. The name of the FeatureOnlineStore resource.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/FeatureOnlineStore"
    }
  ];
}

// Request message for
// [FeatureOnlineStoreAdminService.ListFeatureOnlineStores][google.cloud.aiplatform.v1beta1.FeatureOnlineStoreAdminService.ListFeatureOnlineStores].
message ListFeatureOnlineStoresRequest {
  // Required. The resource name of the Location to list FeatureOnlineStores.
  // Format:
  // `projects/{project}/locations/{location}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "aiplatform.googleapis.com/FeatureOnlineStore"
    }
  ];

  // Lists the FeatureOnlineStores that match the filter expression. The
  // following fields are supported:
  //
  // * `create_time`: Supports `=`, `!=`, `<`, `>`, `<=`, and `>=` comparisons.
  // Values must be
  //   in RFC 3339 format.
  // * `update_time`: Supports `=`, `!=`, `<`, `>`, `<=`, and `>=` comparisons.
  // Values must be
  //   in RFC 3339 format.
  // * `labels`: Supports key-value equality and key presence.
  //
  // Examples:
  //
  // * `create_time > "2020-01-01" OR update_time > "2020-01-01"`
  //    FeatureOnlineStores created or updated after 2020-01-01.
  // * `labels.env = "prod"`
  //    FeatureOnlineStores with label "env" set to "prod".
  string filter = 2;

  // The maximum number of FeatureOnlineStores to return. The service may return
  // fewer than this value. If unspecified, at most 100 FeatureOnlineStores will
  // be returned. The maximum value is 100; any value greater than 100 will be
  // coerced to 100.
  int32 page_size = 3;

  // A page token, received from a previous
  // [FeatureOnlineStoreAdminService.ListFeatureOnlineStores][google.cloud.aiplatform.v1beta1.FeatureOnlineStoreAdminService.ListFeatureOnlineStores]
  // call. Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to
  // [FeatureOnlineStoreAdminService.ListFeatureOnlineStores][google.cloud.aiplatform.v1beta1.FeatureOnlineStoreAdminService.ListFeatureOnlineStores]
  // must match the call that provided the page token.
  string page_token = 4;

  // A comma-separated list of fields to order by, sorted in ascending order.
  // Use "desc" after a field name for descending.
  // Supported Fields:
  //
  //   * `create_time`
  //   * `update_time`
  string order_by = 5;
}

// Response message for
// [FeatureOnlineStoreAdminService.ListFeatureOnlineStores][google.cloud.aiplatform.v1beta1.FeatureOnlineStoreAdminService.ListFeatureOnlineStores].
message ListFeatureOnlineStoresResponse {
  // The FeatureOnlineStores matching the request.
  repeated FeatureOnlineStore feature_online_stores = 1;

  // A token, which can be sent as
  // [ListFeatureOnlineStoresRequest.page_token][google.cloud.aiplatform.v1beta1.ListFeatureOnlineStoresRequest.page_token]
  // to retrieve the next page. If this field is omitted, there are no
  // subsequent pages.
  string next_page_token = 2;
}

// Request message for
// [FeatureOnlineStoreAdminService.UpdateFeatureOnlineStore][google.cloud.aiplatform.v1beta1.FeatureOnlineStoreAdminService.UpdateFeatureOnlineStore].
message UpdateFeatureOnlineStoreRequest {
  // Required. The FeatureOnlineStore's `name` field is used to identify the
  // FeatureOnlineStore to be updated. Format:
  // `projects/{project}/locations/{location}/featureOnlineStores/{feature_online_store}`
  FeatureOnlineStore feature_online_store = 1
      [(google.api.field_behavior) = REQUIRED];

  // Field mask is used to specify the fields to be overwritten in the
  // FeatureOnlineStore resource by the update.
  // The fields specified in the update_mask are relative to the resource, not
  // the full request. A field will be overwritten if it is in the mask. If the
  // user does not provide a mask then only the non-empty fields present in the
  // request will be overwritten. Set the update_mask to `*` to override all
  // fields.
  //
  // Updatable fields:
  //
  //   * `labels`
  //   * `description`
  //   * `bigtable`
  //   * `bigtable.auto_scaling`
  //   * `bigtable.enable_multi_region_replica`
  google.protobuf.FieldMask update_mask = 2;
}

// Request message for
// [FeatureOnlineStoreAdminService.DeleteFeatureOnlineStore][google.cloud.aiplatform.v1beta1.FeatureOnlineStoreAdminService.DeleteFeatureOnlineStore].
message DeleteFeatureOnlineStoreRequest {
  // Required. The name of the FeatureOnlineStore to be deleted.
  // Format:
  // `projects/{project}/locations/{location}/featureOnlineStores/{feature_online_store}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/FeatureOnlineStore"
    }
  ];

  // If set to true, any FeatureViews and Features for this FeatureOnlineStore
  // will also be deleted. (Otherwise, the request will only work if the
  // FeatureOnlineStore has no FeatureViews.)
  bool force = 2;
}

// Request message for
// [FeatureOnlineStoreAdminService.CreateFeatureView][google.cloud.aiplatform.v1beta1.FeatureOnlineStoreAdminService.CreateFeatureView].
message CreateFeatureViewRequest {
  // Required. The resource name of the FeatureOnlineStore to create
  // FeatureViews. Format:
  // `projects/{project}/locations/{location}/featureOnlineStores/{feature_online_store}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/FeatureOnlineStore"
    }
  ];

  // Required. The FeatureView to create.
  FeatureView feature_view = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The ID to use for the FeatureView, which will become the final
  // component of the FeatureView's resource name.
  //
  // This value may be up to 60 characters, and valid characters are
  // `[a-z0-9_]`. The first character cannot be a number.
  //
  // The value must be unique within a FeatureOnlineStore.
  string feature_view_id = 3 [(google.api.field_behavior) = REQUIRED];

  // Immutable. If set to true, one on demand sync will be run immediately,
  // regardless whether the
  // [FeatureView.sync_config][google.cloud.aiplatform.v1beta1.FeatureView.sync_config]
  // is configured or not.
  bool run_sync_immediately = 4 [(google.api.field_behavior) = IMMUTABLE];
}

// Request message for
// [FeatureOnlineStoreAdminService.GetFeatureView][google.cloud.aiplatform.v1beta1.FeatureOnlineStoreAdminService.GetFeatureView].
message GetFeatureViewRequest {
  // Required. The name of the FeatureView resource.
  // Format:
  // `projects/{project}/locations/{location}/featureOnlineStores/{feature_online_store}/featureViews/{feature_view}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/FeatureView"
    }
  ];
}

// Request message for
// [FeatureOnlineStoreAdminService.ListFeatureViews][google.cloud.aiplatform.v1beta1.FeatureOnlineStoreAdminService.ListFeatureViews].
message ListFeatureViewsRequest {
  // Required. The resource name of the FeatureOnlineStore to list FeatureViews.
  // Format:
  // `projects/{project}/locations/{location}/featureOnlineStores/{feature_online_store}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "aiplatform.googleapis.com/FeatureView"
    }
  ];

  // Lists the FeatureViews that match the filter expression. The following
  // filters are supported:
  //
  // * `create_time`: Supports `=`, `!=`, `<`, `>`, `>=`, and `<=` comparisons.
  // Values must be in RFC 3339 format.
  // * `update_time`: Supports `=`, `!=`, `<`, `>`, `>=`, and `<=` comparisons.
  // Values must be in RFC 3339 format.
  // * `labels`: Supports key-value equality as well as key presence.
  //
  // Examples:
  //
  // * `create_time > \"2020-01-31T15:30:00.000000Z\" OR
  //      update_time > \"2020-01-31T15:30:00.000000Z\"` --> FeatureViews
  //      created or updated after 2020-01-31T15:30:00.000000Z.
  // * `labels.active = yes AND labels.env = prod` --> FeatureViews having both
  //     (active: yes) and (env: prod) labels.
  // * `labels.env: *` --> Any FeatureView which has a label with 'env' as the
  //   key.
  string filter = 2;

  // The maximum number of FeatureViews to return. The service may return fewer
  // than this value. If unspecified, at most 1000 FeatureViews will be
  // returned. The maximum value is 1000; any value greater than 1000 will be
  // coerced to 1000.
  int32 page_size = 3;

  // A page token, received from a previous
  // [FeatureOnlineStoreAdminService.ListFeatureViews][google.cloud.aiplatform.v1beta1.FeatureOnlineStoreAdminService.ListFeatureViews]
  // call. Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to
  // [FeatureOnlineStoreAdminService.ListFeatureViews][google.cloud.aiplatform.v1beta1.FeatureOnlineStoreAdminService.ListFeatureViews]
  // must match the call that provided the page token.
  string page_token = 4;

  // A comma-separated list of fields to order by, sorted in ascending order.
  // Use "desc" after a field name for descending.
  //
  // Supported fields:
  //
  //   * `feature_view_id`
  //   * `create_time`
  //   * `update_time`
  string order_by = 5;
}

// Response message for
// [FeatureOnlineStoreAdminService.ListFeatureViews][google.cloud.aiplatform.v1beta1.FeatureOnlineStoreAdminService.ListFeatureViews].
message ListFeatureViewsResponse {
  // The FeatureViews matching the request.
  repeated FeatureView feature_views = 1;

  // A token, which can be sent as
  // [ListFeatureViewsRequest.page_token][google.cloud.aiplatform.v1beta1.ListFeatureViewsRequest.page_token]
  // to retrieve the next page. If this field is omitted, there are no
  // subsequent pages.
  string next_page_token = 2;
}

// Request message for
// [FeatureOnlineStoreAdminService.UpdateFeatureView][google.cloud.aiplatform.v1beta1.FeatureOnlineStoreAdminService.UpdateFeatureView].
message UpdateFeatureViewRequest {
  // Required. The FeatureView's `name` field is used to identify the
  // FeatureView to be updated. Format:
  // `projects/{project}/locations/{location}/featureOnlineStores/{feature_online_store}/featureViews/{feature_view}`
  FeatureView feature_view = 1 [(google.api.field_behavior) = REQUIRED];

  // Field mask is used to specify the fields to be overwritten in the
  // FeatureView resource by the update.
  // The fields specified in the update_mask are relative to the resource, not
  // the full request. A field will be overwritten if it is in the mask. If the
  // user does not provide a mask then only the non-empty fields present in the
  // request will be overwritten. Set the update_mask to `*` to override all
  // fields.
  //
  // Updatable fields:
  //
  //   * `labels`
  //   * `service_agent_type`
  //   * `big_query_source`
  //   * `big_query_source.uri`
  //   * `big_query_source.entity_id_columns`
  //   * `feature_registry_source`
  //   * `feature_registry_source.feature_groups`
  //   * `sync_config`
  //   * `sync_config.cron`
  google.protobuf.FieldMask update_mask = 2;
}

// Request message for [FeatureOnlineStoreAdminService.DeleteFeatureViews][].
message DeleteFeatureViewRequest {
  // Required. The name of the FeatureView to be deleted.
  // Format:
  // `projects/{project}/locations/{location}/featureOnlineStores/{feature_online_store}/featureViews/{feature_view}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/FeatureView"
    }
  ];
}

// Details of operations that perform create FeatureOnlineStore.
message CreateFeatureOnlineStoreOperationMetadata {
  // Operation metadata for FeatureOnlineStore.
  GenericOperationMetadata generic_metadata = 1;
}

// Details of operations that perform update FeatureOnlineStore.
message UpdateFeatureOnlineStoreOperationMetadata {
  // Operation metadata for FeatureOnlineStore.
  GenericOperationMetadata generic_metadata = 1;
}

// Details of operations that perform create FeatureView.
message CreateFeatureViewOperationMetadata {
  // Operation metadata for FeatureView Create.
  GenericOperationMetadata generic_metadata = 1;
}

// Details of operations that perform update FeatureView.
message UpdateFeatureViewOperationMetadata {
  // Operation metadata for FeatureView Update.
  GenericOperationMetadata generic_metadata = 1;
}

// Request message for
// [FeatureOnlineStoreAdminService.SyncFeatureView][google.cloud.aiplatform.v1beta1.FeatureOnlineStoreAdminService.SyncFeatureView].
message SyncFeatureViewRequest {
  // Required. Format:
  // `projects/{project}/locations/{location}/featureOnlineStores/{feature_online_store}/featureViews/{feature_view}`
  string feature_view = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/FeatureView"
    }
  ];
}

// Response message for
// [FeatureOnlineStoreAdminService.SyncFeatureView][google.cloud.aiplatform.v1beta1.FeatureOnlineStoreAdminService.SyncFeatureView].
message SyncFeatureViewResponse {
  // Format:
  // `projects/{project}/locations/{location}/featureOnlineStores/{feature_online_store}/featureViews/{feature_view}/featureViewSyncs/{feature_view_sync}`
  string feature_view_sync = 1;
}

// Request message for
// [FeatureOnlineStoreAdminService.GetFeatureViewSync][google.cloud.aiplatform.v1beta1.FeatureOnlineStoreAdminService.GetFeatureViewSync].
message GetFeatureViewSyncRequest {
  // Required. The name of the FeatureViewSync resource.
  // Format:
  // `projects/{project}/locations/{location}/featureOnlineStores/{feature_online_store}/featureViews/{feature_view}/featureViewSyncs/{feature_view_sync}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/FeatureViewSync"
    }
  ];
}

// Request message for
// [FeatureOnlineStoreAdminService.ListFeatureViewSyncs][google.cloud.aiplatform.v1beta1.FeatureOnlineStoreAdminService.ListFeatureViewSyncs].
message ListFeatureViewSyncsRequest {
  // Required. The resource name of the FeatureView to list FeatureViewSyncs.
  // Format:
  // `projects/{project}/locations/{location}/featureOnlineStores/{feature_online_store}/featureViews/{feature_view}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/FeatureView"
    }
  ];

  // Lists the FeatureViewSyncs that match the filter expression. The following
  // filters are supported:
  //
  // * `create_time`: Supports `=`, `!=`, `<`, `>`, `>=`, and `<=` comparisons.
  // Values must be in RFC 3339 format.
  //
  // Examples:
  //
  // * `create_time > \"2020-01-31T15:30:00.000000Z\"` --> FeatureViewSyncs
  //      created after 2020-01-31T15:30:00.000000Z.
  string filter = 2;

  // The maximum number of FeatureViewSyncs to return. The service may return
  // fewer than this value. If unspecified, at most 1000 FeatureViewSyncs will
  // be returned. The maximum value is 1000; any value greater than 1000 will be
  // coerced to 1000.
  int32 page_size = 3;

  // A page token, received from a previous
  // [FeatureOnlineStoreAdminService.ListFeatureViewSyncs][google.cloud.aiplatform.v1beta1.FeatureOnlineStoreAdminService.ListFeatureViewSyncs]
  // call. Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to
  // [FeatureOnlineStoreAdminService.ListFeatureViewSyncs][google.cloud.aiplatform.v1beta1.FeatureOnlineStoreAdminService.ListFeatureViewSyncs]
  // must match the call that provided the page token.
  string page_token = 4;

  // A comma-separated list of fields to order by, sorted in ascending order.
  // Use "desc" after a field name for descending.
  //
  // Supported fields:
  //
  //   * `create_time`
  string order_by = 5;
}

// Response message for
// [FeatureOnlineStoreAdminService.ListFeatureViewSyncs][google.cloud.aiplatform.v1beta1.FeatureOnlineStoreAdminService.ListFeatureViewSyncs].
message ListFeatureViewSyncsResponse {
  // The FeatureViewSyncs matching the request.
  repeated FeatureViewSync feature_view_syncs = 1;

  // A token, which can be sent as
  // [ListFeatureViewSyncsRequest.page_token][google.cloud.aiplatform.v1beta1.ListFeatureViewSyncsRequest.page_token]
  // to retrieve the next page. If this field is omitted, there are no
  // subsequent pages.
  string next_page_token = 2;
}
