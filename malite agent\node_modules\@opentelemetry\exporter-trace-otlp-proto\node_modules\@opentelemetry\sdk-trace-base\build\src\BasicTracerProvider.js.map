{"version": 3, "file": "BasicTracerProvider.js", "sourceRoot": "", "sources": ["../../src/BasicTracerProvider.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,4CAO4B;AAC5B,8CAM6B;AAC7B,wDAA+D;AAC/D,wBAA0C;AAC1C,qCAA6C;AAC7C,6DAA0D;AAC1D,kEAA+D;AAG/D,yCAAgD;AAChD,uCAA8C;AAK9C,IAAY,eAKX;AALD,WAAY,eAAe;IACzB,6DAAU,CAAA;IACV,2DAAS,CAAA;IACT,uDAAO,CAAA;IACP,iEAAY,CAAA;AACd,CAAC,EALW,eAAe,GAAf,uBAAe,KAAf,uBAAe,QAK1B;AAED;;GAEG;AACH,MAAa,mBAAmB;IAqB9B,YAAY,SAAuB,EAAE;;QANpB,8BAAyB,GAAoB,EAAE,CAAC;QAChD,aAAQ,GAAwB,IAAI,GAAG,EAAE,CAAC;QAMzD,MAAM,YAAY,GAAG,IAAA,YAAK,EACxB,EAAE,EACF,IAAA,0BAAiB,GAAE,EACnB,IAAA,2BAAiB,EAAC,MAAM,CAAC,CAC1B,CAAC;QACF,IAAI,CAAC,QAAQ,GAAG,MAAA,YAAY,CAAC,QAAQ,mCAAI,oBAAQ,CAAC,KAAK,EAAE,CAAC;QAC1D,IAAI,CAAC,QAAQ,GAAG,oBAAQ,CAAC,OAAO,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACxD,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,YAAY,EAAE;YAC7C,QAAQ,EAAE,IAAI,CAAC,QAAQ;SACxB,CAAC,CAAC;QAEH,MAAM,eAAe,GAAG,IAAI,CAAC,qBAAqB,EAAE,CAAC;QACrD,IAAI,eAAe,KAAK,SAAS,EAAE;YACjC,MAAM,cAAc,GAAG,IAAI,6BAAkB,CAAC,eAAe,CAAC,CAAC;YAC/D,IAAI,CAAC,mBAAmB,GAAG,cAAc,CAAC;SAC3C;aAAM;YACL,IAAI,CAAC,mBAAmB,GAAG,IAAI,qCAAiB,EAAE,CAAC;SACpD;IACH,CAAC;IAED,SAAS,CACP,IAAY,EACZ,OAAgB,EAChB,OAAgC;QAEhC,MAAM,GAAG,GAAG,GAAG,IAAI,IAAI,OAAO,IAAI,EAAE,IAAI,CAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,SAAS,KAAI,EAAE,EAAE,CAAC;QACnE,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;YAC3B,IAAI,CAAC,QAAQ,CAAC,GAAG,CACf,GAAG,EACH,IAAI,SAAM,CACR,EAAE,IAAI,EAAE,OAAO,EAAE,SAAS,EAAE,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,SAAS,EAAE,EAChD,IAAI,CAAC,OAAO,EACZ,IAAI,CACL,CACF,CAAC;SACH;QAED,oEAAoE;QACpE,OAAO,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAE,CAAC;IACjC,CAAC;IAED;;;OAGG;IACH,gBAAgB,CAAC,aAA4B;QAC3C,IAAI,IAAI,CAAC,yBAAyB,CAAC,MAAM,KAAK,CAAC,EAAE;YAC/C,yEAAyE;YACzE,4BAA4B;YAC5B,IAAI,CAAC,mBAAmB;iBACrB,QAAQ,EAAE;iBACV,KAAK,CAAC,GAAG,CAAC,EAAE,CACX,UAAI,CAAC,KAAK,CACR,uDAAuD,EACvD,GAAG,CACJ,CACF,CAAC;SACL;QACD,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QACnD,IAAI,CAAC,mBAAmB,GAAG,IAAI,uCAAkB,CAC/C,IAAI,CAAC,yBAAyB,CAC/B,CAAC;IACJ,CAAC;IAED,sBAAsB;QACpB,OAAO,IAAI,CAAC,mBAAmB,CAAC;IAClC,CAAC;IAED;;;;;;OAMG;IACH,QAAQ,CAAC,SAAgC,EAAE;QACzC,WAAK,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC;QACpC,IAAI,MAAM,CAAC,UAAU,KAAK,SAAS,EAAE;YACnC,MAAM,CAAC,UAAU,GAAG,IAAI,CAAC,uBAAuB,EAAE,CAAC;SACpD;QAED,IAAI,MAAM,CAAC,cAAc,EAAE;YACzB,aAAO,CAAC,uBAAuB,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;SACxD;QAED,IAAI,MAAM,CAAC,UAAU,EAAE;YACrB,iBAAW,CAAC,mBAAmB,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;SACpD;IACH,CAAC;IAED,UAAU;QACR,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,uBAAuB,CAAC;QACrD,MAAM,QAAQ,GAAG,IAAI,CAAC,yBAAyB,CAAC,GAAG,CACjD,CAAC,aAA4B,EAAE,EAAE;YAC/B,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE;gBAC3B,IAAI,KAAsB,CAAC;gBAC3B,MAAM,eAAe,GAAG,UAAU,CAAC,GAAG,EAAE;oBACtC,OAAO,CACL,IAAI,KAAK,CACP,6DAA6D,OAAO,KAAK,CAC1E,CACF,CAAC;oBACF,KAAK,GAAG,eAAe,CAAC,OAAO,CAAC;gBAClC,CAAC,EAAE,OAAO,CAAC,CAAC;gBAEZ,aAAa;qBACV,UAAU,EAAE;qBACZ,IAAI,CAAC,GAAG,EAAE;oBACT,YAAY,CAAC,eAAe,CAAC,CAAC;oBAC9B,IAAI,KAAK,KAAK,eAAe,CAAC,OAAO,EAAE;wBACrC,KAAK,GAAG,eAAe,CAAC,QAAQ,CAAC;wBACjC,OAAO,CAAC,KAAK,CAAC,CAAC;qBAChB;gBACH,CAAC,CAAC;qBACD,KAAK,CAAC,KAAK,CAAC,EAAE;oBACb,YAAY,CAAC,eAAe,CAAC,CAAC;oBAC9B,KAAK,GAAG,eAAe,CAAC,KAAK,CAAC;oBAC9B,OAAO,CAAC,KAAK,CAAC,CAAC;gBACjB,CAAC,CAAC,CAAC;YACP,CAAC,CAAC,CAAC;QACL,CAAC,CACF,CAAC;QAEF,OAAO,IAAI,OAAO,CAAO,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YAC3C,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC;iBAClB,IAAI,CAAC,OAAO,CAAC,EAAE;gBACd,MAAM,MAAM,GAAG,OAAO,CAAC,MAAM,CAC3B,MAAM,CAAC,EAAE,CAAC,MAAM,KAAK,eAAe,CAAC,QAAQ,CAC9C,CAAC;gBACF,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE;oBACrB,MAAM,CAAC,MAAM,CAAC,CAAC;iBAChB;qBAAM;oBACL,OAAO,EAAE,CAAC;iBACX;YACH,CAAC,CAAC;iBACD,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC;IACL,CAAC;IAED,QAAQ;QACN,OAAO,IAAI,CAAC,mBAAmB,CAAC,QAAQ,EAAE,CAAC;IAC7C,CAAC;IAED;;;;;;OAMG;IACO,cAAc,CAAC,IAAY;;QACnC,OAAO,MACL,IAAI,CAAC,WACN,CAAC,sBAAsB,CAAC,GAAG,CAAC,IAAI,CAAC,2CAAI,CAAC;IACzC,CAAC;IAES,gBAAgB,CAAC,IAAY;;QACrC,OAAO,MACL,IAAI,CAAC,WACN,CAAC,oBAAoB,CAAC,GAAG,CAAC,IAAI,CAAC,2CAAI,CAAC;IACvC,CAAC;IAES,uBAAuB;QAC/B,sDAAsD;QACtD,MAAM,qBAAqB,GAAG,KAAK,CAAC,IAAI,CACtC,IAAI,GAAG,CAAC,IAAA,aAAM,GAAE,CAAC,gBAAgB,CAAC,CACnC,CAAC;QAEF,MAAM,WAAW,GAAG,qBAAqB,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;YACnD,MAAM,UAAU,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;YAC7C,IAAI,CAAC,UAAU,EAAE;gBACf,UAAI,CAAC,IAAI,CACP,eAAe,IAAI,0DAA0D,CAC9E,CAAC;aACH;YAED,OAAO,UAAU,CAAC;QACpB,CAAC,CAAC,CAAC;QACH,MAAM,gBAAgB,GAAG,WAAW,CAAC,MAAM,CACzC,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE;YACb,IAAI,IAAI,EAAE;gBACR,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;aACjB;YACD,OAAO,IAAI,CAAC;QACd,CAAC,EACD,EAAE,CACH,CAAC;QAEF,IAAI,gBAAgB,CAAC,MAAM,KAAK,CAAC,EAAE;YACjC,OAAO;SACR;aAAM,IAAI,qBAAqB,CAAC,MAAM,KAAK,CAAC,EAAE;YAC7C,OAAO,gBAAgB,CAAC,CAAC,CAAC,CAAC;SAC5B;aAAM;YACL,OAAO,IAAI,0BAAmB,CAAC;gBAC7B,WAAW,EAAE,gBAAgB;aAC9B,CAAC,CAAC;SACJ;IACH,CAAC;IAES,qBAAqB;QAC7B,MAAM,YAAY,GAAG,IAAA,aAAM,GAAE,CAAC,oBAAoB,CAAC;QACnD,IAAI,YAAY,KAAK,MAAM,IAAI,YAAY,KAAK,EAAE;YAAE,OAAO;QAC3D,MAAM,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC,CAAC;QACrD,IAAI,CAAC,QAAQ,EAAE;YACb,UAAI,CAAC,KAAK,CACR,aAAa,YAAY,0DAA0D,CACpF,CAAC;SACH;QACD,OAAO,QAAQ,CAAC;IAClB,CAAC;;AAvOH,kDAwOC;AAvO2B,0CAAsB,GAAG,IAAI,GAAG,CAGxD;IACA,CAAC,cAAc,EAAE,GAAG,EAAE,CAAC,IAAI,gCAAyB,EAAE,CAAC;IACvD,CAAC,SAAS,EAAE,GAAG,EAAE,CAAC,IAAI,2BAAoB,EAAE,CAAC;CAC9C,CAAC,CAAC;AAEuB,wCAAoB,GAAG,IAAI,GAAG,EAGrD,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  context,\n  diag,\n  propagation,\n  TextMapPropagator,\n  trace,\n  TracerProvider,\n} from '@opentelemetry/api';\nimport {\n  CompositePropagator,\n  W3CBaggagePropagator,\n  W3CTraceContextPropagator,\n  getEnv,\n  merge,\n} from '@opentelemetry/core';\nimport { IResource, Resource } from '@opentelemetry/resources';\nimport { SpanProcessor, Tracer } from '.';\nimport { loadDefaultConfig } from './config';\nimport { MultiSpanProcessor } from './MultiSpanProcessor';\nimport { NoopSpanProcessor } from './export/NoopSpanProcessor';\nimport { SDKRegistrationConfig, TracerConfig } from './types';\nimport { SpanExporter } from './export/SpanExporter';\nimport { BatchSpanProcessor } from './platform';\nimport { reconfigureLimits } from './utility';\n\nexport type PROPAGATOR_FACTORY = () => TextMapPropagator;\nexport type EXPORTER_FACTORY = () => SpanExporter;\n\nexport enum ForceFlushState {\n  'resolved',\n  'timeout',\n  'error',\n  'unresolved',\n}\n\n/**\n * This class represents a basic tracer provider which platform libraries can extend\n */\nexport class BasicTracerProvider implements TracerProvider {\n  protected static readonly _registeredPropagators = new Map<\n    string,\n    PROPAGATOR_FACTORY\n  >([\n    ['tracecontext', () => new W3CTraceContextPropagator()],\n    ['baggage', () => new W3CBaggagePropagator()],\n  ]);\n\n  protected static readonly _registeredExporters = new Map<\n    string,\n    EXPORTER_FACTORY\n  >();\n\n  private readonly _config: TracerConfig;\n  private readonly _registeredSpanProcessors: SpanProcessor[] = [];\n  private readonly _tracers: Map<string, Tracer> = new Map();\n\n  activeSpanProcessor: SpanProcessor;\n  readonly resource: IResource;\n\n  constructor(config: TracerConfig = {}) {\n    const mergedConfig = merge(\n      {},\n      loadDefaultConfig(),\n      reconfigureLimits(config)\n    );\n    this.resource = mergedConfig.resource ?? Resource.empty();\n    this.resource = Resource.default().merge(this.resource);\n    this._config = Object.assign({}, mergedConfig, {\n      resource: this.resource,\n    });\n\n    const defaultExporter = this._buildExporterFromEnv();\n    if (defaultExporter !== undefined) {\n      const batchProcessor = new BatchSpanProcessor(defaultExporter);\n      this.activeSpanProcessor = batchProcessor;\n    } else {\n      this.activeSpanProcessor = new NoopSpanProcessor();\n    }\n  }\n\n  getTracer(\n    name: string,\n    version?: string,\n    options?: { schemaUrl?: string }\n  ): Tracer {\n    const key = `${name}@${version || ''}:${options?.schemaUrl || ''}`;\n    if (!this._tracers.has(key)) {\n      this._tracers.set(\n        key,\n        new Tracer(\n          { name, version, schemaUrl: options?.schemaUrl },\n          this._config,\n          this\n        )\n      );\n    }\n\n    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n    return this._tracers.get(key)!;\n  }\n\n  /**\n   * Adds a new {@link SpanProcessor} to this tracer.\n   * @param spanProcessor the new SpanProcessor to be added.\n   */\n  addSpanProcessor(spanProcessor: SpanProcessor): void {\n    if (this._registeredSpanProcessors.length === 0) {\n      // since we might have enabled by default a batchProcessor, we disable it\n      // before adding the new one\n      this.activeSpanProcessor\n        .shutdown()\n        .catch(err =>\n          diag.error(\n            'Error while trying to shutdown current span processor',\n            err\n          )\n        );\n    }\n    this._registeredSpanProcessors.push(spanProcessor);\n    this.activeSpanProcessor = new MultiSpanProcessor(\n      this._registeredSpanProcessors\n    );\n  }\n\n  getActiveSpanProcessor(): SpanProcessor {\n    return this.activeSpanProcessor;\n  }\n\n  /**\n   * Register this TracerProvider for use with the OpenTelemetry API.\n   * Undefined values may be replaced with defaults, and\n   * null values will be skipped.\n   *\n   * @param config Configuration object for SDK registration\n   */\n  register(config: SDKRegistrationConfig = {}): void {\n    trace.setGlobalTracerProvider(this);\n    if (config.propagator === undefined) {\n      config.propagator = this._buildPropagatorFromEnv();\n    }\n\n    if (config.contextManager) {\n      context.setGlobalContextManager(config.contextManager);\n    }\n\n    if (config.propagator) {\n      propagation.setGlobalPropagator(config.propagator);\n    }\n  }\n\n  forceFlush(): Promise<void> {\n    const timeout = this._config.forceFlushTimeoutMillis;\n    const promises = this._registeredSpanProcessors.map(\n      (spanProcessor: SpanProcessor) => {\n        return new Promise(resolve => {\n          let state: ForceFlushState;\n          const timeoutInterval = setTimeout(() => {\n            resolve(\n              new Error(\n                `Span processor did not completed within timeout period of ${timeout} ms`\n              )\n            );\n            state = ForceFlushState.timeout;\n          }, timeout);\n\n          spanProcessor\n            .forceFlush()\n            .then(() => {\n              clearTimeout(timeoutInterval);\n              if (state !== ForceFlushState.timeout) {\n                state = ForceFlushState.resolved;\n                resolve(state);\n              }\n            })\n            .catch(error => {\n              clearTimeout(timeoutInterval);\n              state = ForceFlushState.error;\n              resolve(error);\n            });\n        });\n      }\n    );\n\n    return new Promise<void>((resolve, reject) => {\n      Promise.all(promises)\n        .then(results => {\n          const errors = results.filter(\n            result => result !== ForceFlushState.resolved\n          );\n          if (errors.length > 0) {\n            reject(errors);\n          } else {\n            resolve();\n          }\n        })\n        .catch(error => reject([error]));\n    });\n  }\n\n  shutdown(): Promise<void> {\n    return this.activeSpanProcessor.shutdown();\n  }\n\n  /**\n   * TS cannot yet infer the type of this.constructor:\n   * https://github.com/Microsoft/TypeScript/issues/3841#issuecomment-337560146\n   * There is no need to override either of the getters in your child class.\n   * The type of the registered component maps should be the same across all\n   * classes in the inheritance tree.\n   */\n  protected _getPropagator(name: string): TextMapPropagator | undefined {\n    return (\n      this.constructor as typeof BasicTracerProvider\n    )._registeredPropagators.get(name)?.();\n  }\n\n  protected _getSpanExporter(name: string): SpanExporter | undefined {\n    return (\n      this.constructor as typeof BasicTracerProvider\n    )._registeredExporters.get(name)?.();\n  }\n\n  protected _buildPropagatorFromEnv(): TextMapPropagator | undefined {\n    // per spec, propagators from env must be deduplicated\n    const uniquePropagatorNames = Array.from(\n      new Set(getEnv().OTEL_PROPAGATORS)\n    );\n\n    const propagators = uniquePropagatorNames.map(name => {\n      const propagator = this._getPropagator(name);\n      if (!propagator) {\n        diag.warn(\n          `Propagator \"${name}\" requested through environment variable is unavailable.`\n        );\n      }\n\n      return propagator;\n    });\n    const validPropagators = propagators.reduce<TextMapPropagator[]>(\n      (list, item) => {\n        if (item) {\n          list.push(item);\n        }\n        return list;\n      },\n      []\n    );\n\n    if (validPropagators.length === 0) {\n      return;\n    } else if (uniquePropagatorNames.length === 1) {\n      return validPropagators[0];\n    } else {\n      return new CompositePropagator({\n        propagators: validPropagators,\n      });\n    }\n  }\n\n  protected _buildExporterFromEnv(): SpanExporter | undefined {\n    const exporterName = getEnv().OTEL_TRACES_EXPORTER;\n    if (exporterName === 'none' || exporterName === '') return;\n    const exporter = this._getSpanExporter(exporterName);\n    if (!exporter) {\n      diag.error(\n        `Exporter \"${exporterName}\" requested through environment variable is unavailable.`\n      );\n    }\n    return exporter;\n  }\n}\n"]}