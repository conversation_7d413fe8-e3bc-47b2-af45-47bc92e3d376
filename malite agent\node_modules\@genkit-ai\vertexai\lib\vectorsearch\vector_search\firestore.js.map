{"version": 3, "sources": ["../../../src/vectorsearch/vector_search/firestore.ts"], "sourcesContent": ["/**\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Firestore } from 'firebase-admin/firestore';\nimport { Document, DocumentDataSchema } from 'genkit';\nimport { DocumentIndexer, DocumentRetriever, Neighbor } from './types';\n/**\n * Creates a Firestore Document Retriever.\n *\n * This function returns a DocumentRetriever function that retrieves documents\n * from a Firestore collection based on the provided Vertex AI Vector Search neighbors.\n *\n * @param {Firestore} db - The Firestore instance.\n * @param {string} collectionName - The name of the Firestore collection.\n * @returns {DocumentRetriever} - The DocumentRetriever function.\n */\nexport const getFirestoreDocumentRetriever = (\n  db: Firestore,\n  collectionName: string\n): DocumentRetriever => {\n  const firestoreRetriever: DocumentRetriever = async (\n    neighbors: Neighbor[]\n  ): Promise<Document[]> => {\n    const docs: Document[] = [];\n    for (const neighbor of neighbors) {\n      const docRef = db\n        .collection(collectionName)\n        .doc(neighbor.datapoint?.datapointId!);\n      const docSnapshot = await docRef.get();\n      if (docSnapshot.exists) {\n        const docData = { ...docSnapshot.data() }; // includes content & metadata\n        docData.metadata = { ...docData.metadata, ...neighbor }; // add neighbor\n        const parsedDocData = DocumentDataSchema.safeParse(docData);\n        if (parsedDocData.success) {\n          docs.push(new Document(parsedDocData.data));\n        }\n      }\n    }\n    return docs;\n  };\n  return firestoreRetriever;\n};\n\n/**\n * Creates a Firestore Document Indexer.\n *\n * This function returns a DocumentIndexer function that indexes documents\n * into a Firestore collection.\n *\n * @param {Firestore} db - The Firestore instance.\n * @param {string} collectionName - The name of the Firestore collection.\n * @returns {DocumentIndexer} - The DocumentIndexer function.\n */\nexport const getFirestoreDocumentIndexer = (\n  db: Firestore,\n  collectionName: string\n) => {\n  const firestoreIndexer: DocumentIndexer = async (\n    docs: Document[]\n  ): Promise<string[]> => {\n    const batch = db.batch();\n    const ids: string[] = [];\n    docs.forEach((doc) => {\n      const docRef = db.collection(collectionName).doc();\n      batch.set(docRef, {\n        content: doc.content,\n        metadata: doc.metadata || null,\n      });\n      ids.push(docRef.id);\n    });\n    await batch.commit();\n    return ids;\n  };\n  return firestoreIndexer;\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAiBA,oBAA6C;AAYtC,MAAM,gCAAgC,CAC3C,IACA,mBACsB;AACtB,QAAM,qBAAwC,OAC5C,cACwB;AACxB,UAAM,OAAmB,CAAC;AAC1B,eAAW,YAAY,WAAW;AAChC,YAAM,SAAS,GACZ,WAAW,cAAc,EACzB,IAAI,SAAS,WAAW,WAAY;AACvC,YAAM,cAAc,MAAM,OAAO,IAAI;AACrC,UAAI,YAAY,QAAQ;AACtB,cAAM,UAAU,EAAE,GAAG,YAAY,KAAK,EAAE;AACxC,gBAAQ,WAAW,EAAE,GAAG,QAAQ,UAAU,GAAG,SAAS;AACtD,cAAM,gBAAgB,iCAAmB,UAAU,OAAO;AAC1D,YAAI,cAAc,SAAS;AACzB,eAAK,KAAK,IAAI,uBAAS,cAAc,IAAI,CAAC;AAAA,QAC5C;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACA,SAAO;AACT;AAYO,MAAM,8BAA8B,CACzC,IACA,mBACG;AACH,QAAM,mBAAoC,OACxC,SACsB;AACtB,UAAM,QAAQ,GAAG,MAAM;AACvB,UAAM,MAAgB,CAAC;AACvB,SAAK,QAAQ,CAAC,QAAQ;AACpB,YAAM,SAAS,GAAG,WAAW,cAAc,EAAE,IAAI;AACjD,YAAM,IAAI,QAAQ;AAAA,QAChB,SAAS,IAAI;AAAA,QACb,UAAU,IAAI,YAAY;AAAA,MAC5B,CAAC;AACD,UAAI,KAAK,OAAO,EAAE;AAAA,IACpB,CAAC;AACD,UAAM,MAAM,OAAO;AACnB,WAAO;AAAA,EACT;AACA,SAAO;AACT;", "names": []}