import { z, Genkit, Action } from 'genkit';
import { BaseEvalDataPoint, Score } from 'genkit/evaluator';
import { GoogleAuth } from 'google-auth-library';
import { VertexAIEvaluationMetricType } from './evaluation.js';

/**
 * Copyright 2024 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

declare class EvaluatorFactory {
    private readonly auth;
    private readonly location;
    private readonly projectId;
    constructor(auth: GoogleAuth, location: string, projectId: string);
    create<ResponseType extends z.ZodTypeAny>(ai: Genkit, config: {
        metric: VertexAIEvaluationMetricType;
        displayName: string;
        definition: string;
        responseSchema: ResponseType;
    }, toRequest: (datapoint: BaseEvalDataPoint) => any, responseHandler: (response: z.infer<ResponseType>) => Score): Action;
    evaluateInstances<ResponseType extends z.ZodTypeAny>(ai: Genkit, partialRequest: any, responseSchema: ResponseType): Promise<z.infer<ResponseType>>;
}

export { EvaluatorFactory };
