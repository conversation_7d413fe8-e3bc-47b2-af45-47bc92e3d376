// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.aiplatform.v1beta1;

import "google/protobuf/duration.proto";

option csharp_namespace = "Google.Cloud.AIPlatform.V1Beta1";
option go_package = "cloud.google.com/go/aiplatform/apiv1beta1/aiplatformpb;aiplatformpb";
option java_multiple_files = true;
option java_outer_classname = "FeaturestoreMonitoringProto";
option java_package = "com.google.cloud.aiplatform.v1beta1";
option php_namespace = "Google\\Cloud\\AIPlatform\\V1beta1";
option ruby_package = "Google::Cloud::AIPlatform::V1beta1";

// Configuration of how features in Featurestore are monitored.
message FeaturestoreMonitoringConfig {
  // Configuration of the Featurestore's Snapshot Analysis Based Monitoring.
  // This type of analysis generates statistics for each Feature based on a
  // snapshot of the latest feature value of each entities every
  // monitoring_interval.
  message SnapshotAnalysis {
    // The monitoring schedule for snapshot analysis.
    // For EntityType-level config:
    //   unset / disabled = true indicates disabled by
    //   default for Features under it; otherwise by default enable snapshot
    //   analysis monitoring with monitoring_interval for Features under it.
    // Feature-level config:
    //   disabled = true indicates disabled regardless of the EntityType-level
    //   config; unset monitoring_interval indicates going with EntityType-level
    //   config; otherwise run snapshot analysis monitoring with
    //   monitoring_interval regardless of the EntityType-level config.
    // Explicitly Disable the snapshot analysis based monitoring.
    bool disabled = 1;

    // Configuration of the snapshot analysis based monitoring pipeline running
    // interval. The value is rolled up to full day.
    // If both
    // [monitoring_interval_days][google.cloud.aiplatform.v1beta1.FeaturestoreMonitoringConfig.SnapshotAnalysis.monitoring_interval_days]
    // and the deprecated `monitoring_interval` field
    // are set when creating/updating EntityTypes/Features,
    // [monitoring_interval_days][google.cloud.aiplatform.v1beta1.FeaturestoreMonitoringConfig.SnapshotAnalysis.monitoring_interval_days]
    // will be used.
    google.protobuf.Duration monitoring_interval = 2 [deprecated = true];

    // Configuration of the snapshot analysis based monitoring pipeline
    // running interval. The value indicates number of days.
    int32 monitoring_interval_days = 3;

    // Customized export features time window for snapshot analysis. Unit is one
    // day. Default value is 3 weeks. Minimum value is 1 day. Maximum value is
    // 4000 days.
    int32 staleness_days = 4;
  }

  // Configuration of the Featurestore's ImportFeature Analysis Based
  // Monitoring. This type of analysis generates statistics for values of each
  // Feature imported by every
  // [ImportFeatureValues][google.cloud.aiplatform.v1beta1.FeaturestoreService.ImportFeatureValues]
  // operation.
  message ImportFeaturesAnalysis {
    // The state defines whether to enable ImportFeature analysis.
    enum State {
      // Should not be used.
      STATE_UNSPECIFIED = 0;

      // The default behavior of whether to enable the monitoring.
      // EntityType-level config: disabled.
      // Feature-level config: inherited from the configuration of EntityType
      // this Feature belongs to.
      DEFAULT = 1;

      // Explicitly enables import features analysis.
      // EntityType-level config: by default enables import features analysis
      // for all Features under it. Feature-level config: enables import
      // features analysis regardless of the EntityType-level config.
      ENABLED = 2;

      // Explicitly disables import features analysis.
      // EntityType-level config: by default disables import features analysis
      // for all Features under it. Feature-level config: disables import
      // features analysis regardless of the EntityType-level config.
      DISABLED = 3;
    }

    // Defines the baseline to do anomaly detection for feature values imported
    // by each
    // [ImportFeatureValues][google.cloud.aiplatform.v1beta1.FeaturestoreService.ImportFeatureValues]
    // operation.
    enum Baseline {
      // Should not be used.
      BASELINE_UNSPECIFIED = 0;

      // Choose the later one statistics generated by either most recent
      // snapshot analysis or previous import features analysis. If non of them
      // exists, skip anomaly detection and only generate a statistics.
      LATEST_STATS = 1;

      // Use the statistics generated by the most recent snapshot analysis if
      // exists.
      MOST_RECENT_SNAPSHOT_STATS = 2;

      // Use the statistics generated by the previous import features analysis
      // if exists.
      PREVIOUS_IMPORT_FEATURES_STATS = 3;
    }

    // Whether to enable / disable / inherite default hebavior for import
    // features analysis.
    State state = 1;

    // The baseline used to do anomaly detection for the statistics generated by
    // import features analysis.
    Baseline anomaly_detection_baseline = 2;
  }

  // The config for Featurestore Monitoring threshold.
  message ThresholdConfig {
    oneof threshold {
      // Specify a threshold value that can trigger the alert.
      // 1. For categorical feature, the distribution distance is calculated by
      // L-inifinity norm.
      // 2. For numerical feature, the distribution distance is calculated by
      // Jensen–Shannon divergence. Each feature must have a non-zero threshold
      // if they need to be monitored. Otherwise no alert will be triggered for
      // that feature.
      double value = 1;
    }
  }

  // The config for Snapshot Analysis Based Feature Monitoring.
  SnapshotAnalysis snapshot_analysis = 1;

  // The config for ImportFeatures Analysis Based Feature Monitoring.
  ImportFeaturesAnalysis import_features_analysis = 2;

  // Threshold for numerical features of anomaly detection.
  // This is shared by all objectives of Featurestore Monitoring for numerical
  // features (i.e. Features with type
  // ([Feature.ValueType][google.cloud.aiplatform.v1beta1.Feature.ValueType])
  // DOUBLE or INT64).
  ThresholdConfig numerical_threshold_config = 3;

  // Threshold for categorical features of anomaly detection.
  // This is shared by all types of Featurestore Monitoring for categorical
  // features (i.e. Features with type
  // ([Feature.ValueType][google.cloud.aiplatform.v1beta1.Feature.ValueType])
  // BOOL or STRING).
  ThresholdConfig categorical_threshold_config = 4;
}
