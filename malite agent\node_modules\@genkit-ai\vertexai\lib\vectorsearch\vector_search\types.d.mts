import '@google-cloud/aiplatform';
import 'genkit';
import 'genkit/embedder';
import 'genkit/retriever';
import 'google-auth-library';
export { s as CrowdingTag, C as CrowdingTagSchema, i as Datapoint, D as DocumentIndexer, b as DocumentRetriever, F as FindNeighborsResponse, u as FindNeighborsResponseSchema, j as IFindNeighborsRequest, k as IFindNeighborsResponse, h as IIndexDatapoint, g as INumericRestriction, I as IRestriction, l as ISparseEmbedding, N as Neighbor, t as NeighborSchema, r as NumericRestriction, p as NumericRestrictionOperator, o as NumericRestrictionOperatorSchema, q as NumericRestrictionSchema, n as Restriction, R as RestrictionSchema, m as SparseEmbedding, S as SparseEmbeddingSchema, c as VectorSearchOptions, d as VertexAIVectorIndexerOptions, V as VertexAIVectorIndexerOptionsSchema, e as VertexAIVectorRetrieverOptions, a as VertexAIVectorRetrieverOptionsSchema, f as VertexVectorSearchOptions } from '../../types-C_tGep_V.mjs';
import '../../types-Bc0LKM8D.mjs';
import '@google-cloud/vertexai';
import 'genkit/model';
