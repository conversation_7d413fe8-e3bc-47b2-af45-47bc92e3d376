{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/index.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AACH,cAAc,YAAY,CAAC;AAC3B,OAAO,EAAE,gBAAgB,EAAE,MAAM,oBAAoB,CAAC;AACtD,OAAO,EACL,iBAAiB,GAGlB,MAAM,SAAS,CAAC;AACjB,OAAO,EACL,YAAY,EACZ,uBAAuB,EACvB,2BAA2B,EAC3B,wBAAwB,EACxB,cAAc,GACf,MAAM,QAAQ,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nexport * from './platform';\nexport { OTLPExporterBase } from './OTLPExporterBase';\nexport {\n  OTLPExporterError,\n  OTLPExporterConfigBase,\n  ExportServiceError,\n} from './types';\nexport {\n  parseHeaders,\n  appendResourcePathToUrl,\n  appendRootPathToUrlIfNeeded,\n  configureExporterTimeout,\n  invalidTimeout,\n} from './util';\n"]}