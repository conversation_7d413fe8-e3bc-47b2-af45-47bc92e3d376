"use strict";
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ResponseFormat$ = exports.ResponseFormat$outboundSchema = exports.ResponseFormat$inboundSchema = void 0;
exports.responseFormatToJSON = responseFormatToJSON;
exports.responseFormatFromJSON = responseFormatFromJSON;
const z = __importStar(require("zod"));
const primitives_js_1 = require("../../lib/primitives.js");
const schemas_js_1 = require("../../lib/schemas.js");
const jsonschema_js_1 = require("./jsonschema.js");
const responseformats_js_1 = require("./responseformats.js");
/** @internal */
exports.ResponseFormat$inboundSchema = z.object({
    type: responseformats_js_1.ResponseFormats$inboundSchema.optional(),
    json_schema: z.nullable(jsonschema_js_1.JsonSchema$inboundSchema).optional(),
}).transform((v) => {
    return (0, primitives_js_1.remap)(v, {
        "json_schema": "jsonSchema",
    });
});
/** @internal */
exports.ResponseFormat$outboundSchema = z.object({
    type: responseformats_js_1.ResponseFormats$outboundSchema.optional(),
    jsonSchema: z.nullable(jsonschema_js_1.JsonSchema$outboundSchema).optional(),
}).transform((v) => {
    return (0, primitives_js_1.remap)(v, {
        jsonSchema: "json_schema",
    });
});
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var ResponseFormat$;
(function (ResponseFormat$) {
    /** @deprecated use `ResponseFormat$inboundSchema` instead. */
    ResponseFormat$.inboundSchema = exports.ResponseFormat$inboundSchema;
    /** @deprecated use `ResponseFormat$outboundSchema` instead. */
    ResponseFormat$.outboundSchema = exports.ResponseFormat$outboundSchema;
})(ResponseFormat$ || (exports.ResponseFormat$ = ResponseFormat$ = {}));
function responseFormatToJSON(responseFormat) {
    return JSON.stringify(exports.ResponseFormat$outboundSchema.parse(responseFormat));
}
function responseFormatFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.ResponseFormat$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'ResponseFormat' from JSON`);
}
//# sourceMappingURL=responseformat.js.map