{"version": 3, "file": "autoLoader.js", "sourceRoot": "", "sources": ["../../src/autoLoader.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAEH,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,MAAM,oBAAoB,CAAC;AACpD,OAAO,EAAE,IAAI,EAAE,MAAM,yBAAyB,CAAC;AAC/C,OAAO,EACL,uBAAuB,EACvB,sBAAsB,GACvB,MAAM,mBAAmB,CAAC;AAG3B;;;;;GAKG;AACH,MAAM,UAAU,wBAAwB,CACtC,OAA0B;;IAE1B,MAAM,cAAc,GAAG,OAAO,CAAC,cAAc,IAAI,KAAK,CAAC,iBAAiB,EAAE,CAAC;IAC3E,MAAM,aAAa,GAAG,OAAO,CAAC,aAAa,IAAI,OAAO,CAAC,gBAAgB,EAAE,CAAC;IAC1E,MAAM,cAAc,GAAG,OAAO,CAAC,cAAc,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC;IAC1E,MAAM,gBAAgB,GAAG,MAAA,MAAA,OAAO,CAAC,gBAAgB,0CAAE,IAAI,EAAE,mCAAI,EAAE,CAAC;IAEhE,sBAAsB,CACpB,gBAAgB,EAChB,cAAc,EACd,aAAa,EACb,cAAc,CACf,CAAC;IAEF,OAAO,GAAG,EAAE;QACV,uBAAuB,CAAC,gBAAgB,CAAC,CAAC;IAC5C,CAAC,CAAC;AACJ,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { trace, metrics } from '@opentelemetry/api';\nimport { logs } from '@opentelemetry/api-logs';\nimport {\n  disableInstrumentations,\n  enableInstrumentations,\n} from './autoLoaderUtils';\nimport { AutoLoaderOptions } from './types_internal';\n\n/**\n * It will register instrumentations and plugins\n * @param options\n * @return returns function to unload instrumentation and plugins that were\n *   registered\n */\nexport function registerInstrumentations(\n  options: AutoLoaderOptions\n): () => void {\n  const tracerProvider = options.tracerProvider || trace.getTracerProvider();\n  const meterProvider = options.meterProvider || metrics.getMeterProvider();\n  const loggerProvider = options.loggerProvider || logs.getLoggerProvider();\n  const instrumentations = options.instrumentations?.flat() ?? [];\n\n  enableInstrumentations(\n    instrumentations,\n    tracerProvider,\n    meterProvider,\n    loggerProvider\n  );\n\n  return () => {\n    disableInstrumentations(instrumentations);\n  };\n}\n"]}