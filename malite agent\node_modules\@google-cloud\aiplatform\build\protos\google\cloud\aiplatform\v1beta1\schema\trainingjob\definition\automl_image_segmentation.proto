// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.aiplatform.v1beta1.schema.trainingjob.definition;


option csharp_namespace = "Google.Cloud.AIPlatform.V1Beta1.Schema.TrainingJob.Definition";
option go_package = "cloud.google.com/go/aiplatform/apiv1beta1/schema/trainingjob/definition/definitionpb;definitionpb";
option java_multiple_files = true;
option java_outer_classname = "AutoMLImageSegmentationProto";
option java_package = "com.google.cloud.aiplatform.v1beta1.schema.trainingjob.definition";
option php_namespace = "Google\\Cloud\\AIPlatform\\V1beta1\\Schema\\TrainingJob\\Definition";
option ruby_package = "Google::Cloud::AIPlatform::V1beta1::Schema::TrainingJob::Definition";

// A TrainingJob that trains and uploads an AutoML Image Segmentation Model.
message AutoMlImageSegmentation {
  // The input parameters of this TrainingJob.
  AutoMlImageSegmentationInputs inputs = 1;

  // The metadata information.
  AutoMlImageSegmentationMetadata metadata = 2;
}

message AutoMlImageSegmentationInputs {
  enum ModelType {
    // Should not be set.
    MODEL_TYPE_UNSPECIFIED = 0;

    // A model to be used via prediction calls to uCAIP API. Expected
    // to have a higher latency, but should also have a higher prediction
    // quality than other models.
    CLOUD_HIGH_ACCURACY_1 = 1;

    // A model to be used via prediction calls to uCAIP API. Expected
    // to have a lower latency but relatively lower prediction quality.
    CLOUD_LOW_ACCURACY_1 = 2;

    // A model that, in addition to being available within Google
    // Cloud, can also be exported (see ModelService.ExportModel) as TensorFlow
    // model and used on a mobile or edge device afterwards.
    // Expected to have low latency, but may have lower prediction
    // quality than other mobile models.
    MOBILE_TF_LOW_LATENCY_1 = 3;
  }

  ModelType model_type = 1;

  // The training budget of creating this model, expressed in milli node
  // hours i.e. 1,000 value in this field means 1 node hour. The actual
  // metadata.costMilliNodeHours will be equal or less than this value.
  // If further model training ceases to provide any improvements, it will
  // stop without using the full budget and the metadata.successfulStopReason
  // will be `model-converged`.
  // Note, node_hour  = actual_hour * number_of_nodes_involved. Or
  // actaul_wall_clock_hours = train_budget_milli_node_hours /
  //                           (number_of_nodes_involved * 1000)
  // For modelType `cloud-high-accuracy-1`(default), the budget must be between
  // 20,000 and 2,000,000 milli node hours, inclusive. The default value is
  // 192,000 which represents one day in wall time
  // (1000 milli * 24 hours * 8 nodes).
  int64 budget_milli_node_hours = 2;

  // The ID of the `base` model. If it is specified, the new model will be
  // trained based on the `base` model. Otherwise, the new model will be
  // trained from scratch. The `base` model must be in the same
  // Project and Location as the new Model to train, and have the same
  // modelType.
  string base_model_id = 3;
}

message AutoMlImageSegmentationMetadata {
  enum SuccessfulStopReason {
    // Should not be set.
    SUCCESSFUL_STOP_REASON_UNSPECIFIED = 0;

    // The inputs.budgetMilliNodeHours had been reached.
    BUDGET_REACHED = 1;

    // Further training of the Model ceased to increase its quality, since it
    // already has converged.
    MODEL_CONVERGED = 2;
  }

  // The actual training cost of creating this model, expressed in
  // milli node hours, i.e. 1,000 value in this field means 1 node hour.
  // Guaranteed to not exceed inputs.budgetMilliNodeHours.
  int64 cost_milli_node_hours = 1;

  // For successful job completions, this is the reason why the job has
  // finished.
  SuccessfulStopReason successful_stop_reason = 2;
}
