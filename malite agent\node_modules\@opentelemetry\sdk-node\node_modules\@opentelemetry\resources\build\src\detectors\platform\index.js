"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.serviceInstanceIdDetectorSync = exports.processDetectorSync = exports.processDetector = exports.osDetectorSync = exports.osDetector = exports.hostDetectorSync = exports.hostDetector = void 0;
/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
var node_1 = require("./node");
Object.defineProperty(exports, "hostDetector", { enumerable: true, get: function () { return node_1.hostDetector; } });
Object.defineProperty(exports, "hostDetectorSync", { enumerable: true, get: function () { return node_1.hostDetectorSync; } });
Object.defineProperty(exports, "osDetector", { enumerable: true, get: function () { return node_1.osDetector; } });
Object.defineProperty(exports, "osDetectorSync", { enumerable: true, get: function () { return node_1.osDetectorSync; } });
Object.defineProperty(exports, "processDetector", { enumerable: true, get: function () { return node_1.processDetector; } });
Object.defineProperty(exports, "processDetectorSync", { enumerable: true, get: function () { return node_1.processDetectorSync; } });
Object.defineProperty(exports, "serviceInstanceIdDetectorSync", { enumerable: true, get: function () { return node_1.serviceInstanceIdDetectorSync; } });
//# sourceMappingURL=index.js.map