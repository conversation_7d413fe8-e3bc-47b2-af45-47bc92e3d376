import { google } from "googleapis";
async function getAccessToken(auth) {
  const client = await auth.getClient();
  const _accessToken = await client.getAccessToken();
  return _accessToken.token || null;
}
async function getProjectNumber(projectId) {
  const client = google.cloudresourcemanager("v1");
  const authClient = await google.auth.getClient({
    scopes: ["https://www.googleapis.com/auth/cloud-platform"]
  });
  try {
    const response = await client.projects.get({
      projectId,
      auth: authClient
    });
    if (!response.data.projectNumber) {
      throw new Error(
        `Error fetching project number for Vertex AI plugin for project ${projectId}`
      );
    }
    return response.data["projectNumber"];
  } catch (error) {
    throw new Error(
      `Error fetching project number for Vertex AI plugin for project ${projectId}`
    );
  }
}
export {
  getAccessToken,
  getProjectNumber
};
//# sourceMappingURL=utils.mjs.map