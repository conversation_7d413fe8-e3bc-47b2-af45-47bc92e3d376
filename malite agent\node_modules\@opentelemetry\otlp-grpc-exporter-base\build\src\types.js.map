{"version": 3, "file": "types.js", "sourceRoot": "", "sources": ["../../src/types.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport type { ChannelCredentials, Metadata } from '@grpc/grpc-js';\n\nimport {\n  CompressionAlgorithm,\n  OTLPExporterConfigBase,\n  OTLPExporterError,\n} from '@opentelemetry/otlp-exporter-base';\n\n/**\n * Queue item to be used to save temporary spans/metrics/logs in case the GRPC service\n * hasn't been fully initialized yet\n */\nexport interface GRPCQueueItem<ExportedItem> {\n  objects: ExportedItem[];\n  onSuccess: () => void;\n  onError: (error: OTLPExporterError) => void;\n}\n\n/**\n * OTLP Exporter Config for Node\n */\nexport interface OTLPGRPCExporterConfigNode extends OTLPExporterConfigBase {\n  credentials?: ChannelCredentials;\n  metadata?: Metadata;\n  compression?: CompressionAlgorithm;\n}\n"]}