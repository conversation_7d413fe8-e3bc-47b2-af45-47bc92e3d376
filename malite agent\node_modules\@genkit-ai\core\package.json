{"name": "@genkit-ai/core", "description": "Genkit AI framework core libraries.", "keywords": ["genkit", "ai", "genai", "generative-ai"], "version": "1.10.0", "type": "commonjs", "repository": {"type": "git", "url": "https://github.com/firebase/genkit.git", "directory": "js/core"}, "author": "genkit", "license": "Apache-2.0", "dependencies": {"@opentelemetry/api": "^1.9.0", "@opentelemetry/context-async-hooks": "^1.25.0", "@opentelemetry/core": "^1.25.0", "@opentelemetry/sdk-metrics": "^1.25.0", "@opentelemetry/sdk-node": "^0.52.0", "@opentelemetry/sdk-trace-base": "^1.25.0", "@types/json-schema": "^7.0.15", "ajv": "^8.12.0", "ajv-formats": "^3.0.1", "async-mutex": "^0.5.0", "body-parser": "^1.20.3", "cors": "^2.8.5", "express": "^4.21.0", "get-port": "^5.1.0", "json-schema": "^0.4.0", "zod": "^3.23.8", "zod-to-json-schema": "^3.22.4", "dotprompt": "^1.1.1"}, "devDependencies": {"@types/express": "^4.17.21", "@types/node": "^20.11.30", "genversion": "^3.2.0", "npm-run-all": "^4.1.5", "rimraf": "^6.0.1", "tsup": "^8.3.5", "tsx": "^4.19.2", "typescript": "^4.9.0"}, "types": "lib/index.d.ts", "exports": {".": {"types": "./lib/index.d.ts", "require": "./lib/index.js", "import": "./lib/index.mjs", "default": "./lib/index.js"}, "./async": {"types": "./lib/async.ts", "require": "./lib/async.js", "import": "./lib/async.mjs", "default": "./lib/async.js"}, "./registry": {"types": "./lib/registry.d.ts", "require": "./lib/registry.js", "import": "./lib/registry.mjs", "default": "./lib/registry.js"}, "./tracing": {"types": "./lib/tracing.d.ts", "require": "./lib/tracing.js", "import": "./lib/tracing.mjs", "default": "./lib/tracing.js"}, "./logging": {"types": "./lib/logging.d.ts", "require": "./lib/logging.js", "import": "./lib/logging.mjs", "default": "./lib/logging.js"}, "./schema": {"types": "./lib/schema.d.ts", "require": "./lib/schema.js", "import": "./lib/schema.mjs", "default": "./lib/schema.js"}}, "typesVersions": {"*": {"metrics": ["lib/metrics"], "registry": ["lib/registry"], "tracing": ["lib/tracing"], "logging": ["lib/logging"], "config": ["lib/config"], "runtime": ["lib/runtime"], "schema": ["lib/schema"]}}, "scripts": {"check": "tsc", "compile": "tsup-node", "build:clean": "rimraf ./lib", "build": "npm-run-all genversion build:clean check compile", "build:watch": "tsup-node --watch", "test": "node --import tsx --test tests/*_test.ts", "genversion": "genversion -esf src/__codegen/version.ts"}}