{"version": 3, "sources": ["../../src/rerankers/types.ts"], "sourcesContent": ["/**\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { z } from 'genkit';\nimport { GoogleAuth } from 'google-auth-library';\nimport { CommonPluginOptions } from '../common/types.js';\n\n// Define the schema for the options used in the Vertex AI reranker\nexport const VertexAIRerankerOptionsSchema = z.object({\n  k: z.number().optional().describe('Number of top documents to rerank'), // Optional: Number of documents to rerank\n  model: z.string().optional().describe('Model name for reranking'), // Optional: Model name, defaults to a pre-defined model\n  location: z\n    .string()\n    .optional()\n    .describe('Google Cloud location, e.g., \"us-central1\"'), // Optional: Location of the reranking model\n});\n\n// Type alias for the options schema\nexport type VertexAIRerankerOptions = z.infer<\n  typeof VertexAIRerankerOptionsSchema\n>;\n\nexport interface VertexRerankerConfig {\n  // Optional: Name of the reranker\n  name?: string;\n  // Optional: Model name, defaults to a pre-defined model\n  model?: string;\n}\n\nexport interface VertexRerankOptions {\n  authClient: GoogleAuth;\n  location: string;\n  projectId: string;\n  rerankOptions: VertexRerankerConfig[];\n}\n\nexport interface RerankerOptions {\n  /** Configure reranker options */\n  rerankers: (string | VertexRerankerConfig)[];\n}\n\nexport interface PluginOptions extends CommonPluginOptions, RerankerOptions {}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAgBA,oBAAkB;AAKX,MAAM,gCAAgC,gBAAE,OAAO;AAAA,EACpD,GAAG,gBAAE,OAAO,EAAE,SAAS,EAAE,SAAS,mCAAmC;AAAA;AAAA,EACrE,OAAO,gBAAE,OAAO,EAAE,SAAS,EAAE,SAAS,0BAA0B;AAAA;AAAA,EAChE,UAAU,gBACP,OAAO,EACP,SAAS,EACT,SAAS,4CAA4C;AAAA;AAC1D,CAAC;", "names": []}