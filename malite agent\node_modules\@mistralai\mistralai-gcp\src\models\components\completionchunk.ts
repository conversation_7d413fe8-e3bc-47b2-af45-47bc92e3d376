/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  CompletionResponseStreamChoice,
  CompletionResponseStreamChoice$inboundSchema,
  CompletionResponseStreamChoice$Outbound,
  CompletionResponseStreamChoice$outboundSchema,
} from "./completionresponsestreamchoice.js";
import {
  UsageInfo,
  UsageInfo$inboundSchema,
  UsageInfo$Outbound,
  UsageInfo$outboundSchema,
} from "./usageinfo.js";

export type CompletionChunk = {
  id: string;
  object?: string | undefined;
  created?: number | undefined;
  model: string;
  usage?: UsageInfo | undefined;
  choices: Array<CompletionResponseStreamChoice>;
};

/** @internal */
export const CompletionChunk$inboundSchema: z.ZodType<
  CompletionChunk,
  z.ZodTypeDef,
  unknown
> = z.object({
  id: z.string(),
  object: z.string().optional(),
  created: z.number().int().optional(),
  model: z.string(),
  usage: UsageInfo$inboundSchema.optional(),
  choices: z.array(CompletionResponseStreamChoice$inboundSchema),
});

/** @internal */
export type CompletionChunk$Outbound = {
  id: string;
  object?: string | undefined;
  created?: number | undefined;
  model: string;
  usage?: UsageInfo$Outbound | undefined;
  choices: Array<CompletionResponseStreamChoice$Outbound>;
};

/** @internal */
export const CompletionChunk$outboundSchema: z.ZodType<
  CompletionChunk$Outbound,
  z.ZodTypeDef,
  CompletionChunk
> = z.object({
  id: z.string(),
  object: z.string().optional(),
  created: z.number().int().optional(),
  model: z.string(),
  usage: UsageInfo$outboundSchema.optional(),
  choices: z.array(CompletionResponseStreamChoice$outboundSchema),
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace CompletionChunk$ {
  /** @deprecated use `CompletionChunk$inboundSchema` instead. */
  export const inboundSchema = CompletionChunk$inboundSchema;
  /** @deprecated use `CompletionChunk$outboundSchema` instead. */
  export const outboundSchema = CompletionChunk$outboundSchema;
  /** @deprecated use `CompletionChunk$Outbound` instead. */
  export type Outbound = CompletionChunk$Outbound;
}

export function completionChunkToJSON(
  completionChunk: CompletionChunk,
): string {
  return JSON.stringify(CompletionChunk$outboundSchema.parse(completionChunk));
}

export function completionChunkFromJSON(
  jsonString: string,
): SafeParseResult<CompletionChunk, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => CompletionChunk$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'CompletionChunk' from JSON`,
  );
}
