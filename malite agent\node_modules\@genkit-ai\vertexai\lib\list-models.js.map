{"version": 3, "sources": ["../src/list-models.ts"], "sourcesContent": ["/**\n * Copyright 2025 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { GENKIT_CLIENT_HEADER } from 'genkit';\nimport { GoogleAuth } from 'google-auth-library';\n\n// Gemini  model definition\nexport interface Model {\n  name: string;\n  launchStage: string;\n}\n\n// Gemini list models response\ninterface ListModelsResponse {\n  publisherModels: Model[];\n}\n\n/**\n * List Gemini models by making an RPC call to the API.\n */\nexport async function listModels(\n  authClient: GoogleAuth,\n  location: string,\n  projectId: string\n): Promise<Model[]> {\n  const fetch = (await import('node-fetch')).default;\n  const accessToken = await authClient.getAccessToken();\n  const response = await fetch(\n    `https://${location}-aiplatform.googleapis.com/v1beta1/publishers/google/models`,\n    {\n      method: 'GET',\n      headers: {\n        Authorization: `Bearer ${accessToken}`,\n        'x-goog-user-project': projectId,\n        'Content-Type': 'application/json',\n        'X-Goog-Api-Client': GENKIT_CLIENT_HEADER,\n      },\n    }\n  );\n  if (!response.ok) {\n    const ee = await response.text();\n    throw new Error(\n      `Error from Vertex AI predict: HTTP ${response.status}: ${ee}`\n    );\n  }\n\n  const modelResponse = (await response.json()) as ListModelsResponse;\n  for (const m of modelResponse.publisherModels) {\n    console.log(m.name);\n  }\n  return modelResponse.publisherModels;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAgBA,oBAAqC;AAiBrC,eAAsB,WACpB,YACA,UACA,WACkB;AAClB,QAAM,SAAS,MAAM,OAAO,YAAY,GAAG;AAC3C,QAAM,cAAc,MAAM,WAAW,eAAe;AACpD,QAAM,WAAW,MAAM;AAAA,IACrB,WAAW,QAAQ;AAAA,IACnB;AAAA,MACE,QAAQ;AAAA,MACR,SAAS;AAAA,QACP,eAAe,UAAU,WAAW;AAAA,QACpC,uBAAuB;AAAA,QACvB,gBAAgB;AAAA,QAChB,qBAAqB;AAAA,MACvB;AAAA,IACF;AAAA,EACF;AACA,MAAI,CAAC,SAAS,IAAI;AAChB,UAAM,KAAK,MAAM,SAAS,KAAK;AAC/B,UAAM,IAAI;AAAA,MACR,sCAAsC,SAAS,MAAM,KAAK,EAAE;AAAA,IAC9D;AAAA,EACF;AAEA,QAAM,gBAAiB,MAAM,SAAS,KAAK;AAC3C,aAAW,KAAK,cAAc,iBAAiB;AAC7C,YAAQ,IAAI,EAAE,IAAI;AAAA,EACpB;AACA,SAAO,cAAc;AACvB;", "names": []}