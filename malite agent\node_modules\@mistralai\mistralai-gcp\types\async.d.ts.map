{"version": 3, "file": "async.d.ts", "sourceRoot": "", "sources": ["../src/types/async.ts"], "names": [], "mappings": "AAIA,MAAM,MAAM,OAAO,GACf;IACE,MAAM,EAAE,UAAU,CAAC;IACnB,OAAO,EAAE,OAAO,CAAC;IACjB,QAAQ,EAAE,QAAQ,CAAC;CACpB,GACD;IACE,MAAM,EAAE,eAAe,CAAC;IACxB,OAAO,EAAE,OAAO,CAAC;IACjB,QAAQ,CAAC,EAAE,SAAS,CAAC;CACtB,GACD;IACE,MAAM,EAAE,SAAS,CAAC;IAClB,OAAO,CAAC,EAAE,SAAS,CAAC;IACpB,QAAQ,CAAC,EAAE,SAAS,CAAC;CACtB,CAAC;AAEN,qBAAa,UAAU,CAAC,CAAC,CAAE,YAAW,OAAO,CAAC,CAAC,CAAC;;IAI9C,QAAQ,CAAC,CAAC,MAAM,CAAC,WAAW,CAAC,gBAAgB;gBAEjC,CAAC,EAAE,CAAC,CAAC,EAAE,OAAO,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;IAQnD,IAAI,CAAC,QAAQ,GAAG,CAAC,EAAE,QAAQ,GAAG,KAAK,EACjC,WAAW,CAAC,EACR,CAAC,CAAC,KAAK,EAAE,CAAC,KAAK,QAAQ,GAAG,WAAW,CAAC,QAAQ,CAAC,CAAC,GAChD,IAAI,GACJ,SAAS,EACb,UAAU,CAAC,EACP,CAAC,CAAC,MAAM,EAAE,GAAG,KAAK,QAAQ,GAAG,WAAW,CAAC,QAAQ,CAAC,CAAC,GACnD,IAAI,GACJ,SAAS,GACZ,OAAO,CAAC,QAAQ,GAAG,QAAQ,CAAC;IAO/B,KAAK,CAAC,OAAO,GAAG,KAAK,EACnB,UAAU,CAAC,EACP,CAAC,CAAC,MAAM,EAAE,GAAG,KAAK,OAAO,GAAG,WAAW,CAAC,OAAO,CAAC,CAAC,GACjD,IAAI,GACJ,SAAS,GACZ,OAAO,CAAC,CAAC,GAAG,OAAO,CAAC;IAIvB,OAAO,CAAC,SAAS,CAAC,EAAE,CAAC,MAAM,IAAI,CAAC,GAAG,IAAI,GAAG,SAAS,GAAG,OAAO,CAAC,CAAC,CAAC;IAIhE,QAAQ,IAAI,OAAO,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;CAGlC"}