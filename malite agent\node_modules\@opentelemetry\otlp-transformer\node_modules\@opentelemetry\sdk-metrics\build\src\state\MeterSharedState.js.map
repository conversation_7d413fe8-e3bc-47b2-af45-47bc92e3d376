{"version": 3, "file": "MeterSharedState.js", "sourceRoot": "", "sources": ["../../../src/state/MeterSharedState.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAMH,kEAGiC;AACjC,oCAAiC;AACjC,oCAA+C;AAC/C,6DAA0D;AAG1D,mEAAgE;AAChE,6EAAkE;AAClE,6DAA0D;AAC1D,2DAAwD;AAExD,qEAAkE;AAGlE;;GAEG;AACH,MAAa,gBAAgB;IAK3B,YACU,yBAAmD,EACnD,qBAA2C;QAD3C,8BAAyB,GAAzB,yBAAyB,CAA0B;QACnD,0BAAqB,GAArB,qBAAqB,CAAsB;QANrD,0BAAqB,GAAG,IAAI,6CAAqB,EAAE,CAAC;QACpD,uBAAkB,GAAG,IAAI,uCAAkB,EAAE,CAAC;QAO5C,IAAI,CAAC,KAAK,GAAG,IAAI,aAAK,CAAC,IAAI,CAAC,CAAC;IAC/B,CAAC;IAED,qBAAqB,CAAC,UAAgC;QACpD,MAAM,QAAQ,GAAG,IAAI,CAAC,sBAAsB,CAAC,UAAU,EAAE,qCAAiB,CAAC,CAAC;QAE5E,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE;YACzB,OAAO,QAAQ,CAAC,CAAC,CAAC,CAAC;SACpB;QACD,OAAO,IAAI,+CAAkB,CAAC,QAAQ,CAAC,CAAC;IAC1C,CAAC;IAED,0BAA0B,CAAC,UAAgC;QACzD,MAAM,QAAQ,GAAG,IAAI,CAAC,sBAAsB,CAC1C,UAAU,EACV,uCAAkB,CACnB,CAAC;QAEF,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED;;;;;OAKG;IACH,KAAK,CAAC,OAAO,CACX,SAAgC,EAChC,cAAsB,EACtB,OAA8B;QAE9B;;;WAGG;QACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAClD,cAAc,EACd,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,aAAa,CACvB,CAAC;QACF,MAAM,QAAQ,GAAG,IAAI,CAAC,qBAAqB,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;QAEnE,qDAAqD;QACrD,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE;YACzB,OAAO,IAAI,CAAC;SACb;QAED,MAAM,cAAc,GAAG,QAAQ;aAC5B,GAAG,CAAC,aAAa,CAAC,EAAE;YACnB,OAAO,aAAa,CAAC,OAAO,CAAC,SAAS,EAAE,cAAc,CAAC,CAAC;QAC1D,CAAC,CAAC;aACD,MAAM,CAAC,oBAAY,CAAC,CAAC;QAExB,mFAAmF;QACnF,IAAI,cAAc,CAAC,MAAM,KAAK,CAAC,EAAE;YAC/B,OAAO,EAAE,MAAM,EAAE,CAAC;SACnB;QAED,OAAO;YACL,YAAY,EAAE;gBACZ,KAAK,EAAE,IAAI,CAAC,qBAAqB;gBACjC,OAAO,EAAE,cAAc;aACxB;YACD,MAAM;SACP,CAAC;IACJ,CAAC;IAEO,sBAAsB,CAI5B,UAAgC,EAChC,iBAAoC;QAEpC,MAAM,KAAK,GAAG,IAAI,CAAC,yBAAyB,CAAC,YAAY,CAAC,SAAS,CACjE,UAAU,EACV,IAAI,CAAC,qBAAqB,CAC3B,CAAC;QACF,IAAI,QAAQ,GAAG,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;YAC9B,MAAM,cAAc,GAAG,IAAA,yDAAkC,EACvD,IAAI,EACJ,UAAU,CACX,CAAC;YACF,MAAM,iBAAiB,GACrB,IAAI,CAAC,qBAAqB,CAAC,6BAA6B,CACtD,cAAc,CACf,CAAC;YACJ,IAAI,iBAAiB,IAAI,IAAI,EAAE;gBAC7B,OAAO,iBAAiB,CAAC;aAC1B;YACD,MAAM,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC,gBAAgB,CAAC,cAAc,CAAC,CAAC;YACrE,MAAM,WAAW,GAAG,IAAI,iBAAiB,CACvC,cAAc,EACd,UAAU,EACV,IAAI,CAAC,mBAAmB,EACxB,IAAI,CAAC,yBAAyB,CAAC,gBAAgB,CAC3C,CAAC;YACP,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;YACjD,OAAO,WAAW,CAAC;QACrB,CAAC,CAAC,CAAC;QAEH,0FAA0F;QAC1F,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE;YACzB,MAAM,wBAAwB,GAC5B,IAAI,CAAC,yBAAyB,CAAC,kBAAkB,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;YACrE,MAAM,iBAAiB,GAAG,wBAAwB,CAAC,GAAG,CACpD,CAAC,CAAC,SAAS,EAAE,WAAW,CAAC,EAAE,EAAE;gBAC3B,MAAM,iBAAiB,GACrB,IAAI,CAAC,qBAAqB,CAAC,sCAAsC,CAC/D,SAAS,EACT,UAAU,CACX,CAAC;gBACJ,IAAI,iBAAiB,IAAI,IAAI,EAAE;oBAC7B,OAAO,iBAAiB,CAAC;iBAC1B;gBACD,MAAM,UAAU,GAAG,WAAW,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC;gBAC5D,MAAM,OAAO,GAAG,IAAI,iBAAiB,CACnC,UAAU,EACV,UAAU,EACV,yCAAmB,CAAC,IAAI,EAAE,EAC1B,CAAC,SAAS,CAAC,CACP,CAAC;gBACP,IAAI,CAAC,qBAAqB,CAAC,oBAAoB,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;gBACpE,OAAO,OAAO,CAAC;YACjB,CAAC,CACF,CAAC;YACF,QAAQ,GAAG,QAAQ,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC;SAC/C;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;CACF;AA5ID,4CA4IC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { HrTime } from '@opentelemetry/api';\nimport { InstrumentationScope } from '@opentelemetry/core';\nimport { MetricCollectOptions } from '../export/MetricProducer';\nimport { ScopeMetrics } from '../export/MetricData';\nimport {\n  createInstrumentDescriptorWithView,\n  InstrumentDescriptor,\n} from '../InstrumentDescriptor';\nimport { Meter } from '../Meter';\nimport { isNotNullish, Maybe } from '../utils';\nimport { AsyncMetricStorage } from './AsyncMetricStorage';\nimport { MeterProviderSharedState } from './MeterProviderSharedState';\nimport { MetricCollectorHandle } from './MetricCollector';\nimport { MetricStorageRegistry } from './MetricStorageRegistry';\nimport { MultiMetricStorage } from './MultiWritableMetricStorage';\nimport { ObservableRegistry } from './ObservableRegistry';\nimport { SyncMetricStorage } from './SyncMetricStorage';\nimport { Accumulation, Aggregator } from '../aggregator/types';\nimport { AttributesProcessor } from '../view/AttributesProcessor';\nimport { MetricStorage } from './MetricStorage';\n\n/**\n * An internal record for shared meter provider states.\n */\nexport class MeterSharedState {\n  metricStorageRegistry = new MetricStorageRegistry();\n  observableRegistry = new ObservableRegistry();\n  meter: Meter;\n\n  constructor(\n    private _meterProviderSharedState: MeterProviderSharedState,\n    private _instrumentationScope: InstrumentationScope\n  ) {\n    this.meter = new Meter(this);\n  }\n\n  registerMetricStorage(descriptor: InstrumentDescriptor) {\n    const storages = this._registerMetricStorage(descriptor, SyncMetricStorage);\n\n    if (storages.length === 1) {\n      return storages[0];\n    }\n    return new MultiMetricStorage(storages);\n  }\n\n  registerAsyncMetricStorage(descriptor: InstrumentDescriptor) {\n    const storages = this._registerMetricStorage(\n      descriptor,\n      AsyncMetricStorage\n    );\n\n    return storages;\n  }\n\n  /**\n   * @param collector opaque handle of {@link MetricCollector} which initiated the collection.\n   * @param collectionTime the HrTime at which the collection was initiated.\n   * @param options options for collection.\n   * @returns the list of metric data collected.\n   */\n  async collect(\n    collector: MetricCollectorHandle,\n    collectionTime: HrTime,\n    options?: MetricCollectOptions\n  ): Promise<ScopeMetricsResult | null> {\n    /**\n     * 1. Call all observable callbacks first.\n     * 2. Collect metric result for the collector.\n     */\n    const errors = await this.observableRegistry.observe(\n      collectionTime,\n      options?.timeoutMillis\n    );\n    const storages = this.metricStorageRegistry.getStorages(collector);\n\n    // prevent more allocations if there are no storages.\n    if (storages.length === 0) {\n      return null;\n    }\n\n    const metricDataList = storages\n      .map(metricStorage => {\n        return metricStorage.collect(collector, collectionTime);\n      })\n      .filter(isNotNullish);\n\n    // skip this scope if no data was collected (storage created, but no data observed)\n    if (metricDataList.length === 0) {\n      return { errors };\n    }\n\n    return {\n      scopeMetrics: {\n        scope: this._instrumentationScope,\n        metrics: metricDataList,\n      },\n      errors,\n    };\n  }\n\n  private _registerMetricStorage<\n    MetricStorageType extends MetricStorageConstructor,\n    R extends InstanceType<MetricStorageType>,\n  >(\n    descriptor: InstrumentDescriptor,\n    MetricStorageType: MetricStorageType\n  ): R[] {\n    const views = this._meterProviderSharedState.viewRegistry.findViews(\n      descriptor,\n      this._instrumentationScope\n    );\n    let storages = views.map(view => {\n      const viewDescriptor = createInstrumentDescriptorWithView(\n        view,\n        descriptor\n      );\n      const compatibleStorage =\n        this.metricStorageRegistry.findOrUpdateCompatibleStorage<R>(\n          viewDescriptor\n        );\n      if (compatibleStorage != null) {\n        return compatibleStorage;\n      }\n      const aggregator = view.aggregation.createAggregator(viewDescriptor);\n      const viewStorage = new MetricStorageType(\n        viewDescriptor,\n        aggregator,\n        view.attributesProcessor,\n        this._meterProviderSharedState.metricCollectors\n      ) as R;\n      this.metricStorageRegistry.register(viewStorage);\n      return viewStorage;\n    });\n\n    // Fallback to the per-collector aggregations if no view is configured for the instrument.\n    if (storages.length === 0) {\n      const perCollectorAggregations =\n        this._meterProviderSharedState.selectAggregations(descriptor.type);\n      const collectorStorages = perCollectorAggregations.map(\n        ([collector, aggregation]) => {\n          const compatibleStorage =\n            this.metricStorageRegistry.findOrUpdateCompatibleCollectorStorage<R>(\n              collector,\n              descriptor\n            );\n          if (compatibleStorage != null) {\n            return compatibleStorage;\n          }\n          const aggregator = aggregation.createAggregator(descriptor);\n          const storage = new MetricStorageType(\n            descriptor,\n            aggregator,\n            AttributesProcessor.Noop(),\n            [collector]\n          ) as R;\n          this.metricStorageRegistry.registerForCollector(collector, storage);\n          return storage;\n        }\n      );\n      storages = storages.concat(collectorStorages);\n    }\n\n    return storages;\n  }\n}\n\ninterface ScopeMetricsResult {\n  scopeMetrics?: ScopeMetrics;\n  errors: unknown[];\n}\n\ninterface MetricStorageConstructor {\n  new (\n    instrumentDescriptor: InstrumentDescriptor,\n    aggregator: Aggregator<Maybe<Accumulation>>,\n    attributesProcessor: AttributesProcessor,\n    collectors: MetricCollectorHandle[]\n  ): MetricStorage;\n}\n"]}