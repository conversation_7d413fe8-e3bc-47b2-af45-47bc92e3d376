"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
var utils_exports = {};
__export(utils_exports, {
  getAccessToken: () => getAccessToken,
  getProjectNumber: () => getProjectNumber
});
module.exports = __toCommonJS(utils_exports);
var import_googleapis = require("googleapis");
async function getAccessToken(auth) {
  const client = await auth.getClient();
  const _accessToken = await client.getAccessToken();
  return _accessToken.token || null;
}
async function getProjectNumber(projectId) {
  const client = import_googleapis.google.cloudresourcemanager("v1");
  const authClient = await import_googleapis.google.auth.getClient({
    scopes: ["https://www.googleapis.com/auth/cloud-platform"]
  });
  try {
    const response = await client.projects.get({
      projectId,
      auth: authClient
    });
    if (!response.data.projectNumber) {
      throw new Error(
        `Error fetching project number for Vertex AI plugin for project ${projectId}`
      );
    }
    return response.data["projectNumber"];
  } catch (error) {
    throw new Error(
      `Error fetching project number for Vertex AI plugin for project ${projectId}`
    );
  }
}
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  getAccessToken,
  getProjectNumber
});
//# sourceMappingURL=utils.js.map