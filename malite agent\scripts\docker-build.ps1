# PowerShell Script لبناء حاوية Docker لتطبيق Malite Agent
# يدعم بناء الحاوية للتطوير والإنتاج على Windows

param(
    [string]$ImageName = "malite-agent",
    [string]$Tag = "latest",
    [string]$Registry = "",
    [string]$Environment = "dev",
    [switch]$NoTest,
    [switch]$NoCache,
    [switch]$Cleanup,
    [switch]$Help
)

# ألوان للنص
$Red = "Red"
$Green = "Green"
$Yellow = "Yellow"
$Blue = "Blue"

# دوال مساعدة
function Write-Info {
    param([string]$Message)
    Write-Host "ℹ️  $Message" -ForegroundColor $Blue
}

function Write-Success {
    param([string]$Message)
    Write-Host "✅ $Message" -ForegroundColor $Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "⚠️  $Message" -ForegroundColor $Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "❌ $Message" -ForegroundColor $Red
}

function Write-Header {
    param([string]$Message)
    Write-Host ""
    Write-Host "==================================================" -ForegroundColor $Blue
    Write-Host "🐳 $Message" -ForegroundColor $Blue
    Write-Host "==================================================" -ForegroundColor $Blue
    Write-Host ""
}

# عرض المساعدة
function Show-Help {
    Write-Host "الاستخدام: .\docker-build.ps1 [OPTIONS]"
    Write-Host ""
    Write-Host "الخيارات:"
    Write-Host "  -ImageName NAME          اسم الصورة (افتراضي: malite-agent)"
    Write-Host "  -Tag TAG                علامة الصورة (افتراضي: latest)"
    Write-Host "  -Registry REGISTRY       رفع إلى Registry"
    Write-Host "  -Environment ENV         بيئة البناء (dev|prod)"
    Write-Host "  -NoTest                  تخطي الاختبارات"
    Write-Host "  -NoCache                 بناء بدون استخدام Cache"
    Write-Host "  -Cleanup                 تنظيف الصور القديمة"
    Write-Host "  -Help                    عرض هذه المساعدة"
    Write-Host ""
    Write-Host "أمثلة:"
    Write-Host "  .\docker-build.ps1                                    # بناء أساسي"
    Write-Host "  .\docker-build.ps1 -ImageName myapp -Tag v1.0.0      # بناء مع اسم وعلامة مخصصة"
    Write-Host "  .\docker-build.ps1 -Registry gcr.io/my-project       # بناء ورفع إلى GCR"
    Write-Host "  .\docker-build.ps1 -Environment prod -NoTest         # بناء للإنتاج بدون اختبارات"
}

# التحقق من المتطلبات
function Test-Requirements {
    Write-Header "التحقق من المتطلبات"
    
    # التحقق من وجود Docker
    try {
        $dockerVersion = docker --version
        Write-Success "Docker مثبت: $dockerVersion"
    }
    catch {
        Write-Error "Docker غير مثبت. يرجى تثبيته من: https://docs.docker.com/get-docker/"
        exit 1
    }
    
    # التحقق من تشغيل Docker
    try {
        docker info | Out-Null
        Write-Success "Docker يعمل بشكل صحيح"
    }
    catch {
        Write-Error "Docker غير يعمل. يرجى تشغيل Docker Desktop"
        exit 1
    }
    
    # التحقق من وجود Dockerfile
    if (-not (Test-Path "Dockerfile")) {
        Write-Error "ملف Dockerfile غير موجود"
        exit 1
    }
    Write-Success "ملف Dockerfile موجود"
}

# بناء الصورة
function Build-Image {
    param(
        [string]$ImageName,
        [string]$Tag,
        [string]$BuildArgs
    )
    
    Write-Header "بناء صورة Docker"
    
    Write-Info "اسم الصورة: $ImageName`:$Tag"
    Write-Info "بدء عملية البناء..."
    
    # إعداد أمر البناء
    $buildCommand = "docker build"
    
    if ($BuildArgs) {
        $buildCommand += " $BuildArgs"
    }
    
    $buildCommand += " -t $ImageName`:$Tag -t $ImageName`:latest ."
    
    Write-Info "تنفيذ: $buildCommand"
    
    # تنفيذ البناء
    try {
        Invoke-Expression $buildCommand
        Write-Success "تم بناء الصورة بنجاح"
    }
    catch {
        Write-Error "فشل في بناء الصورة: $_"
        exit 1
    }
    
    # عرض معلومات الصورة
    Write-Info "معلومات الصورة:"
    docker images $ImageName --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}\t{{.CreatedAt}}"
}

# اختبار الصورة
function Test-Image {
    param(
        [string]$ImageName,
        [string]$Tag
    )
    
    Write-Header "اختبار الصورة"
    
    Write-Info "تشغيل اختبار سريع للصورة..."
    
    # تشغيل حاوية مؤقتة للاختبار
    try {
        $containerId = docker run -d -p 8081:8080 --name malite-test "$ImageName`:$Tag"
        Write-Info "تم تشغيل حاوية الاختبار: $containerId"
        
        # انتظار حتى تصبح الحاوية جاهزة
        Write-Info "انتظار تشغيل الحاوية..."
        Start-Sleep -Seconds 15
        
        # اختبار فحص الصحة
        try {
            $response = Invoke-WebRequest -Uri "http://localhost:8081/health" -TimeoutSec 10
            if ($response.StatusCode -eq 200) {
                Write-Success "اختبار فحص الصحة نجح"
                $testPassed = $true
            }
            else {
                Write-Warning "اختبار فحص الصحة فشل - Status Code: $($response.StatusCode)"
                $testPassed = $false
            }
        }
        catch {
            Write-Warning "اختبار فحص الصحة فشل: $_"
            $testPassed = $false
        }
        
        # إيقاف وحذف الحاوية المؤقتة
        docker stop $containerId | Out-Null
        docker rm $containerId | Out-Null
        
        if ($testPassed) {
            Write-Success "جميع الاختبارات نجحت"
        }
        else {
            Write-Warning "بعض الاختبارات فشلت"
        }
    }
    catch {
        Write-Error "فشل في تشغيل اختبار الصورة: $_"
    }
}

# رفع الصورة إلى Registry
function Push-Image {
    param(
        [string]$ImageName,
        [string]$Tag,
        [string]$Registry
    )
    
    if ($Registry) {
        Write-Header "رفع الصورة إلى Registry"
        
        # إعادة تسمية الصورة للـ Registry
        $registryImage = "$Registry/$ImageName"
        
        try {
            docker tag "$ImageName`:$Tag" "$registryImage`:$Tag"
            docker tag "$ImageName`:$Tag" "$registryImage`:latest"
            
            Write-Info "رفع الصورة إلى $Registry..."
            docker push "$registryImage`:$Tag"
            docker push "$registryImage`:latest"
            
            Write-Success "تم رفع الصورة بنجاح"
            Write-Info "الصورة متاحة على: $registryImage`:$Tag"
        }
        catch {
            Write-Error "فشل في رفع الصورة: $_"
        }
    }
}

# تنظيف الصور القديمة
function Invoke-Cleanup {
    Write-Header "تنظيف الصور القديمة"
    
    try {
        Write-Info "حذف الصور غير المستخدمة..."
        docker image prune -f
        
        Write-Info "حذف الحاويات المتوقفة..."
        docker container prune -f
        
        Write-Success "تم التنظيف بنجاح"
    }
    catch {
        Write-Warning "حدث خطأ أثناء التنظيف: $_"
    }
}

# الدالة الرئيسية
function Main {
    if ($Help) {
        Show-Help
        return
    }
    
    Write-Header "بناء حاوية Malite Agent"
    
    # إعداد معاملات البناء
    $buildArgs = @()
    
    if ($NoCache) {
        $buildArgs += "--no-cache"
    }
    
    if ($Environment -eq "prod") {
        $buildArgs += "--build-arg NODE_ENV=production"
        $Tag = "$Tag-prod"
    }
    
    $buildArgsString = $buildArgs -join " "
    
    # تنفيذ خطوات البناء
    Test-Requirements
    Build-Image -ImageName $ImageName -Tag $Tag -BuildArgs $buildArgsString
    
    if (-not $NoTest) {
        Test-Image -ImageName $ImageName -Tag $Tag
    }
    
    if ($Registry) {
        Push-Image -ImageName $ImageName -Tag $Tag -Registry $Registry
    }
    
    if ($Cleanup) {
        Invoke-Cleanup
    }
    
    Write-Header "اكتمل البناء بنجاح"
    Write-Success "الصورة جاهزة: $ImageName`:$Tag"
    
    Write-Host ""
    Write-Host "🚀 أوامر مفيدة:"
    Write-Host "   تشغيل الحاوية: docker run -p 8080:8080 $ImageName`:$Tag"
    Write-Host "   عرض السجلات: docker logs <container_id>"
    Write-Host "   دخول الحاوية: docker exec -it <container_id> sh"
}

# تشغيل الدالة الرئيسية
Main
