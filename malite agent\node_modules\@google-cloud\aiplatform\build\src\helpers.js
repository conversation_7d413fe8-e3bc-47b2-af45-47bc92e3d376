"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.addToValue = addToValue;
exports.toValue = toValue;
exports.fromValue = fromValue;
// Copyright 2020 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     https://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//
const value_converter_1 = require("./value-converter");
// Assigns the toValue() function as a member of an enhanced class.
function addToValue() {
    const methods = {};
    methods.toValue = function () {
        return toValue(this);
    };
    return methods;
}
/**
 * Converts an object or protobuf.Message to a protobuf.Value object.
 * @param message Object or protobuf.Message to convert
 * @returns a Value-formatted object
 */
function toValue(message) {
    if (message === undefined) {
        return undefined;
    }
    const value = (0, value_converter_1.googleProtobufValueFromObject)(message, (val) => {
        return val;
    });
    return value;
}
/**
 * Creates instance of class from a protobuf.Value object.
 * @param value Value to convert
 * @returns a Message
 */
function fromValue(value) {
    if (!value) {
        return undefined;
    }
    if (!value.structValue || !value.structValue.fields) {
        throw new Error('ERROR: fromValue() was provided a malformed protobuf object');
    }
    return (0, value_converter_1.googleProtobufValueToObject)(value);
}
//# sourceMappingURL=helpers.js.map