{"version": 3, "file": "ExponentMapping.js", "sourceRoot": "", "sources": ["../../../../../src/aggregator/exponential-histogram/mapping/ExponentMapping.ts"], "names": [], "mappings": ";;;AAAA;;;;;;;;;;;;;;GAcG;AACH,qCAAqC;AACrC,gCAAgC;AAChC,mCAAgD;AAEhD;;;GAGG;AACH,MAAa,eAAe;IAG1B,YAAY,KAAa;QACvB,IAAI,CAAC,MAAM,GAAG,CAAC,KAAK,CAAC;IACvB,CAAC;IAED;;;;OAIG;IACH,UAAU,CAAC,KAAa;QACtB,IAAI,KAAK,GAAG,OAAO,CAAC,SAAS,EAAE;YAC7B,OAAO,IAAI,CAAC,4BAA4B,EAAE,CAAC;SAC5C;QAED,MAAM,GAAG,GAAG,OAAO,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;QAE1C,wDAAwD;QACxD,4DAA4D;QAC5D,6DAA6D;QAC7D,2BAA2B;QAC3B,MAAM,UAAU,GAAG,IAAI,CAAC,WAAW,CACjC,OAAO,CAAC,cAAc,CAAC,KAAK,CAAC,GAAG,CAAC,EACjC,OAAO,CAAC,iBAAiB,CAC1B,CAAC;QAEF,OAAO,CAAC,GAAG,GAAG,UAAU,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC;IAC3C,CAAC;IAED;;;;;OAKG;IACH,aAAa,CAAC,KAAa;QACzB,MAAM,QAAQ,GAAG,IAAI,CAAC,4BAA4B,EAAE,CAAC;QACrD,IAAI,KAAK,GAAG,QAAQ,EAAE;YACpB,MAAM,IAAI,oBAAY,CACpB,cAAc,KAAK,iCAAiC,QAAQ,EAAE,CAC/D,CAAC;SACH;QACD,MAAM,QAAQ,GAAG,IAAI,CAAC,4BAA4B,EAAE,CAAC;QACrD,IAAI,KAAK,GAAG,QAAQ,EAAE;YACpB,MAAM,IAAI,oBAAY,CACpB,aAAa,KAAK,iCAAiC,QAAQ,EAAE,CAC9D,CAAC;SACH;QAED,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC;IAC7C,CAAC;IAED;;;OAGG;IACH,IAAI,KAAK;QACP,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;YACrB,OAAO,CAAC,CAAC;SACV;QACD,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC;IACtB,CAAC;IAEO,4BAA4B;QAClC,IAAI,KAAK,GAAG,OAAO,CAAC,mBAAmB,IAAI,IAAI,CAAC,MAAM,CAAC;QACvD,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE;YACnB,KAAK,EAAE,CAAC;SACT;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAEO,4BAA4B;QAClC,OAAO,OAAO,CAAC,mBAAmB,IAAI,IAAI,CAAC,MAAM,CAAC;IACpD,CAAC;IAEO,WAAW,CAAC,KAAa,EAAE,KAAa;QAC9C,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC;IACjD,CAAC;CACF;AAjFD,0CAiFC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport * as ieee754 from './ieee754';\nimport * as util from '../util';\nimport { Mapping, MappingError } from './types';\n\n/**\n * ExponentMapping implements exponential mapping functions for\n * scales <=0. For scales > 0 LogarithmMapping should be used.\n */\nexport class ExponentMapping implements Mapping {\n  private readonly _shift: number;\n\n  constructor(scale: number) {\n    this._shift = -scale;\n  }\n\n  /**\n   * Maps positive floating point values to indexes corresponding to scale\n   * @param value\n   * @returns {number} index for provided value at the current scale\n   */\n  mapToIndex(value: number): number {\n    if (value < ieee754.MIN_VALUE) {\n      return this._minNormalLowerBoundaryIndex();\n    }\n\n    const exp = ieee754.getNormalBase2(value);\n\n    // In case the value is an exact power of two, compute a\n    // correction of -1. Note, we are using a custom _rightShift\n    // to accommodate a 52-bit argument, which the native bitwise\n    // operators do not support\n    const correction = this._rightShift(\n      ieee754.getSignificand(value) - 1,\n      ieee754.SIGNIFICAND_WIDTH\n    );\n\n    return (exp + correction) >> this._shift;\n  }\n\n  /**\n   * Returns the lower bucket boundary for the given index for scale\n   *\n   * @param index\n   * @returns {number}\n   */\n  lowerBoundary(index: number): number {\n    const minIndex = this._minNormalLowerBoundaryIndex();\n    if (index < minIndex) {\n      throw new MappingError(\n        `underflow: ${index} is < minimum lower boundary: ${minIndex}`\n      );\n    }\n    const maxIndex = this._maxNormalLowerBoundaryIndex();\n    if (index > maxIndex) {\n      throw new MappingError(\n        `overflow: ${index} is > maximum lower boundary: ${maxIndex}`\n      );\n    }\n\n    return util.ldexp(1, index << this._shift);\n  }\n\n  /**\n   * The scale used by this mapping\n   * @returns {number}\n   */\n  get scale(): number {\n    if (this._shift === 0) {\n      return 0;\n    }\n    return -this._shift;\n  }\n\n  private _minNormalLowerBoundaryIndex(): number {\n    let index = ieee754.MIN_NORMAL_EXPONENT >> this._shift;\n    if (this._shift < 2) {\n      index--;\n    }\n\n    return index;\n  }\n\n  private _maxNormalLowerBoundaryIndex(): number {\n    return ieee754.MAX_NORMAL_EXPONENT >> this._shift;\n  }\n\n  private _rightShift(value: number, shift: number): number {\n    return Math.floor(value * Math.pow(2, -shift));\n  }\n}\n"]}