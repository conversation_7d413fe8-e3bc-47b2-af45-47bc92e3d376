{"interfaces": {"google.cloud.aiplatform.v1.VizierService": {"retry_codes": {"non_idempotent": [], "idempotent": ["DEADLINE_EXCEEDED", "UNAVAILABLE"]}, "retry_params": {"default": {"initial_retry_delay_millis": 100, "retry_delay_multiplier": 1.3, "max_retry_delay_millis": 60000, "initial_rpc_timeout_millis": 60000, "rpc_timeout_multiplier": 1, "max_rpc_timeout_millis": 60000, "total_timeout_millis": 600000}}, "methods": {"CreateStudy": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "GetStudy": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "ListStudies": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "DeleteStudy": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "LookupStudy": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "SuggestTrials": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "CreateTrial": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "GetTrial": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "ListTrials": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "AddTrialMeasurement": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "CompleteTrial": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "DeleteTrial": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "CheckTrialEarlyStoppingState": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "StopTrial": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "ListOptimalTrials": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}}}}}