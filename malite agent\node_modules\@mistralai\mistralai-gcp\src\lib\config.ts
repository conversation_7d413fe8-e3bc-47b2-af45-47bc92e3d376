/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import { HTTPClient } from "./http.js";
import { Logger } from "./logger.js";
import { RetryConfig } from "./retries.js";
import { Params, pathToFunc } from "./url.js";

/**
 * EU Production server
 */
export const ServerEu = "eu";
/**
 * Contains the list of servers available to the SDK
 */
export const ServerList = {
  [ServerEu]: "https://api.mistral.ai",
} as const;

export type SDKOptions = {
  apiKey?: string | (() => Promise<string>) | undefined;

  httpClient?: HTTPClient;
  /**
   * Allows overriding the default server used by the SDK
   */
  server?: keyof typeof ServerList | undefined;
  /**
   * Allows overriding the default server URL used by the SDK
   */
  serverURL?: string | undefined;
  /**
   * Allows overriding the default retry config used by the SDK
   */
  retryConfig?: RetryConfig;
  timeoutMs?: number;
  debugLogger?: Logger;
};

export function serverURLFromOptions(options: SDKOptions): URL | null {
  let serverURL = options.serverURL;

  const params: Params = {};

  if (!serverURL) {
    const server = options.server ?? ServerEu;
    serverURL = ServerList[server] || "";
  }

  const u = pathToFunc(serverURL)(params);
  return new URL(u);
}

export const SDK_METADATA = {
  language: "typescript",
  openapiDocVersion: "0.0.2",
  sdkVersion: "1.5.0",
  genVersion: "2.548.6",
  userAgent:
    "speakeasy-sdk/typescript 1.5.0 2.548.6 0.0.2 @mistralai/mistralai-gcp",
} as const;
