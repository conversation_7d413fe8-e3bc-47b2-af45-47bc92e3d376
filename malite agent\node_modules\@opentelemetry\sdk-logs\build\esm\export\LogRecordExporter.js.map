{"version": 3, "file": "LogRecordExporter.js", "sourceRoot": "", "sources": ["../../../src/export/LogRecordExporter.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport type { ExportResult } from '@opentelemetry/core';\n\nimport type { ReadableLogRecord } from './ReadableLogRecord';\n\nexport interface LogRecordExporter {\n  /**\n   * Called to export {@link ReadableLogRecord}s.\n   * @param logs the list of sampled LogRecords to be exported.\n   */\n  export(\n    logs: ReadableLogRecord[],\n    resultCallback: (result: ExportResult) => void\n  ): void;\n\n  /** Stops the exporter. */\n  shutdown(): Promise<void>;\n}\n"]}