// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.aiplatform.v1beta1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/aiplatform/v1beta1/encryption_spec.proto";
import "google/cloud/aiplatform/v1beta1/service_networking.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.AIPlatform.V1Beta1";
option go_package = "cloud.google.com/go/aiplatform/apiv1beta1/aiplatformpb;aiplatformpb";
option java_multiple_files = true;
option java_outer_classname = "FeatureOnlineStoreProto";
option java_package = "com.google.cloud.aiplatform.v1beta1";
option php_namespace = "Google\\Cloud\\AIPlatform\\V1beta1";
option ruby_package = "Google::Cloud::AIPlatform::V1beta1";

// Vertex AI Feature Online Store provides a centralized repository for serving
// ML features and embedding indexes at low latency. The Feature Online Store is
// a top-level container.
message FeatureOnlineStore {
  option (google.api.resource) = {
    type: "aiplatform.googleapis.com/FeatureOnlineStore"
    pattern: "projects/{project}/locations/{location}/featureOnlineStores/{feature_online_store}"
  };

  message Bigtable {
    message AutoScaling {
      // Required. The minimum number of nodes to scale down to. Must be greater
      // than or equal to 1.
      int32 min_node_count = 1 [(google.api.field_behavior) = REQUIRED];

      // Required. The maximum number of nodes to scale up to. Must be greater
      // than or equal to min_node_count, and less than or equal to 10 times of
      // 'min_node_count'.
      int32 max_node_count = 2 [(google.api.field_behavior) = REQUIRED];

      // Optional. A percentage of the cluster's CPU capacity. Can be from 10%
      // to 80%. When a cluster's CPU utilization exceeds the target that you
      // have set, Bigtable immediately adds nodes to the cluster. When CPU
      // utilization is substantially lower than the target, Bigtable removes
      // nodes. If not set will default to 50%.
      int32 cpu_utilization_target = 3 [(google.api.field_behavior) = OPTIONAL];
    }

    // Required. Autoscaling config applied to Bigtable Instance.
    AutoScaling auto_scaling = 1 [(google.api.field_behavior) = REQUIRED];
  }

  // Optimized storage type
  message Optimized {}

  // The dedicated serving endpoint for this FeatureOnlineStore. Only need to
  // set when you choose Optimized storage type. Public endpoint is provisioned
  // by default.
  message DedicatedServingEndpoint {
    // Output only. This field will be populated with the domain name to use for
    // this FeatureOnlineStore
    string public_endpoint_domain_name = 2
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Optional. Private service connect config. The private service connection
    // is available only for Optimized storage type, not for embedding
    // management now. If
    // [PrivateServiceConnectConfig.enable_private_service_connect][google.cloud.aiplatform.v1beta1.PrivateServiceConnectConfig.enable_private_service_connect]
    // set to true, customers will use private service connection to send
    // request. Otherwise, the connection will set to public endpoint.
    PrivateServiceConnectConfig private_service_connect_config = 3
        [(google.api.field_behavior) = OPTIONAL];

    // Output only. The name of the service attachment resource. Populated if
    // private service connect is enabled and after FeatureViewSync is created.
    string service_attachment = 4 [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // Deprecated: This sub message is no longer needed anymore and embedding
  // management is automatically enabled when specifying Optimized storage type.
  // Contains settings for embedding management.
  message EmbeddingManagement {
    option deprecated = true;

    // Optional. Immutable. Whether to enable embedding management in this
    // FeatureOnlineStore. It's immutable after creation to ensure the
    // FeatureOnlineStore availability.
    bool enabled = 1 [
      (google.api.field_behavior) = OPTIONAL,
      (google.api.field_behavior) = IMMUTABLE
    ];
  }

  // Possible states a featureOnlineStore can have.
  enum State {
    // Default value. This value is unused.
    STATE_UNSPECIFIED = 0;

    // State when the featureOnlineStore configuration is not being updated and
    // the fields reflect the current configuration of the featureOnlineStore.
    // The featureOnlineStore is usable in this state.
    STABLE = 1;

    // The state of the featureOnlineStore configuration when it is being
    // updated. During an update, the fields reflect either the original
    // configuration or the updated configuration of the featureOnlineStore. The
    // featureOnlineStore is still usable in this state.
    UPDATING = 2;
  }

  oneof storage_type {
    // Contains settings for the Cloud Bigtable instance that will be created
    // to serve featureValues for all FeatureViews under this
    // FeatureOnlineStore.
    Bigtable bigtable = 8;

    // Contains settings for the Optimized store that will be created
    // to serve featureValues for all FeatureViews under this
    // FeatureOnlineStore. When choose Optimized storage type, need to set
    // [PrivateServiceConnectConfig.enable_private_service_connect][google.cloud.aiplatform.v1beta1.PrivateServiceConnectConfig.enable_private_service_connect]
    // to use private endpoint. Otherwise will use public endpoint by default.
    Optimized optimized = 12;
  }

  // Identifier. Name of the FeatureOnlineStore. Format:
  // `projects/{project}/locations/{location}/featureOnlineStores/{featureOnlineStore}`
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // Output only. Timestamp when this FeatureOnlineStore was created.
  google.protobuf.Timestamp create_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Timestamp when this FeatureOnlineStore was last updated.
  google.protobuf.Timestamp update_time = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Used to perform consistent read-modify-write updates. If not set,
  // a blind "overwrite" update happens.
  string etag = 5 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The labels with user-defined metadata to organize your
  // FeatureOnlineStore.
  //
  // Label keys and values can be no longer than 64 characters
  // (Unicode codepoints), can only contain lowercase letters, numeric
  // characters, underscores and dashes. International characters are allowed.
  //
  // See https://goo.gl/xmQnxf for more information on and examples of labels.
  // No more than 64 user labels can be associated with one
  // FeatureOnlineStore(System labels are excluded)." System reserved label keys
  // are prefixed with "aiplatform.googleapis.com/" and are immutable.
  map<string, string> labels = 6 [(google.api.field_behavior) = OPTIONAL];

  // Output only. State of the featureOnlineStore.
  State state = 7 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. The dedicated serving endpoint for this FeatureOnlineStore, which
  // is different from common Vertex service endpoint.
  DedicatedServingEndpoint dedicated_serving_endpoint = 10
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. Deprecated: This field is no longer needed anymore and embedding
  // management is automatically enabled when specifying Optimized storage type.
  EmbeddingManagement embedding_management = 11
      [deprecated = true, (google.api.field_behavior) = OPTIONAL];

  // Optional. Customer-managed encryption key spec for data storage. If set,
  // online store will be secured by this key.
  EncryptionSpec encryption_spec = 13 [(google.api.field_behavior) = OPTIONAL];

  // Output only. Reserved for future use.
  bool satisfies_pzs = 15 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Reserved for future use.
  bool satisfies_pzi = 16 [(google.api.field_behavior) = OUTPUT_ONLY];
}
