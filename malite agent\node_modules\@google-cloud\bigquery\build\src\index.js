"use strict";
/*!
 * Copyright 2019 Google Inc. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.Table = exports.RowQueue = exports.RowBatch = exports.Routine = exports.Model = exports.Job = exports.Dataset = exports.PROTOCOL_REGEX = exports.Geography = exports.common = exports.BigQueryTimestamp = exports.BigQueryTime = exports.BigQueryInt = exports.BigQueryDatetime = exports.BigQueryDate = exports.BigQuery = void 0;
var bigquery_1 = require("./bigquery");
Object.defineProperty(exports, "BigQuery", { enumerable: true, get: function () { return bigquery_1.BigQuery; } });
Object.defineProperty(exports, "BigQueryDate", { enumerable: true, get: function () { return bigquery_1.BigQueryDate; } });
Object.defineProperty(exports, "BigQueryDatetime", { enumerable: true, get: function () { return bigquery_1.BigQueryDatetime; } });
Object.defineProperty(exports, "BigQueryInt", { enumerable: true, get: function () { return bigquery_1.BigQueryInt; } });
Object.defineProperty(exports, "BigQueryTime", { enumerable: true, get: function () { return bigquery_1.BigQueryTime; } });
Object.defineProperty(exports, "BigQueryTimestamp", { enumerable: true, get: function () { return bigquery_1.BigQueryTimestamp; } });
Object.defineProperty(exports, "common", { enumerable: true, get: function () { return bigquery_1.common; } });
Object.defineProperty(exports, "Geography", { enumerable: true, get: function () { return bigquery_1.Geography; } });
Object.defineProperty(exports, "PROTOCOL_REGEX", { enumerable: true, get: function () { return bigquery_1.PROTOCOL_REGEX; } });
var dataset_1 = require("./dataset");
Object.defineProperty(exports, "Dataset", { enumerable: true, get: function () { return dataset_1.Dataset; } });
var job_1 = require("./job");
Object.defineProperty(exports, "Job", { enumerable: true, get: function () { return job_1.Job; } });
var model_1 = require("./model");
Object.defineProperty(exports, "Model", { enumerable: true, get: function () { return model_1.Model; } });
var routine_1 = require("./routine");
Object.defineProperty(exports, "Routine", { enumerable: true, get: function () { return routine_1.Routine; } });
var rowBatch_1 = require("./rowBatch");
Object.defineProperty(exports, "RowBatch", { enumerable: true, get: function () { return rowBatch_1.RowBatch; } });
var rowQueue_1 = require("./rowQueue");
Object.defineProperty(exports, "RowQueue", { enumerable: true, get: function () { return rowQueue_1.RowQueue; } });
var table_1 = require("./table");
Object.defineProperty(exports, "Table", { enumerable: true, get: function () { return table_1.Table; } });
//# sourceMappingURL=index.js.map