import { GenkitError } from "@genkit-ai/core";
import { extractItems } from "../extract";
const arrayFormatter = {
  name: "array",
  config: {
    contentType: "application/json",
    constrained: true
  },
  handler: (schema) => {
    if (schema && schema.type !== "array") {
      throw new GenkitError({
        status: "INVALID_ARGUMENT",
        message: `Must supply an 'array' schema type when using the 'items' parser format.`
      });
    }
    let instructions;
    if (schema) {
      instructions = `Output should be a JSON array conforming to the following schema:
    
\`\`\`
${JSON.stringify(schema)}
\`\`\`
    `;
    }
    return {
      parseChunk: (chunk) => {
        const cursor = chunk.previousChunks?.length ? extractItems(chunk.previousText).cursor : 0;
        const { items } = extractItems(chunk.accumulatedText, cursor);
        return items;
      },
      parseMessage: (message) => {
        const { items } = extractItems(message.text, 0);
        return items;
      },
      instructions
    };
  }
};
export {
  arrayFormatter
};
//# sourceMappingURL=array.mjs.map