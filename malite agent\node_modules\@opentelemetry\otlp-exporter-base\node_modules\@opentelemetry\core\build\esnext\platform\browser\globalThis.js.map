{"version": 3, "file": "globalThis.js", "sourceRoot": "", "sources": ["../../../../src/platform/browser/globalThis.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAEH,4EAA4E;AAE5E;;;;;;GAMG;AAEH,gEAAgE;AAChE,8EAA8E;AAC9E,MAAM,CAAC,MAAM,WAAW,GACtB,OAAO,UAAU,KAAK,QAAQ;IAC5B,CAAC,CAAC,UAAU;IACZ,CAAC,CAAC,OAAO,IAAI,KAAK,QAAQ;QAC1B,CAAC,CAAC,IAAI;QACN,CAAC,CAAC,OAAO,MAAM,KAAK,QAAQ;YAC5B,CAAC,CAAC,MAAM;YACR,CAAC,CAAC,OAAO,MAAM,KAAK,QAAQ;gBAC5B,CAAC,CAAC,MAAM;gBACR,CAAC,CAAE,EAAwB,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n// Updates to this file should also be replicated to @opentelemetry/api too.\n\n/**\n * - globalThis (New standard)\n * - self (Will return the current window instance for supported browsers)\n * - window (fallback for older browser implementations)\n * - global (NodeJS implementation)\n * - <object> (When all else fails)\n */\n\n/** only globals that common to node and browsers are allowed */\n// eslint-disable-next-line node/no-unsupported-features/es-builtins, no-undef\nexport const _globalThis: typeof globalThis =\n  typeof globalThis === 'object'\n    ? globalThis\n    : typeof self === 'object'\n    ? self\n    : typeof window === 'object'\n    ? window\n    : typeof global === 'object'\n    ? global\n    : ({} as typeof globalThis);\n"]}