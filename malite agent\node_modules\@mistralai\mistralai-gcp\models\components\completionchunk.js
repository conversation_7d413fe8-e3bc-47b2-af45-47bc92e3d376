"use strict";
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CompletionChunk$ = exports.CompletionChunk$outboundSchema = exports.CompletionChunk$inboundSchema = void 0;
exports.completionChunkToJSON = completionChunkToJSON;
exports.completionChunkFromJSON = completionChunkFromJSON;
const z = __importStar(require("zod"));
const schemas_js_1 = require("../../lib/schemas.js");
const completionresponsestreamchoice_js_1 = require("./completionresponsestreamchoice.js");
const usageinfo_js_1 = require("./usageinfo.js");
/** @internal */
exports.CompletionChunk$inboundSchema = z.object({
    id: z.string(),
    object: z.string().optional(),
    created: z.number().int().optional(),
    model: z.string(),
    usage: usageinfo_js_1.UsageInfo$inboundSchema.optional(),
    choices: z.array(completionresponsestreamchoice_js_1.CompletionResponseStreamChoice$inboundSchema),
});
/** @internal */
exports.CompletionChunk$outboundSchema = z.object({
    id: z.string(),
    object: z.string().optional(),
    created: z.number().int().optional(),
    model: z.string(),
    usage: usageinfo_js_1.UsageInfo$outboundSchema.optional(),
    choices: z.array(completionresponsestreamchoice_js_1.CompletionResponseStreamChoice$outboundSchema),
});
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var CompletionChunk$;
(function (CompletionChunk$) {
    /** @deprecated use `CompletionChunk$inboundSchema` instead. */
    CompletionChunk$.inboundSchema = exports.CompletionChunk$inboundSchema;
    /** @deprecated use `CompletionChunk$outboundSchema` instead. */
    CompletionChunk$.outboundSchema = exports.CompletionChunk$outboundSchema;
})(CompletionChunk$ || (exports.CompletionChunk$ = CompletionChunk$ = {}));
function completionChunkToJSON(completionChunk) {
    return JSON.stringify(exports.CompletionChunk$outboundSchema.parse(completionChunk));
}
function completionChunkFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.CompletionChunk$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'CompletionChunk' from JSON`);
}
//# sourceMappingURL=completionchunk.js.map