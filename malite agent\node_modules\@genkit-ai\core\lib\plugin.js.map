{"version": 3, "sources": ["../src/plugin.ts"], "sourcesContent": ["/**\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { z } from 'zod';\nimport { Action, ActionMetadata } from './action.js';\nimport { ActionType } from './registry.js';\n\nexport interface Provider<T> {\n  id: string;\n  value: T;\n}\n\nexport interface PluginProvider {\n  name: string;\n  initializer: () =>\n    | InitializedPlugin\n    | void\n    | Promise<InitializedPlugin | void>;\n  resolver?: (action: ActionType, target: string) => Promise<void>;\n  listActions?: () => Promise<ActionMetadata[]>;\n}\n\nexport interface InitializedPlugin {\n  models?: Action<z.ZodType<PERSON>ny, z.ZodTypeAny>[];\n  retrievers?: Action<z.Zod<PERSON>ype<PERSON>, z.ZodTypeAny>[];\n  embedders?: Action<z.Zod<PERSON>, z.ZodTypeAny>[];\n  indexers?: Action<z.ZodTypeAny, z.ZodTypeAny>[];\n  evaluators?: Action<z.ZodTypeAny, z.ZodTypeAny>[];\n  /** @deprecated */\n  flowStateStore?: Provider<any> | Provider<any>[];\n  /** @deprecated */\n  traceStore?: Provider<any> | Provider<any>[];\n  /** @deprecated */\n  telemetry?: any;\n}\n\nexport type Plugin<T extends any[]> = (...args: T) => PluginProvider;\n"], "mappings": ";;;;;;;;;;;;;;AAAA;AAAA;", "names": []}