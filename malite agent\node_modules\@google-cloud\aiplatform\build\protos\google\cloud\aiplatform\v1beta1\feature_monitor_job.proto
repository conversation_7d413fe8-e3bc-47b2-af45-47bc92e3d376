// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.aiplatform.v1beta1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/aiplatform/v1beta1/feature_monitor.proto";
import "google/protobuf/timestamp.proto";
import "google/rpc/status.proto";

option csharp_namespace = "Google.Cloud.AIPlatform.V1Beta1";
option go_package = "cloud.google.com/go/aiplatform/apiv1beta1/aiplatformpb;aiplatformpb";
option java_multiple_files = true;
option java_outer_classname = "FeatureMonitorJobProto";
option java_package = "com.google.cloud.aiplatform.v1beta1";
option php_namespace = "Google\\Cloud\\AIPlatform\\V1beta1";
option ruby_package = "Google::Cloud::AIPlatform::V1beta1";

// Vertex AI Feature Monitor Job.
message FeatureMonitorJob {
  option (google.api.resource) = {
    type: "aiplatform.googleapis.com/FeatureMonitorJob"
    pattern: "projects/{project}/locations/{location}/featureGroups/{feature_group}/featureMonitors/{feature_monitor}/featureMonitorJobs/{feature_monitor_job}"
    plural: "featureMonitorJobs"
    singular: "featureMonitorJob"
  };

  // Summary from the FeatureMonitorJob.
  message JobSummary {
    // Output only. BigQuery slot milliseconds consumed.
    int64 total_slot_ms = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Features and their stats and anomalies
    repeated FeatureStatsAndAnomaly feature_stats_and_anomalies = 2
        [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // Choices of the trigger type.
  enum FeatureMonitorJobTrigger {
    // Trigger type unspecified.
    FEATURE_MONITOR_JOB_TRIGGER_UNSPECIFIED = 0;

    // Triggered by periodic schedule.
    FEATURE_MONITOR_JOB_TRIGGER_PERIODIC = 1;

    // Triggered on demand by CreateFeatureMonitorJob request.
    FEATURE_MONITOR_JOB_TRIGGER_ON_DEMAND = 2;
  }

  // Identifier. Name of the FeatureMonitorJob. Format:
  // `projects/{project}/locations/{location}/featureGroups/{feature_group}/featureMonitors/{feature_monitor}/featureMonitorJobs/{feature_monitor_job}`.
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // Output only. Timestamp when this FeatureMonitorJob was created. Creation of
  // a FeatureMonitorJob means that the job is pending / waiting for sufficient
  // resources but may not have started running yet.
  google.protobuf.Timestamp create_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Final status of the FeatureMonitorJob.
  google.rpc.Status final_status = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Summary from the FeatureMonitorJob.
  JobSummary job_summary = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. The labels with user-defined metadata to organize your
  // FeatureMonitorJob.
  //
  // Label keys and values can be no longer than 64 characters
  // (Unicode codepoints), can only contain lowercase letters, numeric
  // characters, underscores and dashes. International characters are allowed.
  //
  // See https://goo.gl/xmQnxf for more information on and examples of labels.
  // No more than 64 user labels can be associated with one
  // FeatureMonitor(System labels are excluded)." System reserved label keys
  // are prefixed with "aiplatform.googleapis.com/" and are immutable.
  map<string, string> labels = 5 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Description of the FeatureMonitor.
  string description = 6 [(google.api.field_behavior) = OPTIONAL];

  // Output only. FeatureMonitorJob ID comparing to which the drift is
  // calculated.
  int64 drift_base_feature_monitor_job_id = 7
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Data snapshot time comparing to which the drift is calculated.
  google.protobuf.Timestamp drift_base_snapshot_time = 8
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Feature selection config used when creating FeatureMonitorJob.
  FeatureSelectionConfig feature_selection_config = 9
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Trigger type of the Feature Monitor Job.
  FeatureMonitorJobTrigger trigger_type = 10
      [(google.api.field_behavior) = OUTPUT_ONLY];
}
