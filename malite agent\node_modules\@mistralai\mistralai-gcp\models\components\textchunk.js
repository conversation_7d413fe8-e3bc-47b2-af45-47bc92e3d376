"use strict";
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TextChunk$ = exports.TextChunk$outboundSchema = exports.TextChunk$inboundSchema = exports.Type$ = exports.Type$outboundSchema = exports.Type$inboundSchema = exports.Type = void 0;
exports.textChunkToJSON = textChunkToJSON;
exports.textChunkFromJSON = textChunkFromJSON;
const z = __importStar(require("zod"));
const schemas_js_1 = require("../../lib/schemas.js");
exports.Type = {
    Text: "text",
};
/** @internal */
exports.Type$inboundSchema = z.nativeEnum(exports.Type);
/** @internal */
exports.Type$outboundSchema = exports.Type$inboundSchema;
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var Type$;
(function (Type$) {
    /** @deprecated use `Type$inboundSchema` instead. */
    Type$.inboundSchema = exports.Type$inboundSchema;
    /** @deprecated use `Type$outboundSchema` instead. */
    Type$.outboundSchema = exports.Type$outboundSchema;
})(Type$ || (exports.Type$ = Type$ = {}));
/** @internal */
exports.TextChunk$inboundSchema = z.object({
    text: z.string(),
    type: exports.Type$inboundSchema.default("text"),
});
/** @internal */
exports.TextChunk$outboundSchema = z.object({
    text: z.string(),
    type: exports.Type$outboundSchema.default("text"),
});
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var TextChunk$;
(function (TextChunk$) {
    /** @deprecated use `TextChunk$inboundSchema` instead. */
    TextChunk$.inboundSchema = exports.TextChunk$inboundSchema;
    /** @deprecated use `TextChunk$outboundSchema` instead. */
    TextChunk$.outboundSchema = exports.TextChunk$outboundSchema;
})(TextChunk$ || (exports.TextChunk$ = TextChunk$ = {}));
function textChunkToJSON(textChunk) {
    return JSON.stringify(exports.TextChunk$outboundSchema.parse(textChunk));
}
function textChunkFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.TextChunk$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'TextChunk' from JSON`);
}
//# sourceMappingURL=textchunk.js.map