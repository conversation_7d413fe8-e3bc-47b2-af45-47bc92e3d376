// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.aiplatform.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/aiplatform/v1/index_endpoint.proto";
import "google/cloud/aiplatform/v1/operation.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";

option csharp_namespace = "Google.Cloud.AIPlatform.V1";
option go_package = "cloud.google.com/go/aiplatform/apiv1/aiplatformpb;aiplatformpb";
option java_multiple_files = true;
option java_outer_classname = "IndexEndpointServiceProto";
option java_package = "com.google.cloud.aiplatform.v1";
option php_namespace = "Google\\Cloud\\AIPlatform\\V1";
option ruby_package = "Google::Cloud::AIPlatform::V1";

// A service for managing Vertex AI's IndexEndpoints.
service IndexEndpointService {
  option (google.api.default_host) = "aiplatform.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Creates an IndexEndpoint.
  rpc CreateIndexEndpoint(CreateIndexEndpointRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/indexEndpoints"
      body: "index_endpoint"
    };
    option (google.api.method_signature) = "parent,index_endpoint";
    option (google.longrunning.operation_info) = {
      response_type: "IndexEndpoint"
      metadata_type: "CreateIndexEndpointOperationMetadata"
    };
  }

  // Gets an IndexEndpoint.
  rpc GetIndexEndpoint(GetIndexEndpointRequest) returns (IndexEndpoint) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/indexEndpoints/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists IndexEndpoints in a Location.
  rpc ListIndexEndpoints(ListIndexEndpointsRequest)
      returns (ListIndexEndpointsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/indexEndpoints"
    };
    option (google.api.method_signature) = "parent";
  }

  // Updates an IndexEndpoint.
  rpc UpdateIndexEndpoint(UpdateIndexEndpointRequest) returns (IndexEndpoint) {
    option (google.api.http) = {
      patch: "/v1/{index_endpoint.name=projects/*/locations/*/indexEndpoints/*}"
      body: "index_endpoint"
    };
    option (google.api.method_signature) = "index_endpoint,update_mask";
  }

  // Deletes an IndexEndpoint.
  rpc DeleteIndexEndpoint(DeleteIndexEndpointRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/indexEndpoints/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "DeleteOperationMetadata"
    };
  }

  // Deploys an Index into this IndexEndpoint, creating a DeployedIndex within
  // it.
  // Only non-empty Indexes can be deployed.
  rpc DeployIndex(DeployIndexRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{index_endpoint=projects/*/locations/*/indexEndpoints/*}:deployIndex"
      body: "*"
    };
    option (google.api.method_signature) = "index_endpoint,deployed_index";
    option (google.longrunning.operation_info) = {
      response_type: "DeployIndexResponse"
      metadata_type: "DeployIndexOperationMetadata"
    };
  }

  // Undeploys an Index from an IndexEndpoint, removing a DeployedIndex from it,
  // and freeing all resources it's using.
  rpc UndeployIndex(UndeployIndexRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{index_endpoint=projects/*/locations/*/indexEndpoints/*}:undeployIndex"
      body: "*"
    };
    option (google.api.method_signature) = "index_endpoint,deployed_index_id";
    option (google.longrunning.operation_info) = {
      response_type: "UndeployIndexResponse"
      metadata_type: "UndeployIndexOperationMetadata"
    };
  }

  // Update an existing DeployedIndex under an IndexEndpoint.
  rpc MutateDeployedIndex(MutateDeployedIndexRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{index_endpoint=projects/*/locations/*/indexEndpoints/*}:mutateDeployedIndex"
      body: "deployed_index"
    };
    option (google.api.method_signature) = "index_endpoint,deployed_index";
    option (google.longrunning.operation_info) = {
      response_type: "MutateDeployedIndexResponse"
      metadata_type: "MutateDeployedIndexOperationMetadata"
    };
  }
}

// Request message for
// [IndexEndpointService.CreateIndexEndpoint][google.cloud.aiplatform.v1.IndexEndpointService.CreateIndexEndpoint].
message CreateIndexEndpointRequest {
  // Required. The resource name of the Location to create the IndexEndpoint in.
  // Format: `projects/{project}/locations/{location}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Required. The IndexEndpoint to create.
  IndexEndpoint index_endpoint = 2 [(google.api.field_behavior) = REQUIRED];
}

// Runtime operation information for
// [IndexEndpointService.CreateIndexEndpoint][google.cloud.aiplatform.v1.IndexEndpointService.CreateIndexEndpoint].
message CreateIndexEndpointOperationMetadata {
  // The operation generic information.
  GenericOperationMetadata generic_metadata = 1;
}

// Request message for
// [IndexEndpointService.GetIndexEndpoint][google.cloud.aiplatform.v1.IndexEndpointService.GetIndexEndpoint]
message GetIndexEndpointRequest {
  // Required. The name of the IndexEndpoint resource.
  // Format:
  // `projects/{project}/locations/{location}/indexEndpoints/{index_endpoint}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/IndexEndpoint"
    }
  ];
}

// Request message for
// [IndexEndpointService.ListIndexEndpoints][google.cloud.aiplatform.v1.IndexEndpointService.ListIndexEndpoints].
message ListIndexEndpointsRequest {
  // Required. The resource name of the Location from which to list the
  // IndexEndpoints. Format: `projects/{project}/locations/{location}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Optional. An expression for filtering the results of the request. For field
  // names both snake_case and camelCase are supported.
  //
  //   * `index_endpoint` supports = and !=. `index_endpoint` represents the
  //      IndexEndpoint ID, ie. the last segment of the IndexEndpoint's
  //      [resourcename][google.cloud.aiplatform.v1.IndexEndpoint.name].
  //   * `display_name` supports =, != and regex()
  //             (uses [re2](https://github.com/google/re2/wiki/Syntax) syntax)
  //   * `labels` supports general map functions that is:
  //             `labels.key=value` - key:value equality
  //             `labels.key:* or labels:key - key existence
  //              A key including a space must be quoted. `labels."a key"`.
  //
  // Some examples:
  //   * `index_endpoint="1"`
  //   * `display_name="myDisplayName"`
  //   * `regex(display_name, "^A") -> The display name starts with an A.
  //   * `labels.myKey="myValue"`
  string filter = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The standard list page size.
  int32 page_size = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The standard list page token.
  // Typically obtained via
  // [ListIndexEndpointsResponse.next_page_token][google.cloud.aiplatform.v1.ListIndexEndpointsResponse.next_page_token]
  // of the previous
  // [IndexEndpointService.ListIndexEndpoints][google.cloud.aiplatform.v1.IndexEndpointService.ListIndexEndpoints]
  // call.
  string page_token = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Mask specifying which fields to read.
  google.protobuf.FieldMask read_mask = 5
      [(google.api.field_behavior) = OPTIONAL];
}

// Response message for
// [IndexEndpointService.ListIndexEndpoints][google.cloud.aiplatform.v1.IndexEndpointService.ListIndexEndpoints].
message ListIndexEndpointsResponse {
  // List of IndexEndpoints in the requested page.
  repeated IndexEndpoint index_endpoints = 1;

  // A token to retrieve next page of results.
  // Pass to
  // [ListIndexEndpointsRequest.page_token][google.cloud.aiplatform.v1.ListIndexEndpointsRequest.page_token]
  // to obtain that page.
  string next_page_token = 2;
}

// Request message for
// [IndexEndpointService.UpdateIndexEndpoint][google.cloud.aiplatform.v1.IndexEndpointService.UpdateIndexEndpoint].
message UpdateIndexEndpointRequest {
  // Required. The IndexEndpoint which replaces the resource on the server.
  IndexEndpoint index_endpoint = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The update mask applies to the resource. See
  // [google.protobuf.FieldMask][google.protobuf.FieldMask].
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Request message for
// [IndexEndpointService.DeleteIndexEndpoint][google.cloud.aiplatform.v1.IndexEndpointService.DeleteIndexEndpoint].
message DeleteIndexEndpointRequest {
  // Required. The name of the IndexEndpoint resource to be deleted.
  // Format:
  // `projects/{project}/locations/{location}/indexEndpoints/{index_endpoint}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/IndexEndpoint"
    }
  ];
}

// Request message for
// [IndexEndpointService.DeployIndex][google.cloud.aiplatform.v1.IndexEndpointService.DeployIndex].
message DeployIndexRequest {
  // Required. The name of the IndexEndpoint resource into which to deploy an
  // Index. Format:
  // `projects/{project}/locations/{location}/indexEndpoints/{index_endpoint}`
  string index_endpoint = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/IndexEndpoint"
    }
  ];

  // Required. The DeployedIndex to be created within the IndexEndpoint.
  DeployedIndex deployed_index = 2 [(google.api.field_behavior) = REQUIRED];
}

// Response message for
// [IndexEndpointService.DeployIndex][google.cloud.aiplatform.v1.IndexEndpointService.DeployIndex].
message DeployIndexResponse {
  // The DeployedIndex that had been deployed in the IndexEndpoint.
  DeployedIndex deployed_index = 1;
}

// Runtime operation information for
// [IndexEndpointService.DeployIndex][google.cloud.aiplatform.v1.IndexEndpointService.DeployIndex].
message DeployIndexOperationMetadata {
  // The operation generic information.
  GenericOperationMetadata generic_metadata = 1;

  // The unique index id specified by user
  string deployed_index_id = 2;
}

// Request message for
// [IndexEndpointService.UndeployIndex][google.cloud.aiplatform.v1.IndexEndpointService.UndeployIndex].
message UndeployIndexRequest {
  // Required. The name of the IndexEndpoint resource from which to undeploy an
  // Index. Format:
  // `projects/{project}/locations/{location}/indexEndpoints/{index_endpoint}`
  string index_endpoint = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/IndexEndpoint"
    }
  ];

  // Required. The ID of the DeployedIndex to be undeployed from the
  // IndexEndpoint.
  string deployed_index_id = 2 [(google.api.field_behavior) = REQUIRED];
}

// Response message for
// [IndexEndpointService.UndeployIndex][google.cloud.aiplatform.v1.IndexEndpointService.UndeployIndex].
message UndeployIndexResponse {}

// Runtime operation information for
// [IndexEndpointService.UndeployIndex][google.cloud.aiplatform.v1.IndexEndpointService.UndeployIndex].
message UndeployIndexOperationMetadata {
  // The operation generic information.
  GenericOperationMetadata generic_metadata = 1;
}

// Request message for
// [IndexEndpointService.MutateDeployedIndex][google.cloud.aiplatform.v1.IndexEndpointService.MutateDeployedIndex].
message MutateDeployedIndexRequest {
  // Required. The name of the IndexEndpoint resource into which to deploy an
  // Index. Format:
  // `projects/{project}/locations/{location}/indexEndpoints/{index_endpoint}`
  string index_endpoint = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/IndexEndpoint"
    }
  ];

  // Required. The DeployedIndex to be updated within the IndexEndpoint.
  // Currently, the updatable fields are
  // [DeployedIndex.automatic_resources][google.cloud.aiplatform.v1.DeployedIndex.automatic_resources]
  // and
  // [DeployedIndex.dedicated_resources][google.cloud.aiplatform.v1.DeployedIndex.dedicated_resources]
  DeployedIndex deployed_index = 2 [(google.api.field_behavior) = REQUIRED];
}

// Response message for
// [IndexEndpointService.MutateDeployedIndex][google.cloud.aiplatform.v1.IndexEndpointService.MutateDeployedIndex].
message MutateDeployedIndexResponse {
  // The DeployedIndex that had been updated in the IndexEndpoint.
  DeployedIndex deployed_index = 1;
}

// Runtime operation information for
// [IndexEndpointService.MutateDeployedIndex][google.cloud.aiplatform.v1.IndexEndpointService.MutateDeployedIndex].
message MutateDeployedIndexOperationMetadata {
  // The operation generic information.
  GenericOperationMetadata generic_metadata = 1;

  // The unique index id specified by user
  string deployed_index_id = 2;
}
