import * as v1beta1 from './v1beta1';
import * as v1 from './v1';
declare const DatasetServiceClient: typeof v1.DatasetServiceClient;
type DatasetServiceClient = v1.DatasetServiceClient;
declare const EndpointServiceClient: typeof v1.EndpointServiceClient;
type EndpointServiceClient = v1.EndpointServiceClient;
declare const JobServiceClient: typeof v1.JobServiceClient;
type JobServiceClient = v1.JobServiceClient;
declare const MigrationServiceClient: typeof v1.MigrationServiceClient;
type MigrationServiceClient = v1.MigrationServiceClient;
declare const ModelServiceClient: typeof v1.ModelServiceClient;
type ModelServiceClient = v1.ModelServiceClient;
declare const PipelineServiceClient: typeof v1.PipelineServiceClient;
type PipelineServiceClient = v1.PipelineServiceClient;
declare const PredictionServiceClient: typeof v1.PredictionServiceClient;
type PredictionServiceClient = v1.PredictionServiceClient;
declare const SpecialistPoolServiceClient: typeof v1.SpecialistPoolServiceClient;
type SpecialistPoolServiceClient = v1.SpecialistPoolServiceClient;
declare const IndexEndpointServiceClient: typeof v1.IndexEndpointServiceClient;
type IndexEndpointServiceClient = v1.IndexEndpointServiceClient;
declare const IndexServiceClient: typeof v1.IndexServiceClient;
type IndexServiceClient = v1.IndexServiceClient;
declare const VizierServiceClient: typeof v1.VizierServiceClient;
type VizierServiceClient = v1.VizierServiceClient;
declare const FeaturestoreServiceClient: typeof v1.FeaturestoreServiceClient;
type FeaturestoreServiceClient = v1.FeaturestoreServiceClient;
declare const FeaturestoreOnlineServingServiceClient: typeof v1.FeaturestoreOnlineServingServiceClient;
type FeaturestoreOnlineServingServiceClient = v1.FeaturestoreOnlineServingServiceClient;
declare const MetadataServiceClient: typeof v1.MetadataServiceClient;
type MetadataServiceClient = v1.MetadataServiceClient;
declare const TensorboardServiceClient: typeof v1.TensorboardServiceClient;
type TensorboardServiceClient = v1.TensorboardServiceClient;
declare const MatchServiceClient: typeof v1.MatchServiceClient;
type MatchServiceClient = v1.MatchServiceClient;
declare const ModelGardenServiceClient: typeof v1.ModelGardenServiceClient;
type ModelGardenServiceClient = v1.ModelGardenServiceClient;
declare const ScheduleServiceClient: typeof v1.ScheduleServiceClient;
type ScheduleServiceClient = v1.ScheduleServiceClient;
declare const FeatureOnlineStoreAdminServiceClient: typeof v1.FeatureOnlineStoreAdminServiceClient;
type FeatureOnlineStoreAdminServiceClient = v1.FeatureOnlineStoreAdminServiceClient;
declare const FeatureOnlineStoreServiceClient: typeof v1.FeatureOnlineStoreServiceClient;
type FeatureOnlineStoreServiceClient = v1.FeatureOnlineStoreServiceClient;
declare const FeatureRegistryServiceClient: typeof v1.FeatureRegistryServiceClient;
type FeatureRegistryServiceClient = v1.FeatureRegistryServiceClient;
declare const LlmUtilityServiceClient: typeof v1.LlmUtilityServiceClient;
type LlmUtilityServiceClient = v1.LlmUtilityServiceClient;
declare const DeploymentResourcePoolServiceClient: typeof v1.DeploymentResourcePoolServiceClient;
type DeploymentResourcePoolServiceClient = v1.DeploymentResourcePoolServiceClient;
declare const GenAiTuningServiceClient: typeof v1.GenAiTuningServiceClient;
type GenAiTuningServiceClient = v1.GenAiTuningServiceClient;
declare const NotebookServiceClient: typeof v1.NotebookServiceClient;
type NotebookServiceClient = v1.NotebookServiceClient;
declare const PersistentResourceServiceClient: typeof v1.PersistentResourceServiceClient;
type PersistentResourceServiceClient = v1.PersistentResourceServiceClient;
declare const EvaluationServiceClient: typeof v1.EvaluationServiceClient;
type EvaluationServiceClient = v1.EvaluationServiceClient;
declare const GenAiCacheServiceClient: typeof v1.GenAiCacheServiceClient;
type GenAiCacheServiceClient = v1.GenAiCacheServiceClient;
declare const ReasoningEngineExecutionServiceClient: typeof v1.ReasoningEngineExecutionServiceClient;
type ReasoningEngineExecutionServiceClient = v1.ReasoningEngineExecutionServiceClient;
declare const ReasoningEngineServiceClient: typeof v1.ReasoningEngineServiceClient;
type ReasoningEngineServiceClient = v1.ReasoningEngineServiceClient;
declare const VertexRagServiceClient: typeof v1.VertexRagServiceClient;
type VertexRagServiceClient = v1.VertexRagServiceClient;
declare const VertexRagDataServiceClient: typeof v1.VertexRagDataServiceClient;
type VertexRagDataServiceClient = v1.VertexRagDataServiceClient;
export { v1beta1, v1, DatasetServiceClient, EndpointServiceClient, JobServiceClient, MigrationServiceClient, ModelServiceClient, PipelineServiceClient, PredictionServiceClient, SpecialistPoolServiceClient, IndexEndpointServiceClient, IndexServiceClient, VizierServiceClient, FeaturestoreServiceClient, FeaturestoreOnlineServingServiceClient, MetadataServiceClient, TensorboardServiceClient, MatchServiceClient, ModelGardenServiceClient, ScheduleServiceClient, FeatureOnlineStoreAdminServiceClient, FeatureOnlineStoreServiceClient, FeatureRegistryServiceClient, LlmUtilityServiceClient, DeploymentResourcePoolServiceClient, GenAiTuningServiceClient, NotebookServiceClient, PersistentResourceServiceClient, EvaluationServiceClient, VertexRagServiceClient, GenAiCacheServiceClient, ReasoningEngineExecutionServiceClient, ReasoningEngineServiceClient, VertexRagDataServiceClient, };
declare const _default: {
    v1beta1: typeof v1beta1;
    v1: typeof v1;
    DatasetServiceClient: typeof v1.DatasetServiceClient;
    EndpointServiceClient: typeof v1.EndpointServiceClient;
    JobServiceClient: typeof v1.JobServiceClient;
    MigrationServiceClient: typeof v1.MigrationServiceClient;
    ModelServiceClient: typeof v1.ModelServiceClient;
    PipelineServiceClient: typeof v1.PipelineServiceClient;
    PredictionServiceClient: typeof v1.PredictionServiceClient;
    SpecialistPoolServiceClient: typeof v1.SpecialistPoolServiceClient;
    IndexEndpointServiceClient: typeof v1.IndexEndpointServiceClient;
    IndexServiceClient: typeof v1.IndexServiceClient;
    VizierServiceClient: typeof v1.VizierServiceClient;
    FeaturestoreServiceClient: typeof v1.FeaturestoreServiceClient;
    FeaturestoreOnlineServingServiceClient: typeof v1.FeaturestoreOnlineServingServiceClient;
    MetadataServiceClient: typeof v1.MetadataServiceClient;
    TensorboardServiceClient: typeof v1.TensorboardServiceClient;
    MatchServiceClient: typeof v1.MatchServiceClient;
    ModelGardenServiceClient: typeof v1.ModelGardenServiceClient;
    ScheduleServiceClient: typeof v1.ScheduleServiceClient;
    FeatureOnlineStoreAdminServiceClient: typeof v1.FeatureOnlineStoreAdminServiceClient;
    FeatureOnlineStoreServiceClient: typeof v1.FeatureOnlineStoreServiceClient;
    FeatureRegistryServiceClient: typeof v1.FeatureRegistryServiceClient;
    LlmUtilityServiceClient: typeof v1.LlmUtilityServiceClient;
    DeploymentResourcePoolServiceClient: typeof v1.DeploymentResourcePoolServiceClient;
    GenAiTuningServiceClient: typeof v1.GenAiTuningServiceClient;
    NotebookServiceClient: typeof v1.NotebookServiceClient;
    PersistentResourceServiceClient: typeof v1.PersistentResourceServiceClient;
    EvaluationServiceClient: typeof v1.EvaluationServiceClient;
    GenAiCacheServiceClient: typeof v1.GenAiCacheServiceClient;
    ReasoningEngineExecutionServiceClient: typeof v1.ReasoningEngineExecutionServiceClient;
    VertexRagServiceClient: typeof v1.VertexRagServiceClient;
    ReasoningEngineServiceClient: typeof v1.ReasoningEngineServiceClient;
    VertexRagDataServiceClient: typeof v1.VertexRagDataServiceClient;
};
export default _default;
import * as protos from '../protos/protos';
export { protos };
import { fromValue, toValue } from './helpers';
declare const helpers: {
    toValue: typeof toValue;
    fromValue: typeof fromValue;
};
export { helpers };
