// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.aiplatform.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/aiplatform/v1/encryption_spec.proto";
import "google/cloud/aiplatform/v1/job_state.proto";
import "google/cloud/aiplatform/v1/machine_resources.proto";
import "google/cloud/aiplatform/v1/network_spec.proto";
import "google/protobuf/duration.proto";
import "google/protobuf/timestamp.proto";
import "google/rpc/status.proto";

option csharp_namespace = "Google.Cloud.AIPlatform.V1";
option go_package = "cloud.google.com/go/aiplatform/apiv1/aiplatformpb;aiplatformpb";
option java_multiple_files = true;
option java_outer_classname = "NotebookExecutionJobProto";
option java_package = "com.google.cloud.aiplatform.v1";
option php_namespace = "Google\\Cloud\\AIPlatform\\V1";
option ruby_package = "Google::Cloud::AIPlatform::V1";

// NotebookExecutionJob represents an instance of a notebook execution.
message NotebookExecutionJob {
  option (google.api.resource) = {
    type: "aiplatform.googleapis.com/NotebookExecutionJob"
    pattern: "projects/{project}/locations/{location}/notebookExecutionJobs/{notebook_execution_job}"
    plural: "notebookExecutionJobs"
    singular: "notebookExecutionJob"
  };

  // The Dataform Repository containing the input notebook.
  message DataformRepositorySource {
    // The resource name of the Dataform Repository. Format:
    // `projects/{project_id}/locations/{location}/repositories/{repository_id}`
    string dataform_repository_resource_name = 1;

    // The commit SHA to read repository with. If unset, the file will be read
    // at HEAD.
    string commit_sha = 2;
  }

  // The Cloud Storage uri for the input notebook.
  message GcsNotebookSource {
    // The Cloud Storage uri pointing to the ipynb file. Format:
    // `gs://bucket/notebook_file.ipynb`
    string uri = 1;

    // The version of the Cloud Storage object to read. If unset, the current
    // version of the object is read. See
    // https://cloud.google.com/storage/docs/metadata#generation-number.
    string generation = 2;
  }

  // The content of the input notebook in ipynb format.
  message DirectNotebookSource {
    // The base64-encoded contents of the input notebook file.
    bytes content = 1;
  }

  // Compute configuration to use for an execution job.
  message CustomEnvironmentSpec {
    // The specification of a single machine for the execution job.
    MachineSpec machine_spec = 1;

    // The specification of a persistent disk to attach for the execution job.
    PersistentDiskSpec persistent_disk_spec = 2;

    // The network configuration to use for the execution job.
    NetworkSpec network_spec = 3;
  }

  // Configuration for a Workbench Instances-based environment.
  message WorkbenchRuntime {}

  // The input notebook.
  oneof notebook_source {
    // The Dataform Repository pointing to a single file notebook repository.
    DataformRepositorySource dataform_repository_source = 3;

    // The Cloud Storage url pointing to the ipynb file. Format:
    // `gs://bucket/notebook_file.ipynb`
    GcsNotebookSource gcs_notebook_source = 4;

    // The contents of an input notebook file.
    DirectNotebookSource direct_notebook_source = 17;
  }

  // The compute config to use for an execution job.
  oneof environment_spec {
    // The NotebookRuntimeTemplate to source compute configuration from.
    string notebook_runtime_template_resource_name = 14
        [(google.api.resource_reference) = {
          type: "aiplatform.googleapis.com/NotebookRuntimeTemplate"
        }];

    // The custom compute configuration for an execution job.
    CustomEnvironmentSpec custom_environment_spec = 16;
  }

  // The location to store the notebook execution result.
  oneof execution_sink {
    // The Cloud Storage location to upload the result to. Format:
    // `gs://bucket-name`
    string gcs_output_uri = 8;
  }

  // The identity to run the execution as.
  oneof execution_identity {
    // The user email to run the execution as. Only supported by Colab runtimes.
    string execution_user = 9;

    // The service account to run the execution as.
    string service_account = 18;
  }

  // Runtime environment for the notebook execution job. If unspecified, the
  // default runtime of Colab is used.
  oneof runtime_environment {
    // The Workbench runtime configuration to use for the notebook execution.
    WorkbenchRuntime workbench_runtime = 23;
  }

  // Output only. The resource name of this NotebookExecutionJob. Format:
  // `projects/{project_id}/locations/{location}/notebookExecutionJobs/{job_id}`
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // The display name of the NotebookExecutionJob. The name can be up to 128
  // characters long and can consist of any UTF-8 characters.
  string display_name = 2;

  // Max running time of the execution job in seconds (default 86400s / 24 hrs).
  google.protobuf.Duration execution_timeout = 5;

  // Output only. The Schedule resource name if this job is triggered by one.
  // Format:
  // `projects/{project_id}/locations/{location}/schedules/{schedule_id}`
  string schedule_resource_name = 6 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/Schedule"
    }
  ];

  // Output only. The state of the NotebookExecutionJob.
  JobState job_state = 10 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Populated when the NotebookExecutionJob is completed. When
  // there is an error during notebook execution, the error details are
  // populated.
  google.rpc.Status status = 11 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Timestamp when this NotebookExecutionJob was created.
  google.protobuf.Timestamp create_time = 12
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Timestamp when this NotebookExecutionJob was most recently
  // updated.
  google.protobuf.Timestamp update_time = 13
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // The labels with user-defined metadata to organize NotebookExecutionJobs.
  //
  // Label keys and values can be no longer than 64 characters
  // (Unicode codepoints), can only contain lowercase letters, numeric
  // characters, underscores and dashes. International characters are allowed.
  //
  // See https://goo.gl/xmQnxf for more information and examples of labels.
  // System reserved label keys are prefixed with "aiplatform.googleapis.com/"
  // and are immutable.
  map<string, string> labels = 19;

  // The name of the kernel to use during notebook execution. If unset, the
  // default kernel is used.
  string kernel_name = 20;

  // Customer-managed encryption key spec for the notebook execution job.
  // This field is auto-populated if the
  // [NotebookRuntimeTemplate][google.cloud.aiplatform.v1.NotebookRuntimeTemplate]
  // has an encryption spec.
  EncryptionSpec encryption_spec = 22;
}
