# Docker Compose لتطبيق Malite Agent
# يسهل تشغيل التطبيق محلياً مع جميع الخدمات المطلوبة

version: '3.8'

services:
  # الخدمة الرئيسية - تطبيق Malite Agent
  malite-agent:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: malite-agent-app
    ports:
      - "8080:8080"
      - "3400:3400"  # منفذ Genkit Developer UI
    environment:
      - NODE_ENV=development
      - GENKIT_ENV=development
      - PORT=8080
      - HOST=0.0.0.0
      - VERTEX_AI_LOCATION=us-central1
      - LOG_LEVEL=INFO
      - ENABLE_PERFORMANCE_MONITORING=true
    env_file:
      - .env
    volumes:
      # ربط الكود للتطوير (hot reload)
      - .:/app
      - /app/node_modules
      # ربط السجلات
      - ./logs:/app/logs
    networks:
      - malite-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "./docker-healthcheck.sh"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    depends_on:
      - redis
      - monitoring

  # خدمة Redis للتخزين المؤقت
  redis:
    image: redis:7-alpine
    container_name: malite-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - malite-network
    restart: unless-stopped
    command: redis-server --appendonly yes
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3

  # خدمة مراقبة الأداء (Prometheus)
  monitoring:
    image: prom/prometheus:latest
    container_name: malite-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    networks:
      - malite-network
    restart: unless-stopped
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--web.enable-lifecycle'

  # خدمة Grafana لعرض المقاييس
  grafana:
    image: grafana/grafana:latest
    container_name: malite-grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin123
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    networks:
      - malite-network
    restart: unless-stopped
    depends_on:
      - monitoring

  # خدمة Nginx كـ Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: malite-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    networks:
      - malite-network
    restart: unless-stopped
    depends_on:
      - malite-agent

# الشبكات
networks:
  malite-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# الأحجام المستمرة
volumes:
  redis_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

# إعدادات إضافية
x-logging: &default-logging
  driver: "json-file"
  options:
    max-size: "10m"
    max-file: "3"
