// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.aiplatform.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/aiplatform/v1/machine_resources.proto";
import "google/cloud/aiplatform/v1/model.proto";

option csharp_namespace = "Google.Cloud.AIPlatform.V1";
option go_package = "cloud.google.com/go/aiplatform/apiv1/aiplatformpb;aiplatformpb";
option java_multiple_files = true;
option java_outer_classname = "PublisherModelProto";
option java_package = "com.google.cloud.aiplatform.v1";
option php_namespace = "Google\\Cloud\\AIPlatform\\V1";
option ruby_package = "Google::Cloud::AIPlatform::V1";

// A Model Garden Publisher Model.
message PublisherModel {
  option (google.api.resource) = {
    type: "aiplatform.googleapis.com/PublisherModel"
    pattern: "publishers/{publisher}/models/{model}"
  };

  // Reference to a resource.
  message ResourceReference {
    oneof reference {
      // The URI of the resource.
      string uri = 1;

      // The resource name of the Google Cloud resource.
      string resource_name = 2;

      // Use case (CUJ) of the resource.
      string use_case = 3 [deprecated = true];

      // Description of the resource.
      string description = 4 [deprecated = true];
    }
  }

  // A named piece of documentation.
  message Documentation {
    // Required. E.g., OVERVIEW, USE CASES, DOCUMENTATION, SDK & SAMPLES, JAVA,
    // NODE.JS, etc..
    string title = 1 [(google.api.field_behavior) = REQUIRED];

    // Required. Content of this piece of document (in Markdown format).
    string content = 2 [(google.api.field_behavior) = REQUIRED];
  }

  // Actions could take on this Publisher Model.
  message CallToAction {
    // The regional resource name or the URI. Key is region, e.g.,
    // us-central1, europe-west2, global, etc..
    message RegionalResourceReferences {
      // Required.
      map<string, ResourceReference> references = 1
          [(google.api.field_behavior) = REQUIRED];

      // Required.
      string title = 2 [(google.api.field_behavior) = REQUIRED];

      // Optional. Title of the resource.
      optional string resource_title = 3
          [(google.api.field_behavior) = OPTIONAL];

      // Optional. Use case (CUJ) of the resource.
      optional string resource_use_case = 4
          [(google.api.field_behavior) = OPTIONAL];

      // Optional. Description of the resource.
      optional string resource_description = 5
          [(google.api.field_behavior) = OPTIONAL];
    }

    // Rest API docs.
    message ViewRestApi {
      // Required.
      repeated Documentation documentations = 1
          [(google.api.field_behavior) = REQUIRED];

      // Required. The title of the view rest API.
      string title = 2 [(google.api.field_behavior) = REQUIRED];
    }

    // Open notebooks.
    message OpenNotebooks {
      // Required. Regional resource references to notebooks.
      repeated RegionalResourceReferences notebooks = 1
          [(google.api.field_behavior) = REQUIRED];
    }

    // Open fine tuning pipelines.
    message OpenFineTuningPipelines {
      // Required. Regional resource references to fine tuning pipelines.
      repeated RegionalResourceReferences fine_tuning_pipelines = 1
          [(google.api.field_behavior) = REQUIRED];
    }

    // Model metadata that is needed for UploadModel or
    // DeployModel/CreateEndpoint requests.
    message Deploy {
      // Metadata information about the deployment for managing deployment
      // config.
      message DeployMetadata {
        // Optional. Labels for the deployment. For managing deployment config
        // like verifying, source of deployment config, etc.
        map<string, string> labels = 1 [(google.api.field_behavior) = OPTIONAL];

        // Optional. Sample request for deployed endpoint.
        string sample_request = 2 [(google.api.field_behavior) = OPTIONAL];
      }

      // The prediction (for example, the machine) resources that the
      // DeployedModel uses.
      oneof prediction_resources {
        // A description of resources that are dedicated to the DeployedModel,
        // and that need a higher degree of manual configuration.
        DedicatedResources dedicated_resources = 5;

        // A description of resources that to large degree are decided by Vertex
        // AI, and require only a modest additional configuration.
        AutomaticResources automatic_resources = 6;

        // The resource name of the shared DeploymentResourcePool to deploy on.
        // Format:
        // `projects/{project}/locations/{location}/deploymentResourcePools/{deployment_resource_pool}`
        string shared_resources = 7;
      }

      // Optional. Default model display name.
      string model_display_name = 1 [(google.api.field_behavior) = OPTIONAL];

      // Optional. Large model reference. When this is set, model_artifact_spec
      // is not needed.
      LargeModelReference large_model_reference = 2
          [(google.api.field_behavior) = OPTIONAL];

      // Optional. The specification of the container that is to be used when
      // deploying this Model in Vertex AI. Not present for Large Models.
      ModelContainerSpec container_spec = 3
          [(google.api.field_behavior) = OPTIONAL];

      // Optional. The path to the directory containing the Model artifact and
      // any of its supporting files.
      string artifact_uri = 4 [(google.api.field_behavior) = OPTIONAL];

      // Optional. The name of the deploy task (e.g., "text to image
      // generation").
      optional string deploy_task_name = 10
          [(google.api.field_behavior) = OPTIONAL];

      // Optional. Metadata information about this deployment config.
      optional DeployMetadata deploy_metadata = 11
          [(google.api.field_behavior) = OPTIONAL];

      // Required. The title of the regional resource reference.
      string title = 8 [(google.api.field_behavior) = REQUIRED];

      // Optional. The signed URI for ephemeral Cloud Storage access to model
      // artifact.
      string public_artifact_uri = 9 [(google.api.field_behavior) = OPTIONAL];
    }

    // Configurations for PublisherModel GKE deployment
    message DeployGke {
      // Optional. GKE deployment configuration in yaml format.
      repeated string gke_yaml_configs = 1
          [(google.api.field_behavior) = OPTIONAL];
    }

    // Optional. To view Rest API docs.
    ViewRestApi view_rest_api = 1 [(google.api.field_behavior) = OPTIONAL];

    // Optional. Open notebook of the PublisherModel.
    RegionalResourceReferences open_notebook = 2
        [(google.api.field_behavior) = OPTIONAL];

    // Optional. Open notebooks of the PublisherModel.
    optional OpenNotebooks open_notebooks = 12
        [(google.api.field_behavior) = OPTIONAL];

    // Optional. Create application using the PublisherModel.
    RegionalResourceReferences create_application = 3
        [(google.api.field_behavior) = OPTIONAL];

    // Optional. Open fine-tuning pipeline of the PublisherModel.
    RegionalResourceReferences open_fine_tuning_pipeline = 4
        [(google.api.field_behavior) = OPTIONAL];

    // Optional. Open fine-tuning pipelines of the PublisherModel.
    optional OpenFineTuningPipelines open_fine_tuning_pipelines = 13
        [(google.api.field_behavior) = OPTIONAL];

    // Optional. Open prompt-tuning pipeline of the PublisherModel.
    RegionalResourceReferences open_prompt_tuning_pipeline = 5
        [(google.api.field_behavior) = OPTIONAL];

    // Optional. Open Genie / Playground.
    RegionalResourceReferences open_genie = 6
        [(google.api.field_behavior) = OPTIONAL];

    // Optional. Deploy the PublisherModel to Vertex Endpoint.
    Deploy deploy = 7 [(google.api.field_behavior) = OPTIONAL];

    // Optional. Deploy PublisherModel to Google Kubernetes Engine.
    DeployGke deploy_gke = 14 [(google.api.field_behavior) = OPTIONAL];

    // Optional. Open in Generation AI Studio.
    RegionalResourceReferences open_generation_ai_studio = 8
        [(google.api.field_behavior) = OPTIONAL];

    // Optional. Request for access.
    RegionalResourceReferences request_access = 9
        [(google.api.field_behavior) = OPTIONAL];

    // Optional. Open evaluation pipeline of the PublisherModel.
    RegionalResourceReferences open_evaluation_pipeline = 11
        [(google.api.field_behavior) = OPTIONAL];
  }

  // An enum representing the open source category of a PublisherModel.
  enum OpenSourceCategory {
    // The open source category is unspecified, which should not be used.
    OPEN_SOURCE_CATEGORY_UNSPECIFIED = 0;

    // Used to indicate the PublisherModel is not open sourced.
    PROPRIETARY = 1;

    // Used to indicate the PublisherModel is a Google-owned open source model
    // w/ Google checkpoint.
    GOOGLE_OWNED_OSS_WITH_GOOGLE_CHECKPOINT = 2;

    // Used to indicate the PublisherModel is a 3p-owned open source model w/
    // Google checkpoint.
    THIRD_PARTY_OWNED_OSS_WITH_GOOGLE_CHECKPOINT = 3;

    // Used to indicate the PublisherModel is a Google-owned pure open source
    // model.
    GOOGLE_OWNED_OSS = 4;

    // Used to indicate the PublisherModel is a 3p-owned pure open source model.
    THIRD_PARTY_OWNED_OSS = 5;
  }

  // An enum representing the launch stage of a PublisherModel.
  enum LaunchStage {
    // The model launch stage is unspecified.
    LAUNCH_STAGE_UNSPECIFIED = 0;

    // Used to indicate the PublisherModel is at Experimental launch stage,
    // available to a small set of customers.
    EXPERIMENTAL = 1;

    // Used to indicate the PublisherModel is at Private Preview launch stage,
    // only available to a small set of customers, although a larger set of
    // customers than an Experimental launch. Previews are the first launch
    // stage used to get feedback from customers.
    PRIVATE_PREVIEW = 2;

    // Used to indicate the PublisherModel is at Public Preview launch stage,
    // available to all customers, although not supported for production
    // workloads.
    PUBLIC_PREVIEW = 3;

    // Used to indicate the PublisherModel is at GA launch stage, available to
    // all customers and ready for production workload.
    GA = 4;
  }

  // An enum representing the state of the PublicModelVersion.
  enum VersionState {
    // The version state is unspecified.
    VERSION_STATE_UNSPECIFIED = 0;

    // Used to indicate the version is stable.
    VERSION_STATE_STABLE = 1;

    // Used to indicate the version is unstable.
    VERSION_STATE_UNSTABLE = 2;
  }

  // Output only. The resource name of the PublisherModel.
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Immutable. The version ID of the PublisherModel.
  // A new version is committed when a new model version is uploaded under an
  // existing model id. It is an auto-incrementing decimal number in string
  // representation.
  string version_id = 2 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.field_behavior) = OUTPUT_ONLY
  ];

  // Required. Indicates the open source category of the publisher model.
  OpenSourceCategory open_source_category = 7
      [(google.api.field_behavior) = REQUIRED];

  // Optional. Supported call-to-action options.
  CallToAction supported_actions = 19 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Additional information about the model's Frameworks.
  repeated string frameworks = 23 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Indicates the launch stage of the model.
  LaunchStage launch_stage = 29 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Indicates the state of the model version.
  VersionState version_state = 37 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Output only. Immutable. Used to indicate this model has a
  // publisher model and provide the template of the publisher model resource
  // name.
  string publisher_model_template = 30 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.field_behavior) = OUTPUT_ONLY
  ];

  // Optional. The schemata that describes formats of the PublisherModel's
  // predictions and explanations as given and returned via
  // [PredictionService.Predict][google.cloud.aiplatform.v1.PredictionService.Predict].
  PredictSchemata predict_schemata = 31
      [(google.api.field_behavior) = OPTIONAL];
}
