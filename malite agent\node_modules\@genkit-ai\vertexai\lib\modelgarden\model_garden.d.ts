import { z, ModelReference, Genkit } from 'genkit';
import { ModelAction } from 'genkit/model';
import { GoogleAuth } from 'google-auth-library';

/**
 * Copyright 2024 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

declare const ModelGardenModelConfigSchema: z.ZodObject<z.objectUtil.extendShape<z.objectUtil.extendShape<{
    version: z.ZodOptional<z.ZodString>;
    temperature: z.ZodOptional<z.ZodNumber>;
    maxOutputTokens: z.ZodOptional<z.ZodNumber>;
    topK: z.ZodOptional<z.ZodNumber>;
    topP: z.ZodOptional<z.ZodNumber>;
    stopSequences: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
}, {
    frequencyPenalty: z.ZodOptional<z.ZodNumber>;
    logitBias: z.ZodOptional<z.ZodRecord<z.ZodString, z.ZodNumber>>;
    logProbs: z.ZodOptional<z.ZodBoolean>;
    presencePenalty: z.ZodOptional<z.ZodNumber>;
    seed: z.ZodOptional<z.ZodNumber>;
    topLogProbs: z.ZodOptional<z.ZodNumber>;
    user: z.ZodOptional<z.ZodString>;
}>, {
    location: z.ZodOptional<z.ZodString>;
}>, "passthrough", z.ZodTypeAny, z.objectOutputType<z.objectUtil.extendShape<z.objectUtil.extendShape<{
    version: z.ZodOptional<z.ZodString>;
    temperature: z.ZodOptional<z.ZodNumber>;
    maxOutputTokens: z.ZodOptional<z.ZodNumber>;
    topK: z.ZodOptional<z.ZodNumber>;
    topP: z.ZodOptional<z.ZodNumber>;
    stopSequences: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
}, {
    frequencyPenalty: z.ZodOptional<z.ZodNumber>;
    logitBias: z.ZodOptional<z.ZodRecord<z.ZodString, z.ZodNumber>>;
    logProbs: z.ZodOptional<z.ZodBoolean>;
    presencePenalty: z.ZodOptional<z.ZodNumber>;
    seed: z.ZodOptional<z.ZodNumber>;
    topLogProbs: z.ZodOptional<z.ZodNumber>;
    user: z.ZodOptional<z.ZodString>;
}>, {
    location: z.ZodOptional<z.ZodString>;
}>, z.ZodTypeAny, "passthrough">, z.objectInputType<z.objectUtil.extendShape<z.objectUtil.extendShape<{
    version: z.ZodOptional<z.ZodString>;
    temperature: z.ZodOptional<z.ZodNumber>;
    maxOutputTokens: z.ZodOptional<z.ZodNumber>;
    topK: z.ZodOptional<z.ZodNumber>;
    topP: z.ZodOptional<z.ZodNumber>;
    stopSequences: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
}, {
    frequencyPenalty: z.ZodOptional<z.ZodNumber>;
    logitBias: z.ZodOptional<z.ZodRecord<z.ZodString, z.ZodNumber>>;
    logProbs: z.ZodOptional<z.ZodBoolean>;
    presencePenalty: z.ZodOptional<z.ZodNumber>;
    seed: z.ZodOptional<z.ZodNumber>;
    topLogProbs: z.ZodOptional<z.ZodNumber>;
    user: z.ZodOptional<z.ZodString>;
}>, {
    location: z.ZodOptional<z.ZodString>;
}>, z.ZodTypeAny, "passthrough">>;
declare const llama31: ModelReference<z.ZodObject<z.objectUtil.extendShape<z.objectUtil.extendShape<{
    version: z.ZodOptional<z.ZodString>;
    temperature: z.ZodOptional<z.ZodNumber>;
    maxOutputTokens: z.ZodOptional<z.ZodNumber>;
    topK: z.ZodOptional<z.ZodNumber>;
    topP: z.ZodOptional<z.ZodNumber>;
    stopSequences: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
}, {
    frequencyPenalty: z.ZodOptional<z.ZodNumber>;
    logitBias: z.ZodOptional<z.ZodRecord<z.ZodString, z.ZodNumber>>;
    logProbs: z.ZodOptional<z.ZodBoolean>;
    presencePenalty: z.ZodOptional<z.ZodNumber>;
    seed: z.ZodOptional<z.ZodNumber>;
    topLogProbs: z.ZodOptional<z.ZodNumber>;
    user: z.ZodOptional<z.ZodString>;
}>, {
    location: z.ZodOptional<z.ZodString>;
}>, "passthrough", z.ZodTypeAny, z.objectOutputType<z.objectUtil.extendShape<z.objectUtil.extendShape<{
    version: z.ZodOptional<z.ZodString>;
    temperature: z.ZodOptional<z.ZodNumber>;
    maxOutputTokens: z.ZodOptional<z.ZodNumber>;
    topK: z.ZodOptional<z.ZodNumber>;
    topP: z.ZodOptional<z.ZodNumber>;
    stopSequences: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
}, {
    frequencyPenalty: z.ZodOptional<z.ZodNumber>;
    logitBias: z.ZodOptional<z.ZodRecord<z.ZodString, z.ZodNumber>>;
    logProbs: z.ZodOptional<z.ZodBoolean>;
    presencePenalty: z.ZodOptional<z.ZodNumber>;
    seed: z.ZodOptional<z.ZodNumber>;
    topLogProbs: z.ZodOptional<z.ZodNumber>;
    user: z.ZodOptional<z.ZodString>;
}>, {
    location: z.ZodOptional<z.ZodString>;
}>, z.ZodTypeAny, "passthrough">, z.objectInputType<z.objectUtil.extendShape<z.objectUtil.extendShape<{
    version: z.ZodOptional<z.ZodString>;
    temperature: z.ZodOptional<z.ZodNumber>;
    maxOutputTokens: z.ZodOptional<z.ZodNumber>;
    topK: z.ZodOptional<z.ZodNumber>;
    topP: z.ZodOptional<z.ZodNumber>;
    stopSequences: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
}, {
    frequencyPenalty: z.ZodOptional<z.ZodNumber>;
    logitBias: z.ZodOptional<z.ZodRecord<z.ZodString, z.ZodNumber>>;
    logProbs: z.ZodOptional<z.ZodBoolean>;
    presencePenalty: z.ZodOptional<z.ZodNumber>;
    seed: z.ZodOptional<z.ZodNumber>;
    topLogProbs: z.ZodOptional<z.ZodNumber>;
    user: z.ZodOptional<z.ZodString>;
}>, {
    location: z.ZodOptional<z.ZodString>;
}>, z.ZodTypeAny, "passthrough">>>;
declare const llama32: ModelReference<z.ZodObject<z.objectUtil.extendShape<z.objectUtil.extendShape<{
    version: z.ZodOptional<z.ZodString>;
    temperature: z.ZodOptional<z.ZodNumber>;
    maxOutputTokens: z.ZodOptional<z.ZodNumber>;
    topK: z.ZodOptional<z.ZodNumber>;
    topP: z.ZodOptional<z.ZodNumber>;
    stopSequences: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
}, {
    frequencyPenalty: z.ZodOptional<z.ZodNumber>;
    logitBias: z.ZodOptional<z.ZodRecord<z.ZodString, z.ZodNumber>>;
    logProbs: z.ZodOptional<z.ZodBoolean>;
    presencePenalty: z.ZodOptional<z.ZodNumber>;
    seed: z.ZodOptional<z.ZodNumber>;
    topLogProbs: z.ZodOptional<z.ZodNumber>;
    user: z.ZodOptional<z.ZodString>;
}>, {
    location: z.ZodOptional<z.ZodString>;
}>, "passthrough", z.ZodTypeAny, z.objectOutputType<z.objectUtil.extendShape<z.objectUtil.extendShape<{
    version: z.ZodOptional<z.ZodString>;
    temperature: z.ZodOptional<z.ZodNumber>;
    maxOutputTokens: z.ZodOptional<z.ZodNumber>;
    topK: z.ZodOptional<z.ZodNumber>;
    topP: z.ZodOptional<z.ZodNumber>;
    stopSequences: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
}, {
    frequencyPenalty: z.ZodOptional<z.ZodNumber>;
    logitBias: z.ZodOptional<z.ZodRecord<z.ZodString, z.ZodNumber>>;
    logProbs: z.ZodOptional<z.ZodBoolean>;
    presencePenalty: z.ZodOptional<z.ZodNumber>;
    seed: z.ZodOptional<z.ZodNumber>;
    topLogProbs: z.ZodOptional<z.ZodNumber>;
    user: z.ZodOptional<z.ZodString>;
}>, {
    location: z.ZodOptional<z.ZodString>;
}>, z.ZodTypeAny, "passthrough">, z.objectInputType<z.objectUtil.extendShape<z.objectUtil.extendShape<{
    version: z.ZodOptional<z.ZodString>;
    temperature: z.ZodOptional<z.ZodNumber>;
    maxOutputTokens: z.ZodOptional<z.ZodNumber>;
    topK: z.ZodOptional<z.ZodNumber>;
    topP: z.ZodOptional<z.ZodNumber>;
    stopSequences: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
}, {
    frequencyPenalty: z.ZodOptional<z.ZodNumber>;
    logitBias: z.ZodOptional<z.ZodRecord<z.ZodString, z.ZodNumber>>;
    logProbs: z.ZodOptional<z.ZodBoolean>;
    presencePenalty: z.ZodOptional<z.ZodNumber>;
    seed: z.ZodOptional<z.ZodNumber>;
    topLogProbs: z.ZodOptional<z.ZodNumber>;
    user: z.ZodOptional<z.ZodString>;
}>, {
    location: z.ZodOptional<z.ZodString>;
}>, z.ZodTypeAny, "passthrough">>>;
/**
 * @deprecated use `llama31` instead
 */
declare const llama3: ModelReference<z.ZodObject<z.objectUtil.extendShape<z.objectUtil.extendShape<{
    version: z.ZodOptional<z.ZodString>;
    temperature: z.ZodOptional<z.ZodNumber>;
    maxOutputTokens: z.ZodOptional<z.ZodNumber>;
    topK: z.ZodOptional<z.ZodNumber>;
    topP: z.ZodOptional<z.ZodNumber>;
    stopSequences: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
}, {
    frequencyPenalty: z.ZodOptional<z.ZodNumber>;
    logitBias: z.ZodOptional<z.ZodRecord<z.ZodString, z.ZodNumber>>;
    logProbs: z.ZodOptional<z.ZodBoolean>;
    presencePenalty: z.ZodOptional<z.ZodNumber>;
    seed: z.ZodOptional<z.ZodNumber>;
    topLogProbs: z.ZodOptional<z.ZodNumber>;
    user: z.ZodOptional<z.ZodString>;
}>, {
    location: z.ZodOptional<z.ZodString>;
}>, "passthrough", z.ZodTypeAny, z.objectOutputType<z.objectUtil.extendShape<z.objectUtil.extendShape<{
    version: z.ZodOptional<z.ZodString>;
    temperature: z.ZodOptional<z.ZodNumber>;
    maxOutputTokens: z.ZodOptional<z.ZodNumber>;
    topK: z.ZodOptional<z.ZodNumber>;
    topP: z.ZodOptional<z.ZodNumber>;
    stopSequences: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
}, {
    frequencyPenalty: z.ZodOptional<z.ZodNumber>;
    logitBias: z.ZodOptional<z.ZodRecord<z.ZodString, z.ZodNumber>>;
    logProbs: z.ZodOptional<z.ZodBoolean>;
    presencePenalty: z.ZodOptional<z.ZodNumber>;
    seed: z.ZodOptional<z.ZodNumber>;
    topLogProbs: z.ZodOptional<z.ZodNumber>;
    user: z.ZodOptional<z.ZodString>;
}>, {
    location: z.ZodOptional<z.ZodString>;
}>, z.ZodTypeAny, "passthrough">, z.objectInputType<z.objectUtil.extendShape<z.objectUtil.extendShape<{
    version: z.ZodOptional<z.ZodString>;
    temperature: z.ZodOptional<z.ZodNumber>;
    maxOutputTokens: z.ZodOptional<z.ZodNumber>;
    topK: z.ZodOptional<z.ZodNumber>;
    topP: z.ZodOptional<z.ZodNumber>;
    stopSequences: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
}, {
    frequencyPenalty: z.ZodOptional<z.ZodNumber>;
    logitBias: z.ZodOptional<z.ZodRecord<z.ZodString, z.ZodNumber>>;
    logProbs: z.ZodOptional<z.ZodBoolean>;
    presencePenalty: z.ZodOptional<z.ZodNumber>;
    seed: z.ZodOptional<z.ZodNumber>;
    topLogProbs: z.ZodOptional<z.ZodNumber>;
    user: z.ZodOptional<z.ZodString>;
}>, {
    location: z.ZodOptional<z.ZodString>;
}>, z.ZodTypeAny, "passthrough">>>;
declare const SUPPORTED_OPENAI_FORMAT_MODELS: {
    'llama3-405b': ModelReference<z.ZodObject<z.objectUtil.extendShape<z.objectUtil.extendShape<{
        version: z.ZodOptional<z.ZodString>;
        temperature: z.ZodOptional<z.ZodNumber>;
        maxOutputTokens: z.ZodOptional<z.ZodNumber>;
        topK: z.ZodOptional<z.ZodNumber>;
        topP: z.ZodOptional<z.ZodNumber>;
        stopSequences: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
    }, {
        frequencyPenalty: z.ZodOptional<z.ZodNumber>;
        logitBias: z.ZodOptional<z.ZodRecord<z.ZodString, z.ZodNumber>>;
        logProbs: z.ZodOptional<z.ZodBoolean>;
        presencePenalty: z.ZodOptional<z.ZodNumber>;
        seed: z.ZodOptional<z.ZodNumber>;
        topLogProbs: z.ZodOptional<z.ZodNumber>;
        user: z.ZodOptional<z.ZodString>;
    }>, {
        location: z.ZodOptional<z.ZodString>;
    }>, "passthrough", z.ZodTypeAny, z.objectOutputType<z.objectUtil.extendShape<z.objectUtil.extendShape<{
        version: z.ZodOptional<z.ZodString>;
        temperature: z.ZodOptional<z.ZodNumber>;
        maxOutputTokens: z.ZodOptional<z.ZodNumber>;
        topK: z.ZodOptional<z.ZodNumber>;
        topP: z.ZodOptional<z.ZodNumber>;
        stopSequences: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
    }, {
        frequencyPenalty: z.ZodOptional<z.ZodNumber>;
        logitBias: z.ZodOptional<z.ZodRecord<z.ZodString, z.ZodNumber>>;
        logProbs: z.ZodOptional<z.ZodBoolean>;
        presencePenalty: z.ZodOptional<z.ZodNumber>;
        seed: z.ZodOptional<z.ZodNumber>;
        topLogProbs: z.ZodOptional<z.ZodNumber>;
        user: z.ZodOptional<z.ZodString>;
    }>, {
        location: z.ZodOptional<z.ZodString>;
    }>, z.ZodTypeAny, "passthrough">, z.objectInputType<z.objectUtil.extendShape<z.objectUtil.extendShape<{
        version: z.ZodOptional<z.ZodString>;
        temperature: z.ZodOptional<z.ZodNumber>;
        maxOutputTokens: z.ZodOptional<z.ZodNumber>;
        topK: z.ZodOptional<z.ZodNumber>;
        topP: z.ZodOptional<z.ZodNumber>;
        stopSequences: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
    }, {
        frequencyPenalty: z.ZodOptional<z.ZodNumber>;
        logitBias: z.ZodOptional<z.ZodRecord<z.ZodString, z.ZodNumber>>;
        logProbs: z.ZodOptional<z.ZodBoolean>;
        presencePenalty: z.ZodOptional<z.ZodNumber>;
        seed: z.ZodOptional<z.ZodNumber>;
        topLogProbs: z.ZodOptional<z.ZodNumber>;
        user: z.ZodOptional<z.ZodString>;
    }>, {
        location: z.ZodOptional<z.ZodString>;
    }>, z.ZodTypeAny, "passthrough">>>;
    'llama-3.1': ModelReference<z.ZodObject<z.objectUtil.extendShape<z.objectUtil.extendShape<{
        version: z.ZodOptional<z.ZodString>;
        temperature: z.ZodOptional<z.ZodNumber>;
        maxOutputTokens: z.ZodOptional<z.ZodNumber>;
        topK: z.ZodOptional<z.ZodNumber>;
        topP: z.ZodOptional<z.ZodNumber>;
        stopSequences: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
    }, {
        frequencyPenalty: z.ZodOptional<z.ZodNumber>;
        logitBias: z.ZodOptional<z.ZodRecord<z.ZodString, z.ZodNumber>>;
        logProbs: z.ZodOptional<z.ZodBoolean>;
        presencePenalty: z.ZodOptional<z.ZodNumber>;
        seed: z.ZodOptional<z.ZodNumber>;
        topLogProbs: z.ZodOptional<z.ZodNumber>;
        user: z.ZodOptional<z.ZodString>;
    }>, {
        location: z.ZodOptional<z.ZodString>;
    }>, "passthrough", z.ZodTypeAny, z.objectOutputType<z.objectUtil.extendShape<z.objectUtil.extendShape<{
        version: z.ZodOptional<z.ZodString>;
        temperature: z.ZodOptional<z.ZodNumber>;
        maxOutputTokens: z.ZodOptional<z.ZodNumber>;
        topK: z.ZodOptional<z.ZodNumber>;
        topP: z.ZodOptional<z.ZodNumber>;
        stopSequences: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
    }, {
        frequencyPenalty: z.ZodOptional<z.ZodNumber>;
        logitBias: z.ZodOptional<z.ZodRecord<z.ZodString, z.ZodNumber>>;
        logProbs: z.ZodOptional<z.ZodBoolean>;
        presencePenalty: z.ZodOptional<z.ZodNumber>;
        seed: z.ZodOptional<z.ZodNumber>;
        topLogProbs: z.ZodOptional<z.ZodNumber>;
        user: z.ZodOptional<z.ZodString>;
    }>, {
        location: z.ZodOptional<z.ZodString>;
    }>, z.ZodTypeAny, "passthrough">, z.objectInputType<z.objectUtil.extendShape<z.objectUtil.extendShape<{
        version: z.ZodOptional<z.ZodString>;
        temperature: z.ZodOptional<z.ZodNumber>;
        maxOutputTokens: z.ZodOptional<z.ZodNumber>;
        topK: z.ZodOptional<z.ZodNumber>;
        topP: z.ZodOptional<z.ZodNumber>;
        stopSequences: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
    }, {
        frequencyPenalty: z.ZodOptional<z.ZodNumber>;
        logitBias: z.ZodOptional<z.ZodRecord<z.ZodString, z.ZodNumber>>;
        logProbs: z.ZodOptional<z.ZodBoolean>;
        presencePenalty: z.ZodOptional<z.ZodNumber>;
        seed: z.ZodOptional<z.ZodNumber>;
        topLogProbs: z.ZodOptional<z.ZodNumber>;
        user: z.ZodOptional<z.ZodString>;
    }>, {
        location: z.ZodOptional<z.ZodString>;
    }>, z.ZodTypeAny, "passthrough">>>;
    'llama-3.2': ModelReference<z.ZodObject<z.objectUtil.extendShape<z.objectUtil.extendShape<{
        version: z.ZodOptional<z.ZodString>;
        temperature: z.ZodOptional<z.ZodNumber>;
        maxOutputTokens: z.ZodOptional<z.ZodNumber>;
        topK: z.ZodOptional<z.ZodNumber>;
        topP: z.ZodOptional<z.ZodNumber>;
        stopSequences: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
    }, {
        frequencyPenalty: z.ZodOptional<z.ZodNumber>;
        logitBias: z.ZodOptional<z.ZodRecord<z.ZodString, z.ZodNumber>>;
        logProbs: z.ZodOptional<z.ZodBoolean>;
        presencePenalty: z.ZodOptional<z.ZodNumber>;
        seed: z.ZodOptional<z.ZodNumber>;
        topLogProbs: z.ZodOptional<z.ZodNumber>;
        user: z.ZodOptional<z.ZodString>;
    }>, {
        location: z.ZodOptional<z.ZodString>;
    }>, "passthrough", z.ZodTypeAny, z.objectOutputType<z.objectUtil.extendShape<z.objectUtil.extendShape<{
        version: z.ZodOptional<z.ZodString>;
        temperature: z.ZodOptional<z.ZodNumber>;
        maxOutputTokens: z.ZodOptional<z.ZodNumber>;
        topK: z.ZodOptional<z.ZodNumber>;
        topP: z.ZodOptional<z.ZodNumber>;
        stopSequences: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
    }, {
        frequencyPenalty: z.ZodOptional<z.ZodNumber>;
        logitBias: z.ZodOptional<z.ZodRecord<z.ZodString, z.ZodNumber>>;
        logProbs: z.ZodOptional<z.ZodBoolean>;
        presencePenalty: z.ZodOptional<z.ZodNumber>;
        seed: z.ZodOptional<z.ZodNumber>;
        topLogProbs: z.ZodOptional<z.ZodNumber>;
        user: z.ZodOptional<z.ZodString>;
    }>, {
        location: z.ZodOptional<z.ZodString>;
    }>, z.ZodTypeAny, "passthrough">, z.objectInputType<z.objectUtil.extendShape<z.objectUtil.extendShape<{
        version: z.ZodOptional<z.ZodString>;
        temperature: z.ZodOptional<z.ZodNumber>;
        maxOutputTokens: z.ZodOptional<z.ZodNumber>;
        topK: z.ZodOptional<z.ZodNumber>;
        topP: z.ZodOptional<z.ZodNumber>;
        stopSequences: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
    }, {
        frequencyPenalty: z.ZodOptional<z.ZodNumber>;
        logitBias: z.ZodOptional<z.ZodRecord<z.ZodString, z.ZodNumber>>;
        logProbs: z.ZodOptional<z.ZodBoolean>;
        presencePenalty: z.ZodOptional<z.ZodNumber>;
        seed: z.ZodOptional<z.ZodNumber>;
        topLogProbs: z.ZodOptional<z.ZodNumber>;
        user: z.ZodOptional<z.ZodString>;
    }>, {
        location: z.ZodOptional<z.ZodString>;
    }>, z.ZodTypeAny, "passthrough">>>;
};
declare function modelGardenOpenaiCompatibleModel(ai: Genkit, name: string, projectId: string, location: string, googleAuth: GoogleAuth, baseUrlTemplate: string | undefined): ModelAction<typeof ModelGardenModelConfigSchema>;

export { ModelGardenModelConfigSchema, SUPPORTED_OPENAI_FORMAT_MODELS, llama3, llama31, llama32, modelGardenOpenaiCompatibleModel };
