// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.aiplatform.v1beta1.schema.trainingjob.definition;


option csharp_namespace = "Google.Cloud.AIPlatform.V1Beta1.Schema.TrainingJob.Definition";
option go_package = "cloud.google.com/go/aiplatform/apiv1beta1/schema/trainingjob/definition/definitionpb;definitionpb";
option java_multiple_files = true;
option java_outer_classname = "AutoMLImageClassificationProto";
option java_package = "com.google.cloud.aiplatform.v1beta1.schema.trainingjob.definition";
option php_namespace = "Google\\Cloud\\AIPlatform\\V1beta1\\Schema\\TrainingJob\\Definition";
option ruby_package = "Google::Cloud::AIPlatform::V1beta1::Schema::TrainingJob::Definition";

// A TrainingJob that trains and uploads an AutoML Image Classification Model.
message AutoMlImageClassification {
  // The input parameters of this TrainingJob.
  AutoMlImageClassificationInputs inputs = 1;

  // The metadata information.
  AutoMlImageClassificationMetadata metadata = 2;
}

message AutoMlImageClassificationInputs {
  enum ModelType {
    // Should not be set.
    MODEL_TYPE_UNSPECIFIED = 0;

    // A Model best tailored to be used within Google Cloud, and which cannot
    // be exported.
    // Default.
    CLOUD = 1;

    // A model that, in addition to being available within Google
    // Cloud, can also be exported (see ModelService.ExportModel) as TensorFlow
    // or Core ML model and used on a mobile or edge device afterwards.
    // Expected to have low latency, but may have lower prediction
    // quality than other mobile models.
    MOBILE_TF_LOW_LATENCY_1 = 2;

    // A model that, in addition to being available within Google
    // Cloud, can also be exported (see ModelService.ExportModel) as TensorFlow
    // or Core ML model and used on a mobile or edge device with afterwards.
    MOBILE_TF_VERSATILE_1 = 3;

    // A model that, in addition to being available within Google
    // Cloud, can also be exported (see ModelService.ExportModel) as TensorFlow
    // or Core ML model and used on a mobile or edge device afterwards.
    // Expected to have a higher latency, but should also have a higher
    // prediction quality than other mobile models.
    MOBILE_TF_HIGH_ACCURACY_1 = 4;
  }

  ModelType model_type = 1;

  // The ID of the `base` model. If it is specified, the new model will be
  // trained based on the `base` model. Otherwise, the new model will be
  // trained from scratch. The `base` model must be in the same
  // Project and Location as the new Model to train, and have the same
  // modelType.
  string base_model_id = 2;

  // The training budget of creating this model, expressed in milli node
  // hours i.e. 1,000 value in this field means 1 node hour. The actual
  // metadata.costMilliNodeHours will be equal or less than this value.
  // If further model training ceases to provide any improvements, it will
  // stop without using the full budget and the metadata.successfulStopReason
  // will be `model-converged`.
  // Note, node_hour  = actual_hour * number_of_nodes_involved.
  // For modelType `cloud`(default), the budget must be between 8,000
  // and 800,000 milli node hours, inclusive. The default value is 192,000
  // which represents one day in wall time, considering 8 nodes are used.
  // For model types `mobile-tf-low-latency-1`, `mobile-tf-versatile-1`,
  // `mobile-tf-high-accuracy-1`, the training budget must be between
  // 1,000 and 100,000 milli node hours, inclusive.
  // The default value is 24,000 which represents one day in wall time on a
  // single node that is used.
  int64 budget_milli_node_hours = 3;

  // Use the entire training budget. This disables the early stopping feature.
  // When false the early stopping feature is enabled, which means that
  // AutoML Image Classification might stop training before the entire
  // training budget has been used.
  bool disable_early_stopping = 4;

  // If false, a single-label (multi-class) Model will be trained (i.e.
  // assuming that for each image just up to one annotation may be
  // applicable). If true, a multi-label Model will be trained (i.e.
  // assuming that for each image multiple annotations may be applicable).
  bool multi_label = 5;
}

message AutoMlImageClassificationMetadata {
  enum SuccessfulStopReason {
    // Should not be set.
    SUCCESSFUL_STOP_REASON_UNSPECIFIED = 0;

    // The inputs.budgetMilliNodeHours had been reached.
    BUDGET_REACHED = 1;

    // Further training of the Model ceased to increase its quality, since it
    // already has converged.
    MODEL_CONVERGED = 2;
  }

  // The actual training cost of creating this model, expressed in
  // milli node hours, i.e. 1,000 value in this field means 1 node hour.
  // Guaranteed to not exceed inputs.budgetMilliNodeHours.
  int64 cost_milli_node_hours = 1;

  // For successful job completions, this is the reason why the job has
  // finished.
  SuccessfulStopReason successful_stop_reason = 2;
}
