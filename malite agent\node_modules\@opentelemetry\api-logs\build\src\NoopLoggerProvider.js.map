{"version": 3, "file": "NoopLoggerProvider.js", "sourceRoot": "", "sources": ["../../src/NoopLoggerProvider.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAKH,6CAA0C;AAE1C,MAAa,kBAAkB;IAC7B,SAAS,CACP,KAAa,EACb,QAA6B,EAC7B,QAAoC;QAEpC,OAAO,IAAI,uBAAU,EAAE,CAAC;IAC1B,CAAC;CACF;AARD,gDAQC;AAEY,QAAA,oBAAoB,GAAG,IAAI,kBAAkB,EAAE,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { LoggerProvider } from './types/LoggerProvider';\nimport { Logger } from './types/Logger';\nimport { LoggerOptions } from './types/LoggerOptions';\nimport { NoopLogger } from './NoopLogger';\n\nexport class NoopLoggerProvider implements LoggerProvider {\n  getLogger(\n    _name: string,\n    _version?: string | undefined,\n    _options?: LoggerOptions | undefined\n  ): Logger {\n    return new NoopLogger();\n  }\n}\n\nexport const NOOP_LOGGER_PROVIDER = new NoopLoggerProvider();\n"]}