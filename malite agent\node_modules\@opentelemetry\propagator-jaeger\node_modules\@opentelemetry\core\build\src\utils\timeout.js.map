{"version": 3, "file": "timeout.js", "sourceRoot": "", "sources": ["../../../src/utils/timeout.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEH;;GAEG;AACH,MAAa,YAAa,SAAQ,KAAK;IACrC,YAAY,OAAgB;QAC1B,KAAK,CAAC,OAAO,CAAC,CAAC;QAEf,0FAA0F;QAC1F,6IAA6I;QAC7I,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,YAAY,CAAC,SAAS,CAAC,CAAC;IACtD,CAAC;CACF;AARD,oCAQC;AAED;;;;;;;;GAQG;AACH,SAAgB,eAAe,CAC7B,OAAmB,EACnB,OAAe;IAEf,IAAI,aAA4C,CAAC;IAEjD,MAAM,cAAc,GAAG,IAAI,OAAO,CAAQ,SAAS,eAAe,CAChE,QAAQ,EACR,MAAM;QAEN,aAAa,GAAG,UAAU,CAAC,SAAS,cAAc;YAChD,MAAM,CAAC,IAAI,YAAY,CAAC,sBAAsB,CAAC,CAAC,CAAC;QACnD,CAAC,EAAE,OAAO,CAAC,CAAC;IACd,CAAC,CAAC,CAAC;IAEH,OAAO,OAAO,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,cAAc,CAAC,CAAC,CAAC,IAAI,CACjD,MAAM,CAAC,EAAE;QACP,YAAY,CAAC,aAAa,CAAC,CAAC;QAC5B,OAAO,MAAM,CAAC;IAChB,CAAC,EACD,MAAM,CAAC,EAAE;QACP,YAAY,CAAC,aAAa,CAAC,CAAC;QAC5B,MAAM,MAAM,CAAC;IACf,CAAC,CACF,CAAC;AACJ,CAAC;AAzBD,0CAyBC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * Error that is thrown on timeouts.\n */\nexport class TimeoutError extends Error {\n  constructor(message?: string) {\n    super(message);\n\n    // manually adjust prototype to retain `instanceof` functionality when targeting ES5, see:\n    // https://github.com/Microsoft/TypeScript-wiki/blob/main/Breaking-Changes.md#extending-built-ins-like-error-array-and-map-may-no-longer-work\n    Object.setPrototypeOf(this, TimeoutError.prototype);\n  }\n}\n\n/**\n * Adds a timeout to a promise and rejects if the specified timeout has elapsed. Also rejects if the specified promise\n * rejects, and resolves if the specified promise resolves.\n *\n * <p> NOTE: this operation will continue even after it throws a {@link TimeoutError}.\n *\n * @param promise promise to use with timeout.\n * @param timeout the timeout in milliseconds until the returned promise is rejected.\n */\nexport function callWithTimeout<T>(\n  promise: Promise<T>,\n  timeout: number\n): Promise<T> {\n  let timeoutHandle: ReturnType<typeof setTimeout>;\n\n  const timeoutPromise = new Promise<never>(function timeoutFunction(\n    _resolve,\n    reject\n  ) {\n    timeoutHandle = setTimeout(function timeoutHandler() {\n      reject(new TimeoutError('Operation timed out.'));\n    }, timeout);\n  });\n\n  return Promise.race([promise, timeoutPromise]).then(\n    result => {\n      clearTimeout(timeoutHandle);\n      return result;\n    },\n    reason => {\n      clearTimeout(timeoutHandle);\n      throw reason;\n    }\n  );\n}\n"]}