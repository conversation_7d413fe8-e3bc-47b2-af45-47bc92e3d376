// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.aiplatform.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/aiplatform/v1/content.proto";
import "google/cloud/aiplatform/v1/tool.proto";
import "google/protobuf/duration.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.AIPlatform.V1";
option go_package = "cloud.google.com/go/aiplatform/apiv1/aiplatformpb;aiplatformpb";
option java_multiple_files = true;
option java_outer_classname = "CachedContentProto";
option java_package = "com.google.cloud.aiplatform.v1";
option php_namespace = "Google\\Cloud\\AIPlatform\\V1";
option ruby_package = "Google::Cloud::AIPlatform::V1";

// A resource used in LLM queries for users to explicitly specify what to cache
// and how to cache.
message CachedContent {
  option (google.api.resource) = {
    type: "aiplatform.googleapis.com/CachedContent"
    pattern: "projects/{project}/locations/{location}/cachedContents/{cached_content}"
    plural: "cachedContents"
    singular: "cachedContent"
  };

  // Metadata on the usage of the cached content.
  message UsageMetadata {
    // Total number of tokens that the cached content consumes.
    int32 total_token_count = 1;

    // Number of text characters.
    int32 text_count = 2;

    // Number of images.
    int32 image_count = 3;

    // Duration of video in seconds.
    int32 video_duration_seconds = 4;

    // Duration of audio in seconds.
    int32 audio_duration_seconds = 5;
  }

  // Expiration time of the cached content.
  oneof expiration {
    // Timestamp of when this resource is considered expired.
    // This is *always* provided on output, regardless of what was sent
    // on input.
    google.protobuf.Timestamp expire_time = 9;

    // Input only. The TTL for this resource. The expiration time is computed:
    // now + TTL.
    google.protobuf.Duration ttl = 10
        [(google.api.field_behavior) = INPUT_ONLY];
  }

  // Immutable. Identifier. The server-generated resource name of the cached
  // content Format:
  // projects/{project}/locations/{location}/cachedContents/{cached_content}
  string name = 1 [
    (google.api.field_behavior) = IDENTIFIER,
    (google.api.field_behavior) = IMMUTABLE
  ];

  // Optional. Immutable. The user-generated meaningful display name of the
  // cached content.
  string display_name = 11 [
    (google.api.field_behavior) = OPTIONAL,
    (google.api.field_behavior) = IMMUTABLE
  ];

  // Immutable. The name of the publisher model to use for cached content.
  // Format:
  // projects/{project}/locations/{location}/publishers/{publisher}/models/{model}
  string model = 2 [(google.api.field_behavior) = IMMUTABLE];

  // Optional. Input only. Immutable. Developer set system instruction.
  // Currently, text only
  Content system_instruction = 3 [
    (google.api.field_behavior) = OPTIONAL,
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.field_behavior) = INPUT_ONLY
  ];

  // Optional. Input only. Immutable. The content to cache
  repeated Content contents = 4 [
    (google.api.field_behavior) = OPTIONAL,
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.field_behavior) = INPUT_ONLY
  ];

  // Optional. Input only. Immutable. A list of `Tools` the model may use to
  // generate the next response
  repeated Tool tools = 5 [
    (google.api.field_behavior) = OPTIONAL,
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.field_behavior) = INPUT_ONLY
  ];

  // Optional. Input only. Immutable. Tool config. This config is shared for all
  // tools
  ToolConfig tool_config = 6 [
    (google.api.field_behavior) = OPTIONAL,
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.field_behavior) = INPUT_ONLY
  ];

  // Output only. Creatation time of the cache entry.
  google.protobuf.Timestamp create_time = 7
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. When the cache entry was last updated in UTC time.
  google.protobuf.Timestamp update_time = 8
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Metadata on the usage of the cached content.
  UsageMetadata usage_metadata = 12 [(google.api.field_behavior) = OUTPUT_ONLY];
}
