import { genkitPlugin } from "genkit/plugin";
import { getDerivedParams } from "../common/index.js";
import { SUPPORTED_ANTHROPIC_MODELS, anthropicModel } from "./anthropic.js";
import { SUPPORTED_MISTRAL_MODELS, mistralModel } from "./mistral.js";
import {
  SUPPORTED_OPENAI_FORMAT_MODELS,
  modelGardenOpenaiCompatibleModel
} from "./model_garden.js";
function vertexAIModelGarden(options) {
  return genkitPlugin("vertexAIModelGarden", async (ai) => {
    const { projectId, location, authClient } = await getDerivedParams(options);
    options.models.forEach((m) => {
      const anthropicEntry = Object.entries(SUPPORTED_ANTHROPIC_MODELS).find(
        ([_, value]) => value.name === m.name
      );
      if (anthropicEntry) {
        anthropicModel(ai, anthropicEntry[0], projectId, location);
        return;
      }
      const mistralEntry = Object.entries(SUPPORTED_MISTRAL_MODELS).find(
        ([_, value]) => value.name === m.name
      );
      if (mistralEntry) {
        mistralModel(ai, mistralEntry[0], projectId, location);
        return;
      }
      const openaiModel = Object.entries(SUPPORTED_OPENAI_FORMAT_MODELS).find(
        ([_, value]) => value.name === m.name
      );
      if (openaiModel) {
        modelGardenOpenaiCompatibleModel(
          ai,
          openaiModel[0],
          projectId,
          location,
          authClient,
          options.openAiBaseUrlTemplate
        );
        return;
      }
      throw new Error(`Unsupported model garden model: ${m.name}`);
    });
  });
}
import {
  claude35Sonnet,
  claude35SonnetV2,
  claude3Haiku,
  claude3Opus,
  claude3Sonnet
} from "./anthropic.js";
import { codestral, mistralLarge, mistralNemo } from "./mistral.js";
import { llama3, llama31, llama32 } from "./model_garden.js";
export {
  claude35Sonnet,
  claude35SonnetV2,
  claude3Haiku,
  claude3Opus,
  claude3Sonnet,
  codestral,
  llama3,
  llama31,
  llama32,
  mistralLarge,
  mistralNemo,
  vertexAIModelGarden
};
//# sourceMappingURL=index.mjs.map