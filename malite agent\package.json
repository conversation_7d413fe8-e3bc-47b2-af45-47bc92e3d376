{"name": "malite-agent", "version": "1.0.0", "description": "مشروع Genkit متقدم للذكاء الاصطناعي مع دعم كامل للغة العربية", "type": "module", "main": "index.js", "scripts": {"start": "node index.js", "dev": "genkit start -- node index.js", "dev-open": "genkit start -o -- node index.js", "vertex:basic": "cd vertex-ai && node index.js", "vertex:arabic": "cd vertex-ai && node arabic-examples.js", "vertex:advanced": "cd vertex-ai && node advanced-features.js", "vertex:practical": "cd vertex-ai && node practical-applications.js", "vertex:multimodal": "cd vertex-ai && node multimodal-examples.js", "vertex:generative": "cd vertex-ai && node generative-ai-examples.js", "vertex:nlp": "cd vertex-ai && node advanced-nlp-examples.js", "vertex:config": "cd vertex-ai && node usage-examples.js", "vertex:advanced-features": "cd vertex-ai && node advanced-vertex-features.js", "vertex:all": "npm run vertex:basic && npm run vertex:arabic && npm run vertex:practical", "demo": "node scripts/quick-demo.js", "demo:full": "npm run start && npm run vertex:arabic", "test": "echo \"تشغيل اختبارات المشروع...\" && npm run start", "info": "node scripts/project-info.js", "setup": "node scripts/setup-check.js"}, "keywords": ["genkit", "vertex-ai", "google-ai", "arabic", "ai", "machine-learning", "nlp", "chatbot"], "author": "Malite Agent Team", "license": "MIT", "dependencies": {"@genkit-ai/googleai": "^1.10.0", "@genkit-ai/vertexai": "^1.10.0", "dotenv": "^16.3.1", "genkit": "^1.10.0"}, "engines": {"node": ">=18.0.0"}}