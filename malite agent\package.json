{"name": "malite-agent", "version": "1.0.0", "description": "مشروع Genkit متقدم للذكاء الاصطناعي مع دعم كامل للغة العربية", "type": "module", "main": "index.js", "scripts": {"start": "node server.js", "start:dev": "node index.js", "dev": "genkit start -- node server.js", "dev-open": "genkit start -o -- node server.js", "build": "echo 'No build step required for Node.js'", "vertex:basic": "cd vertex-ai && node index.js", "vertex:arabic": "cd vertex-ai && node arabic-examples.js", "vertex:advanced": "cd vertex-ai && node advanced-features.js", "vertex:practical": "cd vertex-ai && node practical-applications.js", "vertex:multimodal": "cd vertex-ai && node multimodal-examples.js", "vertex:generative": "cd vertex-ai && node generative-ai-examples.js", "vertex:nlp": "cd vertex-ai && node advanced-nlp-examples.js", "vertex:config": "cd vertex-ai && node usage-examples.js", "vertex:advanced-features": "cd vertex-ai && node advanced-vertex-features.js", "vertex:models": "node examples/vertex-models-usage.js", "vertex:all": "npm run vertex:basic && npm run vertex:arabic && npm run vertex:practical", "demo": "node scripts/quick-demo.js", "demo:full": "npm run start && npm run vertex:arabic", "test": "echo \"تشغيل اختبارات المشروع...\" && npm run start", "test:models": "node tests/vertex-models-test.js", "deploy:gcp": "gcloud app deploy --quiet", "deploy:cloud-run": "gcloud run deploy malite-agent --source . --region us-central1 --allow-unauthenticated", "docker:build": "docker build -t malite-agent .", "docker:build:prod": "docker build -t malite-agent:prod --build-arg NODE_ENV=production .", "docker:run": "docker run -p 8080:8080 malite-agent", "docker:run:detached": "docker run -d -p 8080:8080 --name malite-app malite-agent", "docker:stop": "docker stop malite-app && docker rm malite-app", "docker:logs": "docker logs -f malite-app", "docker:compose:up": "docker-compose up", "docker:compose:up:detached": "docker-compose up -d", "docker:compose:down": "docker-compose down", "docker:compose:prod": "docker-compose -f docker-compose.prod.yml up -d", "info": "node scripts/project-info.js", "setup": "node scripts/setup-check.js"}, "keywords": ["genkit", "vertex-ai", "google-ai", "arabic", "ai", "machine-learning", "nlp", "chatbot"], "author": "Malite Agent Team", "license": "MIT", "dependencies": {"@genkit-ai/googleai": "^1.10.0", "@genkit-ai/vertexai": "^1.10.0", "@genkit-ai/flow": "^1.10.0", "dotenv": "^16.3.1", "express": "^4.18.2", "genkit": "^1.10.0"}, "engines": {"node": ">=18.0.0"}}