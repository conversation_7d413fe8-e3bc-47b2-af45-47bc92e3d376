{"version": 3, "file": "ReadableSpan.js", "sourceRoot": "", "sources": ["../../../src/export/ReadableSpan.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  SpanKind,\n  SpanStatus,\n  SpanAttributes,\n  HrTime,\n  Link,\n  SpanContext,\n} from '@opentelemetry/api';\nimport { IResource } from '@opentelemetry/resources';\nimport { InstrumentationLibrary } from '@opentelemetry/core';\nimport { TimedEvent } from '../TimedEvent';\n\nexport interface ReadableSpan {\n  readonly name: string;\n  readonly kind: SpanKind;\n  readonly spanContext: () => SpanContext;\n  readonly parentSpanId?: string;\n  readonly startTime: HrTime;\n  readonly endTime: HrTime;\n  readonly status: SpanStatus;\n  readonly attributes: SpanAttributes;\n  readonly links: Link[];\n  readonly events: TimedEvent[];\n  readonly duration: HrTime;\n  readonly ended: boolean;\n  readonly resource: IResource;\n  readonly instrumentationLibrary: InstrumentationLibrary;\n  readonly droppedAttributesCount: number;\n  readonly droppedEventsCount: number;\n  readonly droppedLinksCount: number;\n}\n"]}