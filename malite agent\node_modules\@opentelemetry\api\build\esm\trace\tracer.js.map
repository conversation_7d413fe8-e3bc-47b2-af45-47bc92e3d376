{"version": 3, "file": "tracer.js", "sourceRoot": "", "sources": ["../../../src/trace/tracer.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Context } from '../context/types';\nimport { Span } from './span';\nimport { SpanOptions } from './SpanOptions';\n\n/**\n * Tracer provides an interface for creating {@link Span}s.\n */\nexport interface Tracer {\n  /**\n   * Starts a new {@link Span}. Start the span without setting it on context.\n   *\n   * This method do NOT modify the current Context.\n   *\n   * @param name The name of the span\n   * @param [options] SpanOptions used for span creation\n   * @param [context] Context to use to extract parent\n   * @returns Span The newly created span\n   * @example\n   *     const span = tracer.startSpan('op');\n   *     span.setAttribute('key', 'value');\n   *     span.end();\n   */\n  startSpan(name: string, options?: SpanOptions, context?: Context): Span;\n\n  /**\n   * Starts a new {@link Span} and calls the given function passing it the\n   * created span as first argument.\n   * Additionally the new span gets set in context and this context is activated\n   * for the duration of the function call.\n   *\n   * @param name The name of the span\n   * @param [options] SpanOptions used for span creation\n   * @param [context] Context to use to extract parent\n   * @param fn function called in the context of the span and receives the newly created span as an argument\n   * @returns return value of fn\n   * @example\n   *     const something = tracer.startActiveSpan('op', span => {\n   *       try {\n   *         do some work\n   *         span.setStatus({code: SpanStatusCode.OK});\n   *         return something;\n   *       } catch (err) {\n   *         span.setStatus({\n   *           code: SpanStatusCode.ERROR,\n   *           message: err.message,\n   *         });\n   *         throw err;\n   *       } finally {\n   *         span.end();\n   *       }\n   *     });\n   *\n   * @example\n   *     const span = tracer.startActiveSpan('op', span => {\n   *       try {\n   *         do some work\n   *         return span;\n   *       } catch (err) {\n   *         span.setStatus({\n   *           code: SpanStatusCode.ERROR,\n   *           message: err.message,\n   *         });\n   *         throw err;\n   *       }\n   *     });\n   *     do some more work\n   *     span.end();\n   */\n  startActiveSpan<F extends (span: Span) => unknown>(\n    name: string,\n    fn: F\n  ): ReturnType<F>;\n  startActiveSpan<F extends (span: Span) => unknown>(\n    name: string,\n    options: SpanOptions,\n    fn: F\n  ): ReturnType<F>;\n  startActiveSpan<F extends (span: Span) => unknown>(\n    name: string,\n    options: SpanOptions,\n    context: Context,\n    fn: F\n  ): ReturnType<F>;\n}\n"]}