{"version": 3, "file": "sdkvalidationerror.js", "sourceRoot": "", "sources": ["../../src/models/errors/sdkvalidationerror.ts"], "names": [], "mappings": ";AAAA;;GAEG;;;;;;;;;;;;;;;;;;;;;;;;;;AAqCH,wCAyDC;AA5FD,uCAAyB;AAEzB,MAAa,kBAAmB,SAAQ,KAAK;IAW3C,YAAY,OAAe,EAAE,KAAc,EAAE,QAAiB;QAC5D,KAAK,CAAC,GAAG,OAAO,KAAK,KAAK,EAAE,CAAC,CAAC;QAC9B,IAAI,CAAC,IAAI,GAAG,oBAAoB,CAAC;QACjC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,CAAC,UAAU,GAAG,OAAO,CAAC;IAC5B,CAAC;IAED;;;;OAIG;IACI,MAAM;QACX,IAAI,IAAI,CAAC,KAAK,YAAY,CAAC,CAAC,QAAQ,EAAE,CAAC;YACrC,OAAO,GAAG,IAAI,CAAC,UAAU,KAAK,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;QAC7D,CAAC;aAAM,CAAC;YACN,OAAO,IAAI,CAAC,QAAQ,EAAE,CAAC;QACzB,CAAC;IACH,CAAC;CACF;AA/BD,gDA+BC;AAED,SAAgB,cAAc,CAAC,GAAe,EAAE,KAAK,GAAG,CAAC;IACvD,IAAI,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;IAC7B,GAAG,GAAG,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC;IAClC,GAAG,IAAI,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;IAEzB,IAAI,OAAO,GAAG,EAAE,CAAC;IACjB,MAAM,MAAM,GAAG,CAAC,GAAW,EAAE,EAAE,CAAC,CAAC,OAAO,IAAI,KAAK,GAAG,GAAG,GAAG,EAAE,CAAC,CAAC;IAE9D,MAAM,GAAG,GAAG,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC;IAC9B,MAAM,QAAQ,GAAG,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,cAAc,CAAC,CAAC,CAAC,GAAG,GAAG,eAAe,CAAC;IAE1E,IAAI,GAAG,EAAE,CAAC;QACR,MAAM,CAAC,KAAK,QAAQ,GAAG,CAAC,CAAC;IAC3B,CAAC;IAED,KAAK,MAAM,KAAK,IAAI,GAAG,CAAC,MAAM,EAAE,CAAC;QAC/B,IAAI,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAChC,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC,UAAU,IAAI,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC;QAC1C,MAAM,CAAC,QAAQ,IAAI,MAAM,KAAK,CAAC,OAAO,KAAK,KAAK,CAAC,IAAI,GAAG,CAAC,CAAC;QAC1D,QAAQ,KAAK,CAAC,IAAI,EAAE,CAAC;YACnB,KAAK,iBAAiB,CAAC;YACvB,KAAK,cAAc,CAAC,CAAC,CAAC;gBACpB,MAAM,CAAC,eAAe,KAAK,CAAC,QAAQ,EAAE,CAAC,CAAC;gBACxC,MAAM,CAAC,eAAe,KAAK,CAAC,QAAQ,EAAE,CAAC,CAAC;gBACxC,MAAM;YACR,CAAC;YACD,KAAK,mBAAmB,CAAC,CAAC,CAAC;gBACzB,MAAM,CAAC,eAAe,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;gBAC/C,MAAM;YACR,CAAC;YACD,KAAK,oBAAoB,CAAC,CAAC,CAAC;gBAC1B,MAAM,CAAC,kBAAkB,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;gBACrD,MAAM,CAAC,kBAAkB,KAAK,CAAC,QAAQ,EAAE,CAAC,CAAC;gBAC3C,MAAM;YACR,CAAC;YACD,KAAK,6BAA6B,CAAC,CAAC,CAAC;gBACnC,MAAM,CAAC,kBAAkB,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;gBACrD,MAAM;YACR,CAAC;YACD,KAAK,eAAe,CAAC,CAAC,CAAC;gBACrB,MAAM,GAAG,GAAG,KAAK,CAAC,WAAW,CAAC,MAAM,CAAC;gBACrC,MAAM,CACJ,8CAA8C,GAAG,iBAAiB,CACnE,CAAC;gBACF,KAAK,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE;oBACnC,MAAM,CAAC,iBAAiB,CAAC,GAAG,CAAC,OAAO,GAAG,EAAE,CAAC,CAAC;oBAC3C,MAAM,CAAC,GAAG,cAAc,CAAC,GAAG,EAAE,KAAK,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;gBAC9C,CAAC,CAAC,CAAC;YACL,CAAC;QACH,CAAC;IACH,CAAC;IAED,IAAI,GAAG,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;QACtB,MAAM,CAAC,KAAK,CAAC,CAAC;IAChB,CAAC;IAED,OAAO,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AAC1B,CAAC"}