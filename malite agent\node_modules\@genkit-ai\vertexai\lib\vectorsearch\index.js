"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
var vectorsearch_exports = {};
__export(vectorsearch_exports, {
  getBigQueryDocumentIndexer: () => import_vector_search2.getBigQueryDocumentIndexer,
  getBigQueryDocumentRetriever: () => import_vector_search2.getBigQueryDocumentRetriever,
  getFirestoreDocumentIndexer: () => import_vector_search2.getFirestoreDocumentIndexer,
  getFirestoreDocumentRetriever: () => import_vector_search2.getFirestoreDocumentRetriever,
  vertexAIVectorSearch: () => vertexAIVectorSearch,
  vertexAiIndexerRef: () => import_vector_search2.vertexAiIndexerRef,
  vertexAiIndexers: () => import_vector_search2.vertexAiIndexers,
  vertexAiRetrieverRef: () => import_vector_search2.vertexAiRetrieverRef,
  vertexAiRetrievers: () => import_vector_search2.vertexAiRetrievers
});
module.exports = __toCommonJS(vectorsearch_exports);
var import_plugin = require("genkit/plugin");
var import_common = require("../common/index.js");
var import_vector_search = require("./vector_search/index.js");
var import_vector_search2 = require("./vector_search/index.js");
function vertexAIVectorSearch(options) {
  return (0, import_plugin.genkitPlugin)("vertexAIVectorSearch", async (ai) => {
    const { authClient } = await (0, import_common.getDerivedParams)(options);
    if (options?.vectorSearchOptions && options.vectorSearchOptions.length > 0) {
      (0, import_vector_search.vertexAiIndexers)(ai, {
        pluginOptions: options,
        authClient,
        defaultEmbedder: options.embedder
      });
      (0, import_vector_search.vertexAiRetrievers)(ai, {
        pluginOptions: options,
        authClient,
        defaultEmbedder: options.embedder
      });
    }
  });
}
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  getBigQueryDocumentIndexer,
  getBigQueryDocumentRetriever,
  getFirestoreDocumentIndexer,
  getFirestoreDocumentRetriever,
  vertexAIVectorSearch,
  vertexAiIndexerRef,
  vertexAiIndexers,
  vertexAiRetrieverRef,
  vertexAiRetrievers
});
//# sourceMappingURL=index.js.map