// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.aiplatform.v1beta1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/struct.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.AIPlatform.V1Beta1";
option go_package = "cloud.google.com/go/aiplatform/apiv1beta1/aiplatformpb;aiplatformpb";
option java_multiple_files = true;
option java_outer_classname = "ReasoningEngineProto";
option java_package = "com.google.cloud.aiplatform.v1beta1";
option php_namespace = "Google\\Cloud\\AIPlatform\\V1beta1";
option ruby_package = "Google::Cloud::AIPlatform::V1beta1";

// ReasoningEngine configurations
message ReasoningEngineSpec {
  // User provided package spec like pickled object and package requirements.
  message PackageSpec {
    // Optional. The Cloud Storage URI of the pickled python object.
    string pickle_object_gcs_uri = 1 [(google.api.field_behavior) = OPTIONAL];

    // Optional. The Cloud Storage URI of the dependency files in tar.gz format.
    string dependency_files_gcs_uri = 2
        [(google.api.field_behavior) = OPTIONAL];

    // Optional. The Cloud Storage URI of the `requirements.txt` file
    string requirements_gcs_uri = 3 [(google.api.field_behavior) = OPTIONAL];

    // Optional. The Python version. Currently support 3.8, 3.9, 3.10, 3.11.
    // If not specified, default value is 3.10.
    string python_version = 4 [(google.api.field_behavior) = OPTIONAL];
  }

  // Required. User provided package spec of the ReasoningEngine.
  PackageSpec package_spec = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. Declarations for object class methods in OpenAPI specification
  // format.
  repeated google.protobuf.Struct class_methods = 3
      [(google.api.field_behavior) = OPTIONAL];
}

// ReasoningEngine provides a customizable runtime for models to determine
// which actions to take and in which order.
message ReasoningEngine {
  option (google.api.resource) = {
    type: "aiplatform.googleapis.com/ReasoningEngine"
    pattern: "projects/{project}/locations/{location}/reasoningEngines/{reasoning_engine}"
    plural: "reasoningEngines"
    singular: "reasoningEngine"
  };

  // Identifier. The resource name of the ReasoningEngine.
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // Required. The display name of the ReasoningEngine.
  string display_name = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. The description of the ReasoningEngine.
  string description = 7 [(google.api.field_behavior) = OPTIONAL];

  // Required. Configurations of the ReasoningEngine
  ReasoningEngineSpec spec = 3 [(google.api.field_behavior) = REQUIRED];

  // Output only. Timestamp when this ReasoningEngine was created.
  google.protobuf.Timestamp create_time = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Timestamp when this ReasoningEngine was most recently updated.
  google.protobuf.Timestamp update_time = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Used to perform consistent read-modify-write updates. If not set,
  // a blind "overwrite" update happens.
  string etag = 6 [(google.api.field_behavior) = OPTIONAL];
}
