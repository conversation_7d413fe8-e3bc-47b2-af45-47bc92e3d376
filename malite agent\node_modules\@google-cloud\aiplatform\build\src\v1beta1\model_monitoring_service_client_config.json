{"interfaces": {"google.cloud.aiplatform.v1beta1.ModelMonitoringService": {"retry_codes": {"non_idempotent": [], "idempotent": ["DEADLINE_EXCEEDED", "UNAVAILABLE"]}, "retry_params": {"default": {"initial_retry_delay_millis": 100, "retry_delay_multiplier": 1.3, "max_retry_delay_millis": 60000, "initial_rpc_timeout_millis": 60000, "rpc_timeout_multiplier": 1, "max_rpc_timeout_millis": 60000, "total_timeout_millis": 600000}}, "methods": {"CreateModelMonitor": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "UpdateModelMonitor": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "GetModelMonitor": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "ListModelMonitors": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "DeleteModelMonitor": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "CreateModelMonitoringJob": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "GetModelMonitoringJob": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "ListModelMonitoringJobs": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "DeleteModelMonitoringJob": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "SearchModelMonitoringStats": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "SearchModelMonitoringAlerts": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}}}}}