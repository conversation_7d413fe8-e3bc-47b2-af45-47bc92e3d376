{"version": 3, "file": "ieee754.js", "sourceRoot": "", "sources": ["../../../../../src/aggregator/exponential-histogram/mapping/ieee754.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAEH;;;;;;;;GAQG;AAEH,MAAM,CAAC,IAAM,iBAAiB,GAAG,EAAE,CAAC;AAEpC;;;GAGG;AACH,IAAM,aAAa,GAAG,UAAU,CAAC;AAEjC;;;GAGG;AACH,IAAM,gBAAgB,GAAG,OAAO,CAAC;AAEjC;;;GAGG;AACH,IAAM,aAAa,GAAG,IAAI,CAAC;AAE3B;;;GAGG;AACH,MAAM,CAAC,IAAM,mBAAmB,GAAG,CAAC,aAAa,GAAG,CAAC,CAAC;AAEtD;;;GAGG;AACH,MAAM,CAAC,IAAM,mBAAmB,GAAG,aAAa,CAAC;AAEjD;;GAEG;AACH,MAAM,CAAC,IAAM,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC;AAE5C;;;;;;;;GAQG;AACH,MAAM,UAAU,cAAc,CAAC,KAAa;IAC1C,IAAM,EAAE,GAAG,IAAI,QAAQ,CAAC,IAAI,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5C,EAAE,CAAC,UAAU,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;IACxB,8CAA8C;IAC9C,IAAM,MAAM,GAAG,EAAE,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;IAC/B,IAAM,OAAO,GAAG,CAAC,MAAM,GAAG,aAAa,CAAC,IAAI,EAAE,CAAC;IAC/C,OAAO,OAAO,GAAG,aAAa,CAAC;AACjC,CAAC;AAED;;;;GAIG;AACH,MAAM,UAAU,cAAc,CAAC,KAAa;IAC1C,IAAM,EAAE,GAAG,IAAI,QAAQ,CAAC,IAAI,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5C,EAAE,CAAC,UAAU,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;IACxB,kDAAkD;IAClD,IAAM,MAAM,GAAG,EAAE,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;IAC/B,IAAM,MAAM,GAAG,EAAE,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;IAC/B,+EAA+E;IAC/E,gFAAgF;IAChF,IAAM,iBAAiB,GAAG,CAAC,MAAM,GAAG,gBAAgB,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;IACxE,wCAAwC;IACxC,OAAO,iBAAiB,GAAG,MAAM,CAAC;AACpC,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * The functions and constants in this file allow us to interact\n * with the internal representation of an IEEE 64-bit floating point\n * number. We need to work with all 64-bits, thus, care needs to be\n * taken when working with Javascript's bitwise operators (<<, >>, &,\n * |, etc) as they truncate operands to 32-bits. In order to work around\n * this we work with the 64-bits as two 32-bit halves, perform bitwise\n * operations on them independently, and combine the results (if needed).\n */\n\nexport const SIGNIFICAND_WIDTH = 52;\n\n/**\n * EXPONENT_MASK is set to 1 for the hi 32-bits of an IEEE 754\n * floating point exponent: 0x7ff00000.\n */\nconst EXPONENT_MASK = 0x7ff00000;\n\n/**\n * SIGNIFICAND_MASK is the mask for the significand portion of the hi 32-bits\n * of an IEEE 754 double-precision floating-point value: 0xfffff\n */\nconst SIGNIFICAND_MASK = 0xfffff;\n\n/**\n * EXPONENT_BIAS is the exponent bias specified for encoding\n * the IEEE 754 double-precision floating point exponent: 1023\n */\nconst EXPONENT_BIAS = 1023;\n\n/**\n * MIN_NORMAL_EXPONENT is the minimum exponent of a normalized\n * floating point: -1022.\n */\nexport const MIN_NORMAL_EXPONENT = -EXPONENT_BIAS + 1;\n\n/**\n * MAX_NORMAL_EXPONENT is the maximum exponent of a normalized\n * floating point: 1023.\n */\nexport const MAX_NORMAL_EXPONENT = EXPONENT_BIAS;\n\n/**\n * MIN_VALUE is the smallest normal number\n */\nexport const MIN_VALUE = Math.pow(2, -1022);\n\n/**\n * getNormalBase2 extracts the normalized base-2 fractional exponent.\n * This returns k for the equation f x 2**k where f is\n * in the range [1, 2).  Note that this function is not called for\n * subnormal numbers.\n * @param {number} value - the value to determine normalized base-2 fractional\n *    exponent for\n * @returns {number} the normalized base-2 exponent\n */\nexport function getNormalBase2(value: number): number {\n  const dv = new DataView(new ArrayBuffer(8));\n  dv.setFloat64(0, value);\n  // access the raw 64-bit float as 32-bit uints\n  const hiBits = dv.getUint32(0);\n  const expBits = (hiBits & EXPONENT_MASK) >> 20;\n  return expBits - EXPONENT_BIAS;\n}\n\n/**\n * GetSignificand returns the 52 bit (unsigned) significand as a signed value.\n * @param {number} value - the floating point number to extract the significand from\n * @returns {number} The 52-bit significand\n */\nexport function getSignificand(value: number): number {\n  const dv = new DataView(new ArrayBuffer(8));\n  dv.setFloat64(0, value);\n  // access the raw 64-bit float as two 32-bit uints\n  const hiBits = dv.getUint32(0);\n  const loBits = dv.getUint32(4);\n  // extract the significand bits from the hi bits and left shift 32 places note:\n  // we can't use the native << operator as it will truncate the result to 32-bits\n  const significandHiBits = (hiBits & SIGNIFICAND_MASK) * Math.pow(2, 32);\n  // combine the hi and lo bits and return\n  return significandHiBits + loBits;\n}\n"]}