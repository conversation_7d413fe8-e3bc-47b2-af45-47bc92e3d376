"use strict";
// Copyright 2020 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     https://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//
// ** This file is automatically generated by synthtool. **
// ** https://github.com/googleapis/synthtool **
// ** All changes to this file may be overwritten. **
Object.defineProperty(exports, "__esModule", { value: true });
exports.helpers = exports.protos = exports.VertexRagDataServiceClient = exports.ReasoningEngineServiceClient = exports.ReasoningEngineExecutionServiceClient = exports.GenAiCacheServiceClient = exports.VertexRagServiceClient = exports.EvaluationServiceClient = exports.PersistentResourceServiceClient = exports.NotebookServiceClient = exports.GenAiTuningServiceClient = exports.DeploymentResourcePoolServiceClient = exports.LlmUtilityServiceClient = exports.FeatureRegistryServiceClient = exports.FeatureOnlineStoreServiceClient = exports.FeatureOnlineStoreAdminServiceClient = exports.ScheduleServiceClient = exports.ModelGardenServiceClient = exports.MatchServiceClient = exports.TensorboardServiceClient = exports.MetadataServiceClient = exports.FeaturestoreOnlineServingServiceClient = exports.FeaturestoreServiceClient = exports.VizierServiceClient = exports.IndexServiceClient = exports.IndexEndpointServiceClient = exports.SpecialistPoolServiceClient = exports.PredictionServiceClient = exports.PipelineServiceClient = exports.ModelServiceClient = exports.MigrationServiceClient = exports.JobServiceClient = exports.EndpointServiceClient = exports.DatasetServiceClient = exports.v1 = exports.v1beta1 = void 0;
const v1beta1 = require("./v1beta1");
exports.v1beta1 = v1beta1;
const v1 = require("./v1");
exports.v1 = v1;
const DatasetServiceClient = v1.DatasetServiceClient;
exports.DatasetServiceClient = DatasetServiceClient;
const EndpointServiceClient = v1.EndpointServiceClient;
exports.EndpointServiceClient = EndpointServiceClient;
const JobServiceClient = v1.JobServiceClient;
exports.JobServiceClient = JobServiceClient;
const MigrationServiceClient = v1.MigrationServiceClient;
exports.MigrationServiceClient = MigrationServiceClient;
const ModelServiceClient = v1.ModelServiceClient;
exports.ModelServiceClient = ModelServiceClient;
const PipelineServiceClient = v1.PipelineServiceClient;
exports.PipelineServiceClient = PipelineServiceClient;
const PredictionServiceClient = v1.PredictionServiceClient;
exports.PredictionServiceClient = PredictionServiceClient;
const SpecialistPoolServiceClient = v1.SpecialistPoolServiceClient;
exports.SpecialistPoolServiceClient = SpecialistPoolServiceClient;
const IndexEndpointServiceClient = v1.IndexEndpointServiceClient;
exports.IndexEndpointServiceClient = IndexEndpointServiceClient;
const IndexServiceClient = v1.IndexServiceClient;
exports.IndexServiceClient = IndexServiceClient;
const VizierServiceClient = v1.VizierServiceClient;
exports.VizierServiceClient = VizierServiceClient;
const FeaturestoreServiceClient = v1.FeaturestoreServiceClient;
exports.FeaturestoreServiceClient = FeaturestoreServiceClient;
const FeaturestoreOnlineServingServiceClient = v1.FeaturestoreOnlineServingServiceClient;
exports.FeaturestoreOnlineServingServiceClient = FeaturestoreOnlineServingServiceClient;
const MetadataServiceClient = v1.MetadataServiceClient;
exports.MetadataServiceClient = MetadataServiceClient;
const TensorboardServiceClient = v1.TensorboardServiceClient;
exports.TensorboardServiceClient = TensorboardServiceClient;
const MatchServiceClient = v1.MatchServiceClient;
exports.MatchServiceClient = MatchServiceClient;
const ModelGardenServiceClient = v1.ModelGardenServiceClient;
exports.ModelGardenServiceClient = ModelGardenServiceClient;
const ScheduleServiceClient = v1.ScheduleServiceClient;
exports.ScheduleServiceClient = ScheduleServiceClient;
const FeatureOnlineStoreAdminServiceClient = v1.FeatureOnlineStoreAdminServiceClient;
exports.FeatureOnlineStoreAdminServiceClient = FeatureOnlineStoreAdminServiceClient;
const FeatureOnlineStoreServiceClient = v1.FeatureOnlineStoreServiceClient;
exports.FeatureOnlineStoreServiceClient = FeatureOnlineStoreServiceClient;
const FeatureRegistryServiceClient = v1.FeatureRegistryServiceClient;
exports.FeatureRegistryServiceClient = FeatureRegistryServiceClient;
const LlmUtilityServiceClient = v1.LlmUtilityServiceClient;
exports.LlmUtilityServiceClient = LlmUtilityServiceClient;
const DeploymentResourcePoolServiceClient = v1.DeploymentResourcePoolServiceClient;
exports.DeploymentResourcePoolServiceClient = DeploymentResourcePoolServiceClient;
const GenAiTuningServiceClient = v1.GenAiTuningServiceClient;
exports.GenAiTuningServiceClient = GenAiTuningServiceClient;
const NotebookServiceClient = v1.NotebookServiceClient;
exports.NotebookServiceClient = NotebookServiceClient;
const PersistentResourceServiceClient = v1.PersistentResourceServiceClient;
exports.PersistentResourceServiceClient = PersistentResourceServiceClient;
const EvaluationServiceClient = v1.EvaluationServiceClient;
exports.EvaluationServiceClient = EvaluationServiceClient;
const GenAiCacheServiceClient = v1.GenAiCacheServiceClient;
exports.GenAiCacheServiceClient = GenAiCacheServiceClient;
const ReasoningEngineExecutionServiceClient = v1.ReasoningEngineExecutionServiceClient;
exports.ReasoningEngineExecutionServiceClient = ReasoningEngineExecutionServiceClient;
const ReasoningEngineServiceClient = v1.ReasoningEngineServiceClient;
exports.ReasoningEngineServiceClient = ReasoningEngineServiceClient;
const VertexRagServiceClient = v1.VertexRagServiceClient;
exports.VertexRagServiceClient = VertexRagServiceClient;
const VertexRagDataServiceClient = v1.VertexRagDataServiceClient;
exports.VertexRagDataServiceClient = VertexRagDataServiceClient;
exports.default = {
    v1beta1,
    v1,
    DatasetServiceClient,
    EndpointServiceClient,
    JobServiceClient,
    MigrationServiceClient,
    ModelServiceClient,
    PipelineServiceClient,
    PredictionServiceClient,
    SpecialistPoolServiceClient,
    IndexEndpointServiceClient,
    IndexServiceClient,
    VizierServiceClient,
    FeaturestoreServiceClient,
    FeaturestoreOnlineServingServiceClient,
    MetadataServiceClient,
    TensorboardServiceClient,
    MatchServiceClient,
    ModelGardenServiceClient,
    ScheduleServiceClient,
    FeatureOnlineStoreAdminServiceClient,
    FeatureOnlineStoreServiceClient,
    FeatureRegistryServiceClient,
    LlmUtilityServiceClient,
    DeploymentResourcePoolServiceClient,
    GenAiTuningServiceClient,
    NotebookServiceClient,
    PersistentResourceServiceClient,
    EvaluationServiceClient,
    GenAiCacheServiceClient,
    ReasoningEngineExecutionServiceClient,
    VertexRagServiceClient,
    ReasoningEngineServiceClient,
    VertexRagDataServiceClient,
};
const protos = require("../protos/protos");
exports.protos = protos;
const helpers_1 = require("./helpers");
const helpers = { toValue: helpers_1.toValue, fromValue: helpers_1.fromValue };
exports.helpers = helpers;
const decorator_1 = require("./decorator");
(0, decorator_1._enhance)('v1beta1');
(0, decorator_1._enhance)('v1');
//# sourceMappingURL=index.js.map