// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.aiplatform.v1beta1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/aiplatform/v1beta1/operation.proto";
import "google/cloud/aiplatform/v1beta1/persistent_resource.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";

option csharp_namespace = "Google.Cloud.AIPlatform.V1Beta1";
option go_package = "cloud.google.com/go/aiplatform/apiv1beta1/aiplatformpb;aiplatformpb";
option java_multiple_files = true;
option java_outer_classname = "PersistentResourceServiceProto";
option java_package = "com.google.cloud.aiplatform.v1beta1";
option php_namespace = "Google\\Cloud\\AIPlatform\\V1beta1";
option ruby_package = "Google::Cloud::AIPlatform::V1beta1";

// A service for managing Vertex AI's machine learning PersistentResource.
service PersistentResourceService {
  option (google.api.default_host) = "aiplatform.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Creates a PersistentResource.
  rpc CreatePersistentResource(CreatePersistentResourceRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1beta1/{parent=projects/*/locations/*}/persistentResources"
      body: "persistent_resource"
    };
    option (google.api.method_signature) =
        "parent,persistent_resource,persistent_resource_id";
    option (google.longrunning.operation_info) = {
      response_type: "PersistentResource"
      metadata_type: "CreatePersistentResourceOperationMetadata"
    };
  }

  // Gets a PersistentResource.
  rpc GetPersistentResource(GetPersistentResourceRequest)
      returns (PersistentResource) {
    option (google.api.http) = {
      get: "/v1beta1/{name=projects/*/locations/*/persistentResources/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists PersistentResources in a Location.
  rpc ListPersistentResources(ListPersistentResourcesRequest)
      returns (ListPersistentResourcesResponse) {
    option (google.api.http) = {
      get: "/v1beta1/{parent=projects/*/locations/*}/persistentResources"
    };
    option (google.api.method_signature) = "parent";
  }

  // Deletes a PersistentResource.
  rpc DeletePersistentResource(DeletePersistentResourceRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1beta1/{name=projects/*/locations/*/persistentResources/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "DeleteOperationMetadata"
    };
  }

  // Updates a PersistentResource.
  rpc UpdatePersistentResource(UpdatePersistentResourceRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1beta1/{persistent_resource.name=projects/*/locations/*/persistentResources/*}"
      body: "persistent_resource"
    };
    option (google.api.method_signature) = "persistent_resource,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "PersistentResource"
      metadata_type: "UpdatePersistentResourceOperationMetadata"
    };
  }

  // Reboots a PersistentResource.
  rpc RebootPersistentResource(RebootPersistentResourceRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1beta1/{name=projects/*/locations/*/persistentResources/*}:reboot"
      body: "*"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "PersistentResource"
      metadata_type: "RebootPersistentResourceOperationMetadata"
    };
  }
}

// Request message for
// [PersistentResourceService.CreatePersistentResource][google.cloud.aiplatform.v1beta1.PersistentResourceService.CreatePersistentResource].
message CreatePersistentResourceRequest {
  // Required. The resource name of the Location to create the
  // PersistentResource in. Format: `projects/{project}/locations/{location}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Required. The PersistentResource to create.
  PersistentResource persistent_resource = 2
      [(google.api.field_behavior) = REQUIRED];

  // Required. The ID to use for the PersistentResource, which become the final
  // component of the PersistentResource's resource name.
  //
  // The maximum length is 63 characters, and valid characters
  // are `/^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$/`.
  string persistent_resource_id = 3 [(google.api.field_behavior) = REQUIRED];
}

// Details of operations that perform create PersistentResource.
message CreatePersistentResourceOperationMetadata {
  // Operation metadata for PersistentResource.
  GenericOperationMetadata generic_metadata = 1;

  // Progress Message for Create LRO
  string progress_message = 2;
}

// Details of operations that perform update PersistentResource.
message UpdatePersistentResourceOperationMetadata {
  // Operation metadata for PersistentResource.
  GenericOperationMetadata generic_metadata = 1;

  // Progress Message for Update LRO
  string progress_message = 2;
}

// Details of operations that perform reboot PersistentResource.
message RebootPersistentResourceOperationMetadata {
  // Operation metadata for PersistentResource.
  GenericOperationMetadata generic_metadata = 1;

  // Progress Message for Reboot LRO
  string progress_message = 2;
}

// Request message for
// [PersistentResourceService.GetPersistentResource][google.cloud.aiplatform.v1beta1.PersistentResourceService.GetPersistentResource].
message GetPersistentResourceRequest {
  // Required. The name of the PersistentResource resource.
  // Format:
  // `projects/{project_id_or_number}/locations/{location_id}/persistentResources/{persistent_resource_id}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/PersistentResource"
    }
  ];
}

// Request message for [PersistentResourceService.ListPersistentResource][].
message ListPersistentResourcesRequest {
  // Required. The resource name of the Location to list the PersistentResources
  // from. Format: `projects/{project}/locations/{location}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Optional. The standard list page size.
  int32 page_size = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The standard list page token.
  // Typically obtained via
  // [ListPersistentResourceResponse.next_page_token][] of the previous
  // [PersistentResourceService.ListPersistentResource][] call.
  string page_token = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for
// [PersistentResourceService.ListPersistentResources][google.cloud.aiplatform.v1beta1.PersistentResourceService.ListPersistentResources]
message ListPersistentResourcesResponse {
  repeated PersistentResource persistent_resources = 1;

  // A token to retrieve next page of results.
  // Pass to
  // [ListPersistentResourcesRequest.page_token][google.cloud.aiplatform.v1beta1.ListPersistentResourcesRequest.page_token]
  // to obtain that page.
  string next_page_token = 2;
}

// Request message for
// [PersistentResourceService.DeletePersistentResource][google.cloud.aiplatform.v1beta1.PersistentResourceService.DeletePersistentResource].
message DeletePersistentResourceRequest {
  // Required. The name of the PersistentResource to be deleted.
  // Format:
  // `projects/{project}/locations/{location}/persistentResources/{persistent_resource}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/PersistentResource"
    }
  ];
}

// Request message for UpdatePersistentResource method.
message UpdatePersistentResourceRequest {
  // Required. The PersistentResource to update.
  //
  // The PersistentResource's `name` field is used to identify the
  // PersistentResource to update. Format:
  // `projects/{project}/locations/{location}/persistentResources/{persistent_resource}`
  PersistentResource persistent_resource = 1
      [(google.api.field_behavior) = REQUIRED];

  // Required. Specify the fields to be overwritten in the PersistentResource by
  // the update method.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Request message for
// [PersistentResourceService.RebootPersistentResource][google.cloud.aiplatform.v1beta1.PersistentResourceService.RebootPersistentResource].
message RebootPersistentResourceRequest {
  // Required. The name of the PersistentResource resource.
  // Format:
  // `projects/{project_id_or_number}/locations/{location_id}/persistentResources/{persistent_resource_id}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/PersistentResource"
    }
  ];
}
