{"version": 3, "file": "AlignedHistogramBucketExemplarReservoir.js", "sourceRoot": "", "sources": ["../../../src/exemplar/AlignedHistogramBucketExemplarReservoir.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAGH,OAAO,EAAE,8BAA8B,EAAE,MAAM,qBAAqB,CAAC;AAErE;;;;GAIG;AACH,MAAM,OAAO,uCAAwC,SAAQ,8BAA8B;IAEzF,YAAY,UAAoB;QAC9B,KAAK,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QAC7B,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC;IAChC,CAAC;IAEO,gBAAgB,CACtB,KAAa,EACb,UAAkB,EAClB,WAA6B,EAC7B,IAAa;QAEb,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAChD,IAAI,KAAK,IAAI,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE;gBAChC,OAAO,CAAC,CAAC;aACV;SACF;QACD,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;IACjC,CAAC;IAED,KAAK,CACH,KAAa,EACb,SAAiB,EACjB,UAA4B,EAC5B,GAAY;QAEZ,MAAM,KAAK,GAAG,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,SAAS,EAAE,UAAU,EAAE,GAAG,CAAC,CAAC;QACvE,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,KAAK,EAAE,SAAS,EAAE,UAAU,EAAE,GAAG,CAAC,CAAC;IACzE,CAAC;CACF", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Context, HrTime, MetricAttributes } from '@opentelemetry/api';\nimport { FixedSizeExemplarReservoirBase } from './ExemplarReservoir';\n\n/**\n * AlignedHistogramBucketExemplarReservoir takes the same boundaries\n * configuration of a Histogram. This algorithm keeps the last seen measurement\n * that falls within a histogram bucket.\n */\nexport class AlignedHistogramBucketExemplarReservoir extends FixedSizeExemplarReservoirBase {\n  private _boundaries: number[];\n  constructor(boundaries: number[]) {\n    super(boundaries.length + 1);\n    this._boundaries = boundaries;\n  }\n\n  private _findBucketIndex(\n    value: number,\n    _timestamp: HrTime,\n    _attributes: MetricAttributes,\n    _ctx: Context\n  ) {\n    for (let i = 0; i < this._boundaries.length; i++) {\n      if (value <= this._boundaries[i]) {\n        return i;\n      }\n    }\n    return this._boundaries.length;\n  }\n\n  offer(\n    value: number,\n    timestamp: HrTime,\n    attributes: MetricAttributes,\n    ctx: Context\n  ): void {\n    const index = this._findBucketIndex(value, timestamp, attributes, ctx);\n    this._reservoirStorage[index].offer(value, timestamp, attributes, ctx);\n  }\n}\n"]}