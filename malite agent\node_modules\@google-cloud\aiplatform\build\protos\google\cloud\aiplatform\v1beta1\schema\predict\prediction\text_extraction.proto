// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.aiplatform.v1beta1.schema.predict.prediction;


option csharp_namespace = "Google.Cloud.AIPlatform.V1Beta1.Schema.Predict.Prediction";
option go_package = "cloud.google.com/go/aiplatform/apiv1beta1/schema/predict/prediction/predictionpb;predictionpb";
option java_multiple_files = true;
option java_outer_classname = "TextExtractionPredictionResultProto";
option java_package = "com.google.cloud.aiplatform.v1beta1.schema.predict.prediction";
option php_namespace = "Google\\Cloud\\AIPlatform\\V1beta1\\Schema\\Predict\\Prediction";
option ruby_package = "Google::Cloud::AIPlatform::V1beta1::Schema::Predict::Prediction";

// Prediction output format for Text Extraction.
message TextExtractionPredictionResult {
  // The resource IDs of the AnnotationSpecs that had been identified,
  // ordered by the confidence score descendingly.
  repeated int64 ids = 1;

  // The display names of the AnnotationSpecs that had been identified,
  // order matches the IDs.
  repeated string display_names = 2;

  // The start offsets, inclusive, of the text segment in which the
  // AnnotationSpec has been identified. Expressed as a zero-based number
  // of characters as measured from the start of the text snippet.
  repeated int64 text_segment_start_offsets = 3;

  // The end offsets, inclusive, of the text segment in which the
  // AnnotationSpec has been identified. Expressed as a zero-based number
  // of characters as measured from the start of the text snippet.
  repeated int64 text_segment_end_offsets = 4;

  // The Model's confidences in correctness of the predicted IDs, higher
  // value means higher confidence. Order matches the Ids.
  repeated float confidences = 5;
}
