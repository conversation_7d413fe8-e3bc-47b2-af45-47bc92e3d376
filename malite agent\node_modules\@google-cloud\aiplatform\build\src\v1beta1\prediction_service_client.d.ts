import type * as gax from 'google-gax';
import type { Callback, CallOptions, Descriptors, ClientOptions, IamClient, IamProtos, LocationsClient, LocationProtos } from 'google-gax';
import * as protos from '../../protos/protos';
/**
 *  A service for online predictions and explanations.
 * @class
 * @memberof v1beta1
 */
export declare class PredictionServiceClient {
    private _terminated;
    private _opts;
    private _providedCustomServicePath;
    private _gaxModule;
    private _gaxGrpc;
    private _protos;
    private _defaults;
    private _universeDomain;
    private _servicePath;
    auth: gax.GoogleAuth;
    descriptors: Descriptors;
    warn: (code: string, message: string, warnType?: string) => void;
    innerApiCalls: {
        [name: string]: Function;
    };
    iamClient: IamClient;
    locationsClient: LocationsClient;
    pathTemplates: {
        [name: string]: gax.PathTemplate;
    };
    predictionServiceStub?: Promise<{
        [name: string]: Function;
    }>;
    /**
     * Construct an instance of PredictionServiceClient.
     *
     * @param {object} [options] - The configuration object.
     * The options accepted by the constructor are described in detail
     * in [this document](https://github.com/googleapis/gax-nodejs/blob/main/client-libraries.md#creating-the-client-instance).
     * The common options are:
     * @param {object} [options.credentials] - Credentials object.
     * @param {string} [options.credentials.client_email]
     * @param {string} [options.credentials.private_key]
     * @param {string} [options.email] - Account email address. Required when
     *     using a .pem or .p12 keyFilename.
     * @param {string} [options.keyFilename] - Full path to the a .json, .pem, or
     *     .p12 key downloaded from the Google Developers Console. If you provide
     *     a path to a JSON file, the projectId option below is not necessary.
     *     NOTE: .pem and .p12 require you to specify options.email as well.
     * @param {number} [options.port] - The port on which to connect to
     *     the remote host.
     * @param {string} [options.projectId] - The project ID from the Google
     *     Developer's Console, e.g. 'grape-spaceship-123'. We will also check
     *     the environment variable GCLOUD_PROJECT for your project ID. If your
     *     app is running in an environment which supports
     *     {@link https://developers.google.com/identity/protocols/application-default-credentials Application Default Credentials},
     *     your project ID will be detected automatically.
     * @param {string} [options.apiEndpoint] - The domain name of the
     *     API remote host.
     * @param {gax.ClientConfig} [options.clientConfig] - Client configuration override.
     *     Follows the structure of {@link gapicConfig}.
     * @param {boolean} [options.fallback] - Use HTTP/1.1 REST mode.
     *     For more information, please check the
     *     {@link https://github.com/googleapis/gax-nodejs/blob/main/client-libraries.md#http11-rest-api-mode documentation}.
     * @param {gax} [gaxInstance]: loaded instance of `google-gax`. Useful if you
     *     need to avoid loading the default gRPC version and want to use the fallback
     *     HTTP implementation. Load only fallback version and pass it to the constructor:
     *     ```
     *     const gax = require('google-gax/build/src/fallback'); // avoids loading google-gax with gRPC
     *     const client = new PredictionServiceClient({fallback: true}, gax);
     *     ```
     */
    constructor(opts?: ClientOptions, gaxInstance?: typeof gax | typeof gax.fallback);
    /**
     * Initialize the client.
     * Performs asynchronous operations (such as authentication) and prepares the client.
     * This function will be called automatically when any class method is called for the
     * first time, but if you need to initialize it before calling an actual method,
     * feel free to call initialize() directly.
     *
     * You can await on this method if you want to make sure the client is initialized.
     *
     * @returns {Promise} A promise that resolves to an authenticated service stub.
     */
    initialize(): Promise<{
        [name: string]: Function;
    }>;
    /**
     * The DNS address for this API service.
     * @deprecated Use the apiEndpoint method of the client instance.
     * @returns {string} The DNS address for this service.
     */
    static get servicePath(): string;
    /**
     * The DNS address for this API service - same as servicePath.
     * @deprecated Use the apiEndpoint method of the client instance.
     * @returns {string} The DNS address for this service.
     */
    static get apiEndpoint(): string;
    /**
     * The DNS address for this API service.
     * @returns {string} The DNS address for this service.
     */
    get apiEndpoint(): string;
    get universeDomain(): string;
    /**
     * The port for this API service.
     * @returns {number} The default port for this service.
     */
    static get port(): number;
    /**
     * The scopes needed to make gRPC calls for every method defined
     * in this service.
     * @returns {string[]} List of default scopes.
     */
    static get scopes(): string[];
    getProjectId(): Promise<string>;
    getProjectId(callback: Callback<string, undefined, undefined>): void;
    /**
     * Perform an online prediction.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.endpoint
     *   Required. The name of the Endpoint requested to serve the prediction.
     *   Format:
     *   `projects/{project}/locations/{location}/endpoints/{endpoint}`
     * @param {number[]} request.instances
     *   Required. The instances that are the input to the prediction call.
     *   A DeployedModel may have an upper limit on the number of instances it
     *   supports per request, and when it is exceeded the prediction call errors
     *   in case of AutoML Models, or, in case of customer created Models, the
     *   behaviour is as documented by that Model.
     *   The schema of any single instance may be specified via Endpoint's
     *   DeployedModels'
     *   {@link protos.google.cloud.aiplatform.v1beta1.DeployedModel.model|Model's}
     *   {@link protos.google.cloud.aiplatform.v1beta1.Model.predict_schemata|PredictSchemata's}
     *   {@link protos.google.cloud.aiplatform.v1beta1.PredictSchemata.instance_schema_uri|instance_schema_uri}.
     * @param {google.protobuf.Value} request.parameters
     *   The parameters that govern the prediction. The schema of the parameters may
     *   be specified via Endpoint's DeployedModels' [Model's
     *   ][google.cloud.aiplatform.v1beta1.DeployedModel.model]
     *   {@link protos.google.cloud.aiplatform.v1beta1.Model.predict_schemata|PredictSchemata's}
     *   {@link protos.google.cloud.aiplatform.v1beta1.PredictSchemata.parameters_schema_uri|parameters_schema_uri}.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing {@link protos.google.cloud.aiplatform.v1beta1.PredictResponse|PredictResponse}.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#regular-methods | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1beta1/prediction_service.predict.js</caption>
     * region_tag:aiplatform_v1beta1_generated_PredictionService_Predict_async
     */
    predict(request?: protos.google.cloud.aiplatform.v1beta1.IPredictRequest, options?: CallOptions): Promise<[
        protos.google.cloud.aiplatform.v1beta1.IPredictResponse,
        protos.google.cloud.aiplatform.v1beta1.IPredictRequest | undefined,
        {} | undefined
    ]>;
    predict(request: protos.google.cloud.aiplatform.v1beta1.IPredictRequest, options: CallOptions, callback: Callback<protos.google.cloud.aiplatform.v1beta1.IPredictResponse, protos.google.cloud.aiplatform.v1beta1.IPredictRequest | null | undefined, {} | null | undefined>): void;
    predict(request: protos.google.cloud.aiplatform.v1beta1.IPredictRequest, callback: Callback<protos.google.cloud.aiplatform.v1beta1.IPredictResponse, protos.google.cloud.aiplatform.v1beta1.IPredictRequest | null | undefined, {} | null | undefined>): void;
    /**
     * Perform an online prediction with an arbitrary HTTP payload.
     *
     * The response includes the following HTTP headers:
     *
     * * `X-Vertex-AI-Endpoint-Id`: ID of the
     * {@link protos.google.cloud.aiplatform.v1beta1.Endpoint|Endpoint} that served this
     * prediction.
     *
     * * `X-Vertex-AI-Deployed-Model-Id`: ID of the Endpoint's
     * {@link protos.google.cloud.aiplatform.v1beta1.DeployedModel|DeployedModel} that served
     * this prediction.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.endpoint
     *   Required. The name of the Endpoint requested to serve the prediction.
     *   Format:
     *   `projects/{project}/locations/{location}/endpoints/{endpoint}`
     * @param {google.api.HttpBody} request.httpBody
     *   The prediction input. Supports HTTP headers and arbitrary data payload.
     *
     *   A {@link protos.google.cloud.aiplatform.v1beta1.DeployedModel|DeployedModel} may have
     *   an upper limit on the number of instances it supports per request. When
     *   this limit it is exceeded for an AutoML model, the
     *   {@link protos.google.cloud.aiplatform.v1beta1.PredictionService.RawPredict|RawPredict}
     *   method returns an error. When this limit is exceeded for a custom-trained
     *   model, the behavior varies depending on the model.
     *
     *   You can specify the schema for each instance in the
     *   {@link protos.google.cloud.aiplatform.v1beta1.PredictSchemata.instance_schema_uri|predict_schemata.instance_schema_uri}
     *   field when you create a {@link protos.google.cloud.aiplatform.v1beta1.Model|Model}.
     *   This schema applies when you deploy the `Model` as a `DeployedModel` to an
     *   {@link protos.google.cloud.aiplatform.v1beta1.Endpoint|Endpoint} and use the
     *   `RawPredict` method.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing {@link protos.google.api.HttpBody|HttpBody}.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#regular-methods | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1beta1/prediction_service.raw_predict.js</caption>
     * region_tag:aiplatform_v1beta1_generated_PredictionService_RawPredict_async
     */
    rawPredict(request?: protos.google.cloud.aiplatform.v1beta1.IRawPredictRequest, options?: CallOptions): Promise<[
        protos.google.api.IHttpBody,
        protos.google.cloud.aiplatform.v1beta1.IRawPredictRequest | undefined,
        {} | undefined
    ]>;
    rawPredict(request: protos.google.cloud.aiplatform.v1beta1.IRawPredictRequest, options: CallOptions, callback: Callback<protos.google.api.IHttpBody, protos.google.cloud.aiplatform.v1beta1.IRawPredictRequest | null | undefined, {} | null | undefined>): void;
    rawPredict(request: protos.google.cloud.aiplatform.v1beta1.IRawPredictRequest, callback: Callback<protos.google.api.IHttpBody, protos.google.cloud.aiplatform.v1beta1.IRawPredictRequest | null | undefined, {} | null | undefined>): void;
    /**
     * Perform an unary online prediction request to a gRPC model server for
     * Vertex first-party products and frameworks.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.endpoint
     *   Required. The name of the Endpoint requested to serve the prediction.
     *   Format:
     *   `projects/{project}/locations/{location}/endpoints/{endpoint}`
     * @param {number[]} request.inputs
     *   The prediction input.
     * @param {google.cloud.aiplatform.v1beta1.Tensor} request.parameters
     *   The parameters that govern the prediction.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing {@link protos.google.cloud.aiplatform.v1beta1.DirectPredictResponse|DirectPredictResponse}.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#regular-methods | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1beta1/prediction_service.direct_predict.js</caption>
     * region_tag:aiplatform_v1beta1_generated_PredictionService_DirectPredict_async
     */
    directPredict(request?: protos.google.cloud.aiplatform.v1beta1.IDirectPredictRequest, options?: CallOptions): Promise<[
        protos.google.cloud.aiplatform.v1beta1.IDirectPredictResponse,
        protos.google.cloud.aiplatform.v1beta1.IDirectPredictRequest | undefined,
        {} | undefined
    ]>;
    directPredict(request: protos.google.cloud.aiplatform.v1beta1.IDirectPredictRequest, options: CallOptions, callback: Callback<protos.google.cloud.aiplatform.v1beta1.IDirectPredictResponse, protos.google.cloud.aiplatform.v1beta1.IDirectPredictRequest | null | undefined, {} | null | undefined>): void;
    directPredict(request: protos.google.cloud.aiplatform.v1beta1.IDirectPredictRequest, callback: Callback<protos.google.cloud.aiplatform.v1beta1.IDirectPredictResponse, protos.google.cloud.aiplatform.v1beta1.IDirectPredictRequest | null | undefined, {} | null | undefined>): void;
    /**
     * Perform an unary online prediction request to a gRPC model server for
     * custom containers.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.endpoint
     *   Required. The name of the Endpoint requested to serve the prediction.
     *   Format:
     *   `projects/{project}/locations/{location}/endpoints/{endpoint}`
     * @param {string} request.methodName
     *   Fully qualified name of the API method being invoked to perform
     *   predictions.
     *
     *   Format:
     *   `/namespace.Service/Method/`
     *   Example:
     *   `/tensorflow.serving.PredictionService/Predict`
     * @param {Buffer} request.input
     *   The prediction input.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing {@link protos.google.cloud.aiplatform.v1beta1.DirectRawPredictResponse|DirectRawPredictResponse}.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#regular-methods | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1beta1/prediction_service.direct_raw_predict.js</caption>
     * region_tag:aiplatform_v1beta1_generated_PredictionService_DirectRawPredict_async
     */
    directRawPredict(request?: protos.google.cloud.aiplatform.v1beta1.IDirectRawPredictRequest, options?: CallOptions): Promise<[
        protos.google.cloud.aiplatform.v1beta1.IDirectRawPredictResponse,
        (protos.google.cloud.aiplatform.v1beta1.IDirectRawPredictRequest | undefined),
        {} | undefined
    ]>;
    directRawPredict(request: protos.google.cloud.aiplatform.v1beta1.IDirectRawPredictRequest, options: CallOptions, callback: Callback<protos.google.cloud.aiplatform.v1beta1.IDirectRawPredictResponse, protos.google.cloud.aiplatform.v1beta1.IDirectRawPredictRequest | null | undefined, {} | null | undefined>): void;
    directRawPredict(request: protos.google.cloud.aiplatform.v1beta1.IDirectRawPredictRequest, callback: Callback<protos.google.cloud.aiplatform.v1beta1.IDirectRawPredictResponse, protos.google.cloud.aiplatform.v1beta1.IDirectRawPredictRequest | null | undefined, {} | null | undefined>): void;
    /**
     * Perform an online explanation.
     *
     * If
     * {@link protos.google.cloud.aiplatform.v1beta1.ExplainRequest.deployed_model_id|deployed_model_id}
     * is specified, the corresponding DeployModel must have
     * {@link protos.google.cloud.aiplatform.v1beta1.DeployedModel.explanation_spec|explanation_spec}
     * populated. If
     * {@link protos.google.cloud.aiplatform.v1beta1.ExplainRequest.deployed_model_id|deployed_model_id}
     * is not specified, all DeployedModels must have
     * {@link protos.google.cloud.aiplatform.v1beta1.DeployedModel.explanation_spec|explanation_spec}
     * populated.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.endpoint
     *   Required. The name of the Endpoint requested to serve the explanation.
     *   Format:
     *   `projects/{project}/locations/{location}/endpoints/{endpoint}`
     * @param {number[]} request.instances
     *   Required. The instances that are the input to the explanation call.
     *   A DeployedModel may have an upper limit on the number of instances it
     *   supports per request, and when it is exceeded the explanation call errors
     *   in case of AutoML Models, or, in case of customer created Models, the
     *   behaviour is as documented by that Model.
     *   The schema of any single instance may be specified via Endpoint's
     *   DeployedModels'
     *   {@link protos.google.cloud.aiplatform.v1beta1.DeployedModel.model|Model's}
     *   {@link protos.google.cloud.aiplatform.v1beta1.Model.predict_schemata|PredictSchemata's}
     *   {@link protos.google.cloud.aiplatform.v1beta1.PredictSchemata.instance_schema_uri|instance_schema_uri}.
     * @param {google.protobuf.Value} request.parameters
     *   The parameters that govern the prediction. The schema of the parameters may
     *   be specified via Endpoint's DeployedModels' [Model's
     *   ][google.cloud.aiplatform.v1beta1.DeployedModel.model]
     *   {@link protos.google.cloud.aiplatform.v1beta1.Model.predict_schemata|PredictSchemata's}
     *   {@link protos.google.cloud.aiplatform.v1beta1.PredictSchemata.parameters_schema_uri|parameters_schema_uri}.
     * @param {google.cloud.aiplatform.v1beta1.ExplanationSpecOverride} request.explanationSpecOverride
     *   If specified, overrides the
     *   {@link protos.google.cloud.aiplatform.v1beta1.DeployedModel.explanation_spec|explanation_spec}
     *   of the DeployedModel. Can be used for explaining prediction results with
     *   different configurations, such as:
     *    - Explaining top-5 predictions results as opposed to top-1;
     *    - Increasing path count or step count of the attribution methods to reduce
     *      approximate errors;
     *    - Using different baselines for explaining the prediction results.
     * @param {number[]} [request.concurrentExplanationSpecOverride]
     *   Optional. This field is the same as the one above, but supports multiple
     *   explanations to occur in parallel. The key can be any string. Each override
     *   will be run against the model, then its explanations will be grouped
     *   together.
     *
     *   Note - these explanations are run **In Addition** to the default
     *   Explanation in the deployed model.
     * @param {string} request.deployedModelId
     *   If specified, this ExplainRequest will be served by the chosen
     *   DeployedModel, overriding
     *   {@link protos.google.cloud.aiplatform.v1beta1.Endpoint.traffic_split|Endpoint.traffic_split}.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing {@link protos.google.cloud.aiplatform.v1beta1.ExplainResponse|ExplainResponse}.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#regular-methods | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1beta1/prediction_service.explain.js</caption>
     * region_tag:aiplatform_v1beta1_generated_PredictionService_Explain_async
     */
    explain(request?: protos.google.cloud.aiplatform.v1beta1.IExplainRequest, options?: CallOptions): Promise<[
        protos.google.cloud.aiplatform.v1beta1.IExplainResponse,
        protos.google.cloud.aiplatform.v1beta1.IExplainRequest | undefined,
        {} | undefined
    ]>;
    explain(request: protos.google.cloud.aiplatform.v1beta1.IExplainRequest, options: CallOptions, callback: Callback<protos.google.cloud.aiplatform.v1beta1.IExplainResponse, protos.google.cloud.aiplatform.v1beta1.IExplainRequest | null | undefined, {} | null | undefined>): void;
    explain(request: protos.google.cloud.aiplatform.v1beta1.IExplainRequest, callback: Callback<protos.google.cloud.aiplatform.v1beta1.IExplainResponse, protos.google.cloud.aiplatform.v1beta1.IExplainRequest | null | undefined, {} | null | undefined>): void;
    /**
     * Perform a token counting.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.endpoint
     *   Required. The name of the Endpoint requested to perform token counting.
     *   Format:
     *   `projects/{project}/locations/{location}/endpoints/{endpoint}`
     * @param {string} [request.model]
     *   Optional. The name of the publisher model requested to serve the
     *   prediction. Format:
     *   `projects/{project}/locations/{location}/publishers/* /models/*`
     * @param {number[]} [request.instances]
     *   Optional. The instances that are the input to token counting call.
     *   Schema is identical to the prediction schema of the underlying model.
     * @param {number[]} [request.contents]
     *   Optional. Input content.
     * @param {google.cloud.aiplatform.v1beta1.Content} [request.systemInstruction]
     *   Optional. The user provided system instructions for the model.
     *   Note: only text should be used in parts and content in each part will be in
     *   a separate paragraph.
     * @param {number[]} [request.tools]
     *   Optional. A list of `Tools` the model may use to generate the next
     *   response.
     *
     *   A `Tool` is a piece of code that enables the system to interact with
     *   external systems to perform an action, or set of actions, outside of
     *   knowledge and scope of the model.
     * @param {google.cloud.aiplatform.v1beta1.GenerationConfig} [request.generationConfig]
     *   Optional. Generation config that the model will use to generate the
     *   response.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing {@link protos.google.cloud.aiplatform.v1beta1.CountTokensResponse|CountTokensResponse}.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#regular-methods | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1beta1/prediction_service.count_tokens.js</caption>
     * region_tag:aiplatform_v1beta1_generated_PredictionService_CountTokens_async
     */
    countTokens(request?: protos.google.cloud.aiplatform.v1beta1.ICountTokensRequest, options?: CallOptions): Promise<[
        protos.google.cloud.aiplatform.v1beta1.ICountTokensResponse,
        protos.google.cloud.aiplatform.v1beta1.ICountTokensRequest | undefined,
        {} | undefined
    ]>;
    countTokens(request: protos.google.cloud.aiplatform.v1beta1.ICountTokensRequest, options: CallOptions, callback: Callback<protos.google.cloud.aiplatform.v1beta1.ICountTokensResponse, protos.google.cloud.aiplatform.v1beta1.ICountTokensRequest | null | undefined, {} | null | undefined>): void;
    countTokens(request: protos.google.cloud.aiplatform.v1beta1.ICountTokensRequest, callback: Callback<protos.google.cloud.aiplatform.v1beta1.ICountTokensResponse, protos.google.cloud.aiplatform.v1beta1.ICountTokensRequest | null | undefined, {} | null | undefined>): void;
    /**
     * Generate content with multimodal inputs.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.model
     *   Required. The fully qualified name of the publisher model or tuned model
     *   endpoint to use.
     *
     *   Publisher model format:
     *   `projects/{project}/locations/{location}/publishers/* /models/*`
     *
     *   Tuned model endpoint format:
     *   `projects/{project}/locations/{location}/endpoints/{endpoint}`
     * @param {number[]} request.contents
     *   Required. The content of the current conversation with the model.
     *
     *   For single-turn queries, this is a single instance. For multi-turn queries,
     *   this is a repeated field that contains conversation history + latest
     *   request.
     * @param {google.cloud.aiplatform.v1beta1.Content} [request.systemInstruction]
     *   Optional. The user provided system instructions for the model.
     *   Note: only text should be used in parts and content in each part will be in
     *   a separate paragraph.
     * @param {string} [request.cachedContent]
     *   Optional. The name of the cached content used as context to serve the
     *   prediction. Note: only used in explicit caching, where users can have
     *   control over caching (e.g. what content to cache) and enjoy guaranteed cost
     *   savings. Format:
     *   `projects/{project}/locations/{location}/cachedContents/{cachedContent}`
     * @param {number[]} [request.tools]
     *   Optional. A list of `Tools` the model may use to generate the next
     *   response.
     *
     *   A `Tool` is a piece of code that enables the system to interact with
     *   external systems to perform an action, or set of actions, outside of
     *   knowledge and scope of the model.
     * @param {google.cloud.aiplatform.v1beta1.ToolConfig} [request.toolConfig]
     *   Optional. Tool config. This config is shared for all tools provided in the
     *   request.
     * @param {number[]} [request.labels]
     *   Optional. The labels with user-defined metadata for the request. It is used
     *   for billing and reporting only.
     *
     *   Label keys and values can be no longer than 63 characters
     *   (Unicode codepoints) and can only contain lowercase letters, numeric
     *   characters, underscores, and dashes. International characters are allowed.
     *   Label values are optional. Label keys must start with a letter.
     * @param {number[]} [request.safetySettings]
     *   Optional. Per request settings for blocking unsafe content.
     *   Enforced on GenerateContentResponse.candidates.
     * @param {google.cloud.aiplatform.v1beta1.GenerationConfig} [request.generationConfig]
     *   Optional. Generation config.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing {@link protos.google.cloud.aiplatform.v1beta1.GenerateContentResponse|GenerateContentResponse}.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#regular-methods | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1beta1/prediction_service.generate_content.js</caption>
     * region_tag:aiplatform_v1beta1_generated_PredictionService_GenerateContent_async
     */
    generateContent(request?: protos.google.cloud.aiplatform.v1beta1.IGenerateContentRequest, options?: CallOptions): Promise<[
        protos.google.cloud.aiplatform.v1beta1.IGenerateContentResponse,
        (protos.google.cloud.aiplatform.v1beta1.IGenerateContentRequest | undefined),
        {} | undefined
    ]>;
    generateContent(request: protos.google.cloud.aiplatform.v1beta1.IGenerateContentRequest, options: CallOptions, callback: Callback<protos.google.cloud.aiplatform.v1beta1.IGenerateContentResponse, protos.google.cloud.aiplatform.v1beta1.IGenerateContentRequest | null | undefined, {} | null | undefined>): void;
    generateContent(request: protos.google.cloud.aiplatform.v1beta1.IGenerateContentRequest, callback: Callback<protos.google.cloud.aiplatform.v1beta1.IGenerateContentResponse, protos.google.cloud.aiplatform.v1beta1.IGenerateContentRequest | null | undefined, {} | null | undefined>): void;
    /**
     * Perform a streaming online prediction with an arbitrary HTTP payload.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.endpoint
     *   Required. The name of the Endpoint requested to serve the prediction.
     *   Format:
     *   `projects/{project}/locations/{location}/endpoints/{endpoint}`
     * @param {google.api.HttpBody} request.httpBody
     *   The prediction input. Supports HTTP headers and arbitrary data payload.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Stream}
     *   An object stream which emits {@link protos.google.api.HttpBody|HttpBody} on 'data' event.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#server-streaming | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1beta1/prediction_service.stream_raw_predict.js</caption>
     * region_tag:aiplatform_v1beta1_generated_PredictionService_StreamRawPredict_async
     */
    streamRawPredict(request?: protos.google.cloud.aiplatform.v1beta1.IStreamRawPredictRequest, options?: CallOptions): gax.CancellableStream;
    /**
     * Perform a streaming online prediction request to a gRPC model server for
     * Vertex first-party products and frameworks.
     *
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Stream}
     *   An object stream which is both readable and writable. It accepts objects
     *   representing {@link protos.google.cloud.aiplatform.v1beta1.StreamDirectPredictRequest|StreamDirectPredictRequest} for write() method, and
     *   will emit objects representing {@link protos.google.cloud.aiplatform.v1beta1.StreamDirectPredictResponse|StreamDirectPredictResponse} on 'data' event asynchronously.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#bi-directional-streaming | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1beta1/prediction_service.stream_direct_predict.js</caption>
     * region_tag:aiplatform_v1beta1_generated_PredictionService_StreamDirectPredict_async
     */
    streamDirectPredict(options?: CallOptions): gax.CancellableStream;
    /**
     * Perform a streaming online prediction request to a gRPC model server for
     * custom containers.
     *
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Stream}
     *   An object stream which is both readable and writable. It accepts objects
     *   representing {@link protos.google.cloud.aiplatform.v1beta1.StreamDirectRawPredictRequest|StreamDirectRawPredictRequest} for write() method, and
     *   will emit objects representing {@link protos.google.cloud.aiplatform.v1beta1.StreamDirectRawPredictResponse|StreamDirectRawPredictResponse} on 'data' event asynchronously.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#bi-directional-streaming | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1beta1/prediction_service.stream_direct_raw_predict.js</caption>
     * region_tag:aiplatform_v1beta1_generated_PredictionService_StreamDirectRawPredict_async
     */
    streamDirectRawPredict(options?: CallOptions): gax.CancellableStream;
    /**
     * Perform a streaming online prediction request for Vertex first-party
     * products and frameworks.
     *
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Stream}
     *   An object stream which is both readable and writable. It accepts objects
     *   representing {@link protos.google.cloud.aiplatform.v1beta1.StreamingPredictRequest|StreamingPredictRequest} for write() method, and
     *   will emit objects representing {@link protos.google.cloud.aiplatform.v1beta1.StreamingPredictResponse|StreamingPredictResponse} on 'data' event asynchronously.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#bi-directional-streaming | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1beta1/prediction_service.streaming_predict.js</caption>
     * region_tag:aiplatform_v1beta1_generated_PredictionService_StreamingPredict_async
     */
    streamingPredict(options?: CallOptions): gax.CancellableStream;
    /**
     * Perform a server-side streaming online prediction request for Vertex
     * LLM streaming.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.endpoint
     *   Required. The name of the Endpoint requested to serve the prediction.
     *   Format:
     *   `projects/{project}/locations/{location}/endpoints/{endpoint}`
     * @param {number[]} request.inputs
     *   The prediction input.
     * @param {google.cloud.aiplatform.v1beta1.Tensor} request.parameters
     *   The parameters that govern the prediction.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Stream}
     *   An object stream which emits {@link protos.google.cloud.aiplatform.v1beta1.StreamingPredictResponse|StreamingPredictResponse} on 'data' event.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#server-streaming | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1beta1/prediction_service.server_streaming_predict.js</caption>
     * region_tag:aiplatform_v1beta1_generated_PredictionService_ServerStreamingPredict_async
     */
    serverStreamingPredict(request?: protos.google.cloud.aiplatform.v1beta1.IStreamingPredictRequest, options?: CallOptions): gax.CancellableStream;
    /**
     * Perform a streaming online prediction request through gRPC.
     *
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Stream}
     *   An object stream which is both readable and writable. It accepts objects
     *   representing {@link protos.google.cloud.aiplatform.v1beta1.StreamingRawPredictRequest|StreamingRawPredictRequest} for write() method, and
     *   will emit objects representing {@link protos.google.cloud.aiplatform.v1beta1.StreamingRawPredictResponse|StreamingRawPredictResponse} on 'data' event asynchronously.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#bi-directional-streaming | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1beta1/prediction_service.streaming_raw_predict.js</caption>
     * region_tag:aiplatform_v1beta1_generated_PredictionService_StreamingRawPredict_async
     */
    streamingRawPredict(options?: CallOptions): gax.CancellableStream;
    /**
     * Generate content with multimodal inputs with streaming support.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.model
     *   Required. The fully qualified name of the publisher model or tuned model
     *   endpoint to use.
     *
     *   Publisher model format:
     *   `projects/{project}/locations/{location}/publishers/* /models/*`
     *
     *   Tuned model endpoint format:
     *   `projects/{project}/locations/{location}/endpoints/{endpoint}`
     * @param {number[]} request.contents
     *   Required. The content of the current conversation with the model.
     *
     *   For single-turn queries, this is a single instance. For multi-turn queries,
     *   this is a repeated field that contains conversation history + latest
     *   request.
     * @param {google.cloud.aiplatform.v1beta1.Content} [request.systemInstruction]
     *   Optional. The user provided system instructions for the model.
     *   Note: only text should be used in parts and content in each part will be in
     *   a separate paragraph.
     * @param {string} [request.cachedContent]
     *   Optional. The name of the cached content used as context to serve the
     *   prediction. Note: only used in explicit caching, where users can have
     *   control over caching (e.g. what content to cache) and enjoy guaranteed cost
     *   savings. Format:
     *   `projects/{project}/locations/{location}/cachedContents/{cachedContent}`
     * @param {number[]} [request.tools]
     *   Optional. A list of `Tools` the model may use to generate the next
     *   response.
     *
     *   A `Tool` is a piece of code that enables the system to interact with
     *   external systems to perform an action, or set of actions, outside of
     *   knowledge and scope of the model.
     * @param {google.cloud.aiplatform.v1beta1.ToolConfig} [request.toolConfig]
     *   Optional. Tool config. This config is shared for all tools provided in the
     *   request.
     * @param {number[]} [request.labels]
     *   Optional. The labels with user-defined metadata for the request. It is used
     *   for billing and reporting only.
     *
     *   Label keys and values can be no longer than 63 characters
     *   (Unicode codepoints) and can only contain lowercase letters, numeric
     *   characters, underscores, and dashes. International characters are allowed.
     *   Label values are optional. Label keys must start with a letter.
     * @param {number[]} [request.safetySettings]
     *   Optional. Per request settings for blocking unsafe content.
     *   Enforced on GenerateContentResponse.candidates.
     * @param {google.cloud.aiplatform.v1beta1.GenerationConfig} [request.generationConfig]
     *   Optional. Generation config.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Stream}
     *   An object stream which emits {@link protos.google.cloud.aiplatform.v1beta1.GenerateContentResponse|GenerateContentResponse} on 'data' event.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#server-streaming | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1beta1/prediction_service.stream_generate_content.js</caption>
     * region_tag:aiplatform_v1beta1_generated_PredictionService_StreamGenerateContent_async
     */
    streamGenerateContent(request?: protos.google.cloud.aiplatform.v1beta1.IGenerateContentRequest, options?: CallOptions): gax.CancellableStream;
    /**
     * Exposes an OpenAI-compatible endpoint for chat completions.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.endpoint
     *   Required. The name of the endpoint requested to serve the prediction.
     *   Format:
     *   `projects/{project}/locations/{location}/endpoints/{endpoint}`
     * @param {google.api.HttpBody} [request.httpBody]
     *   Optional. The prediction input. Supports HTTP headers and arbitrary data
     *   payload.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Stream}
     *   An object stream which emits {@link protos.google.api.HttpBody|HttpBody} on 'data' event.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#server-streaming | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1beta1/prediction_service.chat_completions.js</caption>
     * region_tag:aiplatform_v1beta1_generated_PredictionService_ChatCompletions_async
     */
    chatCompletions(request?: protos.google.cloud.aiplatform.v1beta1.IChatCompletionsRequest, options?: CallOptions): gax.CancellableStream;
    /**
     * Gets the access control policy for a resource. Returns an empty policy
     * if the resource exists and does not have a policy set.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.resource
     *   REQUIRED: The resource for which the policy is being requested.
     *   See the operation documentation for the appropriate value for this field.
     * @param {Object} [request.options]
     *   OPTIONAL: A `GetPolicyOptions` object for specifying options to
     *   `GetIamPolicy`. This field is only used by Cloud IAM.
     *
     *   This object should have the same structure as {@link google.iam.v1.GetPolicyOptions | GetPolicyOptions}.
     * @param {Object} [options]
     *   Optional parameters. You can override the default settings for this call, e.g, timeout,
     *   retries, paginations, etc. See {@link https://googleapis.github.io/gax-nodejs/interfaces/CallOptions.html | gax.CallOptions} for the details.
     * @param {function(?Error, ?Object)} [callback]
     *   The function which will be called with the result of the API call.
     *
     *   The second parameter to the callback is an object representing {@link google.iam.v1.Policy | Policy}.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing {@link google.iam.v1.Policy | Policy}.
     *   The promise has a method named "cancel" which cancels the ongoing API call.
     */
    getIamPolicy(request: IamProtos.google.iam.v1.GetIamPolicyRequest, options?: gax.CallOptions | Callback<IamProtos.google.iam.v1.Policy, IamProtos.google.iam.v1.GetIamPolicyRequest | null | undefined, {} | null | undefined>, callback?: Callback<IamProtos.google.iam.v1.Policy, IamProtos.google.iam.v1.GetIamPolicyRequest | null | undefined, {} | null | undefined>): Promise<[IamProtos.google.iam.v1.Policy]>;
    /**
     * Returns permissions that a caller has on the specified resource. If the
     * resource does not exist, this will return an empty set of
     * permissions, not a NOT_FOUND error.
     *
     * Note: This operation is designed to be used for building
     * permission-aware UIs and command-line tools, not for authorization
     * checking. This operation may "fail open" without warning.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.resource
     *   REQUIRED: The resource for which the policy detail is being requested.
     *   See the operation documentation for the appropriate value for this field.
     * @param {string[]} request.permissions
     *   The set of permissions to check for the `resource`. Permissions with
     *   wildcards (such as '*' or 'storage.*') are not allowed. For more
     *   information see {@link https://cloud.google.com/iam/docs/overview#permissions | IAM Overview }.
     * @param {Object} [options]
     *   Optional parameters. You can override the default settings for this call, e.g, timeout,
     *   retries, paginations, etc. See {@link https://googleapis.github.io/gax-nodejs/interfaces/CallOptions.html | gax.CallOptions} for the details.
     * @param {function(?Error, ?Object)} [callback]
     *   The function which will be called with the result of the API call.
     *
     *   The second parameter to the callback is an object representing {@link google.iam.v1.TestIamPermissionsResponse | TestIamPermissionsResponse}.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing {@link google.iam.v1.TestIamPermissionsResponse | TestIamPermissionsResponse}.
     *   The promise has a method named "cancel" which cancels the ongoing API call.
     */
    setIamPolicy(request: IamProtos.google.iam.v1.SetIamPolicyRequest, options?: gax.CallOptions | Callback<IamProtos.google.iam.v1.Policy, IamProtos.google.iam.v1.SetIamPolicyRequest | null | undefined, {} | null | undefined>, callback?: Callback<IamProtos.google.iam.v1.Policy, IamProtos.google.iam.v1.SetIamPolicyRequest | null | undefined, {} | null | undefined>): Promise<[IamProtos.google.iam.v1.Policy]>;
    /**
     * Returns permissions that a caller has on the specified resource. If the
     * resource does not exist, this will return an empty set of
     * permissions, not a NOT_FOUND error.
     *
     * Note: This operation is designed to be used for building
     * permission-aware UIs and command-line tools, not for authorization
     * checking. This operation may "fail open" without warning.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.resource
     *   REQUIRED: The resource for which the policy detail is being requested.
     *   See the operation documentation for the appropriate value for this field.
     * @param {string[]} request.permissions
     *   The set of permissions to check for the `resource`. Permissions with
     *   wildcards (such as '*' or 'storage.*') are not allowed. For more
     *   information see {@link https://cloud.google.com/iam/docs/overview#permissions | IAM Overview }.
     * @param {Object} [options]
     *   Optional parameters. You can override the default settings for this call, e.g, timeout,
     *   retries, paginations, etc. See {@link https://googleapis.github.io/gax-nodejs/interfaces/CallOptions.html | gax.CallOptions} for the details.
     * @param {function(?Error, ?Object)} [callback]
     *   The function which will be called with the result of the API call.
     *
     *   The second parameter to the callback is an object representing {@link google.iam.v1.TestIamPermissionsResponse | TestIamPermissionsResponse}.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing {@link google.iam.v1.TestIamPermissionsResponse | TestIamPermissionsResponse}.
     *   The promise has a method named "cancel" which cancels the ongoing API call.
     *
     */
    testIamPermissions(request: IamProtos.google.iam.v1.TestIamPermissionsRequest, options?: gax.CallOptions | Callback<IamProtos.google.iam.v1.TestIamPermissionsResponse, IamProtos.google.iam.v1.TestIamPermissionsRequest | null | undefined, {} | null | undefined>, callback?: Callback<IamProtos.google.iam.v1.TestIamPermissionsResponse, IamProtos.google.iam.v1.TestIamPermissionsRequest | null | undefined, {} | null | undefined>): Promise<[IamProtos.google.iam.v1.TestIamPermissionsResponse]>;
    /**
     * Gets information about a location.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.name
     *   Resource name for the location.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html | CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing {@link google.cloud.location.Location | Location}.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#regular-methods | documentation }
     *   for more details and examples.
     * @example
     * ```
     * const [response] = await client.getLocation(request);
     * ```
     */
    getLocation(request: LocationProtos.google.cloud.location.IGetLocationRequest, options?: gax.CallOptions | Callback<LocationProtos.google.cloud.location.ILocation, LocationProtos.google.cloud.location.IGetLocationRequest | null | undefined, {} | null | undefined>, callback?: Callback<LocationProtos.google.cloud.location.ILocation, LocationProtos.google.cloud.location.IGetLocationRequest | null | undefined, {} | null | undefined>): Promise<LocationProtos.google.cloud.location.ILocation>;
    /**
     * Lists information about the supported locations for this service. Returns an iterable object.
     *
     * `for`-`await`-`of` syntax is used with the iterable to get response elements on-demand.
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.name
     *   The resource that owns the locations collection, if applicable.
     * @param {string} request.filter
     *   The standard list filter.
     * @param {number} request.pageSize
     *   The standard list page size.
     * @param {string} request.pageToken
     *   The standard list page token.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Object}
     *   An iterable Object that allows {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Iteration_protocols | async iteration }.
     *   When you iterate the returned iterable, each element will be an object representing
     *   {@link google.cloud.location.Location | Location}. The API will be called under the hood as needed, once per the page,
     *   so you can stop the iteration when you don't need more results.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     * @example
     * ```
     * const iterable = client.listLocationsAsync(request);
     * for await (const response of iterable) {
     *   // process response
     * }
     * ```
     */
    listLocationsAsync(request: LocationProtos.google.cloud.location.IListLocationsRequest, options?: CallOptions): AsyncIterable<LocationProtos.google.cloud.location.ILocation>;
    /**
     * Return a fully-qualified annotation resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} dataset
     * @param {string} data_item
     * @param {string} annotation
     * @returns {string} Resource name string.
     */
    annotationPath(project: string, location: string, dataset: string, dataItem: string, annotation: string): string;
    /**
     * Parse the project from Annotation resource.
     *
     * @param {string} annotationName
     *   A fully-qualified path representing Annotation resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromAnnotationName(annotationName: string): string | number;
    /**
     * Parse the location from Annotation resource.
     *
     * @param {string} annotationName
     *   A fully-qualified path representing Annotation resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromAnnotationName(annotationName: string): string | number;
    /**
     * Parse the dataset from Annotation resource.
     *
     * @param {string} annotationName
     *   A fully-qualified path representing Annotation resource.
     * @returns {string} A string representing the dataset.
     */
    matchDatasetFromAnnotationName(annotationName: string): string | number;
    /**
     * Parse the data_item from Annotation resource.
     *
     * @param {string} annotationName
     *   A fully-qualified path representing Annotation resource.
     * @returns {string} A string representing the data_item.
     */
    matchDataItemFromAnnotationName(annotationName: string): string | number;
    /**
     * Parse the annotation from Annotation resource.
     *
     * @param {string} annotationName
     *   A fully-qualified path representing Annotation resource.
     * @returns {string} A string representing the annotation.
     */
    matchAnnotationFromAnnotationName(annotationName: string): string | number;
    /**
     * Return a fully-qualified annotationSpec resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} dataset
     * @param {string} annotation_spec
     * @returns {string} Resource name string.
     */
    annotationSpecPath(project: string, location: string, dataset: string, annotationSpec: string): string;
    /**
     * Parse the project from AnnotationSpec resource.
     *
     * @param {string} annotationSpecName
     *   A fully-qualified path representing AnnotationSpec resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromAnnotationSpecName(annotationSpecName: string): string | number;
    /**
     * Parse the location from AnnotationSpec resource.
     *
     * @param {string} annotationSpecName
     *   A fully-qualified path representing AnnotationSpec resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromAnnotationSpecName(annotationSpecName: string): string | number;
    /**
     * Parse the dataset from AnnotationSpec resource.
     *
     * @param {string} annotationSpecName
     *   A fully-qualified path representing AnnotationSpec resource.
     * @returns {string} A string representing the dataset.
     */
    matchDatasetFromAnnotationSpecName(annotationSpecName: string): string | number;
    /**
     * Parse the annotation_spec from AnnotationSpec resource.
     *
     * @param {string} annotationSpecName
     *   A fully-qualified path representing AnnotationSpec resource.
     * @returns {string} A string representing the annotation_spec.
     */
    matchAnnotationSpecFromAnnotationSpecName(annotationSpecName: string): string | number;
    /**
     * Return a fully-qualified artifact resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} metadata_store
     * @param {string} artifact
     * @returns {string} Resource name string.
     */
    artifactPath(project: string, location: string, metadataStore: string, artifact: string): string;
    /**
     * Parse the project from Artifact resource.
     *
     * @param {string} artifactName
     *   A fully-qualified path representing Artifact resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromArtifactName(artifactName: string): string | number;
    /**
     * Parse the location from Artifact resource.
     *
     * @param {string} artifactName
     *   A fully-qualified path representing Artifact resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromArtifactName(artifactName: string): string | number;
    /**
     * Parse the metadata_store from Artifact resource.
     *
     * @param {string} artifactName
     *   A fully-qualified path representing Artifact resource.
     * @returns {string} A string representing the metadata_store.
     */
    matchMetadataStoreFromArtifactName(artifactName: string): string | number;
    /**
     * Parse the artifact from Artifact resource.
     *
     * @param {string} artifactName
     *   A fully-qualified path representing Artifact resource.
     * @returns {string} A string representing the artifact.
     */
    matchArtifactFromArtifactName(artifactName: string): string | number;
    /**
     * Return a fully-qualified batchPredictionJob resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} batch_prediction_job
     * @returns {string} Resource name string.
     */
    batchPredictionJobPath(project: string, location: string, batchPredictionJob: string): string;
    /**
     * Parse the project from BatchPredictionJob resource.
     *
     * @param {string} batchPredictionJobName
     *   A fully-qualified path representing BatchPredictionJob resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromBatchPredictionJobName(batchPredictionJobName: string): string | number;
    /**
     * Parse the location from BatchPredictionJob resource.
     *
     * @param {string} batchPredictionJobName
     *   A fully-qualified path representing BatchPredictionJob resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromBatchPredictionJobName(batchPredictionJobName: string): string | number;
    /**
     * Parse the batch_prediction_job from BatchPredictionJob resource.
     *
     * @param {string} batchPredictionJobName
     *   A fully-qualified path representing BatchPredictionJob resource.
     * @returns {string} A string representing the batch_prediction_job.
     */
    matchBatchPredictionJobFromBatchPredictionJobName(batchPredictionJobName: string): string | number;
    /**
     * Return a fully-qualified cachedContent resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} cached_content
     * @returns {string} Resource name string.
     */
    cachedContentPath(project: string, location: string, cachedContent: string): string;
    /**
     * Parse the project from CachedContent resource.
     *
     * @param {string} cachedContentName
     *   A fully-qualified path representing CachedContent resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromCachedContentName(cachedContentName: string): string | number;
    /**
     * Parse the location from CachedContent resource.
     *
     * @param {string} cachedContentName
     *   A fully-qualified path representing CachedContent resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromCachedContentName(cachedContentName: string): string | number;
    /**
     * Parse the cached_content from CachedContent resource.
     *
     * @param {string} cachedContentName
     *   A fully-qualified path representing CachedContent resource.
     * @returns {string} A string representing the cached_content.
     */
    matchCachedContentFromCachedContentName(cachedContentName: string): string | number;
    /**
     * Return a fully-qualified context resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} metadata_store
     * @param {string} context
     * @returns {string} Resource name string.
     */
    contextPath(project: string, location: string, metadataStore: string, context: string): string;
    /**
     * Parse the project from Context resource.
     *
     * @param {string} contextName
     *   A fully-qualified path representing Context resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromContextName(contextName: string): string | number;
    /**
     * Parse the location from Context resource.
     *
     * @param {string} contextName
     *   A fully-qualified path representing Context resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromContextName(contextName: string): string | number;
    /**
     * Parse the metadata_store from Context resource.
     *
     * @param {string} contextName
     *   A fully-qualified path representing Context resource.
     * @returns {string} A string representing the metadata_store.
     */
    matchMetadataStoreFromContextName(contextName: string): string | number;
    /**
     * Parse the context from Context resource.
     *
     * @param {string} contextName
     *   A fully-qualified path representing Context resource.
     * @returns {string} A string representing the context.
     */
    matchContextFromContextName(contextName: string): string | number;
    /**
     * Return a fully-qualified customJob resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} custom_job
     * @returns {string} Resource name string.
     */
    customJobPath(project: string, location: string, customJob: string): string;
    /**
     * Parse the project from CustomJob resource.
     *
     * @param {string} customJobName
     *   A fully-qualified path representing CustomJob resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromCustomJobName(customJobName: string): string | number;
    /**
     * Parse the location from CustomJob resource.
     *
     * @param {string} customJobName
     *   A fully-qualified path representing CustomJob resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromCustomJobName(customJobName: string): string | number;
    /**
     * Parse the custom_job from CustomJob resource.
     *
     * @param {string} customJobName
     *   A fully-qualified path representing CustomJob resource.
     * @returns {string} A string representing the custom_job.
     */
    matchCustomJobFromCustomJobName(customJobName: string): string | number;
    /**
     * Return a fully-qualified dataItem resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} dataset
     * @param {string} data_item
     * @returns {string} Resource name string.
     */
    dataItemPath(project: string, location: string, dataset: string, dataItem: string): string;
    /**
     * Parse the project from DataItem resource.
     *
     * @param {string} dataItemName
     *   A fully-qualified path representing DataItem resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromDataItemName(dataItemName: string): string | number;
    /**
     * Parse the location from DataItem resource.
     *
     * @param {string} dataItemName
     *   A fully-qualified path representing DataItem resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromDataItemName(dataItemName: string): string | number;
    /**
     * Parse the dataset from DataItem resource.
     *
     * @param {string} dataItemName
     *   A fully-qualified path representing DataItem resource.
     * @returns {string} A string representing the dataset.
     */
    matchDatasetFromDataItemName(dataItemName: string): string | number;
    /**
     * Parse the data_item from DataItem resource.
     *
     * @param {string} dataItemName
     *   A fully-qualified path representing DataItem resource.
     * @returns {string} A string representing the data_item.
     */
    matchDataItemFromDataItemName(dataItemName: string): string | number;
    /**
     * Return a fully-qualified dataLabelingJob resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} data_labeling_job
     * @returns {string} Resource name string.
     */
    dataLabelingJobPath(project: string, location: string, dataLabelingJob: string): string;
    /**
     * Parse the project from DataLabelingJob resource.
     *
     * @param {string} dataLabelingJobName
     *   A fully-qualified path representing DataLabelingJob resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromDataLabelingJobName(dataLabelingJobName: string): string | number;
    /**
     * Parse the location from DataLabelingJob resource.
     *
     * @param {string} dataLabelingJobName
     *   A fully-qualified path representing DataLabelingJob resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromDataLabelingJobName(dataLabelingJobName: string): string | number;
    /**
     * Parse the data_labeling_job from DataLabelingJob resource.
     *
     * @param {string} dataLabelingJobName
     *   A fully-qualified path representing DataLabelingJob resource.
     * @returns {string} A string representing the data_labeling_job.
     */
    matchDataLabelingJobFromDataLabelingJobName(dataLabelingJobName: string): string | number;
    /**
     * Return a fully-qualified dataset resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} dataset
     * @returns {string} Resource name string.
     */
    datasetPath(project: string, location: string, dataset: string): string;
    /**
     * Parse the project from Dataset resource.
     *
     * @param {string} datasetName
     *   A fully-qualified path representing Dataset resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromDatasetName(datasetName: string): string | number;
    /**
     * Parse the location from Dataset resource.
     *
     * @param {string} datasetName
     *   A fully-qualified path representing Dataset resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromDatasetName(datasetName: string): string | number;
    /**
     * Parse the dataset from Dataset resource.
     *
     * @param {string} datasetName
     *   A fully-qualified path representing Dataset resource.
     * @returns {string} A string representing the dataset.
     */
    matchDatasetFromDatasetName(datasetName: string): string | number;
    /**
     * Return a fully-qualified datasetVersion resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} dataset
     * @param {string} dataset_version
     * @returns {string} Resource name string.
     */
    datasetVersionPath(project: string, location: string, dataset: string, datasetVersion: string): string;
    /**
     * Parse the project from DatasetVersion resource.
     *
     * @param {string} datasetVersionName
     *   A fully-qualified path representing DatasetVersion resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromDatasetVersionName(datasetVersionName: string): string | number;
    /**
     * Parse the location from DatasetVersion resource.
     *
     * @param {string} datasetVersionName
     *   A fully-qualified path representing DatasetVersion resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromDatasetVersionName(datasetVersionName: string): string | number;
    /**
     * Parse the dataset from DatasetVersion resource.
     *
     * @param {string} datasetVersionName
     *   A fully-qualified path representing DatasetVersion resource.
     * @returns {string} A string representing the dataset.
     */
    matchDatasetFromDatasetVersionName(datasetVersionName: string): string | number;
    /**
     * Parse the dataset_version from DatasetVersion resource.
     *
     * @param {string} datasetVersionName
     *   A fully-qualified path representing DatasetVersion resource.
     * @returns {string} A string representing the dataset_version.
     */
    matchDatasetVersionFromDatasetVersionName(datasetVersionName: string): string | number;
    /**
     * Return a fully-qualified deploymentResourcePool resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} deployment_resource_pool
     * @returns {string} Resource name string.
     */
    deploymentResourcePoolPath(project: string, location: string, deploymentResourcePool: string): string;
    /**
     * Parse the project from DeploymentResourcePool resource.
     *
     * @param {string} deploymentResourcePoolName
     *   A fully-qualified path representing DeploymentResourcePool resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromDeploymentResourcePoolName(deploymentResourcePoolName: string): string | number;
    /**
     * Parse the location from DeploymentResourcePool resource.
     *
     * @param {string} deploymentResourcePoolName
     *   A fully-qualified path representing DeploymentResourcePool resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromDeploymentResourcePoolName(deploymentResourcePoolName: string): string | number;
    /**
     * Parse the deployment_resource_pool from DeploymentResourcePool resource.
     *
     * @param {string} deploymentResourcePoolName
     *   A fully-qualified path representing DeploymentResourcePool resource.
     * @returns {string} A string representing the deployment_resource_pool.
     */
    matchDeploymentResourcePoolFromDeploymentResourcePoolName(deploymentResourcePoolName: string): string | number;
    /**
     * Return a fully-qualified entityType resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} featurestore
     * @param {string} entity_type
     * @returns {string} Resource name string.
     */
    entityTypePath(project: string, location: string, featurestore: string, entityType: string): string;
    /**
     * Parse the project from EntityType resource.
     *
     * @param {string} entityTypeName
     *   A fully-qualified path representing EntityType resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromEntityTypeName(entityTypeName: string): string | number;
    /**
     * Parse the location from EntityType resource.
     *
     * @param {string} entityTypeName
     *   A fully-qualified path representing EntityType resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromEntityTypeName(entityTypeName: string): string | number;
    /**
     * Parse the featurestore from EntityType resource.
     *
     * @param {string} entityTypeName
     *   A fully-qualified path representing EntityType resource.
     * @returns {string} A string representing the featurestore.
     */
    matchFeaturestoreFromEntityTypeName(entityTypeName: string): string | number;
    /**
     * Parse the entity_type from EntityType resource.
     *
     * @param {string} entityTypeName
     *   A fully-qualified path representing EntityType resource.
     * @returns {string} A string representing the entity_type.
     */
    matchEntityTypeFromEntityTypeName(entityTypeName: string): string | number;
    /**
     * Return a fully-qualified execution resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} metadata_store
     * @param {string} execution
     * @returns {string} Resource name string.
     */
    executionPath(project: string, location: string, metadataStore: string, execution: string): string;
    /**
     * Parse the project from Execution resource.
     *
     * @param {string} executionName
     *   A fully-qualified path representing Execution resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromExecutionName(executionName: string): string | number;
    /**
     * Parse the location from Execution resource.
     *
     * @param {string} executionName
     *   A fully-qualified path representing Execution resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromExecutionName(executionName: string): string | number;
    /**
     * Parse the metadata_store from Execution resource.
     *
     * @param {string} executionName
     *   A fully-qualified path representing Execution resource.
     * @returns {string} A string representing the metadata_store.
     */
    matchMetadataStoreFromExecutionName(executionName: string): string | number;
    /**
     * Parse the execution from Execution resource.
     *
     * @param {string} executionName
     *   A fully-qualified path representing Execution resource.
     * @returns {string} A string representing the execution.
     */
    matchExecutionFromExecutionName(executionName: string): string | number;
    /**
     * Return a fully-qualified extension resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} extension
     * @returns {string} Resource name string.
     */
    extensionPath(project: string, location: string, extension: string): string;
    /**
     * Parse the project from Extension resource.
     *
     * @param {string} extensionName
     *   A fully-qualified path representing Extension resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromExtensionName(extensionName: string): string | number;
    /**
     * Parse the location from Extension resource.
     *
     * @param {string} extensionName
     *   A fully-qualified path representing Extension resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromExtensionName(extensionName: string): string | number;
    /**
     * Parse the extension from Extension resource.
     *
     * @param {string} extensionName
     *   A fully-qualified path representing Extension resource.
     * @returns {string} A string representing the extension.
     */
    matchExtensionFromExtensionName(extensionName: string): string | number;
    /**
     * Return a fully-qualified featureGroup resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} feature_group
     * @returns {string} Resource name string.
     */
    featureGroupPath(project: string, location: string, featureGroup: string): string;
    /**
     * Parse the project from FeatureGroup resource.
     *
     * @param {string} featureGroupName
     *   A fully-qualified path representing FeatureGroup resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromFeatureGroupName(featureGroupName: string): string | number;
    /**
     * Parse the location from FeatureGroup resource.
     *
     * @param {string} featureGroupName
     *   A fully-qualified path representing FeatureGroup resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromFeatureGroupName(featureGroupName: string): string | number;
    /**
     * Parse the feature_group from FeatureGroup resource.
     *
     * @param {string} featureGroupName
     *   A fully-qualified path representing FeatureGroup resource.
     * @returns {string} A string representing the feature_group.
     */
    matchFeatureGroupFromFeatureGroupName(featureGroupName: string): string | number;
    /**
     * Return a fully-qualified featureMonitor resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} feature_group
     * @param {string} feature_monitor
     * @returns {string} Resource name string.
     */
    featureMonitorPath(project: string, location: string, featureGroup: string, featureMonitor: string): string;
    /**
     * Parse the project from FeatureMonitor resource.
     *
     * @param {string} featureMonitorName
     *   A fully-qualified path representing FeatureMonitor resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromFeatureMonitorName(featureMonitorName: string): string | number;
    /**
     * Parse the location from FeatureMonitor resource.
     *
     * @param {string} featureMonitorName
     *   A fully-qualified path representing FeatureMonitor resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromFeatureMonitorName(featureMonitorName: string): string | number;
    /**
     * Parse the feature_group from FeatureMonitor resource.
     *
     * @param {string} featureMonitorName
     *   A fully-qualified path representing FeatureMonitor resource.
     * @returns {string} A string representing the feature_group.
     */
    matchFeatureGroupFromFeatureMonitorName(featureMonitorName: string): string | number;
    /**
     * Parse the feature_monitor from FeatureMonitor resource.
     *
     * @param {string} featureMonitorName
     *   A fully-qualified path representing FeatureMonitor resource.
     * @returns {string} A string representing the feature_monitor.
     */
    matchFeatureMonitorFromFeatureMonitorName(featureMonitorName: string): string | number;
    /**
     * Return a fully-qualified featureMonitorJob resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} feature_group
     * @param {string} feature_monitor
     * @param {string} feature_monitor_job
     * @returns {string} Resource name string.
     */
    featureMonitorJobPath(project: string, location: string, featureGroup: string, featureMonitor: string, featureMonitorJob: string): string;
    /**
     * Parse the project from FeatureMonitorJob resource.
     *
     * @param {string} featureMonitorJobName
     *   A fully-qualified path representing FeatureMonitorJob resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromFeatureMonitorJobName(featureMonitorJobName: string): string | number;
    /**
     * Parse the location from FeatureMonitorJob resource.
     *
     * @param {string} featureMonitorJobName
     *   A fully-qualified path representing FeatureMonitorJob resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromFeatureMonitorJobName(featureMonitorJobName: string): string | number;
    /**
     * Parse the feature_group from FeatureMonitorJob resource.
     *
     * @param {string} featureMonitorJobName
     *   A fully-qualified path representing FeatureMonitorJob resource.
     * @returns {string} A string representing the feature_group.
     */
    matchFeatureGroupFromFeatureMonitorJobName(featureMonitorJobName: string): string | number;
    /**
     * Parse the feature_monitor from FeatureMonitorJob resource.
     *
     * @param {string} featureMonitorJobName
     *   A fully-qualified path representing FeatureMonitorJob resource.
     * @returns {string} A string representing the feature_monitor.
     */
    matchFeatureMonitorFromFeatureMonitorJobName(featureMonitorJobName: string): string | number;
    /**
     * Parse the feature_monitor_job from FeatureMonitorJob resource.
     *
     * @param {string} featureMonitorJobName
     *   A fully-qualified path representing FeatureMonitorJob resource.
     * @returns {string} A string representing the feature_monitor_job.
     */
    matchFeatureMonitorJobFromFeatureMonitorJobName(featureMonitorJobName: string): string | number;
    /**
     * Return a fully-qualified featureOnlineStore resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} feature_online_store
     * @returns {string} Resource name string.
     */
    featureOnlineStorePath(project: string, location: string, featureOnlineStore: string): string;
    /**
     * Parse the project from FeatureOnlineStore resource.
     *
     * @param {string} featureOnlineStoreName
     *   A fully-qualified path representing FeatureOnlineStore resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromFeatureOnlineStoreName(featureOnlineStoreName: string): string | number;
    /**
     * Parse the location from FeatureOnlineStore resource.
     *
     * @param {string} featureOnlineStoreName
     *   A fully-qualified path representing FeatureOnlineStore resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromFeatureOnlineStoreName(featureOnlineStoreName: string): string | number;
    /**
     * Parse the feature_online_store from FeatureOnlineStore resource.
     *
     * @param {string} featureOnlineStoreName
     *   A fully-qualified path representing FeatureOnlineStore resource.
     * @returns {string} A string representing the feature_online_store.
     */
    matchFeatureOnlineStoreFromFeatureOnlineStoreName(featureOnlineStoreName: string): string | number;
    /**
     * Return a fully-qualified featureView resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} feature_online_store
     * @param {string} feature_view
     * @returns {string} Resource name string.
     */
    featureViewPath(project: string, location: string, featureOnlineStore: string, featureView: string): string;
    /**
     * Parse the project from FeatureView resource.
     *
     * @param {string} featureViewName
     *   A fully-qualified path representing FeatureView resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromFeatureViewName(featureViewName: string): string | number;
    /**
     * Parse the location from FeatureView resource.
     *
     * @param {string} featureViewName
     *   A fully-qualified path representing FeatureView resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromFeatureViewName(featureViewName: string): string | number;
    /**
     * Parse the feature_online_store from FeatureView resource.
     *
     * @param {string} featureViewName
     *   A fully-qualified path representing FeatureView resource.
     * @returns {string} A string representing the feature_online_store.
     */
    matchFeatureOnlineStoreFromFeatureViewName(featureViewName: string): string | number;
    /**
     * Parse the feature_view from FeatureView resource.
     *
     * @param {string} featureViewName
     *   A fully-qualified path representing FeatureView resource.
     * @returns {string} A string representing the feature_view.
     */
    matchFeatureViewFromFeatureViewName(featureViewName: string): string | number;
    /**
     * Return a fully-qualified featureViewSync resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} feature_online_store
     * @param {string} feature_view
     * @returns {string} Resource name string.
     */
    featureViewSyncPath(project: string, location: string, featureOnlineStore: string, featureView: string): string;
    /**
     * Parse the project from FeatureViewSync resource.
     *
     * @param {string} featureViewSyncName
     *   A fully-qualified path representing FeatureViewSync resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromFeatureViewSyncName(featureViewSyncName: string): string | number;
    /**
     * Parse the location from FeatureViewSync resource.
     *
     * @param {string} featureViewSyncName
     *   A fully-qualified path representing FeatureViewSync resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromFeatureViewSyncName(featureViewSyncName: string): string | number;
    /**
     * Parse the feature_online_store from FeatureViewSync resource.
     *
     * @param {string} featureViewSyncName
     *   A fully-qualified path representing FeatureViewSync resource.
     * @returns {string} A string representing the feature_online_store.
     */
    matchFeatureOnlineStoreFromFeatureViewSyncName(featureViewSyncName: string): string | number;
    /**
     * Parse the feature_view from FeatureViewSync resource.
     *
     * @param {string} featureViewSyncName
     *   A fully-qualified path representing FeatureViewSync resource.
     * @returns {string} A string representing the feature_view.
     */
    matchFeatureViewFromFeatureViewSyncName(featureViewSyncName: string): string | number;
    /**
     * Return a fully-qualified featurestore resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} featurestore
     * @returns {string} Resource name string.
     */
    featurestorePath(project: string, location: string, featurestore: string): string;
    /**
     * Parse the project from Featurestore resource.
     *
     * @param {string} featurestoreName
     *   A fully-qualified path representing Featurestore resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromFeaturestoreName(featurestoreName: string): string | number;
    /**
     * Parse the location from Featurestore resource.
     *
     * @param {string} featurestoreName
     *   A fully-qualified path representing Featurestore resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromFeaturestoreName(featurestoreName: string): string | number;
    /**
     * Parse the featurestore from Featurestore resource.
     *
     * @param {string} featurestoreName
     *   A fully-qualified path representing Featurestore resource.
     * @returns {string} A string representing the featurestore.
     */
    matchFeaturestoreFromFeaturestoreName(featurestoreName: string): string | number;
    /**
     * Return a fully-qualified hyperparameterTuningJob resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} hyperparameter_tuning_job
     * @returns {string} Resource name string.
     */
    hyperparameterTuningJobPath(project: string, location: string, hyperparameterTuningJob: string): string;
    /**
     * Parse the project from HyperparameterTuningJob resource.
     *
     * @param {string} hyperparameterTuningJobName
     *   A fully-qualified path representing HyperparameterTuningJob resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromHyperparameterTuningJobName(hyperparameterTuningJobName: string): string | number;
    /**
     * Parse the location from HyperparameterTuningJob resource.
     *
     * @param {string} hyperparameterTuningJobName
     *   A fully-qualified path representing HyperparameterTuningJob resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromHyperparameterTuningJobName(hyperparameterTuningJobName: string): string | number;
    /**
     * Parse the hyperparameter_tuning_job from HyperparameterTuningJob resource.
     *
     * @param {string} hyperparameterTuningJobName
     *   A fully-qualified path representing HyperparameterTuningJob resource.
     * @returns {string} A string representing the hyperparameter_tuning_job.
     */
    matchHyperparameterTuningJobFromHyperparameterTuningJobName(hyperparameterTuningJobName: string): string | number;
    /**
     * Return a fully-qualified index resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} index
     * @returns {string} Resource name string.
     */
    indexPath(project: string, location: string, index: string): string;
    /**
     * Parse the project from Index resource.
     *
     * @param {string} indexName
     *   A fully-qualified path representing Index resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromIndexName(indexName: string): string | number;
    /**
     * Parse the location from Index resource.
     *
     * @param {string} indexName
     *   A fully-qualified path representing Index resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromIndexName(indexName: string): string | number;
    /**
     * Parse the index from Index resource.
     *
     * @param {string} indexName
     *   A fully-qualified path representing Index resource.
     * @returns {string} A string representing the index.
     */
    matchIndexFromIndexName(indexName: string): string | number;
    /**
     * Return a fully-qualified indexEndpoint resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} index_endpoint
     * @returns {string} Resource name string.
     */
    indexEndpointPath(project: string, location: string, indexEndpoint: string): string;
    /**
     * Parse the project from IndexEndpoint resource.
     *
     * @param {string} indexEndpointName
     *   A fully-qualified path representing IndexEndpoint resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromIndexEndpointName(indexEndpointName: string): string | number;
    /**
     * Parse the location from IndexEndpoint resource.
     *
     * @param {string} indexEndpointName
     *   A fully-qualified path representing IndexEndpoint resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromIndexEndpointName(indexEndpointName: string): string | number;
    /**
     * Parse the index_endpoint from IndexEndpoint resource.
     *
     * @param {string} indexEndpointName
     *   A fully-qualified path representing IndexEndpoint resource.
     * @returns {string} A string representing the index_endpoint.
     */
    matchIndexEndpointFromIndexEndpointName(indexEndpointName: string): string | number;
    /**
     * Return a fully-qualified metadataSchema resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} metadata_store
     * @param {string} metadata_schema
     * @returns {string} Resource name string.
     */
    metadataSchemaPath(project: string, location: string, metadataStore: string, metadataSchema: string): string;
    /**
     * Parse the project from MetadataSchema resource.
     *
     * @param {string} metadataSchemaName
     *   A fully-qualified path representing MetadataSchema resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromMetadataSchemaName(metadataSchemaName: string): string | number;
    /**
     * Parse the location from MetadataSchema resource.
     *
     * @param {string} metadataSchemaName
     *   A fully-qualified path representing MetadataSchema resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromMetadataSchemaName(metadataSchemaName: string): string | number;
    /**
     * Parse the metadata_store from MetadataSchema resource.
     *
     * @param {string} metadataSchemaName
     *   A fully-qualified path representing MetadataSchema resource.
     * @returns {string} A string representing the metadata_store.
     */
    matchMetadataStoreFromMetadataSchemaName(metadataSchemaName: string): string | number;
    /**
     * Parse the metadata_schema from MetadataSchema resource.
     *
     * @param {string} metadataSchemaName
     *   A fully-qualified path representing MetadataSchema resource.
     * @returns {string} A string representing the metadata_schema.
     */
    matchMetadataSchemaFromMetadataSchemaName(metadataSchemaName: string): string | number;
    /**
     * Return a fully-qualified metadataStore resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} metadata_store
     * @returns {string} Resource name string.
     */
    metadataStorePath(project: string, location: string, metadataStore: string): string;
    /**
     * Parse the project from MetadataStore resource.
     *
     * @param {string} metadataStoreName
     *   A fully-qualified path representing MetadataStore resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromMetadataStoreName(metadataStoreName: string): string | number;
    /**
     * Parse the location from MetadataStore resource.
     *
     * @param {string} metadataStoreName
     *   A fully-qualified path representing MetadataStore resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromMetadataStoreName(metadataStoreName: string): string | number;
    /**
     * Parse the metadata_store from MetadataStore resource.
     *
     * @param {string} metadataStoreName
     *   A fully-qualified path representing MetadataStore resource.
     * @returns {string} A string representing the metadata_store.
     */
    matchMetadataStoreFromMetadataStoreName(metadataStoreName: string): string | number;
    /**
     * Return a fully-qualified model resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} model
     * @returns {string} Resource name string.
     */
    modelPath(project: string, location: string, model: string): string;
    /**
     * Parse the project from Model resource.
     *
     * @param {string} modelName
     *   A fully-qualified path representing Model resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromModelName(modelName: string): string | number;
    /**
     * Parse the location from Model resource.
     *
     * @param {string} modelName
     *   A fully-qualified path representing Model resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromModelName(modelName: string): string | number;
    /**
     * Parse the model from Model resource.
     *
     * @param {string} modelName
     *   A fully-qualified path representing Model resource.
     * @returns {string} A string representing the model.
     */
    matchModelFromModelName(modelName: string): string | number;
    /**
     * Return a fully-qualified modelDeploymentMonitoringJob resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} model_deployment_monitoring_job
     * @returns {string} Resource name string.
     */
    modelDeploymentMonitoringJobPath(project: string, location: string, modelDeploymentMonitoringJob: string): string;
    /**
     * Parse the project from ModelDeploymentMonitoringJob resource.
     *
     * @param {string} modelDeploymentMonitoringJobName
     *   A fully-qualified path representing ModelDeploymentMonitoringJob resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromModelDeploymentMonitoringJobName(modelDeploymentMonitoringJobName: string): string | number;
    /**
     * Parse the location from ModelDeploymentMonitoringJob resource.
     *
     * @param {string} modelDeploymentMonitoringJobName
     *   A fully-qualified path representing ModelDeploymentMonitoringJob resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromModelDeploymentMonitoringJobName(modelDeploymentMonitoringJobName: string): string | number;
    /**
     * Parse the model_deployment_monitoring_job from ModelDeploymentMonitoringJob resource.
     *
     * @param {string} modelDeploymentMonitoringJobName
     *   A fully-qualified path representing ModelDeploymentMonitoringJob resource.
     * @returns {string} A string representing the model_deployment_monitoring_job.
     */
    matchModelDeploymentMonitoringJobFromModelDeploymentMonitoringJobName(modelDeploymentMonitoringJobName: string): string | number;
    /**
     * Return a fully-qualified modelEvaluation resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} model
     * @param {string} evaluation
     * @returns {string} Resource name string.
     */
    modelEvaluationPath(project: string, location: string, model: string, evaluation: string): string;
    /**
     * Parse the project from ModelEvaluation resource.
     *
     * @param {string} modelEvaluationName
     *   A fully-qualified path representing ModelEvaluation resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromModelEvaluationName(modelEvaluationName: string): string | number;
    /**
     * Parse the location from ModelEvaluation resource.
     *
     * @param {string} modelEvaluationName
     *   A fully-qualified path representing ModelEvaluation resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromModelEvaluationName(modelEvaluationName: string): string | number;
    /**
     * Parse the model from ModelEvaluation resource.
     *
     * @param {string} modelEvaluationName
     *   A fully-qualified path representing ModelEvaluation resource.
     * @returns {string} A string representing the model.
     */
    matchModelFromModelEvaluationName(modelEvaluationName: string): string | number;
    /**
     * Parse the evaluation from ModelEvaluation resource.
     *
     * @param {string} modelEvaluationName
     *   A fully-qualified path representing ModelEvaluation resource.
     * @returns {string} A string representing the evaluation.
     */
    matchEvaluationFromModelEvaluationName(modelEvaluationName: string): string | number;
    /**
     * Return a fully-qualified modelEvaluationSlice resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} model
     * @param {string} evaluation
     * @param {string} slice
     * @returns {string} Resource name string.
     */
    modelEvaluationSlicePath(project: string, location: string, model: string, evaluation: string, slice: string): string;
    /**
     * Parse the project from ModelEvaluationSlice resource.
     *
     * @param {string} modelEvaluationSliceName
     *   A fully-qualified path representing ModelEvaluationSlice resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromModelEvaluationSliceName(modelEvaluationSliceName: string): string | number;
    /**
     * Parse the location from ModelEvaluationSlice resource.
     *
     * @param {string} modelEvaluationSliceName
     *   A fully-qualified path representing ModelEvaluationSlice resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromModelEvaluationSliceName(modelEvaluationSliceName: string): string | number;
    /**
     * Parse the model from ModelEvaluationSlice resource.
     *
     * @param {string} modelEvaluationSliceName
     *   A fully-qualified path representing ModelEvaluationSlice resource.
     * @returns {string} A string representing the model.
     */
    matchModelFromModelEvaluationSliceName(modelEvaluationSliceName: string): string | number;
    /**
     * Parse the evaluation from ModelEvaluationSlice resource.
     *
     * @param {string} modelEvaluationSliceName
     *   A fully-qualified path representing ModelEvaluationSlice resource.
     * @returns {string} A string representing the evaluation.
     */
    matchEvaluationFromModelEvaluationSliceName(modelEvaluationSliceName: string): string | number;
    /**
     * Parse the slice from ModelEvaluationSlice resource.
     *
     * @param {string} modelEvaluationSliceName
     *   A fully-qualified path representing ModelEvaluationSlice resource.
     * @returns {string} A string representing the slice.
     */
    matchSliceFromModelEvaluationSliceName(modelEvaluationSliceName: string): string | number;
    /**
     * Return a fully-qualified modelMonitor resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} model_monitor
     * @returns {string} Resource name string.
     */
    modelMonitorPath(project: string, location: string, modelMonitor: string): string;
    /**
     * Parse the project from ModelMonitor resource.
     *
     * @param {string} modelMonitorName
     *   A fully-qualified path representing ModelMonitor resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromModelMonitorName(modelMonitorName: string): string | number;
    /**
     * Parse the location from ModelMonitor resource.
     *
     * @param {string} modelMonitorName
     *   A fully-qualified path representing ModelMonitor resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromModelMonitorName(modelMonitorName: string): string | number;
    /**
     * Parse the model_monitor from ModelMonitor resource.
     *
     * @param {string} modelMonitorName
     *   A fully-qualified path representing ModelMonitor resource.
     * @returns {string} A string representing the model_monitor.
     */
    matchModelMonitorFromModelMonitorName(modelMonitorName: string): string | number;
    /**
     * Return a fully-qualified modelMonitoringJob resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} model_monitor
     * @param {string} model_monitoring_job
     * @returns {string} Resource name string.
     */
    modelMonitoringJobPath(project: string, location: string, modelMonitor: string, modelMonitoringJob: string): string;
    /**
     * Parse the project from ModelMonitoringJob resource.
     *
     * @param {string} modelMonitoringJobName
     *   A fully-qualified path representing ModelMonitoringJob resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromModelMonitoringJobName(modelMonitoringJobName: string): string | number;
    /**
     * Parse the location from ModelMonitoringJob resource.
     *
     * @param {string} modelMonitoringJobName
     *   A fully-qualified path representing ModelMonitoringJob resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromModelMonitoringJobName(modelMonitoringJobName: string): string | number;
    /**
     * Parse the model_monitor from ModelMonitoringJob resource.
     *
     * @param {string} modelMonitoringJobName
     *   A fully-qualified path representing ModelMonitoringJob resource.
     * @returns {string} A string representing the model_monitor.
     */
    matchModelMonitorFromModelMonitoringJobName(modelMonitoringJobName: string): string | number;
    /**
     * Parse the model_monitoring_job from ModelMonitoringJob resource.
     *
     * @param {string} modelMonitoringJobName
     *   A fully-qualified path representing ModelMonitoringJob resource.
     * @returns {string} A string representing the model_monitoring_job.
     */
    matchModelMonitoringJobFromModelMonitoringJobName(modelMonitoringJobName: string): string | number;
    /**
     * Return a fully-qualified nasJob resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} nas_job
     * @returns {string} Resource name string.
     */
    nasJobPath(project: string, location: string, nasJob: string): string;
    /**
     * Parse the project from NasJob resource.
     *
     * @param {string} nasJobName
     *   A fully-qualified path representing NasJob resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromNasJobName(nasJobName: string): string | number;
    /**
     * Parse the location from NasJob resource.
     *
     * @param {string} nasJobName
     *   A fully-qualified path representing NasJob resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromNasJobName(nasJobName: string): string | number;
    /**
     * Parse the nas_job from NasJob resource.
     *
     * @param {string} nasJobName
     *   A fully-qualified path representing NasJob resource.
     * @returns {string} A string representing the nas_job.
     */
    matchNasJobFromNasJobName(nasJobName: string): string | number;
    /**
     * Return a fully-qualified nasTrialDetail resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} nas_job
     * @param {string} nas_trial_detail
     * @returns {string} Resource name string.
     */
    nasTrialDetailPath(project: string, location: string, nasJob: string, nasTrialDetail: string): string;
    /**
     * Parse the project from NasTrialDetail resource.
     *
     * @param {string} nasTrialDetailName
     *   A fully-qualified path representing NasTrialDetail resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromNasTrialDetailName(nasTrialDetailName: string): string | number;
    /**
     * Parse the location from NasTrialDetail resource.
     *
     * @param {string} nasTrialDetailName
     *   A fully-qualified path representing NasTrialDetail resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromNasTrialDetailName(nasTrialDetailName: string): string | number;
    /**
     * Parse the nas_job from NasTrialDetail resource.
     *
     * @param {string} nasTrialDetailName
     *   A fully-qualified path representing NasTrialDetail resource.
     * @returns {string} A string representing the nas_job.
     */
    matchNasJobFromNasTrialDetailName(nasTrialDetailName: string): string | number;
    /**
     * Parse the nas_trial_detail from NasTrialDetail resource.
     *
     * @param {string} nasTrialDetailName
     *   A fully-qualified path representing NasTrialDetail resource.
     * @returns {string} A string representing the nas_trial_detail.
     */
    matchNasTrialDetailFromNasTrialDetailName(nasTrialDetailName: string): string | number;
    /**
     * Return a fully-qualified notebookExecutionJob resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} notebook_execution_job
     * @returns {string} Resource name string.
     */
    notebookExecutionJobPath(project: string, location: string, notebookExecutionJob: string): string;
    /**
     * Parse the project from NotebookExecutionJob resource.
     *
     * @param {string} notebookExecutionJobName
     *   A fully-qualified path representing NotebookExecutionJob resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromNotebookExecutionJobName(notebookExecutionJobName: string): string | number;
    /**
     * Parse the location from NotebookExecutionJob resource.
     *
     * @param {string} notebookExecutionJobName
     *   A fully-qualified path representing NotebookExecutionJob resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromNotebookExecutionJobName(notebookExecutionJobName: string): string | number;
    /**
     * Parse the notebook_execution_job from NotebookExecutionJob resource.
     *
     * @param {string} notebookExecutionJobName
     *   A fully-qualified path representing NotebookExecutionJob resource.
     * @returns {string} A string representing the notebook_execution_job.
     */
    matchNotebookExecutionJobFromNotebookExecutionJobName(notebookExecutionJobName: string): string | number;
    /**
     * Return a fully-qualified notebookRuntime resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} notebook_runtime
     * @returns {string} Resource name string.
     */
    notebookRuntimePath(project: string, location: string, notebookRuntime: string): string;
    /**
     * Parse the project from NotebookRuntime resource.
     *
     * @param {string} notebookRuntimeName
     *   A fully-qualified path representing NotebookRuntime resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromNotebookRuntimeName(notebookRuntimeName: string): string | number;
    /**
     * Parse the location from NotebookRuntime resource.
     *
     * @param {string} notebookRuntimeName
     *   A fully-qualified path representing NotebookRuntime resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromNotebookRuntimeName(notebookRuntimeName: string): string | number;
    /**
     * Parse the notebook_runtime from NotebookRuntime resource.
     *
     * @param {string} notebookRuntimeName
     *   A fully-qualified path representing NotebookRuntime resource.
     * @returns {string} A string representing the notebook_runtime.
     */
    matchNotebookRuntimeFromNotebookRuntimeName(notebookRuntimeName: string): string | number;
    /**
     * Return a fully-qualified notebookRuntimeTemplate resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} notebook_runtime_template
     * @returns {string} Resource name string.
     */
    notebookRuntimeTemplatePath(project: string, location: string, notebookRuntimeTemplate: string): string;
    /**
     * Parse the project from NotebookRuntimeTemplate resource.
     *
     * @param {string} notebookRuntimeTemplateName
     *   A fully-qualified path representing NotebookRuntimeTemplate resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromNotebookRuntimeTemplateName(notebookRuntimeTemplateName: string): string | number;
    /**
     * Parse the location from NotebookRuntimeTemplate resource.
     *
     * @param {string} notebookRuntimeTemplateName
     *   A fully-qualified path representing NotebookRuntimeTemplate resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromNotebookRuntimeTemplateName(notebookRuntimeTemplateName: string): string | number;
    /**
     * Parse the notebook_runtime_template from NotebookRuntimeTemplate resource.
     *
     * @param {string} notebookRuntimeTemplateName
     *   A fully-qualified path representing NotebookRuntimeTemplate resource.
     * @returns {string} A string representing the notebook_runtime_template.
     */
    matchNotebookRuntimeTemplateFromNotebookRuntimeTemplateName(notebookRuntimeTemplateName: string): string | number;
    /**
     * Return a fully-qualified persistentResource resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} persistent_resource
     * @returns {string} Resource name string.
     */
    persistentResourcePath(project: string, location: string, persistentResource: string): string;
    /**
     * Parse the project from PersistentResource resource.
     *
     * @param {string} persistentResourceName
     *   A fully-qualified path representing PersistentResource resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromPersistentResourceName(persistentResourceName: string): string | number;
    /**
     * Parse the location from PersistentResource resource.
     *
     * @param {string} persistentResourceName
     *   A fully-qualified path representing PersistentResource resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromPersistentResourceName(persistentResourceName: string): string | number;
    /**
     * Parse the persistent_resource from PersistentResource resource.
     *
     * @param {string} persistentResourceName
     *   A fully-qualified path representing PersistentResource resource.
     * @returns {string} A string representing the persistent_resource.
     */
    matchPersistentResourceFromPersistentResourceName(persistentResourceName: string): string | number;
    /**
     * Return a fully-qualified pipelineJob resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} pipeline_job
     * @returns {string} Resource name string.
     */
    pipelineJobPath(project: string, location: string, pipelineJob: string): string;
    /**
     * Parse the project from PipelineJob resource.
     *
     * @param {string} pipelineJobName
     *   A fully-qualified path representing PipelineJob resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromPipelineJobName(pipelineJobName: string): string | number;
    /**
     * Parse the location from PipelineJob resource.
     *
     * @param {string} pipelineJobName
     *   A fully-qualified path representing PipelineJob resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromPipelineJobName(pipelineJobName: string): string | number;
    /**
     * Parse the pipeline_job from PipelineJob resource.
     *
     * @param {string} pipelineJobName
     *   A fully-qualified path representing PipelineJob resource.
     * @returns {string} A string representing the pipeline_job.
     */
    matchPipelineJobFromPipelineJobName(pipelineJobName: string): string | number;
    /**
     * Return a fully-qualified projectLocationEndpoint resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} endpoint
     * @returns {string} Resource name string.
     */
    projectLocationEndpointPath(project: string, location: string, endpoint: string): string;
    /**
     * Parse the project from ProjectLocationEndpoint resource.
     *
     * @param {string} projectLocationEndpointName
     *   A fully-qualified path representing project_location_endpoint resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromProjectLocationEndpointName(projectLocationEndpointName: string): string | number;
    /**
     * Parse the location from ProjectLocationEndpoint resource.
     *
     * @param {string} projectLocationEndpointName
     *   A fully-qualified path representing project_location_endpoint resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromProjectLocationEndpointName(projectLocationEndpointName: string): string | number;
    /**
     * Parse the endpoint from ProjectLocationEndpoint resource.
     *
     * @param {string} projectLocationEndpointName
     *   A fully-qualified path representing project_location_endpoint resource.
     * @returns {string} A string representing the endpoint.
     */
    matchEndpointFromProjectLocationEndpointName(projectLocationEndpointName: string): string | number;
    /**
     * Return a fully-qualified projectLocationFeatureGroupFeature resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} feature_group
     * @param {string} feature
     * @returns {string} Resource name string.
     */
    projectLocationFeatureGroupFeaturePath(project: string, location: string, featureGroup: string, feature: string): string;
    /**
     * Parse the project from ProjectLocationFeatureGroupFeature resource.
     *
     * @param {string} projectLocationFeatureGroupFeatureName
     *   A fully-qualified path representing project_location_feature_group_feature resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromProjectLocationFeatureGroupFeatureName(projectLocationFeatureGroupFeatureName: string): string | number;
    /**
     * Parse the location from ProjectLocationFeatureGroupFeature resource.
     *
     * @param {string} projectLocationFeatureGroupFeatureName
     *   A fully-qualified path representing project_location_feature_group_feature resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromProjectLocationFeatureGroupFeatureName(projectLocationFeatureGroupFeatureName: string): string | number;
    /**
     * Parse the feature_group from ProjectLocationFeatureGroupFeature resource.
     *
     * @param {string} projectLocationFeatureGroupFeatureName
     *   A fully-qualified path representing project_location_feature_group_feature resource.
     * @returns {string} A string representing the feature_group.
     */
    matchFeatureGroupFromProjectLocationFeatureGroupFeatureName(projectLocationFeatureGroupFeatureName: string): string | number;
    /**
     * Parse the feature from ProjectLocationFeatureGroupFeature resource.
     *
     * @param {string} projectLocationFeatureGroupFeatureName
     *   A fully-qualified path representing project_location_feature_group_feature resource.
     * @returns {string} A string representing the feature.
     */
    matchFeatureFromProjectLocationFeatureGroupFeatureName(projectLocationFeatureGroupFeatureName: string): string | number;
    /**
     * Return a fully-qualified projectLocationFeaturestoreEntityTypeFeature resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} featurestore
     * @param {string} entity_type
     * @param {string} feature
     * @returns {string} Resource name string.
     */
    projectLocationFeaturestoreEntityTypeFeaturePath(project: string, location: string, featurestore: string, entityType: string, feature: string): string;
    /**
     * Parse the project from ProjectLocationFeaturestoreEntityTypeFeature resource.
     *
     * @param {string} projectLocationFeaturestoreEntityTypeFeatureName
     *   A fully-qualified path representing project_location_featurestore_entity_type_feature resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromProjectLocationFeaturestoreEntityTypeFeatureName(projectLocationFeaturestoreEntityTypeFeatureName: string): string | number;
    /**
     * Parse the location from ProjectLocationFeaturestoreEntityTypeFeature resource.
     *
     * @param {string} projectLocationFeaturestoreEntityTypeFeatureName
     *   A fully-qualified path representing project_location_featurestore_entity_type_feature resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromProjectLocationFeaturestoreEntityTypeFeatureName(projectLocationFeaturestoreEntityTypeFeatureName: string): string | number;
    /**
     * Parse the featurestore from ProjectLocationFeaturestoreEntityTypeFeature resource.
     *
     * @param {string} projectLocationFeaturestoreEntityTypeFeatureName
     *   A fully-qualified path representing project_location_featurestore_entity_type_feature resource.
     * @returns {string} A string representing the featurestore.
     */
    matchFeaturestoreFromProjectLocationFeaturestoreEntityTypeFeatureName(projectLocationFeaturestoreEntityTypeFeatureName: string): string | number;
    /**
     * Parse the entity_type from ProjectLocationFeaturestoreEntityTypeFeature resource.
     *
     * @param {string} projectLocationFeaturestoreEntityTypeFeatureName
     *   A fully-qualified path representing project_location_featurestore_entity_type_feature resource.
     * @returns {string} A string representing the entity_type.
     */
    matchEntityTypeFromProjectLocationFeaturestoreEntityTypeFeatureName(projectLocationFeaturestoreEntityTypeFeatureName: string): string | number;
    /**
     * Parse the feature from ProjectLocationFeaturestoreEntityTypeFeature resource.
     *
     * @param {string} projectLocationFeaturestoreEntityTypeFeatureName
     *   A fully-qualified path representing project_location_featurestore_entity_type_feature resource.
     * @returns {string} A string representing the feature.
     */
    matchFeatureFromProjectLocationFeaturestoreEntityTypeFeatureName(projectLocationFeaturestoreEntityTypeFeatureName: string): string | number;
    /**
     * Return a fully-qualified projectLocationPublisherModel resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} publisher
     * @param {string} model
     * @returns {string} Resource name string.
     */
    projectLocationPublisherModelPath(project: string, location: string, publisher: string, model: string): string;
    /**
     * Parse the project from ProjectLocationPublisherModel resource.
     *
     * @param {string} projectLocationPublisherModelName
     *   A fully-qualified path representing project_location_publisher_model resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromProjectLocationPublisherModelName(projectLocationPublisherModelName: string): string | number;
    /**
     * Parse the location from ProjectLocationPublisherModel resource.
     *
     * @param {string} projectLocationPublisherModelName
     *   A fully-qualified path representing project_location_publisher_model resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromProjectLocationPublisherModelName(projectLocationPublisherModelName: string): string | number;
    /**
     * Parse the publisher from ProjectLocationPublisherModel resource.
     *
     * @param {string} projectLocationPublisherModelName
     *   A fully-qualified path representing project_location_publisher_model resource.
     * @returns {string} A string representing the publisher.
     */
    matchPublisherFromProjectLocationPublisherModelName(projectLocationPublisherModelName: string): string | number;
    /**
     * Parse the model from ProjectLocationPublisherModel resource.
     *
     * @param {string} projectLocationPublisherModelName
     *   A fully-qualified path representing project_location_publisher_model resource.
     * @returns {string} A string representing the model.
     */
    matchModelFromProjectLocationPublisherModelName(projectLocationPublisherModelName: string): string | number;
    /**
     * Return a fully-qualified publisherModel resource name string.
     *
     * @param {string} publisher
     * @param {string} model
     * @returns {string} Resource name string.
     */
    publisherModelPath(publisher: string, model: string): string;
    /**
     * Parse the publisher from PublisherModel resource.
     *
     * @param {string} publisherModelName
     *   A fully-qualified path representing PublisherModel resource.
     * @returns {string} A string representing the publisher.
     */
    matchPublisherFromPublisherModelName(publisherModelName: string): string | number;
    /**
     * Parse the model from PublisherModel resource.
     *
     * @param {string} publisherModelName
     *   A fully-qualified path representing PublisherModel resource.
     * @returns {string} A string representing the model.
     */
    matchModelFromPublisherModelName(publisherModelName: string): string | number;
    /**
     * Return a fully-qualified ragCorpus resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} rag_corpus
     * @returns {string} Resource name string.
     */
    ragCorpusPath(project: string, location: string, ragCorpus: string): string;
    /**
     * Parse the project from RagCorpus resource.
     *
     * @param {string} ragCorpusName
     *   A fully-qualified path representing RagCorpus resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromRagCorpusName(ragCorpusName: string): string | number;
    /**
     * Parse the location from RagCorpus resource.
     *
     * @param {string} ragCorpusName
     *   A fully-qualified path representing RagCorpus resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromRagCorpusName(ragCorpusName: string): string | number;
    /**
     * Parse the rag_corpus from RagCorpus resource.
     *
     * @param {string} ragCorpusName
     *   A fully-qualified path representing RagCorpus resource.
     * @returns {string} A string representing the rag_corpus.
     */
    matchRagCorpusFromRagCorpusName(ragCorpusName: string): string | number;
    /**
     * Return a fully-qualified ragFile resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} rag_corpus
     * @param {string} rag_file
     * @returns {string} Resource name string.
     */
    ragFilePath(project: string, location: string, ragCorpus: string, ragFile: string): string;
    /**
     * Parse the project from RagFile resource.
     *
     * @param {string} ragFileName
     *   A fully-qualified path representing RagFile resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromRagFileName(ragFileName: string): string | number;
    /**
     * Parse the location from RagFile resource.
     *
     * @param {string} ragFileName
     *   A fully-qualified path representing RagFile resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromRagFileName(ragFileName: string): string | number;
    /**
     * Parse the rag_corpus from RagFile resource.
     *
     * @param {string} ragFileName
     *   A fully-qualified path representing RagFile resource.
     * @returns {string} A string representing the rag_corpus.
     */
    matchRagCorpusFromRagFileName(ragFileName: string): string | number;
    /**
     * Parse the rag_file from RagFile resource.
     *
     * @param {string} ragFileName
     *   A fully-qualified path representing RagFile resource.
     * @returns {string} A string representing the rag_file.
     */
    matchRagFileFromRagFileName(ragFileName: string): string | number;
    /**
     * Return a fully-qualified reasoningEngine resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} reasoning_engine
     * @returns {string} Resource name string.
     */
    reasoningEnginePath(project: string, location: string, reasoningEngine: string): string;
    /**
     * Parse the project from ReasoningEngine resource.
     *
     * @param {string} reasoningEngineName
     *   A fully-qualified path representing ReasoningEngine resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromReasoningEngineName(reasoningEngineName: string): string | number;
    /**
     * Parse the location from ReasoningEngine resource.
     *
     * @param {string} reasoningEngineName
     *   A fully-qualified path representing ReasoningEngine resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromReasoningEngineName(reasoningEngineName: string): string | number;
    /**
     * Parse the reasoning_engine from ReasoningEngine resource.
     *
     * @param {string} reasoningEngineName
     *   A fully-qualified path representing ReasoningEngine resource.
     * @returns {string} A string representing the reasoning_engine.
     */
    matchReasoningEngineFromReasoningEngineName(reasoningEngineName: string): string | number;
    /**
     * Return a fully-qualified savedQuery resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} dataset
     * @param {string} saved_query
     * @returns {string} Resource name string.
     */
    savedQueryPath(project: string, location: string, dataset: string, savedQuery: string): string;
    /**
     * Parse the project from SavedQuery resource.
     *
     * @param {string} savedQueryName
     *   A fully-qualified path representing SavedQuery resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromSavedQueryName(savedQueryName: string): string | number;
    /**
     * Parse the location from SavedQuery resource.
     *
     * @param {string} savedQueryName
     *   A fully-qualified path representing SavedQuery resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromSavedQueryName(savedQueryName: string): string | number;
    /**
     * Parse the dataset from SavedQuery resource.
     *
     * @param {string} savedQueryName
     *   A fully-qualified path representing SavedQuery resource.
     * @returns {string} A string representing the dataset.
     */
    matchDatasetFromSavedQueryName(savedQueryName: string): string | number;
    /**
     * Parse the saved_query from SavedQuery resource.
     *
     * @param {string} savedQueryName
     *   A fully-qualified path representing SavedQuery resource.
     * @returns {string} A string representing the saved_query.
     */
    matchSavedQueryFromSavedQueryName(savedQueryName: string): string | number;
    /**
     * Return a fully-qualified schedule resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} schedule
     * @returns {string} Resource name string.
     */
    schedulePath(project: string, location: string, schedule: string): string;
    /**
     * Parse the project from Schedule resource.
     *
     * @param {string} scheduleName
     *   A fully-qualified path representing Schedule resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromScheduleName(scheduleName: string): string | number;
    /**
     * Parse the location from Schedule resource.
     *
     * @param {string} scheduleName
     *   A fully-qualified path representing Schedule resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromScheduleName(scheduleName: string): string | number;
    /**
     * Parse the schedule from Schedule resource.
     *
     * @param {string} scheduleName
     *   A fully-qualified path representing Schedule resource.
     * @returns {string} A string representing the schedule.
     */
    matchScheduleFromScheduleName(scheduleName: string): string | number;
    /**
     * Return a fully-qualified specialistPool resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} specialist_pool
     * @returns {string} Resource name string.
     */
    specialistPoolPath(project: string, location: string, specialistPool: string): string;
    /**
     * Parse the project from SpecialistPool resource.
     *
     * @param {string} specialistPoolName
     *   A fully-qualified path representing SpecialistPool resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromSpecialistPoolName(specialistPoolName: string): string | number;
    /**
     * Parse the location from SpecialistPool resource.
     *
     * @param {string} specialistPoolName
     *   A fully-qualified path representing SpecialistPool resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromSpecialistPoolName(specialistPoolName: string): string | number;
    /**
     * Parse the specialist_pool from SpecialistPool resource.
     *
     * @param {string} specialistPoolName
     *   A fully-qualified path representing SpecialistPool resource.
     * @returns {string} A string representing the specialist_pool.
     */
    matchSpecialistPoolFromSpecialistPoolName(specialistPoolName: string): string | number;
    /**
     * Return a fully-qualified study resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} study
     * @returns {string} Resource name string.
     */
    studyPath(project: string, location: string, study: string): string;
    /**
     * Parse the project from Study resource.
     *
     * @param {string} studyName
     *   A fully-qualified path representing Study resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromStudyName(studyName: string): string | number;
    /**
     * Parse the location from Study resource.
     *
     * @param {string} studyName
     *   A fully-qualified path representing Study resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromStudyName(studyName: string): string | number;
    /**
     * Parse the study from Study resource.
     *
     * @param {string} studyName
     *   A fully-qualified path representing Study resource.
     * @returns {string} A string representing the study.
     */
    matchStudyFromStudyName(studyName: string): string | number;
    /**
     * Return a fully-qualified tensorboard resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} tensorboard
     * @returns {string} Resource name string.
     */
    tensorboardPath(project: string, location: string, tensorboard: string): string;
    /**
     * Parse the project from Tensorboard resource.
     *
     * @param {string} tensorboardName
     *   A fully-qualified path representing Tensorboard resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromTensorboardName(tensorboardName: string): string | number;
    /**
     * Parse the location from Tensorboard resource.
     *
     * @param {string} tensorboardName
     *   A fully-qualified path representing Tensorboard resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromTensorboardName(tensorboardName: string): string | number;
    /**
     * Parse the tensorboard from Tensorboard resource.
     *
     * @param {string} tensorboardName
     *   A fully-qualified path representing Tensorboard resource.
     * @returns {string} A string representing the tensorboard.
     */
    matchTensorboardFromTensorboardName(tensorboardName: string): string | number;
    /**
     * Return a fully-qualified tensorboardExperiment resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} tensorboard
     * @param {string} experiment
     * @returns {string} Resource name string.
     */
    tensorboardExperimentPath(project: string, location: string, tensorboard: string, experiment: string): string;
    /**
     * Parse the project from TensorboardExperiment resource.
     *
     * @param {string} tensorboardExperimentName
     *   A fully-qualified path representing TensorboardExperiment resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromTensorboardExperimentName(tensorboardExperimentName: string): string | number;
    /**
     * Parse the location from TensorboardExperiment resource.
     *
     * @param {string} tensorboardExperimentName
     *   A fully-qualified path representing TensorboardExperiment resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromTensorboardExperimentName(tensorboardExperimentName: string): string | number;
    /**
     * Parse the tensorboard from TensorboardExperiment resource.
     *
     * @param {string} tensorboardExperimentName
     *   A fully-qualified path representing TensorboardExperiment resource.
     * @returns {string} A string representing the tensorboard.
     */
    matchTensorboardFromTensorboardExperimentName(tensorboardExperimentName: string): string | number;
    /**
     * Parse the experiment from TensorboardExperiment resource.
     *
     * @param {string} tensorboardExperimentName
     *   A fully-qualified path representing TensorboardExperiment resource.
     * @returns {string} A string representing the experiment.
     */
    matchExperimentFromTensorboardExperimentName(tensorboardExperimentName: string): string | number;
    /**
     * Return a fully-qualified tensorboardRun resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} tensorboard
     * @param {string} experiment
     * @param {string} run
     * @returns {string} Resource name string.
     */
    tensorboardRunPath(project: string, location: string, tensorboard: string, experiment: string, run: string): string;
    /**
     * Parse the project from TensorboardRun resource.
     *
     * @param {string} tensorboardRunName
     *   A fully-qualified path representing TensorboardRun resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromTensorboardRunName(tensorboardRunName: string): string | number;
    /**
     * Parse the location from TensorboardRun resource.
     *
     * @param {string} tensorboardRunName
     *   A fully-qualified path representing TensorboardRun resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromTensorboardRunName(tensorboardRunName: string): string | number;
    /**
     * Parse the tensorboard from TensorboardRun resource.
     *
     * @param {string} tensorboardRunName
     *   A fully-qualified path representing TensorboardRun resource.
     * @returns {string} A string representing the tensorboard.
     */
    matchTensorboardFromTensorboardRunName(tensorboardRunName: string): string | number;
    /**
     * Parse the experiment from TensorboardRun resource.
     *
     * @param {string} tensorboardRunName
     *   A fully-qualified path representing TensorboardRun resource.
     * @returns {string} A string representing the experiment.
     */
    matchExperimentFromTensorboardRunName(tensorboardRunName: string): string | number;
    /**
     * Parse the run from TensorboardRun resource.
     *
     * @param {string} tensorboardRunName
     *   A fully-qualified path representing TensorboardRun resource.
     * @returns {string} A string representing the run.
     */
    matchRunFromTensorboardRunName(tensorboardRunName: string): string | number;
    /**
     * Return a fully-qualified tensorboardTimeSeries resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} tensorboard
     * @param {string} experiment
     * @param {string} run
     * @param {string} time_series
     * @returns {string} Resource name string.
     */
    tensorboardTimeSeriesPath(project: string, location: string, tensorboard: string, experiment: string, run: string, timeSeries: string): string;
    /**
     * Parse the project from TensorboardTimeSeries resource.
     *
     * @param {string} tensorboardTimeSeriesName
     *   A fully-qualified path representing TensorboardTimeSeries resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromTensorboardTimeSeriesName(tensorboardTimeSeriesName: string): string | number;
    /**
     * Parse the location from TensorboardTimeSeries resource.
     *
     * @param {string} tensorboardTimeSeriesName
     *   A fully-qualified path representing TensorboardTimeSeries resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromTensorboardTimeSeriesName(tensorboardTimeSeriesName: string): string | number;
    /**
     * Parse the tensorboard from TensorboardTimeSeries resource.
     *
     * @param {string} tensorboardTimeSeriesName
     *   A fully-qualified path representing TensorboardTimeSeries resource.
     * @returns {string} A string representing the tensorboard.
     */
    matchTensorboardFromTensorboardTimeSeriesName(tensorboardTimeSeriesName: string): string | number;
    /**
     * Parse the experiment from TensorboardTimeSeries resource.
     *
     * @param {string} tensorboardTimeSeriesName
     *   A fully-qualified path representing TensorboardTimeSeries resource.
     * @returns {string} A string representing the experiment.
     */
    matchExperimentFromTensorboardTimeSeriesName(tensorboardTimeSeriesName: string): string | number;
    /**
     * Parse the run from TensorboardTimeSeries resource.
     *
     * @param {string} tensorboardTimeSeriesName
     *   A fully-qualified path representing TensorboardTimeSeries resource.
     * @returns {string} A string representing the run.
     */
    matchRunFromTensorboardTimeSeriesName(tensorboardTimeSeriesName: string): string | number;
    /**
     * Parse the time_series from TensorboardTimeSeries resource.
     *
     * @param {string} tensorboardTimeSeriesName
     *   A fully-qualified path representing TensorboardTimeSeries resource.
     * @returns {string} A string representing the time_series.
     */
    matchTimeSeriesFromTensorboardTimeSeriesName(tensorboardTimeSeriesName: string): string | number;
    /**
     * Return a fully-qualified trainingPipeline resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} training_pipeline
     * @returns {string} Resource name string.
     */
    trainingPipelinePath(project: string, location: string, trainingPipeline: string): string;
    /**
     * Parse the project from TrainingPipeline resource.
     *
     * @param {string} trainingPipelineName
     *   A fully-qualified path representing TrainingPipeline resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromTrainingPipelineName(trainingPipelineName: string): string | number;
    /**
     * Parse the location from TrainingPipeline resource.
     *
     * @param {string} trainingPipelineName
     *   A fully-qualified path representing TrainingPipeline resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromTrainingPipelineName(trainingPipelineName: string): string | number;
    /**
     * Parse the training_pipeline from TrainingPipeline resource.
     *
     * @param {string} trainingPipelineName
     *   A fully-qualified path representing TrainingPipeline resource.
     * @returns {string} A string representing the training_pipeline.
     */
    matchTrainingPipelineFromTrainingPipelineName(trainingPipelineName: string): string | number;
    /**
     * Return a fully-qualified trial resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} study
     * @param {string} trial
     * @returns {string} Resource name string.
     */
    trialPath(project: string, location: string, study: string, trial: string): string;
    /**
     * Parse the project from Trial resource.
     *
     * @param {string} trialName
     *   A fully-qualified path representing Trial resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromTrialName(trialName: string): string | number;
    /**
     * Parse the location from Trial resource.
     *
     * @param {string} trialName
     *   A fully-qualified path representing Trial resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromTrialName(trialName: string): string | number;
    /**
     * Parse the study from Trial resource.
     *
     * @param {string} trialName
     *   A fully-qualified path representing Trial resource.
     * @returns {string} A string representing the study.
     */
    matchStudyFromTrialName(trialName: string): string | number;
    /**
     * Parse the trial from Trial resource.
     *
     * @param {string} trialName
     *   A fully-qualified path representing Trial resource.
     * @returns {string} A string representing the trial.
     */
    matchTrialFromTrialName(trialName: string): string | number;
    /**
     * Return a fully-qualified tuningJob resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} tuning_job
     * @returns {string} Resource name string.
     */
    tuningJobPath(project: string, location: string, tuningJob: string): string;
    /**
     * Parse the project from TuningJob resource.
     *
     * @param {string} tuningJobName
     *   A fully-qualified path representing TuningJob resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromTuningJobName(tuningJobName: string): string | number;
    /**
     * Parse the location from TuningJob resource.
     *
     * @param {string} tuningJobName
     *   A fully-qualified path representing TuningJob resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromTuningJobName(tuningJobName: string): string | number;
    /**
     * Parse the tuning_job from TuningJob resource.
     *
     * @param {string} tuningJobName
     *   A fully-qualified path representing TuningJob resource.
     * @returns {string} A string representing the tuning_job.
     */
    matchTuningJobFromTuningJobName(tuningJobName: string): string | number;
    /**
     * Terminate the gRPC channel and close the client.
     *
     * The client will no longer be usable and all future behavior is undefined.
     * @returns {Promise} A promise that resolves when the client is closed.
     */
    close(): Promise<void>;
}
