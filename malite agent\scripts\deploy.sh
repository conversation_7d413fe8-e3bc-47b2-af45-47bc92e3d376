#!/bin/bash

# Script تلقائي لنشر تطبيق Malite Agent على Google Cloud
# يدعم النشر على App Engine و Cloud Run

set -e  # إيقاف التنفيذ عند حدوث خطأ

# ألوان للنص
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# دوال مساعدة
print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_header() {
    echo -e "${BLUE}"
    echo "=================================================="
    echo "🚀 $1"
    echo "=================================================="
    echo -e "${NC}"
}

# التحقق من المتطلبات
check_requirements() {
    print_header "التحقق من المتطلبات"
    
    # التحقق من وجود gcloud
    if ! command -v gcloud &> /dev/null; then
        print_error "Google Cloud CLI غير مثبت. يرجى تثبيته من: https://cloud.google.com/sdk/docs/install"
        exit 1
    fi
    print_success "Google Cloud CLI مثبت"
    
    # التحقق من وجود npm
    if ! command -v npm &> /dev/null; then
        print_error "npm غير مثبت. يرجى تثبيت Node.js"
        exit 1
    fi
    print_success "npm مثبت"
    
    # التحقق من وجود ملفات المشروع
    if [ ! -f "package.json" ]; then
        print_error "ملف package.json غير موجود. تأكد من تشغيل الأمر من مجلد المشروع"
        exit 1
    fi
    print_success "ملفات المشروع موجودة"
}

# إعداد متغيرات البيئة
setup_environment() {
    print_header "إعداد متغيرات البيئة"
    
    # الحصول على PROJECT_ID الحالي
    PROJECT_ID=$(gcloud config get-value project 2>/dev/null || echo "")
    
    if [ -z "$PROJECT_ID" ]; then
        print_warning "لم يتم تعيين PROJECT_ID"
        read -p "أدخل PROJECT_ID: " PROJECT_ID
        gcloud config set project $PROJECT_ID
    fi
    
    print_info "PROJECT_ID: $PROJECT_ID"
    
    # تعيين المنطقة الافتراضية
    REGION=${REGION:-"us-central1"}
    print_info "REGION: $REGION"
    
    # تعيين اسم الخدمة
    SERVICE_NAME=${SERVICE_NAME:-"malite-agent"}
    print_info "SERVICE_NAME: $SERVICE_NAME"
}

# تفعيل APIs المطلوبة
enable_apis() {
    print_header "تفعيل APIs المطلوبة"
    
    APIS=(
        "run.googleapis.com"
        "cloudbuild.googleapis.com"
        "aiplatform.googleapis.com"
        "secretmanager.googleapis.com"
        "appengine.googleapis.com"
    )
    
    for api in "${APIS[@]}"; do
        print_info "تفعيل $api..."
        gcloud services enable $api --quiet
    done
    
    print_success "تم تفعيل جميع APIs المطلوبة"
}

# تثبيت التبعيات
install_dependencies() {
    print_header "تثبيت التبعيات"
    
    print_info "تثبيت تبعيات npm..."
    npm install
    
    print_success "تم تثبيت التبعيات بنجاح"
}

# تشغيل الاختبارات
run_tests() {
    print_header "تشغيل الاختبارات"
    
    print_info "تشغيل اختبارات النماذج..."
    if npm run test:models; then
        print_success "نجحت جميع الاختبارات"
    else
        print_warning "فشلت بعض الاختبارات، لكن سنتابع النشر"
    fi
}

# النشر على Cloud Run
deploy_cloud_run() {
    print_header "النشر على Cloud Run"
    
    print_info "بدء النشر على Cloud Run..."
    
    gcloud run deploy $SERVICE_NAME \
        --source . \
        --region $REGION \
        --allow-unauthenticated \
        --memory 2Gi \
        --cpu 1 \
        --max-instances 10 \
        --set-env-vars NODE_ENV=production,GENKIT_ENV=production,VERTEX_AI_LOCATION=$REGION,GOOGLE_CLOUD_PROJECT=$PROJECT_ID \
        --quiet
    
    # الحصول على URL الخدمة
    SERVICE_URL=$(gcloud run services describe $SERVICE_NAME --region=$REGION --format="value(status.url)")
    
    print_success "تم النشر بنجاح على Cloud Run"
    print_info "URL الخدمة: $SERVICE_URL"
    
    return 0
}

# النشر على App Engine
deploy_app_engine() {
    print_header "النشر على App Engine"
    
    # التحقق من وجود تطبيق App Engine
    if ! gcloud app describe &>/dev/null; then
        print_info "إنشاء تطبيق App Engine..."
        gcloud app create --region=$REGION --quiet
    fi
    
    print_info "بدء النشر على App Engine..."
    
    # تحديث app.yaml بـ PROJECT_ID
    sed -i.bak "s/YOUR_PROJECT_ID/$PROJECT_ID/g" app.yaml
    
    gcloud app deploy --quiet
    
    # استعادة app.yaml الأصلي
    mv app.yaml.bak app.yaml
    
    # الحصول على URL التطبيق
    APP_URL=$(gcloud app describe --format="value(defaultHostname)")
    
    print_success "تم النشر بنجاح على App Engine"
    print_info "URL التطبيق: https://$APP_URL"
    
    return 0
}

# اختبار النشر
test_deployment() {
    print_header "اختبار النشر"
    
    local url=$1
    
    print_info "اختبار فحص الصحة..."
    if curl -s "$url/health" | grep -q "healthy"; then
        print_success "فحص الصحة نجح"
    else
        print_warning "فحص الصحة فشل"
    fi
    
    print_info "اختبار API الدردشة..."
    response=$(curl -s -X POST "$url/api/chat" \
        -H "Content-Type: application/json" \
        -d '{"message": "مرحباً"}' | jq -r '.success' 2>/dev/null || echo "false")
    
    if [ "$response" = "true" ]; then
        print_success "API الدردشة يعمل بشكل صحيح"
    else
        print_warning "API الدردشة قد لا يعمل بشكل صحيح"
    fi
}

# عرض معلومات ما بعد النشر
show_post_deployment_info() {
    print_header "معلومات ما بعد النشر"
    
    local url=$1
    
    echo "🎉 تم النشر بنجاح!"
    echo ""
    echo "📱 روابط مفيدة:"
    echo "   🌐 التطبيق: $url"
    echo "   🏥 فحص الصحة: $url/health"
    echo "   🤖 API الدردشة: $url/api/chat"
    echo "   📊 قائمة النماذج: $url/api/models"
    echo ""
    echo "🛠️ أوامر مفيدة:"
    echo "   📋 عرض السجلات: gcloud run services logs read $SERVICE_NAME --region=$REGION"
    echo "   ⚙️ تحديث الخدمة: gcloud run services update $SERVICE_NAME --region=$REGION"
    echo "   🗑️ حذف الخدمة: gcloud run services delete $SERVICE_NAME --region=$REGION"
    echo ""
    echo "📚 للمزيد من المعلومات، راجع: docs/deployment-guide.md"
}

# الدالة الرئيسية
main() {
    print_header "نشر تطبيق Malite Agent على Google Cloud"
    
    # التحقق من المعاملات
    DEPLOYMENT_TYPE=${1:-"cloud-run"}
    
    if [ "$DEPLOYMENT_TYPE" != "cloud-run" ] && [ "$DEPLOYMENT_TYPE" != "app-engine" ]; then
        print_error "نوع النشر غير صحيح. استخدم: cloud-run أو app-engine"
        echo "الاستخدام: $0 [cloud-run|app-engine]"
        exit 1
    fi
    
    print_info "نوع النشر: $DEPLOYMENT_TYPE"
    
    # تنفيذ خطوات النشر
    check_requirements
    setup_environment
    enable_apis
    install_dependencies
    run_tests
    
    # النشر حسب النوع المحدد
    if [ "$DEPLOYMENT_TYPE" = "cloud-run" ]; then
        deploy_cloud_run
        SERVICE_URL=$(gcloud run services describe $SERVICE_NAME --region=$REGION --format="value(status.url)")
    else
        deploy_app_engine
        APP_URL=$(gcloud app describe --format="value(defaultHostname)")
        SERVICE_URL="https://$APP_URL"
    fi
    
    # اختبار النشر
    sleep 10  # انتظار حتى يصبح التطبيق جاهزاً
    test_deployment "$SERVICE_URL"
    
    # عرض معلومات ما بعد النشر
    show_post_deployment_info "$SERVICE_URL"
}

# تشغيل الدالة الرئيسية
main "$@"
