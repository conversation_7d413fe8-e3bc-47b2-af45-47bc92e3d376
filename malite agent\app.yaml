# ملف تكوين Google App Engine لنشر تطبيق Malite Agent
# يحدد هذا الملف كيفية تشغيل التطبيق على Google Cloud Platform

runtime: nodejs20

# إعدادات البيئة
env: standard

# إعدادات التوسع التلقائي
automatic_scaling:
  min_instances: 1
  max_instances: 10
  target_cpu_utilization: 0.6

# إعدادات الموارد
resources:
  cpu: 1
  memory_gb: 2
  disk_size_gb: 10

# متغيرات البيئة
env_variables:
  # تفعيل وضع الإنتاج
  NODE_ENV: production
  
  # إعدادات Genkit
  GENKIT_ENV: production
  GENKIT_ENV_DEV: false
  
  # إعدادات Vertex AI
  VERTEX_AI_LOCATION: us-central1
  
  # إعدادات الأداء
  DEFAULT_TEMPERATURE: 0.7
  DEFAULT_MAX_OUTPUT_TOKENS: 1024
  
  # إعدادات الأمان
  ENABLE_CONTENT_FILTERING: true
  SAFETY_LEVEL: MEDIUM
  
  # إعدادات التخزين المؤقت
  ENABLE_CACHING: true
  CACHE_DURATION_SECONDS: 3600
  
  # إعدادات المراقبة
  ENABLE_PERFORMANCE_MONITORING: true
  LOG_LEVEL: INFO

# معالجات الطلبات
handlers:
  # الملفات الثابتة
  - url: /static
    static_dir: public
    secure: always
  
  # ملفات CSS و JS
  - url: /(.*\.(css|js|ico|png|jpg|jpeg|gif|svg))
    static_files: public/\1
    upload: public/.*\.(css|js|ico|png|jpg|jpeg|gif|svg)
    secure: always
  
  # جميع الطلبات الأخرى
  - url: /.*
    script: auto
    secure: always

# إعدادات الشبكة
network:
  forwarded_ports:
    - 8080

# إعدادات الصحة
health_check:
  enable_health_check: true
  check_interval_sec: 30
  timeout_sec: 4
  unhealthy_threshold: 2
  healthy_threshold: 2

# إعدادات الجلسة
session_affinity: false

# إعدادات الأمان
includes:
  - security_headers.yaml

# تجاهل الملفات عند النشر
skip_files:
  - ^(.*/)?#.*#$
  - ^(.*/)?.*~$
  - ^(.*/)?.*\.py[co]$
  - ^(.*/)?.*/RCS/.*$
  - ^(.*/)?\..*$
  - ^(.*/)?tests/.*$
  - ^(.*/)?test/.*$
  - ^(.*/)?docs/.*$
  - ^(.*/)?node_modules/(?!(@genkit-ai|genkit|dotenv|@google-cloud)).*$
  - ^(.*/)?\.env\..*$
  - ^(.*/)?\.git.*$
  - ^(.*/)?README\.md$
  - ^(.*/)?CHANGELOG\.md$
  - ^(.*/)?\.vscode/.*$
  - ^(.*/)?\.idea/.*$

# إعدادات VPC (اختياري)
# vpc_access_connector:
#   name: projects/YOUR_PROJECT_ID/locations/us-central1/connectors/YOUR_CONNECTOR

# إعدادات قاعدة البيانات (إذا كنت تستخدم Cloud SQL)
# beta_settings:
#   cloud_sql_instances: YOUR_PROJECT_ID:us-central1:YOUR_INSTANCE_NAME
