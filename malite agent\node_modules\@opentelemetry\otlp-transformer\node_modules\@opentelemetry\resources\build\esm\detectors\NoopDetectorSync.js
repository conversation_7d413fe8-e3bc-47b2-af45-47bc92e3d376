/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
import { Resource } from '../Resource';
var NoopDetectorSync = /** @class */ (function () {
    function NoopDetectorSync() {
    }
    NoopDetectorSync.prototype.detect = function () {
        return new Resource({});
    };
    return NoopDetectorSync;
}());
export { NoopDetectorSync };
export var noopDetectorSync = new NoopDetectorSync();
//# sourceMappingURL=NoopDetectorSync.js.map