"use strict";
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
var __classPrivateFieldSet = (this && this.__classPrivateFieldSet) || function (receiver, state, value, kind, f) {
    if (kind === "m") throw new TypeError("Private method is not writable");
    if (kind === "a" && !f) throw new TypeError("Private accessor was defined without a setter");
    if (typeof state === "function" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError("Cannot write private member to an object whose class did not declare it");
    return (kind === "a" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;
};
var __classPrivateFieldGet = (this && this.__classPrivateFieldGet) || function (receiver, state, kind, f) {
    if (kind === "a" && !f) throw new TypeError("Private accessor was defined without a getter");
    if (typeof state === "function" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError("Cannot read private member from an object whose class did not declare it");
    return kind === "m" ? f : kind === "a" ? f.call(receiver) : f ? f.value : state.get(receiver);
};
var _APIPromise_promise, _APIPromise_unwrapped, _a;
Object.defineProperty(exports, "__esModule", { value: true });
exports.APIPromise = void 0;
class APIPromise {
    constructor(p) {
        _APIPromise_promise.set(this, void 0);
        _APIPromise_unwrapped.set(this, void 0);
        this[_a] = "APIPromise";
        __classPrivateFieldSet(this, _APIPromise_promise, p instanceof Promise ? p : Promise.resolve(p), "f");
        __classPrivateFieldSet(this, _APIPromise_unwrapped, p instanceof Promise
            ? __classPrivateFieldGet(this, _APIPromise_promise, "f").then(([value]) => value)
            : Promise.resolve(p[0]), "f");
    }
    then(onfulfilled, onrejected) {
        return __classPrivateFieldGet(this, _APIPromise_promise, "f").then(onfulfilled ? ([value]) => onfulfilled(value) : void 0, onrejected);
    }
    catch(onrejected) {
        return __classPrivateFieldGet(this, _APIPromise_unwrapped, "f").catch(onrejected);
    }
    finally(onfinally) {
        return __classPrivateFieldGet(this, _APIPromise_unwrapped, "f").finally(onfinally);
    }
    $inspect() {
        return __classPrivateFieldGet(this, _APIPromise_promise, "f");
    }
}
exports.APIPromise = APIPromise;
_APIPromise_promise = new WeakMap(), _APIPromise_unwrapped = new WeakMap(), _a = Symbol.toStringTag;
//# sourceMappingURL=async.js.map