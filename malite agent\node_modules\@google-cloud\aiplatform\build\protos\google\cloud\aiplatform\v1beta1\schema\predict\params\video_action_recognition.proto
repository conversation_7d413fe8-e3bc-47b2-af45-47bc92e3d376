// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.aiplatform.v1beta1.schema.predict.params;


option csharp_namespace = "Google.Cloud.AIPlatform.V1Beta1.Schema.Predict.Params";
option go_package = "cloud.google.com/go/aiplatform/apiv1beta1/schema/predict/params/paramspb;paramspb";
option java_multiple_files = true;
option java_outer_classname = "VideoActionRecognitionPredictionParamsProto";
option java_package = "com.google.cloud.aiplatform.v1beta1.schema.predict.params";
option php_namespace = "Google\\Cloud\\AIPlatform\\V1beta1\\Schema\\Predict\\Params";
option ruby_package = "Google::Cloud::AIPlatform::V1beta1::Schema::Predict::Params";

// Prediction model parameters for Video Action Recognition.
message VideoActionRecognitionPredictionParams {
  // The Model only returns predictions with at least this confidence score.
  // Default value is 0.0
  float confidence_threshold = 1;

  // The model only returns up to that many top, by confidence score,
  // predictions per frame of the video. If this number is very high, the
  // Model may return fewer predictions per frame. Default value is 50.
  int32 max_predictions = 2;
}
