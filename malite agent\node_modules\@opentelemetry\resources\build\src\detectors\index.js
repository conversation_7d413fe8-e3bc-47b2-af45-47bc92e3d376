"use strict";
/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.envDetectorSync = exports.browserDetectorSync = exports.envDetector = exports.browserDetector = exports.serviceInstanceIdDetectorSync = exports.processDetectorSync = exports.processDetector = exports.osDetectorSync = exports.osDetector = exports.hostDetectorSync = exports.hostDetector = void 0;
var platform_1 = require("./platform");
Object.defineProperty(exports, "hostDetector", { enumerable: true, get: function () { return platform_1.hostDetector; } });
Object.defineProperty(exports, "hostDetectorSync", { enumerable: true, get: function () { return platform_1.hostDetectorSync; } });
Object.defineProperty(exports, "osDetector", { enumerable: true, get: function () { return platform_1.osDetector; } });
Object.defineProperty(exports, "osDetectorSync", { enumerable: true, get: function () { return platform_1.osDetectorSync; } });
Object.defineProperty(exports, "processDetector", { enumerable: true, get: function () { return platform_1.processDetector; } });
Object.defineProperty(exports, "processDetectorSync", { enumerable: true, get: function () { return platform_1.processDetectorSync; } });
Object.defineProperty(exports, "serviceInstanceIdDetectorSync", { enumerable: true, get: function () { return platform_1.serviceInstanceIdDetectorSync; } });
var BrowserDetector_1 = require("./BrowserDetector");
Object.defineProperty(exports, "browserDetector", { enumerable: true, get: function () { return BrowserDetector_1.browserDetector; } });
var EnvDetector_1 = require("./EnvDetector");
Object.defineProperty(exports, "envDetector", { enumerable: true, get: function () { return EnvDetector_1.envDetector; } });
var BrowserDetectorSync_1 = require("./BrowserDetectorSync");
Object.defineProperty(exports, "browserDetectorSync", { enumerable: true, get: function () { return BrowserDetectorSync_1.browserDetectorSync; } });
var EnvDetectorSync_1 = require("./EnvDetectorSync");
Object.defineProperty(exports, "envDetectorSync", { enumerable: true, get: function () { return EnvDetectorSync_1.envDetectorSync; } });
//# sourceMappingURL=index.js.map