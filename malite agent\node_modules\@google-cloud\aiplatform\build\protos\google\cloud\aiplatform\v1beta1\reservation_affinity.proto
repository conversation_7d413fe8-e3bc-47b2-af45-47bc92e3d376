// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.aiplatform.v1beta1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Cloud.AIPlatform.V1Beta1";
option go_package = "cloud.google.com/go/aiplatform/apiv1beta1/aiplatformpb;aiplatformpb";
option java_multiple_files = true;
option java_outer_classname = "ReservationAffinityProto";
option java_package = "com.google.cloud.aiplatform.v1beta1";
option php_namespace = "Google\\Cloud\\AIPlatform\\V1beta1";
option ruby_package = "Google::Cloud::AIPlatform::V1beta1";
option (google.api.resource_definition) = {
  type: "compute.googleapis.com/Reservation"
  pattern: "projects/{project_id_or_number}/zones/{zone}/reservations/{reservation_name}"
};

// A ReservationAffinity can be used to configure a Vertex AI resource (e.g., a
// DeployedModel) to draw its Compute Engine resources from a Shared
// Reservation, or exclusively from on-demand capacity.
message ReservationAffinity {
  // Identifies a type of reservation affinity.
  enum Type {
    // Default value. This should not be used.
    TYPE_UNSPECIFIED = 0;

    // Do not consume from any reserved capacity, only use on-demand.
    NO_RESERVATION = 1;

    // Consume any reservation available, falling back to on-demand.
    ANY_RESERVATION = 2;

    // Consume from a specific reservation. When chosen, the reservation
    // must be identified via the `key` and `values` fields.
    SPECIFIC_RESERVATION = 3;
  }

  // Required. Specifies the reservation affinity type.
  Type reservation_affinity_type = 1 [(google.api.field_behavior) = REQUIRED];

  // Optional. Corresponds to the label key of a reservation resource. To target
  // a SPECIFIC_RESERVATION by name, use
  // `compute.googleapis.com/reservation-name` as the key and specify the name
  // of your reservation as its value.
  string key = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Corresponds to the label values of a reservation resource. This
  // must be the full resource name of the reservation.
  repeated string values = 3 [
    (google.api.field_behavior) = OPTIONAL,
    (google.api.resource_reference) = {
      type: "compute.googleapis.com/Reservation"
    }
  ];
}
