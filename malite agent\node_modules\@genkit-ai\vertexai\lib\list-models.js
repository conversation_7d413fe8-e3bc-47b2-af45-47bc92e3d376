"use strict";
var __create = Object.create;
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __getProtoOf = Object.getPrototypeOf;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(
  // If the importer is in node compatibility mode or this is not an ESM
  // file that has been converted to a CommonJS file using a Babel-
  // compatible transform (i.e. "__esModule" has not been set), then set
  // "default" to the CommonJS "module.exports" for node compatibility.
  isNodeMode || !mod || !mod.__esModule ? __defProp(target, "default", { value: mod, enumerable: true }) : target,
  mod
));
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
var list_models_exports = {};
__export(list_models_exports, {
  listModels: () => listModels
});
module.exports = __toCommonJS(list_models_exports);
var import_genkit = require("genkit");
async function listModels(authClient, location, projectId) {
  const fetch = (await import("node-fetch")).default;
  const accessToken = await authClient.getAccessToken();
  const response = await fetch(
    `https://${location}-aiplatform.googleapis.com/v1beta1/publishers/google/models`,
    {
      method: "GET",
      headers: {
        Authorization: `Bearer ${accessToken}`,
        "x-goog-user-project": projectId,
        "Content-Type": "application/json",
        "X-Goog-Api-Client": import_genkit.GENKIT_CLIENT_HEADER
      }
    }
  );
  if (!response.ok) {
    const ee = await response.text();
    throw new Error(
      `Error from Vertex AI predict: HTTP ${response.status}: ${ee}`
    );
  }
  const modelResponse = await response.json();
  for (const m of modelResponse.publisherModels) {
    console.log(m.name);
  }
  return modelResponse.publisherModels;
}
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  listModels
});
//# sourceMappingURL=list-models.js.map