{"version": 3, "file": "sdk.js", "sourceRoot": "", "sources": ["../src/sdk/sdk.ts"], "names": [], "mappings": ";;;AAAA,6DAAiD;AAEjD,4CAA2C;AAC3C,iCAA8B;AAC9B,+BAA4B;AAC5B,oCAAoC;AAapC,MAAM,sBAAsB,GAA8B;IACxD,gBAAgB,EAAE,gBAAgB;IAClC,oBAAoB,EAAE,oBAAoB;IAC1C,mBAAmB,EAAE,mBAAmB;CACzC,CAAC;AAEF,SAAS,YAAY,CAAC,KAAa;IACjC,MAAM,OAAO,GAAG,sBAAsB,CAAC,KAAK,CAAC,CAAC;IAC9C,IAAI,OAAO,KAAK,SAAS,EAAE,CAAC;QAC1B,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;IACxB,CAAC;IACD,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAChD,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;AAC1B,CAAC;AAED,MAAa,kBAAmB,SAAQ,mBAAS;IAC/C,YAAY,UAA2C,EAAE;QACvD,IAAI,SAAS,GAAG,OAAO,CAAC,SAAS,IAAI,EAAE,CAAC;QACxC,OAAO,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,IAAI,cAAc,CAAC;QAClD,OAAO,CAAC,SAAS,GAAG,WAAW,OAAO,CAAC,MAAM,4BAA4B,CAAC;QAC1E,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;YACnB,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC;gBACvB,MAAM,IAAI,KAAK,CAAC,+FAA+F,CAAC,CAAC;YACnH,CAAC;QACH,CAAC;aAAM,CAAC;YACN,MAAM,IAAI,GAAG,IAAI,gCAAU,CAAC;gBAC1B,MAAM,EAAE,gDAAgD;aACzD,CAAC,CAAC;YACH,OAAO,CAAC,MAAM,GAAG,KAAK,IAAI,EAAE;gBAC1B,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,SAAS,EAAE,CAAC;gBAC1C,MAAM,WAAW,GAAG,MAAM,UAAU,CAAC,iBAAiB,EAAE,CAAC;gBACzD,MAAM,KAAK,GAAG,WAAW,CAAC,eAAe,CAAC,CAAC;gBAC3C,IAAI,CAAC,KAAK,EAAE,CAAC;oBACX,MAAM,IAAI,KAAK,CAAC,yGAAyG,CAAC,CAAC;gBAC7H,CAAC;gBAED,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC;oBACvB,IAAI,CAAC,WAAW,CAAC,qBAAqB,CAAC,EAAE,CAAC;wBACxC,MAAM,IAAI,KAAK,CACb,kIAAkI,CACnI,CAAC;oBACJ,CAAC;oBACD,SAAS,GAAG,WAAW,CAAC,qBAAqB,CAAC,CAAC;gBACjD,CAAC;gBACD,OAAO,KAAK,CAAC;YACf,CAAC,CAAA;QACH,CAAC;QAED,MAAM,KAAK,GAAG,IAAI,gBAAQ,EAAE,CAAC;QAE7B,MAAM,YAAY,GAAsC,OAAO,CAAC;QAChE,YAAY,CAAC,KAAK,GAAG,KAAK,CAAC;QAE3B,KAAK,CAAC,OAAO,CAAC,CAAA;QAEd,KAAK,CAAC,+BAA+B,CAAC;YACpC,mBAAmB,EAAE,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE;gBAChC,IAAI,CAAC,SAAS,EAAE,CAAC;oBACf,MAAM,IAAI,KAAK,CAAC,6CAA6C,CAAC,CAAC;gBACjE,CAAC;gBAED,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,IAAI,EAAE,CAAC;oBACzB,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC,CAAC;gBACnC,CAAC;gBAED,IAAI,IAAI,GAAY,EAAE,CAAC;gBACvB,MAAM,UAAU,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC;gBACtC,IAAI,UAAU,EAAE,CAAC;oBACf,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC,CAAC;gBAC3C,CAAC;gBAED,IAAI,OAAO,IAAI,KAAK,QAAQ,IAAI,CAAC,IAAI,EAAE,CAAC;oBACtC,MAAM,IAAI,KAAK,CAAC,sBAAsB,CAAC,CAAC;gBAC1C,CAAC;gBAED,MAAM,cAAc,GAAG,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,QAAQ,CAAC,kBAAkB,CAAC;oBACpE,CAAC,CAAC,kBAAkB;oBACpB,CAAC,CAAC,YAAY,CAAC;gBAEjB,IAAI,CAAC,CAAC,OAAO,IAAI,IAAI,CAAC,IAAI,OAAO,IAAI,CAAC,KAAK,KAAK,QAAQ,EAAE,CAAC;oBACzD,MAAM,IAAI,KAAK,CAAC,6CAA6C,CAAC,CAAC;gBACjE,CAAC;gBAED,MAAM,CAAC,KAAK,EAAE,OAAO,CAAC,GAAG,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBAElD,IAAI,CAAC,KAAK,IAAI,CAAC,OAAO,EAAE,CAAC;oBACvB,MAAM,IAAI,KAAK,CAAC,6CAA6C,CAAC,CAAC;gBACjE,CAAC;gBAED,KAAK,CAAC,GAAG,CAAC,QAAQ,GAAG,eAAe,SAAS,cAAc,OAAO,CAAC,MAAM,gCAAgC,OAAO,IAAI,cAAc,EAAE,CAAC;gBAErI,IAAI,CAAC,KAAK,GAAG,KAAK,CAAA;gBAElB,KAAK,CAAC,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;gBAE1C,OAAO,KAAK,CAAC;YACf,CAAC;SACF,CAAC,CAAC;IACL,CAAC;IAGD,IAAI,IAAI;QACN,OAAO,CAAC,IAAI,CAAC,KAAK,KAAV,IAAI,CAAC,KAAK,GAAK,IAAI,WAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAC,CAAC;IAClD,CAAC;IAID,IAAI,GAAG;QACL,OAAO,CAAC,IAAI,CAAC,IAAI,KAAT,IAAI,CAAC,IAAI,GAAK,IAAI,SAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAC,CAAC;IAChD,CAAC;CACF;AA/FD,gDA+FC"}