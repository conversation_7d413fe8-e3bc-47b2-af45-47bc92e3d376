// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.aiplatform.v1beta1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/aiplatform/v1beta1/custom_job.proto";
import "google/cloud/aiplatform/v1beta1/encryption_spec.proto";
import "google/cloud/aiplatform/v1beta1/job_state.proto";
import "google/cloud/aiplatform/v1beta1/study.proto";
import "google/protobuf/timestamp.proto";
import "google/rpc/status.proto";

option csharp_namespace = "Google.Cloud.AIPlatform.V1Beta1";
option go_package = "cloud.google.com/go/aiplatform/apiv1beta1/aiplatformpb;aiplatformpb";
option java_multiple_files = true;
option java_outer_classname = "HyperparameterTuningJobProto";
option java_package = "com.google.cloud.aiplatform.v1beta1";
option php_namespace = "Google\\Cloud\\AIPlatform\\V1beta1";
option ruby_package = "Google::Cloud::AIPlatform::V1beta1";

// Represents a HyperparameterTuningJob. A HyperparameterTuningJob
// has a Study specification and multiple CustomJobs with identical
// CustomJob specification.
message HyperparameterTuningJob {
  option (google.api.resource) = {
    type: "aiplatform.googleapis.com/HyperparameterTuningJob"
    pattern: "projects/{project}/locations/{location}/hyperparameterTuningJobs/{hyperparameter_tuning_job}"
  };

  // Output only. Resource name of the HyperparameterTuningJob.
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. The display name of the HyperparameterTuningJob.
  // The name can be up to 128 characters long and can consist of any UTF-8
  // characters.
  string display_name = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. Study configuration of the HyperparameterTuningJob.
  StudySpec study_spec = 4 [(google.api.field_behavior) = REQUIRED];

  // Required. The desired total number of Trials.
  int32 max_trial_count = 5 [(google.api.field_behavior) = REQUIRED];

  // Required. The desired number of Trials to run in parallel.
  int32 parallel_trial_count = 6 [(google.api.field_behavior) = REQUIRED];

  // The number of failed Trials that need to be seen before failing
  // the HyperparameterTuningJob.
  //
  // If set to 0, Vertex AI decides how many Trials must fail
  // before the whole job fails.
  int32 max_failed_trial_count = 7;

  // Required. The spec of a trial job. The same spec applies to the CustomJobs
  // created in all the trials.
  CustomJobSpec trial_job_spec = 8 [(google.api.field_behavior) = REQUIRED];

  // Output only. Trials of the HyperparameterTuningJob.
  repeated Trial trials = 9 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The detailed state of the job.
  JobState state = 10 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Time when the HyperparameterTuningJob was created.
  google.protobuf.Timestamp create_time = 11
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Time when the HyperparameterTuningJob for the first time
  // entered the `JOB_STATE_RUNNING` state.
  google.protobuf.Timestamp start_time = 12
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Time when the HyperparameterTuningJob entered any of the
  // following states: `JOB_STATE_SUCCEEDED`, `JOB_STATE_FAILED`,
  // `JOB_STATE_CANCELLED`.
  google.protobuf.Timestamp end_time = 13
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Time when the HyperparameterTuningJob was most recently
  // updated.
  google.protobuf.Timestamp update_time = 14
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Only populated when job's state is JOB_STATE_FAILED or
  // JOB_STATE_CANCELLED.
  google.rpc.Status error = 15 [(google.api.field_behavior) = OUTPUT_ONLY];

  // The labels with user-defined metadata to organize HyperparameterTuningJobs.
  //
  // Label keys and values can be no longer than 64 characters
  // (Unicode codepoints), can only contain lowercase letters, numeric
  // characters, underscores and dashes. International characters are allowed.
  //
  // See https://goo.gl/xmQnxf for more information and examples of labels.
  map<string, string> labels = 16;

  // Customer-managed encryption key options for a HyperparameterTuningJob.
  // If this is set, then all resources created by the HyperparameterTuningJob
  // will be encrypted with the provided encryption key.
  EncryptionSpec encryption_spec = 17;

  // Output only. Reserved for future use.
  bool satisfies_pzs = 19 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Reserved for future use.
  bool satisfies_pzi = 20 [(google.api.field_behavior) = OUTPUT_ONLY];
}
