{"version": 3, "sources": ["../../src/vectorsearch/index.ts"], "sourcesContent": ["/**\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Genkit } from 'genkit';\nimport { GenkitPlugin, genkitPlugin } from 'genkit/plugin';\nimport { getDerivedParams } from '../common/index.js';\nimport { PluginOptions } from './types.js';\nimport { vertexAiIndexers, vertexAiRetrievers } from './vector_search/index.js';\nexport { type PluginOptions } from '../common/types.js';\nexport {\n  getBigQueryDocumentIndexer,\n  getBigQueryDocumentRetriever,\n  getFirestoreDocumentIndexer,\n  getFirestoreDocumentRetriever,\n  vertexAiIndexerRef,\n  vertexAiIndexers,\n  vertexAiRetrieverRef,\n  vertexAiRetrievers,\n  type DocumentIndexer,\n  type DocumentRetriever,\n  type Neighbor,\n  type VectorSearchOptions,\n} from './vector_search/index.js';\n/**\n * VertexAI vector search plugin\n *\n * ```ts\n * import { vertexAIVectorSearch } from '@genkit-ai/vertexai/vectorsearch';\n *\n * const ai = genkit({\n *   plugins: [\n *     vertexAI({ ... }),\n *     vertexAIVectorSearch({\n        projectId: PROJECT_ID,\n        location: LOCATION,\n        vectorSearchOptions: [\n          {\n            publicDomainName: VECTOR_SEARCH_PUBLIC_DOMAIN_NAME,\n            indexEndpointId: VECTOR_SEARCH_INDEX_ENDPOINT_ID,\n            indexId: VECTOR_SEARCH_INDEX_ID,\n            deployedIndexId: VECTOR_SEARCH_DEPLOYED_INDEX_ID,\n            documentRetriever: VECTOR_SEARCH_DOCUMENT_RETRIEVER,\n            documentIndexer: VECTOR_SEARCH_DOCUMENT_INDEXER,\n            embedder: VECTOR_SEARCH_EMBEDDER,\n          },\n        ],\n      }),\n *   ],\n * });\n *\n * const metadata1 = {\n *   restricts: [{\n *     namespace: \"colour\",\n *     allowList: [\"green\", \"blue, \"purple\"],\n *     denyList:  [\"red\", \"grey\"],\n *   }],\n *   numericRestricts: [\n *   {\n *     namespace: \"price\",\n *     valueFloat: 4199.99,\n *   },\n *   {\n *     namespace: \"weight\",\n *     valueDouble: 987.6543,\n *   },\n *   {\n *     namespace: \"ports\",\n *     valueInt: 3,\n *   },\n * ],\n * }\n * const productDescription1 = \"The 'Synapse Slate' seamlessly integrates neural pathways, allowing users to control applications with thought alone. Its holographic display adapts to any environment, projecting interactive interfaces onto any surface.\"\n * const doc1 = Document.fromText(productDescription1, metadata1);\n *\n * // Index the document along with its restricts and numericRestricts\n * const indexResponse = await ai.index({\n *   indexer: vertexAiIndexerRef({ ... }),\n *   [doc1],\n * });\n *\n *\n * // Later, construct a query using restricts and numeric restricts\n * const queryMetadata = {\n *   restricts: [{\n *     namespace: \"colour\",\n *     allowList: [\"purple\"],\n *     denyList: [\"red\"],\n *   }],\n *   numericRestricts: [{\n *     namespace: \"price\",\n *     valueFloat: 5000.00,\n *     op: LESS,\n *   }]\n * };\n * const query = \"I'm looking for something with a projected display\";\n * const queryDoc = new Document(query, queryMetadata);\n *\n * const response = await ai.retrieve({\n *   retriever: vertexAIRetrieverRef({ ... }),\n *   query: queryDocument,\n *   options: { k },\n * });\n *\n * console.log(`response: ${response}`);\n * ```\n */\nexport function vertexAIVectorSearch(options?: PluginOptions): GenkitPlugin {\n  return genkitPlugin('vertexAIVectorSearch', async (ai: Genkit) => {\n    const { authClient } = await getDerivedParams(options);\n\n    if (\n      options?.vectorSearchOptions &&\n      options.vectorSearchOptions.length > 0\n    ) {\n      vertexAiIndexers(ai, {\n        pluginOptions: options,\n        authClient,\n        defaultEmbedder: options.embedder,\n      });\n\n      vertexAiRetrievers(ai, {\n        pluginOptions: options,\n        authClient,\n        defaultEmbedder: options.embedder,\n      });\n    }\n  });\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAiBA,oBAA2C;AAC3C,oBAAiC;AAEjC,2BAAqD;AAErD,IAAAA,wBAaO;AAoFA,SAAS,qBAAqB,SAAuC;AAC1E,aAAO,4BAAa,wBAAwB,OAAO,OAAe;AAChE,UAAM,EAAE,WAAW,IAAI,UAAM,gCAAiB,OAAO;AAErD,QACE,SAAS,uBACT,QAAQ,oBAAoB,SAAS,GACrC;AACA,iDAAiB,IAAI;AAAA,QACnB,eAAe;AAAA,QACf;AAAA,QACA,iBAAiB,QAAQ;AAAA,MAC3B,CAAC;AAED,mDAAmB,IAAI;AAAA,QACrB,eAAe;AAAA,QACf;AAAA,QACA,iBAAiB,QAAQ;AAAA,MAC3B,CAAC;AAAA,IACH;AAAA,EACF,CAAC;AACH;", "names": ["import_vector_search"]}