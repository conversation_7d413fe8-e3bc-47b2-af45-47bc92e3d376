{"version": 3, "file": "environment.js", "sourceRoot": "", "sources": ["../../../src/utils/environment.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAEH,OAAO,EAAE,YAAY,EAAE,MAAM,oBAAoB,CAAC;AAClD,OAAO,EAAE,mBAAmB,EAAE,MAAM,YAAY,CAAC;AAEjD,MAAM,sBAAsB,GAAG,GAAG,CAAC;AAEnC;;GAEG;AAEH,MAAM,wBAAwB,GAAG,CAAC,mBAAmB,CAAU,CAAC;AAMhE,SAAS,gBAAgB,CAAC,GAAY;IACpC,OAAO,CACL,wBAAwB,CAAC,OAAO,CAAC,GAAiC,CAAC,GAAG,CAAC,CAAC,CACzE,CAAC;AACJ,CAAC;AAED,MAAM,wBAAwB,GAAG;IAC/B,yBAAyB;IACzB,gCAAgC;IAChC,yBAAyB;IACzB,yBAAyB;IACzB,0BAA0B;IAC1B,iCAAiC;IACjC,0BAA0B;IAC1B,0BAA0B;IAC1B,mCAAmC;IACnC,4BAA4B;IAC5B,wCAAwC;IACxC,iCAAiC;IACjC,6CAA6C;IAC7C,sCAAsC;IACtC,6BAA6B;IAC7B,4BAA4B;IAC5B,2CAA2C;IAC3C,0CAA0C;IAC1C,4BAA4B;IAC5B,mCAAmC;IACnC,oCAAoC;IACpC,iCAAiC;IACjC,iCAAiC;CACzB,CAAC;AAMX,SAAS,eAAe,CAAC,GAAY;IACnC,OAAO,CACL,wBAAwB,CAAC,OAAO,CAAC,GAAgC,CAAC,GAAG,CAAC,CAAC,CACxE,CAAC;AACJ,CAAC;AAED,MAAM,sBAAsB,GAAG;IAC7B,uBAAuB;IACvB,kBAAkB;CACV,CAAC;AAMX,SAAS,aAAa,CAAC,GAAY;IACjC,OAAO,sBAAsB,CAAC,OAAO,CAAC,GAA8B,CAAC,GAAG,CAAC,CAAC,CAAC;AAC7E,CAAC;AA8DD,MAAM,CAAC,MAAM,oCAAoC,GAAG,QAAQ,CAAC;AAE7D,MAAM,CAAC,MAAM,6BAA6B,GAAG,GAAG,CAAC;AAEjD,MAAM,CAAC,MAAM,4CAA4C,GAAG,GAAG,CAAC;AAChE,MAAM,CAAC,MAAM,2CAA2C,GAAG,GAAG,CAAC;AAE/D;;GAEG;AACH,MAAM,CAAC,MAAM,mBAAmB,GAA0B;IACxD,iBAAiB,EAAE,KAAK;IACxB,cAAc,EAAE,EAAE;IAClB,6BAA6B,EAAE,EAAE;IACjC,0BAA0B,EAAE,EAAE;IAC9B,QAAQ,EAAE,EAAE;IACZ,uBAAuB,EAAE,EAAE;IAC3B,SAAS,EAAE,EAAE;IACb,uBAAuB,EAAE,KAAK;IAC9B,8BAA8B,EAAE,GAAG;IACnC,uBAAuB,EAAE,IAAI;IAC7B,uBAAuB,EAAE,IAAI;IAC7B,wBAAwB,EAAE,KAAK;IAC/B,+BAA+B,EAAE,GAAG;IACpC,wBAAwB,EAAE,IAAI;IAC9B,wBAAwB,EAAE,IAAI;IAC9B,+BAA+B,EAAE,EAAE;IACnC,+BAA+B,EAAE,IAAI;IACrC,6BAA6B,EAAE,EAAE;IACjC,6BAA6B,EAAE,EAAE;IACjC,yBAAyB,EAAE,EAAE;IAC7B,2BAA2B,EAAE,EAAE;IAC/B,kCAAkC,EAAE,EAAE;IACtC,mCAAmC,EAAE,EAAE;IACvC,gCAAgC,EAAE,EAAE;IACpC,0BAA0B,EAAE,EAAE;IAC9B,iCAAiC,EAAE,EAAE;IACrC,kCAAkC,EAAE,EAAE;IACtC,+BAA+B,EAAE,EAAE;IACnC,0BAA0B,EAAE,KAAK;IACjC,iCAAiC,EAAE,KAAK;IACxC,kCAAkC,EAAE,KAAK;IACzC,+BAA+B,EAAE,KAAK;IACtC,6BAA6B,EAAE,oCAAoC;IACnE,cAAc,EAAE,YAAY,CAAC,IAAI;IACjC,qBAAqB,EAAE,EAAE;IACzB,gBAAgB,EAAE,CAAC,cAAc,EAAE,SAAS,CAAC;IAC7C,wBAAwB,EAAE,EAAE;IAC5B,iBAAiB,EAAE,EAAE;IACrB,iCAAiC,EAAE,oCAAoC;IACvE,0BAA0B,EAAE,6BAA6B;IACzD,sCAAsC,EAAE,oCAAoC;IAC5E,+BAA+B,EAAE,6BAA6B;IAC9D,2CAA2C,EACzC,oCAAoC;IACtC,oCAAoC,EAAE,6BAA6B;IACnE,2BAA2B,EAAE,GAAG;IAChC,0BAA0B,EAAE,GAAG;IAC/B,yCAAyC,EACvC,4CAA4C;IAC9C,wCAAwC,EACtC,2CAA2C;IAC7C,oBAAoB,EAAE,EAAE;IACxB,mBAAmB,EAAE,mBAAmB,CAAC,mBAAmB;IAC5D,uBAAuB,EAAE,EAAE;IAC3B,kBAAkB,EAAE,EAAE;IACtB,2BAA2B,EAAE,EAAE;IAC/B,kCAAkC,EAAE,EAAE;IACtC,mCAAmC,EAAE,EAAE;IACvC,gCAAgC,EAAE,EAAE;IACpC,8BAA8B,EAAE,EAAE;IAClC,qCAAqC,EAAE,EAAE;IACzC,sCAAsC,EAAE,EAAE;IAC1C,mCAAmC,EAAE,EAAE;IACvC,8BAA8B,EAAE,EAAE;IAClC,qCAAqC,EAAE,EAAE;IACzC,sCAAsC,EAAE,EAAE;IAC1C,mCAAmC,EAAE,EAAE;IACvC,6BAA6B,EAAE,EAAE;IACjC,oCAAoC,EAAE,EAAE;IACxC,qCAAqC,EAAE,EAAE;IACzC,kCAAkC,EAAE,EAAE;IACtC,qCAAqC,EAAE,EAAE;IACzC,4CAA4C,EAAE,EAAE;IAChD,6CAA6C,EAAE,EAAE;IACjD,0CAA0C,EAAE,EAAE;IAC9C,2BAA2B,EAAE,eAAe;IAC5C,kCAAkC,EAAE,eAAe;IACnD,mCAAmC,EAAE,eAAe;IACpD,gCAAgC,EAAE,eAAe;IACjD,iDAAiD,EAAE,YAAY;CAChE,CAAC;AAEF;;;;GAIG;AACH,SAAS,YAAY,CACnB,GAA+B,EAC/B,WAAwB,EACxB,MAAuB;IAEvB,IAAI,OAAO,MAAM,CAAC,GAAG,CAAC,KAAK,WAAW,EAAE;QACtC,OAAO;KACR;IAED,MAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;IAClC,kCAAkC;IAClC,WAAW,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,WAAW,EAAE,KAAK,MAAM,CAAC;AACpD,CAAC;AAED;;;;;;;GAOG;AACH,SAAS,WAAW,CAClB,IAA+B,EAC/B,WAAwB,EACxB,MAAuB,EACvB,GAAG,GAAG,CAAC,QAAQ,EACf,GAAG,GAAG,QAAQ;IAEd,IAAI,OAAO,MAAM,CAAC,IAAI,CAAC,KAAK,WAAW,EAAE;QACvC,MAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAW,CAAC,CAAC;QAC7C,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;YACjB,IAAI,KAAK,GAAG,GAAG,EAAE;gBACf,WAAW,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC;aACzB;iBAAM,IAAI,KAAK,GAAG,GAAG,EAAE;gBACtB,WAAW,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC;aACzB;iBAAM;gBACL,WAAW,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC;aAC3B;SACF;KACF;AACH,CAAC;AAED;;;;;;GAMG;AACH,SAAS,eAAe,CACtB,IAA6B,EAC7B,MAAmB,EACnB,KAAsB,EACtB,SAAS,GAAG,sBAAsB;IAElC,MAAM,UAAU,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC;IAC/B,IAAI,OAAO,UAAU,KAAK,QAAQ,EAAE;QAClC,MAAM,CAAC,IAAI,CAAC,GAAG,UAAU,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC;KAC/D;AACH,CAAC;AAED,8CAA8C;AAC9C,MAAM,WAAW,GAAoC;IACnD,GAAG,EAAE,YAAY,CAAC,GAAG;IACrB,OAAO,EAAE,YAAY,CAAC,OAAO;IAC7B,KAAK,EAAE,YAAY,CAAC,KAAK;IACzB,IAAI,EAAE,YAAY,CAAC,IAAI;IACvB,IAAI,EAAE,YAAY,CAAC,IAAI;IACvB,KAAK,EAAE,YAAY,CAAC,KAAK;IACzB,IAAI,EAAE,YAAY,CAAC,IAAI;CACxB,CAAC;AAEF;;;;;GAKG;AACH,SAAS,kBAAkB,CACzB,GAAsB,EACtB,WAA0C,EAC1C,MAAuB;IAEvB,MAAM,KAAK,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC;IAC1B,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;QAC7B,MAAM,QAAQ,GAAG,WAAW,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC,CAAC;QAClD,IAAI,QAAQ,IAAI,IAAI,EAAE;YACpB,WAAW,CAAC,GAAG,CAAC,GAAG,QAAQ,CAAC;SAC7B;KACF;AACH,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,gBAAgB,CAAC,MAAuB;IACtD,MAAM,WAAW,GAAgB,EAAE,CAAC;IAEpC,KAAK,MAAM,GAAG,IAAI,mBAAmB,EAAE;QACrC,MAAM,GAAG,GAAG,GAAwB,CAAC;QAErC,QAAQ,GAAG,EAAE;YACX,KAAK,gBAAgB;gBACnB,kBAAkB,CAAC,GAAG,EAAE,WAAW,EAAE,MAAM,CAAC,CAAC;gBAC7C,MAAM;YAER;gBACE,IAAI,gBAAgB,CAAC,GAAG,CAAC,EAAE;oBACzB,YAAY,CAAC,GAAG,EAAE,WAAW,EAAE,MAAM,CAAC,CAAC;iBACxC;qBAAM,IAAI,eAAe,CAAC,GAAG,CAAC,EAAE;oBAC/B,WAAW,CAAC,GAAG,EAAE,WAAW,EAAE,MAAM,CAAC,CAAC;iBACvC;qBAAM,IAAI,aAAa,CAAC,GAAG,CAAC,EAAE;oBAC7B,eAAe,CAAC,GAAG,EAAE,WAAW,EAAE,MAAM,CAAC,CAAC;iBAC3C;qBAAM;oBACL,MAAM,KAAK,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC;oBAC1B,IAAI,OAAO,KAAK,KAAK,WAAW,IAAI,KAAK,KAAK,IAAI,EAAE;wBAClD,WAAW,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;qBAClC;iBACF;SACJ;KACF;IAED,OAAO,WAAW,CAAC;AACrB,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { DiagLogLevel } from '@opentelemetry/api';\nimport { TracesSamplerValues } from './sampling';\n\nconst DEFAULT_LIST_SEPARATOR = ',';\n\n/**\n * Environment interface to define all names\n */\n\nconst ENVIRONMENT_BOOLEAN_KEYS = ['OTEL_SDK_DISABLED'] as const;\n\ntype ENVIRONMENT_BOOLEANS = {\n  [K in (typeof ENVIRONMENT_BOOLEAN_KEYS)[number]]?: boolean;\n};\n\nfunction isEnvVarABoolean(key: unknown): key is keyof ENVIRONMENT_BOOLEANS {\n  return (\n    ENVIRONMENT_BOOLEAN_KEYS.indexOf(key as keyof ENVIRONMENT_BOOLEANS) > -1\n  );\n}\n\nconst ENVIRONMENT_NUMBERS_KEYS = [\n  'OTEL_BSP_EXPORT_TIMEOUT',\n  'OTEL_BSP_MAX_EXPORT_BATCH_SIZE',\n  'OTEL_BSP_MAX_QUEUE_SIZE',\n  'OTEL_BSP_SCHEDULE_DELAY',\n  'OTEL_BLRP_EXPORT_TIMEOUT',\n  'OTEL_BLRP_MAX_EXPORT_BATCH_SIZE',\n  'OTEL_BLRP_MAX_QUEUE_SIZE',\n  'OTEL_BLRP_SCHEDULE_DELAY',\n  'OTEL_ATTRIBUTE_VALUE_LENGTH_LIMIT',\n  'OTEL_ATTRIBUTE_COUNT_LIMIT',\n  'OTEL_SPAN_ATTRIBUTE_VALUE_LENGTH_LIMIT',\n  'OTEL_SPAN_ATTRIBUTE_COUNT_LIMIT',\n  'OTEL_LOGRECORD_ATTRIBUTE_VALUE_LENGTH_LIMIT',\n  'OTEL_LOGRECORD_ATTRIBUTE_COUNT_LIMIT',\n  'OTEL_SPAN_EVENT_COUNT_LIMIT',\n  'OTEL_SPAN_LINK_COUNT_LIMIT',\n  'OTEL_SPAN_ATTRIBUTE_PER_EVENT_COUNT_LIMIT',\n  'OTEL_SPAN_ATTRIBUTE_PER_LINK_COUNT_LIMIT',\n  'OTEL_EXPORTER_OTLP_TIMEOUT',\n  'OTEL_EXPORTER_OTLP_TRACES_TIMEOUT',\n  'OTEL_EXPORTER_OTLP_METRICS_TIMEOUT',\n  'OTEL_EXPORTER_OTLP_LOGS_TIMEOUT',\n  'OTEL_EXPORTER_JAEGER_AGENT_PORT',\n] as const;\n\ntype ENVIRONMENT_NUMBERS = {\n  [K in (typeof ENVIRONMENT_NUMBERS_KEYS)[number]]?: number;\n};\n\nfunction isEnvVarANumber(key: unknown): key is keyof ENVIRONMENT_NUMBERS {\n  return (\n    ENVIRONMENT_NUMBERS_KEYS.indexOf(key as keyof ENVIRONMENT_NUMBERS) > -1\n  );\n}\n\nconst ENVIRONMENT_LISTS_KEYS = [\n  'OTEL_NO_PATCH_MODULES',\n  'OTEL_PROPAGATORS',\n] as const;\n\ntype ENVIRONMENT_LISTS = {\n  [K in (typeof ENVIRONMENT_LISTS_KEYS)[number]]?: string[];\n};\n\nfunction isEnvVarAList(key: unknown): key is keyof ENVIRONMENT_LISTS {\n  return ENVIRONMENT_LISTS_KEYS.indexOf(key as keyof ENVIRONMENT_LISTS) > -1;\n}\n\nexport type ENVIRONMENT = {\n  CONTAINER_NAME?: string;\n  ECS_CONTAINER_METADATA_URI_V4?: string;\n  ECS_CONTAINER_METADATA_URI?: string;\n  HOSTNAME?: string;\n  KUBERNETES_SERVICE_HOST?: string;\n  NAMESPACE?: string;\n  OTEL_EXPORTER_JAEGER_AGENT_HOST?: string;\n  OTEL_EXPORTER_JAEGER_ENDPOINT?: string;\n  OTEL_EXPORTER_JAEGER_PASSWORD?: string;\n  OTEL_EXPORTER_JAEGER_USER?: string;\n  OTEL_EXPORTER_OTLP_ENDPOINT?: string;\n  OTEL_EXPORTER_OTLP_TRACES_ENDPOINT?: string;\n  OTEL_EXPORTER_OTLP_METRICS_ENDPOINT?: string;\n  OTEL_EXPORTER_OTLP_LOGS_ENDPOINT?: string;\n  OTEL_EXPORTER_OTLP_HEADERS?: string;\n  OTEL_EXPORTER_OTLP_TRACES_HEADERS?: string;\n  OTEL_EXPORTER_OTLP_METRICS_HEADERS?: string;\n  OTEL_EXPORTER_OTLP_LOGS_HEADERS?: string;\n  OTEL_EXPORTER_ZIPKIN_ENDPOINT?: string;\n  OTEL_LOG_LEVEL?: DiagLogLevel;\n  OTEL_RESOURCE_ATTRIBUTES?: string;\n  OTEL_SERVICE_NAME?: string;\n  OTEL_TRACES_EXPORTER?: string;\n  OTEL_TRACES_SAMPLER_ARG?: string;\n  OTEL_TRACES_SAMPLER?: string;\n  OTEL_LOGS_EXPORTER?: string;\n  OTEL_EXPORTER_OTLP_INSECURE?: string;\n  OTEL_EXPORTER_OTLP_TRACES_INSECURE?: string;\n  OTEL_EXPORTER_OTLP_METRICS_INSECURE?: string;\n  OTEL_EXPORTER_OTLP_LOGS_INSECURE?: string;\n  OTEL_EXPORTER_OTLP_CERTIFICATE?: string;\n  OTEL_EXPORTER_OTLP_TRACES_CERTIFICATE?: string;\n  OTEL_EXPORTER_OTLP_METRICS_CERTIFICATE?: string;\n  OTEL_EXPORTER_OTLP_LOGS_CERTIFICATE?: string;\n  OTEL_EXPORTER_OTLP_COMPRESSION?: string;\n  OTEL_EXPORTER_OTLP_TRACES_COMPRESSION?: string;\n  OTEL_EXPORTER_OTLP_METRICS_COMPRESSION?: string;\n  OTEL_EXPORTER_OTLP_LOGS_COMPRESSION?: string;\n  OTEL_EXPORTER_OTLP_CLIENT_KEY?: string;\n  OTEL_EXPORTER_OTLP_TRACES_CLIENT_KEY?: string;\n  OTEL_EXPORTER_OTLP_METRICS_CLIENT_KEY?: string;\n  OTEL_EXPORTER_OTLP_LOGS_CLIENT_KEY?: string;\n  OTEL_EXPORTER_OTLP_CLIENT_CERTIFICATE?: string;\n  OTEL_EXPORTER_OTLP_TRACES_CLIENT_CERTIFICATE?: string;\n  OTEL_EXPORTER_OTLP_METRICS_CLIENT_CERTIFICATE?: string;\n  OTEL_EXPORTER_OTLP_LOGS_CLIENT_CERTIFICATE?: string;\n  OTEL_EXPORTER_OTLP_PROTOCOL?: string;\n  OTEL_EXPORTER_OTLP_TRACES_PROTOCOL?: string;\n  OTEL_EXPORTER_OTLP_METRICS_PROTOCOL?: string;\n  OTEL_EXPORTER_OTLP_LOGS_PROTOCOL?: string;\n  OTEL_EXPORTER_OTLP_METRICS_TEMPORALITY_PREFERENCE?: string;\n} & ENVIRONMENT_BOOLEANS &\n  ENVIRONMENT_NUMBERS &\n  ENVIRONMENT_LISTS;\n\nexport type RAW_ENVIRONMENT = {\n  [key: string]: string | number | undefined | string[];\n};\n\nexport const DEFAULT_ATTRIBUTE_VALUE_LENGTH_LIMIT = Infinity;\n\nexport const DEFAULT_ATTRIBUTE_COUNT_LIMIT = 128;\n\nexport const DEFAULT_SPAN_ATTRIBUTE_PER_EVENT_COUNT_LIMIT = 128;\nexport const DEFAULT_SPAN_ATTRIBUTE_PER_LINK_COUNT_LIMIT = 128;\n\n/**\n * Default environment variables\n */\nexport const DEFAULT_ENVIRONMENT: Required<ENVIRONMENT> = {\n  OTEL_SDK_DISABLED: false,\n  CONTAINER_NAME: '',\n  ECS_CONTAINER_METADATA_URI_V4: '',\n  ECS_CONTAINER_METADATA_URI: '',\n  HOSTNAME: '',\n  KUBERNETES_SERVICE_HOST: '',\n  NAMESPACE: '',\n  OTEL_BSP_EXPORT_TIMEOUT: 30000,\n  OTEL_BSP_MAX_EXPORT_BATCH_SIZE: 512,\n  OTEL_BSP_MAX_QUEUE_SIZE: 2048,\n  OTEL_BSP_SCHEDULE_DELAY: 5000,\n  OTEL_BLRP_EXPORT_TIMEOUT: 30000,\n  OTEL_BLRP_MAX_EXPORT_BATCH_SIZE: 512,\n  OTEL_BLRP_MAX_QUEUE_SIZE: 2048,\n  OTEL_BLRP_SCHEDULE_DELAY: 5000,\n  OTEL_EXPORTER_JAEGER_AGENT_HOST: '',\n  OTEL_EXPORTER_JAEGER_AGENT_PORT: 6832,\n  OTEL_EXPORTER_JAEGER_ENDPOINT: '',\n  OTEL_EXPORTER_JAEGER_PASSWORD: '',\n  OTEL_EXPORTER_JAEGER_USER: '',\n  OTEL_EXPORTER_OTLP_ENDPOINT: '',\n  OTEL_EXPORTER_OTLP_TRACES_ENDPOINT: '',\n  OTEL_EXPORTER_OTLP_METRICS_ENDPOINT: '',\n  OTEL_EXPORTER_OTLP_LOGS_ENDPOINT: '',\n  OTEL_EXPORTER_OTLP_HEADERS: '',\n  OTEL_EXPORTER_OTLP_TRACES_HEADERS: '',\n  OTEL_EXPORTER_OTLP_METRICS_HEADERS: '',\n  OTEL_EXPORTER_OTLP_LOGS_HEADERS: '',\n  OTEL_EXPORTER_OTLP_TIMEOUT: 10000,\n  OTEL_EXPORTER_OTLP_TRACES_TIMEOUT: 10000,\n  OTEL_EXPORTER_OTLP_METRICS_TIMEOUT: 10000,\n  OTEL_EXPORTER_OTLP_LOGS_TIMEOUT: 10000,\n  OTEL_EXPORTER_ZIPKIN_ENDPOINT: 'http://localhost:9411/api/v2/spans',\n  OTEL_LOG_LEVEL: DiagLogLevel.INFO,\n  OTEL_NO_PATCH_MODULES: [],\n  OTEL_PROPAGATORS: ['tracecontext', 'baggage'],\n  OTEL_RESOURCE_ATTRIBUTES: '',\n  OTEL_SERVICE_NAME: '',\n  OTEL_ATTRIBUTE_VALUE_LENGTH_LIMIT: DEFAULT_ATTRIBUTE_VALUE_LENGTH_LIMIT,\n  OTEL_ATTRIBUTE_COUNT_LIMIT: DEFAULT_ATTRIBUTE_COUNT_LIMIT,\n  OTEL_SPAN_ATTRIBUTE_VALUE_LENGTH_LIMIT: DEFAULT_ATTRIBUTE_VALUE_LENGTH_LIMIT,\n  OTEL_SPAN_ATTRIBUTE_COUNT_LIMIT: DEFAULT_ATTRIBUTE_COUNT_LIMIT,\n  OTEL_LOGRECORD_ATTRIBUTE_VALUE_LENGTH_LIMIT:\n    DEFAULT_ATTRIBUTE_VALUE_LENGTH_LIMIT,\n  OTEL_LOGRECORD_ATTRIBUTE_COUNT_LIMIT: DEFAULT_ATTRIBUTE_COUNT_LIMIT,\n  OTEL_SPAN_EVENT_COUNT_LIMIT: 128,\n  OTEL_SPAN_LINK_COUNT_LIMIT: 128,\n  OTEL_SPAN_ATTRIBUTE_PER_EVENT_COUNT_LIMIT:\n    DEFAULT_SPAN_ATTRIBUTE_PER_EVENT_COUNT_LIMIT,\n  OTEL_SPAN_ATTRIBUTE_PER_LINK_COUNT_LIMIT:\n    DEFAULT_SPAN_ATTRIBUTE_PER_LINK_COUNT_LIMIT,\n  OTEL_TRACES_EXPORTER: '',\n  OTEL_TRACES_SAMPLER: TracesSamplerValues.ParentBasedAlwaysOn,\n  OTEL_TRACES_SAMPLER_ARG: '',\n  OTEL_LOGS_EXPORTER: '',\n  OTEL_EXPORTER_OTLP_INSECURE: '',\n  OTEL_EXPORTER_OTLP_TRACES_INSECURE: '',\n  OTEL_EXPORTER_OTLP_METRICS_INSECURE: '',\n  OTEL_EXPORTER_OTLP_LOGS_INSECURE: '',\n  OTEL_EXPORTER_OTLP_CERTIFICATE: '',\n  OTEL_EXPORTER_OTLP_TRACES_CERTIFICATE: '',\n  OTEL_EXPORTER_OTLP_METRICS_CERTIFICATE: '',\n  OTEL_EXPORTER_OTLP_LOGS_CERTIFICATE: '',\n  OTEL_EXPORTER_OTLP_COMPRESSION: '',\n  OTEL_EXPORTER_OTLP_TRACES_COMPRESSION: '',\n  OTEL_EXPORTER_OTLP_METRICS_COMPRESSION: '',\n  OTEL_EXPORTER_OTLP_LOGS_COMPRESSION: '',\n  OTEL_EXPORTER_OTLP_CLIENT_KEY: '',\n  OTEL_EXPORTER_OTLP_TRACES_CLIENT_KEY: '',\n  OTEL_EXPORTER_OTLP_METRICS_CLIENT_KEY: '',\n  OTEL_EXPORTER_OTLP_LOGS_CLIENT_KEY: '',\n  OTEL_EXPORTER_OTLP_CLIENT_CERTIFICATE: '',\n  OTEL_EXPORTER_OTLP_TRACES_CLIENT_CERTIFICATE: '',\n  OTEL_EXPORTER_OTLP_METRICS_CLIENT_CERTIFICATE: '',\n  OTEL_EXPORTER_OTLP_LOGS_CLIENT_CERTIFICATE: '',\n  OTEL_EXPORTER_OTLP_PROTOCOL: 'http/protobuf',\n  OTEL_EXPORTER_OTLP_TRACES_PROTOCOL: 'http/protobuf',\n  OTEL_EXPORTER_OTLP_METRICS_PROTOCOL: 'http/protobuf',\n  OTEL_EXPORTER_OTLP_LOGS_PROTOCOL: 'http/protobuf',\n  OTEL_EXPORTER_OTLP_METRICS_TEMPORALITY_PREFERENCE: 'cumulative',\n};\n\n/**\n * @param key\n * @param environment\n * @param values\n */\nfunction parseBoolean(\n  key: keyof ENVIRONMENT_BOOLEANS,\n  environment: ENVIRONMENT,\n  values: RAW_ENVIRONMENT\n) {\n  if (typeof values[key] === 'undefined') {\n    return;\n  }\n\n  const value = String(values[key]);\n  // support case-insensitive \"true\"\n  environment[key] = value.toLowerCase() === 'true';\n}\n\n/**\n * Parses a variable as number with number validation\n * @param name\n * @param environment\n * @param values\n * @param min\n * @param max\n */\nfunction parseNumber(\n  name: keyof ENVIRONMENT_NUMBERS,\n  environment: ENVIRONMENT,\n  values: RAW_ENVIRONMENT,\n  min = -Infinity,\n  max = Infinity\n) {\n  if (typeof values[name] !== 'undefined') {\n    const value = Number(values[name] as string);\n    if (!isNaN(value)) {\n      if (value < min) {\n        environment[name] = min;\n      } else if (value > max) {\n        environment[name] = max;\n      } else {\n        environment[name] = value;\n      }\n    }\n  }\n}\n\n/**\n * Parses list-like strings from input into output.\n * @param name\n * @param environment\n * @param values\n * @param separator\n */\nfunction parseStringList(\n  name: keyof ENVIRONMENT_LISTS,\n  output: ENVIRONMENT,\n  input: RAW_ENVIRONMENT,\n  separator = DEFAULT_LIST_SEPARATOR\n) {\n  const givenValue = input[name];\n  if (typeof givenValue === 'string') {\n    output[name] = givenValue.split(separator).map(v => v.trim());\n  }\n}\n\n// The support string -> DiagLogLevel mappings\nconst logLevelMap: { [key: string]: DiagLogLevel } = {\n  ALL: DiagLogLevel.ALL,\n  VERBOSE: DiagLogLevel.VERBOSE,\n  DEBUG: DiagLogLevel.DEBUG,\n  INFO: DiagLogLevel.INFO,\n  WARN: DiagLogLevel.WARN,\n  ERROR: DiagLogLevel.ERROR,\n  NONE: DiagLogLevel.NONE,\n};\n\n/**\n * Environmentally sets log level if valid log level string is provided\n * @param key\n * @param environment\n * @param values\n */\nfunction setLogLevelFromEnv(\n  key: keyof ENVIRONMENT,\n  environment: RAW_ENVIRONMENT | ENVIRONMENT,\n  values: RAW_ENVIRONMENT\n) {\n  const value = values[key];\n  if (typeof value === 'string') {\n    const theLevel = logLevelMap[value.toUpperCase()];\n    if (theLevel != null) {\n      environment[key] = theLevel;\n    }\n  }\n}\n\n/**\n * Parses environment values\n * @param values\n */\nexport function parseEnvironment(values: RAW_ENVIRONMENT): ENVIRONMENT {\n  const environment: ENVIRONMENT = {};\n\n  for (const env in DEFAULT_ENVIRONMENT) {\n    const key = env as keyof ENVIRONMENT;\n\n    switch (key) {\n      case 'OTEL_LOG_LEVEL':\n        setLogLevelFromEnv(key, environment, values);\n        break;\n\n      default:\n        if (isEnvVarABoolean(key)) {\n          parseBoolean(key, environment, values);\n        } else if (isEnvVarANumber(key)) {\n          parseNumber(key, environment, values);\n        } else if (isEnvVarAList(key)) {\n          parseStringList(key, environment, values);\n        } else {\n          const value = values[key];\n          if (typeof value !== 'undefined' && value !== null) {\n            environment[key] = String(value);\n          }\n        }\n    }\n  }\n\n  return environment;\n}\n"]}