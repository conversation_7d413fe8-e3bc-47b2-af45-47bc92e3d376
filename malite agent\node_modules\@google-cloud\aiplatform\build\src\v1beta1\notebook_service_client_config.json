{"interfaces": {"google.cloud.aiplatform.v1beta1.NotebookService": {"retry_codes": {"non_idempotent": [], "idempotent": ["DEADLINE_EXCEEDED", "UNAVAILABLE"]}, "retry_params": {"default": {"initial_retry_delay_millis": 100, "retry_delay_multiplier": 1.3, "max_retry_delay_millis": 60000, "initial_rpc_timeout_millis": 60000, "rpc_timeout_multiplier": 1, "max_rpc_timeout_millis": 60000, "total_timeout_millis": 600000}}, "methods": {"CreateNotebookRuntimeTemplate": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "GetNotebookRuntimeTemplate": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "ListNotebookRuntimeTemplates": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "DeleteNotebookRuntimeTemplate": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "UpdateNotebookRuntimeTemplate": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "AssignNotebookRuntime": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "GetNotebookRuntime": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "ListNotebookRuntimes": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "DeleteNotebookRuntime": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "UpgradeNotebookRuntime": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "StartNotebookRuntime": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "StopNotebookRuntime": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "CreateNotebookExecutionJob": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "GetNotebookExecutionJob": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "ListNotebookExecutionJobs": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "DeleteNotebookExecutionJob": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}}}}}