"use strict";
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConnectionError = exports.RequestTimeoutError = exports.RequestAbortedError = exports.InvalidRequestError = exports.UnexpectedClientError = exports.HTTPClientError = void 0;
/**
 * Base class for all HTTP errors.
 */
class HTTPClientError extends Error {
    constructor(message, opts) {
        let msg = message;
        if (opts?.cause) {
            msg += `: ${opts.cause}`;
        }
        super(msg, opts);
        this.name = "HTTPClientError";
        // In older runtimes, the cause field would not have been assigned through
        // the super() call.
        if (typeof this.cause === "undefined") {
            this.cause = opts?.cause;
        }
    }
}
exports.HTTPClientError = HTTPClientError;
/**
 * An error to capture unrecognised or unexpected errors when making HTTP calls.
 */
class UnexpectedClientError extends HTTPClientError {
    constructor() {
        super(...arguments);
        this.name = "UnexpectedClientError";
    }
}
exports.UnexpectedClientError = UnexpectedClientError;
/**
 * An error that is raised when any inputs used to create a request are invalid.
 */
class InvalidRequestError extends HTTPClientError {
    constructor() {
        super(...arguments);
        this.name = "InvalidRequestError";
    }
}
exports.InvalidRequestError = InvalidRequestError;
/**
 * An error that is raised when a HTTP request was aborted by the client error.
 */
class RequestAbortedError extends HTTPClientError {
    constructor() {
        super(...arguments);
        this.name = "RequestAbortedError";
    }
}
exports.RequestAbortedError = RequestAbortedError;
/**
 * An error that is raised when a HTTP request timed out due to an AbortSignal
 * signal timeout.
 */
class RequestTimeoutError extends HTTPClientError {
    constructor() {
        super(...arguments);
        this.name = "RequestTimeoutError";
    }
}
exports.RequestTimeoutError = RequestTimeoutError;
/**
 * An error that is raised when a HTTP client is unable to make a request to
 * a server.
 */
class ConnectionError extends HTTPClientError {
    constructor() {
        super(...arguments);
        this.name = "ConnectionError";
    }
}
exports.ConnectionError = ConnectionError;
//# sourceMappingURL=httpclienterrors.js.map