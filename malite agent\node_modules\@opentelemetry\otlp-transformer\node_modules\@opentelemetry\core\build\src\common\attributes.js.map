{"version": 3, "file": "attributes.js", "sourceRoot": "", "sources": ["../../../src/common/attributes.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,4CAA8E;AAE9E,SAAgB,kBAAkB,CAAC,UAAmB;IACpD,MAAM,GAAG,GAAmB,EAAE,CAAC;IAE/B,IAAI,OAAO,UAAU,KAAK,QAAQ,IAAI,UAAU,IAAI,IAAI,EAAE;QACxD,OAAO,GAAG,CAAC;KACZ;IAED,KAAK,MAAM,CAAC,GAAG,EAAE,GAAG,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE;QACnD,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE;YACxB,UAAI,CAAC,IAAI,CAAC,0BAA0B,GAAG,EAAE,CAAC,CAAC;YAC3C,SAAS;SACV;QACD,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,EAAE;YAC1B,UAAI,CAAC,IAAI,CAAC,wCAAwC,GAAG,EAAE,CAAC,CAAC;YACzD,SAAS;SACV;QACD,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;YACtB,GAAG,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,KAAK,EAAE,CAAC;SACxB;aAAM;YACL,GAAG,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC;SAChB;KACF;IAED,OAAO,GAAG,CAAC;AACb,CAAC;AAxBD,gDAwBC;AAED,SAAgB,cAAc,CAAC,GAAY;IACzC,OAAO,OAAO,GAAG,KAAK,QAAQ,IAAI,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC;AACnD,CAAC;AAFD,wCAEC;AAED,SAAgB,gBAAgB,CAAC,GAAY;IAC3C,IAAI,GAAG,IAAI,IAAI,EAAE;QACf,OAAO,IAAI,CAAC;KACb;IAED,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;QACtB,OAAO,gCAAgC,CAAC,GAAG,CAAC,CAAC;KAC9C;IAED,OAAO,8BAA8B,CAAC,GAAG,CAAC,CAAC;AAC7C,CAAC;AAVD,4CAUC;AAED,SAAS,gCAAgC,CAAC,GAAc;IACtD,IAAI,IAAwB,CAAC;IAE7B,KAAK,MAAM,OAAO,IAAI,GAAG,EAAE;QACzB,sCAAsC;QACtC,IAAI,OAAO,IAAI,IAAI;YAAE,SAAS;QAE9B,IAAI,CAAC,IAAI,EAAE;YACT,IAAI,8BAA8B,CAAC,OAAO,CAAC,EAAE;gBAC3C,IAAI,GAAG,OAAO,OAAO,CAAC;gBACtB,SAAS;aACV;YACD,mCAAmC;YACnC,OAAO,KAAK,CAAC;SACd;QAED,IAAI,OAAO,OAAO,KAAK,IAAI,EAAE;YAC3B,SAAS;SACV;QAED,OAAO,KAAK,CAAC;KACd;IAED,OAAO,IAAI,CAAC;AACd,CAAC;AAED,SAAS,8BAA8B,CAAC,GAAY;IAClD,QAAQ,OAAO,GAAG,EAAE;QAClB,KAAK,QAAQ,CAAC;QACd,KAAK,SAAS,CAAC;QACf,KAAK,QAAQ;YACX,OAAO,IAAI,CAAC;KACf;IAED,OAAO,KAAK,CAAC;AACf,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { diag, SpanAttributeValue, SpanAttributes } from '@opentelemetry/api';\n\nexport function sanitizeAttributes(attributes: unknown): SpanAttributes {\n  const out: SpanAttributes = {};\n\n  if (typeof attributes !== 'object' || attributes == null) {\n    return out;\n  }\n\n  for (const [key, val] of Object.entries(attributes)) {\n    if (!isAttributeKey(key)) {\n      diag.warn(`Invalid attribute key: ${key}`);\n      continue;\n    }\n    if (!isAttributeValue(val)) {\n      diag.warn(`Invalid attribute value set for key: ${key}`);\n      continue;\n    }\n    if (Array.isArray(val)) {\n      out[key] = val.slice();\n    } else {\n      out[key] = val;\n    }\n  }\n\n  return out;\n}\n\nexport function isAttributeKey(key: unknown): key is string {\n  return typeof key === 'string' && key.length > 0;\n}\n\nexport function isAttributeValue(val: unknown): val is SpanAttributeValue {\n  if (val == null) {\n    return true;\n  }\n\n  if (Array.isArray(val)) {\n    return isHomogeneousAttributeValueArray(val);\n  }\n\n  return isValidPrimitiveAttributeValue(val);\n}\n\nfunction isHomogeneousAttributeValueArray(arr: unknown[]): boolean {\n  let type: string | undefined;\n\n  for (const element of arr) {\n    // null/undefined elements are allowed\n    if (element == null) continue;\n\n    if (!type) {\n      if (isValidPrimitiveAttributeValue(element)) {\n        type = typeof element;\n        continue;\n      }\n      // encountered an invalid primitive\n      return false;\n    }\n\n    if (typeof element === type) {\n      continue;\n    }\n\n    return false;\n  }\n\n  return true;\n}\n\nfunction isValidPrimitiveAttributeValue(val: unknown): boolean {\n  switch (typeof val) {\n    case 'number':\n    case 'boolean':\n    case 'string':\n      return true;\n  }\n\n  return false;\n}\n"]}