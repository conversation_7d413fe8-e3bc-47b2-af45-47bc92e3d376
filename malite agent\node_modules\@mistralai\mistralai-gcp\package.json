{"name": "@mistralai/mistralai-gcp", "version": "1.5.0", "author": "Speakeasy", "main": "./index.js", "sideEffects": false, "repository": {"type": "git", "url": "https://github.com/mistralai/client-ts.git", "directory": "packages/mistralai-gcp"}, "scripts": {"lint": "eslint --cache --max-warnings=0 src", "build": "tsc", "prepublishOnly": "npm run build"}, "peerDependencies": {"zod": ">= 3"}, "devDependencies": {"@eslint/js": "^9.19.0", "eslint": "^9.19.0", "globals": "^15.14.0", "typescript": "^5.4.5", "typescript-eslint": "^8.22.0", "zod": "^3.23.4"}, "dependencies": {"google-auth-library": "^9.11.0"}}