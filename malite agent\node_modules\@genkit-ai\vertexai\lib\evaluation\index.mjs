import { genkitPlugin } from "genkit/plugin";
import { getDerivedParams } from "../common/index.js";
import { vertexEvaluators } from "./evaluation.js";
import { VertexAIEvaluationMetricType } from "./types.js";
function vertexAIEvaluation(options) {
  return genkitPlugin("vertexAIEvaluation", async (ai) => {
    const { projectId, location, authClient } = await getDerivedParams(options);
    vertexEvaluators(ai, authClient, options.metrics, projectId, location);
  });
}
export {
  VertexAIEvaluationMetricType,
  vertexAIEvaluation
};
//# sourceMappingURL=index.mjs.map