{"version": 3, "file": "anchored-clock.js", "sourceRoot": "", "sources": ["../../../src/common/anchored-clock.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AASH;;;;;;;;;;;;;;;;GAgBG;AACH,MAAM,OAAO,aAAa;IAKxB;;;;;OAKG;IACH,YAAmB,WAAkB,EAAE,cAAqB;QAC1D,IAAI,CAAC,eAAe,GAAG,cAAc,CAAC;QACtC,IAAI,CAAC,YAAY,GAAG,WAAW,CAAC,GAAG,EAAE,CAAC;QACtC,IAAI,CAAC,kBAAkB,GAAG,cAAc,CAAC,GAAG,EAAE,CAAC;IACjD,CAAC;IAED;;;OAGG;IACI,GAAG;QACR,MAAM,KAAK,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,kBAAkB,CAAC;QACnE,OAAO,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;IACnC,CAAC;CACF", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport interface Clock {\n  /**\n   * Return the current time in milliseconds from some epoch such as the Unix epoch or process start\n   */\n  now(): number;\n}\n\n/**\n * A utility for returning wall times anchored to a given point in time. Wall time measurements will\n * not be taken from the system, but instead are computed by adding a monotonic clock time\n * to the anchor point.\n *\n * This is needed because the system time can change and result in unexpected situations like\n * spans ending before they are started. Creating an anchored clock for each local root span\n * ensures that span timings and durations are accurate while preventing span times from drifting\n * too far from the system clock.\n *\n * Only creating an anchored clock once per local trace ensures span times are correct relative\n * to each other. For example, a child span will never have a start time before its parent even\n * if the system clock is corrected during the local trace.\n *\n * Heavily inspired by the OTel Java anchored clock\n * https://github.com/open-telemetry/opentelemetry-java/blob/main/sdk/trace/src/main/java/io/opentelemetry/sdk/trace/AnchoredClock.java\n */\nexport class AnchoredClock implements Clock {\n  private _monotonicClock: Clock;\n  private _epochMillis: number;\n  private _performanceMillis: number;\n\n  /**\n   * Create a new AnchoredClock anchored to the current time returned by systemClock.\n   *\n   * @param systemClock should be a clock that returns the number of milliseconds since January 1 1970 such as Date\n   * @param monotonicClock should be a clock that counts milliseconds monotonically such as window.performance or perf_hooks.performance\n   */\n  public constructor(systemClock: Clock, monotonicClock: Clock) {\n    this._monotonicClock = monotonicClock;\n    this._epochMillis = systemClock.now();\n    this._performanceMillis = monotonicClock.now();\n  }\n\n  /**\n   * Returns the current time by adding the number of milliseconds since the\n   * AnchoredClock was created to the creation epoch time\n   */\n  public now(): number {\n    const delta = this._monotonicClock.now() - this._performanceMillis;\n    return this._epochMillis + delta;\n  }\n}\n"]}