// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.aiplatform.v1beta1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/aiplatform/v1beta1/machine_resources.proto";
import "google/cloud/aiplatform/v1beta1/model.proto";
import "google/cloud/aiplatform/v1beta1/operation.proto";
import "google/cloud/aiplatform/v1beta1/publisher_model.proto";
import "google/longrunning/operations.proto";

option csharp_namespace = "Google.Cloud.AIPlatform.V1Beta1";
option go_package = "cloud.google.com/go/aiplatform/apiv1beta1/aiplatformpb;aiplatformpb";
option java_multiple_files = true;
option java_outer_classname = "ModelGardenServiceProto";
option java_package = "com.google.cloud.aiplatform.v1beta1";
option php_namespace = "Google\\Cloud\\AIPlatform\\V1beta1";
option ruby_package = "Google::Cloud::AIPlatform::V1beta1";

// The interface of Model Garden Service.
service ModelGardenService {
  option (google.api.default_host) = "aiplatform.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Gets a Model Garden publisher model.
  rpc GetPublisherModel(GetPublisherModelRequest) returns (PublisherModel) {
    option (google.api.http) = {
      get: "/v1beta1/{name=publishers/*/models/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists publisher models in Model Garden.
  rpc ListPublisherModels(ListPublisherModelsRequest)
      returns (ListPublisherModelsResponse) {
    option (google.api.http) = {
      get: "/v1beta1/{parent=publishers/*}/models"
    };
    option (google.api.method_signature) = "parent";
  }

  // Deploys a model to a new endpoint.
  rpc Deploy(DeployRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1beta1/{destination=projects/*/locations/*}:deploy"
      body: "*"
    };
    option (google.longrunning.operation_info) = {
      response_type: "DeployResponse"
      metadata_type: "DeployOperationMetadata"
    };
  }

  // Deploys publisher models.
  rpc DeployPublisherModel(DeployPublisherModelRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1beta1/{destination=projects/*/locations/*}:deploy"
      body: "*"
    };
    option (google.longrunning.operation_info) = {
      response_type: "DeployPublisherModelResponse"
      metadata_type: "DeployPublisherModelOperationMetadata"
    };
  }
}

// View enumeration of PublisherModel.
enum PublisherModelView {
  // The default / unset value. The API will default to the BASIC view.
  PUBLISHER_MODEL_VIEW_UNSPECIFIED = 0;

  // Include basic metadata about the publisher model, but not the full
  // contents.
  PUBLISHER_MODEL_VIEW_BASIC = 1;

  // Include everything.
  PUBLISHER_MODEL_VIEW_FULL = 2;

  // Include: VersionId, ModelVersionExternalName, and SupportedActions.
  PUBLISHER_MODEL_VERSION_VIEW_BASIC = 3;
}

// Request message for
// [ModelGardenService.GetPublisherModel][google.cloud.aiplatform.v1beta1.ModelGardenService.GetPublisherModel]
message GetPublisherModelRequest {
  // Required. The name of the PublisherModel resource.
  // Format:
  // `publishers/{publisher}/models/{publisher_model}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/PublisherModel"
    }
  ];

  // Optional. The IETF BCP-47 language code representing the language in which
  // the publisher model's text information should be written in.
  string language_code = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. PublisherModel view specifying which fields to read.
  PublisherModelView view = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Boolean indicates whether the requested model is a Hugging Face
  // model.
  bool is_hugging_face_model = 5 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Token used to access Hugging Face gated models.
  string hugging_face_token = 6 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for
// [ModelGardenService.ListPublisherModels][google.cloud.aiplatform.v1beta1.ModelGardenService.ListPublisherModels].
message ListPublisherModelsRequest {
  // Required. The name of the Publisher from which to list the PublisherModels.
  // Format: `publishers/{publisher}`
  string parent = 1 [(google.api.field_behavior) = REQUIRED];

  // Optional. The standard list filter.
  string filter = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The standard list page size.
  int32 page_size = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The standard list page token.
  // Typically obtained via
  // [ListPublisherModelsResponse.next_page_token][google.cloud.aiplatform.v1beta1.ListPublisherModelsResponse.next_page_token]
  // of the previous
  // [ModelGardenService.ListPublisherModels][google.cloud.aiplatform.v1beta1.ModelGardenService.ListPublisherModels]
  // call.
  string page_token = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. PublisherModel view specifying which fields to read.
  PublisherModelView view = 5 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A comma-separated list of fields to order by, sorted in ascending
  // order. Use "desc" after a field name for descending.
  string order_by = 6 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The IETF BCP-47 language code representing the language in which
  // the publisher models' text information should be written in. If not set, by
  // default English (en).
  string language_code = 7 [(google.api.field_behavior) = OPTIONAL];

  // Optional. List all publisher model versions if the flag is set to true.
  bool list_all_versions = 8 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for
// [ModelGardenService.ListPublisherModels][google.cloud.aiplatform.v1beta1.ModelGardenService.ListPublisherModels].
message ListPublisherModelsResponse {
  // List of PublisherModels in the requested page.
  repeated PublisherModel publisher_models = 1;

  // A token to retrieve next page of results.
  // Pass to [ListPublisherModels.page_token][] to obtain that page.
  string next_page_token = 2;
}

// Request message for
// [ModelGardenService.Deploy][google.cloud.aiplatform.v1beta1.ModelGardenService.Deploy].
message DeployRequest {
  // The model config to use for the deployment.
  message ModelConfig {
    // Optional. Whether the user accepts the End User License Agreement (EULA)
    // for the model.
    bool accept_eula = 1 [(google.api.field_behavior) = OPTIONAL];

    // Optional. The Hugging Face read access token used to access the model
    // artifacts of gated models.
    string hugging_face_access_token = 2
        [(google.api.field_behavior) = OPTIONAL];

    // Optional. If true, the model will deploy with a cached version instead of
    // directly downloading the model artifacts from Hugging Face. This is
    // suitable for VPC-SC users with limited internet access.
    bool hugging_face_cache_enabled = 3
        [(google.api.field_behavior) = OPTIONAL];

    // Optional. The user-specified display name of the uploaded model. If not
    // set, a default name will be used.
    string model_display_name = 4 [(google.api.field_behavior) = OPTIONAL];

    // Optional. The specification of the container that is to be used when
    // deploying. If not set, the default container spec will be used.
    ModelContainerSpec container_spec = 5
        [(google.api.field_behavior) = OPTIONAL];
  }

  // The endpoint config to use for the deployment.
  message EndpointConfig {
    // Optional. The user-specified display name of the endpoint. If not set, a
    // default name will be used.
    string endpoint_display_name = 1 [(google.api.field_behavior) = OPTIONAL];

    // Optional. If true, the endpoint will be exposed through a dedicated
    // DNS [Endpoint.dedicated_endpoint_dns]. Your request to the dedicated DNS
    // will be isolated from other users' traffic and will have better
    // performance and reliability. Note: Once you enabled dedicated endpoint,
    // you won't be able to send request to the shared DNS
    // {region}-aiplatform.googleapis.com. The limitations will be removed soon.
    bool dedicated_endpoint_enabled = 2
        [(google.api.field_behavior) = OPTIONAL];
  }

  // The deploy config to use for the deployment.
  message DeployConfig {
    // Optional. The dedicated resources to use for the endpoint. If not set,
    // the default resources will be used.
    DedicatedResources dedicated_resources = 1
        [(google.api.field_behavior) = OPTIONAL];

    // Optional. If true, enable the QMT fast tryout feature for this model if
    // possible.
    bool fast_tryout_enabled = 2 [(google.api.field_behavior) = OPTIONAL];
  }

  // The artifacts to deploy.
  oneof artifacts {
    // The Model Garden model to deploy.
    // Format:
    // `publishers/{publisher}/models/{publisher_model}@{version_id}`, or
    // `publishers/hf-{hugging-face-author}/models/{hugging-face-model-name}@001`.
    string publisher_model_name = 1 [(google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/PublisherModel"
    }];

    // The Hugging Face model to deploy.
    // Format: Hugging Face model ID like `google/gemma-2-2b-it`.
    string hugging_face_model_id = 2;
  }

  // Required. The resource name of the Location to deploy the model in.
  // Format: `projects/{project}/locations/{location}`
  string destination = 4 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Optional. The model config to use for the deployment.
  // If not specified, the default model config will be used.
  ModelConfig model_config = 5 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The endpoint config to use for the deployment.
  // If not specified, the default endpoint config will be used.
  EndpointConfig endpoint_config = 6 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The deploy config to use for the deployment.
  // If not specified, the default deploy config will be used.
  DeployConfig deploy_config = 7 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for
// [ModelGardenService.DeployPublisherModel][google.cloud.aiplatform.v1beta1.ModelGardenService.DeployPublisherModel].
message DeployPublisherModelRequest {
  // Required. The name of the PublisherModel resource.
  // Format:
  // `publishers/{publisher}/models/{publisher_model}@{version_id}`, or
  // `publishers/hf-{hugging-face-author}/models/{hugging-face-model-name}@001`
  // or Hugging Face model ID like `google/gemma-2-2b-it`.
  string model = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The resource name of the Location to deploy the model in.
  // Format: `projects/{project}/locations/{location}`
  string destination = 2 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Optional. The user-specified display name of the endpoint. If not set, a
  // default name will be used.
  string endpoint_display_name = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The dedicated resources to use for the endpoint. If not set, the
  // default resources will be used.
  DedicatedResources dedicated_resources = 4
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. The user-specified display name of the uploaded model. If not
  // set, a default name will be used.
  string model_display_name = 5 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The Hugging Face read access token used to access the model
  // artifacts of gated models.
  string hugging_face_access_token = 6 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Whether the user accepts the End User License Agreement (EULA)
  // for the model.
  bool accept_eula = 7 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for
// [ModelGardenService.Deploy][google.cloud.aiplatform.v1beta1.ModelGardenService.Deploy].
message DeployResponse {
  // Output only. The name of the PublisherModel resource.
  // Format:
  // `publishers/{publisher}/models/{publisher_model}@{version_id}`, or
  // `publishers/hf-{hugging-face-author}/models/{hugging-face-model-name}@001`
  string publisher_model = 1 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/PublisherModel"
    }
  ];

  // Output only. The name of the Endpoint created.
  // Format: `projects/{project}/locations/{location}/endpoints/{endpoint}`
  string endpoint = 2 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/Endpoint"
    }
  ];

  // Output only. The name of the Model created.
  // Format: `projects/{project}/locations/{location}/models/{model}`
  string model = 3 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/Model"
    }
  ];
}

// Response message for
// [ModelGardenService.DeployPublisherModel][google.cloud.aiplatform.v1beta1.ModelGardenService.DeployPublisherModel].
message DeployPublisherModelResponse {
  // Output only. The name of the PublisherModel resource.
  // Format:
  // `publishers/{publisher}/models/{publisher_model}@{version_id}`, or
  // `publishers/hf-{hugging-face-author}/models/{hugging-face-model-name}@001`
  string publisher_model = 1 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/PublisherModel"
    }
  ];

  // Output only. The name of the Endpoint created.
  // Format: `projects/{project}/locations/{location}/endpoints/{endpoint}`
  string endpoint = 2 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/Endpoint"
    }
  ];

  // Output only. The name of the Model created.
  // Format: `projects/{project}/locations/{location}/models/{model}`
  string model = 3 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/Model"
    }
  ];
}

// Runtime operation information for
// [ModelGardenService.Deploy][google.cloud.aiplatform.v1beta1.ModelGardenService.Deploy].
message DeployOperationMetadata {
  // The operation generic information.
  GenericOperationMetadata generic_metadata = 1;

  // Output only. The name of the model resource.
  string publisher_model = 2 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/PublisherModel"
    }
  ];

  // Output only. The resource name of the Location to deploy the model in.
  // Format: `projects/{project}/locations/{location}`
  string destination = 3 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Output only. The project number where the deploy model request is sent.
  int64 project_number = 4 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Runtime operation information for
// [ModelGardenService.DeployPublisherModel][google.cloud.aiplatform.v1beta1.ModelGardenService.DeployPublisherModel].
message DeployPublisherModelOperationMetadata {
  // The operation generic information.
  GenericOperationMetadata generic_metadata = 1;

  // Output only. The name of the PublisherModel resource.
  // Format:
  // `publishers/{publisher}/models/{publisher_model}@{version_id}`, or
  // `publishers/hf-{hugging-face-author}/models/{hugging-face-model-name}@001`
  string publisher_model = 2 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/PublisherModel"
    }
  ];

  // Output only. The resource name of the Location to deploy the model in.
  // Format: `projects/{project}/locations/{location}`
  string destination = 3 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Output only. The project number where the deploy model request is sent.
  int64 project_number = 4 [(google.api.field_behavior) = OUTPUT_ONLY];
}
