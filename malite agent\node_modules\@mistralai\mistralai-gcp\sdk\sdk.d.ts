import { SDKOptions } from "../lib/config.js";
import { ClientSDK } from "../lib/sdks.js";
import { Chat } from "./chat";
import { Fim } from "./fim";
export type GoogleCloudOptions = {
    /** The region of the Google Cloud AI Platform endpoint */
    region?: string;
    projectId?: string;
} | {
    /** The region of the Google Cloud AI Platform endpoint */
    region?: string;
    apiKey: () => Promise<string>;
    projectId: string;
};
export declare class MistralGoogleCloud extends ClientSDK {
    constructor(options?: SDKOptions & GoogleCloudOptions);
    private _chat?;
    get chat(): Chat;
    private _fim?;
    get fim(): Fim;
}
//# sourceMappingURL=sdk.d.ts.map