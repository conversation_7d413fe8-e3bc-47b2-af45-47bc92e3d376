{"version": 3, "file": "SemanticAttributes.js", "sourceRoot": "", "sources": ["../../../src/trace/SemanticAttributes.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAEH,OAAO,EAAE,cAAc,EAAE,MAAM,mBAAmB,CAAC;AAEnD,4GAA4G;AAC5G,iHAAiH;AACjH,4GAA4G;AAE5G,4GAA4G;AAC5G,yCAAyC;AACzC,4GAA4G;AAE5G,2FAA2F;AAC3F,oGAAoG;AACpG,IAAM,0BAA0B,GAAG,wBAAwB,CAAC;AAC5D,IAAM,aAAa,GAAG,WAAW,CAAC;AAClC,IAAM,wBAAwB,GAAG,sBAAsB,CAAC;AACxD,IAAM,WAAW,GAAG,SAAS,CAAC;AAC9B,IAAM,4BAA4B,GAAG,0BAA0B,CAAC;AAChE,IAAM,WAAW,GAAG,SAAS,CAAC;AAC9B,IAAM,gBAAgB,GAAG,cAAc,CAAC;AACxC,IAAM,gBAAgB,GAAG,cAAc,CAAC;AACxC,IAAM,0BAA0B,GAAG,wBAAwB,CAAC;AAC5D,IAAM,yBAAyB,GAAG,uBAAuB,CAAC;AAC1D,IAAM,0BAA0B,GAAG,wBAAwB,CAAC;AAC5D,IAAM,kCAAkC,GAAG,gCAAgC,CAAC;AAC5E,IAAM,sBAAsB,GAAG,oBAAoB,CAAC;AACpD,IAAM,4BAA4B,GAAG,0BAA0B,CAAC;AAChE,IAAM,4CAA4C,GAChD,0CAA0C,CAAC;AAC7C,IAAM,+BAA+B,GAAG,6BAA6B,CAAC;AACtE,IAAM,+BAA+B,GAAG,6BAA6B,CAAC;AACtE,IAAM,sBAAsB,GAAG,oBAAoB,CAAC;AACpD,IAAM,2BAA2B,GAAG,yBAAyB,CAAC;AAC9D,IAAM,yBAAyB,GAAG,uBAAuB,CAAC;AAC1D,IAAM,gBAAgB,GAAG,cAAc,CAAC;AACxC,IAAM,kBAAkB,GAAG,gBAAgB,CAAC;AAC5C,IAAM,qBAAqB,GAAG,mBAAmB,CAAC;AAClD,IAAM,wBAAwB,GAAG,sBAAsB,CAAC;AACxD,IAAM,qBAAqB,GAAG,mBAAmB,CAAC;AAClD,IAAM,gBAAgB,GAAG,cAAc,CAAC;AACxC,IAAM,kBAAkB,GAAG,gBAAgB,CAAC;AAC5C,IAAM,4BAA4B,GAAG,0BAA0B,CAAC;AAChE,IAAM,2BAA2B,GAAG,yBAAyB,CAAC;AAC9D,IAAM,sBAAsB,GAAG,oBAAoB,CAAC;AACpD,IAAM,sBAAsB,GAAG,oBAAoB,CAAC;AACpD,IAAM,aAAa,GAAG,WAAW,CAAC;AAClC,IAAM,aAAa,GAAG,WAAW,CAAC;AAClC,IAAM,kBAAkB,GAAG,gBAAgB,CAAC;AAC5C,IAAM,qBAAqB,GAAG,mBAAmB,CAAC;AAClD,IAAM,yBAAyB,GAAG,uBAAuB,CAAC;AAC1D,IAAM,uBAAuB,GAAG,qBAAqB,CAAC;AACtD,IAAM,iBAAiB,GAAG,eAAe,CAAC;AAC1C,IAAM,eAAe,GAAG,aAAa,CAAC;AACtC,IAAM,iBAAiB,GAAG,eAAe,CAAC;AAC1C,IAAM,iBAAiB,GAAG,eAAe,CAAC;AAC1C,IAAM,eAAe,GAAG,aAAa,CAAC;AACtC,IAAM,iBAAiB,GAAG,eAAe,CAAC;AAC1C,IAAM,iBAAiB,GAAG,eAAe,CAAC;AAC1C,IAAM,4BAA4B,GAAG,0BAA0B,CAAC;AAChE,IAAM,+BAA+B,GAAG,6BAA6B,CAAC;AACtE,IAAM,yBAAyB,GAAG,uBAAuB,CAAC;AAC1D,IAAM,wBAAwB,GAAG,sBAAsB,CAAC;AACxD,IAAM,wBAAwB,GAAG,sBAAsB,CAAC;AACxD,IAAM,wBAAwB,GAAG,sBAAsB,CAAC;AACxD,IAAM,gBAAgB,GAAG,cAAc,CAAC;AACxC,IAAM,cAAc,GAAG,YAAY,CAAC;AACpC,IAAM,gBAAgB,GAAG,cAAc,CAAC;AACxC,IAAM,iBAAiB,GAAG,eAAe,CAAC;AAC1C,IAAM,aAAa,GAAG,WAAW,CAAC;AAClC,IAAM,eAAe,GAAG,aAAa,CAAC;AACtC,IAAM,iBAAiB,GAAG,eAAe,CAAC;AAC1C,IAAM,kBAAkB,GAAG,gBAAgB,CAAC;AAC5C,IAAM,iBAAiB,GAAG,eAAe,CAAC;AAC1C,IAAM,eAAe,GAAG,aAAa,CAAC;AACtC,IAAM,eAAe,GAAG,aAAa,CAAC;AACtC,IAAM,YAAY,GAAG,UAAU,CAAC;AAChC,IAAM,eAAe,GAAG,aAAa,CAAC;AACtC,IAAM,aAAa,GAAG,WAAW,CAAC;AAClC,IAAM,eAAe,GAAG,aAAa,CAAC;AACtC,IAAM,oBAAoB,GAAG,kBAAkB,CAAC;AAChD,IAAM,eAAe,GAAG,aAAa,CAAC;AACtC,IAAM,mBAAmB,GAAG,iBAAiB,CAAC;AAC9C,IAAM,+BAA+B,GAAG,6BAA6B,CAAC;AACtE,IAAM,4CAA4C,GAChD,0CAA0C,CAAC;AAC7C,IAAM,gCAAgC,GAAG,8BAA8B,CAAC;AACxE,IAAM,6CAA6C,GACjD,2CAA2C,CAAC;AAC9C,IAAM,oBAAoB,GAAG,kBAAkB,CAAC;AAChD,IAAM,cAAc,GAAG,YAAY,CAAC;AACpC,IAAM,kBAAkB,GAAG,gBAAgB,CAAC;AAC5C,IAAM,4BAA4B,GAAG,0BAA0B,CAAC;AAChE,IAAM,kCAAkC,GAAG,gCAAgC,CAAC;AAC5E,IAAM,wCAAwC,GAC5C,sCAAsC,CAAC;AACzC,IAAM,0CAA0C,GAC9C,wCAAwC,CAAC;AAC3C,IAAM,2CAA2C,GAC/C,yCAAyC,CAAC;AAC5C,IAAM,gCAAgC,GAAG,8BAA8B,CAAC;AACxE,IAAM,2BAA2B,GAAG,yBAAyB,CAAC;AAC9D,IAAM,sBAAsB,GAAG,oBAAoB,CAAC;AACpD,IAAM,kCAAkC,GAAG,gCAAgC,CAAC;AAC5E,IAAM,2BAA2B,GAAG,yBAAyB,CAAC;AAC9D,IAAM,uBAAuB,GAAG,qBAAqB,CAAC;AACtD,IAAM,yCAAyC,GAC7C,uCAAuC,CAAC;AAC1C,IAAM,wCAAwC,GAC5C,sCAAsC,CAAC;AACzC,IAAM,sCAAsC,GAC1C,oCAAoC,CAAC;AACvC,IAAM,4BAA4B,GAAG,0BAA0B,CAAC;AAChE,IAAM,6BAA6B,GAAG,2BAA2B,CAAC;AAClE,IAAM,wBAAwB,GAAG,sBAAsB,CAAC;AACxD,IAAM,+BAA+B,GAAG,6BAA6B,CAAC;AACtE,IAAM,sBAAsB,GAAG,oBAAoB,CAAC;AACpD,IAAM,8BAA8B,GAAG,4BAA4B,CAAC;AACpE,IAAM,sCAAsC,GAC1C,oCAAoC,CAAC;AACvC,IAAM,+CAA+C,GACnD,6CAA6C,CAAC;AAChD,IAAM,oBAAoB,GAAG,kBAAkB,CAAC;AAChD,IAAM,yBAAyB,GAAG,uBAAuB,CAAC;AAC1D,IAAM,8BAA8B,GAAG,4BAA4B,CAAC;AACpE,IAAM,8BAA8B,GAAG,4BAA4B,CAAC;AACpE,IAAM,sBAAsB,GAAG,oBAAoB,CAAC;AACpD,IAAM,8BAA8B,GAAG,4BAA4B,CAAC;AACpE,IAAM,iBAAiB,GAAG,eAAe,CAAC;AAC1C,IAAM,wBAAwB,GAAG,sBAAsB,CAAC;AACxD,IAAM,6BAA6B,GAAG,2BAA2B,CAAC;AAClE,IAAM,wCAAwC,GAC5C,sCAAsC,CAAC;AACzC,IAAM,mDAAmD,GACvD,iDAAiD,CAAC;AACpD,IAAM,uBAAuB,GAAG,qBAAqB,CAAC;AACtD,IAAM,yBAAyB,GAAG,uBAAuB,CAAC;AAC1D,IAAM,kCAAkC,GAAG,gCAAgC,CAAC;AAC5E,IAAM,+BAA+B,GAAG,6BAA6B,CAAC;AACtE,IAAM,kCAAkC,GAAG,gCAAgC,CAAC;AAC5E,IAAM,6BAA6B,GAAG,2BAA2B,CAAC;AAClE,IAAM,6BAA6B,GAAG,2BAA2B,CAAC;AAClE,IAAM,6BAA6B,GAAG,2BAA2B,CAAC;AAClE,IAAM,cAAc,GAAG,YAAY,CAAC;AACpC,IAAM,eAAe,GAAG,aAAa,CAAC;AACtC,IAAM,cAAc,GAAG,YAAY,CAAC;AACpC,IAAM,wBAAwB,GAAG,sBAAsB,CAAC;AACxD,IAAM,uBAAuB,GAAG,qBAAqB,CAAC;AACtD,IAAM,0BAA0B,GAAG,wBAAwB,CAAC;AAC5D,IAAM,0BAA0B,GAAG,wBAAwB,CAAC;AAC5D,IAAM,6BAA6B,GAAG,2BAA2B,CAAC;AAClE,IAAM,gBAAgB,GAAG,cAAc,CAAC;AACxC,IAAM,cAAc,GAAG,YAAY,CAAC;AACpC,IAAM,2BAA2B,GAAG,yBAAyB,CAAC;AAC9D,IAAM,6BAA6B,GAAG,2BAA2B,CAAC;AAElE;;;;GAIG;AACH,MAAM,CAAC,IAAM,+BAA+B,GAAG,0BAA0B,CAAC;AAE1E;;GAEG;AACH,MAAM,CAAC,IAAM,kBAAkB,GAAG,aAAa,CAAC;AAEhD;;GAEG;AACH,MAAM,CAAC,IAAM,6BAA6B,GAAG,wBAAwB,CAAC;AAEtE;;GAEG;AACH,MAAM,CAAC,IAAM,gBAAgB,GAAG,WAAW,CAAC;AAE5C;;GAEG;AACH,MAAM,CAAC,IAAM,iCAAiC,GAAG,4BAA4B,CAAC;AAE9E;;;;GAIG;AACH,MAAM,CAAC,IAAM,gBAAgB,GAAG,WAAW,CAAC;AAE5C;;;;GAIG;AACH,MAAM,CAAC,IAAM,qBAAqB,GAAG,gBAAgB,CAAC;AAEtD;;;;GAIG;AACH,MAAM,CAAC,IAAM,qBAAqB,GAAG,gBAAgB,CAAC;AAEtD;;;;GAIG;AACH,MAAM,CAAC,IAAM,+BAA+B,GAAG,0BAA0B,CAAC;AAE1E;;GAEG;AACH,MAAM,CAAC,IAAM,8BAA8B,GAAG,yBAAyB,CAAC;AAExE;;GAEG;AACH,MAAM,CAAC,IAAM,+BAA+B,GAAG,0BAA0B,CAAC;AAE1E;;GAEG;AACH,MAAM,CAAC,IAAM,uCAAuC,GAClD,kCAAkC,CAAC;AAErC;;;;GAIG;AACH,MAAM,CAAC,IAAM,2BAA2B,GAAG,sBAAsB,CAAC;AAElE;;GAEG;AACH,MAAM,CAAC,IAAM,iCAAiC,GAAG,4BAA4B,CAAC;AAE9E;;GAEG;AACH,MAAM,CAAC,IAAM,iDAAiD,GAC5D,4CAA4C,CAAC;AAE/C;;GAEG;AACH,MAAM,CAAC,IAAM,oCAAoC,GAC/C,+BAA+B,CAAC;AAElC;;GAEG;AACH,MAAM,CAAC,IAAM,oCAAoC,GAC/C,+BAA+B,CAAC;AAElC;;GAEG;AACH,MAAM,CAAC,IAAM,2BAA2B,GAAG,sBAAsB,CAAC;AAElE;;GAEG;AACH,MAAM,CAAC,IAAM,gCAAgC,GAAG,2BAA2B,CAAC;AAE5E;;GAEG;AACH,MAAM,CAAC,IAAM,8BAA8B,GAAG,yBAAyB,CAAC;AAExE;;;;GAIG;AACH,MAAM,CAAC,IAAM,qBAAqB,GAAG,gBAAgB,CAAC;AAEtD;;GAEG;AACH,MAAM,CAAC,IAAM,uBAAuB,GAAG,kBAAkB,CAAC;AAE1D;;GAEG;AACH,MAAM,CAAC,IAAM,0BAA0B,GAAG,qBAAqB,CAAC;AAEhE;;GAEG;AACH,MAAM,CAAC,IAAM,6BAA6B,GAAG,wBAAwB,CAAC;AAEtE;;;;;;;;;;;;;;;;;;;EAmBE;AACF,MAAM,CAAC,IAAM,0BAA0B,GAAG,qBAAqB,CAAC;AAEhE;;GAEG;AACH,MAAM,CAAC,IAAM,qBAAqB,GAAG,gBAAgB,CAAC;AAEtD;;GAEG;AACH,MAAM,CAAC,IAAM,uBAAuB,GAAG,kBAAkB,CAAC;AAE1D;;GAEG;AACH,MAAM,CAAC,IAAM,iCAAiC,GAAG,4BAA4B,CAAC;AAE9E;;GAEG;AACH,MAAM,CAAC,IAAM,gCAAgC,GAAG,2BAA2B,CAAC;AAE5E;;GAEG;AACH,MAAM,CAAC,IAAM,2BAA2B,GAAG,sBAAsB,CAAC;AAElE;;GAEG;AACH,MAAM,CAAC,IAAM,2BAA2B,GAAG,sBAAsB,CAAC;AAElE;;GAEG;AACH,MAAM,CAAC,IAAM,kBAAkB,GAAG,aAAa,CAAC;AAEhD;;GAEG;AACH,MAAM,CAAC,IAAM,kBAAkB,GAAG,aAAa,CAAC;AAEhD;;GAEG;AACH,MAAM,CAAC,IAAM,uBAAuB,GAAG,kBAAkB,CAAC;AAE1D;;;;GAIG;AACH,MAAM,CAAC,IAAM,0BAA0B,GAAG,qBAAqB,CAAC;AAEhE;;;;GAIG;AACH,MAAM,CAAC,IAAM,8BAA8B,GAAG,yBAAyB,CAAC;AAExE;;;;GAIG;AACH,MAAM,CAAC,IAAM,4BAA4B,GAAG,uBAAuB,CAAC;AAEpE;;GAEG;AACH,MAAM,CAAC,IAAM,sBAAsB,GAAG,iBAAiB,CAAC;AAExD;;GAEG;AACH,MAAM,CAAC,IAAM,oBAAoB,GAAG,eAAe,CAAC;AAEpD;;GAEG;AACH,MAAM,CAAC,IAAM,sBAAsB,GAAG,iBAAiB,CAAC;AAExD;;GAEG;AACH,MAAM,CAAC,IAAM,sBAAsB,GAAG,iBAAiB,CAAC;AAExD;;GAEG;AACH,MAAM,CAAC,IAAM,oBAAoB,GAAG,eAAe,CAAC;AAEpD;;GAEG;AACH,MAAM,CAAC,IAAM,sBAAsB,GAAG,iBAAiB,CAAC;AAExD;;GAEG;AACH,MAAM,CAAC,IAAM,sBAAsB,GAAG,iBAAiB,CAAC;AAExD;;GAEG;AACH,MAAM,CAAC,IAAM,iCAAiC,GAAG,4BAA4B,CAAC;AAE9E;;GAEG;AACH,MAAM,CAAC,IAAM,oCAAoC,GAC/C,+BAA+B,CAAC;AAElC;;GAEG;AACH,MAAM,CAAC,IAAM,8BAA8B,GAAG,yBAAyB,CAAC;AAExE;;GAEG;AACH,MAAM,CAAC,IAAM,6BAA6B,GAAG,wBAAwB,CAAC;AAEtE;;GAEG;AACH,MAAM,CAAC,IAAM,6BAA6B,GAAG,wBAAwB,CAAC;AAEtE;;GAEG;AACH,MAAM,CAAC,IAAM,6BAA6B,GAAG,wBAAwB,CAAC;AAEtE;;GAEG;AACH,MAAM,CAAC,IAAM,qBAAqB,GAAG,gBAAgB,CAAC;AAEtD;;GAEG;AACH,MAAM,CAAC,IAAM,mBAAmB,GAAG,cAAc,CAAC;AAElD;;GAEG;AACH,MAAM,CAAC,IAAM,qBAAqB,GAAG,gBAAgB,CAAC;AAEtD;;GAEG;AACH,MAAM,CAAC,IAAM,sBAAsB,GAAG,iBAAiB,CAAC;AAExD;;GAEG;AACH,MAAM,CAAC,IAAM,kBAAkB,GAAG,aAAa,CAAC;AAEhD;;GAEG;AACH,MAAM,CAAC,IAAM,oBAAoB,GAAG,eAAe,CAAC;AAEpD;;GAEG;AACH,MAAM,CAAC,IAAM,sBAAsB,GAAG,iBAAiB,CAAC;AAExD;;GAEG;AACH,MAAM,CAAC,IAAM,uBAAuB,GAAG,kBAAkB,CAAC;AAE1D;;GAEG;AACH,MAAM,CAAC,IAAM,sBAAsB,GAAG,iBAAiB,CAAC;AAExD;;GAEG;AACH,MAAM,CAAC,IAAM,oBAAoB,GAAG,eAAe,CAAC;AAEpD;;GAEG;AACH,MAAM,CAAC,IAAM,oBAAoB,GAAG,eAAe,CAAC;AAEpD;;;;GAIG;AACH,MAAM,CAAC,IAAM,iBAAiB,GAAG,YAAY,CAAC;AAE9C;;GAEG;AACH,MAAM,CAAC,IAAM,oBAAoB,GAAG,eAAe,CAAC;AAEpD;;;;GAIG;AACH,MAAM,CAAC,IAAM,kBAAkB,GAAG,aAAa,CAAC;AAEhD;;GAEG;AACH,MAAM,CAAC,IAAM,oBAAoB,GAAG,eAAe,CAAC;AAEpD;;GAEG;AACH,MAAM,CAAC,IAAM,yBAAyB,GAAG,oBAAoB,CAAC;AAE9D;;;;GAIG;AACH,MAAM,CAAC,IAAM,oBAAoB,GAAG,eAAe,CAAC;AAEpD;;GAEG;AACH,MAAM,CAAC,IAAM,wBAAwB,GAAG,mBAAmB,CAAC;AAE5D;;GAEG;AACH,MAAM,CAAC,IAAM,oCAAoC,GAC/C,+BAA+B,CAAC;AAElC;;GAEG;AACH,MAAM,CAAC,IAAM,iDAAiD,GAC5D,4CAA4C,CAAC;AAE/C;;GAEG;AACH,MAAM,CAAC,IAAM,qCAAqC,GAChD,gCAAgC,CAAC;AAEnC;;GAEG;AACH,MAAM,CAAC,IAAM,kDAAkD,GAC7D,6CAA6C,CAAC;AAEhD;;;;GAIG;AACH,MAAM,CAAC,IAAM,yBAAyB,GAAG,oBAAoB,CAAC;AAE9D;;GAEG;AACH,MAAM,CAAC,IAAM,mBAAmB,GAAG,cAAc,CAAC;AAElD;;;;;;;;;;;;;;EAcE;AACF,MAAM,CAAC,IAAM,uBAAuB,GAAG,kBAAkB,CAAC;AAE1D;;GAEG;AACH,MAAM,CAAC,IAAM,iCAAiC,GAAG,4BAA4B,CAAC;AAE9E;;GAEG;AACH,MAAM,CAAC,IAAM,uCAAuC,GAClD,kCAAkC,CAAC;AAErC;;GAEG;AACH,MAAM,CAAC,IAAM,6CAA6C,GACxD,wCAAwC,CAAC;AAE3C;;GAEG;AACH,MAAM,CAAC,IAAM,+CAA+C,GAC1D,0CAA0C,CAAC;AAE7C;;GAEG;AACH,MAAM,CAAC,IAAM,gDAAgD,GAC3D,2CAA2C,CAAC;AAE9C;;GAEG;AACH,MAAM,CAAC,IAAM,qCAAqC,GAChD,gCAAgC,CAAC;AAEnC;;GAEG;AACH,MAAM,CAAC,IAAM,gCAAgC,GAAG,2BAA2B,CAAC;AAE5E;;GAEG;AACH,MAAM,CAAC,IAAM,2BAA2B,GAAG,sBAAsB,CAAC;AAElE;;GAEG;AACH,MAAM,CAAC,IAAM,uCAAuC,GAClD,kCAAkC,CAAC;AAErC;;GAEG;AACH,MAAM,CAAC,IAAM,gCAAgC,GAAG,2BAA2B,CAAC;AAE5E;;GAEG;AACH,MAAM,CAAC,IAAM,4BAA4B,GAAG,uBAAuB,CAAC;AAEpE;;GAEG;AACH,MAAM,CAAC,IAAM,8CAA8C,GACzD,yCAAyC,CAAC;AAE5C;;GAEG;AACH,MAAM,CAAC,IAAM,6CAA6C,GACxD,wCAAwC,CAAC;AAE3C;;GAEG;AACH,MAAM,CAAC,IAAM,2CAA2C,GACtD,sCAAsC,CAAC;AAEzC;;GAEG;AACH,MAAM,CAAC,IAAM,iCAAiC,GAAG,4BAA4B,CAAC;AAE9E;;GAEG;AACH,MAAM,CAAC,IAAM,kCAAkC,GAAG,6BAA6B,CAAC;AAEhF;;GAEG;AACH,MAAM,CAAC,IAAM,6BAA6B,GAAG,wBAAwB,CAAC;AAEtE;;GAEG;AACH,MAAM,CAAC,IAAM,oCAAoC,GAC/C,+BAA+B,CAAC;AAElC;;GAEG;AACH,MAAM,CAAC,IAAM,2BAA2B,GAAG,sBAAsB,CAAC;AAElE;;GAEG;AACH,MAAM,CAAC,IAAM,mCAAmC,GAC9C,8BAA8B,CAAC;AAEjC;;GAEG;AACH,MAAM,CAAC,IAAM,2CAA2C,GACtD,sCAAsC,CAAC;AAEzC;;GAEG;AACH,MAAM,CAAC,IAAM,oDAAoD,GAC/D,+CAA+C,CAAC;AAElD;;GAEG;AACH,MAAM,CAAC,IAAM,yBAAyB,GAAG,oBAAoB,CAAC;AAE9D;;GAEG;AACH,MAAM,CAAC,IAAM,8BAA8B,GAAG,yBAAyB,CAAC;AAExE;;GAEG;AACH,MAAM,CAAC,IAAM,mCAAmC,GAC9C,8BAA8B,CAAC;AAEjC;;GAEG;AACH,MAAM,CAAC,IAAM,mCAAmC,GAC9C,8BAA8B,CAAC;AAEjC;;GAEG;AACH,MAAM,CAAC,IAAM,2BAA2B,GAAG,sBAAsB,CAAC;AAElE;;GAEG;AACH,MAAM,CAAC,IAAM,mCAAmC,GAC9C,8BAA8B,CAAC;AAEjC;;GAEG;AACH,MAAM,CAAC,IAAM,sBAAsB,GAAG,iBAAiB,CAAC;AAExD;;GAEG;AACH,MAAM,CAAC,IAAM,6BAA6B,GAAG,wBAAwB,CAAC;AAEtE;;GAEG;AACH,MAAM,CAAC,IAAM,kCAAkC,GAAG,6BAA6B,CAAC;AAEhF;;GAEG;AACH,MAAM,CAAC,IAAM,6CAA6C,GACxD,wCAAwC,CAAC;AAE3C;;GAEG;AACH,MAAM,CAAC,IAAM,wDAAwD,GACnE,mDAAmD,CAAC;AAEtD;;GAEG;AACH,MAAM,CAAC,IAAM,4BAA4B,GAAG,uBAAuB,CAAC;AAEpE;;GAEG;AACH,MAAM,CAAC,IAAM,8BAA8B,GAAG,yBAAyB,CAAC;AAExE;;GAEG;AACH,MAAM,CAAC,IAAM,uCAAuC,GAClD,kCAAkC,CAAC;AAErC;;;;GAIG;AACH,MAAM,CAAC,IAAM,oCAAoC,GAC/C,+BAA+B,CAAC;AAElC;;GAEG;AACH,MAAM,CAAC,IAAM,uCAAuC,GAClD,kCAAkC,CAAC;AAErC;;GAEG;AACH,MAAM,CAAC,IAAM,kCAAkC,GAAG,6BAA6B,CAAC;AAEhF;;GAEG;AACH,MAAM,CAAC,IAAM,kCAAkC,GAAG,6BAA6B,CAAC;AAEhF;;GAEG;AACH,MAAM,CAAC,IAAM,kCAAkC,GAAG,6BAA6B,CAAC;AAEhF;;GAEG;AACH,MAAM,CAAC,IAAM,mBAAmB,GAAG,cAAc,CAAC;AAElD;;;;GAIG;AACH,MAAM,CAAC,IAAM,oBAAoB,GAAG,eAAe,CAAC;AAEpD;;;;GAIG;AACH,MAAM,CAAC,IAAM,mBAAmB,GAAG,cAAc,CAAC;AAElD;;GAEG;AACH,MAAM,CAAC,IAAM,6BAA6B,GAAG,wBAAwB,CAAC;AAEtE;;GAEG;AACH,MAAM,CAAC,IAAM,4BAA4B,GAAG,uBAAuB,CAAC;AAEpE;;GAEG;AACH,MAAM,CAAC,IAAM,+BAA+B,GAAG,0BAA0B,CAAC;AAE1E;;GAEG;AACH,MAAM,CAAC,IAAM,+BAA+B,GAAG,0BAA0B,CAAC;AAE1E;;GAEG;AACH,MAAM,CAAC,IAAM,kCAAkC,GAAG,6BAA6B,CAAC;AAEhF;;GAEG;AACH,MAAM,CAAC,IAAM,qBAAqB,GAAG,gBAAgB,CAAC;AAEtD;;;;GAIG;AACH,MAAM,CAAC,IAAM,mBAAmB,GAAG,cAAc,CAAC;AAElD;;GAEG;AACH,MAAM,CAAC,IAAM,gCAAgC,GAAG,2BAA2B,CAAC;AAE5E;;GAEG;AACH,MAAM,CAAC,IAAM,kCAAkC,GAAG,6BAA6B,CAAC;AA4sBhF;;;GAGG;AACH,MAAM,CAAC,IAAM,kBAAkB;AAC7B,aAAa,CAAC,cAAc,CAAqB;IAC/C,0BAA0B;IAC1B,aAAa;IACb,wBAAwB;IACxB,WAAW;IACX,4BAA4B;IAC5B,WAAW;IACX,gBAAgB;IAChB,gBAAgB;IAChB,0BAA0B;IAC1B,yBAAyB;IACzB,0BAA0B;IAC1B,kCAAkC;IAClC,sBAAsB;IACtB,4BAA4B;IAC5B,4CAA4C;IAC5C,+BAA+B;IAC/B,+BAA+B;IAC/B,sBAAsB;IACtB,2BAA2B;IAC3B,yBAAyB;IACzB,gBAAgB;IAChB,kBAAkB;IAClB,qBAAqB;IACrB,wBAAwB;IACxB,qBAAqB;IACrB,gBAAgB;IAChB,kBAAkB;IAClB,4BAA4B;IAC5B,2BAA2B;IAC3B,sBAAsB;IACtB,sBAAsB;IACtB,aAAa;IACb,aAAa;IACb,kBAAkB;IAClB,qBAAqB;IACrB,yBAAyB;IACzB,uBAAuB;IACvB,iBAAiB;IACjB,eAAe;IACf,iBAAiB;IACjB,iBAAiB;IACjB,eAAe;IACf,iBAAiB;IACjB,iBAAiB;IACjB,4BAA4B;IAC5B,+BAA+B;IAC/B,yBAAyB;IACzB,wBAAwB;IACxB,wBAAwB;IACxB,wBAAwB;IACxB,gBAAgB;IAChB,cAAc;IACd,gBAAgB;IAChB,iBAAiB;IACjB,aAAa;IACb,eAAe;IACf,iBAAiB;IACjB,kBAAkB;IAClB,iBAAiB;IACjB,eAAe;IACf,eAAe;IACf,YAAY;IACZ,eAAe;IACf,aAAa;IACb,eAAe;IACf,oBAAoB;IACpB,eAAe;IACf,mBAAmB;IACnB,+BAA+B;IAC/B,4CAA4C;IAC5C,gCAAgC;IAChC,6CAA6C;IAC7C,oBAAoB;IACpB,cAAc;IACd,kBAAkB;IAClB,4BAA4B;IAC5B,kCAAkC;IAClC,wCAAwC;IACxC,0CAA0C;IAC1C,2CAA2C;IAC3C,gCAAgC;IAChC,2BAA2B;IAC3B,sBAAsB;IACtB,kCAAkC;IAClC,2BAA2B;IAC3B,uBAAuB;IACvB,yCAAyC;IACzC,wCAAwC;IACxC,sCAAsC;IACtC,4BAA4B;IAC5B,6BAA6B;IAC7B,wBAAwB;IACxB,+BAA+B;IAC/B,sBAAsB;IACtB,8BAA8B;IAC9B,sCAAsC;IACtC,+CAA+C;IAC/C,oBAAoB;IACpB,yBAAyB;IACzB,8BAA8B;IAC9B,8BAA8B;IAC9B,sBAAsB;IACtB,8BAA8B;IAC9B,iBAAiB;IACjB,wBAAwB;IACxB,6BAA6B;IAC7B,wCAAwC;IACxC,mDAAmD;IACnD,uBAAuB;IACvB,yBAAyB;IACzB,kCAAkC;IAClC,+BAA+B;IAC/B,kCAAkC;IAClC,6BAA6B;IAC7B,6BAA6B;IAC7B,6BAA6B;IAC7B,cAAc;IACd,eAAe;IACf,cAAc;IACd,wBAAwB;IACxB,uBAAuB;IACvB,0BAA0B;IAC1B,0BAA0B;IAC1B,6BAA6B;IAC7B,gBAAgB;IAChB,cAAc;IACd,2BAA2B;IAC3B,6BAA6B;CAC9B,CAAC,CAAC;AAEL;;;;gHAIgH;AAEhH,2FAA2F;AAC3F,oGAAoG;AACpG,IAAM,4BAA4B,GAAG,WAAW,CAAC;AACjD,IAAM,wBAAwB,GAAG,OAAO,CAAC;AACzC,IAAM,wBAAwB,GAAG,OAAO,CAAC;AACzC,IAAM,yBAAyB,GAAG,QAAQ,CAAC;AAC3C,IAAM,sBAAsB,GAAG,KAAK,CAAC;AACrC,IAAM,6BAA6B,GAAG,YAAY,CAAC;AACnD,IAAM,2BAA2B,GAAG,UAAU,CAAC;AAC/C,IAAM,uBAAuB,GAAG,MAAM,CAAC;AACvC,IAAM,6BAA6B,GAAG,YAAY,CAAC;AACnD,IAAM,yBAAyB,GAAG,QAAQ,CAAC;AAC3C,IAAM,2BAA2B,GAAG,UAAU,CAAC;AAC/C,IAAM,wBAAwB,GAAG,OAAO,CAAC;AACzC,IAAM,yBAAyB,GAAG,QAAQ,CAAC;AAC3C,IAAM,yBAAyB,GAAG,QAAQ,CAAC;AAC3C,IAAM,2BAA2B,GAAG,UAAU,CAAC;AAC/C,IAAM,sBAAsB,GAAG,KAAK,CAAC;AACrC,IAAM,wBAAwB,GAAG,OAAO,CAAC;AACzC,IAAM,yBAAyB,GAAG,QAAQ,CAAC;AAC3C,IAAM,2BAA2B,GAAG,UAAU,CAAC;AAC/C,IAAM,wBAAwB,GAAG,OAAO,CAAC;AACzC,IAAM,4BAA4B,GAAG,WAAW,CAAC;AACjD,IAAM,2BAA2B,GAAG,UAAU,CAAC;AAC/C,IAAM,4BAA4B,GAAG,WAAW,CAAC;AACjD,IAAM,4BAA4B,GAAG,WAAW,CAAC;AACjD,IAAM,0BAA0B,GAAG,SAAS,CAAC;AAC7C,IAAM,0BAA0B,GAAG,SAAS,CAAC;AAC7C,IAAM,4BAA4B,GAAG,WAAW,CAAC;AACjD,IAAM,4BAA4B,GAAG,WAAW,CAAC;AACjD,IAAM,yBAAyB,GAAG,QAAQ,CAAC;AAC3C,IAAM,yBAAyB,GAAG,QAAQ,CAAC;AAC3C,IAAM,2BAA2B,GAAG,UAAU,CAAC;AAC/C,IAAM,0BAA0B,GAAG,SAAS,CAAC;AAC7C,IAAM,qBAAqB,GAAG,IAAI,CAAC;AACnC,IAAM,6BAA6B,GAAG,YAAY,CAAC;AACnD,IAAM,4BAA4B,GAAG,WAAW,CAAC;AACjD,IAAM,wBAAwB,GAAG,OAAO,CAAC;AACzC,IAAM,0BAA0B,GAAG,SAAS,CAAC;AAC7C,IAAM,wBAAwB,GAAG,OAAO,CAAC;AACzC,IAAM,4BAA4B,GAAG,WAAW,CAAC;AACjD,IAAM,0BAA0B,GAAG,SAAS,CAAC;AAC7C,IAAM,2BAA2B,GAAG,UAAU,CAAC;AAC/C,IAAM,2BAA2B,GAAG,UAAU,CAAC;AAC/C,IAAM,wBAAwB,GAAG,OAAO,CAAC;AACzC,IAAM,wBAAwB,GAAG,OAAO,CAAC;AACzC,IAAM,gCAAgC,GAAG,eAAe,CAAC;AACzD,IAAM,4BAA4B,GAAG,WAAW,CAAC;AACjD,IAAM,8BAA8B,GAAG,aAAa,CAAC;AAErD;;GAEG;AACH,MAAM,CAAC,IAAM,wBAAwB,GAAG,4BAA4B,CAAC;AAErE;;GAEG;AACH,MAAM,CAAC,IAAM,oBAAoB,GAAG,wBAAwB,CAAC;AAE7D;;GAEG;AACH,MAAM,CAAC,IAAM,oBAAoB,GAAG,wBAAwB,CAAC;AAE7D;;GAEG;AACH,MAAM,CAAC,IAAM,qBAAqB,GAAG,yBAAyB,CAAC;AAE/D;;GAEG;AACH,MAAM,CAAC,IAAM,kBAAkB,GAAG,sBAAsB,CAAC;AAEzD;;GAEG;AACH,MAAM,CAAC,IAAM,yBAAyB,GAAG,6BAA6B,CAAC;AAEvE;;GAEG;AACH,MAAM,CAAC,IAAM,uBAAuB,GAAG,2BAA2B,CAAC;AAEnE;;GAEG;AACH,MAAM,CAAC,IAAM,mBAAmB,GAAG,uBAAuB,CAAC;AAE3D;;GAEG;AACH,MAAM,CAAC,IAAM,yBAAyB,GAAG,6BAA6B,CAAC;AAEvE;;GAEG;AACH,MAAM,CAAC,IAAM,qBAAqB,GAAG,yBAAyB,CAAC;AAE/D;;GAEG;AACH,MAAM,CAAC,IAAM,uBAAuB,GAAG,2BAA2B,CAAC;AAEnE;;GAEG;AACH,MAAM,CAAC,IAAM,oBAAoB,GAAG,wBAAwB,CAAC;AAE7D;;GAEG;AACH,MAAM,CAAC,IAAM,qBAAqB,GAAG,yBAAyB,CAAC;AAE/D;;GAEG;AACH,MAAM,CAAC,IAAM,qBAAqB,GAAG,yBAAyB,CAAC;AAE/D;;GAEG;AACH,MAAM,CAAC,IAAM,uBAAuB,GAAG,2BAA2B,CAAC;AAEnE;;GAEG;AACH,MAAM,CAAC,IAAM,kBAAkB,GAAG,sBAAsB,CAAC;AAEzD;;GAEG;AACH,MAAM,CAAC,IAAM,oBAAoB,GAAG,wBAAwB,CAAC;AAE7D;;GAEG;AACH,MAAM,CAAC,IAAM,qBAAqB,GAAG,yBAAyB,CAAC;AAE/D;;GAEG;AACH,MAAM,CAAC,IAAM,uBAAuB,GAAG,2BAA2B,CAAC;AAEnE;;GAEG;AACH,MAAM,CAAC,IAAM,oBAAoB,GAAG,wBAAwB,CAAC;AAE7D;;GAEG;AACH,MAAM,CAAC,IAAM,wBAAwB,GAAG,4BAA4B,CAAC;AAErE;;GAEG;AACH,MAAM,CAAC,IAAM,uBAAuB,GAAG,2BAA2B,CAAC;AAEnE;;GAEG;AACH,MAAM,CAAC,IAAM,wBAAwB,GAAG,4BAA4B,CAAC;AAErE;;GAEG;AACH,MAAM,CAAC,IAAM,wBAAwB,GAAG,4BAA4B,CAAC;AAErE;;GAEG;AACH,MAAM,CAAC,IAAM,sBAAsB,GAAG,0BAA0B,CAAC;AAEjE;;GAEG;AACH,MAAM,CAAC,IAAM,sBAAsB,GAAG,0BAA0B,CAAC;AAEjE;;GAEG;AACH,MAAM,CAAC,IAAM,wBAAwB,GAAG,4BAA4B,CAAC;AAErE;;GAEG;AACH,MAAM,CAAC,IAAM,wBAAwB,GAAG,4BAA4B,CAAC;AAErE;;GAEG;AACH,MAAM,CAAC,IAAM,qBAAqB,GAAG,yBAAyB,CAAC;AAE/D;;GAEG;AACH,MAAM,CAAC,IAAM,qBAAqB,GAAG,yBAAyB,CAAC;AAE/D;;GAEG;AACH,MAAM,CAAC,IAAM,uBAAuB,GAAG,2BAA2B,CAAC;AAEnE;;GAEG;AACH,MAAM,CAAC,IAAM,sBAAsB,GAAG,0BAA0B,CAAC;AAEjE;;GAEG;AACH,MAAM,CAAC,IAAM,iBAAiB,GAAG,qBAAqB,CAAC;AAEvD;;GAEG;AACH,MAAM,CAAC,IAAM,yBAAyB,GAAG,6BAA6B,CAAC;AAEvE;;GAEG;AACH,MAAM,CAAC,IAAM,wBAAwB,GAAG,4BAA4B,CAAC;AAErE;;GAEG;AACH,MAAM,CAAC,IAAM,oBAAoB,GAAG,wBAAwB,CAAC;AAE7D;;GAEG;AACH,MAAM,CAAC,IAAM,sBAAsB,GAAG,0BAA0B,CAAC;AAEjE;;GAEG;AACH,MAAM,CAAC,IAAM,oBAAoB,GAAG,wBAAwB,CAAC;AAE7D;;GAEG;AACH,MAAM,CAAC,IAAM,wBAAwB,GAAG,4BAA4B,CAAC;AAErE;;GAEG;AACH,MAAM,CAAC,IAAM,sBAAsB,GAAG,0BAA0B,CAAC;AAEjE;;GAEG;AACH,MAAM,CAAC,IAAM,uBAAuB,GAAG,2BAA2B,CAAC;AAEnE;;GAEG;AACH,MAAM,CAAC,IAAM,uBAAuB,GAAG,2BAA2B,CAAC;AAEnE;;GAEG;AACH,MAAM,CAAC,IAAM,oBAAoB,GAAG,wBAAwB,CAAC;AAE7D;;GAEG;AACH,MAAM,CAAC,IAAM,oBAAoB,GAAG,wBAAwB,CAAC;AAE7D;;GAEG;AACH,MAAM,CAAC,IAAM,4BAA4B,GAAG,gCAAgC,CAAC;AAE7E;;GAEG;AACH,MAAM,CAAC,IAAM,wBAAwB,GAAG,4BAA4B,CAAC;AAErE;;GAEG;AACH,MAAM,CAAC,IAAM,0BAA0B,GAAG,8BAA8B,CAAC;AAuJzE;;;GAGG;AACH,MAAM,CAAC,IAAM,cAAc;AACzB,aAAa,CAAC,cAAc,CAAiB;IAC3C,4BAA4B;IAC5B,wBAAwB;IACxB,wBAAwB;IACxB,yBAAyB;IACzB,sBAAsB;IACtB,6BAA6B;IAC7B,2BAA2B;IAC3B,uBAAuB;IACvB,6BAA6B;IAC7B,yBAAyB;IACzB,2BAA2B;IAC3B,wBAAwB;IACxB,yBAAyB;IACzB,yBAAyB;IACzB,2BAA2B;IAC3B,sBAAsB;IACtB,wBAAwB;IACxB,yBAAyB;IACzB,2BAA2B;IAC3B,wBAAwB;IACxB,4BAA4B;IAC5B,2BAA2B;IAC3B,4BAA4B;IAC5B,4BAA4B;IAC5B,0BAA0B;IAC1B,0BAA0B;IAC1B,4BAA4B;IAC5B,4BAA4B;IAC5B,yBAAyB;IACzB,yBAAyB;IACzB,2BAA2B;IAC3B,0BAA0B;IAC1B,qBAAqB;IACrB,6BAA6B;IAC7B,4BAA4B;IAC5B,wBAAwB;IACxB,0BAA0B;IAC1B,wBAAwB;IACxB,4BAA4B;IAC5B,0BAA0B;IAC1B,2BAA2B;IAC3B,2BAA2B;IAC3B,wBAAwB;IACxB,wBAAwB;IACxB,gCAAgC;IAChC,4BAA4B;IAC5B,8BAA8B;CAC/B,CAAC,CAAC;AAEL;;;;gHAIgH;AAEhH,2FAA2F;AAC3F,oGAAoG;AACpG,IAAM,yCAAyC,GAAG,KAAK,CAAC;AACxD,IAAM,iDAAiD,GAAG,aAAa,CAAC;AACxE,IAAM,4CAA4C,GAAG,QAAQ,CAAC;AAC9D,IAAM,kDAAkD,GAAG,cAAc,CAAC;AAC1E,IAAM,yCAAyC,GAAG,KAAK,CAAC;AACxD,IAAM,yCAAyC,GAAG,KAAK,CAAC;AACxD,IAAM,2CAA2C,GAAG,OAAO,CAAC;AAC5D,IAAM,+CAA+C,GAAG,WAAW,CAAC;AACpE,IAAM,yCAAyC,GAAG,KAAK,CAAC;AACxD,IAAM,4CAA4C,GAAG,QAAQ,CAAC;AAC9D,IAAM,kDAAkD,GAAG,cAAc,CAAC;AAE1E;;GAEG;AACH,MAAM,CAAC,IAAM,qCAAqC,GAChD,yCAAyC,CAAC;AAE5C;;GAEG;AACH,MAAM,CAAC,IAAM,6CAA6C,GACxD,iDAAiD,CAAC;AAEpD;;GAEG;AACH,MAAM,CAAC,IAAM,wCAAwC,GACnD,4CAA4C,CAAC;AAE/C;;GAEG;AACH,MAAM,CAAC,IAAM,8CAA8C,GACzD,kDAAkD,CAAC;AAErD;;GAEG;AACH,MAAM,CAAC,IAAM,qCAAqC,GAChD,yCAAyC,CAAC;AAE5C;;GAEG;AACH,MAAM,CAAC,IAAM,qCAAqC,GAChD,yCAAyC,CAAC;AAE5C;;GAEG;AACH,MAAM,CAAC,IAAM,uCAAuC,GAClD,2CAA2C,CAAC;AAE9C;;GAEG;AACH,MAAM,CAAC,IAAM,2CAA2C,GACtD,+CAA+C,CAAC;AAElD;;GAEG;AACH,MAAM,CAAC,IAAM,qCAAqC,GAChD,yCAAyC,CAAC;AAE5C;;GAEG;AACH,MAAM,CAAC,IAAM,wCAAwC,GACnD,4CAA4C,CAAC;AAE/C;;GAEG;AACH,MAAM,CAAC,IAAM,8CAA8C,GACzD,kDAAkD,CAAC;AA2CrD;;;GAGG;AACH,MAAM,CAAC,IAAM,iCAAiC;AAC5C,aAAa,CAAC,cAAc,CAAoC;IAC9D,yCAAyC;IACzC,iDAAiD;IACjD,4CAA4C;IAC5C,kDAAkD;IAClD,yCAAyC;IACzC,yCAAyC;IACzC,2CAA2C;IAC3C,+CAA+C;IAC/C,yCAAyC;IACzC,4CAA4C;IAC5C,kDAAkD;CACnD,CAAC,CAAC;AAEL;;;;gHAIgH;AAEhH,2FAA2F;AAC3F,oGAAoG;AACpG,IAAM,gCAAgC,GAAG,YAAY,CAAC;AACtD,IAAM,0BAA0B,GAAG,MAAM,CAAC;AAC1C,IAAM,4BAA4B,GAAG,QAAQ,CAAC;AAC9C,IAAM,2BAA2B,GAAG,OAAO,CAAC;AAC5C,IAAM,2BAA2B,GAAG,OAAO,CAAC;AAE5C;;GAEG;AACH,MAAM,CAAC,IAAM,4BAA4B,GAAG,gCAAgC,CAAC;AAE7E;;GAEG;AACH,MAAM,CAAC,IAAM,sBAAsB,GAAG,0BAA0B,CAAC;AAEjE;;GAEG;AACH,MAAM,CAAC,IAAM,wBAAwB,GAAG,4BAA4B,CAAC;AAErE;;GAEG;AACH,MAAM,CAAC,IAAM,uBAAuB,GAAG,2BAA2B,CAAC;AAEnE;;GAEG;AACH,MAAM,CAAC,IAAM,uBAAuB,GAAG,2BAA2B,CAAC;AAyBnE;;;GAGG;AACH,MAAM,CAAC,IAAM,iBAAiB;AAC5B,aAAa,CAAC,cAAc,CAAoB;IAC9C,gCAAgC;IAChC,0BAA0B;IAC1B,4BAA4B;IAC5B,2BAA2B;IAC3B,2BAA2B;CAC5B,CAAC,CAAC;AAEL;;;;gHAIgH;AAEhH,2FAA2F;AAC3F,oGAAoG;AACpG,IAAM,sCAAsC,GAAG,QAAQ,CAAC;AACxD,IAAM,oCAAoC,GAAG,MAAM,CAAC;AACpD,IAAM,sCAAsC,GAAG,QAAQ,CAAC;AAExD;;GAEG;AACH,MAAM,CAAC,IAAM,kCAAkC,GAC7C,sCAAsC,CAAC;AAEzC;;GAEG;AACH,MAAM,CAAC,IAAM,gCAAgC,GAC3C,oCAAoC,CAAC;AAEvC;;GAEG;AACH,MAAM,CAAC,IAAM,kCAAkC,GAC7C,sCAAsC,CAAC;AAmBzC;;;GAGG;AACH,MAAM,CAAC,IAAM,2BAA2B;AACtC,aAAa,CAAC,cAAc,CAA8B;IACxD,sCAAsC;IACtC,oCAAoC;IACpC,sCAAsC;CACvC,CAAC,CAAC;AAEL;;;;;;gHAMgH;AAEhH,2FAA2F;AAC3F,oGAAoG;AACpG,IAAM,2CAA2C,GAAG,eAAe,CAAC;AACpE,IAAM,iCAAiC,GAAG,KAAK,CAAC;AAChD,IAAM,mCAAmC,GAAG,OAAO,CAAC;AACpD,IAAM,iCAAiC,GAAG,KAAK,CAAC;AAEhD;;;;GAIG;AACH,MAAM,CAAC,IAAM,uCAAuC,GAClD,2CAA2C,CAAC;AAE9C;;;;GAIG;AACH,MAAM,CAAC,IAAM,6BAA6B,GAAG,iCAAiC,CAAC;AAE/E;;;;GAIG;AACH,MAAM,CAAC,IAAM,+BAA+B,GAC1C,mCAAmC,CAAC;AAEtC;;;;GAIG;AACH,MAAM,CAAC,IAAM,6BAA6B,GAAG,iCAAiC,CAAC;AAwB/E;;;GAGG;AACH,MAAM,CAAC,IAAM,yBAAyB;AACpC,aAAa,CAAC,cAAc,CAA4B;IACtD,2CAA2C;IAC3C,iCAAiC;IACjC,mCAAmC;IACnC,iCAAiC;CAClC,CAAC,CAAC;AAEL;;;;gHAIgH;AAEhH,2FAA2F;AAC3F,oGAAoG;AACpG,IAAM,6BAA6B,GAAG,QAAQ,CAAC;AAC/C,IAAM,6BAA6B,GAAG,QAAQ,CAAC;AAC/C,IAAM,yBAAyB,GAAG,IAAI,CAAC;AACvC,IAAM,2BAA2B,GAAG,MAAM,CAAC;AAC3C,IAAM,2BAA2B,GAAG,MAAM,CAAC;AAC3C,IAAM,6BAA6B,GAAG,QAAQ,CAAC;AAC/C,IAAM,4BAA4B,GAAG,OAAO,CAAC;AAE7C;;GAEG;AACH,MAAM,CAAC,IAAM,yBAAyB,GAAG,6BAA6B,CAAC;AAEvE;;GAEG;AACH,MAAM,CAAC,IAAM,yBAAyB,GAAG,6BAA6B,CAAC;AAEvE;;GAEG;AACH,MAAM,CAAC,IAAM,qBAAqB,GAAG,yBAAyB,CAAC;AAE/D;;GAEG;AACH,MAAM,CAAC,IAAM,uBAAuB,GAAG,2BAA2B,CAAC;AAEnE;;GAEG;AACH,MAAM,CAAC,IAAM,uBAAuB,GAAG,2BAA2B,CAAC;AAEnE;;GAEG;AACH,MAAM,CAAC,IAAM,yBAAyB,GAAG,6BAA6B,CAAC;AAEvE;;GAEG;AACH,MAAM,CAAC,IAAM,wBAAwB,GAAG,4BAA4B,CAAC;AA+BrE;;;GAGG;AACH,MAAM,CAAC,IAAM,kBAAkB;AAC7B,aAAa,CAAC,cAAc,CAAqB;IAC/C,6BAA6B;IAC7B,6BAA6B;IAC7B,yBAAyB;IACzB,2BAA2B;IAC3B,2BAA2B;IAC3B,6BAA6B;IAC7B,4BAA4B;CAC7B,CAAC,CAAC;AAEL;;;;gHAIgH;AAEhH,2FAA2F;AAC3F,oGAAoG;AACpG,IAAM,oCAAoC,GAAG,MAAM,CAAC;AACpD,IAAM,qCAAqC,GAAG,OAAO,CAAC;AACtD,IAAM,oCAAoC,GAAG,MAAM,CAAC;AACpD,IAAM,2CAA2C,GAAG,aAAa,CAAC;AAClE,IAAM,uCAAuC,GAAG,SAAS,CAAC;AAE1D;;GAEG;AACH,MAAM,CAAC,IAAM,gCAAgC,GAC3C,oCAAoC,CAAC;AAEvC;;GAEG;AACH,MAAM,CAAC,IAAM,iCAAiC,GAC5C,qCAAqC,CAAC;AAExC;;GAEG;AACH,MAAM,CAAC,IAAM,gCAAgC,GAC3C,oCAAoC,CAAC;AAEvC;;GAEG;AACH,MAAM,CAAC,IAAM,uCAAuC,GAClD,2CAA2C,CAAC;AAE9C;;GAEG;AACH,MAAM,CAAC,IAAM,mCAAmC,GAC9C,uCAAuC,CAAC;AAyB1C;;;GAGG;AACH,MAAM,CAAC,IAAM,2BAA2B;AACtC,aAAa,CAAC,cAAc,CAA8B;IACxD,oCAAoC;IACpC,qCAAqC;IACrC,oCAAoC;IACpC,2CAA2C;IAC3C,uCAAuC;CACxC,CAAC,CAAC;AAEL;;;;gHAIgH;AAEhH,2FAA2F;AAC3F,oGAAoG;AACpG,IAAM,uCAAuC,GAAG,MAAM,CAAC;AACvD,IAAM,uCAAuC,GAAG,MAAM,CAAC;AACvD,IAAM,uCAAuC,GAAG,MAAM,CAAC;AACvD,IAAM,uCAAuC,GAAG,MAAM,CAAC;AACvD,IAAM,yCAAyC,GAAG,QAAQ,CAAC;AAC3D,IAAM,yCAAyC,GAAG,QAAQ,CAAC;AAC3D,IAAM,iDAAiD,GAAG,gBAAgB,CAAC;AAC3E,IAAM,wCAAwC,GAAG,OAAO,CAAC;AACzD,IAAM,wCAAwC,GAAG,OAAO,CAAC;AACzD,IAAM,uCAAuC,GAAG,MAAM,CAAC;AACvD,IAAM,uCAAuC,GAAG,MAAM,CAAC;AACvD,IAAM,yCAAyC,GAAG,QAAQ,CAAC;AAC3D,IAAM,sCAAsC,GAAG,KAAK,CAAC;AACrD,IAAM,wCAAwC,GAAG,OAAO,CAAC;AACzD,IAAM,wCAAwC,GAAG,OAAO,CAAC;AACzD,IAAM,sCAAsC,GAAG,KAAK,CAAC;AACrD,IAAM,2CAA2C,GAAG,UAAU,CAAC;AAC/D,IAAM,wCAAwC,GAAG,OAAO,CAAC;AACzD,IAAM,qCAAqC,GAAG,IAAI,CAAC;AACnD,IAAM,wCAAwC,GAAG,OAAO,CAAC;AACzD,IAAM,yCAAyC,GAAG,QAAQ,CAAC;AAE3D;;GAEG;AACH,MAAM,CAAC,IAAM,mCAAmC,GAC9C,uCAAuC,CAAC;AAE1C;;GAEG;AACH,MAAM,CAAC,IAAM,mCAAmC,GAC9C,uCAAuC,CAAC;AAE1C;;GAEG;AACH,MAAM,CAAC,IAAM,mCAAmC,GAC9C,uCAAuC,CAAC;AAE1C;;GAEG;AACH,MAAM,CAAC,IAAM,mCAAmC,GAC9C,uCAAuC,CAAC;AAE1C;;GAEG;AACH,MAAM,CAAC,IAAM,qCAAqC,GAChD,yCAAyC,CAAC;AAE5C;;GAEG;AACH,MAAM,CAAC,IAAM,qCAAqC,GAChD,yCAAyC,CAAC;AAE5C;;GAEG;AACH,MAAM,CAAC,IAAM,6CAA6C,GACxD,iDAAiD,CAAC;AAEpD;;GAEG;AACH,MAAM,CAAC,IAAM,oCAAoC,GAC/C,wCAAwC,CAAC;AAE3C;;GAEG;AACH,MAAM,CAAC,IAAM,oCAAoC,GAC/C,wCAAwC,CAAC;AAE3C;;GAEG;AACH,MAAM,CAAC,IAAM,mCAAmC,GAC9C,uCAAuC,CAAC;AAE1C;;GAEG;AACH,MAAM,CAAC,IAAM,mCAAmC,GAC9C,uCAAuC,CAAC;AAE1C;;GAEG;AACH,MAAM,CAAC,IAAM,qCAAqC,GAChD,yCAAyC,CAAC;AAE5C;;GAEG;AACH,MAAM,CAAC,IAAM,kCAAkC,GAC7C,sCAAsC,CAAC;AAEzC;;GAEG;AACH,MAAM,CAAC,IAAM,oCAAoC,GAC/C,wCAAwC,CAAC;AAE3C;;GAEG;AACH,MAAM,CAAC,IAAM,oCAAoC,GAC/C,wCAAwC,CAAC;AAE3C;;GAEG;AACH,MAAM,CAAC,IAAM,kCAAkC,GAC7C,sCAAsC,CAAC;AAEzC;;GAEG;AACH,MAAM,CAAC,IAAM,uCAAuC,GAClD,2CAA2C,CAAC;AAE9C;;GAEG;AACH,MAAM,CAAC,IAAM,oCAAoC,GAC/C,wCAAwC,CAAC;AAE3C;;GAEG;AACH,MAAM,CAAC,IAAM,iCAAiC,GAC5C,qCAAqC,CAAC;AAExC;;GAEG;AACH,MAAM,CAAC,IAAM,oCAAoC,GAC/C,wCAAwC,CAAC;AAE3C;;GAEG;AACH,MAAM,CAAC,IAAM,qCAAqC,GAChD,yCAAyC,CAAC;AAyE5C;;;GAGG;AACH,MAAM,CAAC,IAAM,8BAA8B;AACzC,aAAa,CAAC,cAAc,CAAiC;IAC3D,uCAAuC;IACvC,uCAAuC;IACvC,uCAAuC;IACvC,uCAAuC;IACvC,yCAAyC;IACzC,yCAAyC;IACzC,iDAAiD;IACjD,wCAAwC;IACxC,wCAAwC;IACxC,uCAAuC;IACvC,uCAAuC;IACvC,yCAAyC;IACzC,sCAAsC;IACtC,wCAAwC;IACxC,wCAAwC;IACxC,sCAAsC;IACtC,2CAA2C;IAC3C,wCAAwC;IACxC,qCAAqC;IACrC,wCAAwC;IACxC,yCAAyC;CAC1C,CAAC,CAAC;AAEL;;;;;;gHAMgH;AAEhH,2FAA2F;AAC3F,oGAAoG;AACpG,IAAM,6BAA6B,GAAG,KAAK,CAAC;AAC5C,IAAM,6BAA6B,GAAG,KAAK,CAAC;AAC5C,IAAM,6BAA6B,GAAG,KAAK,CAAC;AAC5C,IAAM,yBAAyB,GAAG,MAAM,CAAC;AACzC,IAAM,yBAAyB,GAAG,MAAM,CAAC;AAEzC;;;;GAIG;AACH,MAAM,CAAC,IAAM,yBAAyB,GAAG,6BAA6B,CAAC;AAEvE;;;;GAIG;AACH,MAAM,CAAC,IAAM,yBAAyB,GAAG,6BAA6B,CAAC;AAEvE;;;;GAIG;AACH,MAAM,CAAC,IAAM,yBAAyB,GAAG,6BAA6B,CAAC;AAEvE;;;;GAIG;AACH,MAAM,CAAC,IAAM,qBAAqB,GAAG,yBAAyB,CAAC;AAE/D;;;;GAIG;AACH,MAAM,CAAC,IAAM,qBAAqB,GAAG,yBAAyB,CAAC;AA2B/D;;;GAGG;AACH,MAAM,CAAC,IAAM,gBAAgB,GAAqB;IAChD,QAAQ,EAAE,6BAA6B;IACvC,QAAQ,EAAE,6BAA6B;IACvC,QAAQ,EAAE,6BAA6B;IACvC,IAAI,EAAE,yBAAyB;IAC/B,IAAI,EAAE,yBAAyB;CAChC,CAAC;AAEF;;;;gHAIgH;AAEhH,2FAA2F;AAC3F,oGAAoG;AACpG,IAAM,wCAAwC,GAAG,OAAO,CAAC;AACzD,IAAM,wCAAwC,GAAG,OAAO,CAAC;AAEzD;;GAEG;AACH,MAAM,CAAC,IAAM,oCAAoC,GAC/C,wCAAwC,CAAC;AAE3C;;GAEG;AACH,MAAM,CAAC,IAAM,oCAAoC,GAC/C,wCAAwC,CAAC;AAgB3C;;;GAGG;AACH,MAAM,CAAC,IAAM,8BAA8B;AACzC,aAAa,CAAC,cAAc,CAAiC;IAC3D,wCAAwC;IACxC,wCAAwC;CACzC,CAAC,CAAC;AAEL;;;;gHAIgH;AAEhH,2FAA2F;AAC3F,oGAAoG;AACpG,IAAM,oCAAoC,GAAG,SAAS,CAAC;AACvD,IAAM,oCAAoC,GAAG,SAAS,CAAC;AAEvD;;GAEG;AACH,MAAM,CAAC,IAAM,gCAAgC,GAC3C,oCAAoC,CAAC;AAEvC;;GAEG;AACH,MAAM,CAAC,IAAM,gCAAgC,GAC3C,oCAAoC,CAAC;AAgBvC;;;GAGG;AACH,MAAM,CAAC,IAAM,wBAAwB;AACnC,aAAa,CAAC,cAAc,CAA2B;IACrD,oCAAoC;IACpC,oCAAoC;CACrC,CAAC,CAAC;AAEL;;;;gHAIgH;AAEhH,2FAA2F;AAC3F,oGAAoG;AACpG,IAAM,8BAA8B,GAAG,CAAC,CAAC;AACzC,IAAM,qCAAqC,GAAG,CAAC,CAAC;AAChD,IAAM,mCAAmC,GAAG,CAAC,CAAC;AAC9C,IAAM,4CAA4C,GAAG,CAAC,CAAC;AACvD,IAAM,6CAA6C,GAAG,CAAC,CAAC;AACxD,IAAM,qCAAqC,GAAG,CAAC,CAAC;AAChD,IAAM,0CAA0C,GAAG,CAAC,CAAC;AACrD,IAAM,6CAA6C,GAAG,CAAC,CAAC;AACxD,IAAM,8CAA8C,GAAG,CAAC,CAAC;AACzD,IAAM,+CAA+C,GAAG,CAAC,CAAC;AAC1D,IAAM,mCAAmC,GAAG,EAAE,CAAC;AAC/C,IAAM,wCAAwC,GAAG,EAAE,CAAC;AACpD,IAAM,yCAAyC,GAAG,EAAE,CAAC;AACrD,IAAM,oCAAoC,GAAG,EAAE,CAAC;AAChD,IAAM,uCAAuC,GAAG,EAAE,CAAC;AACnD,IAAM,qCAAqC,GAAG,EAAE,CAAC;AACjD,IAAM,2CAA2C,GAAG,EAAE,CAAC;AAEvD;;GAEG;AACH,MAAM,CAAC,IAAM,0BAA0B,GAAG,8BAA8B,CAAC;AAEzE;;GAEG;AACH,MAAM,CAAC,IAAM,iCAAiC,GAC5C,qCAAqC,CAAC;AAExC;;GAEG;AACH,MAAM,CAAC,IAAM,+BAA+B,GAC1C,mCAAmC,CAAC;AAEtC;;GAEG;AACH,MAAM,CAAC,IAAM,wCAAwC,GACnD,4CAA4C,CAAC;AAE/C;;GAEG;AACH,MAAM,CAAC,IAAM,yCAAyC,GACpD,6CAA6C,CAAC;AAEhD;;GAEG;AACH,MAAM,CAAC,IAAM,iCAAiC,GAC5C,qCAAqC,CAAC;AAExC;;GAEG;AACH,MAAM,CAAC,IAAM,sCAAsC,GACjD,0CAA0C,CAAC;AAE7C;;GAEG;AACH,MAAM,CAAC,IAAM,yCAAyC,GACpD,6CAA6C,CAAC;AAEhD;;GAEG;AACH,MAAM,CAAC,IAAM,0CAA0C,GACrD,8CAA8C,CAAC;AAEjD;;GAEG;AACH,MAAM,CAAC,IAAM,2CAA2C,GACtD,+CAA+C,CAAC;AAElD;;GAEG;AACH,MAAM,CAAC,IAAM,+BAA+B,GAC1C,mCAAmC,CAAC;AAEtC;;GAEG;AACH,MAAM,CAAC,IAAM,oCAAoC,GAC/C,wCAAwC,CAAC;AAE3C;;GAEG;AACH,MAAM,CAAC,IAAM,qCAAqC,GAChD,yCAAyC,CAAC;AAE5C;;GAEG;AACH,MAAM,CAAC,IAAM,gCAAgC,GAC3C,oCAAoC,CAAC;AAEvC;;GAEG;AACH,MAAM,CAAC,IAAM,mCAAmC,GAC9C,uCAAuC,CAAC;AAE1C;;GAEG;AACH,MAAM,CAAC,IAAM,iCAAiC,GAC5C,qCAAqC,CAAC;AAExC;;GAEG;AACH,MAAM,CAAC,IAAM,uCAAuC,GAClD,2CAA2C,CAAC;AA6D9C;;;GAGG;AACH,MAAM,CAAC,IAAM,uBAAuB,GAA4B;IAC9D,EAAE,EAAE,8BAA8B;IAClC,SAAS,EAAE,qCAAqC;IAChD,OAAO,EAAE,mCAAmC;IAC5C,gBAAgB,EAAE,4CAA4C;IAC9D,iBAAiB,EAAE,6CAA6C;IAChE,SAAS,EAAE,qCAAqC;IAChD,cAAc,EAAE,0CAA0C;IAC1D,iBAAiB,EAAE,6CAA6C;IAChE,kBAAkB,EAAE,8CAA8C;IAClE,mBAAmB,EAAE,+CAA+C;IACpE,OAAO,EAAE,mCAAmC;IAC5C,YAAY,EAAE,wCAAwC;IACtD,aAAa,EAAE,yCAAyC;IACxD,QAAQ,EAAE,oCAAoC;IAC9C,WAAW,EAAE,uCAAuC;IACpD,SAAS,EAAE,qCAAqC;IAChD,eAAe,EAAE,2CAA2C;CAC7D,CAAC;AAEF;;;;gHAIgH;AAEhH,2FAA2F;AAC3F,oGAAoG;AACpG,IAAM,0BAA0B,GAAG,MAAM,CAAC;AAC1C,IAAM,8BAA8B,GAAG,UAAU,CAAC;AAElD;;GAEG;AACH,MAAM,CAAC,IAAM,sBAAsB,GAAG,0BAA0B,CAAC;AAEjE;;GAEG;AACH,MAAM,CAAC,IAAM,0BAA0B,GAAG,8BAA8B,CAAC;AAgBzE;;;GAGG;AACH,MAAM,CAAC,IAAM,iBAAiB;AAC5B,aAAa,CAAC,cAAc,CAAoB;IAC9C,0BAA0B;IAC1B,8BAA8B;CAC/B,CAAC,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { createConstMap } from '../internal/utils';\n\n//----------------------------------------------------------------------------------------------------------\n// DO NOT EDIT, this is an Auto-generated file from scripts/semconv/templates//templates/SemanticAttributes.ts.j2\n//----------------------------------------------------------------------------------------------------------\n\n//----------------------------------------------------------------------------------------------------------\n// Constant values for SemanticAttributes\n//----------------------------------------------------------------------------------------------------------\n\n// Temporary local constants to assign to the individual exports and the namespaced version\n// Required to avoid the namespace exports using the unminifable export names for some package types\nconst TMP_AWS_LAMBDA_INVOKED_ARN = 'aws.lambda.invoked_arn';\nconst TMP_DB_SYSTEM = 'db.system';\nconst TMP_DB_CONNECTION_STRING = 'db.connection_string';\nconst TMP_DB_USER = 'db.user';\nconst TMP_DB_JDBC_DRIVER_CLASSNAME = 'db.jdbc.driver_classname';\nconst TMP_DB_NAME = 'db.name';\nconst TMP_DB_STATEMENT = 'db.statement';\nconst TMP_DB_OPERATION = 'db.operation';\nconst TMP_DB_MSSQL_INSTANCE_NAME = 'db.mssql.instance_name';\nconst TMP_DB_CASSANDRA_KEYSPACE = 'db.cassandra.keyspace';\nconst TMP_DB_CASSANDRA_PAGE_SIZE = 'db.cassandra.page_size';\nconst TMP_DB_CASSANDRA_CONSISTENCY_LEVEL = 'db.cassandra.consistency_level';\nconst TMP_DB_CASSANDRA_TABLE = 'db.cassandra.table';\nconst TMP_DB_CASSANDRA_IDEMPOTENCE = 'db.cassandra.idempotence';\nconst TMP_DB_CASSANDRA_SPECULATIVE_EXECUTION_COUNT =\n  'db.cassandra.speculative_execution_count';\nconst TMP_DB_CASSANDRA_COORDINATOR_ID = 'db.cassandra.coordinator.id';\nconst TMP_DB_CASSANDRA_COORDINATOR_DC = 'db.cassandra.coordinator.dc';\nconst TMP_DB_HBASE_NAMESPACE = 'db.hbase.namespace';\nconst TMP_DB_REDIS_DATABASE_INDEX = 'db.redis.database_index';\nconst TMP_DB_MONGODB_COLLECTION = 'db.mongodb.collection';\nconst TMP_DB_SQL_TABLE = 'db.sql.table';\nconst TMP_EXCEPTION_TYPE = 'exception.type';\nconst TMP_EXCEPTION_MESSAGE = 'exception.message';\nconst TMP_EXCEPTION_STACKTRACE = 'exception.stacktrace';\nconst TMP_EXCEPTION_ESCAPED = 'exception.escaped';\nconst TMP_FAAS_TRIGGER = 'faas.trigger';\nconst TMP_FAAS_EXECUTION = 'faas.execution';\nconst TMP_FAAS_DOCUMENT_COLLECTION = 'faas.document.collection';\nconst TMP_FAAS_DOCUMENT_OPERATION = 'faas.document.operation';\nconst TMP_FAAS_DOCUMENT_TIME = 'faas.document.time';\nconst TMP_FAAS_DOCUMENT_NAME = 'faas.document.name';\nconst TMP_FAAS_TIME = 'faas.time';\nconst TMP_FAAS_CRON = 'faas.cron';\nconst TMP_FAAS_COLDSTART = 'faas.coldstart';\nconst TMP_FAAS_INVOKED_NAME = 'faas.invoked_name';\nconst TMP_FAAS_INVOKED_PROVIDER = 'faas.invoked_provider';\nconst TMP_FAAS_INVOKED_REGION = 'faas.invoked_region';\nconst TMP_NET_TRANSPORT = 'net.transport';\nconst TMP_NET_PEER_IP = 'net.peer.ip';\nconst TMP_NET_PEER_PORT = 'net.peer.port';\nconst TMP_NET_PEER_NAME = 'net.peer.name';\nconst TMP_NET_HOST_IP = 'net.host.ip';\nconst TMP_NET_HOST_PORT = 'net.host.port';\nconst TMP_NET_HOST_NAME = 'net.host.name';\nconst TMP_NET_HOST_CONNECTION_TYPE = 'net.host.connection.type';\nconst TMP_NET_HOST_CONNECTION_SUBTYPE = 'net.host.connection.subtype';\nconst TMP_NET_HOST_CARRIER_NAME = 'net.host.carrier.name';\nconst TMP_NET_HOST_CARRIER_MCC = 'net.host.carrier.mcc';\nconst TMP_NET_HOST_CARRIER_MNC = 'net.host.carrier.mnc';\nconst TMP_NET_HOST_CARRIER_ICC = 'net.host.carrier.icc';\nconst TMP_PEER_SERVICE = 'peer.service';\nconst TMP_ENDUSER_ID = 'enduser.id';\nconst TMP_ENDUSER_ROLE = 'enduser.role';\nconst TMP_ENDUSER_SCOPE = 'enduser.scope';\nconst TMP_THREAD_ID = 'thread.id';\nconst TMP_THREAD_NAME = 'thread.name';\nconst TMP_CODE_FUNCTION = 'code.function';\nconst TMP_CODE_NAMESPACE = 'code.namespace';\nconst TMP_CODE_FILEPATH = 'code.filepath';\nconst TMP_CODE_LINENO = 'code.lineno';\nconst TMP_HTTP_METHOD = 'http.method';\nconst TMP_HTTP_URL = 'http.url';\nconst TMP_HTTP_TARGET = 'http.target';\nconst TMP_HTTP_HOST = 'http.host';\nconst TMP_HTTP_SCHEME = 'http.scheme';\nconst TMP_HTTP_STATUS_CODE = 'http.status_code';\nconst TMP_HTTP_FLAVOR = 'http.flavor';\nconst TMP_HTTP_USER_AGENT = 'http.user_agent';\nconst TMP_HTTP_REQUEST_CONTENT_LENGTH = 'http.request_content_length';\nconst TMP_HTTP_REQUEST_CONTENT_LENGTH_UNCOMPRESSED =\n  'http.request_content_length_uncompressed';\nconst TMP_HTTP_RESPONSE_CONTENT_LENGTH = 'http.response_content_length';\nconst TMP_HTTP_RESPONSE_CONTENT_LENGTH_UNCOMPRESSED =\n  'http.response_content_length_uncompressed';\nconst TMP_HTTP_SERVER_NAME = 'http.server_name';\nconst TMP_HTTP_ROUTE = 'http.route';\nconst TMP_HTTP_CLIENT_IP = 'http.client_ip';\nconst TMP_AWS_DYNAMODB_TABLE_NAMES = 'aws.dynamodb.table_names';\nconst TMP_AWS_DYNAMODB_CONSUMED_CAPACITY = 'aws.dynamodb.consumed_capacity';\nconst TMP_AWS_DYNAMODB_ITEM_COLLECTION_METRICS =\n  'aws.dynamodb.item_collection_metrics';\nconst TMP_AWS_DYNAMODB_PROVISIONED_READ_CAPACITY =\n  'aws.dynamodb.provisioned_read_capacity';\nconst TMP_AWS_DYNAMODB_PROVISIONED_WRITE_CAPACITY =\n  'aws.dynamodb.provisioned_write_capacity';\nconst TMP_AWS_DYNAMODB_CONSISTENT_READ = 'aws.dynamodb.consistent_read';\nconst TMP_AWS_DYNAMODB_PROJECTION = 'aws.dynamodb.projection';\nconst TMP_AWS_DYNAMODB_LIMIT = 'aws.dynamodb.limit';\nconst TMP_AWS_DYNAMODB_ATTRIBUTES_TO_GET = 'aws.dynamodb.attributes_to_get';\nconst TMP_AWS_DYNAMODB_INDEX_NAME = 'aws.dynamodb.index_name';\nconst TMP_AWS_DYNAMODB_SELECT = 'aws.dynamodb.select';\nconst TMP_AWS_DYNAMODB_GLOBAL_SECONDARY_INDEXES =\n  'aws.dynamodb.global_secondary_indexes';\nconst TMP_AWS_DYNAMODB_LOCAL_SECONDARY_INDEXES =\n  'aws.dynamodb.local_secondary_indexes';\nconst TMP_AWS_DYNAMODB_EXCLUSIVE_START_TABLE =\n  'aws.dynamodb.exclusive_start_table';\nconst TMP_AWS_DYNAMODB_TABLE_COUNT = 'aws.dynamodb.table_count';\nconst TMP_AWS_DYNAMODB_SCAN_FORWARD = 'aws.dynamodb.scan_forward';\nconst TMP_AWS_DYNAMODB_SEGMENT = 'aws.dynamodb.segment';\nconst TMP_AWS_DYNAMODB_TOTAL_SEGMENTS = 'aws.dynamodb.total_segments';\nconst TMP_AWS_DYNAMODB_COUNT = 'aws.dynamodb.count';\nconst TMP_AWS_DYNAMODB_SCANNED_COUNT = 'aws.dynamodb.scanned_count';\nconst TMP_AWS_DYNAMODB_ATTRIBUTE_DEFINITIONS =\n  'aws.dynamodb.attribute_definitions';\nconst TMP_AWS_DYNAMODB_GLOBAL_SECONDARY_INDEX_UPDATES =\n  'aws.dynamodb.global_secondary_index_updates';\nconst TMP_MESSAGING_SYSTEM = 'messaging.system';\nconst TMP_MESSAGING_DESTINATION = 'messaging.destination';\nconst TMP_MESSAGING_DESTINATION_KIND = 'messaging.destination_kind';\nconst TMP_MESSAGING_TEMP_DESTINATION = 'messaging.temp_destination';\nconst TMP_MESSAGING_PROTOCOL = 'messaging.protocol';\nconst TMP_MESSAGING_PROTOCOL_VERSION = 'messaging.protocol_version';\nconst TMP_MESSAGING_URL = 'messaging.url';\nconst TMP_MESSAGING_MESSAGE_ID = 'messaging.message_id';\nconst TMP_MESSAGING_CONVERSATION_ID = 'messaging.conversation_id';\nconst TMP_MESSAGING_MESSAGE_PAYLOAD_SIZE_BYTES =\n  'messaging.message_payload_size_bytes';\nconst TMP_MESSAGING_MESSAGE_PAYLOAD_COMPRESSED_SIZE_BYTES =\n  'messaging.message_payload_compressed_size_bytes';\nconst TMP_MESSAGING_OPERATION = 'messaging.operation';\nconst TMP_MESSAGING_CONSUMER_ID = 'messaging.consumer_id';\nconst TMP_MESSAGING_RABBITMQ_ROUTING_KEY = 'messaging.rabbitmq.routing_key';\nconst TMP_MESSAGING_KAFKA_MESSAGE_KEY = 'messaging.kafka.message_key';\nconst TMP_MESSAGING_KAFKA_CONSUMER_GROUP = 'messaging.kafka.consumer_group';\nconst TMP_MESSAGING_KAFKA_CLIENT_ID = 'messaging.kafka.client_id';\nconst TMP_MESSAGING_KAFKA_PARTITION = 'messaging.kafka.partition';\nconst TMP_MESSAGING_KAFKA_TOMBSTONE = 'messaging.kafka.tombstone';\nconst TMP_RPC_SYSTEM = 'rpc.system';\nconst TMP_RPC_SERVICE = 'rpc.service';\nconst TMP_RPC_METHOD = 'rpc.method';\nconst TMP_RPC_GRPC_STATUS_CODE = 'rpc.grpc.status_code';\nconst TMP_RPC_JSONRPC_VERSION = 'rpc.jsonrpc.version';\nconst TMP_RPC_JSONRPC_REQUEST_ID = 'rpc.jsonrpc.request_id';\nconst TMP_RPC_JSONRPC_ERROR_CODE = 'rpc.jsonrpc.error_code';\nconst TMP_RPC_JSONRPC_ERROR_MESSAGE = 'rpc.jsonrpc.error_message';\nconst TMP_MESSAGE_TYPE = 'message.type';\nconst TMP_MESSAGE_ID = 'message.id';\nconst TMP_MESSAGE_COMPRESSED_SIZE = 'message.compressed_size';\nconst TMP_MESSAGE_UNCOMPRESSED_SIZE = 'message.uncompressed_size';\n\n/**\n * The full invoked ARN as provided on the `Context` passed to the function (`Lambda-Runtime-Invoked-Function-Arn` header on the `/runtime/invocation/next` applicable).\n *\n * Note: This may be different from `faas.id` if an alias is involved.\n */\nexport const SEMATTRS_AWS_LAMBDA_INVOKED_ARN = TMP_AWS_LAMBDA_INVOKED_ARN;\n\n/**\n * An identifier for the database management system (DBMS) product being used. See below for a list of well-known identifiers.\n */\nexport const SEMATTRS_DB_SYSTEM = TMP_DB_SYSTEM;\n\n/**\n * The connection string used to connect to the database. It is recommended to remove embedded credentials.\n */\nexport const SEMATTRS_DB_CONNECTION_STRING = TMP_DB_CONNECTION_STRING;\n\n/**\n * Username for accessing the database.\n */\nexport const SEMATTRS_DB_USER = TMP_DB_USER;\n\n/**\n * The fully-qualified class name of the [Java Database Connectivity (JDBC)](https://docs.oracle.com/javase/8/docs/technotes/guides/jdbc/) driver used to connect.\n */\nexport const SEMATTRS_DB_JDBC_DRIVER_CLASSNAME = TMP_DB_JDBC_DRIVER_CLASSNAME;\n\n/**\n * If no [tech-specific attribute](#call-level-attributes-for-specific-technologies) is defined, this attribute is used to report the name of the database being accessed. For commands that switch the database, this should be set to the target database (even if the command fails).\n *\n * Note: In some SQL databases, the database name to be used is called &#34;schema name&#34;.\n */\nexport const SEMATTRS_DB_NAME = TMP_DB_NAME;\n\n/**\n * The database statement being executed.\n *\n * Note: The value may be sanitized to exclude sensitive information.\n */\nexport const SEMATTRS_DB_STATEMENT = TMP_DB_STATEMENT;\n\n/**\n * The name of the operation being executed, e.g. the [MongoDB command name](https://docs.mongodb.com/manual/reference/command/#database-operations) such as `findAndModify`, or the SQL keyword.\n *\n * Note: When setting this to an SQL keyword, it is not recommended to attempt any client-side parsing of `db.statement` just to get this property, but it should be set if the operation name is provided by the library being instrumented. If the SQL statement has an ambiguous operation, or performs more than one operation, this value may be omitted.\n */\nexport const SEMATTRS_DB_OPERATION = TMP_DB_OPERATION;\n\n/**\n * The Microsoft SQL Server [instance name](https://docs.microsoft.com/en-us/sql/connect/jdbc/building-the-connection-url?view=sql-server-ver15) connecting to. This name is used to determine the port of a named instance.\n *\n * Note: If setting a `db.mssql.instance_name`, `net.peer.port` is no longer required (but still recommended if non-standard).\n */\nexport const SEMATTRS_DB_MSSQL_INSTANCE_NAME = TMP_DB_MSSQL_INSTANCE_NAME;\n\n/**\n * The name of the keyspace being accessed. To be used instead of the generic `db.name` attribute.\n */\nexport const SEMATTRS_DB_CASSANDRA_KEYSPACE = TMP_DB_CASSANDRA_KEYSPACE;\n\n/**\n * The fetch size used for paging, i.e. how many rows will be returned at once.\n */\nexport const SEMATTRS_DB_CASSANDRA_PAGE_SIZE = TMP_DB_CASSANDRA_PAGE_SIZE;\n\n/**\n * The consistency level of the query. Based on consistency values from [CQL](https://docs.datastax.com/en/cassandra-oss/3.0/cassandra/dml/dmlConfigConsistency.html).\n */\nexport const SEMATTRS_DB_CASSANDRA_CONSISTENCY_LEVEL =\n  TMP_DB_CASSANDRA_CONSISTENCY_LEVEL;\n\n/**\n * The name of the primary table that the operation is acting upon, including the schema name (if applicable).\n *\n * Note: This mirrors the db.sql.table attribute but references cassandra rather than sql. It is not recommended to attempt any client-side parsing of `db.statement` just to get this property, but it should be set if it is provided by the library being instrumented. If the operation is acting upon an anonymous table, or more than one table, this value MUST NOT be set.\n */\nexport const SEMATTRS_DB_CASSANDRA_TABLE = TMP_DB_CASSANDRA_TABLE;\n\n/**\n * Whether or not the query is idempotent.\n */\nexport const SEMATTRS_DB_CASSANDRA_IDEMPOTENCE = TMP_DB_CASSANDRA_IDEMPOTENCE;\n\n/**\n * The number of times a query was speculatively executed. Not set or `0` if the query was not executed speculatively.\n */\nexport const SEMATTRS_DB_CASSANDRA_SPECULATIVE_EXECUTION_COUNT =\n  TMP_DB_CASSANDRA_SPECULATIVE_EXECUTION_COUNT;\n\n/**\n * The ID of the coordinating node for a query.\n */\nexport const SEMATTRS_DB_CASSANDRA_COORDINATOR_ID =\n  TMP_DB_CASSANDRA_COORDINATOR_ID;\n\n/**\n * The data center of the coordinating node for a query.\n */\nexport const SEMATTRS_DB_CASSANDRA_COORDINATOR_DC =\n  TMP_DB_CASSANDRA_COORDINATOR_DC;\n\n/**\n * The [HBase namespace](https://hbase.apache.org/book.html#_namespace) being accessed. To be used instead of the generic `db.name` attribute.\n */\nexport const SEMATTRS_DB_HBASE_NAMESPACE = TMP_DB_HBASE_NAMESPACE;\n\n/**\n * The index of the database being accessed as used in the [`SELECT` command](https://redis.io/commands/select), provided as an integer. To be used instead of the generic `db.name` attribute.\n */\nexport const SEMATTRS_DB_REDIS_DATABASE_INDEX = TMP_DB_REDIS_DATABASE_INDEX;\n\n/**\n * The collection being accessed within the database stated in `db.name`.\n */\nexport const SEMATTRS_DB_MONGODB_COLLECTION = TMP_DB_MONGODB_COLLECTION;\n\n/**\n * The name of the primary table that the operation is acting upon, including the schema name (if applicable).\n *\n * Note: It is not recommended to attempt any client-side parsing of `db.statement` just to get this property, but it should be set if it is provided by the library being instrumented. If the operation is acting upon an anonymous table, or more than one table, this value MUST NOT be set.\n */\nexport const SEMATTRS_DB_SQL_TABLE = TMP_DB_SQL_TABLE;\n\n/**\n * The type of the exception (its fully-qualified class name, if applicable). The dynamic type of the exception should be preferred over the static type in languages that support it.\n */\nexport const SEMATTRS_EXCEPTION_TYPE = TMP_EXCEPTION_TYPE;\n\n/**\n * The exception message.\n */\nexport const SEMATTRS_EXCEPTION_MESSAGE = TMP_EXCEPTION_MESSAGE;\n\n/**\n * A stacktrace as a string in the natural representation for the language runtime. The representation is to be determined and documented by each language SIG.\n */\nexport const SEMATTRS_EXCEPTION_STACKTRACE = TMP_EXCEPTION_STACKTRACE;\n\n/**\n* SHOULD be set to true if the exception event is recorded at a point where it is known that the exception is escaping the scope of the span.\n*\n* Note: An exception is considered to have escaped (or left) the scope of a span,\nif that span is ended while the exception is still logically &#34;in flight&#34;.\nThis may be actually &#34;in flight&#34; in some languages (e.g. if the exception\nis passed to a Context manager&#39;s `__exit__` method in Python) but will\nusually be caught at the point of recording the exception in most languages.\n\nIt is usually not possible to determine at the point where an exception is thrown\nwhether it will escape the scope of a span.\nHowever, it is trivial to know that an exception\nwill escape, if one checks for an active exception just before ending the span,\nas done in the [example above](#exception-end-example).\n\nIt follows that an exception may still escape the scope of the span\neven if the `exception.escaped` attribute was not set or set to false,\nsince the event might have been recorded at a time where it was not\nclear whether the exception will escape.\n*/\nexport const SEMATTRS_EXCEPTION_ESCAPED = TMP_EXCEPTION_ESCAPED;\n\n/**\n * Type of the trigger on which the function is executed.\n */\nexport const SEMATTRS_FAAS_TRIGGER = TMP_FAAS_TRIGGER;\n\n/**\n * The execution ID of the current function execution.\n */\nexport const SEMATTRS_FAAS_EXECUTION = TMP_FAAS_EXECUTION;\n\n/**\n * The name of the source on which the triggering operation was performed. For example, in Cloud Storage or S3 corresponds to the bucket name, and in Cosmos DB to the database name.\n */\nexport const SEMATTRS_FAAS_DOCUMENT_COLLECTION = TMP_FAAS_DOCUMENT_COLLECTION;\n\n/**\n * Describes the type of the operation that was performed on the data.\n */\nexport const SEMATTRS_FAAS_DOCUMENT_OPERATION = TMP_FAAS_DOCUMENT_OPERATION;\n\n/**\n * A string containing the time when the data was accessed in the [ISO 8601](https://www.iso.org/iso-8601-date-and-time-format.html) format expressed in [UTC](https://www.w3.org/TR/NOTE-datetime).\n */\nexport const SEMATTRS_FAAS_DOCUMENT_TIME = TMP_FAAS_DOCUMENT_TIME;\n\n/**\n * The document name/table subjected to the operation. For example, in Cloud Storage or S3 is the name of the file, and in Cosmos DB the table name.\n */\nexport const SEMATTRS_FAAS_DOCUMENT_NAME = TMP_FAAS_DOCUMENT_NAME;\n\n/**\n * A string containing the function invocation time in the [ISO 8601](https://www.iso.org/iso-8601-date-and-time-format.html) format expressed in [UTC](https://www.w3.org/TR/NOTE-datetime).\n */\nexport const SEMATTRS_FAAS_TIME = TMP_FAAS_TIME;\n\n/**\n * A string containing the schedule period as [Cron Expression](https://docs.oracle.com/cd/E12058_01/doc/doc.1014/e12030/cron_expressions.htm).\n */\nexport const SEMATTRS_FAAS_CRON = TMP_FAAS_CRON;\n\n/**\n * A boolean that is true if the serverless function is executed for the first time (aka cold-start).\n */\nexport const SEMATTRS_FAAS_COLDSTART = TMP_FAAS_COLDSTART;\n\n/**\n * The name of the invoked function.\n *\n * Note: SHOULD be equal to the `faas.name` resource attribute of the invoked function.\n */\nexport const SEMATTRS_FAAS_INVOKED_NAME = TMP_FAAS_INVOKED_NAME;\n\n/**\n * The cloud provider of the invoked function.\n *\n * Note: SHOULD be equal to the `cloud.provider` resource attribute of the invoked function.\n */\nexport const SEMATTRS_FAAS_INVOKED_PROVIDER = TMP_FAAS_INVOKED_PROVIDER;\n\n/**\n * The cloud region of the invoked function.\n *\n * Note: SHOULD be equal to the `cloud.region` resource attribute of the invoked function.\n */\nexport const SEMATTRS_FAAS_INVOKED_REGION = TMP_FAAS_INVOKED_REGION;\n\n/**\n * Transport protocol used. See note below.\n */\nexport const SEMATTRS_NET_TRANSPORT = TMP_NET_TRANSPORT;\n\n/**\n * Remote address of the peer (dotted decimal for IPv4 or [RFC5952](https://tools.ietf.org/html/rfc5952) for IPv6).\n */\nexport const SEMATTRS_NET_PEER_IP = TMP_NET_PEER_IP;\n\n/**\n * Remote port number.\n */\nexport const SEMATTRS_NET_PEER_PORT = TMP_NET_PEER_PORT;\n\n/**\n * Remote hostname or similar, see note below.\n */\nexport const SEMATTRS_NET_PEER_NAME = TMP_NET_PEER_NAME;\n\n/**\n * Like `net.peer.ip` but for the host IP. Useful in case of a multi-IP host.\n */\nexport const SEMATTRS_NET_HOST_IP = TMP_NET_HOST_IP;\n\n/**\n * Like `net.peer.port` but for the host port.\n */\nexport const SEMATTRS_NET_HOST_PORT = TMP_NET_HOST_PORT;\n\n/**\n * Local hostname or similar, see note below.\n */\nexport const SEMATTRS_NET_HOST_NAME = TMP_NET_HOST_NAME;\n\n/**\n * The internet connection type currently being used by the host.\n */\nexport const SEMATTRS_NET_HOST_CONNECTION_TYPE = TMP_NET_HOST_CONNECTION_TYPE;\n\n/**\n * This describes more details regarding the connection.type. It may be the type of cell technology connection, but it could be used for describing details about a wifi connection.\n */\nexport const SEMATTRS_NET_HOST_CONNECTION_SUBTYPE =\n  TMP_NET_HOST_CONNECTION_SUBTYPE;\n\n/**\n * The name of the mobile carrier.\n */\nexport const SEMATTRS_NET_HOST_CARRIER_NAME = TMP_NET_HOST_CARRIER_NAME;\n\n/**\n * The mobile carrier country code.\n */\nexport const SEMATTRS_NET_HOST_CARRIER_MCC = TMP_NET_HOST_CARRIER_MCC;\n\n/**\n * The mobile carrier network code.\n */\nexport const SEMATTRS_NET_HOST_CARRIER_MNC = TMP_NET_HOST_CARRIER_MNC;\n\n/**\n * The ISO 3166-1 alpha-2 2-character country code associated with the mobile carrier network.\n */\nexport const SEMATTRS_NET_HOST_CARRIER_ICC = TMP_NET_HOST_CARRIER_ICC;\n\n/**\n * The [`service.name`](../../resource/semantic_conventions/README.md#service) of the remote service. SHOULD be equal to the actual `service.name` resource attribute of the remote service if any.\n */\nexport const SEMATTRS_PEER_SERVICE = TMP_PEER_SERVICE;\n\n/**\n * Username or client_id extracted from the access token or [Authorization](https://tools.ietf.org/html/rfc7235#section-4.2) header in the inbound request from outside the system.\n */\nexport const SEMATTRS_ENDUSER_ID = TMP_ENDUSER_ID;\n\n/**\n * Actual/assumed role the client is making the request under extracted from token or application security context.\n */\nexport const SEMATTRS_ENDUSER_ROLE = TMP_ENDUSER_ROLE;\n\n/**\n * Scopes or granted authorities the client currently possesses extracted from token or application security context. The value would come from the scope associated with an [OAuth 2.0 Access Token](https://tools.ietf.org/html/rfc6749#section-3.3) or an attribute value in a [SAML 2.0 Assertion](http://docs.oasis-open.org/security/saml/Post2.0/sstc-saml-tech-overview-2.0.html).\n */\nexport const SEMATTRS_ENDUSER_SCOPE = TMP_ENDUSER_SCOPE;\n\n/**\n * Current &#34;managed&#34; thread ID (as opposed to OS thread ID).\n */\nexport const SEMATTRS_THREAD_ID = TMP_THREAD_ID;\n\n/**\n * Current thread name.\n */\nexport const SEMATTRS_THREAD_NAME = TMP_THREAD_NAME;\n\n/**\n * The method or function name, or equivalent (usually rightmost part of the code unit&#39;s name).\n */\nexport const SEMATTRS_CODE_FUNCTION = TMP_CODE_FUNCTION;\n\n/**\n * The &#34;namespace&#34; within which `code.function` is defined. Usually the qualified class or module name, such that `code.namespace` + some separator + `code.function` form a unique identifier for the code unit.\n */\nexport const SEMATTRS_CODE_NAMESPACE = TMP_CODE_NAMESPACE;\n\n/**\n * The source code file name that identifies the code unit as uniquely as possible (preferably an absolute file path).\n */\nexport const SEMATTRS_CODE_FILEPATH = TMP_CODE_FILEPATH;\n\n/**\n * The line number in `code.filepath` best representing the operation. It SHOULD point within the code unit named in `code.function`.\n */\nexport const SEMATTRS_CODE_LINENO = TMP_CODE_LINENO;\n\n/**\n * HTTP request method.\n */\nexport const SEMATTRS_HTTP_METHOD = TMP_HTTP_METHOD;\n\n/**\n * Full HTTP request URL in the form `scheme://host[:port]/path?query[#fragment]`. Usually the fragment is not transmitted over HTTP, but if it is known, it should be included nevertheless.\n *\n * Note: `http.url` MUST NOT contain credentials passed via URL in form of `https://username:<EMAIL>/`. In such case the attribute&#39;s value should be `https://www.example.com/`.\n */\nexport const SEMATTRS_HTTP_URL = TMP_HTTP_URL;\n\n/**\n * The full request target as passed in a HTTP request line or equivalent.\n */\nexport const SEMATTRS_HTTP_TARGET = TMP_HTTP_TARGET;\n\n/**\n * The value of the [HTTP host header](https://tools.ietf.org/html/rfc7230#section-5.4). An empty Host header should also be reported, see note.\n *\n * Note: When the header is present but empty the attribute SHOULD be set to the empty string. Note that this is a valid situation that is expected in certain cases, according the aforementioned [section of RFC 7230](https://tools.ietf.org/html/rfc7230#section-5.4). When the header is not set the attribute MUST NOT be set.\n */\nexport const SEMATTRS_HTTP_HOST = TMP_HTTP_HOST;\n\n/**\n * The URI scheme identifying the used protocol.\n */\nexport const SEMATTRS_HTTP_SCHEME = TMP_HTTP_SCHEME;\n\n/**\n * [HTTP response status code](https://tools.ietf.org/html/rfc7231#section-6).\n */\nexport const SEMATTRS_HTTP_STATUS_CODE = TMP_HTTP_STATUS_CODE;\n\n/**\n * Kind of HTTP protocol used.\n *\n * Note: If `net.transport` is not specified, it can be assumed to be `IP.TCP` except if `http.flavor` is `QUIC`, in which case `IP.UDP` is assumed.\n */\nexport const SEMATTRS_HTTP_FLAVOR = TMP_HTTP_FLAVOR;\n\n/**\n * Value of the [HTTP User-Agent](https://tools.ietf.org/html/rfc7231#section-5.5.3) header sent by the client.\n */\nexport const SEMATTRS_HTTP_USER_AGENT = TMP_HTTP_USER_AGENT;\n\n/**\n * The size of the request payload body in bytes. This is the number of bytes transferred excluding headers and is often, but not always, present as the [Content-Length](https://tools.ietf.org/html/rfc7230#section-3.3.2) header. For requests using transport encoding, this should be the compressed size.\n */\nexport const SEMATTRS_HTTP_REQUEST_CONTENT_LENGTH =\n  TMP_HTTP_REQUEST_CONTENT_LENGTH;\n\n/**\n * The size of the uncompressed request payload body after transport decoding. Not set if transport encoding not used.\n */\nexport const SEMATTRS_HTTP_REQUEST_CONTENT_LENGTH_UNCOMPRESSED =\n  TMP_HTTP_REQUEST_CONTENT_LENGTH_UNCOMPRESSED;\n\n/**\n * The size of the response payload body in bytes. This is the number of bytes transferred excluding headers and is often, but not always, present as the [Content-Length](https://tools.ietf.org/html/rfc7230#section-3.3.2) header. For requests using transport encoding, this should be the compressed size.\n */\nexport const SEMATTRS_HTTP_RESPONSE_CONTENT_LENGTH =\n  TMP_HTTP_RESPONSE_CONTENT_LENGTH;\n\n/**\n * The size of the uncompressed response payload body after transport decoding. Not set if transport encoding not used.\n */\nexport const SEMATTRS_HTTP_RESPONSE_CONTENT_LENGTH_UNCOMPRESSED =\n  TMP_HTTP_RESPONSE_CONTENT_LENGTH_UNCOMPRESSED;\n\n/**\n * The primary server name of the matched virtual host. This should be obtained via configuration. If no such configuration can be obtained, this attribute MUST NOT be set ( `net.host.name` should be used instead).\n *\n * Note: `http.url` is usually not readily available on the server side but would have to be assembled in a cumbersome and sometimes lossy process from other information (see e.g. open-telemetry/opentelemetry-python/pull/148). It is thus preferred to supply the raw data that is available.\n */\nexport const SEMATTRS_HTTP_SERVER_NAME = TMP_HTTP_SERVER_NAME;\n\n/**\n * The matched route (path template).\n */\nexport const SEMATTRS_HTTP_ROUTE = TMP_HTTP_ROUTE;\n\n/**\n* The IP address of the original client behind all proxies, if known (e.g. from [X-Forwarded-For](https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/X-Forwarded-For)).\n*\n* Note: This is not necessarily the same as `net.peer.ip`, which would\nidentify the network-level peer, which may be a proxy.\n\nThis attribute should be set when a source of information different\nfrom the one used for `net.peer.ip`, is available even if that other\nsource just confirms the same value as `net.peer.ip`.\nRationale: For `net.peer.ip`, one typically does not know if it\ncomes from a proxy, reverse proxy, or the actual client. Setting\n`http.client_ip` when it&#39;s the same as `net.peer.ip` means that\none is at least somewhat confident that the address is not that of\nthe closest proxy.\n*/\nexport const SEMATTRS_HTTP_CLIENT_IP = TMP_HTTP_CLIENT_IP;\n\n/**\n * The keys in the `RequestItems` object field.\n */\nexport const SEMATTRS_AWS_DYNAMODB_TABLE_NAMES = TMP_AWS_DYNAMODB_TABLE_NAMES;\n\n/**\n * The JSON-serialized value of each item in the `ConsumedCapacity` response field.\n */\nexport const SEMATTRS_AWS_DYNAMODB_CONSUMED_CAPACITY =\n  TMP_AWS_DYNAMODB_CONSUMED_CAPACITY;\n\n/**\n * The JSON-serialized value of the `ItemCollectionMetrics` response field.\n */\nexport const SEMATTRS_AWS_DYNAMODB_ITEM_COLLECTION_METRICS =\n  TMP_AWS_DYNAMODB_ITEM_COLLECTION_METRICS;\n\n/**\n * The value of the `ProvisionedThroughput.ReadCapacityUnits` request parameter.\n */\nexport const SEMATTRS_AWS_DYNAMODB_PROVISIONED_READ_CAPACITY =\n  TMP_AWS_DYNAMODB_PROVISIONED_READ_CAPACITY;\n\n/**\n * The value of the `ProvisionedThroughput.WriteCapacityUnits` request parameter.\n */\nexport const SEMATTRS_AWS_DYNAMODB_PROVISIONED_WRITE_CAPACITY =\n  TMP_AWS_DYNAMODB_PROVISIONED_WRITE_CAPACITY;\n\n/**\n * The value of the `ConsistentRead` request parameter.\n */\nexport const SEMATTRS_AWS_DYNAMODB_CONSISTENT_READ =\n  TMP_AWS_DYNAMODB_CONSISTENT_READ;\n\n/**\n * The value of the `ProjectionExpression` request parameter.\n */\nexport const SEMATTRS_AWS_DYNAMODB_PROJECTION = TMP_AWS_DYNAMODB_PROJECTION;\n\n/**\n * The value of the `Limit` request parameter.\n */\nexport const SEMATTRS_AWS_DYNAMODB_LIMIT = TMP_AWS_DYNAMODB_LIMIT;\n\n/**\n * The value of the `AttributesToGet` request parameter.\n */\nexport const SEMATTRS_AWS_DYNAMODB_ATTRIBUTES_TO_GET =\n  TMP_AWS_DYNAMODB_ATTRIBUTES_TO_GET;\n\n/**\n * The value of the `IndexName` request parameter.\n */\nexport const SEMATTRS_AWS_DYNAMODB_INDEX_NAME = TMP_AWS_DYNAMODB_INDEX_NAME;\n\n/**\n * The value of the `Select` request parameter.\n */\nexport const SEMATTRS_AWS_DYNAMODB_SELECT = TMP_AWS_DYNAMODB_SELECT;\n\n/**\n * The JSON-serialized value of each item of the `GlobalSecondaryIndexes` request field.\n */\nexport const SEMATTRS_AWS_DYNAMODB_GLOBAL_SECONDARY_INDEXES =\n  TMP_AWS_DYNAMODB_GLOBAL_SECONDARY_INDEXES;\n\n/**\n * The JSON-serialized value of each item of the `LocalSecondaryIndexes` request field.\n */\nexport const SEMATTRS_AWS_DYNAMODB_LOCAL_SECONDARY_INDEXES =\n  TMP_AWS_DYNAMODB_LOCAL_SECONDARY_INDEXES;\n\n/**\n * The value of the `ExclusiveStartTableName` request parameter.\n */\nexport const SEMATTRS_AWS_DYNAMODB_EXCLUSIVE_START_TABLE =\n  TMP_AWS_DYNAMODB_EXCLUSIVE_START_TABLE;\n\n/**\n * The the number of items in the `TableNames` response parameter.\n */\nexport const SEMATTRS_AWS_DYNAMODB_TABLE_COUNT = TMP_AWS_DYNAMODB_TABLE_COUNT;\n\n/**\n * The value of the `ScanIndexForward` request parameter.\n */\nexport const SEMATTRS_AWS_DYNAMODB_SCAN_FORWARD = TMP_AWS_DYNAMODB_SCAN_FORWARD;\n\n/**\n * The value of the `Segment` request parameter.\n */\nexport const SEMATTRS_AWS_DYNAMODB_SEGMENT = TMP_AWS_DYNAMODB_SEGMENT;\n\n/**\n * The value of the `TotalSegments` request parameter.\n */\nexport const SEMATTRS_AWS_DYNAMODB_TOTAL_SEGMENTS =\n  TMP_AWS_DYNAMODB_TOTAL_SEGMENTS;\n\n/**\n * The value of the `Count` response parameter.\n */\nexport const SEMATTRS_AWS_DYNAMODB_COUNT = TMP_AWS_DYNAMODB_COUNT;\n\n/**\n * The value of the `ScannedCount` response parameter.\n */\nexport const SEMATTRS_AWS_DYNAMODB_SCANNED_COUNT =\n  TMP_AWS_DYNAMODB_SCANNED_COUNT;\n\n/**\n * The JSON-serialized value of each item in the `AttributeDefinitions` request field.\n */\nexport const SEMATTRS_AWS_DYNAMODB_ATTRIBUTE_DEFINITIONS =\n  TMP_AWS_DYNAMODB_ATTRIBUTE_DEFINITIONS;\n\n/**\n * The JSON-serialized value of each item in the the `GlobalSecondaryIndexUpdates` request field.\n */\nexport const SEMATTRS_AWS_DYNAMODB_GLOBAL_SECONDARY_INDEX_UPDATES =\n  TMP_AWS_DYNAMODB_GLOBAL_SECONDARY_INDEX_UPDATES;\n\n/**\n * A string identifying the messaging system.\n */\nexport const SEMATTRS_MESSAGING_SYSTEM = TMP_MESSAGING_SYSTEM;\n\n/**\n * The message destination name. This might be equal to the span name but is required nevertheless.\n */\nexport const SEMATTRS_MESSAGING_DESTINATION = TMP_MESSAGING_DESTINATION;\n\n/**\n * The kind of message destination.\n */\nexport const SEMATTRS_MESSAGING_DESTINATION_KIND =\n  TMP_MESSAGING_DESTINATION_KIND;\n\n/**\n * A boolean that is true if the message destination is temporary.\n */\nexport const SEMATTRS_MESSAGING_TEMP_DESTINATION =\n  TMP_MESSAGING_TEMP_DESTINATION;\n\n/**\n * The name of the transport protocol.\n */\nexport const SEMATTRS_MESSAGING_PROTOCOL = TMP_MESSAGING_PROTOCOL;\n\n/**\n * The version of the transport protocol.\n */\nexport const SEMATTRS_MESSAGING_PROTOCOL_VERSION =\n  TMP_MESSAGING_PROTOCOL_VERSION;\n\n/**\n * Connection string.\n */\nexport const SEMATTRS_MESSAGING_URL = TMP_MESSAGING_URL;\n\n/**\n * A value used by the messaging system as an identifier for the message, represented as a string.\n */\nexport const SEMATTRS_MESSAGING_MESSAGE_ID = TMP_MESSAGING_MESSAGE_ID;\n\n/**\n * The [conversation ID](#conversations) identifying the conversation to which the message belongs, represented as a string. Sometimes called &#34;Correlation ID&#34;.\n */\nexport const SEMATTRS_MESSAGING_CONVERSATION_ID = TMP_MESSAGING_CONVERSATION_ID;\n\n/**\n * The (uncompressed) size of the message payload in bytes. Also use this attribute if it is unknown whether the compressed or uncompressed payload size is reported.\n */\nexport const SEMATTRS_MESSAGING_MESSAGE_PAYLOAD_SIZE_BYTES =\n  TMP_MESSAGING_MESSAGE_PAYLOAD_SIZE_BYTES;\n\n/**\n * The compressed size of the message payload in bytes.\n */\nexport const SEMATTRS_MESSAGING_MESSAGE_PAYLOAD_COMPRESSED_SIZE_BYTES =\n  TMP_MESSAGING_MESSAGE_PAYLOAD_COMPRESSED_SIZE_BYTES;\n\n/**\n * A string identifying the kind of message consumption as defined in the [Operation names](#operation-names) section above. If the operation is &#34;send&#34;, this attribute MUST NOT be set, since the operation can be inferred from the span kind in that case.\n */\nexport const SEMATTRS_MESSAGING_OPERATION = TMP_MESSAGING_OPERATION;\n\n/**\n * The identifier for the consumer receiving a message. For Kafka, set it to `{messaging.kafka.consumer_group} - {messaging.kafka.client_id}`, if both are present, or only `messaging.kafka.consumer_group`. For brokers, such as RabbitMQ and Artemis, set it to the `client_id` of the client consuming the message.\n */\nexport const SEMATTRS_MESSAGING_CONSUMER_ID = TMP_MESSAGING_CONSUMER_ID;\n\n/**\n * RabbitMQ message routing key.\n */\nexport const SEMATTRS_MESSAGING_RABBITMQ_ROUTING_KEY =\n  TMP_MESSAGING_RABBITMQ_ROUTING_KEY;\n\n/**\n * Message keys in Kafka are used for grouping alike messages to ensure they&#39;re processed on the same partition. They differ from `messaging.message_id` in that they&#39;re not unique. If the key is `null`, the attribute MUST NOT be set.\n *\n * Note: If the key type is not string, it&#39;s string representation has to be supplied for the attribute. If the key has no unambiguous, canonical string form, don&#39;t include its value.\n */\nexport const SEMATTRS_MESSAGING_KAFKA_MESSAGE_KEY =\n  TMP_MESSAGING_KAFKA_MESSAGE_KEY;\n\n/**\n * Name of the Kafka Consumer Group that is handling the message. Only applies to consumers, not producers.\n */\nexport const SEMATTRS_MESSAGING_KAFKA_CONSUMER_GROUP =\n  TMP_MESSAGING_KAFKA_CONSUMER_GROUP;\n\n/**\n * Client Id for the Consumer or Producer that is handling the message.\n */\nexport const SEMATTRS_MESSAGING_KAFKA_CLIENT_ID = TMP_MESSAGING_KAFKA_CLIENT_ID;\n\n/**\n * Partition the message is sent to.\n */\nexport const SEMATTRS_MESSAGING_KAFKA_PARTITION = TMP_MESSAGING_KAFKA_PARTITION;\n\n/**\n * A boolean that is true if the message is a tombstone.\n */\nexport const SEMATTRS_MESSAGING_KAFKA_TOMBSTONE = TMP_MESSAGING_KAFKA_TOMBSTONE;\n\n/**\n * A string identifying the remoting system.\n */\nexport const SEMATTRS_RPC_SYSTEM = TMP_RPC_SYSTEM;\n\n/**\n * The full (logical) name of the service being called, including its package name, if applicable.\n *\n * Note: This is the logical name of the service from the RPC interface perspective, which can be different from the name of any implementing class. The `code.namespace` attribute may be used to store the latter (despite the attribute name, it may include a class name; e.g., class with method actually executing the call on the server side, RPC client stub class on the client side).\n */\nexport const SEMATTRS_RPC_SERVICE = TMP_RPC_SERVICE;\n\n/**\n * The name of the (logical) method being called, must be equal to the $method part in the span name.\n *\n * Note: This is the logical name of the method from the RPC interface perspective, which can be different from the name of any implementing method/function. The `code.function` attribute may be used to store the latter (e.g., method actually executing the call on the server side, RPC client stub method on the client side).\n */\nexport const SEMATTRS_RPC_METHOD = TMP_RPC_METHOD;\n\n/**\n * The [numeric status code](https://github.com/grpc/grpc/blob/v1.33.2/doc/statuscodes.md) of the gRPC request.\n */\nexport const SEMATTRS_RPC_GRPC_STATUS_CODE = TMP_RPC_GRPC_STATUS_CODE;\n\n/**\n * Protocol version as in `jsonrpc` property of request/response. Since JSON-RPC 1.0 does not specify this, the value can be omitted.\n */\nexport const SEMATTRS_RPC_JSONRPC_VERSION = TMP_RPC_JSONRPC_VERSION;\n\n/**\n * `id` property of request or response. Since protocol allows id to be int, string, `null` or missing (for notifications), value is expected to be cast to string for simplicity. Use empty string in case of `null` value. Omit entirely if this is a notification.\n */\nexport const SEMATTRS_RPC_JSONRPC_REQUEST_ID = TMP_RPC_JSONRPC_REQUEST_ID;\n\n/**\n * `error.code` property of response if it is an error response.\n */\nexport const SEMATTRS_RPC_JSONRPC_ERROR_CODE = TMP_RPC_JSONRPC_ERROR_CODE;\n\n/**\n * `error.message` property of response if it is an error response.\n */\nexport const SEMATTRS_RPC_JSONRPC_ERROR_MESSAGE = TMP_RPC_JSONRPC_ERROR_MESSAGE;\n\n/**\n * Whether this is a received or sent message.\n */\nexport const SEMATTRS_MESSAGE_TYPE = TMP_MESSAGE_TYPE;\n\n/**\n * MUST be calculated as two different counters starting from `1` one for sent messages and one for received message.\n *\n * Note: This way we guarantee that the values will be consistent between different implementations.\n */\nexport const SEMATTRS_MESSAGE_ID = TMP_MESSAGE_ID;\n\n/**\n * Compressed size of the message in bytes.\n */\nexport const SEMATTRS_MESSAGE_COMPRESSED_SIZE = TMP_MESSAGE_COMPRESSED_SIZE;\n\n/**\n * Uncompressed size of the message in bytes.\n */\nexport const SEMATTRS_MESSAGE_UNCOMPRESSED_SIZE = TMP_MESSAGE_UNCOMPRESSED_SIZE;\n\n/**\n * Definition of available values for SemanticAttributes\n * This type is used for backward compatibility, you should use the individual exported\n * constants SemanticAttributes_XXXXX rather than the exported constant map. As any single reference\n * to a constant map value will result in all strings being included into your bundle.\n * @deprecated Use the SEMATTRS_XXXXX constants rather than the SemanticAttributes.XXXXX for bundle minification.\n */\nexport type SemanticAttributes = {\n  /**\n   * The full invoked ARN as provided on the `Context` passed to the function (`Lambda-Runtime-Invoked-Function-Arn` header on the `/runtime/invocation/next` applicable).\n   *\n   * Note: This may be different from `faas.id` if an alias is involved.\n   */\n  AWS_LAMBDA_INVOKED_ARN: 'aws.lambda.invoked_arn';\n\n  /**\n   * An identifier for the database management system (DBMS) product being used. See below for a list of well-known identifiers.\n   */\n  DB_SYSTEM: 'db.system';\n\n  /**\n   * The connection string used to connect to the database. It is recommended to remove embedded credentials.\n   */\n  DB_CONNECTION_STRING: 'db.connection_string';\n\n  /**\n   * Username for accessing the database.\n   */\n  DB_USER: 'db.user';\n\n  /**\n   * The fully-qualified class name of the [Java Database Connectivity (JDBC)](https://docs.oracle.com/javase/8/docs/technotes/guides/jdbc/) driver used to connect.\n   */\n  DB_JDBC_DRIVER_CLASSNAME: 'db.jdbc.driver_classname';\n\n  /**\n   * If no [tech-specific attribute](#call-level-attributes-for-specific-technologies) is defined, this attribute is used to report the name of the database being accessed. For commands that switch the database, this should be set to the target database (even if the command fails).\n   *\n   * Note: In some SQL databases, the database name to be used is called &#34;schema name&#34;.\n   */\n  DB_NAME: 'db.name';\n\n  /**\n   * The database statement being executed.\n   *\n   * Note: The value may be sanitized to exclude sensitive information.\n   */\n  DB_STATEMENT: 'db.statement';\n\n  /**\n   * The name of the operation being executed, e.g. the [MongoDB command name](https://docs.mongodb.com/manual/reference/command/#database-operations) such as `findAndModify`, or the SQL keyword.\n   *\n   * Note: When setting this to an SQL keyword, it is not recommended to attempt any client-side parsing of `db.statement` just to get this property, but it should be set if the operation name is provided by the library being instrumented. If the SQL statement has an ambiguous operation, or performs more than one operation, this value may be omitted.\n   */\n  DB_OPERATION: 'db.operation';\n\n  /**\n   * The Microsoft SQL Server [instance name](https://docs.microsoft.com/en-us/sql/connect/jdbc/building-the-connection-url?view=sql-server-ver15) connecting to. This name is used to determine the port of a named instance.\n   *\n   * Note: If setting a `db.mssql.instance_name`, `net.peer.port` is no longer required (but still recommended if non-standard).\n   */\n  DB_MSSQL_INSTANCE_NAME: 'db.mssql.instance_name';\n\n  /**\n   * The name of the keyspace being accessed. To be used instead of the generic `db.name` attribute.\n   */\n  DB_CASSANDRA_KEYSPACE: 'db.cassandra.keyspace';\n\n  /**\n   * The fetch size used for paging, i.e. how many rows will be returned at once.\n   */\n  DB_CASSANDRA_PAGE_SIZE: 'db.cassandra.page_size';\n\n  /**\n   * The consistency level of the query. Based on consistency values from [CQL](https://docs.datastax.com/en/cassandra-oss/3.0/cassandra/dml/dmlConfigConsistency.html).\n   */\n  DB_CASSANDRA_CONSISTENCY_LEVEL: 'db.cassandra.consistency_level';\n\n  /**\n   * The name of the primary table that the operation is acting upon, including the schema name (if applicable).\n   *\n   * Note: This mirrors the db.sql.table attribute but references cassandra rather than sql. It is not recommended to attempt any client-side parsing of `db.statement` just to get this property, but it should be set if it is provided by the library being instrumented. If the operation is acting upon an anonymous table, or more than one table, this value MUST NOT be set.\n   */\n  DB_CASSANDRA_TABLE: 'db.cassandra.table';\n\n  /**\n   * Whether or not the query is idempotent.\n   */\n  DB_CASSANDRA_IDEMPOTENCE: 'db.cassandra.idempotence';\n\n  /**\n   * The number of times a query was speculatively executed. Not set or `0` if the query was not executed speculatively.\n   */\n  DB_CASSANDRA_SPECULATIVE_EXECUTION_COUNT: 'db.cassandra.speculative_execution_count';\n\n  /**\n   * The ID of the coordinating node for a query.\n   */\n  DB_CASSANDRA_COORDINATOR_ID: 'db.cassandra.coordinator.id';\n\n  /**\n   * The data center of the coordinating node for a query.\n   */\n  DB_CASSANDRA_COORDINATOR_DC: 'db.cassandra.coordinator.dc';\n\n  /**\n   * The [HBase namespace](https://hbase.apache.org/book.html#_namespace) being accessed. To be used instead of the generic `db.name` attribute.\n   */\n  DB_HBASE_NAMESPACE: 'db.hbase.namespace';\n\n  /**\n   * The index of the database being accessed as used in the [`SELECT` command](https://redis.io/commands/select), provided as an integer. To be used instead of the generic `db.name` attribute.\n   */\n  DB_REDIS_DATABASE_INDEX: 'db.redis.database_index';\n\n  /**\n   * The collection being accessed within the database stated in `db.name`.\n   */\n  DB_MONGODB_COLLECTION: 'db.mongodb.collection';\n\n  /**\n   * The name of the primary table that the operation is acting upon, including the schema name (if applicable).\n   *\n   * Note: It is not recommended to attempt any client-side parsing of `db.statement` just to get this property, but it should be set if it is provided by the library being instrumented. If the operation is acting upon an anonymous table, or more than one table, this value MUST NOT be set.\n   */\n  DB_SQL_TABLE: 'db.sql.table';\n\n  /**\n   * The type of the exception (its fully-qualified class name, if applicable). The dynamic type of the exception should be preferred over the static type in languages that support it.\n   */\n  EXCEPTION_TYPE: 'exception.type';\n\n  /**\n   * The exception message.\n   */\n  EXCEPTION_MESSAGE: 'exception.message';\n\n  /**\n   * A stacktrace as a string in the natural representation for the language runtime. The representation is to be determined and documented by each language SIG.\n   */\n  EXCEPTION_STACKTRACE: 'exception.stacktrace';\n\n  /**\n  * SHOULD be set to true if the exception event is recorded at a point where it is known that the exception is escaping the scope of the span.\n  *\n  * Note: An exception is considered to have escaped (or left) the scope of a span,\nif that span is ended while the exception is still logically &#34;in flight&#34;.\nThis may be actually &#34;in flight&#34; in some languages (e.g. if the exception\nis passed to a Context manager&#39;s `__exit__` method in Python) but will\nusually be caught at the point of recording the exception in most languages.\n\nIt is usually not possible to determine at the point where an exception is thrown\nwhether it will escape the scope of a span.\nHowever, it is trivial to know that an exception\nwill escape, if one checks for an active exception just before ending the span,\nas done in the [example above](#exception-end-example).\n\nIt follows that an exception may still escape the scope of the span\neven if the `exception.escaped` attribute was not set or set to false,\nsince the event might have been recorded at a time where it was not\nclear whether the exception will escape.\n  */\n  EXCEPTION_ESCAPED: 'exception.escaped';\n\n  /**\n   * Type of the trigger on which the function is executed.\n   */\n  FAAS_TRIGGER: 'faas.trigger';\n\n  /**\n   * The execution ID of the current function execution.\n   */\n  FAAS_EXECUTION: 'faas.execution';\n\n  /**\n   * The name of the source on which the triggering operation was performed. For example, in Cloud Storage or S3 corresponds to the bucket name, and in Cosmos DB to the database name.\n   */\n  FAAS_DOCUMENT_COLLECTION: 'faas.document.collection';\n\n  /**\n   * Describes the type of the operation that was performed on the data.\n   */\n  FAAS_DOCUMENT_OPERATION: 'faas.document.operation';\n\n  /**\n   * A string containing the time when the data was accessed in the [ISO 8601](https://www.iso.org/iso-8601-date-and-time-format.html) format expressed in [UTC](https://www.w3.org/TR/NOTE-datetime).\n   */\n  FAAS_DOCUMENT_TIME: 'faas.document.time';\n\n  /**\n   * The document name/table subjected to the operation. For example, in Cloud Storage or S3 is the name of the file, and in Cosmos DB the table name.\n   */\n  FAAS_DOCUMENT_NAME: 'faas.document.name';\n\n  /**\n   * A string containing the function invocation time in the [ISO 8601](https://www.iso.org/iso-8601-date-and-time-format.html) format expressed in [UTC](https://www.w3.org/TR/NOTE-datetime).\n   */\n  FAAS_TIME: 'faas.time';\n\n  /**\n   * A string containing the schedule period as [Cron Expression](https://docs.oracle.com/cd/E12058_01/doc/doc.1014/e12030/cron_expressions.htm).\n   */\n  FAAS_CRON: 'faas.cron';\n\n  /**\n   * A boolean that is true if the serverless function is executed for the first time (aka cold-start).\n   */\n  FAAS_COLDSTART: 'faas.coldstart';\n\n  /**\n   * The name of the invoked function.\n   *\n   * Note: SHOULD be equal to the `faas.name` resource attribute of the invoked function.\n   */\n  FAAS_INVOKED_NAME: 'faas.invoked_name';\n\n  /**\n   * The cloud provider of the invoked function.\n   *\n   * Note: SHOULD be equal to the `cloud.provider` resource attribute of the invoked function.\n   */\n  FAAS_INVOKED_PROVIDER: 'faas.invoked_provider';\n\n  /**\n   * The cloud region of the invoked function.\n   *\n   * Note: SHOULD be equal to the `cloud.region` resource attribute of the invoked function.\n   */\n  FAAS_INVOKED_REGION: 'faas.invoked_region';\n\n  /**\n   * Transport protocol used. See note below.\n   */\n  NET_TRANSPORT: 'net.transport';\n\n  /**\n   * Remote address of the peer (dotted decimal for IPv4 or [RFC5952](https://tools.ietf.org/html/rfc5952) for IPv6).\n   */\n  NET_PEER_IP: 'net.peer.ip';\n\n  /**\n   * Remote port number.\n   */\n  NET_PEER_PORT: 'net.peer.port';\n\n  /**\n   * Remote hostname or similar, see note below.\n   */\n  NET_PEER_NAME: 'net.peer.name';\n\n  /**\n   * Like `net.peer.ip` but for the host IP. Useful in case of a multi-IP host.\n   */\n  NET_HOST_IP: 'net.host.ip';\n\n  /**\n   * Like `net.peer.port` but for the host port.\n   */\n  NET_HOST_PORT: 'net.host.port';\n\n  /**\n   * Local hostname or similar, see note below.\n   */\n  NET_HOST_NAME: 'net.host.name';\n\n  /**\n   * The internet connection type currently being used by the host.\n   */\n  NET_HOST_CONNECTION_TYPE: 'net.host.connection.type';\n\n  /**\n   * This describes more details regarding the connection.type. It may be the type of cell technology connection, but it could be used for describing details about a wifi connection.\n   */\n  NET_HOST_CONNECTION_SUBTYPE: 'net.host.connection.subtype';\n\n  /**\n   * The name of the mobile carrier.\n   */\n  NET_HOST_CARRIER_NAME: 'net.host.carrier.name';\n\n  /**\n   * The mobile carrier country code.\n   */\n  NET_HOST_CARRIER_MCC: 'net.host.carrier.mcc';\n\n  /**\n   * The mobile carrier network code.\n   */\n  NET_HOST_CARRIER_MNC: 'net.host.carrier.mnc';\n\n  /**\n   * The ISO 3166-1 alpha-2 2-character country code associated with the mobile carrier network.\n   */\n  NET_HOST_CARRIER_ICC: 'net.host.carrier.icc';\n\n  /**\n   * The [`service.name`](../../resource/semantic_conventions/README.md#service) of the remote service. SHOULD be equal to the actual `service.name` resource attribute of the remote service if any.\n   */\n  PEER_SERVICE: 'peer.service';\n\n  /**\n   * Username or client_id extracted from the access token or [Authorization](https://tools.ietf.org/html/rfc7235#section-4.2) header in the inbound request from outside the system.\n   */\n  ENDUSER_ID: 'enduser.id';\n\n  /**\n   * Actual/assumed role the client is making the request under extracted from token or application security context.\n   */\n  ENDUSER_ROLE: 'enduser.role';\n\n  /**\n   * Scopes or granted authorities the client currently possesses extracted from token or application security context. The value would come from the scope associated with an [OAuth 2.0 Access Token](https://tools.ietf.org/html/rfc6749#section-3.3) or an attribute value in a [SAML 2.0 Assertion](http://docs.oasis-open.org/security/saml/Post2.0/sstc-saml-tech-overview-2.0.html).\n   */\n  ENDUSER_SCOPE: 'enduser.scope';\n\n  /**\n   * Current &#34;managed&#34; thread ID (as opposed to OS thread ID).\n   */\n  THREAD_ID: 'thread.id';\n\n  /**\n   * Current thread name.\n   */\n  THREAD_NAME: 'thread.name';\n\n  /**\n   * The method or function name, or equivalent (usually rightmost part of the code unit&#39;s name).\n   */\n  CODE_FUNCTION: 'code.function';\n\n  /**\n   * The &#34;namespace&#34; within which `code.function` is defined. Usually the qualified class or module name, such that `code.namespace` + some separator + `code.function` form a unique identifier for the code unit.\n   */\n  CODE_NAMESPACE: 'code.namespace';\n\n  /**\n   * The source code file name that identifies the code unit as uniquely as possible (preferably an absolute file path).\n   */\n  CODE_FILEPATH: 'code.filepath';\n\n  /**\n   * The line number in `code.filepath` best representing the operation. It SHOULD point within the code unit named in `code.function`.\n   */\n  CODE_LINENO: 'code.lineno';\n\n  /**\n   * HTTP request method.\n   */\n  HTTP_METHOD: 'http.method';\n\n  /**\n   * Full HTTP request URL in the form `scheme://host[:port]/path?query[#fragment]`. Usually the fragment is not transmitted over HTTP, but if it is known, it should be included nevertheless.\n   *\n   * Note: `http.url` MUST NOT contain credentials passed via URL in form of `https://username:<EMAIL>/`. In such case the attribute&#39;s value should be `https://www.example.com/`.\n   */\n  HTTP_URL: 'http.url';\n\n  /**\n   * The full request target as passed in a HTTP request line or equivalent.\n   */\n  HTTP_TARGET: 'http.target';\n\n  /**\n   * The value of the [HTTP host header](https://tools.ietf.org/html/rfc7230#section-5.4). An empty Host header should also be reported, see note.\n   *\n   * Note: When the header is present but empty the attribute SHOULD be set to the empty string. Note that this is a valid situation that is expected in certain cases, according the aforementioned [section of RFC 7230](https://tools.ietf.org/html/rfc7230#section-5.4). When the header is not set the attribute MUST NOT be set.\n   */\n  HTTP_HOST: 'http.host';\n\n  /**\n   * The URI scheme identifying the used protocol.\n   */\n  HTTP_SCHEME: 'http.scheme';\n\n  /**\n   * [HTTP response status code](https://tools.ietf.org/html/rfc7231#section-6).\n   */\n  HTTP_STATUS_CODE: 'http.status_code';\n\n  /**\n   * Kind of HTTP protocol used.\n   *\n   * Note: If `net.transport` is not specified, it can be assumed to be `IP.TCP` except if `http.flavor` is `QUIC`, in which case `IP.UDP` is assumed.\n   */\n  HTTP_FLAVOR: 'http.flavor';\n\n  /**\n   * Value of the [HTTP User-Agent](https://tools.ietf.org/html/rfc7231#section-5.5.3) header sent by the client.\n   */\n  HTTP_USER_AGENT: 'http.user_agent';\n\n  /**\n   * The size of the request payload body in bytes. This is the number of bytes transferred excluding headers and is often, but not always, present as the [Content-Length](https://tools.ietf.org/html/rfc7230#section-3.3.2) header. For requests using transport encoding, this should be the compressed size.\n   */\n  HTTP_REQUEST_CONTENT_LENGTH: 'http.request_content_length';\n\n  /**\n   * The size of the uncompressed request payload body after transport decoding. Not set if transport encoding not used.\n   */\n  HTTP_REQUEST_CONTENT_LENGTH_UNCOMPRESSED: 'http.request_content_length_uncompressed';\n\n  /**\n   * The size of the response payload body in bytes. This is the number of bytes transferred excluding headers and is often, but not always, present as the [Content-Length](https://tools.ietf.org/html/rfc7230#section-3.3.2) header. For requests using transport encoding, this should be the compressed size.\n   */\n  HTTP_RESPONSE_CONTENT_LENGTH: 'http.response_content_length';\n\n  /**\n   * The size of the uncompressed response payload body after transport decoding. Not set if transport encoding not used.\n   */\n  HTTP_RESPONSE_CONTENT_LENGTH_UNCOMPRESSED: 'http.response_content_length_uncompressed';\n\n  /**\n   * The primary server name of the matched virtual host. This should be obtained via configuration. If no such configuration can be obtained, this attribute MUST NOT be set ( `net.host.name` should be used instead).\n   *\n   * Note: `http.url` is usually not readily available on the server side but would have to be assembled in a cumbersome and sometimes lossy process from other information (see e.g. open-telemetry/opentelemetry-python/pull/148). It is thus preferred to supply the raw data that is available.\n   */\n  HTTP_SERVER_NAME: 'http.server_name';\n\n  /**\n   * The matched route (path template).\n   */\n  HTTP_ROUTE: 'http.route';\n\n  /**\n  * The IP address of the original client behind all proxies, if known (e.g. from [X-Forwarded-For](https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/X-Forwarded-For)).\n  *\n  * Note: This is not necessarily the same as `net.peer.ip`, which would\nidentify the network-level peer, which may be a proxy.\n\nThis attribute should be set when a source of information different\nfrom the one used for `net.peer.ip`, is available even if that other\nsource just confirms the same value as `net.peer.ip`.\nRationale: For `net.peer.ip`, one typically does not know if it\ncomes from a proxy, reverse proxy, or the actual client. Setting\n`http.client_ip` when it&#39;s the same as `net.peer.ip` means that\none is at least somewhat confident that the address is not that of\nthe closest proxy.\n  */\n  HTTP_CLIENT_IP: 'http.client_ip';\n\n  /**\n   * The keys in the `RequestItems` object field.\n   */\n  AWS_DYNAMODB_TABLE_NAMES: 'aws.dynamodb.table_names';\n\n  /**\n   * The JSON-serialized value of each item in the `ConsumedCapacity` response field.\n   */\n  AWS_DYNAMODB_CONSUMED_CAPACITY: 'aws.dynamodb.consumed_capacity';\n\n  /**\n   * The JSON-serialized value of the `ItemCollectionMetrics` response field.\n   */\n  AWS_DYNAMODB_ITEM_COLLECTION_METRICS: 'aws.dynamodb.item_collection_metrics';\n\n  /**\n   * The value of the `ProvisionedThroughput.ReadCapacityUnits` request parameter.\n   */\n  AWS_DYNAMODB_PROVISIONED_READ_CAPACITY: 'aws.dynamodb.provisioned_read_capacity';\n\n  /**\n   * The value of the `ProvisionedThroughput.WriteCapacityUnits` request parameter.\n   */\n  AWS_DYNAMODB_PROVISIONED_WRITE_CAPACITY: 'aws.dynamodb.provisioned_write_capacity';\n\n  /**\n   * The value of the `ConsistentRead` request parameter.\n   */\n  AWS_DYNAMODB_CONSISTENT_READ: 'aws.dynamodb.consistent_read';\n\n  /**\n   * The value of the `ProjectionExpression` request parameter.\n   */\n  AWS_DYNAMODB_PROJECTION: 'aws.dynamodb.projection';\n\n  /**\n   * The value of the `Limit` request parameter.\n   */\n  AWS_DYNAMODB_LIMIT: 'aws.dynamodb.limit';\n\n  /**\n   * The value of the `AttributesToGet` request parameter.\n   */\n  AWS_DYNAMODB_ATTRIBUTES_TO_GET: 'aws.dynamodb.attributes_to_get';\n\n  /**\n   * The value of the `IndexName` request parameter.\n   */\n  AWS_DYNAMODB_INDEX_NAME: 'aws.dynamodb.index_name';\n\n  /**\n   * The value of the `Select` request parameter.\n   */\n  AWS_DYNAMODB_SELECT: 'aws.dynamodb.select';\n\n  /**\n   * The JSON-serialized value of each item of the `GlobalSecondaryIndexes` request field.\n   */\n  AWS_DYNAMODB_GLOBAL_SECONDARY_INDEXES: 'aws.dynamodb.global_secondary_indexes';\n\n  /**\n   * The JSON-serialized value of each item of the `LocalSecondaryIndexes` request field.\n   */\n  AWS_DYNAMODB_LOCAL_SECONDARY_INDEXES: 'aws.dynamodb.local_secondary_indexes';\n\n  /**\n   * The value of the `ExclusiveStartTableName` request parameter.\n   */\n  AWS_DYNAMODB_EXCLUSIVE_START_TABLE: 'aws.dynamodb.exclusive_start_table';\n\n  /**\n   * The the number of items in the `TableNames` response parameter.\n   */\n  AWS_DYNAMODB_TABLE_COUNT: 'aws.dynamodb.table_count';\n\n  /**\n   * The value of the `ScanIndexForward` request parameter.\n   */\n  AWS_DYNAMODB_SCAN_FORWARD: 'aws.dynamodb.scan_forward';\n\n  /**\n   * The value of the `Segment` request parameter.\n   */\n  AWS_DYNAMODB_SEGMENT: 'aws.dynamodb.segment';\n\n  /**\n   * The value of the `TotalSegments` request parameter.\n   */\n  AWS_DYNAMODB_TOTAL_SEGMENTS: 'aws.dynamodb.total_segments';\n\n  /**\n   * The value of the `Count` response parameter.\n   */\n  AWS_DYNAMODB_COUNT: 'aws.dynamodb.count';\n\n  /**\n   * The value of the `ScannedCount` response parameter.\n   */\n  AWS_DYNAMODB_SCANNED_COUNT: 'aws.dynamodb.scanned_count';\n\n  /**\n   * The JSON-serialized value of each item in the `AttributeDefinitions` request field.\n   */\n  AWS_DYNAMODB_ATTRIBUTE_DEFINITIONS: 'aws.dynamodb.attribute_definitions';\n\n  /**\n   * The JSON-serialized value of each item in the the `GlobalSecondaryIndexUpdates` request field.\n   */\n  AWS_DYNAMODB_GLOBAL_SECONDARY_INDEX_UPDATES: 'aws.dynamodb.global_secondary_index_updates';\n\n  /**\n   * A string identifying the messaging system.\n   */\n  MESSAGING_SYSTEM: 'messaging.system';\n\n  /**\n   * The message destination name. This might be equal to the span name but is required nevertheless.\n   */\n  MESSAGING_DESTINATION: 'messaging.destination';\n\n  /**\n   * The kind of message destination.\n   */\n  MESSAGING_DESTINATION_KIND: 'messaging.destination_kind';\n\n  /**\n   * A boolean that is true if the message destination is temporary.\n   */\n  MESSAGING_TEMP_DESTINATION: 'messaging.temp_destination';\n\n  /**\n   * The name of the transport protocol.\n   */\n  MESSAGING_PROTOCOL: 'messaging.protocol';\n\n  /**\n   * The version of the transport protocol.\n   */\n  MESSAGING_PROTOCOL_VERSION: 'messaging.protocol_version';\n\n  /**\n   * Connection string.\n   */\n  MESSAGING_URL: 'messaging.url';\n\n  /**\n   * A value used by the messaging system as an identifier for the message, represented as a string.\n   */\n  MESSAGING_MESSAGE_ID: 'messaging.message_id';\n\n  /**\n   * The [conversation ID](#conversations) identifying the conversation to which the message belongs, represented as a string. Sometimes called &#34;Correlation ID&#34;.\n   */\n  MESSAGING_CONVERSATION_ID: 'messaging.conversation_id';\n\n  /**\n   * The (uncompressed) size of the message payload in bytes. Also use this attribute if it is unknown whether the compressed or uncompressed payload size is reported.\n   */\n  MESSAGING_MESSAGE_PAYLOAD_SIZE_BYTES: 'messaging.message_payload_size_bytes';\n\n  /**\n   * The compressed size of the message payload in bytes.\n   */\n  MESSAGING_MESSAGE_PAYLOAD_COMPRESSED_SIZE_BYTES: 'messaging.message_payload_compressed_size_bytes';\n\n  /**\n   * A string identifying the kind of message consumption as defined in the [Operation names](#operation-names) section above. If the operation is &#34;send&#34;, this attribute MUST NOT be set, since the operation can be inferred from the span kind in that case.\n   */\n  MESSAGING_OPERATION: 'messaging.operation';\n\n  /**\n   * The identifier for the consumer receiving a message. For Kafka, set it to `{messaging.kafka.consumer_group} - {messaging.kafka.client_id}`, if both are present, or only `messaging.kafka.consumer_group`. For brokers, such as RabbitMQ and Artemis, set it to the `client_id` of the client consuming the message.\n   */\n  MESSAGING_CONSUMER_ID: 'messaging.consumer_id';\n\n  /**\n   * RabbitMQ message routing key.\n   */\n  MESSAGING_RABBITMQ_ROUTING_KEY: 'messaging.rabbitmq.routing_key';\n\n  /**\n   * Message keys in Kafka are used for grouping alike messages to ensure they&#39;re processed on the same partition. They differ from `messaging.message_id` in that they&#39;re not unique. If the key is `null`, the attribute MUST NOT be set.\n   *\n   * Note: If the key type is not string, it&#39;s string representation has to be supplied for the attribute. If the key has no unambiguous, canonical string form, don&#39;t include its value.\n   */\n  MESSAGING_KAFKA_MESSAGE_KEY: 'messaging.kafka.message_key';\n\n  /**\n   * Name of the Kafka Consumer Group that is handling the message. Only applies to consumers, not producers.\n   */\n  MESSAGING_KAFKA_CONSUMER_GROUP: 'messaging.kafka.consumer_group';\n\n  /**\n   * Client Id for the Consumer or Producer that is handling the message.\n   */\n  MESSAGING_KAFKA_CLIENT_ID: 'messaging.kafka.client_id';\n\n  /**\n   * Partition the message is sent to.\n   */\n  MESSAGING_KAFKA_PARTITION: 'messaging.kafka.partition';\n\n  /**\n   * A boolean that is true if the message is a tombstone.\n   */\n  MESSAGING_KAFKA_TOMBSTONE: 'messaging.kafka.tombstone';\n\n  /**\n   * A string identifying the remoting system.\n   */\n  RPC_SYSTEM: 'rpc.system';\n\n  /**\n   * The full (logical) name of the service being called, including its package name, if applicable.\n   *\n   * Note: This is the logical name of the service from the RPC interface perspective, which can be different from the name of any implementing class. The `code.namespace` attribute may be used to store the latter (despite the attribute name, it may include a class name; e.g., class with method actually executing the call on the server side, RPC client stub class on the client side).\n   */\n  RPC_SERVICE: 'rpc.service';\n\n  /**\n   * The name of the (logical) method being called, must be equal to the $method part in the span name.\n   *\n   * Note: This is the logical name of the method from the RPC interface perspective, which can be different from the name of any implementing method/function. The `code.function` attribute may be used to store the latter (e.g., method actually executing the call on the server side, RPC client stub method on the client side).\n   */\n  RPC_METHOD: 'rpc.method';\n\n  /**\n   * The [numeric status code](https://github.com/grpc/grpc/blob/v1.33.2/doc/statuscodes.md) of the gRPC request.\n   */\n  RPC_GRPC_STATUS_CODE: 'rpc.grpc.status_code';\n\n  /**\n   * Protocol version as in `jsonrpc` property of request/response. Since JSON-RPC 1.0 does not specify this, the value can be omitted.\n   */\n  RPC_JSONRPC_VERSION: 'rpc.jsonrpc.version';\n\n  /**\n   * `id` property of request or response. Since protocol allows id to be int, string, `null` or missing (for notifications), value is expected to be cast to string for simplicity. Use empty string in case of `null` value. Omit entirely if this is a notification.\n   */\n  RPC_JSONRPC_REQUEST_ID: 'rpc.jsonrpc.request_id';\n\n  /**\n   * `error.code` property of response if it is an error response.\n   */\n  RPC_JSONRPC_ERROR_CODE: 'rpc.jsonrpc.error_code';\n\n  /**\n   * `error.message` property of response if it is an error response.\n   */\n  RPC_JSONRPC_ERROR_MESSAGE: 'rpc.jsonrpc.error_message';\n\n  /**\n   * Whether this is a received or sent message.\n   */\n  MESSAGE_TYPE: 'message.type';\n\n  /**\n   * MUST be calculated as two different counters starting from `1` one for sent messages and one for received message.\n   *\n   * Note: This way we guarantee that the values will be consistent between different implementations.\n   */\n  MESSAGE_ID: 'message.id';\n\n  /**\n   * Compressed size of the message in bytes.\n   */\n  MESSAGE_COMPRESSED_SIZE: 'message.compressed_size';\n\n  /**\n   * Uncompressed size of the message in bytes.\n   */\n  MESSAGE_UNCOMPRESSED_SIZE: 'message.uncompressed_size';\n};\n\n/**\n * Create exported Value Map for SemanticAttributes values\n * @deprecated Use the SEMATTRS_XXXXX constants rather than the SemanticAttributes.XXXXX for bundle minification\n */\nexport const SemanticAttributes: SemanticAttributes =\n  /*#__PURE__*/ createConstMap<SemanticAttributes>([\n    TMP_AWS_LAMBDA_INVOKED_ARN,\n    TMP_DB_SYSTEM,\n    TMP_DB_CONNECTION_STRING,\n    TMP_DB_USER,\n    TMP_DB_JDBC_DRIVER_CLASSNAME,\n    TMP_DB_NAME,\n    TMP_DB_STATEMENT,\n    TMP_DB_OPERATION,\n    TMP_DB_MSSQL_INSTANCE_NAME,\n    TMP_DB_CASSANDRA_KEYSPACE,\n    TMP_DB_CASSANDRA_PAGE_SIZE,\n    TMP_DB_CASSANDRA_CONSISTENCY_LEVEL,\n    TMP_DB_CASSANDRA_TABLE,\n    TMP_DB_CASSANDRA_IDEMPOTENCE,\n    TMP_DB_CASSANDRA_SPECULATIVE_EXECUTION_COUNT,\n    TMP_DB_CASSANDRA_COORDINATOR_ID,\n    TMP_DB_CASSANDRA_COORDINATOR_DC,\n    TMP_DB_HBASE_NAMESPACE,\n    TMP_DB_REDIS_DATABASE_INDEX,\n    TMP_DB_MONGODB_COLLECTION,\n    TMP_DB_SQL_TABLE,\n    TMP_EXCEPTION_TYPE,\n    TMP_EXCEPTION_MESSAGE,\n    TMP_EXCEPTION_STACKTRACE,\n    TMP_EXCEPTION_ESCAPED,\n    TMP_FAAS_TRIGGER,\n    TMP_FAAS_EXECUTION,\n    TMP_FAAS_DOCUMENT_COLLECTION,\n    TMP_FAAS_DOCUMENT_OPERATION,\n    TMP_FAAS_DOCUMENT_TIME,\n    TMP_FAAS_DOCUMENT_NAME,\n    TMP_FAAS_TIME,\n    TMP_FAAS_CRON,\n    TMP_FAAS_COLDSTART,\n    TMP_FAAS_INVOKED_NAME,\n    TMP_FAAS_INVOKED_PROVIDER,\n    TMP_FAAS_INVOKED_REGION,\n    TMP_NET_TRANSPORT,\n    TMP_NET_PEER_IP,\n    TMP_NET_PEER_PORT,\n    TMP_NET_PEER_NAME,\n    TMP_NET_HOST_IP,\n    TMP_NET_HOST_PORT,\n    TMP_NET_HOST_NAME,\n    TMP_NET_HOST_CONNECTION_TYPE,\n    TMP_NET_HOST_CONNECTION_SUBTYPE,\n    TMP_NET_HOST_CARRIER_NAME,\n    TMP_NET_HOST_CARRIER_MCC,\n    TMP_NET_HOST_CARRIER_MNC,\n    TMP_NET_HOST_CARRIER_ICC,\n    TMP_PEER_SERVICE,\n    TMP_ENDUSER_ID,\n    TMP_ENDUSER_ROLE,\n    TMP_ENDUSER_SCOPE,\n    TMP_THREAD_ID,\n    TMP_THREAD_NAME,\n    TMP_CODE_FUNCTION,\n    TMP_CODE_NAMESPACE,\n    TMP_CODE_FILEPATH,\n    TMP_CODE_LINENO,\n    TMP_HTTP_METHOD,\n    TMP_HTTP_URL,\n    TMP_HTTP_TARGET,\n    TMP_HTTP_HOST,\n    TMP_HTTP_SCHEME,\n    TMP_HTTP_STATUS_CODE,\n    TMP_HTTP_FLAVOR,\n    TMP_HTTP_USER_AGENT,\n    TMP_HTTP_REQUEST_CONTENT_LENGTH,\n    TMP_HTTP_REQUEST_CONTENT_LENGTH_UNCOMPRESSED,\n    TMP_HTTP_RESPONSE_CONTENT_LENGTH,\n    TMP_HTTP_RESPONSE_CONTENT_LENGTH_UNCOMPRESSED,\n    TMP_HTTP_SERVER_NAME,\n    TMP_HTTP_ROUTE,\n    TMP_HTTP_CLIENT_IP,\n    TMP_AWS_DYNAMODB_TABLE_NAMES,\n    TMP_AWS_DYNAMODB_CONSUMED_CAPACITY,\n    TMP_AWS_DYNAMODB_ITEM_COLLECTION_METRICS,\n    TMP_AWS_DYNAMODB_PROVISIONED_READ_CAPACITY,\n    TMP_AWS_DYNAMODB_PROVISIONED_WRITE_CAPACITY,\n    TMP_AWS_DYNAMODB_CONSISTENT_READ,\n    TMP_AWS_DYNAMODB_PROJECTION,\n    TMP_AWS_DYNAMODB_LIMIT,\n    TMP_AWS_DYNAMODB_ATTRIBUTES_TO_GET,\n    TMP_AWS_DYNAMODB_INDEX_NAME,\n    TMP_AWS_DYNAMODB_SELECT,\n    TMP_AWS_DYNAMODB_GLOBAL_SECONDARY_INDEXES,\n    TMP_AWS_DYNAMODB_LOCAL_SECONDARY_INDEXES,\n    TMP_AWS_DYNAMODB_EXCLUSIVE_START_TABLE,\n    TMP_AWS_DYNAMODB_TABLE_COUNT,\n    TMP_AWS_DYNAMODB_SCAN_FORWARD,\n    TMP_AWS_DYNAMODB_SEGMENT,\n    TMP_AWS_DYNAMODB_TOTAL_SEGMENTS,\n    TMP_AWS_DYNAMODB_COUNT,\n    TMP_AWS_DYNAMODB_SCANNED_COUNT,\n    TMP_AWS_DYNAMODB_ATTRIBUTE_DEFINITIONS,\n    TMP_AWS_DYNAMODB_GLOBAL_SECONDARY_INDEX_UPDATES,\n    TMP_MESSAGING_SYSTEM,\n    TMP_MESSAGING_DESTINATION,\n    TMP_MESSAGING_DESTINATION_KIND,\n    TMP_MESSAGING_TEMP_DESTINATION,\n    TMP_MESSAGING_PROTOCOL,\n    TMP_MESSAGING_PROTOCOL_VERSION,\n    TMP_MESSAGING_URL,\n    TMP_MESSAGING_MESSAGE_ID,\n    TMP_MESSAGING_CONVERSATION_ID,\n    TMP_MESSAGING_MESSAGE_PAYLOAD_SIZE_BYTES,\n    TMP_MESSAGING_MESSAGE_PAYLOAD_COMPRESSED_SIZE_BYTES,\n    TMP_MESSAGING_OPERATION,\n    TMP_MESSAGING_CONSUMER_ID,\n    TMP_MESSAGING_RABBITMQ_ROUTING_KEY,\n    TMP_MESSAGING_KAFKA_MESSAGE_KEY,\n    TMP_MESSAGING_KAFKA_CONSUMER_GROUP,\n    TMP_MESSAGING_KAFKA_CLIENT_ID,\n    TMP_MESSAGING_KAFKA_PARTITION,\n    TMP_MESSAGING_KAFKA_TOMBSTONE,\n    TMP_RPC_SYSTEM,\n    TMP_RPC_SERVICE,\n    TMP_RPC_METHOD,\n    TMP_RPC_GRPC_STATUS_CODE,\n    TMP_RPC_JSONRPC_VERSION,\n    TMP_RPC_JSONRPC_REQUEST_ID,\n    TMP_RPC_JSONRPC_ERROR_CODE,\n    TMP_RPC_JSONRPC_ERROR_MESSAGE,\n    TMP_MESSAGE_TYPE,\n    TMP_MESSAGE_ID,\n    TMP_MESSAGE_COMPRESSED_SIZE,\n    TMP_MESSAGE_UNCOMPRESSED_SIZE,\n  ]);\n\n/* ----------------------------------------------------------------------------------------------------------\n * Constant values for DbSystemValues enum definition\n *\n * An identifier for the database management system (DBMS) product being used. See below for a list of well-known identifiers.\n * ---------------------------------------------------------------------------------------------------------- */\n\n// Temporary local constants to assign to the individual exports and the namespaced version\n// Required to avoid the namespace exports using the unminifable export names for some package types\nconst TMP_DBSYSTEMVALUES_OTHER_SQL = 'other_sql';\nconst TMP_DBSYSTEMVALUES_MSSQL = 'mssql';\nconst TMP_DBSYSTEMVALUES_MYSQL = 'mysql';\nconst TMP_DBSYSTEMVALUES_ORACLE = 'oracle';\nconst TMP_DBSYSTEMVALUES_DB2 = 'db2';\nconst TMP_DBSYSTEMVALUES_POSTGRESQL = 'postgresql';\nconst TMP_DBSYSTEMVALUES_REDSHIFT = 'redshift';\nconst TMP_DBSYSTEMVALUES_HIVE = 'hive';\nconst TMP_DBSYSTEMVALUES_CLOUDSCAPE = 'cloudscape';\nconst TMP_DBSYSTEMVALUES_HSQLDB = 'hsqldb';\nconst TMP_DBSYSTEMVALUES_PROGRESS = 'progress';\nconst TMP_DBSYSTEMVALUES_MAXDB = 'maxdb';\nconst TMP_DBSYSTEMVALUES_HANADB = 'hanadb';\nconst TMP_DBSYSTEMVALUES_INGRES = 'ingres';\nconst TMP_DBSYSTEMVALUES_FIRSTSQL = 'firstsql';\nconst TMP_DBSYSTEMVALUES_EDB = 'edb';\nconst TMP_DBSYSTEMVALUES_CACHE = 'cache';\nconst TMP_DBSYSTEMVALUES_ADABAS = 'adabas';\nconst TMP_DBSYSTEMVALUES_FIREBIRD = 'firebird';\nconst TMP_DBSYSTEMVALUES_DERBY = 'derby';\nconst TMP_DBSYSTEMVALUES_FILEMAKER = 'filemaker';\nconst TMP_DBSYSTEMVALUES_INFORMIX = 'informix';\nconst TMP_DBSYSTEMVALUES_INSTANTDB = 'instantdb';\nconst TMP_DBSYSTEMVALUES_INTERBASE = 'interbase';\nconst TMP_DBSYSTEMVALUES_MARIADB = 'mariadb';\nconst TMP_DBSYSTEMVALUES_NETEZZA = 'netezza';\nconst TMP_DBSYSTEMVALUES_PERVASIVE = 'pervasive';\nconst TMP_DBSYSTEMVALUES_POINTBASE = 'pointbase';\nconst TMP_DBSYSTEMVALUES_SQLITE = 'sqlite';\nconst TMP_DBSYSTEMVALUES_SYBASE = 'sybase';\nconst TMP_DBSYSTEMVALUES_TERADATA = 'teradata';\nconst TMP_DBSYSTEMVALUES_VERTICA = 'vertica';\nconst TMP_DBSYSTEMVALUES_H2 = 'h2';\nconst TMP_DBSYSTEMVALUES_COLDFUSION = 'coldfusion';\nconst TMP_DBSYSTEMVALUES_CASSANDRA = 'cassandra';\nconst TMP_DBSYSTEMVALUES_HBASE = 'hbase';\nconst TMP_DBSYSTEMVALUES_MONGODB = 'mongodb';\nconst TMP_DBSYSTEMVALUES_REDIS = 'redis';\nconst TMP_DBSYSTEMVALUES_COUCHBASE = 'couchbase';\nconst TMP_DBSYSTEMVALUES_COUCHDB = 'couchdb';\nconst TMP_DBSYSTEMVALUES_COSMOSDB = 'cosmosdb';\nconst TMP_DBSYSTEMVALUES_DYNAMODB = 'dynamodb';\nconst TMP_DBSYSTEMVALUES_NEO4J = 'neo4j';\nconst TMP_DBSYSTEMVALUES_GEODE = 'geode';\nconst TMP_DBSYSTEMVALUES_ELASTICSEARCH = 'elasticsearch';\nconst TMP_DBSYSTEMVALUES_MEMCACHED = 'memcached';\nconst TMP_DBSYSTEMVALUES_COCKROACHDB = 'cockroachdb';\n\n/**\n * An identifier for the database management system (DBMS) product being used. See below for a list of well-known identifiers.\n */\nexport const DBSYSTEMVALUES_OTHER_SQL = TMP_DBSYSTEMVALUES_OTHER_SQL;\n\n/**\n * An identifier for the database management system (DBMS) product being used. See below for a list of well-known identifiers.\n */\nexport const DBSYSTEMVALUES_MSSQL = TMP_DBSYSTEMVALUES_MSSQL;\n\n/**\n * An identifier for the database management system (DBMS) product being used. See below for a list of well-known identifiers.\n */\nexport const DBSYSTEMVALUES_MYSQL = TMP_DBSYSTEMVALUES_MYSQL;\n\n/**\n * An identifier for the database management system (DBMS) product being used. See below for a list of well-known identifiers.\n */\nexport const DBSYSTEMVALUES_ORACLE = TMP_DBSYSTEMVALUES_ORACLE;\n\n/**\n * An identifier for the database management system (DBMS) product being used. See below for a list of well-known identifiers.\n */\nexport const DBSYSTEMVALUES_DB2 = TMP_DBSYSTEMVALUES_DB2;\n\n/**\n * An identifier for the database management system (DBMS) product being used. See below for a list of well-known identifiers.\n */\nexport const DBSYSTEMVALUES_POSTGRESQL = TMP_DBSYSTEMVALUES_POSTGRESQL;\n\n/**\n * An identifier for the database management system (DBMS) product being used. See below for a list of well-known identifiers.\n */\nexport const DBSYSTEMVALUES_REDSHIFT = TMP_DBSYSTEMVALUES_REDSHIFT;\n\n/**\n * An identifier for the database management system (DBMS) product being used. See below for a list of well-known identifiers.\n */\nexport const DBSYSTEMVALUES_HIVE = TMP_DBSYSTEMVALUES_HIVE;\n\n/**\n * An identifier for the database management system (DBMS) product being used. See below for a list of well-known identifiers.\n */\nexport const DBSYSTEMVALUES_CLOUDSCAPE = TMP_DBSYSTEMVALUES_CLOUDSCAPE;\n\n/**\n * An identifier for the database management system (DBMS) product being used. See below for a list of well-known identifiers.\n */\nexport const DBSYSTEMVALUES_HSQLDB = TMP_DBSYSTEMVALUES_HSQLDB;\n\n/**\n * An identifier for the database management system (DBMS) product being used. See below for a list of well-known identifiers.\n */\nexport const DBSYSTEMVALUES_PROGRESS = TMP_DBSYSTEMVALUES_PROGRESS;\n\n/**\n * An identifier for the database management system (DBMS) product being used. See below for a list of well-known identifiers.\n */\nexport const DBSYSTEMVALUES_MAXDB = TMP_DBSYSTEMVALUES_MAXDB;\n\n/**\n * An identifier for the database management system (DBMS) product being used. See below for a list of well-known identifiers.\n */\nexport const DBSYSTEMVALUES_HANADB = TMP_DBSYSTEMVALUES_HANADB;\n\n/**\n * An identifier for the database management system (DBMS) product being used. See below for a list of well-known identifiers.\n */\nexport const DBSYSTEMVALUES_INGRES = TMP_DBSYSTEMVALUES_INGRES;\n\n/**\n * An identifier for the database management system (DBMS) product being used. See below for a list of well-known identifiers.\n */\nexport const DBSYSTEMVALUES_FIRSTSQL = TMP_DBSYSTEMVALUES_FIRSTSQL;\n\n/**\n * An identifier for the database management system (DBMS) product being used. See below for a list of well-known identifiers.\n */\nexport const DBSYSTEMVALUES_EDB = TMP_DBSYSTEMVALUES_EDB;\n\n/**\n * An identifier for the database management system (DBMS) product being used. See below for a list of well-known identifiers.\n */\nexport const DBSYSTEMVALUES_CACHE = TMP_DBSYSTEMVALUES_CACHE;\n\n/**\n * An identifier for the database management system (DBMS) product being used. See below for a list of well-known identifiers.\n */\nexport const DBSYSTEMVALUES_ADABAS = TMP_DBSYSTEMVALUES_ADABAS;\n\n/**\n * An identifier for the database management system (DBMS) product being used. See below for a list of well-known identifiers.\n */\nexport const DBSYSTEMVALUES_FIREBIRD = TMP_DBSYSTEMVALUES_FIREBIRD;\n\n/**\n * An identifier for the database management system (DBMS) product being used. See below for a list of well-known identifiers.\n */\nexport const DBSYSTEMVALUES_DERBY = TMP_DBSYSTEMVALUES_DERBY;\n\n/**\n * An identifier for the database management system (DBMS) product being used. See below for a list of well-known identifiers.\n */\nexport const DBSYSTEMVALUES_FILEMAKER = TMP_DBSYSTEMVALUES_FILEMAKER;\n\n/**\n * An identifier for the database management system (DBMS) product being used. See below for a list of well-known identifiers.\n */\nexport const DBSYSTEMVALUES_INFORMIX = TMP_DBSYSTEMVALUES_INFORMIX;\n\n/**\n * An identifier for the database management system (DBMS) product being used. See below for a list of well-known identifiers.\n */\nexport const DBSYSTEMVALUES_INSTANTDB = TMP_DBSYSTEMVALUES_INSTANTDB;\n\n/**\n * An identifier for the database management system (DBMS) product being used. See below for a list of well-known identifiers.\n */\nexport const DBSYSTEMVALUES_INTERBASE = TMP_DBSYSTEMVALUES_INTERBASE;\n\n/**\n * An identifier for the database management system (DBMS) product being used. See below for a list of well-known identifiers.\n */\nexport const DBSYSTEMVALUES_MARIADB = TMP_DBSYSTEMVALUES_MARIADB;\n\n/**\n * An identifier for the database management system (DBMS) product being used. See below for a list of well-known identifiers.\n */\nexport const DBSYSTEMVALUES_NETEZZA = TMP_DBSYSTEMVALUES_NETEZZA;\n\n/**\n * An identifier for the database management system (DBMS) product being used. See below for a list of well-known identifiers.\n */\nexport const DBSYSTEMVALUES_PERVASIVE = TMP_DBSYSTEMVALUES_PERVASIVE;\n\n/**\n * An identifier for the database management system (DBMS) product being used. See below for a list of well-known identifiers.\n */\nexport const DBSYSTEMVALUES_POINTBASE = TMP_DBSYSTEMVALUES_POINTBASE;\n\n/**\n * An identifier for the database management system (DBMS) product being used. See below for a list of well-known identifiers.\n */\nexport const DBSYSTEMVALUES_SQLITE = TMP_DBSYSTEMVALUES_SQLITE;\n\n/**\n * An identifier for the database management system (DBMS) product being used. See below for a list of well-known identifiers.\n */\nexport const DBSYSTEMVALUES_SYBASE = TMP_DBSYSTEMVALUES_SYBASE;\n\n/**\n * An identifier for the database management system (DBMS) product being used. See below for a list of well-known identifiers.\n */\nexport const DBSYSTEMVALUES_TERADATA = TMP_DBSYSTEMVALUES_TERADATA;\n\n/**\n * An identifier for the database management system (DBMS) product being used. See below for a list of well-known identifiers.\n */\nexport const DBSYSTEMVALUES_VERTICA = TMP_DBSYSTEMVALUES_VERTICA;\n\n/**\n * An identifier for the database management system (DBMS) product being used. See below for a list of well-known identifiers.\n */\nexport const DBSYSTEMVALUES_H2 = TMP_DBSYSTEMVALUES_H2;\n\n/**\n * An identifier for the database management system (DBMS) product being used. See below for a list of well-known identifiers.\n */\nexport const DBSYSTEMVALUES_COLDFUSION = TMP_DBSYSTEMVALUES_COLDFUSION;\n\n/**\n * An identifier for the database management system (DBMS) product being used. See below for a list of well-known identifiers.\n */\nexport const DBSYSTEMVALUES_CASSANDRA = TMP_DBSYSTEMVALUES_CASSANDRA;\n\n/**\n * An identifier for the database management system (DBMS) product being used. See below for a list of well-known identifiers.\n */\nexport const DBSYSTEMVALUES_HBASE = TMP_DBSYSTEMVALUES_HBASE;\n\n/**\n * An identifier for the database management system (DBMS) product being used. See below for a list of well-known identifiers.\n */\nexport const DBSYSTEMVALUES_MONGODB = TMP_DBSYSTEMVALUES_MONGODB;\n\n/**\n * An identifier for the database management system (DBMS) product being used. See below for a list of well-known identifiers.\n */\nexport const DBSYSTEMVALUES_REDIS = TMP_DBSYSTEMVALUES_REDIS;\n\n/**\n * An identifier for the database management system (DBMS) product being used. See below for a list of well-known identifiers.\n */\nexport const DBSYSTEMVALUES_COUCHBASE = TMP_DBSYSTEMVALUES_COUCHBASE;\n\n/**\n * An identifier for the database management system (DBMS) product being used. See below for a list of well-known identifiers.\n */\nexport const DBSYSTEMVALUES_COUCHDB = TMP_DBSYSTEMVALUES_COUCHDB;\n\n/**\n * An identifier for the database management system (DBMS) product being used. See below for a list of well-known identifiers.\n */\nexport const DBSYSTEMVALUES_COSMOSDB = TMP_DBSYSTEMVALUES_COSMOSDB;\n\n/**\n * An identifier for the database management system (DBMS) product being used. See below for a list of well-known identifiers.\n */\nexport const DBSYSTEMVALUES_DYNAMODB = TMP_DBSYSTEMVALUES_DYNAMODB;\n\n/**\n * An identifier for the database management system (DBMS) product being used. See below for a list of well-known identifiers.\n */\nexport const DBSYSTEMVALUES_NEO4J = TMP_DBSYSTEMVALUES_NEO4J;\n\n/**\n * An identifier for the database management system (DBMS) product being used. See below for a list of well-known identifiers.\n */\nexport const DBSYSTEMVALUES_GEODE = TMP_DBSYSTEMVALUES_GEODE;\n\n/**\n * An identifier for the database management system (DBMS) product being used. See below for a list of well-known identifiers.\n */\nexport const DBSYSTEMVALUES_ELASTICSEARCH = TMP_DBSYSTEMVALUES_ELASTICSEARCH;\n\n/**\n * An identifier for the database management system (DBMS) product being used. See below for a list of well-known identifiers.\n */\nexport const DBSYSTEMVALUES_MEMCACHED = TMP_DBSYSTEMVALUES_MEMCACHED;\n\n/**\n * An identifier for the database management system (DBMS) product being used. See below for a list of well-known identifiers.\n */\nexport const DBSYSTEMVALUES_COCKROACHDB = TMP_DBSYSTEMVALUES_COCKROACHDB;\n\n/**\n * Identifies the Values for DbSystemValues enum definition\n *\n * An identifier for the database management system (DBMS) product being used. See below for a list of well-known identifiers.\n * @deprecated Use the DBSYSTEMVALUES_XXXXX constants rather than the DbSystemValues.XXXXX for bundle minification.\n */\nexport type DbSystemValues = {\n  /** Some other SQL database. Fallback only. See notes. */\n  OTHER_SQL: 'other_sql';\n\n  /** Microsoft SQL Server. */\n  MSSQL: 'mssql';\n\n  /** MySQL. */\n  MYSQL: 'mysql';\n\n  /** Oracle Database. */\n  ORACLE: 'oracle';\n\n  /** IBM Db2. */\n  DB2: 'db2';\n\n  /** PostgreSQL. */\n  POSTGRESQL: 'postgresql';\n\n  /** Amazon Redshift. */\n  REDSHIFT: 'redshift';\n\n  /** Apache Hive. */\n  HIVE: 'hive';\n\n  /** Cloudscape. */\n  CLOUDSCAPE: 'cloudscape';\n\n  /** HyperSQL DataBase. */\n  HSQLDB: 'hsqldb';\n\n  /** Progress Database. */\n  PROGRESS: 'progress';\n\n  /** SAP MaxDB. */\n  MAXDB: 'maxdb';\n\n  /** SAP HANA. */\n  HANADB: 'hanadb';\n\n  /** Ingres. */\n  INGRES: 'ingres';\n\n  /** FirstSQL. */\n  FIRSTSQL: 'firstsql';\n\n  /** EnterpriseDB. */\n  EDB: 'edb';\n\n  /** InterSystems Caché. */\n  CACHE: 'cache';\n\n  /** Adabas (Adaptable Database System). */\n  ADABAS: 'adabas';\n\n  /** Firebird. */\n  FIREBIRD: 'firebird';\n\n  /** Apache Derby. */\n  DERBY: 'derby';\n\n  /** FileMaker. */\n  FILEMAKER: 'filemaker';\n\n  /** Informix. */\n  INFORMIX: 'informix';\n\n  /** InstantDB. */\n  INSTANTDB: 'instantdb';\n\n  /** InterBase. */\n  INTERBASE: 'interbase';\n\n  /** MariaDB. */\n  MARIADB: 'mariadb';\n\n  /** Netezza. */\n  NETEZZA: 'netezza';\n\n  /** Pervasive PSQL. */\n  PERVASIVE: 'pervasive';\n\n  /** PointBase. */\n  POINTBASE: 'pointbase';\n\n  /** SQLite. */\n  SQLITE: 'sqlite';\n\n  /** Sybase. */\n  SYBASE: 'sybase';\n\n  /** Teradata. */\n  TERADATA: 'teradata';\n\n  /** Vertica. */\n  VERTICA: 'vertica';\n\n  /** H2. */\n  H2: 'h2';\n\n  /** ColdFusion IMQ. */\n  COLDFUSION: 'coldfusion';\n\n  /** Apache Cassandra. */\n  CASSANDRA: 'cassandra';\n\n  /** Apache HBase. */\n  HBASE: 'hbase';\n\n  /** MongoDB. */\n  MONGODB: 'mongodb';\n\n  /** Redis. */\n  REDIS: 'redis';\n\n  /** Couchbase. */\n  COUCHBASE: 'couchbase';\n\n  /** CouchDB. */\n  COUCHDB: 'couchdb';\n\n  /** Microsoft Azure Cosmos DB. */\n  COSMOSDB: 'cosmosdb';\n\n  /** Amazon DynamoDB. */\n  DYNAMODB: 'dynamodb';\n\n  /** Neo4j. */\n  NEO4J: 'neo4j';\n\n  /** Apache Geode. */\n  GEODE: 'geode';\n\n  /** Elasticsearch. */\n  ELASTICSEARCH: 'elasticsearch';\n\n  /** Memcached. */\n  MEMCACHED: 'memcached';\n\n  /** CockroachDB. */\n  COCKROACHDB: 'cockroachdb';\n};\n\n/**\n * The constant map of values for DbSystemValues.\n * @deprecated Use the DBSYSTEMVALUES_XXXXX constants rather than the DbSystemValues.XXXXX for bundle minification.\n */\nexport const DbSystemValues: DbSystemValues =\n  /*#__PURE__*/ createConstMap<DbSystemValues>([\n    TMP_DBSYSTEMVALUES_OTHER_SQL,\n    TMP_DBSYSTEMVALUES_MSSQL,\n    TMP_DBSYSTEMVALUES_MYSQL,\n    TMP_DBSYSTEMVALUES_ORACLE,\n    TMP_DBSYSTEMVALUES_DB2,\n    TMP_DBSYSTEMVALUES_POSTGRESQL,\n    TMP_DBSYSTEMVALUES_REDSHIFT,\n    TMP_DBSYSTEMVALUES_HIVE,\n    TMP_DBSYSTEMVALUES_CLOUDSCAPE,\n    TMP_DBSYSTEMVALUES_HSQLDB,\n    TMP_DBSYSTEMVALUES_PROGRESS,\n    TMP_DBSYSTEMVALUES_MAXDB,\n    TMP_DBSYSTEMVALUES_HANADB,\n    TMP_DBSYSTEMVALUES_INGRES,\n    TMP_DBSYSTEMVALUES_FIRSTSQL,\n    TMP_DBSYSTEMVALUES_EDB,\n    TMP_DBSYSTEMVALUES_CACHE,\n    TMP_DBSYSTEMVALUES_ADABAS,\n    TMP_DBSYSTEMVALUES_FIREBIRD,\n    TMP_DBSYSTEMVALUES_DERBY,\n    TMP_DBSYSTEMVALUES_FILEMAKER,\n    TMP_DBSYSTEMVALUES_INFORMIX,\n    TMP_DBSYSTEMVALUES_INSTANTDB,\n    TMP_DBSYSTEMVALUES_INTERBASE,\n    TMP_DBSYSTEMVALUES_MARIADB,\n    TMP_DBSYSTEMVALUES_NETEZZA,\n    TMP_DBSYSTEMVALUES_PERVASIVE,\n    TMP_DBSYSTEMVALUES_POINTBASE,\n    TMP_DBSYSTEMVALUES_SQLITE,\n    TMP_DBSYSTEMVALUES_SYBASE,\n    TMP_DBSYSTEMVALUES_TERADATA,\n    TMP_DBSYSTEMVALUES_VERTICA,\n    TMP_DBSYSTEMVALUES_H2,\n    TMP_DBSYSTEMVALUES_COLDFUSION,\n    TMP_DBSYSTEMVALUES_CASSANDRA,\n    TMP_DBSYSTEMVALUES_HBASE,\n    TMP_DBSYSTEMVALUES_MONGODB,\n    TMP_DBSYSTEMVALUES_REDIS,\n    TMP_DBSYSTEMVALUES_COUCHBASE,\n    TMP_DBSYSTEMVALUES_COUCHDB,\n    TMP_DBSYSTEMVALUES_COSMOSDB,\n    TMP_DBSYSTEMVALUES_DYNAMODB,\n    TMP_DBSYSTEMVALUES_NEO4J,\n    TMP_DBSYSTEMVALUES_GEODE,\n    TMP_DBSYSTEMVALUES_ELASTICSEARCH,\n    TMP_DBSYSTEMVALUES_MEMCACHED,\n    TMP_DBSYSTEMVALUES_COCKROACHDB,\n  ]);\n\n/* ----------------------------------------------------------------------------------------------------------\n * Constant values for DbCassandraConsistencyLevelValues enum definition\n *\n * The consistency level of the query. Based on consistency values from [CQL](https://docs.datastax.com/en/cassandra-oss/3.0/cassandra/dml/dmlConfigConsistency.html).\n * ---------------------------------------------------------------------------------------------------------- */\n\n// Temporary local constants to assign to the individual exports and the namespaced version\n// Required to avoid the namespace exports using the unminifable export names for some package types\nconst TMP_DBCASSANDRACONSISTENCYLEVELVALUES_ALL = 'all';\nconst TMP_DBCASSANDRACONSISTENCYLEVELVALUES_EACH_QUORUM = 'each_quorum';\nconst TMP_DBCASSANDRACONSISTENCYLEVELVALUES_QUORUM = 'quorum';\nconst TMP_DBCASSANDRACONSISTENCYLEVELVALUES_LOCAL_QUORUM = 'local_quorum';\nconst TMP_DBCASSANDRACONSISTENCYLEVELVALUES_ONE = 'one';\nconst TMP_DBCASSANDRACONSISTENCYLEVELVALUES_TWO = 'two';\nconst TMP_DBCASSANDRACONSISTENCYLEVELVALUES_THREE = 'three';\nconst TMP_DBCASSANDRACONSISTENCYLEVELVALUES_LOCAL_ONE = 'local_one';\nconst TMP_DBCASSANDRACONSISTENCYLEVELVALUES_ANY = 'any';\nconst TMP_DBCASSANDRACONSISTENCYLEVELVALUES_SERIAL = 'serial';\nconst TMP_DBCASSANDRACONSISTENCYLEVELVALUES_LOCAL_SERIAL = 'local_serial';\n\n/**\n * The consistency level of the query. Based on consistency values from [CQL](https://docs.datastax.com/en/cassandra-oss/3.0/cassandra/dml/dmlConfigConsistency.html).\n */\nexport const DBCASSANDRACONSISTENCYLEVELVALUES_ALL =\n  TMP_DBCASSANDRACONSISTENCYLEVELVALUES_ALL;\n\n/**\n * The consistency level of the query. Based on consistency values from [CQL](https://docs.datastax.com/en/cassandra-oss/3.0/cassandra/dml/dmlConfigConsistency.html).\n */\nexport const DBCASSANDRACONSISTENCYLEVELVALUES_EACH_QUORUM =\n  TMP_DBCASSANDRACONSISTENCYLEVELVALUES_EACH_QUORUM;\n\n/**\n * The consistency level of the query. Based on consistency values from [CQL](https://docs.datastax.com/en/cassandra-oss/3.0/cassandra/dml/dmlConfigConsistency.html).\n */\nexport const DBCASSANDRACONSISTENCYLEVELVALUES_QUORUM =\n  TMP_DBCASSANDRACONSISTENCYLEVELVALUES_QUORUM;\n\n/**\n * The consistency level of the query. Based on consistency values from [CQL](https://docs.datastax.com/en/cassandra-oss/3.0/cassandra/dml/dmlConfigConsistency.html).\n */\nexport const DBCASSANDRACONSISTENCYLEVELVALUES_LOCAL_QUORUM =\n  TMP_DBCASSANDRACONSISTENCYLEVELVALUES_LOCAL_QUORUM;\n\n/**\n * The consistency level of the query. Based on consistency values from [CQL](https://docs.datastax.com/en/cassandra-oss/3.0/cassandra/dml/dmlConfigConsistency.html).\n */\nexport const DBCASSANDRACONSISTENCYLEVELVALUES_ONE =\n  TMP_DBCASSANDRACONSISTENCYLEVELVALUES_ONE;\n\n/**\n * The consistency level of the query. Based on consistency values from [CQL](https://docs.datastax.com/en/cassandra-oss/3.0/cassandra/dml/dmlConfigConsistency.html).\n */\nexport const DBCASSANDRACONSISTENCYLEVELVALUES_TWO =\n  TMP_DBCASSANDRACONSISTENCYLEVELVALUES_TWO;\n\n/**\n * The consistency level of the query. Based on consistency values from [CQL](https://docs.datastax.com/en/cassandra-oss/3.0/cassandra/dml/dmlConfigConsistency.html).\n */\nexport const DBCASSANDRACONSISTENCYLEVELVALUES_THREE =\n  TMP_DBCASSANDRACONSISTENCYLEVELVALUES_THREE;\n\n/**\n * The consistency level of the query. Based on consistency values from [CQL](https://docs.datastax.com/en/cassandra-oss/3.0/cassandra/dml/dmlConfigConsistency.html).\n */\nexport const DBCASSANDRACONSISTENCYLEVELVALUES_LOCAL_ONE =\n  TMP_DBCASSANDRACONSISTENCYLEVELVALUES_LOCAL_ONE;\n\n/**\n * The consistency level of the query. Based on consistency values from [CQL](https://docs.datastax.com/en/cassandra-oss/3.0/cassandra/dml/dmlConfigConsistency.html).\n */\nexport const DBCASSANDRACONSISTENCYLEVELVALUES_ANY =\n  TMP_DBCASSANDRACONSISTENCYLEVELVALUES_ANY;\n\n/**\n * The consistency level of the query. Based on consistency values from [CQL](https://docs.datastax.com/en/cassandra-oss/3.0/cassandra/dml/dmlConfigConsistency.html).\n */\nexport const DBCASSANDRACONSISTENCYLEVELVALUES_SERIAL =\n  TMP_DBCASSANDRACONSISTENCYLEVELVALUES_SERIAL;\n\n/**\n * The consistency level of the query. Based on consistency values from [CQL](https://docs.datastax.com/en/cassandra-oss/3.0/cassandra/dml/dmlConfigConsistency.html).\n */\nexport const DBCASSANDRACONSISTENCYLEVELVALUES_LOCAL_SERIAL =\n  TMP_DBCASSANDRACONSISTENCYLEVELVALUES_LOCAL_SERIAL;\n\n/**\n * Identifies the Values for DbCassandraConsistencyLevelValues enum definition\n *\n * The consistency level of the query. Based on consistency values from [CQL](https://docs.datastax.com/en/cassandra-oss/3.0/cassandra/dml/dmlConfigConsistency.html).\n * @deprecated Use the DBCASSANDRACONSISTENCYLEVELVALUES_XXXXX constants rather than the DbCassandraConsistencyLevelValues.XXXXX for bundle minification.\n */\nexport type DbCassandraConsistencyLevelValues = {\n  /** all. */\n  ALL: 'all';\n\n  /** each_quorum. */\n  EACH_QUORUM: 'each_quorum';\n\n  /** quorum. */\n  QUORUM: 'quorum';\n\n  /** local_quorum. */\n  LOCAL_QUORUM: 'local_quorum';\n\n  /** one. */\n  ONE: 'one';\n\n  /** two. */\n  TWO: 'two';\n\n  /** three. */\n  THREE: 'three';\n\n  /** local_one. */\n  LOCAL_ONE: 'local_one';\n\n  /** any. */\n  ANY: 'any';\n\n  /** serial. */\n  SERIAL: 'serial';\n\n  /** local_serial. */\n  LOCAL_SERIAL: 'local_serial';\n};\n\n/**\n * The constant map of values for DbCassandraConsistencyLevelValues.\n * @deprecated Use the DBCASSANDRACONSISTENCYLEVELVALUES_XXXXX constants rather than the DbCassandraConsistencyLevelValues.XXXXX for bundle minification.\n */\nexport const DbCassandraConsistencyLevelValues: DbCassandraConsistencyLevelValues =\n  /*#__PURE__*/ createConstMap<DbCassandraConsistencyLevelValues>([\n    TMP_DBCASSANDRACONSISTENCYLEVELVALUES_ALL,\n    TMP_DBCASSANDRACONSISTENCYLEVELVALUES_EACH_QUORUM,\n    TMP_DBCASSANDRACONSISTENCYLEVELVALUES_QUORUM,\n    TMP_DBCASSANDRACONSISTENCYLEVELVALUES_LOCAL_QUORUM,\n    TMP_DBCASSANDRACONSISTENCYLEVELVALUES_ONE,\n    TMP_DBCASSANDRACONSISTENCYLEVELVALUES_TWO,\n    TMP_DBCASSANDRACONSISTENCYLEVELVALUES_THREE,\n    TMP_DBCASSANDRACONSISTENCYLEVELVALUES_LOCAL_ONE,\n    TMP_DBCASSANDRACONSISTENCYLEVELVALUES_ANY,\n    TMP_DBCASSANDRACONSISTENCYLEVELVALUES_SERIAL,\n    TMP_DBCASSANDRACONSISTENCYLEVELVALUES_LOCAL_SERIAL,\n  ]);\n\n/* ----------------------------------------------------------------------------------------------------------\n * Constant values for FaasTriggerValues enum definition\n *\n * Type of the trigger on which the function is executed.\n * ---------------------------------------------------------------------------------------------------------- */\n\n// Temporary local constants to assign to the individual exports and the namespaced version\n// Required to avoid the namespace exports using the unminifable export names for some package types\nconst TMP_FAASTRIGGERVALUES_DATASOURCE = 'datasource';\nconst TMP_FAASTRIGGERVALUES_HTTP = 'http';\nconst TMP_FAASTRIGGERVALUES_PUBSUB = 'pubsub';\nconst TMP_FAASTRIGGERVALUES_TIMER = 'timer';\nconst TMP_FAASTRIGGERVALUES_OTHER = 'other';\n\n/**\n * Type of the trigger on which the function is executed.\n */\nexport const FAASTRIGGERVALUES_DATASOURCE = TMP_FAASTRIGGERVALUES_DATASOURCE;\n\n/**\n * Type of the trigger on which the function is executed.\n */\nexport const FAASTRIGGERVALUES_HTTP = TMP_FAASTRIGGERVALUES_HTTP;\n\n/**\n * Type of the trigger on which the function is executed.\n */\nexport const FAASTRIGGERVALUES_PUBSUB = TMP_FAASTRIGGERVALUES_PUBSUB;\n\n/**\n * Type of the trigger on which the function is executed.\n */\nexport const FAASTRIGGERVALUES_TIMER = TMP_FAASTRIGGERVALUES_TIMER;\n\n/**\n * Type of the trigger on which the function is executed.\n */\nexport const FAASTRIGGERVALUES_OTHER = TMP_FAASTRIGGERVALUES_OTHER;\n\n/**\n * Identifies the Values for FaasTriggerValues enum definition\n *\n * Type of the trigger on which the function is executed.\n * @deprecated Use the FAASTRIGGERVALUES_XXXXX constants rather than the FaasTriggerValues.XXXXX for bundle minification.\n */\nexport type FaasTriggerValues = {\n  /** A response to some data source operation such as a database or filesystem read/write. */\n  DATASOURCE: 'datasource';\n\n  /** To provide an answer to an inbound HTTP request. */\n  HTTP: 'http';\n\n  /** A function is set to be executed when messages are sent to a messaging system. */\n  PUBSUB: 'pubsub';\n\n  /** A function is scheduled to be executed regularly. */\n  TIMER: 'timer';\n\n  /** If none of the others apply. */\n  OTHER: 'other';\n};\n\n/**\n * The constant map of values for FaasTriggerValues.\n * @deprecated Use the FAASTRIGGERVALUES_XXXXX constants rather than the FaasTriggerValues.XXXXX for bundle minification.\n */\nexport const FaasTriggerValues: FaasTriggerValues =\n  /*#__PURE__*/ createConstMap<FaasTriggerValues>([\n    TMP_FAASTRIGGERVALUES_DATASOURCE,\n    TMP_FAASTRIGGERVALUES_HTTP,\n    TMP_FAASTRIGGERVALUES_PUBSUB,\n    TMP_FAASTRIGGERVALUES_TIMER,\n    TMP_FAASTRIGGERVALUES_OTHER,\n  ]);\n\n/* ----------------------------------------------------------------------------------------------------------\n * Constant values for FaasDocumentOperationValues enum definition\n *\n * Describes the type of the operation that was performed on the data.\n * ---------------------------------------------------------------------------------------------------------- */\n\n// Temporary local constants to assign to the individual exports and the namespaced version\n// Required to avoid the namespace exports using the unminifable export names for some package types\nconst TMP_FAASDOCUMENTOPERATIONVALUES_INSERT = 'insert';\nconst TMP_FAASDOCUMENTOPERATIONVALUES_EDIT = 'edit';\nconst TMP_FAASDOCUMENTOPERATIONVALUES_DELETE = 'delete';\n\n/**\n * Describes the type of the operation that was performed on the data.\n */\nexport const FAASDOCUMENTOPERATIONVALUES_INSERT =\n  TMP_FAASDOCUMENTOPERATIONVALUES_INSERT;\n\n/**\n * Describes the type of the operation that was performed on the data.\n */\nexport const FAASDOCUMENTOPERATIONVALUES_EDIT =\n  TMP_FAASDOCUMENTOPERATIONVALUES_EDIT;\n\n/**\n * Describes the type of the operation that was performed on the data.\n */\nexport const FAASDOCUMENTOPERATIONVALUES_DELETE =\n  TMP_FAASDOCUMENTOPERATIONVALUES_DELETE;\n\n/**\n * Identifies the Values for FaasDocumentOperationValues enum definition\n *\n * Describes the type of the operation that was performed on the data.\n * @deprecated Use the FAASDOCUMENTOPERATIONVALUES_XXXXX constants rather than the FaasDocumentOperationValues.XXXXX for bundle minification.\n */\nexport type FaasDocumentOperationValues = {\n  /** When a new object is created. */\n  INSERT: 'insert';\n\n  /** When an object is modified. */\n  EDIT: 'edit';\n\n  /** When an object is deleted. */\n  DELETE: 'delete';\n};\n\n/**\n * The constant map of values for FaasDocumentOperationValues.\n * @deprecated Use the FAASDOCUMENTOPERATIONVALUES_XXXXX constants rather than the FaasDocumentOperationValues.XXXXX for bundle minification.\n */\nexport const FaasDocumentOperationValues: FaasDocumentOperationValues =\n  /*#__PURE__*/ createConstMap<FaasDocumentOperationValues>([\n    TMP_FAASDOCUMENTOPERATIONVALUES_INSERT,\n    TMP_FAASDOCUMENTOPERATIONVALUES_EDIT,\n    TMP_FAASDOCUMENTOPERATIONVALUES_DELETE,\n  ]);\n\n/* ----------------------------------------------------------------------------------------------------------\n * Constant values for FaasInvokedProviderValues enum definition\n *\n * The cloud provider of the invoked function.\n *\n * Note: SHOULD be equal to the `cloud.provider` resource attribute of the invoked function.\n * ---------------------------------------------------------------------------------------------------------- */\n\n// Temporary local constants to assign to the individual exports and the namespaced version\n// Required to avoid the namespace exports using the unminifable export names for some package types\nconst TMP_FAASINVOKEDPROVIDERVALUES_ALIBABA_CLOUD = 'alibaba_cloud';\nconst TMP_FAASINVOKEDPROVIDERVALUES_AWS = 'aws';\nconst TMP_FAASINVOKEDPROVIDERVALUES_AZURE = 'azure';\nconst TMP_FAASINVOKEDPROVIDERVALUES_GCP = 'gcp';\n\n/**\n * The cloud provider of the invoked function.\n *\n * Note: SHOULD be equal to the `cloud.provider` resource attribute of the invoked function.\n */\nexport const FAASINVOKEDPROVIDERVALUES_ALIBABA_CLOUD =\n  TMP_FAASINVOKEDPROVIDERVALUES_ALIBABA_CLOUD;\n\n/**\n * The cloud provider of the invoked function.\n *\n * Note: SHOULD be equal to the `cloud.provider` resource attribute of the invoked function.\n */\nexport const FAASINVOKEDPROVIDERVALUES_AWS = TMP_FAASINVOKEDPROVIDERVALUES_AWS;\n\n/**\n * The cloud provider of the invoked function.\n *\n * Note: SHOULD be equal to the `cloud.provider` resource attribute of the invoked function.\n */\nexport const FAASINVOKEDPROVIDERVALUES_AZURE =\n  TMP_FAASINVOKEDPROVIDERVALUES_AZURE;\n\n/**\n * The cloud provider of the invoked function.\n *\n * Note: SHOULD be equal to the `cloud.provider` resource attribute of the invoked function.\n */\nexport const FAASINVOKEDPROVIDERVALUES_GCP = TMP_FAASINVOKEDPROVIDERVALUES_GCP;\n\n/**\n * Identifies the Values for FaasInvokedProviderValues enum definition\n *\n * The cloud provider of the invoked function.\n *\n * Note: SHOULD be equal to the `cloud.provider` resource attribute of the invoked function.\n * @deprecated Use the FAASINVOKEDPROVIDERVALUES_XXXXX constants rather than the FaasInvokedProviderValues.XXXXX for bundle minification.\n */\nexport type FaasInvokedProviderValues = {\n  /** Alibaba Cloud. */\n  ALIBABA_CLOUD: 'alibaba_cloud';\n\n  /** Amazon Web Services. */\n  AWS: 'aws';\n\n  /** Microsoft Azure. */\n  AZURE: 'azure';\n\n  /** Google Cloud Platform. */\n  GCP: 'gcp';\n};\n\n/**\n * The constant map of values for FaasInvokedProviderValues.\n * @deprecated Use the FAASINVOKEDPROVIDERVALUES_XXXXX constants rather than the FaasInvokedProviderValues.XXXXX for bundle minification.\n */\nexport const FaasInvokedProviderValues: FaasInvokedProviderValues =\n  /*#__PURE__*/ createConstMap<FaasInvokedProviderValues>([\n    TMP_FAASINVOKEDPROVIDERVALUES_ALIBABA_CLOUD,\n    TMP_FAASINVOKEDPROVIDERVALUES_AWS,\n    TMP_FAASINVOKEDPROVIDERVALUES_AZURE,\n    TMP_FAASINVOKEDPROVIDERVALUES_GCP,\n  ]);\n\n/* ----------------------------------------------------------------------------------------------------------\n * Constant values for NetTransportValues enum definition\n *\n * Transport protocol used. See note below.\n * ---------------------------------------------------------------------------------------------------------- */\n\n// Temporary local constants to assign to the individual exports and the namespaced version\n// Required to avoid the namespace exports using the unminifable export names for some package types\nconst TMP_NETTRANSPORTVALUES_IP_TCP = 'ip_tcp';\nconst TMP_NETTRANSPORTVALUES_IP_UDP = 'ip_udp';\nconst TMP_NETTRANSPORTVALUES_IP = 'ip';\nconst TMP_NETTRANSPORTVALUES_UNIX = 'unix';\nconst TMP_NETTRANSPORTVALUES_PIPE = 'pipe';\nconst TMP_NETTRANSPORTVALUES_INPROC = 'inproc';\nconst TMP_NETTRANSPORTVALUES_OTHER = 'other';\n\n/**\n * Transport protocol used. See note below.\n */\nexport const NETTRANSPORTVALUES_IP_TCP = TMP_NETTRANSPORTVALUES_IP_TCP;\n\n/**\n * Transport protocol used. See note below.\n */\nexport const NETTRANSPORTVALUES_IP_UDP = TMP_NETTRANSPORTVALUES_IP_UDP;\n\n/**\n * Transport protocol used. See note below.\n */\nexport const NETTRANSPORTVALUES_IP = TMP_NETTRANSPORTVALUES_IP;\n\n/**\n * Transport protocol used. See note below.\n */\nexport const NETTRANSPORTVALUES_UNIX = TMP_NETTRANSPORTVALUES_UNIX;\n\n/**\n * Transport protocol used. See note below.\n */\nexport const NETTRANSPORTVALUES_PIPE = TMP_NETTRANSPORTVALUES_PIPE;\n\n/**\n * Transport protocol used. See note below.\n */\nexport const NETTRANSPORTVALUES_INPROC = TMP_NETTRANSPORTVALUES_INPROC;\n\n/**\n * Transport protocol used. See note below.\n */\nexport const NETTRANSPORTVALUES_OTHER = TMP_NETTRANSPORTVALUES_OTHER;\n\n/**\n * Identifies the Values for NetTransportValues enum definition\n *\n * Transport protocol used. See note below.\n * @deprecated Use the NETTRANSPORTVALUES_XXXXX constants rather than the NetTransportValues.XXXXX for bundle minification.\n */\nexport type NetTransportValues = {\n  /** ip_tcp. */\n  IP_TCP: 'ip_tcp';\n\n  /** ip_udp. */\n  IP_UDP: 'ip_udp';\n\n  /** Another IP-based protocol. */\n  IP: 'ip';\n\n  /** Unix Domain socket. See below. */\n  UNIX: 'unix';\n\n  /** Named or anonymous pipe. See note below. */\n  PIPE: 'pipe';\n\n  /** In-process communication. */\n  INPROC: 'inproc';\n\n  /** Something else (non IP-based). */\n  OTHER: 'other';\n};\n\n/**\n * The constant map of values for NetTransportValues.\n * @deprecated Use the NETTRANSPORTVALUES_XXXXX constants rather than the NetTransportValues.XXXXX for bundle minification.\n */\nexport const NetTransportValues: NetTransportValues =\n  /*#__PURE__*/ createConstMap<NetTransportValues>([\n    TMP_NETTRANSPORTVALUES_IP_TCP,\n    TMP_NETTRANSPORTVALUES_IP_UDP,\n    TMP_NETTRANSPORTVALUES_IP,\n    TMP_NETTRANSPORTVALUES_UNIX,\n    TMP_NETTRANSPORTVALUES_PIPE,\n    TMP_NETTRANSPORTVALUES_INPROC,\n    TMP_NETTRANSPORTVALUES_OTHER,\n  ]);\n\n/* ----------------------------------------------------------------------------------------------------------\n * Constant values for NetHostConnectionTypeValues enum definition\n *\n * The internet connection type currently being used by the host.\n * ---------------------------------------------------------------------------------------------------------- */\n\n// Temporary local constants to assign to the individual exports and the namespaced version\n// Required to avoid the namespace exports using the unminifable export names for some package types\nconst TMP_NETHOSTCONNECTIONTYPEVALUES_WIFI = 'wifi';\nconst TMP_NETHOSTCONNECTIONTYPEVALUES_WIRED = 'wired';\nconst TMP_NETHOSTCONNECTIONTYPEVALUES_CELL = 'cell';\nconst TMP_NETHOSTCONNECTIONTYPEVALUES_UNAVAILABLE = 'unavailable';\nconst TMP_NETHOSTCONNECTIONTYPEVALUES_UNKNOWN = 'unknown';\n\n/**\n * The internet connection type currently being used by the host.\n */\nexport const NETHOSTCONNECTIONTYPEVALUES_WIFI =\n  TMP_NETHOSTCONNECTIONTYPEVALUES_WIFI;\n\n/**\n * The internet connection type currently being used by the host.\n */\nexport const NETHOSTCONNECTIONTYPEVALUES_WIRED =\n  TMP_NETHOSTCONNECTIONTYPEVALUES_WIRED;\n\n/**\n * The internet connection type currently being used by the host.\n */\nexport const NETHOSTCONNECTIONTYPEVALUES_CELL =\n  TMP_NETHOSTCONNECTIONTYPEVALUES_CELL;\n\n/**\n * The internet connection type currently being used by the host.\n */\nexport const NETHOSTCONNECTIONTYPEVALUES_UNAVAILABLE =\n  TMP_NETHOSTCONNECTIONTYPEVALUES_UNAVAILABLE;\n\n/**\n * The internet connection type currently being used by the host.\n */\nexport const NETHOSTCONNECTIONTYPEVALUES_UNKNOWN =\n  TMP_NETHOSTCONNECTIONTYPEVALUES_UNKNOWN;\n\n/**\n * Identifies the Values for NetHostConnectionTypeValues enum definition\n *\n * The internet connection type currently being used by the host.\n * @deprecated Use the NETHOSTCONNECTIONTYPEVALUES_XXXXX constants rather than the NetHostConnectionTypeValues.XXXXX for bundle minification.\n */\nexport type NetHostConnectionTypeValues = {\n  /** wifi. */\n  WIFI: 'wifi';\n\n  /** wired. */\n  WIRED: 'wired';\n\n  /** cell. */\n  CELL: 'cell';\n\n  /** unavailable. */\n  UNAVAILABLE: 'unavailable';\n\n  /** unknown. */\n  UNKNOWN: 'unknown';\n};\n\n/**\n * The constant map of values for NetHostConnectionTypeValues.\n * @deprecated Use the NETHOSTCONNECTIONTYPEVALUES_XXXXX constants rather than the NetHostConnectionTypeValues.XXXXX for bundle minification.\n */\nexport const NetHostConnectionTypeValues: NetHostConnectionTypeValues =\n  /*#__PURE__*/ createConstMap<NetHostConnectionTypeValues>([\n    TMP_NETHOSTCONNECTIONTYPEVALUES_WIFI,\n    TMP_NETHOSTCONNECTIONTYPEVALUES_WIRED,\n    TMP_NETHOSTCONNECTIONTYPEVALUES_CELL,\n    TMP_NETHOSTCONNECTIONTYPEVALUES_UNAVAILABLE,\n    TMP_NETHOSTCONNECTIONTYPEVALUES_UNKNOWN,\n  ]);\n\n/* ----------------------------------------------------------------------------------------------------------\n * Constant values for NetHostConnectionSubtypeValues enum definition\n *\n * This describes more details regarding the connection.type. It may be the type of cell technology connection, but it could be used for describing details about a wifi connection.\n * ---------------------------------------------------------------------------------------------------------- */\n\n// Temporary local constants to assign to the individual exports and the namespaced version\n// Required to avoid the namespace exports using the unminifable export names for some package types\nconst TMP_NETHOSTCONNECTIONSUBTYPEVALUES_GPRS = 'gprs';\nconst TMP_NETHOSTCONNECTIONSUBTYPEVALUES_EDGE = 'edge';\nconst TMP_NETHOSTCONNECTIONSUBTYPEVALUES_UMTS = 'umts';\nconst TMP_NETHOSTCONNECTIONSUBTYPEVALUES_CDMA = 'cdma';\nconst TMP_NETHOSTCONNECTIONSUBTYPEVALUES_EVDO_0 = 'evdo_0';\nconst TMP_NETHOSTCONNECTIONSUBTYPEVALUES_EVDO_A = 'evdo_a';\nconst TMP_NETHOSTCONNECTIONSUBTYPEVALUES_CDMA2000_1XRTT = 'cdma2000_1xrtt';\nconst TMP_NETHOSTCONNECTIONSUBTYPEVALUES_HSDPA = 'hsdpa';\nconst TMP_NETHOSTCONNECTIONSUBTYPEVALUES_HSUPA = 'hsupa';\nconst TMP_NETHOSTCONNECTIONSUBTYPEVALUES_HSPA = 'hspa';\nconst TMP_NETHOSTCONNECTIONSUBTYPEVALUES_IDEN = 'iden';\nconst TMP_NETHOSTCONNECTIONSUBTYPEVALUES_EVDO_B = 'evdo_b';\nconst TMP_NETHOSTCONNECTIONSUBTYPEVALUES_LTE = 'lte';\nconst TMP_NETHOSTCONNECTIONSUBTYPEVALUES_EHRPD = 'ehrpd';\nconst TMP_NETHOSTCONNECTIONSUBTYPEVALUES_HSPAP = 'hspap';\nconst TMP_NETHOSTCONNECTIONSUBTYPEVALUES_GSM = 'gsm';\nconst TMP_NETHOSTCONNECTIONSUBTYPEVALUES_TD_SCDMA = 'td_scdma';\nconst TMP_NETHOSTCONNECTIONSUBTYPEVALUES_IWLAN = 'iwlan';\nconst TMP_NETHOSTCONNECTIONSUBTYPEVALUES_NR = 'nr';\nconst TMP_NETHOSTCONNECTIONSUBTYPEVALUES_NRNSA = 'nrnsa';\nconst TMP_NETHOSTCONNECTIONSUBTYPEVALUES_LTE_CA = 'lte_ca';\n\n/**\n * This describes more details regarding the connection.type. It may be the type of cell technology connection, but it could be used for describing details about a wifi connection.\n */\nexport const NETHOSTCONNECTIONSUBTYPEVALUES_GPRS =\n  TMP_NETHOSTCONNECTIONSUBTYPEVALUES_GPRS;\n\n/**\n * This describes more details regarding the connection.type. It may be the type of cell technology connection, but it could be used for describing details about a wifi connection.\n */\nexport const NETHOSTCONNECTIONSUBTYPEVALUES_EDGE =\n  TMP_NETHOSTCONNECTIONSUBTYPEVALUES_EDGE;\n\n/**\n * This describes more details regarding the connection.type. It may be the type of cell technology connection, but it could be used for describing details about a wifi connection.\n */\nexport const NETHOSTCONNECTIONSUBTYPEVALUES_UMTS =\n  TMP_NETHOSTCONNECTIONSUBTYPEVALUES_UMTS;\n\n/**\n * This describes more details regarding the connection.type. It may be the type of cell technology connection, but it could be used for describing details about a wifi connection.\n */\nexport const NETHOSTCONNECTIONSUBTYPEVALUES_CDMA =\n  TMP_NETHOSTCONNECTIONSUBTYPEVALUES_CDMA;\n\n/**\n * This describes more details regarding the connection.type. It may be the type of cell technology connection, but it could be used for describing details about a wifi connection.\n */\nexport const NETHOSTCONNECTIONSUBTYPEVALUES_EVDO_0 =\n  TMP_NETHOSTCONNECTIONSUBTYPEVALUES_EVDO_0;\n\n/**\n * This describes more details regarding the connection.type. It may be the type of cell technology connection, but it could be used for describing details about a wifi connection.\n */\nexport const NETHOSTCONNECTIONSUBTYPEVALUES_EVDO_A =\n  TMP_NETHOSTCONNECTIONSUBTYPEVALUES_EVDO_A;\n\n/**\n * This describes more details regarding the connection.type. It may be the type of cell technology connection, but it could be used for describing details about a wifi connection.\n */\nexport const NETHOSTCONNECTIONSUBTYPEVALUES_CDMA2000_1XRTT =\n  TMP_NETHOSTCONNECTIONSUBTYPEVALUES_CDMA2000_1XRTT;\n\n/**\n * This describes more details regarding the connection.type. It may be the type of cell technology connection, but it could be used for describing details about a wifi connection.\n */\nexport const NETHOSTCONNECTIONSUBTYPEVALUES_HSDPA =\n  TMP_NETHOSTCONNECTIONSUBTYPEVALUES_HSDPA;\n\n/**\n * This describes more details regarding the connection.type. It may be the type of cell technology connection, but it could be used for describing details about a wifi connection.\n */\nexport const NETHOSTCONNECTIONSUBTYPEVALUES_HSUPA =\n  TMP_NETHOSTCONNECTIONSUBTYPEVALUES_HSUPA;\n\n/**\n * This describes more details regarding the connection.type. It may be the type of cell technology connection, but it could be used for describing details about a wifi connection.\n */\nexport const NETHOSTCONNECTIONSUBTYPEVALUES_HSPA =\n  TMP_NETHOSTCONNECTIONSUBTYPEVALUES_HSPA;\n\n/**\n * This describes more details regarding the connection.type. It may be the type of cell technology connection, but it could be used for describing details about a wifi connection.\n */\nexport const NETHOSTCONNECTIONSUBTYPEVALUES_IDEN =\n  TMP_NETHOSTCONNECTIONSUBTYPEVALUES_IDEN;\n\n/**\n * This describes more details regarding the connection.type. It may be the type of cell technology connection, but it could be used for describing details about a wifi connection.\n */\nexport const NETHOSTCONNECTIONSUBTYPEVALUES_EVDO_B =\n  TMP_NETHOSTCONNECTIONSUBTYPEVALUES_EVDO_B;\n\n/**\n * This describes more details regarding the connection.type. It may be the type of cell technology connection, but it could be used for describing details about a wifi connection.\n */\nexport const NETHOSTCONNECTIONSUBTYPEVALUES_LTE =\n  TMP_NETHOSTCONNECTIONSUBTYPEVALUES_LTE;\n\n/**\n * This describes more details regarding the connection.type. It may be the type of cell technology connection, but it could be used for describing details about a wifi connection.\n */\nexport const NETHOSTCONNECTIONSUBTYPEVALUES_EHRPD =\n  TMP_NETHOSTCONNECTIONSUBTYPEVALUES_EHRPD;\n\n/**\n * This describes more details regarding the connection.type. It may be the type of cell technology connection, but it could be used for describing details about a wifi connection.\n */\nexport const NETHOSTCONNECTIONSUBTYPEVALUES_HSPAP =\n  TMP_NETHOSTCONNECTIONSUBTYPEVALUES_HSPAP;\n\n/**\n * This describes more details regarding the connection.type. It may be the type of cell technology connection, but it could be used for describing details about a wifi connection.\n */\nexport const NETHOSTCONNECTIONSUBTYPEVALUES_GSM =\n  TMP_NETHOSTCONNECTIONSUBTYPEVALUES_GSM;\n\n/**\n * This describes more details regarding the connection.type. It may be the type of cell technology connection, but it could be used for describing details about a wifi connection.\n */\nexport const NETHOSTCONNECTIONSUBTYPEVALUES_TD_SCDMA =\n  TMP_NETHOSTCONNECTIONSUBTYPEVALUES_TD_SCDMA;\n\n/**\n * This describes more details regarding the connection.type. It may be the type of cell technology connection, but it could be used for describing details about a wifi connection.\n */\nexport const NETHOSTCONNECTIONSUBTYPEVALUES_IWLAN =\n  TMP_NETHOSTCONNECTIONSUBTYPEVALUES_IWLAN;\n\n/**\n * This describes more details regarding the connection.type. It may be the type of cell technology connection, but it could be used for describing details about a wifi connection.\n */\nexport const NETHOSTCONNECTIONSUBTYPEVALUES_NR =\n  TMP_NETHOSTCONNECTIONSUBTYPEVALUES_NR;\n\n/**\n * This describes more details regarding the connection.type. It may be the type of cell technology connection, but it could be used for describing details about a wifi connection.\n */\nexport const NETHOSTCONNECTIONSUBTYPEVALUES_NRNSA =\n  TMP_NETHOSTCONNECTIONSUBTYPEVALUES_NRNSA;\n\n/**\n * This describes more details regarding the connection.type. It may be the type of cell technology connection, but it could be used for describing details about a wifi connection.\n */\nexport const NETHOSTCONNECTIONSUBTYPEVALUES_LTE_CA =\n  TMP_NETHOSTCONNECTIONSUBTYPEVALUES_LTE_CA;\n\n/**\n * Identifies the Values for NetHostConnectionSubtypeValues enum definition\n *\n * This describes more details regarding the connection.type. It may be the type of cell technology connection, but it could be used for describing details about a wifi connection.\n * @deprecated Use the NETHOSTCONNECTIONSUBTYPEVALUES_XXXXX constants rather than the NetHostConnectionSubtypeValues.XXXXX for bundle minification.\n */\nexport type NetHostConnectionSubtypeValues = {\n  /** GPRS. */\n  GPRS: 'gprs';\n\n  /** EDGE. */\n  EDGE: 'edge';\n\n  /** UMTS. */\n  UMTS: 'umts';\n\n  /** CDMA. */\n  CDMA: 'cdma';\n\n  /** EVDO Rel. 0. */\n  EVDO_0: 'evdo_0';\n\n  /** EVDO Rev. A. */\n  EVDO_A: 'evdo_a';\n\n  /** CDMA2000 1XRTT. */\n  CDMA2000_1XRTT: 'cdma2000_1xrtt';\n\n  /** HSDPA. */\n  HSDPA: 'hsdpa';\n\n  /** HSUPA. */\n  HSUPA: 'hsupa';\n\n  /** HSPA. */\n  HSPA: 'hspa';\n\n  /** IDEN. */\n  IDEN: 'iden';\n\n  /** EVDO Rev. B. */\n  EVDO_B: 'evdo_b';\n\n  /** LTE. */\n  LTE: 'lte';\n\n  /** EHRPD. */\n  EHRPD: 'ehrpd';\n\n  /** HSPAP. */\n  HSPAP: 'hspap';\n\n  /** GSM. */\n  GSM: 'gsm';\n\n  /** TD-SCDMA. */\n  TD_SCDMA: 'td_scdma';\n\n  /** IWLAN. */\n  IWLAN: 'iwlan';\n\n  /** 5G NR (New Radio). */\n  NR: 'nr';\n\n  /** 5G NRNSA (New Radio Non-Standalone). */\n  NRNSA: 'nrnsa';\n\n  /** LTE CA. */\n  LTE_CA: 'lte_ca';\n};\n\n/**\n * The constant map of values for NetHostConnectionSubtypeValues.\n * @deprecated Use the NETHOSTCONNECTIONSUBTYPEVALUES_XXXXX constants rather than the NetHostConnectionSubtypeValues.XXXXX for bundle minification.\n */\nexport const NetHostConnectionSubtypeValues: NetHostConnectionSubtypeValues =\n  /*#__PURE__*/ createConstMap<NetHostConnectionSubtypeValues>([\n    TMP_NETHOSTCONNECTIONSUBTYPEVALUES_GPRS,\n    TMP_NETHOSTCONNECTIONSUBTYPEVALUES_EDGE,\n    TMP_NETHOSTCONNECTIONSUBTYPEVALUES_UMTS,\n    TMP_NETHOSTCONNECTIONSUBTYPEVALUES_CDMA,\n    TMP_NETHOSTCONNECTIONSUBTYPEVALUES_EVDO_0,\n    TMP_NETHOSTCONNECTIONSUBTYPEVALUES_EVDO_A,\n    TMP_NETHOSTCONNECTIONSUBTYPEVALUES_CDMA2000_1XRTT,\n    TMP_NETHOSTCONNECTIONSUBTYPEVALUES_HSDPA,\n    TMP_NETHOSTCONNECTIONSUBTYPEVALUES_HSUPA,\n    TMP_NETHOSTCONNECTIONSUBTYPEVALUES_HSPA,\n    TMP_NETHOSTCONNECTIONSUBTYPEVALUES_IDEN,\n    TMP_NETHOSTCONNECTIONSUBTYPEVALUES_EVDO_B,\n    TMP_NETHOSTCONNECTIONSUBTYPEVALUES_LTE,\n    TMP_NETHOSTCONNECTIONSUBTYPEVALUES_EHRPD,\n    TMP_NETHOSTCONNECTIONSUBTYPEVALUES_HSPAP,\n    TMP_NETHOSTCONNECTIONSUBTYPEVALUES_GSM,\n    TMP_NETHOSTCONNECTIONSUBTYPEVALUES_TD_SCDMA,\n    TMP_NETHOSTCONNECTIONSUBTYPEVALUES_IWLAN,\n    TMP_NETHOSTCONNECTIONSUBTYPEVALUES_NR,\n    TMP_NETHOSTCONNECTIONSUBTYPEVALUES_NRNSA,\n    TMP_NETHOSTCONNECTIONSUBTYPEVALUES_LTE_CA,\n  ]);\n\n/* ----------------------------------------------------------------------------------------------------------\n * Constant values for HttpFlavorValues enum definition\n *\n * Kind of HTTP protocol used.\n *\n * Note: If `net.transport` is not specified, it can be assumed to be `IP.TCP` except if `http.flavor` is `QUIC`, in which case `IP.UDP` is assumed.\n * ---------------------------------------------------------------------------------------------------------- */\n\n// Temporary local constants to assign to the individual exports and the namespaced version\n// Required to avoid the namespace exports using the unminifable export names for some package types\nconst TMP_HTTPFLAVORVALUES_HTTP_1_0 = '1.0';\nconst TMP_HTTPFLAVORVALUES_HTTP_1_1 = '1.1';\nconst TMP_HTTPFLAVORVALUES_HTTP_2_0 = '2.0';\nconst TMP_HTTPFLAVORVALUES_SPDY = 'SPDY';\nconst TMP_HTTPFLAVORVALUES_QUIC = 'QUIC';\n\n/**\n * Kind of HTTP protocol used.\n *\n * Note: If `net.transport` is not specified, it can be assumed to be `IP.TCP` except if `http.flavor` is `QUIC`, in which case `IP.UDP` is assumed.\n */\nexport const HTTPFLAVORVALUES_HTTP_1_0 = TMP_HTTPFLAVORVALUES_HTTP_1_0;\n\n/**\n * Kind of HTTP protocol used.\n *\n * Note: If `net.transport` is not specified, it can be assumed to be `IP.TCP` except if `http.flavor` is `QUIC`, in which case `IP.UDP` is assumed.\n */\nexport const HTTPFLAVORVALUES_HTTP_1_1 = TMP_HTTPFLAVORVALUES_HTTP_1_1;\n\n/**\n * Kind of HTTP protocol used.\n *\n * Note: If `net.transport` is not specified, it can be assumed to be `IP.TCP` except if `http.flavor` is `QUIC`, in which case `IP.UDP` is assumed.\n */\nexport const HTTPFLAVORVALUES_HTTP_2_0 = TMP_HTTPFLAVORVALUES_HTTP_2_0;\n\n/**\n * Kind of HTTP protocol used.\n *\n * Note: If `net.transport` is not specified, it can be assumed to be `IP.TCP` except if `http.flavor` is `QUIC`, in which case `IP.UDP` is assumed.\n */\nexport const HTTPFLAVORVALUES_SPDY = TMP_HTTPFLAVORVALUES_SPDY;\n\n/**\n * Kind of HTTP protocol used.\n *\n * Note: If `net.transport` is not specified, it can be assumed to be `IP.TCP` except if `http.flavor` is `QUIC`, in which case `IP.UDP` is assumed.\n */\nexport const HTTPFLAVORVALUES_QUIC = TMP_HTTPFLAVORVALUES_QUIC;\n\n/**\n * Identifies the Values for HttpFlavorValues enum definition\n *\n * Kind of HTTP protocol used.\n *\n * Note: If `net.transport` is not specified, it can be assumed to be `IP.TCP` except if `http.flavor` is `QUIC`, in which case `IP.UDP` is assumed.\n * @deprecated Use the HTTPFLAVORVALUES_XXXXX constants rather than the HttpFlavorValues.XXXXX for bundle minification.\n */\nexport type HttpFlavorValues = {\n  /** HTTP 1.0. */\n  HTTP_1_0: '1.0';\n\n  /** HTTP 1.1. */\n  HTTP_1_1: '1.1';\n\n  /** HTTP 2. */\n  HTTP_2_0: '2.0';\n\n  /** SPDY protocol. */\n  SPDY: 'SPDY';\n\n  /** QUIC protocol. */\n  QUIC: 'QUIC';\n};\n\n/**\n * The constant map of values for HttpFlavorValues.\n * @deprecated Use the HTTPFLAVORVALUES_XXXXX constants rather than the HttpFlavorValues.XXXXX for bundle minification.\n */\nexport const HttpFlavorValues: HttpFlavorValues = {\n  HTTP_1_0: TMP_HTTPFLAVORVALUES_HTTP_1_0,\n  HTTP_1_1: TMP_HTTPFLAVORVALUES_HTTP_1_1,\n  HTTP_2_0: TMP_HTTPFLAVORVALUES_HTTP_2_0,\n  SPDY: TMP_HTTPFLAVORVALUES_SPDY,\n  QUIC: TMP_HTTPFLAVORVALUES_QUIC,\n};\n\n/* ----------------------------------------------------------------------------------------------------------\n * Constant values for MessagingDestinationKindValues enum definition\n *\n * The kind of message destination.\n * ---------------------------------------------------------------------------------------------------------- */\n\n// Temporary local constants to assign to the individual exports and the namespaced version\n// Required to avoid the namespace exports using the unminifable export names for some package types\nconst TMP_MESSAGINGDESTINATIONKINDVALUES_QUEUE = 'queue';\nconst TMP_MESSAGINGDESTINATIONKINDVALUES_TOPIC = 'topic';\n\n/**\n * The kind of message destination.\n */\nexport const MESSAGINGDESTINATIONKINDVALUES_QUEUE =\n  TMP_MESSAGINGDESTINATIONKINDVALUES_QUEUE;\n\n/**\n * The kind of message destination.\n */\nexport const MESSAGINGDESTINATIONKINDVALUES_TOPIC =\n  TMP_MESSAGINGDESTINATIONKINDVALUES_TOPIC;\n\n/**\n * Identifies the Values for MessagingDestinationKindValues enum definition\n *\n * The kind of message destination.\n * @deprecated Use the MESSAGINGDESTINATIONKINDVALUES_XXXXX constants rather than the MessagingDestinationKindValues.XXXXX for bundle minification.\n */\nexport type MessagingDestinationKindValues = {\n  /** A message sent to a queue. */\n  QUEUE: 'queue';\n\n  /** A message sent to a topic. */\n  TOPIC: 'topic';\n};\n\n/**\n * The constant map of values for MessagingDestinationKindValues.\n * @deprecated Use the MESSAGINGDESTINATIONKINDVALUES_XXXXX constants rather than the MessagingDestinationKindValues.XXXXX for bundle minification.\n */\nexport const MessagingDestinationKindValues: MessagingDestinationKindValues =\n  /*#__PURE__*/ createConstMap<MessagingDestinationKindValues>([\n    TMP_MESSAGINGDESTINATIONKINDVALUES_QUEUE,\n    TMP_MESSAGINGDESTINATIONKINDVALUES_TOPIC,\n  ]);\n\n/* ----------------------------------------------------------------------------------------------------------\n * Constant values for MessagingOperationValues enum definition\n *\n * A string identifying the kind of message consumption as defined in the [Operation names](#operation-names) section above. If the operation is &#34;send&#34;, this attribute MUST NOT be set, since the operation can be inferred from the span kind in that case.\n * ---------------------------------------------------------------------------------------------------------- */\n\n// Temporary local constants to assign to the individual exports and the namespaced version\n// Required to avoid the namespace exports using the unminifable export names for some package types\nconst TMP_MESSAGINGOPERATIONVALUES_RECEIVE = 'receive';\nconst TMP_MESSAGINGOPERATIONVALUES_PROCESS = 'process';\n\n/**\n * A string identifying the kind of message consumption as defined in the [Operation names](#operation-names) section above. If the operation is &#34;send&#34;, this attribute MUST NOT be set, since the operation can be inferred from the span kind in that case.\n */\nexport const MESSAGINGOPERATIONVALUES_RECEIVE =\n  TMP_MESSAGINGOPERATIONVALUES_RECEIVE;\n\n/**\n * A string identifying the kind of message consumption as defined in the [Operation names](#operation-names) section above. If the operation is &#34;send&#34;, this attribute MUST NOT be set, since the operation can be inferred from the span kind in that case.\n */\nexport const MESSAGINGOPERATIONVALUES_PROCESS =\n  TMP_MESSAGINGOPERATIONVALUES_PROCESS;\n\n/**\n * Identifies the Values for MessagingOperationValues enum definition\n *\n * A string identifying the kind of message consumption as defined in the [Operation names](#operation-names) section above. If the operation is &#34;send&#34;, this attribute MUST NOT be set, since the operation can be inferred from the span kind in that case.\n * @deprecated Use the MESSAGINGOPERATIONVALUES_XXXXX constants rather than the MessagingOperationValues.XXXXX for bundle minification.\n */\nexport type MessagingOperationValues = {\n  /** receive. */\n  RECEIVE: 'receive';\n\n  /** process. */\n  PROCESS: 'process';\n};\n\n/**\n * The constant map of values for MessagingOperationValues.\n * @deprecated Use the MESSAGINGOPERATIONVALUES_XXXXX constants rather than the MessagingOperationValues.XXXXX for bundle minification.\n */\nexport const MessagingOperationValues: MessagingOperationValues =\n  /*#__PURE__*/ createConstMap<MessagingOperationValues>([\n    TMP_MESSAGINGOPERATIONVALUES_RECEIVE,\n    TMP_MESSAGINGOPERATIONVALUES_PROCESS,\n  ]);\n\n/* ----------------------------------------------------------------------------------------------------------\n * Constant values for RpcGrpcStatusCodeValues enum definition\n *\n * The [numeric status code](https://github.com/grpc/grpc/blob/v1.33.2/doc/statuscodes.md) of the gRPC request.\n * ---------------------------------------------------------------------------------------------------------- */\n\n// Temporary local constants to assign to the individual exports and the namespaced version\n// Required to avoid the namespace exports using the unminifable export names for some package types\nconst TMP_RPCGRPCSTATUSCODEVALUES_OK = 0;\nconst TMP_RPCGRPCSTATUSCODEVALUES_CANCELLED = 1;\nconst TMP_RPCGRPCSTATUSCODEVALUES_UNKNOWN = 2;\nconst TMP_RPCGRPCSTATUSCODEVALUES_INVALID_ARGUMENT = 3;\nconst TMP_RPCGRPCSTATUSCODEVALUES_DEADLINE_EXCEEDED = 4;\nconst TMP_RPCGRPCSTATUSCODEVALUES_NOT_FOUND = 5;\nconst TMP_RPCGRPCSTATUSCODEVALUES_ALREADY_EXISTS = 6;\nconst TMP_RPCGRPCSTATUSCODEVALUES_PERMISSION_DENIED = 7;\nconst TMP_RPCGRPCSTATUSCODEVALUES_RESOURCE_EXHAUSTED = 8;\nconst TMP_RPCGRPCSTATUSCODEVALUES_FAILED_PRECONDITION = 9;\nconst TMP_RPCGRPCSTATUSCODEVALUES_ABORTED = 10;\nconst TMP_RPCGRPCSTATUSCODEVALUES_OUT_OF_RANGE = 11;\nconst TMP_RPCGRPCSTATUSCODEVALUES_UNIMPLEMENTED = 12;\nconst TMP_RPCGRPCSTATUSCODEVALUES_INTERNAL = 13;\nconst TMP_RPCGRPCSTATUSCODEVALUES_UNAVAILABLE = 14;\nconst TMP_RPCGRPCSTATUSCODEVALUES_DATA_LOSS = 15;\nconst TMP_RPCGRPCSTATUSCODEVALUES_UNAUTHENTICATED = 16;\n\n/**\n * The [numeric status code](https://github.com/grpc/grpc/blob/v1.33.2/doc/statuscodes.md) of the gRPC request.\n */\nexport const RPCGRPCSTATUSCODEVALUES_OK = TMP_RPCGRPCSTATUSCODEVALUES_OK;\n\n/**\n * The [numeric status code](https://github.com/grpc/grpc/blob/v1.33.2/doc/statuscodes.md) of the gRPC request.\n */\nexport const RPCGRPCSTATUSCODEVALUES_CANCELLED =\n  TMP_RPCGRPCSTATUSCODEVALUES_CANCELLED;\n\n/**\n * The [numeric status code](https://github.com/grpc/grpc/blob/v1.33.2/doc/statuscodes.md) of the gRPC request.\n */\nexport const RPCGRPCSTATUSCODEVALUES_UNKNOWN =\n  TMP_RPCGRPCSTATUSCODEVALUES_UNKNOWN;\n\n/**\n * The [numeric status code](https://github.com/grpc/grpc/blob/v1.33.2/doc/statuscodes.md) of the gRPC request.\n */\nexport const RPCGRPCSTATUSCODEVALUES_INVALID_ARGUMENT =\n  TMP_RPCGRPCSTATUSCODEVALUES_INVALID_ARGUMENT;\n\n/**\n * The [numeric status code](https://github.com/grpc/grpc/blob/v1.33.2/doc/statuscodes.md) of the gRPC request.\n */\nexport const RPCGRPCSTATUSCODEVALUES_DEADLINE_EXCEEDED =\n  TMP_RPCGRPCSTATUSCODEVALUES_DEADLINE_EXCEEDED;\n\n/**\n * The [numeric status code](https://github.com/grpc/grpc/blob/v1.33.2/doc/statuscodes.md) of the gRPC request.\n */\nexport const RPCGRPCSTATUSCODEVALUES_NOT_FOUND =\n  TMP_RPCGRPCSTATUSCODEVALUES_NOT_FOUND;\n\n/**\n * The [numeric status code](https://github.com/grpc/grpc/blob/v1.33.2/doc/statuscodes.md) of the gRPC request.\n */\nexport const RPCGRPCSTATUSCODEVALUES_ALREADY_EXISTS =\n  TMP_RPCGRPCSTATUSCODEVALUES_ALREADY_EXISTS;\n\n/**\n * The [numeric status code](https://github.com/grpc/grpc/blob/v1.33.2/doc/statuscodes.md) of the gRPC request.\n */\nexport const RPCGRPCSTATUSCODEVALUES_PERMISSION_DENIED =\n  TMP_RPCGRPCSTATUSCODEVALUES_PERMISSION_DENIED;\n\n/**\n * The [numeric status code](https://github.com/grpc/grpc/blob/v1.33.2/doc/statuscodes.md) of the gRPC request.\n */\nexport const RPCGRPCSTATUSCODEVALUES_RESOURCE_EXHAUSTED =\n  TMP_RPCGRPCSTATUSCODEVALUES_RESOURCE_EXHAUSTED;\n\n/**\n * The [numeric status code](https://github.com/grpc/grpc/blob/v1.33.2/doc/statuscodes.md) of the gRPC request.\n */\nexport const RPCGRPCSTATUSCODEVALUES_FAILED_PRECONDITION =\n  TMP_RPCGRPCSTATUSCODEVALUES_FAILED_PRECONDITION;\n\n/**\n * The [numeric status code](https://github.com/grpc/grpc/blob/v1.33.2/doc/statuscodes.md) of the gRPC request.\n */\nexport const RPCGRPCSTATUSCODEVALUES_ABORTED =\n  TMP_RPCGRPCSTATUSCODEVALUES_ABORTED;\n\n/**\n * The [numeric status code](https://github.com/grpc/grpc/blob/v1.33.2/doc/statuscodes.md) of the gRPC request.\n */\nexport const RPCGRPCSTATUSCODEVALUES_OUT_OF_RANGE =\n  TMP_RPCGRPCSTATUSCODEVALUES_OUT_OF_RANGE;\n\n/**\n * The [numeric status code](https://github.com/grpc/grpc/blob/v1.33.2/doc/statuscodes.md) of the gRPC request.\n */\nexport const RPCGRPCSTATUSCODEVALUES_UNIMPLEMENTED =\n  TMP_RPCGRPCSTATUSCODEVALUES_UNIMPLEMENTED;\n\n/**\n * The [numeric status code](https://github.com/grpc/grpc/blob/v1.33.2/doc/statuscodes.md) of the gRPC request.\n */\nexport const RPCGRPCSTATUSCODEVALUES_INTERNAL =\n  TMP_RPCGRPCSTATUSCODEVALUES_INTERNAL;\n\n/**\n * The [numeric status code](https://github.com/grpc/grpc/blob/v1.33.2/doc/statuscodes.md) of the gRPC request.\n */\nexport const RPCGRPCSTATUSCODEVALUES_UNAVAILABLE =\n  TMP_RPCGRPCSTATUSCODEVALUES_UNAVAILABLE;\n\n/**\n * The [numeric status code](https://github.com/grpc/grpc/blob/v1.33.2/doc/statuscodes.md) of the gRPC request.\n */\nexport const RPCGRPCSTATUSCODEVALUES_DATA_LOSS =\n  TMP_RPCGRPCSTATUSCODEVALUES_DATA_LOSS;\n\n/**\n * The [numeric status code](https://github.com/grpc/grpc/blob/v1.33.2/doc/statuscodes.md) of the gRPC request.\n */\nexport const RPCGRPCSTATUSCODEVALUES_UNAUTHENTICATED =\n  TMP_RPCGRPCSTATUSCODEVALUES_UNAUTHENTICATED;\n\n/**\n * Identifies the Values for RpcGrpcStatusCodeValues enum definition\n *\n * The [numeric status code](https://github.com/grpc/grpc/blob/v1.33.2/doc/statuscodes.md) of the gRPC request.\n * @deprecated Use the RPCGRPCSTATUSCODEVALUES_XXXXX constants rather than the RpcGrpcStatusCodeValues.XXXXX for bundle minification.\n */\nexport type RpcGrpcStatusCodeValues = {\n  /** OK. */\n  OK: 0;\n\n  /** CANCELLED. */\n  CANCELLED: 1;\n\n  /** UNKNOWN. */\n  UNKNOWN: 2;\n\n  /** INVALID_ARGUMENT. */\n  INVALID_ARGUMENT: 3;\n\n  /** DEADLINE_EXCEEDED. */\n  DEADLINE_EXCEEDED: 4;\n\n  /** NOT_FOUND. */\n  NOT_FOUND: 5;\n\n  /** ALREADY_EXISTS. */\n  ALREADY_EXISTS: 6;\n\n  /** PERMISSION_DENIED. */\n  PERMISSION_DENIED: 7;\n\n  /** RESOURCE_EXHAUSTED. */\n  RESOURCE_EXHAUSTED: 8;\n\n  /** FAILED_PRECONDITION. */\n  FAILED_PRECONDITION: 9;\n\n  /** ABORTED. */\n  ABORTED: 10;\n\n  /** OUT_OF_RANGE. */\n  OUT_OF_RANGE: 11;\n\n  /** UNIMPLEMENTED. */\n  UNIMPLEMENTED: 12;\n\n  /** INTERNAL. */\n  INTERNAL: 13;\n\n  /** UNAVAILABLE. */\n  UNAVAILABLE: 14;\n\n  /** DATA_LOSS. */\n  DATA_LOSS: 15;\n\n  /** UNAUTHENTICATED. */\n  UNAUTHENTICATED: 16;\n};\n\n/**\n * The constant map of values for RpcGrpcStatusCodeValues.\n * @deprecated Use the RPCGRPCSTATUSCODEVALUES_XXXXX constants rather than the RpcGrpcStatusCodeValues.XXXXX for bundle minification.\n */\nexport const RpcGrpcStatusCodeValues: RpcGrpcStatusCodeValues = {\n  OK: TMP_RPCGRPCSTATUSCODEVALUES_OK,\n  CANCELLED: TMP_RPCGRPCSTATUSCODEVALUES_CANCELLED,\n  UNKNOWN: TMP_RPCGRPCSTATUSCODEVALUES_UNKNOWN,\n  INVALID_ARGUMENT: TMP_RPCGRPCSTATUSCODEVALUES_INVALID_ARGUMENT,\n  DEADLINE_EXCEEDED: TMP_RPCGRPCSTATUSCODEVALUES_DEADLINE_EXCEEDED,\n  NOT_FOUND: TMP_RPCGRPCSTATUSCODEVALUES_NOT_FOUND,\n  ALREADY_EXISTS: TMP_RPCGRPCSTATUSCODEVALUES_ALREADY_EXISTS,\n  PERMISSION_DENIED: TMP_RPCGRPCSTATUSCODEVALUES_PERMISSION_DENIED,\n  RESOURCE_EXHAUSTED: TMP_RPCGRPCSTATUSCODEVALUES_RESOURCE_EXHAUSTED,\n  FAILED_PRECONDITION: TMP_RPCGRPCSTATUSCODEVALUES_FAILED_PRECONDITION,\n  ABORTED: TMP_RPCGRPCSTATUSCODEVALUES_ABORTED,\n  OUT_OF_RANGE: TMP_RPCGRPCSTATUSCODEVALUES_OUT_OF_RANGE,\n  UNIMPLEMENTED: TMP_RPCGRPCSTATUSCODEVALUES_UNIMPLEMENTED,\n  INTERNAL: TMP_RPCGRPCSTATUSCODEVALUES_INTERNAL,\n  UNAVAILABLE: TMP_RPCGRPCSTATUSCODEVALUES_UNAVAILABLE,\n  DATA_LOSS: TMP_RPCGRPCSTATUSCODEVALUES_DATA_LOSS,\n  UNAUTHENTICATED: TMP_RPCGRPCSTATUSCODEVALUES_UNAUTHENTICATED,\n};\n\n/* ----------------------------------------------------------------------------------------------------------\n * Constant values for MessageTypeValues enum definition\n *\n * Whether this is a received or sent message.\n * ---------------------------------------------------------------------------------------------------------- */\n\n// Temporary local constants to assign to the individual exports and the namespaced version\n// Required to avoid the namespace exports using the unminifable export names for some package types\nconst TMP_MESSAGETYPEVALUES_SENT = 'SENT';\nconst TMP_MESSAGETYPEVALUES_RECEIVED = 'RECEIVED';\n\n/**\n * Whether this is a received or sent message.\n */\nexport const MESSAGETYPEVALUES_SENT = TMP_MESSAGETYPEVALUES_SENT;\n\n/**\n * Whether this is a received or sent message.\n */\nexport const MESSAGETYPEVALUES_RECEIVED = TMP_MESSAGETYPEVALUES_RECEIVED;\n\n/**\n * Identifies the Values for MessageTypeValues enum definition\n *\n * Whether this is a received or sent message.\n * @deprecated Use the MESSAGETYPEVALUES_XXXXX constants rather than the MessageTypeValues.XXXXX for bundle minification.\n */\nexport type MessageTypeValues = {\n  /** sent. */\n  SENT: 'SENT';\n\n  /** received. */\n  RECEIVED: 'RECEIVED';\n};\n\n/**\n * The constant map of values for MessageTypeValues.\n * @deprecated Use the MESSAGETYPEVALUES_XXXXX constants rather than the MessageTypeValues.XXXXX for bundle minification.\n */\nexport const MessageTypeValues: MessageTypeValues =\n  /*#__PURE__*/ createConstMap<MessageTypeValues>([\n    TMP_MESSAGETYPEVALUES_SENT,\n    TMP_MESSAGETYPEVALUES_RECEIVED,\n  ]);\n"]}