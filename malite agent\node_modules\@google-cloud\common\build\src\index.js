"use strict";
// Copyright 2016 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
Object.defineProperty(exports, "__esModule", { value: true });
exports.util = exports.ApiError = exports.ServiceObject = exports.Service = exports.Operation = void 0;
/**
 * @type {module:common/operation}
 * @private
 */
var operation_1 = require("./operation");
Object.defineProperty(exports, "Operation", { enumerable: true, get: function () { return operation_1.Operation; } });
/**
 * @type {module:common/service}
 * @private
 */
var service_1 = require("./service");
Object.defineProperty(exports, "Service", { enumerable: true, get: function () { return service_1.Service; } });
/**
 * @type {module:common/serviceObject}
 * @private
 */
var service_object_1 = require("./service-object");
Object.defineProperty(exports, "ServiceObject", { enumerable: true, get: function () { return service_object_1.ServiceObject; } });
/**
 * @type {module:common/util}
 * @private
 */
var util_1 = require("./util");
Object.defineProperty(exports, "ApiError", { enumerable: true, get: function () { return util_1.ApiError; } });
Object.defineProperty(exports, "util", { enumerable: true, get: function () { return util_1.util; } });
//# sourceMappingURL=index.js.map