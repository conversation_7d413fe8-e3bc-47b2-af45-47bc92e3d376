"use strict";
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ChatCompletionChoice$ = exports.ChatCompletionChoice$outboundSchema = exports.ChatCompletionChoice$inboundSchema = exports.ChatCompletionChoiceFinishReason$ = exports.ChatCompletionChoiceFinishReason$outboundSchema = exports.ChatCompletionChoiceFinishReason$inboundSchema = exports.ChatCompletionChoiceFinishReason = void 0;
exports.chatCompletionChoiceToJSON = chatCompletionChoiceToJSON;
exports.chatCompletionChoiceFromJSON = chatCompletionChoiceFromJSON;
const z = __importStar(require("zod"));
const primitives_js_1 = require("../../lib/primitives.js");
const schemas_js_1 = require("../../lib/schemas.js");
const enums_js_1 = require("../../types/enums.js");
const assistantmessage_js_1 = require("./assistantmessage.js");
exports.ChatCompletionChoiceFinishReason = {
    Stop: "stop",
    Length: "length",
    ModelLength: "model_length",
    Error: "error",
    ToolCalls: "tool_calls",
};
/** @internal */
exports.ChatCompletionChoiceFinishReason$inboundSchema = z
    .union([
    z.nativeEnum(exports.ChatCompletionChoiceFinishReason),
    z.string().transform(enums_js_1.catchUnrecognizedEnum),
]);
/** @internal */
exports.ChatCompletionChoiceFinishReason$outboundSchema = z.union([
    z.nativeEnum(exports.ChatCompletionChoiceFinishReason),
    z.string().and(z.custom()),
]);
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var ChatCompletionChoiceFinishReason$;
(function (ChatCompletionChoiceFinishReason$) {
    /** @deprecated use `ChatCompletionChoiceFinishReason$inboundSchema` instead. */
    ChatCompletionChoiceFinishReason$.inboundSchema = exports.ChatCompletionChoiceFinishReason$inboundSchema;
    /** @deprecated use `ChatCompletionChoiceFinishReason$outboundSchema` instead. */
    ChatCompletionChoiceFinishReason$.outboundSchema = exports.ChatCompletionChoiceFinishReason$outboundSchema;
})(ChatCompletionChoiceFinishReason$ || (exports.ChatCompletionChoiceFinishReason$ = ChatCompletionChoiceFinishReason$ = {}));
/** @internal */
exports.ChatCompletionChoice$inboundSchema = z.object({
    index: z.number().int(),
    message: assistantmessage_js_1.AssistantMessage$inboundSchema,
    finish_reason: exports.ChatCompletionChoiceFinishReason$inboundSchema,
}).transform((v) => {
    return (0, primitives_js_1.remap)(v, {
        "finish_reason": "finishReason",
    });
});
/** @internal */
exports.ChatCompletionChoice$outboundSchema = z.object({
    index: z.number().int(),
    message: assistantmessage_js_1.AssistantMessage$outboundSchema,
    finishReason: exports.ChatCompletionChoiceFinishReason$outboundSchema,
}).transform((v) => {
    return (0, primitives_js_1.remap)(v, {
        finishReason: "finish_reason",
    });
});
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var ChatCompletionChoice$;
(function (ChatCompletionChoice$) {
    /** @deprecated use `ChatCompletionChoice$inboundSchema` instead. */
    ChatCompletionChoice$.inboundSchema = exports.ChatCompletionChoice$inboundSchema;
    /** @deprecated use `ChatCompletionChoice$outboundSchema` instead. */
    ChatCompletionChoice$.outboundSchema = exports.ChatCompletionChoice$outboundSchema;
})(ChatCompletionChoice$ || (exports.ChatCompletionChoice$ = ChatCompletionChoice$ = {}));
function chatCompletionChoiceToJSON(chatCompletionChoice) {
    return JSON.stringify(exports.ChatCompletionChoice$outboundSchema.parse(chatCompletionChoice));
}
function chatCompletionChoiceFromJSON(jsonString) {
    return (0, schemas_js_1.safeParse)(jsonString, (x) => exports.ChatCompletionChoice$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'ChatCompletionChoice' from JSON`);
}
//# sourceMappingURL=chatcompletionchoice.js.map