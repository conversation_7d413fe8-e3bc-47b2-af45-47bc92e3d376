"use strict";
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.MistralGoogleCloudCore = void 0;
const sdks_js_1 = require("./lib/sdks.js");
/**
 * A minimal client to use when calling standalone SDK functions. Typically, an
 * instance of this class would be instantiated once at the start of an
 * application and passed around through some dependency injection mechanism  to
 * parts of an application that need to make SDK calls.
 */
class MistralGoogleCloudCore extends sdks_js_1.ClientSDK {
}
exports.MistralGoogleCloudCore = MistralGoogleCloudCore;
//# sourceMappingURL=core.js.map