{"version": 3, "file": "ExemplarReservoir.js", "sourceRoot": "", "sources": ["../../../src/exemplar/ExemplarReservoir.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAEH,OAAO,EAGL,kBAAkB,EAClB,KAAK,GAEN,MAAM,oBAAoB,CAAC;AA0B5B,MAAM,cAAc;IAApB;QACU,UAAK,GAAW,CAAC,CAAC;QAClB,eAAU,GAAqB,EAAE,CAAC;QAClC,cAAS,GAAW,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAG3B,aAAQ,GAAY,KAAK,CAAC;IA2CpC,CAAC;IAzCC,KAAK,CACH,KAAa,EACb,SAAiB,EACjB,UAA4B,EAC5B,GAAY;QAEZ,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC3B,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,MAAM,WAAW,GAAG,KAAK,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC;QAC9C,IAAI,WAAW,IAAI,kBAAkB,CAAC,WAAW,CAAC,EAAE;YAClD,IAAI,CAAC,MAAM,GAAG,WAAW,CAAC,MAAM,CAAC;YACjC,IAAI,CAAC,OAAO,GAAG,WAAW,CAAC,OAAO,CAAC;SACpC;QACD,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;IACvB,CAAC;IAED,OAAO,CAAC,eAAiC;QACvC,IAAI,CAAC,IAAI,CAAC,QAAQ;YAAE,OAAO,IAAI,CAAC;QAChC,MAAM,iBAAiB,GAAG,IAAI,CAAC,UAAU,CAAC;QAC1C,oBAAoB;QACpB,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;YACzC,IAAI,eAAe,CAAC,GAAG,CAAC,KAAK,iBAAiB,CAAC,GAAG,CAAC,EAAE;gBACnD,OAAO,iBAAiB,CAAC,GAAG,CAAC,CAAC;aAC/B;QACH,CAAC,CAAC,CAAC;QACH,MAAM,MAAM,GAAa;YACvB,kBAAkB,EAAE,iBAAiB;YACrC,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,OAAO,EAAE,IAAI,CAAC,OAAO;SACtB,CAAC;QACF,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC;QACrB,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;QACf,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACxB,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC;QACxB,IAAI,CAAC,OAAO,GAAG,SAAS,CAAC;QACzB,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;QACtB,OAAO,MAAM,CAAC;IAChB,CAAC;CACF;AAED,MAAM,OAAgB,8BAA8B;IAMlD,YAAY,IAAY;QACtB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;QAClB,IAAI,CAAC,iBAAiB,GAAG,IAAI,KAAK,CAAiB,IAAI,CAAC,CAAC;QACzD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC,EAAE,EAAE;YACnC,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,GAAG,IAAI,cAAc,EAAE,CAAC;SAClD;IACH,CAAC;IASD,OAAO;QACL,OAAO,IAAI,CAAC,KAAK,CAAC;IACpB,CAAC;IAED;;OAEG;IACO,KAAK,KAAU,CAAC;IAE1B,OAAO,CAAC,eAAiC;QACvC,MAAM,SAAS,GAAe,EAAE,CAAC;QACjC,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE;YAC3C,MAAM,GAAG,GAAG,WAAW,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;YACjD,IAAI,GAAG,KAAK,IAAI,EAAE;gBAChB,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;aACrB;QACH,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,KAAK,EAAE,CAAC;QACb,OAAO,SAAS,CAAC;IACnB,CAAC;CACF", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  Context,\n  HrTime,\n  isSpanContextValid,\n  trace,\n  MetricAttributes,\n} from '@opentelemetry/api';\nimport { Exemplar } from './Exemplar';\n\n/**\n * An interface for an exemplar reservoir of samples.\n */\nexport interface ExemplarReservoir {\n  /** Offers a measurement to be sampled. */\n  offer(\n    value: number,\n    timestamp: HrTime,\n    attributes: MetricAttributes,\n    ctx: Context\n  ): void;\n  /**\n   * Returns accumulated Exemplars and also resets the reservoir\n   * for the next sampling period\n   *\n   * @param pointAttributes The attributes associated with metric point.\n   *\n   * @returns a list of {@link Exemplar}s. Returned exemplars contain the attributes that were filtered out by the\n   * aggregator, but recorded alongside the original measurement.\n   */\n  collect(pointAttributes: MetricAttributes): Exemplar[];\n}\n\nclass ExemplarBucket {\n  private value: number = 0;\n  private attributes: MetricAttributes = {};\n  private timestamp: HrTime = [0, 0];\n  private spanId?: string;\n  private traceId?: string;\n  private _offered: boolean = false;\n\n  offer(\n    value: number,\n    timestamp: HrTime,\n    attributes: MetricAttributes,\n    ctx: Context\n  ) {\n    this.value = value;\n    this.timestamp = timestamp;\n    this.attributes = attributes;\n    const spanContext = trace.getSpanContext(ctx);\n    if (spanContext && isSpanContextValid(spanContext)) {\n      this.spanId = spanContext.spanId;\n      this.traceId = spanContext.traceId;\n    }\n    this._offered = true;\n  }\n\n  collect(pointAttributes: MetricAttributes): Exemplar | null {\n    if (!this._offered) return null;\n    const currentAttributes = this.attributes;\n    // filter attributes\n    Object.keys(pointAttributes).forEach(key => {\n      if (pointAttributes[key] === currentAttributes[key]) {\n        delete currentAttributes[key];\n      }\n    });\n    const retVal: Exemplar = {\n      filteredAttributes: currentAttributes,\n      value: this.value,\n      timestamp: this.timestamp,\n      spanId: this.spanId,\n      traceId: this.traceId,\n    };\n    this.attributes = {};\n    this.value = 0;\n    this.timestamp = [0, 0];\n    this.spanId = undefined;\n    this.traceId = undefined;\n    this._offered = false;\n    return retVal;\n  }\n}\n\nexport abstract class FixedSizeExemplarReservoirBase\n  implements ExemplarReservoir\n{\n  protected _reservoirStorage: ExemplarBucket[];\n  protected _size: number;\n\n  constructor(size: number) {\n    this._size = size;\n    this._reservoirStorage = new Array<ExemplarBucket>(size);\n    for (let i = 0; i < this._size; i++) {\n      this._reservoirStorage[i] = new ExemplarBucket();\n    }\n  }\n\n  abstract offer(\n    value: number,\n    timestamp: HrTime,\n    attributes: MetricAttributes,\n    ctx: Context\n  ): void;\n\n  maxSize(): number {\n    return this._size;\n  }\n\n  /**\n   * Resets the reservoir\n   */\n  protected reset(): void {}\n\n  collect(pointAttributes: MetricAttributes): Exemplar[] {\n    const exemplars: Exemplar[] = [];\n    this._reservoirStorage.forEach(storageItem => {\n      const res = storageItem.collect(pointAttributes);\n      if (res !== null) {\n        exemplars.push(res);\n      }\n    });\n    this.reset();\n    return exemplars;\n  }\n}\n"]}